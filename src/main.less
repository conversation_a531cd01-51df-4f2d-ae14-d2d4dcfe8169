/* 文件上传列表-text */
.file-upload-footer-title {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 7px;
    padding: 2px 3px;
    /* border: 1px solid red !important; */
    .name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      flex: 1;
    }
    .is-error {
      display: block !important;
      color: red !important;
    }
    .operation {
      width: 50px;
      height: 100%;
      justify-content: space-around;
      display: flex;
      .icon {
        display: none;
        cursor: pointer;
        padding: 4px;
        color: #868686;
      }
    }
    .icon:hover {
      background-color: #e2e2e2;
      color: #1b1b1b;
    }
    .ant-progress-inner {
      height: 1px !important;
      width: 1px !important;
      font-size: 1px;
    }
  }
  .file-upload-footer-title:hover {
    background-color: #f0f0f0;
    .operation {
      .icon {
        display: block;
      }
    }
  }
  /* 文件上传列表-card */
  .file-upload-card-title {
    height: 84px;
    width: 84px;
    border: 1px solid #ff4d4f;
    padding: 8px;
    border-radius: 8px;
    position: relative;
    .card {
      height: 80px;
      width: 80px;
      padding: 2px;
      color: #ff4d4f;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .bg-card {
      position: absolute;
      top: 9px;
      left: 9px;
      height: 84px;
      width: 84px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .bg-card:hover {
      background-color: rgba(36, 36, 36, 0.5);
      .delete {
        display: block;
        padding: 4px;
        font-size: 16px;
        color: #d0d0d0;
      }
      .delete:hover {
        color: #f7f5ef;
      }
    }
    .delete {
      display: none;
    }
  }
  
  .upload-card-readonly-container {
    .ant-upload-select {
      display: none !important;
    }
  }