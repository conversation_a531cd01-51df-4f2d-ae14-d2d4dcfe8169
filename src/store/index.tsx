/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * @description 模版采用了Zustand作为状态管理工具，该工具具有体积小、无需模版代码，无需 Provider 等特点
 * 更少的样板代码
 * Zustand 只在 state 的值改变时渲染组件, 通常可以处理状态的改变而无需渲染代码
 * 状态管理通过简单定义的操作进行集中和更新, 在这方面和 Redux 类似, 但是又和 Redux 不太类似, Redux 开发必须创建 reducer、action、dispatch来处理状态, Zustand 让它变得更加容易
 * 使用 hooks 来管理 states, Hooks 在 react 中很流行, 因此是一个很受欢迎的状态管理库
 * Zustand 使用简单使用和简单实现的代码
 * 通过消除使用 Context Provides 从而使代码更短、更易读
 * 参考文章：https://juejin.cn/post/7134633741774749710
 */
import { lazy } from 'react';
import { ThemeConfig } from 'antd';
// import { appThemeColor, appThemeMode } from '@/constants';
import { antdThemeMode, appThemeColor } from '@/constants';
import { AllRouteMappings, InitialRoutes } from '@/routes';
import { fullScreenRouter } from '@/routes/modules/fullScreen';
import { clone } from 'lodash';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

const PageLayout = lazy(() => import('@/layouts/PageLayout'));

type TTestStoreProps = {
  votes: number;
  addVotes: () => void;
  subtractVotes: () => void;
};

type TokenConfig = string;

export const useTokenStore = create<{
  token: TokenConfig;
  setToken: (_token: TokenConfig) => void;
}>()(
  devtools(
    persist(
      (set, get) => ({
        token: '',
        setToken: (_token) => set((state) => ({ token: _token })),
      }),
      {
        name: 'gzjk_token_storage', // unique name
        storage: createJSONStorage(() => sessionStorage),
      }
    )
  )
);

// 定义 Store，并导出
export const useStore = create<TTestStoreProps>((set) => ({
  votes: 0,
  addVotes: () => set((state) => ({ votes: state.votes + 1 })),
  subtractVotes: () => set((state) => ({ votes: state.votes - 1 })),
}));

/**
 * @TODO 定义切换主题变量, 5.x 版本升级后，这里数据结构发生修改
 */
export const useThemeStore = create<{
  theme: ThemeConfig;
  setTheme: (_theme: ThemeConfig) => void;
}>()(
  devtools(
    persist(
      (set, get) => ({
        theme: {
          token: { colorPrimary: appThemeColor },
          // algorithm: appThemeMode,
          components: {
            Layout: {
              headerBg: '#008BFF',
            },
            // Slider: { ...antdThemeMode.Slider },
          },
        },
        setTheme: (_theme) =>
          set((state) => ({ theme: { ...state.theme, ..._theme } })),
      }),
      {
        name: 'gzjk', // unique name
      }
    )
  )
);
/**
 * @type allRouter
 */
export type TAllRouter = {
  name: string;
  title: string;
  path: string;
  completePath?: string;
  parentPath?: string;
  icon: string;
};
/**
 * @TODO 权限路由
 */
export const usePermissionRouterStore = create<{
  allRouter: TAllRouter[];
  setAllRouter: (payload: any) => void;
  routeStructure: any;
  setRouteStructure: (payload: any) => void;
  menuData: any; // 头部菜单数据
  setMenuData: (payload: any) => void;
  childMenuData: any; // 左侧菜单数据
  setChildMenuData: (payload: any) => void;
}>()(
  devtools(
    persist(
      (set) => ({
        allRouter: [],
        setAllRouter: (payload: any) => {
          // 生成扁平化的可访问的路由
          const arr = getRouterPath(payload, '');
          set({ allRouter: arr });

          localStorage.setItem('allRouter', JSON.stringify({ allRouter: arr })); 
        },
        routeStructure: clone(InitialRoutes),
        setRouteStructure: (payload: any) => {
          set({ routeStructure: payload });
          
          localStorage.setItem('routeStructure', JSON.stringify({ routeStructure: payload }));

        },
        menuData: [],
        setMenuData: (payload: any) => {
          set({ menuData: payload });
        },
        childMenuData: [],
        setChildMenuData: (payload: any) => {
          set({ childMenuData: payload });
        },
      }),
      {
        name: 'gzjk_permission_router',
        storage: createJSONStorage(() => sessionStorage),
        deserialize: (str: string) => {
          const data = JSON.parse(str);
          let routeStructure = data.state.routeStructure;
          routeStructure = deserializeRouter(routeStructure);

          return {
            ...data,
            routeStructure: [...routeStructure],
          };
        },
      }
    )
  )
);

function deserializeRouter(origin: any[]) {
  origin.forEach((_item) => {
    if (!_item.path) _item.element = <PageLayout />;
    if (_item.element && _item.path) {
      _item.element = AllRouteMappings.find((_route) => {
        if (_item.completePath) {
          return _item.completePath === _route.completePath;
        } else {
          return _item.path === _route.path;
        }
      })?.element!;
    }
    if (_item.children) {
      _item.children = deserializeRouter(_item.children);
    }
  });
  return origin;
}

/**
 * @TODO 获取所有的权限路由
 */
export function getRouterPath(
  arr: any[],
  path: string,
  initList: TAllRouter[] = []
) {
  const targetArr: TAllRouter[] = initList || [];
  arr.forEach((item) => {
    const completePath =
      path + (item.path.indexOf('/') > -1 ? item.path : '/' + item.path);
    targetArr.push({
      name: item.name,
      completePath,
      path: item.path,
      title: item.meta?.title,
      parentPath: path,
      icon: item.meta?.icon || '',
    });
    if (item.children && item.children.length) {
      getRouterPath(item.children, completePath, targetArr);
    }
  });
  return targetArr;
}

/**
 * @description 侧边栏展开
 */
export const useSidebarStore = create<{
  collapsed: boolean;
  setCollapsed: (val: boolean) => void;
}>((set) => ({
  collapsed: false,
  setCollapsed: (val) => set({ collapsed: val }),
}));

/**
 * @TODO 定义用户信息
 */
export const useInfoStore = create<{
  userInfo: Record<string, any>;
  isAdmin: boolean;
  setUserInfo: (_userInfo: Record<string, any>) => void;
}>()(
  devtools(
    persist(
      (set, get) => ({
        userInfo: {},
        isAdmin: false, // 是否超级管理员
        setUserInfo: (_userInfo) =>
          set(() => ({ userInfo: _userInfo, isAdmin: _userInfo?.user?.admin })),
      }),
      {
        name: 'gzjk_user_info',
        storage: createJSONStorage(() => sessionStorage),
      }
    )
  )
);

/**
 * 全局年份数据
 */
export const useYearStore = create<{
  yearList: Record<string, any>[];
  setYearList: (_yearList: Record<string, any>[]) => void;
}>()(
  devtools(
    persist(
      (set, get) => ({
        yearList: [],
        setYearList: (_yearList) => set((state) => ({ yearList: _yearList })),
      }),
      {
        name: 'gzjk_yearList_storage', // unique name
        storage: createJSONStorage(() => sessionStorage),
      }
    )
  )
);

/**
 * 全局的loading事件
 */
export const useLoadingStore = create<{
  golbalLoading: boolean;
  setGolbalLoading: (val: boolean) => void;
}>()(
  devtools((set) => ({
    golbalLoading: false,
    setGolbalLoading: (_value) => set(() => ({ golbalLoading: _value })),
  }))
);
