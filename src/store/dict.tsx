/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * @description 模版采用了Zustand作为状态管理工具，该工具具有体积小、无需模版代码，无需 Provider 等特点
 * 更少的样板代码
 * Zustand 只在 state 的值改变时渲染组件, 通常可以处理状态的改变而无需渲染代码
 * 状态管理通过简单定义的操作进行集中和更新, 在这方面和 Redux 类似, 但是又和 Redux 不太类似, Redux 开发必须创建 reducer、action、dispatch来处理状态, Zustand 让它变得更加容易
 * 使用 hooks 来管理 states, Hooks 在 react 中很流行, 因此是一个很受欢迎的状态管理库
 * Zustand 使用简单使用和简单实现的代码
 * 通过消除使用 Context Provides 从而使代码更短、更易读
 * 参考文章：https://juejin.cn/post/7134633741774749710
 */
import { getDict } from '@/api/dict';
import { codeDefinition } from '@/constants';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

export const useDictStore = create<{
  // sys_normal_disable
  sysNormalDisableOnTable: any;
  sysNormalDisableOnForm: any;
  getSysNormalDisable: () => void;
  // 传染病类别
  infectiousDiseasesList: Record<string, any>[];
  getInfectiousDiseasesList: () => void;
  // 病原体分类
  pathogenClassificationList: Record<string, any>[];
  getPathogenClassificationList: () => void;
  // 药品分类
  drugTypeList: Record<string, any>[];
  getDrugTypeList: () => void;
  // 年龄段
  ageRangeList: Record<string, any>[];
  getAgeRangeList: () => void;
  // 考核任务类型
  assessmentTaskTypeEnums: Record<string, any>[];
  getAssessmentTaskTypeEnums: () => void;
  // 样本名称枚举
  sampleNameEnums: Record<string, any>[];
  getSampleNameEnums: () => void;
}>()(
  devtools(
    persist(
      (set, get) => ({
        sysNormalDisableOnTable: null,
        sysNormalDisableOnForm: [],
        getSysNormalDisable: async () => {
          try {
            const o = get().sysNormalDisableOnTable;
            if (o) {
              return o;
            } else {
              const { code, data } = await getDict('sys_normal_disable');
              if (code === codeDefinition.QUERY_SUCCESS) {
                let n: any = {};
                let f: any = [];
                data.forEach((item: any) => {
                  f.push({
                    label: item.dictLabel,
                    value: item.dictValue,
                  });
                  n[item.dictValue] = { text: item.dictLabel };
                });
                set({ sysNormalDisableOnTable: n, sysNormalDisableOnForm: f });
              }
            }
          } catch (error) {
            throw new Error(`Error: ${error}`);
          }
        },

        // 传染病类别
        infectiousDiseasesList: [],
        getInfectiousDiseasesList: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().infectiousDiseasesList?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict(
              'infectious_disease_type'
            );
            if (code === codeDefinition.QUERY_SUCCESS) {
              const _data: Record<string, any>[] = [];
              data?.forEach((_item: Record<string, any>) => {
                _data?.push({
                  label: _item?.dictLabel,
                  value: ~~_item?.dictValue,
                });
              });
              set({ infectiousDiseasesList: _data });
            }
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },

        // 病原体分类
        pathogenClassificationList: [],
        getPathogenClassificationList: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().pathogenClassificationList?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict('etiology_type');
            if (code === codeDefinition.QUERY_SUCCESS) {
              const _data: Record<string, any>[] = [];
              data?.forEach((_item: Record<string, any>) => {
                _data?.push({
                  label: _item?.dictLabel,
                  value: _item?.dictValue,
                });
              });
              set({ pathogenClassificationList: _data });
            }
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },

        // 药品分类
        drugTypeList: [],
        getDrugTypeList: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().drugTypeList?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict('drug_type');
            if (code === codeDefinition.QUERY_SUCCESS) {
              const _data: Record<string, any>[] = [];
              data?.forEach((_item: Record<string, any>) => {
                _data?.push({
                  label: _item?.dictLabel,
                  value: _item?.dictValue,
                });
              });
              set({ drugTypeList: _data });
            }
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },

        // 年龄段
        ageRangeList: [],
        getAgeRangeList: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().ageRangeList?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict('age_group');
            if (code === codeDefinition.QUERY_SUCCESS) {
              const _data: Record<string, any>[] = [];
              data?.forEach((_item: Record<string, any>) => {
                _data?.push({
                  label: _item?.dictLabel,
                  value: _item?.dictValue,
                });
              });
              set({ ageRangeList: _data });
            }
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },

        // 考核任务类型
        assessmentTaskTypeEnums: [],
        getAssessmentTaskTypeEnums: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().assessmentTaskTypeEnums?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict('assessment_task_type');
            if (code !== codeDefinition.QUERY_SUCCESS) {
              throw new Error(msg);
            }
            const _enums = data?.map((item: any) => ({
              label: item?.dictLabel,
              value: item?.dictValue,
            }));
            set({ assessmentTaskTypeEnums: _enums });
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },

        // 样本名称枚举
        sampleNameEnums: [],
        getSampleNameEnums: async () => {
          try {
            // 如果已有缓存数据且不为空数组，则直接返回
            if (get().sampleNameEnums?.length > 0) {
              return;
            }

            const { code, data, msg } = await getDict('sample_name');
            if (code !== codeDefinition.QUERY_SUCCESS) {
              throw new Error(msg);
            }
            const _enums = data?.map((item: any) => ({
              label: item?.dictLabel,
              value: item?.dictValue,
            }));
            set({ sampleNameEnums: _enums });
          } catch (err) {
            throw new Error(`Error: err`);
          }
        },
      }),
      {
        name: 'gzjk_dict_storage', // 唯一的存储名称
        storage: createJSONStorage(() => sessionStorage), // 使用 sessionStorage 存储
      }
    )
  )
);
