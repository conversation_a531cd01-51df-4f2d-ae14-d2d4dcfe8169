/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * @description 模版采用了Zustand作为状态管理工具，该工具具有体积小、无需模版代码，无需 Provider 等特点
 * 更少的样板代码
 * Zustand 只在 state 的值改变时渲染组件, 通常可以处理状态的改变而无需渲染代码
 * 状态管理通过简单定义的操作进行集中和更新, 在这方面和 Redux 类似, 但是又和 Redux 不太类似, Redux 开发必须创建 reducer、action、dispatch来处理状态, Zustand 让它变得更加容易
 * 使用 hooks 来管理 states, Hooks 在 react 中很流行, 因此是一个很受欢迎的状态管理库
 * Zustand 使用简单使用和简单实现的代码
 * 通过消除使用 Context Provides 从而使代码更短、更易读
 * 参考文章：https://juejin.cn/post/7134633741774749710
 */
import { getDict, getDictAuth } from '@/api/dict';
import { getTempList } from '@/api/temp';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

// 定义 store 的状态类型
type QualityState = {
  //考核类型
  assessmentTypesOnTable: any;
  assessmentTypesOnForm: any;
  getAssessmentTypes: () => void;
  //考核类型（填报时查询，带权限查询）
  assessmentTypesOnForm2: any;
  getAssessmentTypesOnForm2: () => void;
  //任务类型
  assessmentTaskTypesOnTable: any;
  assessmentTaskTypesOnForm: any;
  getAssessmentTaskTypes: () => void;
  //考核任务状态
  taskStatusOnTable: any;
  // 模板管理状态
  templateStatusOnTable: any;
  taskStatusOnForm: any;
  reviewType: any; // 评审申请类型
};

export const useQualityStore = create<QualityState>()(
  persist(
    (set, get) => ({
      assessmentTypesOnTable: null,
      assessmentTypesOnForm: [],
      assessmentTypesOnForm2: [],
      getAssessmentTypes: async () => {
        try {
          const { code, data } = await getDict('assessment_type');
          if (code === codeDefinition.QUERY_SUCCESS) {
            let n: any = {};
            let f: any = [];
            data.forEach((item: any) => {
              f.push({
                label: item.dictLabel,
                value: item.dictValue,
              });
              n[item.dictValue] = { text: item.dictLabel };
            });
            set({ assessmentTypesOnTable: n, assessmentTypesOnForm: f });
          }
        } catch (error) {
          throw new Error(`Error: ${error}`);
        }
      },
      getAssessmentTypesOnForm2: async () => {
        try {
          const { code, data } = await getDictAuth('assessment_type');
          if (code === codeDefinition.QUERY_SUCCESS) {
            let f: any = [];
            data.forEach((item: any) => {
              f.push({
                label: item.dictLabel,
                value: item.dictValue,
              });
            });
            set({ assessmentTypesOnForm2: f });
          }
        } catch (error) {
          throw new Error(`Error: ${error}`);
        }
      },
      assessmentTaskTypesOnTable: null,
      assessmentTaskTypesOnForm: [],
      getAssessmentTaskTypes: async () => {
        try {
          const o = get().assessmentTaskTypesOnTable;
          if (o) {
            return o;
          } else {
            const { code, data } = await getDict('assessment_task_type');
            if (code === codeDefinition.QUERY_SUCCESS) {
              let n: any = {};
              let f: any = [];
              data.forEach((item: any) => {
                f.push({
                  label: item.dictLabel,
                  value: item.dictValue,
                });
                n[item.dictValue] = { text: item.dictLabel };
              });
              set({
                assessmentTaskTypesOnTable: n,
                assessmentTaskTypesOnForm: f,
              });
            }
          }
        } catch (error) {
          throw new Error(`Error: ${error}`);
        }
      },
      taskStatusOnTable: {
        0: { text: '草稿' },
        1: { text: '进行中' },
        4: { text: '已完成' },
        5: { text: '已公示' },
        2: { text: '变更中' },
      },
      templateStatusOnTable: {
        0: { text: '草稿' },
        1: { text: '可使用' },
      },
      taskStatusOnForm: [
        {
          label: '草稿',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        },
        {
          label: '已完成',
          value: 4,
        },
      ],
      // 评审申请类型
      reviewType: [
        {
          label: '初次评审',
          value: '1',
        },
        {
          label: '等级复合',
          value: '2',
        },
      ],
    }),
    {
      name: 'quality-storage', // 存储的键名
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
