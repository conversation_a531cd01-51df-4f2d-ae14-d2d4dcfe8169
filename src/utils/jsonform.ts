/*
 * @Author: LGX
 * @Date: 2024-02-29 10:13:50
 * @LastEditors: LGX
 * @LastEditTime: 2024-02-29 10:24:28
 * @FilePath: \xr-qc-jk-web\src\utils\jsonform.ts
 * @Description: json动态表单项
 * 
 */


export const setupForm = (orgData: any[], readOnly: boolean = false) => {
    return orgData.map((item: any) => {
        if (item.inputBox === 'text') {
            return {
                readonly: readOnly,
                title: item.fieldName,
                dataIndex: 'f' + item.id,
                initialValue: item.fieldValue,
                formItemProps: {
                    tooltip: item.pomptInformation,
                    rules: [
                        {
                            required: item.isRequired,
                            message: '此项为必填项',
                        },
                    ],
                },
                fieldProps: {
                    maxLength: 200,
                }
            };
        } else if (item.inputBox === 'select') {
            const enums: any = {};
            if (item.enumItems) {
                item.enumItems.split(',').map((en: any) => {
                    enums[en] = {
                        text: en,
                    };
                });
            }
            return {
                readonly: readOnly,
                title: item.fieldName,
                dataIndex: 'f' + item.id,
                initialValue: item.fieldValue,
                valueType: 'select',
                valueEnum: enums,
                formItemProps: {
                    tooltip: item.pomptInformation,
                    rules: [
                        {
                            required: item.isRequired,
                            message: '此项为必填项',
                        },
                    ],
                },
            };
        } else if (item.inputBox === 'digit') {
            return {
                readonly: readOnly,
                title: item.fieldName,
                dataIndex: 'f' + item.id,
                initialValue: item.fieldValue,
                valueType: 'digit',
                fieldProps: {
                    style: { width: '100%' },
                },
                formItemProps: {
                    tooltip: item.pomptInformation,
                    rules: [
                        {
                            required: item.isRequired,
                            message: '此项为必填项',
                        },
                    ],
                },
            };
        } else if (item.inputBox === 'calendar') {
            return {
                readonly: readOnly,
                title: item.fieldName,
                dataIndex: 'f' + item.id,
                initialValue: item.fieldValue,
                valueType: 'date',
                fieldProps: {
                    format: 'YYYY-MM-DD',
                    style: { width: '100%' },
                },
                formItemProps: {
                    tooltip: item.pomptInformation,
                    rules: [
                        {
                            required: item.isRequired,
                            message: '此项为必填项',
                        },
                    ],
                },
            };
        } else if (item.inputBox === 'file') {
            let fileList = [];
            try {
                fileList = item.fieldValue
                    ? JSON.parse(item.fieldValue).map((item: any) => {
                        return {
                            uid: item.id,
                            name: item.name,
                            status: 'done',
                            type: 'application/msword',
                            url: item.id,
                            response: {
                                data: {
                                    fileName: item.name,
                                    ossId: item.id,
                                },
                            },
                        };
                    })
                    : [];
            } catch (error) {
                fileList = [];
            }
            return {
                title: item.fieldName,
                dataIndex: 'f' + item.id,
                initialValue: fileList,
                colProps: { span: 24 },
                formItemProps: {
                    labelWrap: true,
                    className: 'inspect-upload-style',
                    labelCol: { span: 1.5 },
                    tooltip: item.pomptInformation,
                    rules: [
                        {
                            required: item.isRequired,
                            message: '此项为必填项',
                        },
                    ],
                }
            }
        }
    })
}