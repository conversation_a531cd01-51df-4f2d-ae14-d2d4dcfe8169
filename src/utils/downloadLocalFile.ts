/*
 * @Author: LGX
 * @Date: 2024-03-07 14:20:08
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-07 14:25:28
 * @FilePath: \xr-qc-jk-web\src\utils\downloadLocalFile.ts
 * @Description: 下载本地文件
 * 
 */
import { message } from 'antd'

const downloadLocalFile = (file: any, name?: string) => {
    if (!file) {
        message.error('下载失败，未查询到文件链接')
        return
    }
    // 创建一个隐藏的链接元素
    const linkElement = document.createElement('a');
    linkElement.style.display = 'none';
    linkElement.href = file; // 设置图片URL
    linkElement.download = name ?? "未命名"; // 设置下载文件名
    // 将链接添加到DOM中并触发点击
    document.body.appendChild(linkElement);
    linkElement.click();
    // 下载完成后从DOM中移除该链接元素
    document.body.removeChild(linkElement);
}

export default downloadLocalFile;