import excel from '@/assets/fileicon/excel.svg';
import img from '@/assets/fileicon/img.svg';
import pdf from '@/assets/fileicon/pdf.svg';
import ppt from '@/assets/fileicon/ppt.svg';
import unknow from '@/assets/fileicon/unknow.svg';
import video from '@/assets/fileicon/video.svg';
import word from '@/assets/fileicon/word.svg';

export const getIconByName = (fileName: any) => {
  const fileTypes: any = {
    image: ['jpg', 'jpeg', 'png', 'gif'],
    video: ['mp4', 'avi', 'mkv'],
    pdf: ['pdf'],
    doc: ['doc', 'docx'],
    spreadsheet: ['xls', 'xlsx', 'csv'],
    presentation: ['ppt', 'pptx'],
    unknown: [],
  };

  const fileExtension = fileName ? fileName.split('.').pop().toLowerCase() : '';

  let t = '';
  for (const type in fileTypes) {
    if (fileTypes[type].includes(fileExtension)) {
      t = type.charAt(0).toUpperCase() + type.slice(1); // Capitalize the first letter
    }
  }

  switch (t) {
    case 'Image':
      return img;
    case 'Video':
      return video;
    case 'Pdf':
      return pdf;
    case 'Doc':
      return word;
    case 'Spreadsheet':
      return excel;
    case 'Presentation':
      return ppt;
    default:
      return unknow;
  }
};
export const getFileTypeByName = (fileName: any) => {
  const fileTypes: any = {
    image: ['jpg', 'jpeg', 'png', 'gif'],
    video: ['mp4', 'avi', 'mkv'],
    pdf: ['pdf'],
    doc: ['doc', 'docx'],
    spreadsheet: ['xls', 'xlsx', 'csv'],
    presentation: ['ppt', 'pptx'],
    unknown: [],
  };

  const fileExtension = fileName.split('.').pop().toLowerCase();

  let t = '';
  for (const type in fileTypes) {
    if (fileTypes[type].includes(fileExtension)) {
      t = type.charAt(0).toUpperCase() + type.slice(1); // Capitalize the first letter
    }
  }
  return t;
};
