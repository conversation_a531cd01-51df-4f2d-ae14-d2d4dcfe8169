/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { message, notification } from 'antd';
import { API } from '@/api/file';
import { codeMessage } from '@/constants';
import { extend, type ResponseError } from 'umi-request';
import history from '@/utils/history';

/**
 * @TODO 异常处理程序
 */
const errorHandler = (error: ResponseError) => {
  const { response } = error;

  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    notification.error({
      description: '因系统服务更新,更新完成后请重新登录!',
      message: '系统服务更新重启中...',
    });
  }

  return response;
};

/**
 * @TODO 配置request请求时的默认参数
 */
const request = extend({
  prefix: import.meta.env.VITE_URL,
  // 默认错误处理
  errorHandler,
  // 开启CORS跨域
  crossOrigin: true,
  // 默认请求是否带上cookie
  // credentials: 'include',
});

/**
 * @TODO 请求拦截
 */
request.interceptors.request.use((url, options) => {
  let token = sessionStorage.getItem('gzjk_token_storage');

  if (token) {
    token = JSON.parse(token).state.token;
  }

  let newOptions = { ...options };
  newOptions.headers = {
    ...newOptions.headers,
    Authorization: `Bearer ${token}`,
  };

  // 如果是导出类接口加上类型
  if (
    url.includes('export') ||
    url.includes('Export') ||
    url.includes('download') ||
    url.includes('downFile') ||
    url.includes('Download') ||
    url.includes('preview')
  ) {
    newOptions.responseType = 'blob';
    // newOptions.getResponse = true   // 重点
  }
  return {
    url: `${url}`,
    options: { ...newOptions },
  };
});

/**
 * @TODO 响应拦截
 */
request.interceptors.response.use(async (response) => {
  // 克隆响应对象做解析处理
  // 这里的res就是我们请求到的数据

  // 文件流处理
  if (
    response.status === 200 &&
    (response.url.indexOf(API.FILE_DOWN) > -1 ||
      response.url.includes('export') ||
      response.url.includes('Export') ||
      response.url.includes('download') ||
      response.url.includes('downFile') ||
      response.url.includes('Download') ||
      response.url.includes('preview'))
  ) {
    const disposition = response.headers.get('content-disposition');
    const fileName = disposition
      ? decodeURI(disposition.split(';')[1].split('filename=')[1])
      : null;
    return {
      fileName: fileName,
      data: response.body,
      response: response,
    };
  }
  const res = await response.clone().json();
  const { code, msg } = res;
  if (code === 401) {
    message.info(msg);
    sessionStorage.setItem('gzjk_token_storage', '');
    history.push(`${import.meta.env.VITE_PUBLIC_PATH}#/login`);
    window.location.reload();
    return;
  }
  return res;
});

export default request;
