/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * @TODO 判断当前系统是否启用深色模式
 */
export const isDarkTheme = window.matchMedia('(prefers-color-scheme: dark)');

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data: any, id: any, parentId?: any, children?: any) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  };

  var childrenListMap: any = {};
  var nodeIds: any = {};
  var tree: any = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o: any) {
    if (childrenListMap[o[config.id]] != null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}
/**
 * @description 判断系统路由模式及前缀返回
 */
export const routerPrefix = () => {
  let _str = '';
  // if (import.meta.env.VITE_ROUTER_MODE === 'hash') {
  //   _str = '/#';
  // }
  return import.meta.env.VITE_PUBLIC_PATH + '?current=1&pageSize=10#/';
};

/**
 * @description 转换后台获取的平铺的行政区域数据
 */
export const convertToCascading = (data: any[]): any[] => {
  const result: any[] = [];
  const cityMap: { [key: string]: any } = {};

  data.forEach((item) => {
    if (!cityMap[item.cityName]) {
      cityMap[item.cityName] = {
        value: item.cityId.toString(),
        label: item.cityName,
        children: [],
      };
      result.push(cityMap[item.cityName]);
    }

    const cityObj = cityMap[item.cityName];

    const areaObj = {
      value: item.areaId.toString(),
      label: item.areaName,
    };

    cityObj.children.push(areaObj);
  });

  return result;
};

export const converPxToVW = (px: number) => {
  return ((px * 100) / 1920).toFixed(6);
};

export const converPxToVH = (px: number) => {
  return ((px * 100) / 1080).toFixed(6);
};
