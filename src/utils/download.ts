/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2022-12-13 13:56:59
 * @LastEditTime: 2024-03-29 16:59:50
 * @LastEditors: LGX
 * @FilePath: \xr-qc-jk-web\src\utils\download.ts
 * @Description: 传入文件流和文件名下载
 */

/**
 * 下载文件 Excel
 * data：文件流
 * name: 文件名称
 * */
import { message } from 'antd';

const download = async (datas: any, name: string) => {
  try {
    const { data, fileName } = datas;
    if (!data.getReader) {
      return;
    }
    const reader = data.getReader();
    // 手动控制读取流以便回传下载进度
    const stream = new ReadableStream({
      start(controller) {
        // 声明一个 pumpRead 方法读取片段
        // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
        const pumpRead = (): any =>
          reader.read().then(({ done, value }: any) => {
            // done  - 当 stream 传完所有数据时则变成 true
            // value - 数据片段 - done 为 true 时为 undefined
            if (done) {
              // 结束读取 关闭读取流
              controller.close();
              return;
            }
            // 每次推入读取队列 并链式执行
            controller.enqueue(value);
            return pumpRead();
          });
        // 开始读取
        return pumpRead();
      },
    });
    // 最后我们通过 Response 函数接收这个文件流 并转为 blob
    const blob = await new Response(stream).blob();
    const objUrl = window.URL.createObjectURL(blob);
    const aLink = document.createElement('a');
    aLink.style.display = 'none';
    aLink.href = objUrl;
    aLink.setAttribute('download', decodeURI(name ?? fileName));
    document.body.appendChild(aLink);
    aLink.click();
    document.body.removeChild(aLink);
    window.URL.revokeObjectURL(objUrl);
  } catch (e) {
    message.error('下载失败，请检查后再试');
  }
};

export default download;
