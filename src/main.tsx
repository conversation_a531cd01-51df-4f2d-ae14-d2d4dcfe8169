import React from 'react';
import ReactDOM from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import { register } from 'swiper/element/bundle';
import App from './app';
import './main.css';
import './main.less';

register();
ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <HashRouter basename={import.meta.env.VITE_STATIC_CDN}>
      <App />
    </HashRouter>
  </React.StrictMode>
);
