export const sampleCrowdEnums = [
  {
    key: '1',
    label: '男',
    value: 1,
  },
  {
    key: '0',
    label: '女',
    value: 0,
  },
];

// 样本检测结果枚举
export const sampleDetectionResultEnums = [
  {
    key: '0',
    label: '阴性',
    value: '0',
  },
  {
    key: '1',
    label: '阳性',
    value: '1',
  },
  {
    key: '2',
    label: '未检测',
    value: '2',
  },
];

// 性别枚举
export const sexSelectEnums = [
  { label: '男性', value: 1 },
  { label: '女性', value: 0 },
];

// 公共是、否选择Enum
export const commonYesNoSelectEnums = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];
