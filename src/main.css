@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  margin: 0;
  padding: 0;
}

#root {
  width: 100%;
  height: 100%;
}

.default-drawer-body {
  padding: 0;
}

.default-popover-message-title {
  display: inline-block;
  padding-left: 5px;
}

p {
  margin: 0;
  padding: 0;
}
.upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-inline-end: 8px;
  cursor: pointer;
}
.upload-list-inlines .ant-upload-list-item {
  max-width: 150px;
  display: flex;
  margin-inline-end: 8px;
  cursor: pointer;
}
.upload-list-inlines .ant-upload-list-item-name {
  flex: 1 !important;
}
.upload-list-inlines .ant-upload-list-item-actions {
  width: 50px !important;
}

.ant-upload-rtl.upload-list-inline .ant-upload-list-item {
  float: right;
}
/* 设置全局滚动条样式 */
::-webkit-scrollbar {
  width: 10px; /* 滚动条宽度 */
  height: 10px;
  position: absolute;
}
/* 设置滚动条轨道（背景）样式 */
::-webkit-scrollbar-track {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.1);
}
::-webkit-scrollbar-track:hover {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.1);
}
/* 设置滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* background-color: transparent; */
  background: rgba(0, 0, 0, 0.15); /* 鼠标悬停时的颜色 */
}
/* 当鼠标悬停在滚动条上时的滑块样式 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.15); /* 鼠标悬停时的颜色 */
}

.progress .ant-progress-outer .ant-progress-inner {
  height: 2px;
}

div {
  box-sizing: border-box;
}

@font-face {
  font-family: '优设标题黑';
  src: url('./assets/font/优设标题黑.ttf');
}

@font-face {
  font-family: '庞门正道标题体';
  src: url('./assets/font/庞门正道标题体.ttf');
}

.bg-full {
  background-size: 100% 100% !important;
}

.ant-select-arrow .anticon > svg {
  display: none;
}

.ant-select-arrow::after {
  content: '';
  display: block;
  width: 18px;
  height: 18px;
  background: url('./assets/arrow-down-s-line.svg') no-repeat center;
  background-size: contain;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55); /* 使用弹性曲线 */
  transform-origin: center 45%; /* 调整旋转支点 */
}

.ant-select-open .ant-select-arrow::after {
  transform: rotate(180deg) scale(1) translateY(-2px); /* 组合动画效果 */
  filter: brightness(1); /* 增加亮度 */
}

/* 全局滚动条样式 */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(193, 193, 193, 0); /* 初始透明 */
  border-radius: 3px;
  transition: background 0.3s ease;
}

*:hover::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 悬停时显示 */
}

*::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox 兼容 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(193, 193, 193, 0) #f1f1f1; /* 初始透明 */
  transition: scrollbar-color 0.3s ease;
}

*:hover {
  scrollbar-color: #c1c1c1 #f1f1f1; /* 悬停时显示 */
}
