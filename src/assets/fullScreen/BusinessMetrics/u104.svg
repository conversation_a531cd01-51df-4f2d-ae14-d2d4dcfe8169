﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="27px" height="27px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip97">
      <path d="M 0 13.5  C 0 5.9399999999999995  5.9399999999999995 0  13.5 0  C 21.060000000000002 0  27 5.9399999999999995  27 13.5  C 27 21.060000000000002  21.060000000000002 27  13.5 27  C 5.9399999999999995 27  0 21.060000000000002  0 13.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -314 -641 )">
    <path d="M 0 13.5  C 0 5.9399999999999995  5.9399999999999995 0  13.5 0  C 21.060000000000002 0  27 5.9399999999999995  27 13.5  C 27 21.060000000000002  21.060000000000002 27  13.5 27  C 5.9399999999999995 27  0 21.060000000000002  0 13.5  Z " fill-rule="nonzero" fill="rgba(255, 244, 92, 0.09803921568627451)" stroke="none" transform="matrix(1 0 0 1 314 641 )" class="fill" />
    <path d="M 0 13.5  C 0 5.9399999999999995  5.9399999999999995 0  13.5 0  C 21.060000000000002 0  27 5.9399999999999995  27 13.5  C 27 21.060000000000002  21.060000000000002 27  13.5 27  C 5.9399999999999995 27  0 21.060000000000002  0 13.5  Z " stroke-width="4" stroke-dasharray="0" stroke="rgba(255, 244, 92, 1)" fill="none" transform="matrix(1 0 0 1 314 641 )" class="stroke" mask="url(#Clip97)" />
  </g>
</svg>