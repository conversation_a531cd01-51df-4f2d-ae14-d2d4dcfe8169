/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { Outlet } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { Layout } from 'antd';
import classNames from 'classnames';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import Sider from '@/components/Sider';

const { Content } = Layout;

const PageLayout: React.FC = () => {
  const { pathname } = useLocation();

  return (
    <Layout className="w-full h-full relative flex flex-col">
      <Header />
      <Layout className="flex-1 relative mt-[48px]">
        {['/', '/usercenter'].indexOf(pathname) === -1 ? <Sider /> : undefined}
        <Content className="flex-1 p-4 overflow-auto flex flex-col">
          <Outlet />
          <Footer></Footer>
        </Content>
      </Layout>
    </Layout>
  );
};

export default PageLayout;
