/* eslint-disable array-callback-return */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable no-template-curly-in-string */
// 可视化大屏头部
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { converPxToVH, converPxToVW } from '@/utils';
import { LogoutOutlined } from '@ant-design/icons';
import classnames from 'classnames';

const leftNavItemList = [
  {
    key: 'monitoringOverview',
    label: '监测概况',
    url: '/dataVisualizationDashboard/monitoringOverview',
  },
  {
    key: 'dataCockpit',
    label: '数据驾驶舱',
    url: '/dataVisualizationDashboard/dataCockpit',
  },
  {
    key: 'businessMetrics',
    label: '业务指标',
    url: '/dataVisualizationDashboard/businessMetrics',
  },
  {
    key: 'qualityControlMetrics',
    label: '质控指标',
    url: '/dataVisualizationDashboard/qualityControlMetrics',
  },
  {
    key: 'monitoringEarlyWarning',
    label: '监测预警',
    url: '/dataVisualizationDashboard/monitoringEarlyWarning',
  },
];

const rightNavItemList = [
  {
    key: 'laboratoryPortrait',
    label: '实验室画像',
    url: '/dataVisualizationDashboard/laboratoryPortrait',
  },
  {
    key: 'qualityControlOnline',
    label: '质控在线',
    url: '/dataVisualizationDashboard/qualityControlOnline',
  },
  {
    key: 'dataQualityControl',
    label: '数据质控',
    url: '/dataVisualizationDashboard/dataQualityControl',
  },
  {
    key: 'qualityAssessment',
    label: '质量考核',
    url: '/dataVisualizationDashboard/qualityAssessment',
  },
];

const routerKeys = [
  'monitoringOverview',
  'dataCockpit',
  'businessMetrics',
  'qualityControlMetrics',
  'monitoringEarlyWarning',
  'laboratoryPortrait',
  'qualityControlOnline',
  'dataQualityControl',
  'qualityAssessment',
];

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [curSeletedRouter, setCurSeletedRouter] = useState<string>();

  useEffect(() => {
    routerKeys?.map((_item) => {
      if (pathname?.includes(_item)) {
        setCurSeletedRouter(_item);
      }
    });
  }, [pathname]);

  const renderNavItem = (key: string, label: string, url: string) => {
    return (
      <div
        key={key}
        className={classnames(
          'bg-cover text-center leading-7 cursor-pointer',
          key === curSeletedRouter
            ? "bg-[url('/fullScreen/common/nav-hover.png')]"
            : "bg-[url('/fullScreen/common/nav-normal.png')]"
        )}
        style={{
          width: `${converPxToVW(127)}vw`,
          height: `${converPxToVH(36)}vh`,
        }}
        onClick={() => navigate(url)}
      >
        <span
          className={classnames(
            'bg-gradient-to-b  to-[#FFFFFF] inline-block bg-clip-text text-transparent',
            key === curSeletedRouter ? 'from-[#FFB746]' : 'from-[#86C2FF]'
          )}
        >
          {label}
        </span>
      </div>
    );
  };

  return (
    <div
      className="relative w-full bg-[url('/fullScreen/common/header-bg.png')] bg-cover text-center font-PangmengZhengdaoBiaoti leading-[70px]"
      style={{
        height: `${converPxToVH(96)}vh`,
        fontSize: `${converPxToVW(43)}vw`,
      }}
    >
      <span className="font-YousheBiaotiHei bg-gradient-to-b from-[#ffffff] to-[#47DFFF] inline-block bg-clip-text text-transparent">
        传染病病原监测信息系统
      </span>
      <div
        className="absolute bottom-[3px] left-[20px] flex flex-row flex-nowrap "
        style={{ fontSize: `${converPxToVW(17)}vw` }}
      >
        {leftNavItemList?.map((_item) =>
          renderNavItem(_item?.key, _item?.label, _item?.url)
        )}
      </div>
      <div
        className="absolute bottom-[3px] right-[20px] flex flex-row flex-nowrap"
        style={{ fontSize: `${converPxToVW(17)}vw` }}
      >
        {rightNavItemList?.map((_item) =>
          renderNavItem(_item?.key, _item?.label, _item?.url)
        )}
      </div>
      <div
        className=" absolute top-0 right-0 h-1/2 flex items-center gap-3 text-white text-base px-6 cursor-pointer"
        onClick={() => {
          navigate('/');
        }}
      >
        <span>回到首页</span>
        <LogoutOutlined />
      </div>
    </div>
  );
};

export default Header;
