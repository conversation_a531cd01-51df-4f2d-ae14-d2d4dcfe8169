/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout } from 'antd';
import bgImage from '@/assets/fullScreen/common/bg.png';
import Header from './header';

const FullScreenLayout: React.FC = () => {
  return (
    <Layout className="w-full h-full relative flex flex-col bg-[url('/fullScreen/common/bg.png')] bg-cover text-white">
      <Header />
      <Outlet />
    </Layout>
  );
};

export default FullScreenLayout;
