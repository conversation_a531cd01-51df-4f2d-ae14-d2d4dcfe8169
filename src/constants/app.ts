// APP 项目相关的常量配置
import { theme } from 'antd';
// import { MappingAlgorithm } from 'antd/es/config-provider/context';

// 项目名称
export const appTitle = '传染病病原监测信息系统';

// 项目主题风格
export const appThemeColor = '#1677ff';

// 项目主题模式 (常规/暗黑)
export const appThemeMode: any | undefined =
  theme.defaultAlgorithm;

// antd的主题变量
export const antdThemeMode: any =
{
  Slider: {
    /* slider滑块组件 */
    // dotSize: 50,
    // handleColor: "red"
  }
}
