/**
 * @TODO 全局常量配置
 */
import packageInfo from '../../package.json';
import {
  antdThemeMode,
  appThemeColor, // appThemeMode,
  appTitle,
} from './app';
import { codeMessage } from './http';

export const COMPANY = packageInfo.company;

export const APP_VERSION = packageInfo.version;

export {
  appThemeColor,
  // appThemeMode,
  appTitle,
  codeMessage,
  antdThemeMode,
};

export const QUERY_SUCCESS_MSG = '操作成功!';

/**
 * @TODO 全局 Code 定义
 */
export const codeDefinition: {
  QUERY_SUCCESS: number;
  CLIENT_REQUEST_PARAMS_ERROR: number;
  UNAUTHORIZED_ERROR: number;
  RESOURCE_NOT_FOUND: number;
  INTERNAL_SERVER_ERROR: number;
  POST_DATA_SUCCESS: string;
} = {
  QUERY_SUCCESS: 200,
  CLIENT_REQUEST_PARAMS_ERROR: 400,
  UNAUTHORIZED_ERROR: 401,
  RESOURCE_NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  POST_DATA_SUCCESS: '提交成功',
};

export const orgName = '贵州省疾病预防控制中心';
