/*
 * @Date: 2024-07-16 10:44:56
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-07-23 14:31:42
 * @FilePath: \xr-qc-jk-web\src\components\RichTextEditor\index.tsx
 * @Description: 富文本编辑器
 */
import { useImperativeHandle, useRef, useEffect, useState } from 'react';

import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import classNames from 'classnames';

type TRichTextEditorProps = {
  value?: string;
  onChange?: ReactQuill.ReactQuillProps['onChange'];
  cssStyle?: string;
  onRef?: any;
  readonly?:boolean
};

const RichTextEditor: React.FC<TRichTextEditorProps> = ({
  value,
  onChange,
  cssStyle,
  onRef,
  readonly
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleGetData: () => {
        console.log('富文本内容', content);
        
        return content;
      },
    };
  });

  const [content, setContent] = useState(''); // 存储富文本内容

  useEffect(() => {
    setContent(value || ''); // 初始化内容  
  }, [value])


  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    // if (onChange) {
    //   onChange(newContent);
    // }
  };



  return (
    <ReactQuill
      theme="snow"
      value={content}
       onChange={handleContentChange}
      className={classNames(cssStyle)}
      readOnly={readonly}
    />
  );
};

export default RichTextEditor;
