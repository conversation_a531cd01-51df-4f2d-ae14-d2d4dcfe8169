import React, { useEffect, useState } from 'react';
import { Checkbox, Input } from 'antd';
import './index.less';

const CheckboxWithOther: React.FC<{
  inline?: boolean;
  value?: string;
  disabled?: boolean;
  onChange?: (value: string) => void;
}> = ({ value, onChange, inline = false, disabled = false }) => {
  const [values, setValues] = useState<string[]>([]);
  const [otherText, setOtherText] = useState('');

  const options = [
    { label: '冷藏', value: '0' },
    { label: '常温', value: '1' },
    { label: '完好', value: '2' },
    { label: '撒漏/破损', value: '3' },
    { label: '冷冻', value: '5' },
    { label: '标识不清楚', value: '6' },
    { label: '其他', value: '4（）' },
  ];

  const onChangeItem = (v: any) => {
    setValues(v);
    onChange &&
      onChange(
        v
          .map((item: string) => {
            let t = item;
            if (t.startsWith('4')) {
              t = t.replace(/（.*?）/, `（${otherText}）`);
            }
            return t;
          })
          .join(',')
      );
  };

  const onChangeInput = (v: any) => {
    setOtherText(v.target.value);
    onChange &&
      onChange(
        values
          .map((item) => {
            let t = item;
            if (t.startsWith('4')) {
              t = t.replace(/（.*?）/, `（${v.target.value}）`);
            }
            return t;
          })
          .join(',')
      );
  };

  useEffect(() => {
    const arr = value?.split(',') || [];
    let text = '';
    let real = arr.map((item) => {
      if (item.startsWith('4')) {
        let r = item.match(/（(.*?)）$/);
        text = r && r[1] ? r[1] : '';
        item = '4（）';
      }
      return item;
    });
    setOtherText(text);
    setValues(real);
  }, [value]);

  return (
    <div className="CheckboxWithOther flex">
      <Checkbox.Group
        defaultValue={[]}
        value={values}
        onChange={onChangeItem}
        disabled={disabled}
      >
        <div className={`flex ${!inline ? 'flex-col' : 'flex-row'}`}>
          {options.map((item) => (
            <div key={item.value}>
              <Checkbox value={item.value}>{item.label}</Checkbox>
              {item.label === '其他' && (
                <>
                  {disabled ? (
                    otherText ? (
                      `(${otherText})`
                    ) : (
                      ''
                    )
                  ) : (
                    <Input
                      size="small"
                      className="w-[100px]"
                      value={otherText}
                      disabled={
                        !values.filter((item) => item.startsWith('4')).length
                      }
                      onChange={onChangeInput}
                    />
                  )}
                </>
              )}
            </div>
          ))}
        </div>
      </Checkbox.Group>
    </div>
  );
};

export default CheckboxWithOther;
