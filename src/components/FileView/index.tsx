/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { Image, Space } from 'antd';
import { downloadFile } from '@/api/file';
import {
  DownloadOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SwapOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from '@ant-design/icons';
import './index.less';

type TFileViewProps = {
  name: string;
  ossId: string;
  url: string;
};

type TRoleDetailProps = {
  open: boolean;
  file?: TFileViewProps;
  closeDetail: () => void;
};

const FileView: React.FC<TRoleDetailProps> = React.memo(
  ({ open, file, closeDetail }) => {
    useEffect(() => {
      if (open && file) {
        setVisible(true);
      }
    }, [open]);

    // 图片下载
    const onDownload = () => {
      file?.ossId && file?.name && downloadFile(file.ossId, file.name);
    };

    const [visible, setVisible] = useState(false);
    return (
      <>
        {file && (
          <div className="w-0 h-0 overflow-hidden file-view-container">
            <Image
              src={file.url}
              preview={{
                visible,
                src: file.url,
                getContainer: () => document.body,
                onVisibleChange: (value) => {
                  setVisible(value);
                  closeDetail();
                },
                toolbarRender: (
                  _,
                  {
                    transform: { scale },
                    actions: {
                      onFlipY,
                      onFlipX,
                      onRotateLeft,
                      onRotateRight,
                      onZoomOut,
                      onZoomIn,
                    },
                  }
                ) => (
                  <Space
                    size={24}
                    className="toolbar-wrapper text-[19px] rounded-[24px] bg-custom-gray px-8 py-2"
                  >
                    <DownloadOutlined onClick={onDownload} />
                    <SwapOutlined rotate={90} onClick={onFlipY} />
                    <SwapOutlined onClick={onFlipX} />
                    <RotateLeftOutlined onClick={onRotateLeft} />
                    <RotateRightOutlined onClick={onRotateRight} />
                    <ZoomOutOutlined
                      disabled={scale === 1}
                      onClick={onZoomOut}
                    />
                    <ZoomInOutlined
                      disabled={scale === 50}
                      onClick={onZoomIn}
                    />
                  </Space>
                ),
              }}
            />
          </div>
        )}
      </>
    );
  }
);

export default FileView;
