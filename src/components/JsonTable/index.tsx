/*
 * @Author: LGX
 * @Date: 2024-02-29 10:27:58
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-03 11:00:44
 * @FilePath: /xr-qc-jk-web/src/components/JsonTable/index.tsx
 * @Description: json生成动态表格组件
 *
 */
import React, {
  MutableRefObject,
  ReactElement,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Button, Form, message, Progress, Spin, Tooltip, Upload } from 'antd';
import type { UploadProps } from 'antd';
import { UploadFile } from 'antd/lib/upload/interface';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { useTokenStore } from '@/store';
import {
  CloseCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  EditableFormInstance,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import EEditableProTable from '@/components/EditableProTable';
import EFileView from '@/components/EFileView';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip';
import FileView from '@/components/FileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import EExcelFileView from '../EExcelFileView';

const formItemLayout = {
  span: 8,
};

type DataSourceType = any;
type ProjectPropsList = any;
type TFormProps = {
  formDataList?: any; // json结构及数据
  projectDataList?: any; // 样品项目数据
  readOnly?: boolean;
  canUpdate?: boolean; // 是否接收
  fileCheckTooltipTitle?: string; // 文件校验提示标题
  setTableData?: (_dataSource: any[]) => void;
  formRef?: ((ref: MutableRefObject<any>) => void) | MutableRefObject<any>;
};

const HeaderSearch: React.FC<TFormProps> = ({
  formDataList,
  readOnly = false,
  canUpdate,
  formRef,
  projectDataList,
}) => {
  const { token } = useTokenStore();

  // 当前表格ref
  const formOnRef = useRef<ProFormInstance>();
  const editableFormRef = useRef<EditableFormInstance>(null);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState<boolean>(false);
  // 预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  const [isShowFileData, setIsShowFileData] = useState<any>();
  // 是否有正在上传的文件
  const [isUpload, setIsUpload] = useState<boolean>(false);

  const [editableKeys, setEditableKeys] = useState<any[]>();

  const [formData, setFormData] = useState<any[]>([]);

  const formMethods = {
    getFormInfo: () => [getFormInfo()],
  };

  useImperativeHandle(formRef, () => {
    if (formOnRef.current) {
      // 将原始表单实例的属性和方法与自定义方法结合在一起
      return { current: { ...formOnRef.current, ...formMethods } };
    }
    // 如果 formOnRef.current 未定义，则返回一个空的 MutableRefObject 对象
    return { current: {} as any };
  });

  // 文件上传配置
  const uploadProps: UploadProps | any = {
    action: uploadFiles,
    headers: { Authorization: `Bearer ${token}` },
    multiple: true,
    listType: 'text',
    className: 'upload-list-inlines',
    autoUpload: true,
    showUploadList: {
      showDownloadIcon: true,
      downloadIcon: <DownloadOutlined />,
      showRemoveIcon: !readOnly,
    },
    // 预览
    async onPreview(file: any) {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.data &&
        file.response.data.ossId
      ) {
        const { fileName, url, ossId } = file.response.data;
        const type = getFileTypeByName(fileName);
        if (type === 'Image') {
          const d = await getFileData(ossId);
          setIsShowFileData({
            name: fileName,
            url: d,
            ossId: ossId,
          });
          setIsShowFileView(true);
        } else {
          setFileUrl(url);
          setOpenPreview(true);
        }
      }
    },
    // 下载
    async onDownload(file: any) {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.data &&
        file.response.data.ossId
      ) {
        const { ossId, fileName } = file.response.data;
        downloadFile(ossId, fileName);
      }
    },
    // 缩略图
    iconRender: (file: any) => {
      return (
        <>
          {file.response ? (
            <img
              src={getIconByName(file.name)}
              className="!w-[15px] !h-[15px] m-auto mt-2"
              alt="logo"
            />
          ) : (
            <LoadingOutlined />
          )}
        </>
      );
    },
    // 文件列表
    itemRender: (
      originNode: any,
      file: any,
      fileList: object[],
      actions: { download: any; preview: any; remove: any }
    ) => {
      let isError = false; // 上传失败的文件
      if (file.status === 'done') {
        const { code, data } = file.response;
        if (code !== 200) {
          isError = true;
        } else {
          isError = false;
        }
      }
      return (
        <>
          {isError ? (
            <>
              <div className="file-upload-footer-title">
                {file.response ? (
                  <>
                    {
                      // 文件上传失败
                      isError ? (
                        <CloseCircleOutlined className="!w-[15px] !h-[15px] mr-1 text-[red]" />
                      ) : (
                        <img
                          src={getIconByName(file.name)}
                          className="!w-[15px] !h-[15px] mr-1"
                          alt="logo"
                        />
                      )
                    }
                  </>
                ) : (
                  <LoadingOutlined className="mr-1" />
                )}
                <EllipsisWithTooltip
                  onClick={actions.preview}
                  error={isError ? '上传失败' : ''}
                  className={`name ${!isError ? '' : 'text-[red]'}`}
                  text={file.name}
                />
                {file.status !== 'uploading' && (
                  <div className="operation">
                    {/* 下载按钮 */}
                    {!isError && (
                      <DownloadOutlined
                        onClick={() => actions.download()}
                        className="icon"
                      />
                    )}
                    {/* 删除按钮 */}
                    {!readOnly && (
                      <DeleteOutlined
                        onClick={() => actions.remove()}
                        className={`icon ${isError ? 'is-error' : ''}`}
                      />
                    )}
                  </div>
                )}
              </div>
              {file.status === 'uploading' && (
                <Progress
                  className="progress"
                  percent={file.percent}
                  showInfo={false}
                  size="small"
                  strokeColor="#52C41A"
                />
              )}
            </>
          ) : (
            originNode
          )}
        </>
      );
    },
  };
  const init = async () => {
    try {
      const temp: any = setup(cloneDeep(formDataList));
      temp?.length &&
        temp.unshift({
          title: '考核项目',
          dataIndex: 'itemName',
          fixed: 'left',
          width: 120,
          editable: false,
          render: (title: any, recoded: any) => {
            return (
              <div>
                {title}
                {recoded.tip ? (
                  <Tooltip title={recoded.tip} className="cursor-help">
                    <QuestionCircleOutlined className="text-[#949494] ml-1" />
                  </Tooltip>
                ) : null}
              </div>
            );
          },
        });
      setFormData(temp);
    } catch (error) {
      console.log(error);
    }
  };

  const setup = (orgData: any[]) => {
    return orgData?.map((item: any) => {
      if (item.inputBox === 'text') {
        return {
          editable: !readOnly,
          disable: true,
          width: 240,
          title: (
            <div>
              {Number(item.isRequired) === 1 ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.id,
          initialValue: item.fieldValue,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired) === 1,
                message: '此项为必填项',
              },
            ],
          },
          fieldProps: {
            maxLength: 200,
          },
        };
      } else if (item.inputBox === 'select') {
        const enums: any = {};
        if (item.enumItems) {
          item.enumItems.split(',')?.map((en: any) => {
            enums[en] = {
              text: en,
            };
          });
        }
        return {
          editable: !readOnly,
          width: 240,
          title: (
            <div>
              {Number(item.isRequired) === 1 ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.id,
          initialValue: item.fieldValue,
          valueType: 'select',
          valueEnum: enums,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired) === 1,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'digit') {
        return {
          editable: !readOnly,
          width: 240,
          title: (
            <div>
              {Number(item.isRequired) === 1 ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.id,
          initialValue: item.fieldValue,
          valueType: 'digit',
          fieldProps: {
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired) === 1,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'calendar') {
        return {
          editable: !readOnly,
          width: 240,
          title: (
            <div>
              {Number(item.isRequired) === 1 ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.id,
          initialValue: item.fieldValue,
          valueType: 'date',
          fieldProps: {
            format: 'YYYY-MM-DD',
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired) === 1,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'file') {
        return {
          width: 240,
          title: (
            <div>
              {Number(item.isRequired) === 1 ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.id,
          colProps: { span: 24 },
          // width: 240,
          formItemProps: {
            labelWrap: true,
            className: 'inspect-upload-style',
            tooltip: item.pomptInformation,
            labelCol: {
              span: 1.5,
            },
            rules: [
              {
                required: Number(item.isRequired) === 1,
                message: '此文件必须上传',
              },
            ],
          },
          renderFormItem: ({ entry }: Record<string, any>) => {
            let fileLists = entry[item.id];
            if (
              !Array.isArray(fileLists) &&
              typeof fileLists === 'string' &&
              fileLists.startsWith('[')
            ) {
              fileLists = JSON.parse(fileLists);
            }
            // 文件修改
            const uploadOnChange = async (info: any) => {
              const { file } = info;
              if (file.status === 'uploading') {
                setIsUpload(true);
              } else {
                setIsUpload(false);
              }
              if (file.status === 'done') {
                if (
                  file.status === 'done' &&
                  file.response &&
                  file.response.code !== 200
                ) {
                  // 文件上传失败
                  const newFileData = {
                    name: file.name,
                    status: 'error',
                    response: '文件上传错误,请重新上传',
                    url: '',
                    uid: '2323',
                  };
                  const currentFormData =
                    formOnRef?.current?.getFieldsValue()?.table || [];
                  const updatedFormData = currentFormData.map((row: any) => {
                    if (row.id === entry.id) {
                      row = {
                        ...row,
                        [item.id]: [...row[item.id], newFileData],
                      };
                    }
                    return row;
                  });
                  // 设置新的表单值
                  formOnRef?.current?.setFieldValue('table', updatedFormData);
                  message.error('上传失败');
                } else {
                  // 文件上传成功
                  const { data, msg } = file.response;
                  const newFileData = file;
                  const currentFormData =
                    formOnRef?.current?.getFieldsValue()?.table || [];
                  const updatedFormData = currentFormData.map((row: any) => {
                    if (row.id === entry.id) {
                      row = {
                        ...row,
                        [item.id]: [...row[item.id], newFileData],
                      };
                    }
                    return row;
                  });
                  // 设置新的表单值
                  formOnRef?.current?.setFieldValue('table', updatedFormData);
                  message.success(msg);
                }
              } else if (file.status === 'removed') {
                const currentFormData =
                  formOnRef?.current?.getFieldsValue()?.table || [];
                const nowItem = currentFormData.filter(
                  (item: any) => item.id === entry.id
                );
                // 删除后的文件arr
                const newFileItem = nowItem[0][item.id].filter(
                  (item: any) => item.ossId !== file.ossId
                );
                const newData = {
                  ...nowItem[0],
                  [item.id]: newFileItem,
                };
                const updatedFormData = currentFormData.map((row: any) => {
                  if (row.id === entry.id) {
                    return newData;
                  }
                  return row;
                });
                // 设置新的表单值
                formOnRef?.current?.setFieldValue('table', updatedFormData);
                message.success('操作成功');
              }
            };
            return (
              <ProForm.Item noStyle name={item.id}>
                {fileLists?.length > 0 || !readOnly ? (
                  <>
                    <Upload
                      {...uploadProps}
                      onChange={uploadOnChange}
                      defaultFileList={fileLists}
                    >
                      {!readOnly && (
                        <Button icon={<UploadOutlined />}>点击上传</Button>
                      )}
                    </Upload>
                    {/* <div className='text-[red] text-[12px]'>此文件必须上传</div> */}
                  </>
                ) : (
                  <div>-</div>
                )}
              </ProForm.Item>
            );
          },
        };
      }
    });
  };

  /**
   * @TODO 判断必填文件上传状态
   * @param 文件数组
   */
  const determineFileStatus = (arrFile: any[]) => {
    if (!arrFile) return '';
    let fileTooltip = '';
    // 校验文件是否上传成功
    const tempStatus1 = isUpload;
    const tempStatus2 = arrFile?.some(
      (item: any) => item.response && !item.response?.data
    );
    if (tempStatus1) {
      fileTooltip = '正在上传中';
    } else if (tempStatus2) {
      fileTooltip = '上传失败';
    }
    return fileTooltip;
  };

  // 数据处理
  const getFormInfo = async () => {
    try {
      // 表格数据
      const formDataArr = await formOnRef?.current?.getFieldsValue().table;
      let fileLit: any[] = []; // 提取已上传的所有文件
      // 组装处理数据
      const data = formDataArr?.map((item: any) => {
        // 删除提示信息字段tip
        delete item.tip;
        for (const i in item) {
          for (const e of formDataList) {
            if (e.id === Number(i)) {
              if (e.inputBox && e.inputBox === 'calendar') {
                item[i] = item[i]
                  ? dayjs(item[i]).format('YYYY-MM-DD')
                  : undefined;
              } else if (e.inputBox && e.inputBox === 'file') {
                let fileObj: any = [];
                if (item && item[i] && Array.isArray(item[i])) {
                  fileLit = [...fileLit, ...item[i]];
                } else {
                  fileLit = [...fileLit];
                }
                if (item && item[i] && Array.isArray(item[i])) {
                  fileObj = item[i]
                    .filter(
                      (item: any) =>
                        item.response &&
                        item.response.data &&
                        item.response.data.ossId
                    )
                    .map((item: any) => {
                      return {
                        name: item.response.data.fileName,
                        ossId: item.response.data.ossId,
                        url: item.response.data.url,
                      };
                    });
                }
                item[i] = JSON.stringify(fileObj);
              }
            }
          }
        }
        return item;
      });
      // 校验已上传文件是否都上传成功了
      const fileTooltip = determineFileStatus(fileLit);
      if (fileTooltip) {
        if (fileTooltip === '上传失败') {
          throw `样本检验信息有${fileTooltip}的文件,请删除后再提交`;
        } else {
          throw `样本检验信息有${fileTooltip}的文件,请等待上传完成`;
        }
      }
      return data;
    } catch (e) {
      console.error(e);
      throw new Error(`${e}`);
    }
  };

  useEffect(() => {
    // 处理初始表头
    formDataList?.length > 0 && init();
  }, [formDataList]);

  useEffect(() => {
    const newProjectDataList = projectDataList.map((item: any) => {
      let Item: any = {
        id: item.id,
        itemName: item.itemName,
        tip: item.tip ?? '',
      };
      // 详情回显
      if (item?.valueObject || item?.valueObject?.length) {
        item?.valueObject?.forEach((item2: any) => {
          formDataList.forEach((item3: any) => {
            if (item2.templateInstanceItemInfoId === item3.id) {
              if (item3.inputBox === 'file') {
                try {
                  Item[item2.templateInstanceItemInfoId] = item2.value
                    ? JSON.parse(item2.value).map((item: any) => {
                        return {
                          uid: item.ossId,
                          name: item.name,
                          status: 'done',
                          type: 'application/msword',
                          response: {
                            data: {
                              fileName: item.name,
                              ossId: item.ossId,
                              url: item.url,
                            },
                            code: 200,
                          },
                        };
                      })
                    : [];
                } catch (error) {
                  Item[item2.templateInstanceItemInfoId] = [];
                }
              } else {
                Item[item2.templateInstanceItemInfoId] = item2.value;
              }
            }
          });
        });
      } else {
        // 新增初始值
        formData.forEach((item4: any) => {
          if (item4.title !== '考核项目') {
            Item[item4.dataIndex] = undefined;
          }
        });
      }
      return Item;
    });
    formOnRef.current?.setFieldValue('table', newProjectDataList);
    // 可编辑key
    const keyArr = newProjectDataList?.map((item: ProjectPropsList) => item.id);
    setEditableKeys(keyArr);
  }, [projectDataList, formData]);

  useEffect(() => {
    if (typeof formRef === 'function') {
      formRef && formRef(formOnRef as MutableRefObject<ProFormInstance>);
    }
  }, []);

  return (
    <>
      <ProForm<{ table: DataSourceType[] }>
        key="form"
        className="w-full"
        formRef={formOnRef}
        {...formItemLayout}
        layout="horizontal"
        grid={true}
        submitter={false}
        initialValues={{
          table: projectDataList,
        }}
        validateTrigger="onBlur"
      >
        <div className="w-full overflow-hidden relative">
          <EEditableProTable<DataSourceType>
            search={false}
            rowKey="id"
            name="table"
            editableFormRef={editableFormRef}
            recordCreatorProps={false}
            columns={formData as any}
            editable={{
              type: 'multiple',
              editableKeys: editableKeys,
              onChange: setEditableKeys,
            }}
            scroll={{ x: 'max-content' }}
          />
        </div>
      </ProForm>
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
      {/* 预览pdf\word */}

      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </>
  );
};

export default HeaderSearch;
