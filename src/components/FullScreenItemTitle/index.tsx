/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Date: 2024-08-22 10:37:40
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-25 17:43:39
 * @FilePath: /xr-qc-jk-web/src/components/FullScreenItemTitle/index.tsx
 * @Description: 大屏公共标题
 */
import { ReactNode, useCallback } from 'react';
import title_big from '@/assets/fullScreen/common/title-big.png';
import title_dec from '@/assets/fullScreen/common/title-dec.png';
import title_middle from '@/assets/fullScreen/common/title-middle.png';
import title_small from '@/assets/fullScreen/common/title-small.png';
import { converPxToVH, converPxToVW } from '@/utils';

type TFullScreenItemTitleProps = {
  size?: 'small' | 'middle' | 'big';
  title: string;
  extra?: ReactNode;
};

const FullScreenItemTitle: React.FC<TFullScreenItemTitleProps> = ({
  size = 'small',
  title,
  extra,
}) => {
  const getBg = useCallback(() => {
    if (size === 'small') {
      return title_small;
    } else if (size === 'middle') {
      return title_middle;
    } else {
      return title_big;
    }
  }, [size]);
  return (
    <div className=" w-full relative">
      {/* <img alt="" src={getBg()} className=" w-full h-full" />
      <div className=" absolute top-0 left-0 w-full h-full flex items-center px-4 justify-between">
        <div className=" flex items-center gap-3">
          <img alt="" src={title_dec} className=" w-[18px] h-[18px]" />
          <span className=" text-white text-xl font-normal">{title}</span>
        </div>
        <div>{extra}</div>
      </div> */}
      <div
        className="w-full bg-[url('/fullScreen/common/title-middle.png')] bg-no-repeat bg-full flex flex-row flex-nowrap items-center justify-between"
        style={{
          height: `${converPxToVH(35)}vh`,
          paddingLeft: `${converPxToVW(16)}vw`,
        }}
      >
        <div className="flex gap-3 items-center">
          <img
            src={title_dec}
            alt=""
            style={{
              width: `${converPxToVW(18)}vw`,
              height: `${converPxToVW(18)}vw`,
            }}
          />
          <div
            className="font-PangmengZhengdaoBiaoti"
            style={{ fontSize: `${converPxToVW(18)}vw` }}
          >
            {title}
          </div>
        </div>
        <div>{extra}</div>
      </div>
    </div>
  );
};

export default FullScreenItemTitle;
