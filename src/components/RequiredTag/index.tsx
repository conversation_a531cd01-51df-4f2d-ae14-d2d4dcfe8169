/*
 * @Author: LGX
 * @Date: 2024-03-06 17:53:24
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-06 17:59:03
 * @FilePath: \xr-qc-jk-web\src\components\RequiredTag\index.tsx
 * @Description: 必填标识组件
 * 
 */
import React from 'react';


type TFormProps = {
  title?: string; // 标题
  isRequired?: boolean; // 是否显示必填标识
};
const RequiredTag: React.FC<TFormProps> = ({ title = "-", isRequired = true }) => {

  return (
    <>
      <div>
        {isRequired ? <span className='text-[red] mr-1'>*</span> : null}
        <span>{title}</span>
      </div>
    </>
  );
};


export default RequiredTag;
