/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import { changePassword } from '@/api/login';
import { submitLogout } from '@/api/login';
import { codeDefinition } from '@/constants';
import { useInfoStore, useTokenStore } from '@/store';
import {
  ProForm,
  ProFormDependency,
  ProFormText,
} from '@ant-design/pro-components';
import { passwordReg } from '@/utils/validators';

const FormInfoInit = {};

type TChangePasswordDialogProps = {
  open: boolean;
  hide: () => void;
};

const ChangePasswordDialog: React.FC<TChangePasswordDialogProps> = ({
  open,
  hide,
}) => {
  const navigate = useNavigate();
  const { setToken } = useTokenStore();
  const { setUserInfo } = useInfoStore();

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    hide();
    formRef.current?.resetFields();
  };

  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await changePassword(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('修改密码成功，请重新登录');
        close();
        submitLogout();
        setToken('');
        setUserInfo({});
        navigate('/login');
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title="修改密码"
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        className="pt-4"
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        onFinish={handleSave}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText.Password
          label="原密码"
          name="oldPassword"
          rules={[{ required: true, message: '请输入原密码' }]}
          placeholder="请输入原密码"
        />
        <ProFormText.Password
          label="新密码"
          name="newPassword"
          rules={[
            { required: true, message: '请输入新密码' },
            {
              required: true,
              validator(rule, value, callback) {
                const reg =
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@&%#_])[a-zA-Z0-9~!@&%#_]{8,16}$/;
                if (!passwordReg.test(value)) {
                  callback(
                    '密码必须包含一个大写，一个小写字母，一个特殊字符，且长度为8到16位'
                  );
                } else {
                  callback();
                }
              },
            },
          ]}
          placeholder="请输入新密码"
        />
        <ProFormDependency name={['newPassword']}>
          {({ newPassword }) => {
            return (
              <ProFormText.Password
                label="再次输入新密码"
                name="newPassword2"
                rules={[
                  {
                    required: true,
                    validator(rule, value, callback) {
                      if (value !== newPassword) {
                        callback('两次输入的密码不一致');
                      } else {
                        callback();
                      }
                    },
                  },
                ]}
                placeholder="请再次输入新密码"
              />
            );
          }}
        </ProFormDependency>
      </ProForm>
    </Modal>
  );
};

export default ChangePasswordDialog;
