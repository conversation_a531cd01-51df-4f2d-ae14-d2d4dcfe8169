import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar, Dropdown, MenuProps, message } from 'antd';
import { submitLogout } from '@/api/login';
import { useInfoStore, useTokenStore } from '@/store';
import { CaretDownOutlined, UserOutlined } from '@ant-design/icons';
import ChangePasswordDialog from './ChangePasswordDialog';
import './index.less';

const Account: React.FC = () => {
  const navigate = useNavigate();
  const { setToken } = useTokenStore();
  const { userInfo, setUserInfo } = useInfoStore();
  const handleLogOutEvent = async () => {
    const { code, msg } = await submitLogout();
    if (code === 200) {
      // 清空 Store 中的 token
      setToken('');
      setUserInfo({});
      // 跳转到登录页面
      navigate('/login');
    } else {
      message.error(msg);
    }
  };

  // 修改密码
  const [isShowChangePasswordDialog, setIsShowChangePasswordDialog] =
    useState(false);
  const handleChangePasswordEvent = async () => {
    setIsShowChangePasswordDialog(true);
  };
  // 用户中心
  const handleGoUserCenterEvent = async () => {
    navigate('/usercenter');
  };

  const items: MenuProps['items'] = [
    {
      key: '3',
      label: (
        <div rel="noopener noreferrer" onClick={handleGoUserCenterEvent}>
          个人中心
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div rel="noopener noreferrer" onClick={handleChangePasswordEvent}>
          修改密码
        </div>
      ),
    },
    {
      key: '1',
      label: (
        <div rel="noopener noreferrer" onClick={handleLogOutEvent}>
          退出登录
        </div>
      ),
    },
  ];
  const handleOpenDictionary = () => {
    window.open('http://59.215.193.211:5080/standard/', '_blank');
  };
  return (
    <div className='right-login-out'>
      <div className='visual-link' onClick={handleOpenDictionary}>病原检测数据字典</div>
      <Dropdown menu={{ items }} placement="bottomRight" arrow>
        <div className="w-full flex flex-row flex-nowrap justify-center items-center gap-1 leading-none">
          <Avatar icon={<UserOutlined />} size="small" />
          {userInfo && userInfo.user ? (
            <span className="pl-2">
              {userInfo.user.nickName || userInfo.user.userName || '用户名'}
              <CaretDownOutlined />
            </span>
          ) : undefined}
        </div>
      </Dropdown>
      <ChangePasswordDialog
        open={isShowChangePasswordDialog}
        hide={() => {
          setIsShowChangePasswordDialog(false);
        }}
      />
    </div>
  );
};

export default Account;
