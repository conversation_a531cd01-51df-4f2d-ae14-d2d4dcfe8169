/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Menu, type MenuProps } from 'antd';
import * as Icon from '@ant-design/icons';
import { usePermissionRouterStore } from '@/store';
import { cloneDeep } from 'lodash';

const AppMenu: React.FC<MenuProps> = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { childMenuData } = usePermissionRouterStore();

  /**
   * @TODO 刷新页面时根据当前的 pathname 自动选中该模块
   */
  const handleDefaultSelectedMenuItem = (): string[] => {
    const _result: string[] = [pathname];
    return _result;
  };

  // 处理默认展开的菜单
  const handleDefaultOpenKeys = (): string[] => {
    const pathSegments = pathname.split('/').filter(Boolean);
    if (pathSegments.length >= 3) {
      // 如果路径有三层或更多层，返回第二层路径作为需要展开的key
      return [`/${pathSegments[0]}/${pathSegments[1]}`];
    }
    return [];
  };

  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<string[]>(() =>
    handleDefaultSelectedMenuItem()
  );
  const [defaultOpenKeys, setDefaultOpenKeys] = useState<string[]>(() =>
    handleDefaultOpenKeys()
  );

  const [menuList, setMenuList] = useState<any>([]);

  useEffect(() => {
    setDefaultSelectedKeys(() => handleDefaultSelectedMenuItem());
    setDefaultOpenKeys(() => handleDefaultOpenKeys());
  }, [pathname]);

  // 创建icon图标元素
  const iconToElement = (name: string) => {
    if ((Icon as any)[name]) {
      return React.createElement(Icon && (Icon as any)[name], {
        style: { fontSize: '16px' },
      });
    } else {
      return '';
    }
  };

  // 控制左侧菜单显示
  useEffect(() => {
    if (childMenuData.length) {
      const _childMenuData = setLabelNode(childMenuData);
      const menu: any = [];
      _childMenuData.forEach((item: any) => {
        let m = { ...item };
        m.icon = item.icon ? iconToElement(item.icon) : null;
        menu.push(m);
      });
      setMenuList(menu);
    }
  }, [childMenuData]);

  const setLabelNode = (data: any) => {
    const _newData = cloneDeep(data);
    _newData.forEach((_item: any) => {
      _item.label = <span title={_item.label}>{_item.label}</span>;
      if (_item.children && _item.children.length) {
        _item.children = setLabelNode(_item.children);
      }
    });
    return _newData;
  };

  return (
    <Menu
      mode="inline"
      selectedKeys={defaultSelectedKeys}
      defaultOpenKeys={defaultOpenKeys}
      items={menuList}
      //@ts-ignore
      onClick={(item) => {
        navigate(item.key || '/');
      }}
    />
  );
};

export default AppMenu;
