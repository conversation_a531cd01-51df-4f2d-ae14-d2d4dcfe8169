/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  专门针对 Excel 文件的预览组件
 */
import React, { useEffect, useState } from 'react';
import { Button, Modal, Table } from 'antd';
import { set } from 'lodash';
import { read, utils } from 'xlsx';

type TExcelFileViewProps = {
  blobUrl: any; // 预览地址
  open: boolean;
  close: () => void;
  previewType?: string;
};

const EExcelFileView: React.FC<TExcelFileViewProps> = ({
  blobUrl,
  open,
  close,
}) => {
  // 解析后的数据
  const [pres, setPres] = useState<any[]>([]);
  // 表格头数据
  const [columns, setColumns] = useState<any[]>([]);

  /**
   * 关闭Modal
   */
  const handleClose = () => {
    close();
  };

  /* Fetch and update the state once */
  useEffect(() => {
    (async () => {
      /* 从传入了 excel 文件地址下载源数据 */
      const f = await fetch(blobUrl);
      const ab = await f.arrayBuffer();

      /* 解析 */
      const wb = read(ab);

      const ws = wb.Sheets[wb.SheetNames[0]];
      const data: any[] = utils.sheet_to_json(ws);

      setPres(data);

      // 生成表格头数据
      if (data?.length) {
        const _columns: any[] = [];
        Object.keys(data[0]).forEach((_title) => {
          _columns.push({
            title: _title,
            dataIndex: _title,
            key: _title,
          });
        });
        setColumns(_columns);
      }
    })();
  }, []);

  return (
    <Modal
      title="文件预览"
      centered
      width="70vw"
      open={open}
      onCancel={close}
      footer={[
        <Button key="close" onClick={handleClose}>
          关闭
        </Button>,
      ]}
    >
      <Table columns={columns} dataSource={pres} pagination={false} />
    </Modal>
  );
};

export default EExcelFileView;
