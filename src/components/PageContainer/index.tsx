import React from 'react';
import { Card } from 'antd';

interface IBlockContainer {
  title?: string;
  extra?: React.ReactNode;
  children: React.ReactNode;
}

const BlockContainer: React.FC<IBlockContainer> = ({
  title,
  extra,
  children,
}) => {
  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1">{children}</div>
    </div>
  );
};

export default BlockContainer;
