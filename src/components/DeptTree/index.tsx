import React, { useEffect, useState } from 'react';
import { message, Skeleton, Tree } from 'antd';
import { getParentDept } from '@/api/department';
import { codeDefinition } from '@/constants';
import { IDeptTree } from '@/pages/System/Dept/type';

type TDeptTreeProps = {
  selectedKeys: React.Key[];
  setSelectedKeys: (val: React.Key[]) => void;
};

const DeptTree: React.FC<TDeptTreeProps> = ({
  selectedKeys,
  setSelectedKeys,
}) => {
  const [treeData, setTreeData] = useState<IDeptTree[]>([]);

  /**
   * @TODO 获取部门树数据
   */
  const getTreeData = async () => {
    try {
      const { code, data, msg } = await getParentDept();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setTreeData(data);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    getTreeData();
  }, []);

  /**
   * @description 选择部门
   */
  const handleSelected = (selectedKeys: React.Key[]) => {
    setSelectedKeys(selectedKeys);
  };
  return (
    <div className=" bg-white max-h-[85vh] h-full flex flex-col overflow-hidden">
      <div className="secondary-title p-2 text-lg">机构列表</div>
      <div className='flex-1 overflow-auto'>
        {treeData.length ? (
          <Tree
            showLine
            defaultExpandAll={true}
            treeData={treeData as any}
            fieldNames={{ key: 'id', title: 'label' }}
            onSelect={handleSelected}
            selectedKeys={selectedKeys}
          />
        ) : (
          <Skeleton active />
        )}
      </div>
    </div>
  );
};

export default DeptTree;
