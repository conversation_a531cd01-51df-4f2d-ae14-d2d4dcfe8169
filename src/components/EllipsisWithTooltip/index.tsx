/*
 * @Author: LGX
 * @Date: 2024-04-07 09:46:30
 * @LastEditors: LGX
 * @LastEditTime: 2024-04-09 10:56:32
 * @FilePath: \xr-qc-jk-web\src\components\EllipsisWithTooltip\index.tsx
 * @Description: 文字超出显示省略号
 * 
 */
import React, { useState, useRef, useEffect } from 'react';
import { Tooltip, TooltipProps } from 'antd';

interface EllipsisWithTooltipProps {
    text: string;
    tooltipProps?: TooltipProps;
    time?: number;
    className?: string;
    error?: string;
    onClick: () => void;
}

const EllipsisWithTooltip = React.memo<EllipsisWithTooltipProps>(({ text, tooltipProps, error = 0, className, onClick }) => {
    const [showTooltip, setShowTooltip] = useState(true);
    const hoverTimerRef = useRef<number | null>(null);
    const wrapperRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        const checkTextOverflow = () => {
            if (wrapperRef.current) {
                const isOverflowing = wrapperRef.current.scrollWidth > wrapperRef.current.offsetWidth;
                setShowTooltip(isOverflowing);
            }
        };
        checkTextOverflow();
        const resizeObserver = new ResizeObserver(checkTextOverflow);
        if (wrapperRef.current) {
            resizeObserver.observe(wrapperRef.current);
        }
        return () => {
            resizeObserver.disconnect();
        };
    }, [showTooltip]);

    return (
        <div
            ref={wrapperRef}
            className={`cursor-pointer ${className}`}
            style={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
            onClick={onClick}
        >
            {showTooltip || error ? (
                <Tooltip placement="left" {...tooltipProps} title={error ? error : text}>
                    {text}
                </Tooltip>
            ) : (
                text
            )}
        </div>
    );
});

export default EllipsisWithTooltip;