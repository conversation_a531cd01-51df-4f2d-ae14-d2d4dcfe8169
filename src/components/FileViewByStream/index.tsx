/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/iframe-has-title */
import React, { useEffect, useRef, useState } from 'react';
import { message, Spin, Table } from 'antd';
import * as XLSX from 'xlsx';
import { getFileStream, getFileStreamForPreview } from '@/api/file';
import mammoth from 'mammoth';
import PptxGenJS from 'pptxgenjs';

interface FileProps {
  fileId: string;
  isPreview?: boolean; // 是否是预览
}

const FileViewer: React.FC<FileProps> = ({ fileId, isPreview = false }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<JSX.Element | null>(null);
  const [tableData, setTableData] = useState<{
    dataSource: any[];
    columns: any[];
  }>({
    dataSource: [],
    columns: [],
  });

  const [loading, setLoading] = useState(false);
  const isLoadingRef = useRef(false);

  const handleGetFileStream = async () => {
    if (!fileId) {
      message.error('缺少文件ID, 获取文件失败');
      return;
    }

    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setLoading(true); // 开始加载时设置 loading 为 true
    try {
      const { data, fileName } = isPreview
        ? await getFileStreamForPreview(fileId)
        : await getFileStream(fileId);
      const reader = data.getReader();

      const stream = new ReadableStream({
        start(controller) {
          const pumpRead = (): any =>
            reader.read().then(({ done, value }: any) => {
              if (done) {
                controller.close();
                return;
              }
              controller.enqueue(value);
              return pumpRead();
            });
          return pumpRead();
        },
      });

      const blob = await new Response(stream).blob();

      // 设置 MIME 类型
      let mimeType = '';
      if (fileName?.endsWith('.pdf')) {
        mimeType = 'application/pdf';
      } else if (fileName?.endsWith('.docx') || fileName?.endsWith('.doc')) {
        mimeType =
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      } else if (fileName?.endsWith('.xlsx') || fileName?.endsWith('.xls')) {
        mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else if (fileName?.endsWith('.ppt') || fileName?.endsWith('.pptx')) {
        mimeType = 'application/vnd.ms-powerpoint';
      } else if (
        fileName?.endsWith('.png') ||
        fileName?.endsWith('.jpg') ||
        fileName?.endsWith('.jpeg')
      ) {
        mimeType = 'image/png';
      } else {
        mimeType = 'application/octet-stream';
      }

      // 创建带有 MIME 类型的 Blob
      const typedBlob = new Blob([blob], { type: mimeType });
      const url = window.URL.createObjectURL(typedBlob);

      setFileUrl(url);
      setFileType(mimeType);

      // 根据文件类型处理内容
      if (mimeType.includes('wordprocessingml.document')) {
        // 处理 docx 文件
        const arrayBuffer = await blob.arrayBuffer();
        const result = await mammoth.extractRawText({ arrayBuffer });
        setFileContent(<pre>{result.value}</pre>);
      } else if (mimeType.includes('spreadsheetml.sheet')) {
        // 处理 xlsx 文件
        const arrayBuffer = await blob.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // 将数据转换为 Ant Design Table 所需的格式
        const dataSource = json.slice(1).map((row: any, rowIndex: number) => {
          const record: any = { key: rowIndex };
          //@ts-ignore
          json[0].forEach((header: any, colIndex: number) => {
            record[header] = row[colIndex];
          });
          return record;
        });
        //@ts-ignore
        const columns = json[0].map((header: any) => ({
          title: header,
          dataIndex: header,
          key: header,
        }));

        setTableData({ dataSource, columns });
      } else if (mimeType.includes('powerpoint')) {
        // 处理 ppt 文件
        const arrayBuffer = await blob.arrayBuffer();
        const pptx = new PptxGenJS();
        //@ts-ignore
        pptx.load(arrayBuffer).then((pres) => {
          //@ts-ignore
          const slidesContent = pres.slides.map((slide, index) => (
            <div key={index}>
              <h3>Slide {index + 1}</h3>
              <pre>{JSON.stringify(slide, null, 2)}</pre>
            </div>
          ));
          setFileContent(<div>{slidesContent}</div>);
        });
      }
    } catch (err) {
      console.error('获取文件流失败:', err);
      message.error('获取文件流失败，请稍后重试');
    } finally {
      isLoadingRef.current = false;
      setLoading(false); // 加载完成或出错时设置 loading 为 false
    }
  };

  useEffect(() => {
    fileId && handleGetFileStream();
  }, [fileId]);

  return (
    <div className="w-full h-[680px] overflow-x-hidden overflow-y-auto">
      {loading ? (
        <div className="w-full h-full flex justify-center items-center">
          <Spin size="large" tip="文件加载中..." />
        </div>
      ) : fileUrl && fileType ? (
        <>
          {fileType === 'application/pdf' ? (
            <iframe src={fileUrl} width="100%" height="680px" />
          ) : fileType.startsWith('image') ? (
            <img src={fileUrl} alt="预览" style={{ maxWidth: '100%' }} />
          ) : fileType.includes('wordprocessingml.document') ? (
            fileContent
          ) : fileType.includes('spreadsheetml.sheet') ? (
            <Table
              dataSource={tableData.dataSource}
              columns={tableData.columns}
              bordered
              pagination={false}
            />
          ) : fileType.includes('powerpoint') ? (
            fileContent
          ) : (
            <p>不支持的文件类型</p>
          )}
        </>
      ) : null}
    </div>
  );
};

export default FileViewer;
