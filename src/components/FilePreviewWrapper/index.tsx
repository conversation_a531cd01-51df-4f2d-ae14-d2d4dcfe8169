/* eslint-disable jsx-a11y/anchor-is-valid */

/*
 * @Description: 文件预览组件，需要区分预览文件的类型，并返回不同的 ReactNode
 * @Author: Liu<PERSON>hen
 * @Date: 2023-05-19 16:59:40
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-09-22 18:12:07
 * @FilePath: \yjc\src\components\FilePreviewWrapper\index.tsx
 */
import React, { useEffect, useState } from 'react';
import { routerPrefix } from './utils';

type TFilePreviewWrapperProps = {
  fileName: string;
  fileUrl: string;
};

const FilePreviewWrapper: React.FC<TFilePreviewWrapperProps> = ({
  fileName,
  fileUrl,
}) => {
  const [isPDF, setIsPDF] = useState<boolean>(false);

  const [isGif, setIsGif] = useState<boolean>(false);

  useEffect(() => {
    if (fileName.includes('pdf')) {
      setIsPDF(true);
    }
    if (fileName.includes('gif')) {
      setIsGif(true);
    }
  }, [fileName]);

  return (
    <>
      {!isPDF && !isGif ? (
        <a
          key={fileUrl}
          className="block mb-2"
          onClick={() =>
            window.open(routerPrefix() + `/file-viewer?u=${fileUrl}`)
          }
        >
          {fileName}
        </a>
      ) : (
        <a
          key={fileUrl}
          className="block mb-2"
          href={fileUrl}
          target="_blank"
          rel="noreferrer"
        >
          {fileName}
        </a>
      )}
    </>
  );
};

export default FilePreviewWrapper;
