/*
 * @Description: 封装 ProTable 组件，修改样式，透传属性
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 17:21:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-03-27 09:23:19
 * @FilePath: \ejc-resource-management-webui\src\components\EditableProTable\index.tsx
 */
import { EditableProTable, ParamsType } from '@ant-design/pro-components';
import './index.less';
import { EditableProTableProps } from './typing';

type TEEditableProTable<
  T extends Record<string, any>,
  U extends ParamsType
> = EditableProTableProps<T, U>;

function EEditableProTable<
  T extends Record<string, any>,
  U extends { [key: string]: any } = {}
>({ ...restProps }: TEEditableProTable<T, U>) {
  return (
    <div className="e-editable-proTable-container">
      <EditableProTable {...(restProps as any)} />
    </div>
  );
}

export default EEditableProTable;
