/*
 * @Date: 2024-07-18 09:49:50
 * @LastEditors: <PERSON><PERSON>hen
 * @LastEditTime: 2024-07-24 08:49:33
 * @FilePath: /xr-qc-jk-web/src/components/ListImport/index.tsx
 * @Description: 导入
 */
import { ReactNode, useState } from 'react';
import { Button, message, Modal, Upload, UploadProps } from 'antd';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { CloudUploadOutlined, InfoCircleOutlined } from '@ant-design/icons';

type TListImportProps = {
  children: ReactNode;
  open: boolean;
  setOpen: (val: boolean) => void;
  downLoadUrl: string;
  importUrl: string;
};

const { Dragger } = Upload;

const ListImport: React.FC<TListImportProps> = ({
  children,
  open,
  setOpen,
  downLoadUrl,
  importUrl,
}) => {
  // 获取当前的token
  const { token } = useTokenStore();

  //上传文件
  const [fileList, setFileList] = useState<any[]>([]);
  const props: UploadProps = {
    name: 'file',
    multiple: true,
    fileList: fileList,
    action: importUrl,
    headers: {
      Authorization: 'Bearer ' + token,
    },
    onChange: async (info) => {
      const { file, fileList } = info;
      setFileList(fileList);
      // 状态必须在完成或者移除时
      if (file.status === 'done' || file.status === 'removed') {
        // 上传文件失败
        if (file.response.code !== codeDefinition.QUERY_SUCCESS) {
          message.warning(file.response.msg);
          // 删除掉上传失败的文件
          const fileData: any = fileList.filter(
            (item) => item.response.code === codeDefinition.QUERY_SUCCESS
          );
          setFileList(fileData);
          return;
        } else {
          message.success(QUERY_SUCCESS_MSG);
        }
      }
    },
  };

  const closeModal = () => {
    setOpen(false);
  };

  // 下载导入模板
  const handleDownloadTemplate = () => {
    const url = `${import.meta.env.VITE_URL}${downLoadUrl}`;
    console.log('URL:', url);
    fetch(url + '', {
      method: 'GET',
      // body: JSON.stringify({ filename }),  //fetch get请求不可以包含body!!
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token,
      }),
    })
      .then((response) => {
        response.blob().then((blob) => {
          const aLink = document.createElement('a');
          document.body.appendChild(aLink);
          aLink.style.display = 'none';
          aLink.href = window.URL.createObjectURL(blob);
          aLink.download = '导入模板.xlsx';
          aLink.click();
          document.body.removeChild(aLink);
        });
      })
      .catch((error) => {
        throw new Error(`Error: ${error}`);
      });
  };
  return (
    <>
      <div className=" inline-block">{children}</div>

      <Modal
        title="导入"
        width="40%"
        open={open}
        onCancel={closeModal}
        footer={null}
        destroyOnClose
      >
        <div className=" w-full flex flex-col justify-center items-center gap-4">
          <div className="flex flex-nowrap gap-3">
            <InfoCircleOutlined
              className="text-xl text-[#40a9ff] relative transform translate-y-1"
              style={{ color: '#40a9ff' }}
            />
            <div>
              <p className="!mb-0">
                1.导入模板中有字段导入说明，必填字段必须填写；
              </p>
              <p className="!mb-0">
                2.当你多次重复导入会时追加数据，不会覆盖数据；
              </p>
              <p className="!mb-0">3.单次导入的文件大小不能超过5M；</p>
            </div>
          </div>
          <div>
            <Dragger {...props} className="px-32">
              <CloudUploadOutlined
                className=" text-4xl"
                style={{ color: '#40a9ff' }}
              />
              {/* <p className="ant-upload-text">
                Click or drag file to this area to upload
              </p> */}
              <p className=" p-4">点击或拖拽 Excel 文件到这里</p>
            </Dragger>
          </div>
          <div>
            <Button type="text" onClick={handleDownloadTemplate}>
              点击下载模板
            </Button>
          </div>
          <div className="w-full flex justify-end">
            <Button onClick={closeModal}>关闭</Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ListImport;
