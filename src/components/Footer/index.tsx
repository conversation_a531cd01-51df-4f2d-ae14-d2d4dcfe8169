/*
 * @Author: l
 * @Date: 2025-01-15 16:07:05
 * @LastEditors: l
 * @LastEditTime: 2025-01-15 16:34:27
 * @FilePath: \xr-qc-jk-web\src\components\Footer\index.tsx
 * @Description: 
 */
import React from 'react';
import baIcon from '@/assets/ba.png';

const Footer: React.FC = () => {
  return import.meta.env.VITE_SHOW_COPYRIGHT === 'true' ? (
    <div className="text-center pt-2">
      <div className="text-gray-400 text-xs">
        主办单位: 贵州省疾病预防控制中心 黔ICP备17011643号-1 客服电话:
        15802850751 19113169796
      </div>
      <a
        target="_blank"
        href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=52010302003205"
        rel="noreferrer"
        className=" text-xs text-gray-400"
      >
        <p>
          <img
            alt=""
            src={baIcon}
            className=" w-[14px] inline-block translate-y-1 mr-1"
          />
          贵公网安备 52010302003205号
        </p>
      </a>
    </div>
  ) : null;
};

export default Footer;
