import React, { useState } from 'react';
import { Empty, Input, Modal } from 'antd'; 

const HeaderSearch: React.FC = () => {
  const [searchModalOpen, setSearchModalOpen] = useState<boolean>(false);

  return (
    <>
      <span
        className="h-full cursor-pointer flex justify-center items-center"
        onClick={() => setSearchModalOpen(true)}
        title="站内搜索"
      > 
      </span>

      <Modal
        title="站内搜索"
        open={searchModalOpen}
        destroyOnClose
        footer={null}
        maskClosable={false}
        onCancel={() => setSearchModalOpen(false)}
      >
        <Input
          size="large" 
          placeholder="请输入业务关键字..."
          className="!pl-2"
        />
        <Empty
          description="暂无搜索数据"
          imageStyle={{ height: 60 }}
          className="mt-5"
        />
      </Modal>
    </>
  );
};

export default HeaderSearch;
