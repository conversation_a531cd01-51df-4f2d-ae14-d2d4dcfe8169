/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { message, Modal, Spin } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { useTokenStore } from '@/store';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { ProFormUploadButton } from '@ant-design/pro-components';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import FileViewByStream from '../FileViewByStream';

export type EProFormUploadButtonProps = {
  /** 字段名称 */
  name?: string;
  /** 字段标签 */
  label?: string;
  /** 最大上传数量 */
  max?: number;
  /** 是否只读模式（详情页面使用，只能查看和下载，不能上传和删除） */
  readonly?: boolean;
  /** 上传文件时的回调，如果需要自定义处理逻辑，可以提供此回调 */
  onChange?: (info: any) => void;
  /** 上传API地址 */
  action?: string;
  /** 自定义提示文字，如不提供则使用默认文字 */
  extraText?: string;
  /** 其他ProFormUploadButton的属性 */
  [key: string]: any;
  /** 是否隐藏默认的提示文字 */
  defaultExtraTextHide?: any;
  tipsWord?: any;
  /** 上传状态变化时的回调，将 file 信息回传给父组件 */
  onUploadChange?: (file: any) => void;
};

/**
 * 封装的上传组件，支持上传、预览、下载和删除附件
 *
 * @param props 组件属性
 * @returns 渲染的组件
 */
const EProFormUploadButton: React.FC<EProFormUploadButtonProps> = ({
  name = 'attachmentIds',
  label = '附件信息',
  max = 10,
  readonly = false,
  onChange,
  isTipShow,
  action,
  extraText,
  tipsWord,
  defaultExtraTextHide = false,
  onUploadChange, // 新增回调函数
  ...restProps
}) => {
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [fileUrl, setFileUrl] = useState('');
  const [previewFileId, setPreviewFileId] = useState('');
  const [isShowFileData, setIsShowFileData] = useState<any>({});
  const [uploadUrl, setUploadUrl] = useState('');
  const [downloading, setDownloading] = useState(false);

  const { token } = useTokenStore();

  // 默认的提示文字
  const defaultExtraText =
    '注意：1、支持上传Excel、Word、PDF格式文件；2、每份文件不得超过10M；3、最多支持上传10份文件；';

  // 获取上传URL
  useEffect(() => {
    const getUploadUrl = async () => {
      if (!action) {
        const url = await uploadFiles();
        setUploadUrl(url);
      } else {
        setUploadUrl(action);
      }
    };
    getUploadUrl();
  }, [action]);

  useEffect(() => {
  }, []);

  

  /**
   * 处理文件下载
   *
   * @param info 文件信息
   */
  const handleDownload = (info: any) => {
    // 检查文件信息
    if (!info) {
      message.error('文件信息不完整，无法下载');
      return;
    }

    // 获取文件ID
    const fileId = info.response?.data?.id || info.id || info.uid;
    if (!fileId) {
      message.error('文件ID不存在，无法下载');
      return;
    }

    // 获取文件名
    const fileName = info.name || '附件';

    // 设置下载状态为 true
    setDownloading(true);

    // 调用API下载文件
    downloadFile(fileId, fileName)
      .then(() => {
        message.success(`${fileName} 下载成功`);
      })
      .catch((error) => {
        console.error('下载文件失败:', error);
        message.error('下载文件失败，请稍后重试');
      })
      .finally(() => {
        // 无论成功或失败，都将下载状态设置为 false
        setTimeout(() => {
          setDownloading(false);
        }, 1000); // 添加短暂延迟，确保下载完成
      });
  };

  /**
   * 处理文件上传的通用逻辑
   * @param info 上传信息
   */
  const handleUploadChange = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else if (file.response && file.response.msg) {
        message.success(file.response.msg);
      }
    }
    console.log(file, 11111);
    

    // 如果提供了外部onChange回调，则调用它
    if (onChange) {
      onChange(info);
    }
  };

  /**
   * 预览文件
   */
  const handlePreview = async (file: any) => {
    if (
      file.status === 'done' &&
      file.response &&
      file.response.data &&
      file.response.data.ossId
    ) {
      const type = getFileTypeByName(file.response.data.fileName);
      const d = await getFileData(file.response.data.ossId);
      setPreviewFileId(file?.uid);

      if (type === 'Image') {
        setIsShowFileData({
          name: file.response.data.fileName,
          url: d,
          ossId: file.response.data.ossId,
        });
        setIsShowFileView(true);
      } else {
        setOpenPreview(true);
      }
    }
  };

  /**
   * 图片预览模态框关闭事件
   */
  const handleFileViewClose = () => {
    setIsShowFileView(false);
    setIsShowFileData({});
  };

  /**
   * 文件预览模态框关闭事件
   */
  const handlePreviewClose = () => {
    setOpenPreview(false);
    setFileUrl('');
  };

  return (
    <>
      <ProFormUploadButton
        name={name}
        label={label}
        max={max}
        title="上传附件"
        colProps={{ span: 24 }}
        labelCol={{ flex: 0.005 }}
        fieldProps={{
          iconRender: (file) => {
            return (
              <img
                src={getIconByName(file.name)}
                className="!w-[40px] !h-[40px] m-auto mt-2"
                alt="logo"
              />
            );
          },
          disabled: readonly || downloading,
          name: 'file',
          listType: 'picture-card',
          showUploadList: {
            showDownloadIcon: true,
            downloadIcon: <DownloadOutlined />,
            showRemoveIcon: !readonly,
          },
          onDownload: (file) => {
            handleDownload(file);
          },
          onChange: handleUploadChange,
          onPreview: handlePreview,
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }}
        action={uploadUrl}
        wrapperCol={{
          span: 24,
        }}
        extra={(readonly|| defaultExtraTextHide )? '' : extraText || defaultExtraText}
        {...restProps}
      >
        <div>
          <PlusOutlined />
          <div style={{ marginTop: 8 }}>Upload</div>
        </div>
      </ProFormUploadButton>
      {
        (tipsWord && tipsWord.length>0) && (
          <div className='tips-word'>{tipsWord}</div>
        )
      }

      {/* 图片预览模态框 */}
      <Modal
        open={isShowFileView}
        title="图片预览"
        onCancel={handleFileViewClose}
        footer={null}
        width={800}
      >
        {isShowFileData.url && (
          <div className="w-full flex items-center justify-center">
            <img
              src={isShowFileData.url}
              alt={isShowFileData.name}
              style={{ maxWidth: '100%' }}
            />
          </div>
        )}
      </Modal>

      {/* 文件预览模态框 */}
      <Modal
        width="90%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <FileViewByStream fileId={previewFileId} isPreview />
      </Modal>

      {/* 下载中的加载效果 */}
      {downloading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
            <Spin size="large" />
            <div className="mt-4 text-lg">文件下载中，请稍候...</div>
          </div>
        </div>
      )}
    </>
  );
};

export default EProFormUploadButton;
