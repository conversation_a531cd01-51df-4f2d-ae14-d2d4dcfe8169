import React from 'react';
import { Card } from 'antd';
import { CardSize } from 'antd/es/card/Card';

interface IBlockContainer {
  title?: string | React.ReactNode;
  extra?: React.ReactNode;
  bg?: string;
  noPadding?: boolean;
  bodyStyle?: any; // 内置模块样式
  hoverable?: boolean; //鼠标移过时可浮起
  className?: string; //鼠标移过时可浮起
  borderRadius?: string; // 圆角
  shadow?: string; // 阴影
  size?: CardSize; // 大小 默认small
  children?: React.ReactNode;
}

const BlockContainer: React.FC<IBlockContainer> = ({
  title,
  extra,
  bg,
  noPadding = false,
  children,
  hoverable = false,
  className,
  bodyStyle,
  borderRadius = 'md',
  shadow = 'none',
  size = 'small',
}) => {
  return (
    <Card
      title={title}
      extra={extra}
      className={`rounded-${borderRadius} shadow-${shadow} ${className}`}
      size={size}
      hoverable={hoverable}
      styles={{
        body: {
          backgroundColor: bg,
          padding: noPadding ? 0 : '10px',
          ...bodyStyle,
        },
      }}
    >
      {children}
    </Card>
  );
};

export default BlockContainer;
