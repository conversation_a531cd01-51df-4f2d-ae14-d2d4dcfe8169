/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Description:File View提供一个新的页面以展示不同类型的文件，比如 PDF、docx、xlsx等。
 * @Author: Liu<PERSON>hen
 * @Date: 2023-05-17 17:07:13
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-09-24 17:13:48
 * @FilePath: \xr-qc-jk-web\src\components\EFileView\index.tsx
 */
import React, { useCallback, useEffect, useState } from 'react';
import DocViewer, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HTMLRenderer,
  IDocument,
  ImageProxyRenderer,
  JPGRenderer,
  MSDocRenderer,
  MSGRenderer,
  PDFRenderer,
  PNGRenderer,
  TIFFRenderer,
  TXTRenderer,
} from 'react-doc-viewer';
import { useSearchParams } from 'react-router-dom';
import { Button, Modal, Skeleton } from 'antd';
import './index.less';

type Props = {
  blobUrl: any; // 预览地址
  open: boolean;
  close: () => void;
  previewType?: string; // 预览类型，路径中带文件拓展名可不传
};

const EFileViewWrapper: React.FC<any> = ({
  blobUrl,
  open,
  close,
  previewType = '',
}) => {
  const [fileUrl, setFileUrl] = useState<any>();
  const [documents, setDocuments] = useState<IDocument[]>([]);

  const handleClose = () => {
    setFileUrl('');
    close();
  };
  useEffect(() => {
    setFileUrl(blobUrl);
  }, [blobUrl]);

  useEffect(() => {
    if (fileUrl) {
      let obj: any = { uri: fileUrl };
      if (previewType) obj.fileType = previewType;
      setDocuments([obj]);
    }
  }, [fileUrl]);
  
  const getFileType = useCallback(() => {
    let fileType = previewType
    if(!fileType) {
      fileType = fileUrl.split('.')[fileUrl.split('.').length-1]
    } 
    return fileType
  }, [fileUrl, previewType])

  return (
    <>
      <Modal
        title="文件预览"
        centered
        width="85vw"
        open={open}
        onCancel={handleClose}
        footer={[
          <Button key="close" onClick={handleClose}>
            关闭
          </Button>,
        ]}
      >
        {fileUrl && open ? (
          getFileType() === 'pdf' ? (
            <iframe
              title="pdf预览"
              src={blobUrl}
              style={{ width: '80vw', margin: 'auto', height: '80vh' }}
            />
          ) : (
            <DocViewer
              pluginRenderers={[
                PDFRenderer,
                PNGRenderer,
                BMPRenderer,
                HTMLRenderer,
                ImageProxyRenderer,
                JPGRenderer,
                MSDocRenderer,
                MSGRenderer,
                TIFFRenderer,
                TXTRenderer,
              ]}
              style={{ width: '80vw', margin: 'auto', height: '80vh' }}
              documents={documents}
            />
          )
        ) : (
          <div className="w-full h-[100vh] p-6">
            <Skeleton active />
          </div>
        )}
      </Modal>
    </>
  );
};

export default EFileViewWrapper;
