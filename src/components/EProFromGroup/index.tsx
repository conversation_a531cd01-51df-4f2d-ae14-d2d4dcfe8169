/*
 * @Description: 封装 ProTable 组件，修改样式，透传属性
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 17:21:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-24 10:29:40
 * @FilePath: /xr-qc-jk-web/src/components/EProFromGroup/index.tsx
 */
import { ProForm, ProFormBaseGroupProps } from '@ant-design/pro-components';
import './index.less';

type EProFormGroupProps = ProFormBaseGroupProps;

function EProFormGroup({ ...restProps }: EProFormGroupProps) {
  return (
    <ProForm.Group
      title={restProps.title ? restProps.title : null}
      style={{
        position: 'relative',
        backgroundColor: '#fff',
        padding: '16px',
        marginBottom: '16px',
        borderRadius: '4px',
      }}
    >
      {restProps.title ? (
        <span className="absolute top-12 left-0 w-full h-[1px] bg-[#f0f0f0]"></span>
      ) : null}

      {restProps.children}
    </ProForm.Group>
  );
}

export default EProFormGroup;
