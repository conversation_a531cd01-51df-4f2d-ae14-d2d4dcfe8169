/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Layout } from 'antd';
import * as Icon from '@ant-design/icons';
import { appTitle } from '@/constants';
import { usePermissionRouterStore } from '@/store';
import { clone } from 'lodash';
import Account from '@/components/Account';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { menuData, setChildMenuData } = usePermissionRouterStore();

  /**
   * @TODO 刷新页面时根据当前的 pathname 自动选中该模块
   */
  const handleDefaultSelectedMenuItem = (): string => {
    if (pathname === '/') {
      return '/';
    }
    const _result: string = '/' + pathname.split('/').filter((item) => item)[0];
    return _result;
  };

  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<any>(() =>
    handleDefaultSelectedMenuItem()
  );

  const [menuList, setMenuList] = useState<any>([]);

  useEffect(() => {
    setDefaultSelectedKeys(() => handleDefaultSelectedMenuItem());
  }, [pathname]);

  // 创建icon图标元素
  const iconToElement = (name: string) => {
    if ((Icon as any)[name]) {
      return React.createElement(Icon && (Icon as any)[name], {
        style: { fontSize: '16px' },
      });
    } else {
      return '';
    }
  };

  const home = {
    key: '/',
    icon: 'HomeOutlined',
    label: '首页',
  };

  const visualization = {
    key: '/dataVisualizationDashboard/monitoringOverview',
    icon: 'AreaChartOutlined',
    label: '可视化',
  };

  // 控制左侧菜单显示
  useEffect(() => {
    if (menuData.length) {
      const arr: any[] = clone(menuData);
      function genMenu(menu: any[]) {
        let mItem: any = [];
        menu.forEach((item) => {
          let m: any = {};
          m.key = item.completePath || item.path;
          m.icon = item.meta?.icon;
          m.label = item.meta?.title;
          if (item.children) {
            m.children = genMenu(item.children);
          }
          mItem.push(m);
        });
        return mItem;
      }
      const menu = genMenu(arr);
      setMenuList([home, ...menu, visualization] as any);
    } else {
      setMenuList([home]);
    }
  }, [menuData]);

  // 递归获取左侧菜单默认路由
  const getDefaultRouter = (item: any): string => {
    if (item.children && item.children.length) {
      return getDefaultRouter(item.children[0]);
    } else {
      return item.key;
    }
  };

  return (
    <Layout.Header className="text-white h-12 w-full pl-3 pr-4 flex items-center justify-between fixed left-0 top-0 right-0 z-50 shadow-md overflow-hidden">
      <div className="h-full flex flex-row flex-nowrap items-center gap-3 w-[260px] border-r border-white">
        <span className="text-[17px] font-bold">{appTitle}</span>
      </div>
      <div className="flex-1 flex h-full items-center justify-start">
        {menuList.map((item: any) => {
          const isSelected = defaultSelectedKeys === item.key;
          return (
            <div
              key={item.key}
              className="hover:bg-white hover:text-blue-700 h-full box-border flex items-center justify-start px-4 text-white cursor-pointer"
              style={{
                borderBottom: isSelected ? '2px solid #fff' : 'none',
              }}
              onClick={() => {
                if (item.children && item.children.length) {
                  setChildMenuData(item.children ? [...item.children] : []);
                  navigate(getDefaultRouter(item) || '/');
                } else {
                  navigate(item.key || '/');
                  setChildMenuData([]);
                }
              }}
              title={item.label}
            >
              {item.icon && iconToElement(item.icon)}
              <div className="pl-1">{item.label}</div>
            </div>
          );
        })}
      </div>
      <div className="h-full flex flex-row flex-nowrap items-center gap-5">
        <span className="h-full cursor-pointer flex justify-center items-center">
          <Account />
        </span>
      </div>
    </Layout.Header>
  );
};

export default Header;
