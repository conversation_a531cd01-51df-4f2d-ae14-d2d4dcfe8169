/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { Layout } from 'antd';
import { useSidebarStore } from '@/store';
import AppMenu from '../AppMenu';

const Sider: React.FC = () => {
  const { collapsed, setCollapsed } = useSidebarStore();
  return (
    <Layout.Sider
      className="w-[380px] pt-4 overflow-x-hidden overflow-y-auto"
      trigger={null}
      collapsible
      collapsed={collapsed}
      collapsedWidth={48}
      theme={'light'}
      width={240}
    >
      <AppMenu />
    </Layout.Sider>
  );
};

export default Sider;
