import React from 'react';
import classnames from 'classnames';
import './index.less';

interface LoadingProps {
  text?: string;
  size?: string;
  textClassName?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = '80px',
  text = '加载中',
  textClassName,
}) => {
  return (
    <div className="absolute w-full h-full flex flex-col flex-nowrap justify-center items-center overflow-hidden">
      <div className="lds-ellipsis" style={{ width: size, height: size }}>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div className={classnames('text-sm text-gray-400', textClassName)}>
        {text}
      </div>
    </div>
  );
};

export default Loading;
