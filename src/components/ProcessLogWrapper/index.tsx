/* eslint-disable react-hooks/exhaustive-deps */

/*
 * @Description: 流程日志模块组件
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-11 11:08:19
 * @LastEditors: meng<PERSON><PERSON>un
 * @LastEditTime: 2023-08-30 15:12:50
 * @FilePath: \yjc\src\components\ProcessLogWrapper\index.tsx
 */
import React, { useEffect, useState } from 'react';
import { List, message } from 'antd';
// import { getTransferLogs } from '@/api/approval';
import classNames from 'classnames';
import dayjs from 'dayjs';

type TProcessLogWrapperProps = {
  logsData?: Record<string, any>[];
  headerTitle?: string;
  detailInfo?: any;
  documentNo?: string; // 单据编号，用于获取日志列表数据
  refresh?: boolean;
};

const ProcessLogWrapper: React.FC<TProcessLogWrapperProps> = ({
  logsData,
  headerTitle = '过程日志',
  documentNo,
  detailInfo,
  refresh,
}) => {
  const [processLogsList, setProcessLogsList] = useState<Record<string, any>[]>(
    [  {
      createName: '张三',
      createTime: dayjs().subtract(1, 'day').toDate(),
      operateContent: '提交了审批申请',
    },
    {
      createName: '李四',
      createTime: dayjs().toDate(),
      operateContent: '批准了审批申请',
    },]
  );
  /**
   * @TODO 获取过程日志信息
   * @param documentNo
   */


  useEffect(() => {

  }, []);

  useEffect(() => {
    if (detailInfo?.logs) {
      setProcessLogsList(detailInfo.logs); 
    }
  }, [detailInfo]);

  return (
    <div className="w-full h-[calc(100%-10px)] py-4">
      <List
        header={<div className="secondary-title">{headerTitle}</div>}
        bordered
        size="large"
        dataSource={processLogsList}
        className="h-full overflow-auto"
        renderItem={(item, index) => (
          <List.Item>
            <div className="flex-1">
              <div
                className={classNames(
                  'flex flex-row flex-nowrap justify-between mb-2',
                  index === 0 ? 'text-[#1890FF] font-semibold' : ''
                )}
              >
                <span>{item.createName}</span>
                <span className="text-slate-500">
                  {item.createTime
                    ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''}
                </span>
              </div>
              <div className={classNames(index === 0 ? 'text-[#1890FF]' : '')}>
                {item.operate}
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};

export default ProcessLogWrapper;
