/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-throw-literal */

/* eslint-disable array-callback-return */
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { message, Tooltip } from 'antd';
import type { UploadProps } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { useTokenStore } from '@/store';
import {
  DeleteOutlined,
  DownloadOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import {
  BetaSchemaForm,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import EFileView from '@/components/EFileView';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip';
import FileView from '@/components/FileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import EExcelFileView from '../EExcelFileView';

type DataItem = {
  name: string;
  state: string;
};

type TFormProps = {
  formDataList?: any;
  readOnly?: boolean;
  fileCheckTooltipTitle?: string; // 文件校验提示标题
  onRef: React.MutableRefObject<any>; // 文件校验提示标题
};

const JsonForm: React.FC<TFormProps> = ({
  formDataList,
  readOnly = false,
  fileCheckTooltipTitle = '表单',
  onRef,
}) => {
  const { token } = useTokenStore();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState<boolean>(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  // word等文件预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  // 这里通过 useRef 创建一个数组
  const formRef = useRef<any>(null);

  const [formData, setFormData] = useState<any[]>([]);

  // 暴露子组件的方法给父组件
  useImperativeHandle(onRef, () => {
    return {
      getFormInfo, // 获取表单数据
      formValidateFields, // 表单校验
    };
  });

  const uploadProps: UploadProps | any = {
    action: uploadFiles,
    fieldProps: {
      multiple: true,
      headers: { Authorization: `Bearer ${token}` },
      listType: 'picture-card',
      showUploadList: {
        showDownloadIcon: true,
        downloadIcon: <DownloadOutlined />,
        showRemoveIcon: !readOnly,
      },
      // 预览
      async onPreview(file: any) {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.data &&
          file.response.data.ossId
        ) {
          const { fileName, url, ossId } = file.response.data;
          const type = getFileTypeByName(fileName);
          if (type === 'Image') {
            const d = await getFileData(ossId);
            setIsShowFileData({
              name: fileName,
              url: d,
            });
            setIsShowFileView(true);
          } else {
            setFileUrl(url);
            setOpenPreview(true);
          }
        }
      },
      // 下载
      async onDownload(file: any) {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.data &&
          file.response.data.ossId
        ) {
          const { ossId, fileName } = file.response.data;
          downloadFile(ossId, fileName);
        }
      },
      iconRender: (file: any) => {
        return (
          <>
            {file.status === 'uploading' ? (
              <LoadingOutlined className="!w-[15px] !h-[15px] mr-1 text-[red]" />
            ) : (
              <img
                src={getIconByName(file.name)}
                className="!w-[40px] !h-[40px] m-auto mt-2"
                alt="logo"
              />
            )}
          </>
        );
      },
      itemRender: (
        originNode: any,
        file: any,
        fileList: object[],
        actions: { download: any; preview: any; remove: any }
      ) => {
        let isError = false; // 上传失败的文件
        if (file.status === 'done' && !readOnly) {
          const { code } = file.response;
          if (code !== 200) {
            isError = true;
          } else {
            isError = false;
          }
        }
        return (
          <>
            {isError ? (
              <>
                <div className="file-upload-card-title">
                  <div className="card">
                    <img
                      src={getIconByName(file.name)}
                      className="!w-[40px] !h-[40px] mt-2"
                      alt="logo"
                    />
                    <EllipsisWithTooltip
                      className="mt-1"
                      onClick={actions.preview}
                      text={file.name}
                    />
                    {/* <div className='text-[red] top-1 text-[12px]'>上传失败</div> */}
                  </div>
                  <Tooltip title="上传失败">
                    <div className="bg-card">
                      <DeleteOutlined
                        className="delete"
                        onClick={() => actions.remove()}
                      />
                    </div>
                  </Tooltip>
                </div>
              </>
            ) : (
              originNode
            )}
          </>
        );
      },
    },
    onChange(info: any) {
      const { file } = info;
      if (file.status === 'done') {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.code !== 200
        ) {
          message.error(file.response.msg);
        } else {
          message.success(file.response.msg);
        }
      } else if (file.status === 'removed') {
        message.success('操作成功');
      }
    },
  };

  const init = async () => {
    try {
      const temp: any = setup(formDataList);
      setFormData(temp);
    } catch (error) {
      console.log(error);
    }
  };

  const setup = (orgData: any[]) => {
    return orgData.map((item: any) => {
      if (item.inputBox === 'text') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          // initialValue: item?.valueObject?.value,
          initialValue: readOnly ? (
            <div className=" break-words">{item?.valueObject?.value}</div>
          ) : (
            item?.valueObject?.value
          ),
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired),
                message: '此项为必填项',
              },
            ],
          },
          fieldProps: {
            maxLength: 200,
          },
        };
      } else if (item.inputBox === 'select') {
        const enums: any = {};
        if (item.enumItems) {
          item.enumItems.split(',').map((en: any) => {
            enums[en] = {
              text: en,
            };
          });
        }
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item?.valueObject?.value,
          valueType: 'select',
          valueEnum: enums,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired),
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'digit') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item?.valueObject?.value,
          valueType: 'digit',
          fieldProps: {
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired),
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'calendar') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue:
            item?.valueObject?.value !== 'Invalid Date' &&
            item?.valueObject?.value !== ''
              ? item?.valueObject?.value
              : null,
          valueType: 'date',
          fieldProps: {
            format: 'YYYY-MM-DD',
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: Number(item.isRequired),
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'file') {
        let fileList = [];
        try {
          fileList = item?.valueObject?.value
            ? JSON.parse(item?.valueObject?.value).map((item: any) => {
                return {
                  uid: item.id,
                  name: item.name,
                  status: 'done',
                  type: 'application/msword',
                  url: item.url,
                  response: {
                    code: 200,
                    data: {
                      fileName: item.name,
                      ossId: item.id,
                      url: item.url,
                    },
                  },
                };
              })
            : [];
        } catch (error) {
          fileList = [];
        }
        return {
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: fileList,
          colProps: { span: 24 },
          formItemProps: {
            labelWrap: true,
            className: `inspect-upload-style ${
              readOnly ? 'upload-card-readonly-container' : ''
            }`,
            tooltip: item.pomptInformation,
            labelCol: {
              span: 1.5,
            },
            rules: [
              {
                required: Number(item.isRequired),
                message: '此项为必填项',
              },
            ],
          },
          renderFormItem: () => (
            <ProFormUploadButton
              {...uploadProps}
              name={'f' + item.id}
              wrapperCol={{ span: 10 }}
            ></ProFormUploadButton>
          ),
        };
      }
    });
  };

  /**
   * @TODO 判断必填文件上传状态
   * @param 文件数组
   */
  const determineFileStatus = (arrFile: any[]) => {
    let fileTooltip = '';
    // 校验文件是否上传成功
    const tempStatus1 = arrFile.some(
      (item) => item.status && item.status === 'uploading'
    );
    const tempStatus2 = arrFile.some(
      (item) => item.response && !item.response?.data
    );
    if (tempStatus1) {
      fileTooltip = '正在上传中';
    } else if (tempStatus2) {
      fileTooltip = '上传失败';
    }
    return fileTooltip;
  };

  /* 表单校验 */
  const formValidateFields = async () => {
    // 表单校验
    await formRef.current.validateFields();
  };

  // 数据处理
  const getFormInfo = async () => {
    try {
      const formInfo = await formRef.current.getFieldsValue();
      // 校验必填文件是否都上传成功了
      let fileiSampleInspectionInformation = '';
      // 组装数据
      const data = formDataList.map((item: any) => {
        item.valueObject = {
          ...item.valueObject,
          value: formInfo[`f${item.id}`],
        };
        if (item.inputBox === 'calendar') {
          item.valueObject = {
            ...item.valueObject,
            value: item?.valueObject?.value
              ? dayjs(item?.valueObject?.value).format('YYYY-MM-DD')
              : null,
          };
        } else if (item.inputBox === 'file') {
          let fileObj = [];
          // 校验文件是否上传成功
          fileiSampleInspectionInformation = determineFileStatus(
            item?.valueObject?.value
          );
          if (item?.valueObject?.value && item?.valueObject?.value.length > 0) {
            fileObj = item?.valueObject?.value
              .filter(
                (item: any) =>
                  item.response &&
                  item.response.data &&
                  item.response.data.ossId
              )
              .map((item: any) => {
                return {
                  name: item.response.data.fileName,
                  id: item.response.data.ossId,
                  url: item.response.data.url,
                };
              });
          }
          item.valueObject = {
            ...item.valueObject,
            value: JSON.stringify(fileObj),
          };
        }
        return item;
      });

      if (fileiSampleInspectionInformation) {
        if (fileiSampleInspectionInformation === '上传失败') {
          throw `${fileCheckTooltipTitle}有${fileiSampleInspectionInformation}的文件,请删除后再提交`;
        } else {
          throw `${fileCheckTooltipTitle}有${fileiSampleInspectionInformation}的文件,请等待上传完成`;
        }
      }
      return data;
    } catch (e) {
      throw new Error(`${e}`);
    }
  };

  useEffect(() => {
    formDataList?.length > 0 && init();
  }, [formDataList]);

  return (
    <>
      <BetaSchemaForm<DataItem>
        className="w-full"
        layoutType={'Form'}
        layout="horizontal"
        formRef={formRef}
        labelWrap
        labelAlign="left"
        rowProps={{
          gutter: [40, 0],
        }}
        labelCol={{
          offset: 1,
        }}
        colProps={{
          sm: 12,
          md: 8,
          lg: 6,
        }}
        grid={true}
        columns={formData as any}
        submitter={false}
      />
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </>
  );
};

export default JsonForm;
