/*
 * @Description: 封装 ProTable 组件，修改样式，透传属性
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 17:21:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-16 17:54:18
 * @FilePath: \ejc-resource-management-webui\src\components\EProTable\index.tsx
 */
import {
  ParamsType,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import './index.less';

type EProTableProps<
  T extends Record<string, any>,
  U extends ParamsType
> = ProTableProps<T, U>;

function EProTable<
  T extends Record<string, any>,
  U extends { [key: string]: any } = {}
>({ ...restProps }: EProTableProps<T, U>) {
  return (
    <div className="e-protable-container">
      <ProTable {...restProps} />
    </div>
  );
}

export default EProTable;
