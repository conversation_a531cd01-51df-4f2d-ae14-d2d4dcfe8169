/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Description: 基于 Swiper10.x的轮播组件，使用了web-component
 * 在React+Typescript项目中，使用自定义的 web-component 需要进行全局的 JSX 定义
 * 具体请参考：https://zhuanlan.zhihu.com/p/444597432
 * @Author: Liu<PERSON>hen
 * @Date: 2023-07-12 14:49:17
 * @LastEditors: LiuZhen
 * @LastEditTime: 2024-09-10 13:32:45
 * @FilePath: /yjc-visual-ui/src/components/SeamlessScrollNext/index.tsx
 */
import React, { useEffect, useRef } from 'react';

type TSeamlessScrollNextProps = {
  children: React.FunctionComponent[];
  spaceBetween?: string;
  slidesPerView?: string;
  speed?: string; // 滚动速率
  loop?: boolean; // 循环
  autoPlay?: boolean; // 自动播放
  direction?: 'horizontal' | 'vertical';
  width?: string | number;
  height?: string | number;
  swiperSlideClassname?: string;
};

const SeamlessScrollNext: React.FC<TSeamlessScrollNextProps> = ({
  children,
  speed = '5000',
  loop = true,
  autoPlay = true,
  direction = 'horizontal',
  spaceBetween = '0',
  slidesPerView = '10',
  width = '100%',
  height = '100%',
  swiperSlideClassname,
}) => {
  const swiperElRef = useRef(null);

  // 当滚动数据个数在slidesPerview的一倍至2倍之间 复制一份让其无缝滚动
  if (
    ~~slidesPerView <= children.length &&
    children.length < 2 * ~~slidesPerView
  ) {
    children = children.concat(children);
  }

  useEffect(() => {
    // listen for Swiper events using addEventListener
    (swiperElRef.current as any).addEventListener('progress', (e: any) => {
      const [swiper, progress] = e.detail;
    });

    (swiperElRef.current as any).addEventListener(
      'slidechange',
      (e: any) => {}
    );
  }, []);

  return (
    <swiper-container
      ref={swiperElRef}
      space-between={spaceBetween}
      slides-per-view={slidesPerView}
      speed={speed}
      loop={loop}
      autoPlay={autoPlay}
      direction={direction}
      style={{ width: width, height: height }}
    >
      {children.map((item: React.FunctionComponent, index: number) => (
        <swiper-slide key={index} className={swiperSlideClassname}>
          {React.createElement(item)}
        </swiper-slide>
      ))}
    </swiper-container>
  );
};
export default SeamlessScrollNext;
