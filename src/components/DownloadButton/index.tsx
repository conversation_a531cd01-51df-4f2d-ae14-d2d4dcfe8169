/* eslint-disable @typescript-eslint/no-unused-vars */
import { ReactNode, useState } from 'react';
import { Button, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import request from '@/utils/request';

type DownloadButtonProps = {
  url: string;
  params?: object;
  children: ReactNode;
  type?: any;
  size?: any;
  method?: 'get' | 'post';
};

const DownloadButton: React.FC<DownloadButtonProps> = ({
  url,
  params,
  children,
  type = 'primary',
  size = 'middle',
  method = 'post',
}) => {
  const [loading, setLoading] = useState(false);
  const download = async () => {
    try {
      setLoading(true);

      if (method === 'post') {
        const { data, fileName } = await request[method](url, {
          data: params,
        });
        const reader = data.getReader();
        // 手动控制读取流以便回传下载进度
        const stream = new ReadableStream({
          start(controller) {
            // 声明一个 pumpRead 方法读取片段
            // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
            const pumpRead = (): any =>
              reader.read().then(({ done, value }: any) => {
                // done  - 当 stream 传完所有数据时则变成 true
                // value - 数据片段 - done 为 true 时为 undefined
                if (done) {
                  // 结束读取 关闭读取流
                  controller.close();
                  return;
                }
                // 每次推入读取队列 并链式执行
                controller.enqueue(value);
                return pumpRead();
              });
            // 开始读取
            return pumpRead();
          },
        });
        // 最后我们通过 Response 函数接收这个文件流 并转为 blob
        const blob = await new Response(stream).blob();
        setLoading(false);
        const objUrl = window.URL.createObjectURL(blob);
        const aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = objUrl;
        aLink.setAttribute('download', decodeURI(fileName));
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(objUrl);
      }

      if (method === 'get') {
        const { data, fileName } = await request[method](url, {
          params,
        });
        const reader = data.getReader();
        // 手动控制读取流以便回传下载进度
        const stream = new ReadableStream({
          start(controller) {
            // 声明一个 pumpRead 方法读取片段
            // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
            const pumpRead = (): any =>
              reader.read().then(({ done, value }: any) => {
                // done  - 当 stream 传完所有数据时则变成 true
                // value - 数据片段 - done 为 true 时为 undefined
                if (done) {
                  // 结束读取 关闭读取流
                  controller.close();
                  return;
                }
                // 每次推入读取队列 并链式执行
                controller.enqueue(value);
                return pumpRead();
              });
            // 开始读取
            return pumpRead();
          },
        });
        // 最后我们通过 Response 函数接收这个文件流 并转为 blob
        const blob = await new Response(stream).blob();
        setLoading(false);
        const objUrl = window.URL.createObjectURL(blob);
        const aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = objUrl;
        aLink.setAttribute('download', decodeURI(fileName));
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(objUrl);
      }

      if (method == 'get') {
        const { data, fileName } = await request[method](url, {
          params,
        });
        const reader = data.getReader();
        // 手动控制读取流以便回传下载进度
        const stream = new ReadableStream({
          start(controller) {
            // 声明一个 pumpRead 方法读取片段
            // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
            const pumpRead = (): any =>
              reader.read().then(({ done, value }: any) => {
                // done  - 当 stream 传完所有数据时则变成 true
                // value - 数据片段 - done 为 true 时为 undefined
                if (done) {
                  // 结束读取 关闭读取流
                  controller.close();
                  return;
                }
                // 每次推入读取队列 并链式执行
                controller.enqueue(value);
                return pumpRead();
              });
            // 开始读取
            return pumpRead();
          },
        });
        // 最后我们通过 Response 函数接收这个文件流 并转为 blob
        const blob = await new Response(stream).blob();
        setLoading(false);
        const objUrl = window.URL.createObjectURL(blob);
        const aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = objUrl;
        aLink.setAttribute('download', decodeURI(fileName));
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(objUrl);
      }
      if (method == 'post') {
        const { data, fileName } = await request[method](url, {
          data: params,
        });
        const reader = data.getReader();
        // 手动控制读取流以便回传下载进度
        const stream = new ReadableStream({
          start(controller) {
            // 声明一个 pumpRead 方法读取片段
            // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
            const pumpRead = (): any =>
              reader.read().then(({ done, value }: any) => {
                // done  - 当 stream 传完所有数据时则变成 true
                // value - 数据片段 - done 为 true 时为 undefined
                if (done) {
                  // 结束读取 关闭读取流
                  controller.close();
                  return;
                }
                // 每次推入读取队列 并链式执行
                controller.enqueue(value);
                return pumpRead();
              });
            // 开始读取
            return pumpRead();
          },
        });
        // 最后我们通过 Response 函数接收这个文件流 并转为 blob
        const blob = await new Response(stream).blob();
        setLoading(false);
        const objUrl = window.URL.createObjectURL(blob);
        const aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = objUrl;
        aLink.setAttribute('download', decodeURI(fileName));
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(objUrl);
      }
    } catch (e) {
      setLoading(false);
      message.error('导出错误');
    }
  };
  return (
    <Button
      loading={loading}
      key="button"
      className="mr-2"
      icon={<DownloadOutlined />}
      onClick={() => {
        download();
      }}
      type={type}
      size={size}
    >
      {children}
    </Button>
  );
};

export default DownloadButton;
