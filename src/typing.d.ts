import { codeMessage } from '@/constants';

declare module 'PrefetchLazyPathsPlugin';
declare module '@/utils/jsencrypt';

export interface ComponentModule {
  default: React.ComponentType;
}

export interface LazyOptions {
  loader: () => Promise<ComponentModule>;
  loading: React.ComponentType;
}

/**
 * @TODO 网络请求响应体 Response 类型定义
 */
export type TResponseModel = {
  code: keyof codeMessage;
  msg: string;
  data: unknown;
};

declare global {
  namespace JSX {
    interface IntrinsicElements {
      // 扩展 IntrinsicElements 否则无法识别自定义标签
      'swiper-container': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      >;
      'swiper-slide': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      >;
    }
  }

  namespace React {
    interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
      // 扩展HTML标签的属性类型
      // 如果需要使用自定义属性的化需要
      [key: string]: any;
    }
  }
}