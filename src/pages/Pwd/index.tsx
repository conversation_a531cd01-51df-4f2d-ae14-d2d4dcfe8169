/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, message } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import { changePassword } from '@/api/login';
import { submitLogout } from '@/api/login';
import baIcon from '@/assets/ba.png';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore, useTokenStore } from '@/store';
import {
  ProForm,
  ProFormDependency,
  ProFormText,
} from '@ant-design/pro-components';
import { passwordReg } from '@/utils/validators';
import './index.less';

const Pwd: React.FC = (props) => {
  const nav = useNavigate();

  const navigate = useNavigate();
  const { setToken } = useTokenStore();
  const { setUserInfo } = useInfoStore();

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await changePassword(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('修改密码成功，请重新登录');
        setToken('');
        navigate('/login');
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="w-screen h-screen overflow-hidden loginStyleContainer">
      <video
        src="https://xinrui-material-resource-ejc.oss-cn-qingdao.aliyuncs.com/video5.mp4"
        autoPlay
        loop
        muted
        className="videoStyle"
      ></video>
      <div className="videoModel"></div>
      <div className="ModelRight"></div>
      <div className="w-[500px] absolute top-1/2 right-[280px] transform overflow-hidden -translate-y-1/2 shadow-2xl !rounded-xl py-[50px] bg-[#fff] box-border">
        <div className="text-center text-lg text text-red-500 pb-4 font-bold">
          第一次登录请修改密码
        </div>
        <ProForm
          className="p-6 pb-0"
          formRef={formRef}
          submitter={false}
          onFinish={handleSave}
          //@ts-ignore
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormText.Password
            label="原密码"
            name="oldPassword"
            rules={[{ required: true, message: '请输入原密码' }]}
            placeholder="请输入原密码"
          />
          <ProFormText.Password
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              {
                required: true,
                validator(rule, value, callback) {
                  const reg =
                    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@&%#_])[a-zA-Z0-9~!@&%#_]{8,16}$/;
                  if (!passwordReg.test(value)) {
                    callback(
                      '密码必须包含一个大写，一个小写字母，一个特殊字符，且长度为8到16位'
                    );
                  } else {
                    callback();
                  }
                },
              },
            ]}
            placeholder="请输入新密码"
          />
          <ProFormDependency name={['newPassword']}>
            {({ newPassword }) => {
              return (
                <ProFormText.Password
                  label="再次输入新密码"
                  name="newPassword2"
                  rules={[
                    {
                      required: true,
                      validator(rule, value, callback) {
                        if (value !== newPassword) {
                          callback('两次输入的密码不一致');
                        } else {
                          callback();
                        }
                      },
                    },
                  ]}
                  placeholder="请再次输入新密码"
                />
              );
            }}
          </ProFormDependency>
          <div className="text-center pt-4">
            <Button
              type="primary"
              size="large"
              className="mr-2"
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              提交
            </Button>
            <Button
              size="large"
              onClick={() => {
                submitLogout();
                setToken('');
                setUserInfo({});
                navigate('/login');
              }}
            >
              退出
            </Button>
          </div>
        </ProForm>
      </div>
      {import.meta.env.VITE_SHOW_COPYRIGHT === 'true' ? (
        <div className="absolute left-0 right-[400px] mx-auto text-right bottom-5 text-slate-400">
          <div className="inline-block translate-x-[200px]">
            主办单位: 贵州省疾病预防控制中心 黔ICP备17011643号-1 客服电话:
            15802850751 19113169796
          </div>
          <a
            target="_blank"
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=52010302003205"
            rel="noreferrer"
          >
            <p>
              <img
                src={baIcon}
                className=" inline-block translate-y-1.5 mr-1"
              />
              贵公网安备 52010302003205号
            </p>
          </a>
          {/* Copyright &copy;{new Date().getFullYear()} {COMPANY} version{' '}
        {APP_VERSION} */}
        </div>
      ) : null}
    </div>
  );
};
export default Pwd;
