import { useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import { addAssessmentType } from '@/api/assessment';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ProForm, ProFormText } from '@ant-design/pro-components';

const FormInfoInit = {
  todo: null,
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
};

const EditTypeIn: React.FC<TRoleDetailProps> = ({ open, closeDetail }) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
  };

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await addAssessmentType(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      className="min-w-[400px]"
      title="录入样本"
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        className="mt-6"
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        onFinish={handleSave}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          label="样本编号"
          name="todo"
          rules={[{ required: true, message: '请输入样本编号' }]}
          placeholder="请输入样本编号"
        />
      </ProForm>
    </Modal>
  );
};

export default EditTypeIn;
