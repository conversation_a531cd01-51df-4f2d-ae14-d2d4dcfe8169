/* eslint-disable jsx-a11y/anchor-is-valid */

/*
 * @Author: meng<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-06-16 17:11:22
 * @LastEditors: LGX
 * @LastEditTime: 2023-10-30 16:39:02
 * @FilePath: \ejc-resource-management-webui\src\pages\Finance\RecordManagement\components\ElectronicRecord.tsx
 * @Description: 档案管理-电子档案
 */
import { useEffect, useState } from 'react';
import {
  Button,
  Drawer,
  Dropdown,
  Image,
  List,
  MenuProps,
  message,
  Skeleton,
  Tooltip,
} from 'antd';
// import { queryRecordERecord } from '@/api/finance';
// import { queryDownloadDocTemplate } from '@/api/templateManagement';
import { codeDefinition } from '@/constants';
import JSZip from 'jszip';
// import printJS from 'print-js';
// import { v4 as uuidv4 } from 'uuid';
// import { getLodop } from '@/utils/print/LodopFuncs';
import { routerPrefix } from '@/utils';

type TElectronicRecordProps = {
  open: boolean;
  close: () => void;
  applyNo: string;
};

const ElectronicRecord: React.FC<any> = ({
  open,
  close,
  applyNo,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [allFiles, setAllFiles] = useState<any[]>([]);

  const [currentView, setCurrentView] = useState<string>('');


// 模拟 queryRecordERecord 函数
const mockQueryRecordERecord = (params: { applyNo: string }) => {
  return new Promise<{ code: number; data: any; msg: string }>((resolve) => {
    setTimeout(() => {
      resolve({
        code: codeDefinition.QUERY_SUCCESS,
        data: {
          applySpreadsheet: 'http://example.com/apply_spreadsheet.pdf',
          applyFiles: JSON.stringify([
            { name: '采购申请附件1', url: 'http://example.com/apply_file1.pdf', fileId: 1 },
            { name: '采购申请附件2', url: 'http://example.com/apply_file2.png', fileId: 2 },
          ]),
          parityFiles: JSON.stringify([
            { name: '采购询价附件1', url: 'http://example.com/parity_file1.pdf', fileId: 3 },
            { name: '采购询价附件2', url: 'http://example.com/parity_file2.jpg', fileId: 4 },
          ]),
          receiptsSpreadsheet: 'http://example.com/receipts_spreadsheet.pdf',
          receiptsFiles: JSON.stringify([
            { name: '采购收货附件1', url: 'http://example.com/receipts_file1.pdf', fileId: 5 },
            { name: '采购收货附件2', url: 'http://example.com/receipts_file2.gif', fileId: 6 },
          ]),
          storageSpreadsheet: 'http://example.com/storage_spreadsheet.pdf',
          storageFiles: JSON.stringify([
            { name: '采购入库附件1', url: 'http://example.com/storage_file1.pdf', fileId: 7 },
            { name: '采购入库附件2', url: 'http://example.com/storage_file2.jpeg', fileId: 8 },
          ]),
        },
        msg: '成功',
      });
    }, 1000);
  });
};


  /**
   * @TODO 获取数据
   */
  const getData = async (no: string) => {
    try {
      setLoading(true);
      const { code, data, msg } = await mockQueryRecordERecord({ applyNo: no });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: any[] = [
          {
            key: '1',
            list: [],
            name: '采购申请',
          },
          {
            key: '2',
            list: [],
            name: '采购询价',
          },
          {
            key: '3',
            list: [],
            name: '采购收货',
          },
          {
            key: '4',
            list: [],
            name: '采购入库',
          },
        ];
        // 采购申请单据
        if (data.applySpreadsheet) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购申请') {
              _item.list.push({
                name: '采购申请单据',
                url: data.applySpreadsheet,
                isImg: false,
                isPdf: data.applySpreadsheet.includes('pdf'),
                // key: uuidv4(),
                isReceipt: true,
              });
            }
          });
        }
        // 采购申请附件
        const _applyFiles = JSON.parse(data.applyFiles);
        if (_applyFiles && _applyFiles.length) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购申请') {
              _item.list = _item.list.concat(
                _applyFiles.map((_i: any) => ({
                  name: _i.name,
                  url: _i.url,
                  isImg: false,
                  isPdf: _i.url.includes('pdf'),
                  // key: uuidv4(),
                  isReceipt: false,
                  fileId: _i.fileId,
                }))
              );
            }
          });
        }
        // 采购询价附件
        const _parityFiles = JSON.parse(data.parityFiles);
        if (_parityFiles && _parityFiles.length) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购询价') {
              _item.list = _item.list.concat(
                _parityFiles.map((_i: any) => ({
                  name: _i.name,
                  url: _i.url,
                  isImg: false,
                  isPdf: _i.url.includes('pdf'),
                  // key: uuidv4(),
                  isReceipt: false,
                  fileId: _i.fileId,
                }))
              );
            }
          });
        }
        // 采购收货单据
        if (data.receiptsSpreadsheet) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购收货') {
              _item.list.push({
                name: '采购收货单据',
                url: data.receiptsSpreadsheet,
                isImg: false,
                isPdf: data.receiptsSpreadsheet.includes('pdf'),
                // key: uuidv4(),
                isReceipt: true,
              });
            }
          });
        }
        // 采购收货附件
        const _receiptsFiles = JSON.parse(data.receiptsFiles);
        if (_receiptsFiles && _receiptsFiles.length) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购询价') {
              _item.list = _item.list.concat(
                _receiptsFiles.map((_i: any) => ({
                  name: _i.name,
                  url: _i.url,
                  isImg: false,
                  isPdf: _i.url.includes('pdf'),
                  // key: uuidv4(),
                  isReceipt: false,
                  fileId: _i.fileId,
                }))
              );
            }
          });
        }
        // 采购入库单据
        if (data.storageSpreadsheet) {
          // 入库单据
          _dataSource.forEach((_item) => {
            if (_item.name === '采购入库') {
              _item.list.push({
                name: '采购入库单据',
                url: data.storageSpreadsheet,
                isImg: false,
                isPdf: data.storageSpreadsheet.includes('pdf'),
                // key: uuidv4(),
                isReceipt: true,
              });
            }
          });
        }
        // 采购入库附件
        const _storageFiles = JSON.parse(data.storageFiles);
        if (_storageFiles && _storageFiles.length) {
          _dataSource.forEach((_item) => {
            if (_item.name === '采购询价') {
              _item.list = _item.list.concat(
                _storageFiles.map((_i: any) => ({
                  name: _i.name,
                  url: _i.url,
                  isImg: false,
                  isPdf: _i.url.includes('pdf'),
                  // key: uuidv4(),
                  isReceipt: false,
                  fileId: _i.fileId,
                }))
              );
            }
          });
        }
        // 所有文件
        let _allFiles: any[] = [];
        _dataSource.forEach((_item) => {
          _item.list.forEach((_i:any) => {
            _i.isImg = [
              'png',
              'gif',
              'jpg',
              'jpeg',
              'bmp',
              'webp',
              'svg',
              'psd',
              'tiff',
            ].some((_type) => _i.url.includes(_type));
          });
          _allFiles = [..._allFiles, ..._item.list];
        });

        setDataSource(_dataSource);
        setAllFiles(_allFiles);
        // 默认展示文件
        setCurrentView(_allFiles[0].key);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      getData(applyNo);
    }
  }, [open, applyNo]);

  return (
    <Drawer
      title="电子档案"
      onClose={close}
      open={open}
      width="80%"
      destroyOnClose
    >
      <div className="w-full flex overflow-auto h-full">
        <div
          className="min-w-[256px] p-3"
          style={{ borderRight: '1px solid #d9d9d9' }}
        >
          <section className="w-full">
            {loading ? (
              <div className="p-6">
                <Skeleton active />
              </div>
            ) : (
              <List
                itemLayout="horizontal"
                dataSource={dataSource}
                renderItem={(item: any) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.name}
                      description={
                        <div>
                          {item.list.map((_file: any) => (
                            <Tooltip
                              title={_file.name}
                              key={_file.key}
                              placement="right"
                            >
                              <div
                                className="w-full cursor-pointer min-w-[180px] pl-2 truncate rounded-[3px] h-[28px] leading-[28px] mb-[4px]"
                                style={{
                                  backgroundColor:
                                    currentView === _file.key
                                      ? '#bae7ff'
                                      : '#fff',
                                }}
                                onClick={() => {
                                  setCurrentView(_file.key);
                                }}
                              >
                                {_file.name}
                              </div>
                            </Tooltip>
                          ))}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </section>
        </div>
        <div className="flex-1 min-w-[800px] p-3 h-full flex flex-col">
          <header className="h-[60px]  leading-[60px] px-6">
    
          </header>
          <section className="flex-1 flex flex-col overflow-y-auto">
            {loading ? (
              <Skeleton active className="p-6" />
            ) : (
              allFiles.map((_file) =>
                currentView === _file.key ? (
                  <div className="w-full h-full px-6" key={_file.key}>
                    {_file.isImg ? (
                      <Image src={_file.url} alt={_file.name} />
                    ) : (
                      <iframe
                        src={
                          _file.isPdf
                            ? _file.url
                            :routerPrefix() +  `/file-viewer?u=${_file.url}`
                        }
                        title="预览"
                        className="w-full h-[95%]"
                      ></iframe>
                    )}
                  </div>
                ) : (
                  <div key={_file.key}></div>
                )
              )
            )}
          </section>
        </div>
      </div>
    </Drawer>
  );
};

export default ElectronicRecord;
