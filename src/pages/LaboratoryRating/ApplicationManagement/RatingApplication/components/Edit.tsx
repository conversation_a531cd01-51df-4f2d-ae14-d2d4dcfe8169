/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Form, Input, message, Modal, Spin } from 'antd';
import { editGrade, getGradeDetail, saveGrade } from '@/api/apply';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import BaseForm from './BaseForm';
import LabApplicationForm from './LabApplicationForm';
import { LabApplicationData } from './LabApplicationForm/types';
import ReceiveForm from './ReceiveForm';

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId,
  dialogTitletext,
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const LabApplicationFormRef = useRef<any>();
  const [isReadonly, setIsReadonly] = useState(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getGradeDetail(id);
        if (code === 200) {
          console.log('原始commitment数据:', data.commitment, typeof data.commitment);
          const commitmentArray = (data.commitment === 1 || data.commitment === '1') ? ['1'] : [];
          console.log('处理后的commitment数组:', commitmentArray);
          const data1 = {
            ...data,
            commitment: commitmentArray,
          };
          console.log('最终传递给表单的数据:', data1);
          setBaseInfo(data1);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
    setIsReadonly(dialogTitletext === '查看');
  }, [detailId]);

  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);

  // 提交表单
  const handleSubmit = async () => {
    try {
      console.log('开始提交申请...');
      if (LabApplicationFormRef.current) {
        console.log('LabApplicationFormRef.current 存在');
        // 校验必填（提交申请时需要验证子项）
        const inspectionBasicInformation1 =
          await LabApplicationFormRef.current.validateAll(true);
        console.log('校验结果:', inspectionBasicInformation1);
        if (inspectionBasicInformation1) {
          const formParams = await LabApplicationFormRef.current.getAllData();
          console.log('表单数据:', formParams);
          console.log('条件检查 - areaName长度:', formParams?.areaName?.length);
          console.log('条件检查 - commitment:', formParams?.commitment);
          console.log('条件检查 - setId长度:', formParams?.setId?.length);
          if (
            formParams.areaName && formParams.areaName.length &&
            formParams.commitment && (formParams.commitment[0] === 1 || formParams.commitment[0] === '1') &&
            formParams.setId && formParams.setId.length
          ) {
            console.log('所有条件满足，开始处理数据...');
            // 申请书附件
            if (formParams.file && formParams.file.length) {
              if (formParams.file.length) {
                const arrList = formParams.file.map((_item: any) => ({
                  url: _item.url,
                  fileName: _item.fileName,
                  size: _item.size,
                  ossId: _item.ossId,
                }));
                formParams.file = JSON.stringify(arrList);
              }
            }
            // 其他附件
            if (formParams.otherFile && formParams.otherFile.length) {
              if (formParams.otherFile.length) {
                const arrList = formParams.otherFile.map((_item: any) => ({
                  url: _item.url,
                  fileName: _item.fileName,
                  size: _item.size,
                  ossId: _item.ossId,
                }));
                formParams.otherFile = JSON.stringify(arrList);
              }
            }
            const tableData = formRef2.current.handleSubmit();
            console.log('ReceiveForm返回的数据:', tableData);
            
            if (!tableData) {
              console.log('ReceiveForm验证失败，停止提交');
              return; // ReceiveForm验证失败，停止提交
            }
            
            const postPrams = {
              ...formParams,
              commitment: formParams.commitment[0],
              indices: tableData,
              eventCode: 'submit',
            };

            // 这里的验证已经在上面做过了，不需要重复验证
            // const isValid = await LabApplicationFormRef.current?.validateAll(true);
            // if (isValid) {
            //   const data = LabApplicationFormRef.current?.getAllData();
            //   console.log('提交的数据:', data);
            //   // 在这里调用您的保存或提交API
            //   // api.save(data)...
            // }
            // return
            console.log('准备提交API，参数:', postPrams);
            console.log('dialogTitletext:', dialogTitletext);
            
            if (dialogTitletext === '新增') {
              console.log('执行新增操作');
              const { code, data, msg } = await saveGrade(postPrams);
              console.log('新增API响应:', { code, data, msg });
              if (code === 200) {
                message.success('新增成功');
                close();
              } else {
                console.error('新增失败:', msg);
                message.error(msg || '新增失败');
              }
            } else if (dialogTitletext === '编辑') {
              console.log('执行编辑操作');
              postPrams.id = detailId;
              const { code, data, msg } = await editGrade(postPrams);
              console.log('编辑API响应:', { code, data, msg });
              if (code === 200) {
                message.success('编辑成功');
                close();
              } else {
                console.error('编辑失败:', msg);
                message.error(msg || '编辑失败');
              }
            }
          } else {
            console.log('条件检查未通过，无法提交');
            message.warning('请检查必填项：所属地区、承诺勾选、评级设置是否完整');
          }
        } else {
          console.log('表单校验未通过');
          message.warning('请完善表单必填项');
        }
      } else {
        console.error('LabApplicationFormRef.current 未定义');
        return;
      }
    } catch (error) {
      console.error('提交过程中发生错误:', error);
      message.error('提交失败：' + (error instanceof Error ? error.message : '未知错误'));
      return;
    }
  };

  // 保存草稿（不需要验证子项）
  const handleSave = async () => {
    try {
      if (LabApplicationFormRef.current) {
        // 保存草稿时不需要验证子项非空规则
        const formParams = await LabApplicationFormRef.current.getAllData();
        const tableData = formRef2.current.handleSubmit('save');
        const postPrams = {
          ...formParams,
          // commitment:formParams?.commitment[0],
          indices: tableData,
          eventCode: 'save',
        };
        // 申请书附件
        if (formParams.file && formParams.file.length) {
          if (formParams.file.length) {
            const arrList = formParams.file.map((_item: any) => ({
              url: _item.url,
              fileName: _item.fileName,
              size: _item.size,
              ossId: _item.ossId,
            }));
            postPrams.file = JSON.stringify(arrList);
          }
        }
        // 其他附件
        if (formParams.otherFile && formParams.otherFile.length) {
          if (formParams.otherFile.length) {
            const arrList = formParams.otherFile.map((_item: any) => ({
              url: _item.url,
              fileName: _item.fileName,
              size: _item.size,
              ossId: _item.ossId,
            }));
            postPrams.otherFile = JSON.stringify(arrList);
          }
        }

        if (
          formParams &&
          formParams.commitment &&
          formParams.commitment.length
        ) {
          postPrams.commitment = formParams.commitment[0];
        }

        if (dialogTitletext === '新增') {
          const { code, data, msg } = await saveGrade(postPrams);
          console.log(code, data, msg);
          if (code === 200) {
            message.success('草稿提交成功');
            close();
          }
        } else if (dialogTitletext === '编辑') {
          postPrams.id = detailId;
          const { code, data, msg } = await editGrade(postPrams);
          console.log(code, data, msg);
          if (code === 200) {
            message.success('草稿编辑成功');
            close();
          }
        }
      } else {
        // console.error('formRef1.current 未定义');
        return;
      }
    } catch (error) {
      console.error('表单校验失败:', error);
      message.error('请完善样品参考结果'); // 使用 message 提示用户
      return; // 捕获异常后返回，避免后续代码执行
    }
  };

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="flex-1 overflow-auto p-6">
          {/* <BaseForm
            readonlyAll={true}
            readonly={true}
            hideRank={true}
            isShowChar={false}
            dialogTitletext={dialogTitletext}
            onRef={formRef1}
            id={id}
            detailInfo={baseInfo}
          /> */}
          <LabApplicationForm
            onRef={LabApplicationFormRef}
            readonly={isReadonly}
            dialogTitletext={dialogTitletext}
            id={id}
            initialData={baseInfo}
          />
          <div className="mt-4">
            <ReceiveForm
              loading={loading}
              onRef={formRef2}
              dialogTitletext={dialogTitletext}
              detailInfo={baseInfo}
              mode={mode}
              onSubmit={(val) => {
                handleSaveSubmit(val);
              }}
            />
          </div>
        </div>
      )}
      <span>{mainLoading}</span>
      <span>{readonly}</span>
      {dialogTitletext !== '查看' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3 bottom-btn-wraper">
          <Button
            loading={loading}
            type="primary"
            onClick={() => {
              handleSubmit();
            }}
          >
            {' '}
            提交申请
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              handleSave();
            }}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}
      {dialogTitletext === '查看' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3 bottom-btn-wraper">
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}
    </div>
  );
};

export default MenuDetail;
