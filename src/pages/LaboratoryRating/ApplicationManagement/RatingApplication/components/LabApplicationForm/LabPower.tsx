// /LabApplicationForm/LabPower.tsx
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { Col, FormInstance, Row } from 'antd';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { ChildFormProps } from './types';

const CHECKBOX_KEYS = [
  'occupationalHealthQualification',
  'radiologicalHealthQualification',
];

const LabPower: React.FC<ChildFormProps> = ({
  onRef,
  readonly,
  initialData,
}) => {
  const formRef = useRef<FormInstance>();

  // 选中值转换
  const transformCheckboxValues = {
    // Convert true/false to "1"/"0" for form submission
    out: (formData: Record<string, any>) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] ? '1' : '0';
        }
      });
      return transformed;
    },

    // Convert "1"/"0" back to true/false for form display
    in: (formData: Record<string, any>) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] === '1';
        }
      });
      return transformed;
    },
  };

  useImperativeHandle(onRef, () => ({
    validateFields: async() => {
      try {
        await formRef.current?.validateFields();
        return true;
      } catch (error) {
        throw ("能力与资质");
      }
    },
    getFormData: () => {
      const formData = formRef.current?.getFieldsValue() || {};
      return transformCheckboxValues.out(formData);
    },
  }));

  useEffect(() => {
    if (initialData && formRef.current) {
      formRef.current.setFieldsValue(initialData);
    }
  }, [initialData]);

  const transform = {
    in: (value: number) => value === 1,
    out: (value: boolean) => (value ? 1 : 0),
  };

  const formItemStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };
  const labelStyle = { color: 'rgba(0, 0, 0, 0.88)' };

  return (
    <ProForm
      formRef={formRef}
      layout="horizontal"
      grid={false}
      submitter={false}
      readonly={readonly}
      initialValues={initialData}
    >
      <div className="flex flex-col gap-5">
        {/* --- 1. 实验室检验检测能力 (流式布局) --- */}
        <BlockContainer title="实验室检验检测能力">
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '16px 50px', // 行间距和列间距
              alignItems: 'center',
            }}
            className='pb-2'
          >
            <div style={formItemStyle}>
              <span style={labelStyle}>检验检测能力共</span>
              <ProFormDigit name="inspectionMajorCount" noStyle width="xs" />
              <span>类</span>
              <ProFormDigit name="inspectionItemCount" noStyle width="xs" />
              <span>项</span>
            </div>
            {/* <div style={formItemStyle}>
              <ProFormDigit name="inspectionItemCount" noStyle width="xs" />
              <span>项</span>
            </div> */}
            <div style={formItemStyle}>
              <span style={labelStyle}>其中: 病原微生物</span>
              <ProFormDigit name="pathogenicMicroCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>卫生微生物</span>
              <ProFormDigit name="healthMicroCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>理化</span>
              <ProFormDigit name="physicalChemicalCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>毒理</span>
              <ProFormDigit name="toxicologyCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>职业卫生</span>
              <ProFormDigit name="occupationalHealthCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>放射卫生</span>
              <ProFormDigit name="radiologicalHealthCount" noStyle width="xs" />
              <span>项</span>
            </div>
            <div style={formItemStyle}>
              <span style={labelStyle}>实验室应急检测</span>
              <ProFormDigit name="labEmergencyTestCount" noStyle width="xs" />
              <span>项</span>
            </div>
          </div>
        </BlockContainer>

        {/* --- 2. 实验室资质 (三列网格布局) --- */}
        <BlockContainer title="实验室资质">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <ProFormText
                labelCol={{ flex: '0 0 165px' }}
                name="qualificationCertNo"
                label="实验室资质认定：证书号"
                placeholder="请输入"
                width="md"
              />
            </Col>
            <Col span={8}>
              <ProFormDatePicker
                labelCol={{ flex: '0 0 70px' }}
                name="qualificationValidity"
                label="有效期"
                width="md"
                placeholder="请选择日期"
                fieldProps={{
                  format: 'YYYY-MM-DD',
                  onChange: (date, dateString) => {
                    // dateString 会是中文格式的日期
                    formRef.current?.setFieldsValue({
                      qualificationValidity: dateString,
                    });
                  },
                }}
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                labelCol={{ flex: '0 0 70px' }}
                name="qualificationProjectCount"
                label="项目数"
                placeholder="请输入"
                width="md"
                addonAfter="项"
              />
            </Col>

            <Col span={8}>
              <ProFormText
                labelCol={{ flex: '0 0 165px' }}
                name="accreditationCertNo"
                label="实验室认可：证书号"
                placeholder="请输入"
                width="md"
              />
            </Col>
            <Col span={8}>
              <ProFormDatePicker
                labelCol={{ flex: '0 0 70px' }}
                name="accreditationValidity"
                label="有效期"
                width="md"
                placeholder="请选择日期"
                fieldProps={{
                  format: 'YYYY-MM-DD',
                  onChange: (date, dateString) => {
                    // dateString 会是中文格式的日期
                    formRef.current?.setFieldsValue({
                      accreditationValidity: dateString,
                    });
                  },
                }}
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                labelCol={{ flex: '0 0 70px' }}
                name="accreditationProjectCount"
                label="项目数"
                placeholder="请输入"
                width="md"
                addonAfter="项"
              />
            </Col>

            <Col span={8}>
              <ProFormCheckbox
                labelCol={{ flex: '0 0 70px' }}
                name="occupationalHealthQualification"
                transform={transform}
                label={null} // 使用空label占位对齐
                colon={false}
                fieldProps={{
                  disabled: readonly,
                }}
              >
                职业卫生技术服务机构资质
              </ProFormCheckbox>
            </Col>
            <Col span={8}>
              <ProFormCheckbox
                labelCol={{ flex: '0 0 70px' }}
                name="radiologicalHealthQualification"
                transform={transform}
                label={null} // 使用空label占位对齐
                colon={false}
                fieldProps={{
                  disabled: readonly,
                }}
              >
                放射卫生技术服务机构资质
              </ProFormCheckbox>
            </Col>
            <Col span={8}>
              <ProFormText
                width="md"
                labelCol={{ flex: '0 0 70px' }}
                name="otherQualifications"
                label="其他资质"
                placeholder="请输入其他资质"
              />
            </Col>
          </Row>
        </BlockContainer>
      </div>
    </ProForm>
  );
};

export default React.memo(LabPower);
