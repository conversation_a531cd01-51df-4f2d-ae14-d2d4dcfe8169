// /LabApplicationForm/types.ts

import { FormInstance } from '@ant-design/pro-components';

/**
 * 整个实验室申请表单的数据模型
 */
export interface LabApplicationData {
  // --- 基本信息 Tab ---
  setId?: string;
  commitment?: string[];
  name?: string; // 机构名称
  areaName?: string; // 所属区域
  address?: string; // 地址
  legalRepresentative?: string;
  legalRepresentativePhone?: string;
  legalRepresentativePosition?: string;
  labPrincipal?: string;
  labPrincipalPhone?: string;
  labPrincipalPosition?: string;
  contactPerson?: string;
  contactPersonMobile?: string;
  contactPersonPosition?: string;
  website?: string;
  organizationCode?: string;
  contactPersonEmail?: string;
  // 附件字段
  applicationFile?: any[]; // 申请书
  legalStatusFile?: any[]; // 法律地位证明文件
  floorPlanFile?: any[]; // 实验室平面图
  otherFile?: any[]; // 其他附件
  remark?: string; // 申请备注

  // --- 实验室概况 Tab ---
  labPathogenicMicro?: number;
  labHealthMicro?: number;
  labDisinfection?: number;
  labToxicology?: number;
  labVectorSurveillance?: number;
  labPhysicalChemical?: number;
  labRadiological?: number;
  bsl2LabCount?: number;
  bsl1LabCount?: number;
  facilityFixed?: number;
  facilityOffSite?: number;
  facilityMobile?: number;
  facilitySingleSite?: number;
  facilityMultiSite?: number;
  agencyEstablishedYear?: number;
  labBuiltYear?: number;
  professionalTechStaff?: number;
  staffPercentage?: number;
  labArea?: number;
  registeredEquipment?: number;
  keyEquipmentCount?: number;

  // --- 能力与资质 Tab ---
  inspectionMajorClass?: string;
  inspectionMajorCount?: number;
  inspectionItemCount?: number;
  pathogenicMicroCount?: number;
  healthMicroCount?: number;
  physicalChemicalCount?: number;
  toxicologyCount?: number;
  occupationalHealthCount?: number;
  radiologicalHealthCount?: number;
  labEmergencyTestCount?: number;
  qualificationCertNo?: string;
  qualificationValidity?: string; // 日期字符串
  qualificationProjectCount?: number;
  accreditationCertNo?: string;
  accreditationValidity?: string; // 日期字符串
  accreditationProjectCount?: number;
  occupationalHealthQualification?: number;
  radiologicalHealthQualification?: number;
  otherQualifications?: string;

  // --- 管理体系建设 Tab ---
  labManagementSystem?: string;

  // --- 基本情况报告 Tab ---
  labBasicSituationReport?: string;
}

/**
 * 子表单组件暴露给父组件的Ref实例类型
 */
export interface ChildFormRef {
  validateFields: () => Promise<any>;
  getFormData: () => Record<string, any>;
  checkIsApplication?: (type: boolean) => Promise<any>;
}

/**
 * 子表单组件的通用Props
 */
export interface ChildFormProps {
  onRef: React.MutableRefObject<ChildFormRef | undefined>;
  readonly: boolean;
  initialData?: Partial<LabApplicationData>;
}