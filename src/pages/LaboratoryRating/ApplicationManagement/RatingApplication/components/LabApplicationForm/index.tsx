// /LabApplicationForm/index.tsx
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, Form, message, Modal, Tabs } from 'antd';
import { generateApplication, gradeConfigTip } from '@/api/apply';
import { downloadFile } from '@/api/file';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import FileViewByStreamByUrl from '@/components/FileViewByStreamByUrl';
import ElectronicRecord from '../ElectronicRecord';
import BasicInfo from './BasicInfo';
import BasicReport from './BasicReport';
import './index.less';
import LabOverview from './LabOverview';
import LabPower from './LabPower';
import ManagementSystem from './ManagementSystem';
import { ChildFormRef, LabApplicationData } from './types';

const { TabPane } = Tabs;

export interface LabApplicationFormProps {
  id?: any;
  onRef?: React.Ref<any>;
  readonly?: boolean; // 全局只读状态
  initialData?: any; // 用于编辑或详情回显的数据
  dialogTitletext?: string; // 状态
}

const LabApplicationForm: React.FC<LabApplicationFormProps> = ({
  id,
  onRef,
  readonly = false,
  initialData = {},
  dialogTitletext,
}) => {
  const [form] = Form.useForm();
  const basicInfoRef = useRef<ChildFormRef>();
  const labOverviewRef = useRef<ChildFormRef>();
  const labPowerRef = useRef<ChildFormRef>();
  const managementSystemRef = useRef<ChildFormRef>();
  const basicReportRef = useRef<ChildFormRef>();

  const [activeTab, setActiveTab] = useState('1');

  // 将所有子组件的 refs 存入数组，方便遍历
  const allChildRefs = [
    basicInfoRef,
    labOverviewRef,
    labPowerRef,
    managementSystemRef,
    basicReportRef,
  ];

  // 校验（isSubmit为true时进行子项验证，为false时不验证）
  const validateAll = async (isSubmit = false) => {
    try {
      await Promise.all(
        allChildRefs.map((ref) => ref.current?.validateFields())
      );
      
      // 只在提交申请时验证子项非空规则
      if (isSubmit) {
        const allData = await getAllData();
        console.log('getAllData结果:', allData);
        const validationResult = validateSubItems(allData);
        console.log('validateSubItems结果:', validationResult);
        if (!validationResult.isValid) {
          setActiveTab(validationResult.tabKey);
          message.error(validationResult.message);
          return false;
        }
      }
      
      return true;
    } catch (errorInfo: any) {
      // 自动切换到第一个校验失败的Tab
      switch (errorInfo) {
        case '基本信息':
          setActiveTab('1');
          break;
        case '实验室概况':
          setActiveTab('2');
          break;
        case '能力与资质':
          setActiveTab('3');
          break;
        case '实验室管理体系建设情况':
          setActiveTab('4');
          break;
        case '实验室基本情况报告':
          setActiveTab('5');
          break;
        default:
      }
      message.error(`请完善 ${errorInfo}模块 必填信息`);
      return false;
    }
  };

  // 获取所有子表单的数据
  const getAllData = async () => {
    try {
      const [basicInfo, labOverview, labPower, managementSystem, basicReport] =
        await Promise.all(
          allChildRefs.map(async (ref) => {
            return ref.current?.getFormData() || {};
          })
        );
      const allData: any = {
        ...initialData,
        ...basicInfo, // 基本信息
        labOverview: Object.keys(labOverview).length
          ? labOverview
          : initialData.labOverview, // 实验室概览数据
        labPower: Object.keys(labPower).length
          ? labPower
          : initialData.labPower, // 能力与资质数据
        ...managementSystem, // 实验室管理体系建设情况
        ...basicReport, // 实验室基本情况报告
      };
      return allData;
    } catch (error) {
      console.error('Error collecting form data:', error);
      return {} as LabApplicationData;
    }
  };

  // 验证子项非空规则
  const validateSubItems = (data: any) => {
    console.log('开始validateSubItems验证...');
    
    // 实验室概况验证：至少选择一个实验室类型
    const labTypes = [
      data.labOverview?.labPathogenicMicro,
      data.labOverview?.labHealthMicro,
      data.labOverview?.labDisinfection,
      data.labOverview?.labToxicology,
      data.labOverview?.labVectorSurveillance,
      data.labOverview?.labPhysicalChemical,
      data.labOverview?.labRadiological
    ];
    console.log('实验室类型检查 - labTypes:', labTypes);
    const hasLabType = labTypes.some(type => type === '1' || type === 1 || type === true);
    console.log('实验室类型检查 - hasLabType:', hasLabType);
    if (!hasLabType) {
      console.log('实验室类型验证失败');
      return {
        isValid: false,
        tabKey: '2',
        message: '实验室概况中至少需要选择一种实验室类型'
      };
    }

    // 实验室设施特点验证：至少选择一个设施特点
    const facilityTypes = [
      data.labOverview?.facilityFixed,
      data.labOverview?.facilityOffSite,
      data.labOverview?.facilityMobile
    ];
    console.log('设施特点检查 - facilityTypes:', facilityTypes);
    const hasFacilityType = facilityTypes.some(type => type === '1' || type === 1 || type === true);
    console.log('设施特点检查 - hasFacilityType:', hasFacilityType);
    if (!hasFacilityType) {
      console.log('设施特点验证失败');
      return {
        isValid: false,
        tabKey: '2',
        message: '实验室概况中至少需要选择一种实验室设施特点'
      };
    }

    // 能力与资质验证：检验检测能力至少填写一项
    const labPower = data.labPower || {};
    const testCapabilities = [
      labPower.inspectionMajorCount,
      labPower.inspectionItemCount,
      labPower.pathogenicMicroCount,
      labPower.healthMicroCount,
      labPower.physicalChemicalCount,
      labPower.toxicologyCount,
      labPower.occupationalHealthCount,
      labPower.radiologicalHealthCount,
      labPower.labEmergencyTestCount
    ];
    console.log('能力检查 - labPower:', labPower);
    console.log('能力检查 - testCapabilities:', testCapabilities);
    const hasTestCapability = testCapabilities.some(cap => cap && (Number(cap) > 0 || cap > 0));
    console.log('能力检查 - hasTestCapability:', hasTestCapability);
    if (!hasTestCapability) {
      console.log('检验检测能力验证失败');
      return {
        isValid: false,
        tabKey: '3',
        message: '能力与资质中检验检测能力至少需要填写一项'
      };
    }

    // 实验室管理体系建设情况验证：必须填写内容
    console.log('管理体系检查 - labManagementSystem:', data.labManagementSystem);
    if (!data.labManagementSystem || data.labManagementSystem.trim() === '') {
      console.log('管理体系验证失败');
      return {
        isValid: false,
        tabKey: '4',
        message: '实验室管理体系建设情况不能为空'
      };
    }

    // 实验室基本情况报告验证：必须填写内容
    console.log('基本报告检查 - labBasicSituationReport:', data.labBasicSituationReport);
    if (!data.labBasicSituationReport || data.labBasicSituationReport.trim() === '') {
      console.log('基本情况报告验证失败');
      return {
        isValid: false,
        tabKey: '5',
        message: '实验室基本情况报告不能为空'
      };
    }

    console.log('所有验证通过');
    return { isValid: true };
  };

  // 暴露方法给外部组件调用
  useImperativeHandle(onRef, () => ({
    // 统一校验所有子表单
    validateAll,
    // 获取所有子表单的数据
    getAllData,
  }));

  // 是否是草稿
  const [isDraft, setIsDraft] = useState<boolean>(false);

  // 考核类型ID
  const [assessmentTypeId, setAssessmentTypeId] = useState<string>('');

  const [detailInfo, setDetailInfo] = useState<any>({});

  // 选中值转换
  const transformCheckboxValues = {
    // Convert true/false to "1"/"0" for form submission
    out: (formData: Record<string, any>, CHECKBOX_KEYS_ARR: any[]) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS_ARR.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] ? '1' : '0';
        }
      });
      return transformed;
    },

    // Convert "1"/"0" back to true/false for form display
    in: (formData: Record<string, any>, CHECKBOX_KEYS_ARR: any[]) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS_ARR.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] === '1';
        }
      });
      return transformed;
    },
  };

  // 获取表单数据详情（统一处理所有数据）
  const getDetailData = () => {
    try {
      if (id && initialData) {
        const record = clone(initialData);
        // 基本信息处理
        record.file = record.file
          ? JSON.parse(record.file).map((_item: any) => {
              return {
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },
              };
            })
          : [];
        record.otherFile = record.otherFile
          ? JSON.parse(record.otherFile).map((_item: any) => {
              return {
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },
              };
            })
          : [];
        setIsDraft(initialData.status === '0');
        // 实验室概况处理
        if (record.labOverview && Object.keys(record.labOverview).length) {
          const CHECKBOX_KEYS = [
            'labPathogenicMicro',
            'labHealthMicro',
            'labDisinfection',
            'labToxicology',
            'labVectorSurveillance',
            'labPhysicalChemical',
            'labRadiological',
            'facilityFixed',
            'facilityOffSite',
            'facilityMobile',
          ];
          record.labOverview = transformCheckboxValues.in(
            record.labOverview,
            CHECKBOX_KEYS
          );
        }
        // 能力与资质处理
        if (record.labOverview && Object.keys(record.labPower).length) {
          const CHECKBOX_KEYS = [
            'occupationalHealthQualification',
            'radiologicalHealthQualification',
          ];
          record.labPower = transformCheckboxValues.in(
            record.labPower,
            CHECKBOX_KEYS
          );
        }

        console.log('LabApplicationForm中原始commitment:', record.commitment, typeof record.commitment);
        const recordNew = {
          ...record,
          commitment: (record.commitment === 1 || record.commitment === '1') ? ['1'] : [],
        };
        console.log('LabApplicationForm处理后commitment:', recordNew.commitment);
        setDetailInfo({ ...recordNew });
        setAssessmentTypeId(initialData.assessmentType);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    getDetailData();
  }, [initialData]);

  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [tipsValue, setTipsValue] = useState<any>('');

  const getGradeConfigTip = async () => {
    try {
      const { code, data, msg } = await gradeConfigTip();
      setTipsValue(msg);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getGradeConfigTip();
  }, []);

  // word等文件预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  const [fileUrl, setFileUrl] = useState<any>();
  const handleLookPdf = () => {
    setOpenPreview(true);
    setFileUrl('/gradeConfig/previewGradeFile');
  };

  const [openOverview, setOpenOverview] = useState<boolean>(false);

  const handleLookAllImg = () => {
    // setOpenAllImg(true)
    setOpenOverview(true);
  };

  // 生成申请书
  const handleGenerateApplication = async () => {
    try {
      // 修改申请书附件非必填
      basicInfoRef.current?.checkIsApplication &&
        basicInfoRef.current?.checkIsApplication(false);
      // 生成申请书时不需要验证子项规则
      const isValidate = await validateAll(false);
      if (isValidate) {
        const formParams = await getAllData();
        const params = {
          ...formParams,
          commitment: formParams.commitment[0]
            ? formParams.commitment[0]
            : null,
        };
        delete params.file;
        delete params.otherFile;
        const { code, data, msg } = await generateApplication(params);
        if (code === 200) {
          await downloadFile(data.ossId, data.fileName);
          message.success('生成成功');
          return Promise.resolve(true);
        } else {
          message.error(msg);
          return Promise.reject(false);
        }
      }
    } catch (error) {
      message.error('生成失败');
      return Promise.reject(error);
    } finally {
      basicInfoRef.current?.checkIsApplication &&
        basicInfoRef.current?.checkIsApplication(true);
    }
  };

  return (
    <>
      <div className="d-flex top-txt-wraper">
        <div className="txt-1">
          <ExclamationCircleOutlined />
          温馨提示：这里展示评级申请提示性的文案内容，文案内容在评级设置中配置。字多的时候可以点击打开弹窗查看更多的内容字多的时候可以点击弹。
          <Button
            type="link"
            size="small"
            onClick={() => {
              setOpenDetail(true);
            }}
          >
            更多
          </Button>
        </div>
        <div className="btn-wraper">
          <Button
            onClick={() => handleLookPdf()}
            type="primary"
            className="mr-2 ml-2"
          >
            等级评审指标文件
          </Button>
          <Button onClick={() => handleLookAllImg()}>支持材料集</Button>
        </div>
      </div>
      <BlockContainer>
        <ProForm
          form={form}
          submitter={false}
          initialValues={initialData}
          layout="horizontal"
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="基本信息" key="1" forceRender>
              <BasicInfo
                onRef={basicInfoRef}
                readonly={readonly}
                initialData={detailInfo}
                dialogTitletext={dialogTitletext}
                onGenerateApplication={handleGenerateApplication}
              />
            </TabPane>
            <TabPane tab="实验室概况" key="2" forceRender>
              <LabOverview
                onRef={labOverviewRef}
                readonly={readonly}
                initialData={
                  detailInfo?.labOverview ? detailInfo?.labOverview : {}
                }
              />
            </TabPane>
            <TabPane tab="能力与资质" key="3" forceRender>
              <LabPower
                onRef={labPowerRef}
                readonly={readonly}
                initialData={detailInfo?.labPower ? detailInfo?.labPower : {}}
              />
            </TabPane>
            <TabPane tab="实验室管理体系建设情况" key="4" forceRender>
              <ManagementSystem
                onRef={managementSystemRef}
                readonly={readonly}
                initialData={detailInfo}
              />
            </TabPane>
            <TabPane tab="实验室基本情况报告" key="5" forceRender>
              <BasicReport
                onRef={basicReportRef}
                readonly={readonly}
                initialData={detailInfo}
              />
            </TabPane>
          </Tabs>
        </ProForm>
      </BlockContainer>
      {/* 录入查找样本编号 */}
      <Modal
        title="温馨提示"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={1200}
      >
        <div className="p-4">
          <div
            dangerouslySetInnerHTML={{
              __html: tipsValue.replace(/\n/g, '<br>'),
            }}
          />
          <div className="flex justify-end space-x-2">
            <Button onClick={() => setOpenDetail(false)}>知道了</Button>
          </div>
        </div>
      </Modal>
      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <FileViewByStreamByUrl fileUrl1={fileUrl} isPreview />
      </Modal>

      {/* 电子档案 */}
      <ElectronicRecord
        open={openOverview}
        close={() => {
          // tableReload();
          setOpenOverview(false);
        }}
        applyNo={1}
      />
    </>
  );
};

export default React.memo(LabApplicationForm);
