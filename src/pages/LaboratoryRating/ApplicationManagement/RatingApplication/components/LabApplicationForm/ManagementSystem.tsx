// /LabApplicationForm/ManagementSystem.tsx
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { FormInstance, Col } from 'antd';
import { ProForm, ProFormTextArea } from '@ant-design/pro-components';
import { ChildFormProps } from './types';

const ManagementSystem: React.FC<ChildFormProps> = ({
  onRef,
  readonly,
  initialData,
}) => {
  const formRef = useRef<FormInstance>();

  useImperativeHandle(onRef, () => ({
    validateFields: async() => {
      try {
        await formRef.current?.validateFields();
        return true;
      } catch (error) {
        throw ("实验室管理体系建设情况");
      }
    },
    getFormData: () => formRef.current?.getFieldsValue() || {},
  }));

  useEffect(() => {
    if (initialData && formRef.current) {
      formRef.current.setFieldsValue(initialData);
    }
  }, [initialData]);

  return (
    <ProForm
      formRef={formRef}
      submitter={false}
      initialValues={initialData}
      layout="horizontal"
    >
      <Col className="mb-2">实验室管理体系建设情况（文字描述）：</Col>
      <ProFormTextArea
        name="labManagementSystem"
        label={null}
        placeholder={readonly ? '暂无数据' : '请输入实验室管理体系建设情况'}
        allowClear
        fieldProps={{
          readOnly: readonly,
          rows: 10
        }}
      />
    </ProForm>
  );
};

export default React.memo(ManagementSystem);
