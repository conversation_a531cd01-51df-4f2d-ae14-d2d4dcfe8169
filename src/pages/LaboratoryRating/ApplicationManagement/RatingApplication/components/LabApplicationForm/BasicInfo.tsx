/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-throw-literal */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, message, Modal } from 'antd';
import { getLabInfo, gradeConfig } from '@/api/apply';
import { useQualityStore } from '@/store/quality';
import {
  FormInstance,
  ProForm,
  ProFormCheckbox,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import { FormInitVal } from '../data';

const layoutProps = {
  colProps: { span: 8 },
};

type TEditProps = {
  id?: any;
  initialData?: any;
  onRef?: any;
  readonly?: boolean;
  dialogTitletext?: string;
  onGenerateApplication: () => Promise<boolean | undefined>;
};

// 辅助函数：将 API 返回的文件数据字符串解析为 antd Form 所需的格式
const parseApiFileData = (fileString: string) => {
  if (!fileString) return [];
  try {
    return JSON.parse(fileString).map((file: any) => ({
      uid: file.ossId || file.id,
      name: file.fileName || file.name,
      status: 'done',
      url: file.url,
      response: {
        code: 200,
        data: {
          fileName: file.fileName || file.name,
          ossId: file.ossId || file.id,
          url: file.url,
          size: file.size,
        },
      },
    }));
  } catch (error) {
    console.error('解析文件数据失败:', error);
    return [];
  }
};

// 辅助函数：将在表单中上传的文件列表格式化为提交给 API 的格式
const formatFileListForApi = (fileList: any[]) => {
  if (!fileList || fileList.length === 0) {
    return '';
  }
  const processed = fileList
    .filter((item) => item.response?.data?.ossId)
    .map((item) => ({
      fileName: item.response.data.fileName,
      ossId: item.response.data.ossId,
      url: item.response.data.url,
      size: item.response.data.size,
    }));
  return processed;
};

const BaseForm: React.FC<TEditProps> = ({
  id,
  initialData,
  onRef,
  readonly = false,
  dialogTitletext,
  onGenerateApplication,
}) => {
  const formRef = useRef<FormInstance>(null);
  const [openDetailMsg, setOpenDetailMsg] = useState<boolean>(false);
  const [gradeList, setGradeList] = useState<any>([]);
  const [labInfoData, setLabInfoData] = useState<any>({});

  const { assessmentTypesOnForm2, getAssessmentTypesOnForm2 } =
    useQualityStore();

  // 暴露父组件方法
  useImperativeHandle(onRef, () => ({
    getFormData: async () => {
      // await formRef.current?.validateFields();
      const formInfo = cloneDeep(formRef.current?.getFieldsValue());
      const params = {
        ...labInfoData,
        ...formInfo,
        file: formatFileListForApi(formInfo.file),
        otherFile: formatFileListForApi(formInfo.otherFile),
      }; 
      return params;
    },
    validateFields: async () => {
      try {
        await formRef.current?.validateFields();
        return true;
      } catch (error) {
        throw '基本信息';
      }
    },
    // 修改申请书是否校验
    checkIsApplication: async (type: boolean) => {
      setIsApplication(type)
    }
  }));

  const gradeConfigData = async () => {
    try {
      const { data } = await gradeConfig();
      setGradeList(
        data.map((item: any) => ({ label: item.grade, value: item.id }))
      );
    } catch (err) {
      console.error(err);
    }
  };

  const getLabInfoData = async () => {
    try {
      const { data } = await getLabInfo();
      setLabInfoData(data);
      if (formRef.current) {
        formRef.current.setFieldsValue({
          name: data.labName,
          areaName: `${data.provinceName}${data.cityName}${data.areaName}`,
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 生成申请书按钮加载
  const [applicationLoading, setApplicationLoading] = useState<boolean>(false);

  // 是否校验申请书必填
  const [isApplication, setIsApplication] = useState<boolean>(true);

  // 生成申请书按钮提交
  const handleGenerateApplication = async () => {
    try {
      setApplicationLoading(true);
      await onGenerateApplication();
    } catch (error) {
      console.error(error)
    } finally {
      setApplicationLoading(false);
    }
  };

  useEffect(() => {
    if (dialogTitletext === '新增') {
      getLabInfoData();
    }
    gradeConfigData();
    getAssessmentTypesOnForm2();
  }, []);

  useEffect(() => {
    if (initialData) {
      console.log('BasicInfo接收到的initialData:', initialData);
      console.log('BasicInfo中commitment值:', initialData.commitment);
      const processedData = {
        ...initialData,
        name: initialData.labName,
        // 确保commitment字段正确处理
        commitment: initialData.commitment || [],
      };
      console.log('BasicInfo设置到表单的数据:', processedData);
      formRef.current?.setFieldsValue(processedData);
    }
  }, [initialData]);

  return (
    <>
      <ProForm
        formRef={formRef}
        labelCol={{ flex: '0 0 135px' }}
        layout="horizontal"
        grid={true}
        submitter={false}
        readonly={readonly}
        initialValues={FormInitVal}
        onValuesChange={(_: any, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        {/* 第 1 行 */}
        <ProFormSelect
          name="type"
          label="申请类型"
          rules={[{ required: true, message: '请选择申请类型' }]}
          options={[
            {
              label: '初次评审',
              value: '1',
            },
            {
              label: '等级复合',
              value: '2',
            },
          ]}
          {...layoutProps}
          placeholder="请选择"
        />
        <ProFormSelect
          name="setId"
          label="申请等级"
          rules={[{ required: true, message: '请选择申请等级' }]}
          options={gradeList}
          {...layoutProps}
          placeholder="请选择"
          extra={
            !readonly ? (
              <div style={{ color: '#1677ff' }}>
                注：专家以申请的等级进行评审
              </div>
            ) : null
          }
        />
        <Col span={8}>
          <ProFormCheckbox.Group
            name="commitment"
            label="申请承诺"
            rules={[{ required: true, message: '请勾选承诺' }]}
            options={[{ label: '承诺满足必备要求', value: '1' }]}
            extra={
              !readonly ? (
                <div style={{ color: '#1677ff' }}>
                  注：原则上以评审周期为限。
                </div>
              ) : null
            }
          />
          <Button
            color="primary"
            size="small"
            variant="dashed"
            onClick={() => setOpenDetailMsg(true)}
            style={{ position: 'absolute', top: '2px', left: '300px' }}
          >
            阅读要求
          </Button>
        </Col>

        {/* 第 2 行 */}
        <ProFormText name="name" label="机构名称" readonly {...layoutProps} />
        <ProFormText
          name="areaName"
          label="所属区域"
          readonly
          {...layoutProps}
        />
        <ProFormText
          name="address"
          label="地址"
          rules={[{ required: true }]}
          placeholder="请输入内容"
          {...layoutProps}
        />

        {/* 第 3 行 - 法定代表人 */}
        <ProFormText
          name="legalRepresentative"
          label="法定代表人"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="legalRepresentativePhone"
          label="法定代表人电话"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="legalRepresentativePosition"
          label="法定代表人职务"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />

        {/* 第 4 行 - 实验室负责人 */}
        <ProFormText
          name="labPrincipal"
          label="实验室负责人"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="labPrincipalPhone"
          label="实验室负责人电话"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="labPrincipalPosition"
          label="实验室负责人职务"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />

        {/* 第 5 行 - 联系人 */}
        <ProFormText
          name="contactPerson"
          label="联系人"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="contactPersonMobile"
          label="联系人手机"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="contactPersonPosition"
          label="联系人职务"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />

        {/* 第 6 行 */}
        <ProFormText
          name="website"
          label="网址"
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="organizationCode"
          label="统一社会信用代码"
          rules={[{ required: true }]}
          placeholder="请输入"
          {...layoutProps}
        />
        <ProFormText
          name="contactPersonEmail"
          label="联系人电子信箱"
          rules={[
            {
              required: true,
              type: 'email',
              message: '请输入有效的邮箱地址',
            },
          ]}
          placeholder="请输入"
          {...layoutProps}
        />

        {/* 文件上传 */}
        <EProFormUploadButton
          labelCol={{ flex: '0 0 135px' }}
          name="file"
          label="申请书"
          readonly={readonly}
          rules={[{ required: isApplication, message: '请上传申请书' }]}
          max={10}
          colProps={{ span: 24 }}
          // extra="上传盖章版本申请书"
          extra={
            <div>
              {!readonly ? (
                <>
                  <p style={{ margin: 0, lineHeight: 1.5 }}>
                    请上传盖章版本申请书
                  </p>
                  <Button
                    loading={applicationLoading}
                    type="primary"
                    className="mt-3"
                    onClick={handleGenerateApplication}
                  >
                    生成申请书
                  </Button>
                </>
              ) : (
                <p style={{ margin: 0, lineHeight: 1.5 }}>盖章版本申请书</p>
              )}
            </div>
          }
        />

        <EProFormUploadButton
          labelCol={{ flex: '0 0 135px' }}
          name="otherFile"
          label="其他附件"
          max={10}
          colProps={{ span: 24 }}
          readonly={readonly}
          extra={
            <div>
              <p style={{ margin: 0, lineHeight: 1.5 }}>
                附件1: 实验室法律地位的证明文件(法人营业执照).pdf
              </p>
              <p style={{ margin: 0, lineHeight: 1.5 }}>附件2: 实验室平面图</p>
            </div>
          }
        />

        <ProFormTextArea
          name="remark"
          label="申请备注"
          placeholder="请输入申请备注"
          colProps={{ span: 24 }}
        />
      </ProForm>

      {/* --- 以下是原组件中的模态框和抽屉，功能保持不变 --- */}
      <Modal
        title="必备要求"
        open={openDetailMsg}
        footer={[
          <Button
            key="submit"
            type="primary"
            onClick={() => setOpenDetailMsg(false)}
          >
            我已知晓，并且满足必备要求
          </Button>,
        ]}
        onCancel={() => setOpenDetailMsg(false)}
        width={1200}
      >
        <div className="p-4">
          <div>
            <div className="title">第一部分 必备要求</div>
            <div className="title-1">一、依法设置与规范从业</div>
            <p className="text">
              （一）严格遵守《中华人民共和国传染病防治法》，未造成传染病传播、流行或其他严重后果；且未发生其他重大卫生违规事件，未造成严重后果或情节严重；且近两年来卫生健康、疾控行政部门或监督执法机构对其进行传染病防治分类监督综合评价未被列为重点监督单位。
            </p>
            <p className="text">
              （二）严格遵守《中华人民共和国疫苗管理法》，未出现违法违规采购或使用疫苗并造成严重后果的情况。
            </p>
            <p className="text">
              （三）严格遵守《中华人民共和国职业病防治法》及其配套法规，未出现违规开展职业卫生技术、放射卫生服务等情况并造成严重后果的情况。
            </p>
            <p className="text">
              （四）严格遵守《中华人民共和国食品安全法》及其配套法规，未出现履职不到位而导致严重后果的情况。
            </p>
            <p className="text">
              （五）未发生过其他造成严重后果或情节严重的重大违法、违规事件。
            </p>
            <div className="title-1">二、党风行风诚信和公益责任</div>
            <p className="text">
              （一）
              全面加强党的领导，全面履行从严治党主体责任、监督责任，未受到过上级党组织的问责处理。
            </p>
            <p className="text">
              （二）领导班子发生严重职务犯罪或严重违纪事件少于1起，且未出现过严重违反职业道德的群体性事件等情况。
            </p>
            <p className="text">
              （三）严格遵守《中华人民共和国统计法》、《医学科研诚信和相关行为规范》等相关法律法规，未出现统计和报告造假、虚假提供申报材料和科研成果等情况。
            </p>
            <p className="text">
              （四）按照国家和省疾控行政部门要求，按时完成对口支援、中国援外医疗队、突发公共事件医疗救援、公共卫生任务等政府指令性工作。
            </p>
            <div className="title-1">三、安全管理与重大事件</div>
            <p className="text">
              （一）未发生过实验室感染等重大生物安全事件并造成严重后果。
            </p>
            <p className="text">
              （二）未发生过火灾、爆炸、放射源泄漏及丢失、有害气体泄漏、菌（毒）株丢失等情况。
            </p>
            <p className="text">
              （三）未出现过瞒报、漏报重大公共卫生过失事件的行为。
            </p>
            <p className="text">
              （四）未发生过泄密、大规模公共卫生数据泄露或其他重大网络安全事件并造成严重后果。
            </p>
            <p className="text">注：原则上以评审周期为限。</p>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default memo(BaseForm);
