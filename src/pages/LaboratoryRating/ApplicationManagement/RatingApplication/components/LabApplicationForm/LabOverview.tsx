// /LabApplicationForm/LabOverview.tsx
import React, { useEffect, useImperativeHandle, useRef } from 'react';
// 1. 引入 Row 和 Col
import { Col, FormInstance, Radio, Row } from 'antd';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDigit,
  ProFormGroup,
  ProFormItem,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { ChildFormProps } from './types';

const CHECKBOX_KEYS = [
  'labPathogenicMicro',
  'labHealthMicro',
  'labDisinfection',
  'labToxicology',
  'labVectorSurveillance',
  'labPhysicalChemical',
  'labRadiological',
  'facilityFixed',
  'facilityOffSite',
  'facilityMobile',
];

const layoutLabel = {
  labelCol: { flex: '0 0 135px' },
};

const LabOverview: React.FC<ChildFormProps> = ({
  onRef,
  readonly,
  initialData,
}) => {
  const formRef = useRef<FormInstance>();

  // 选中值转换
  const transformCheckboxValues = {
    // Convert true/false to "1"/"0" for form submission
    out: (formData: Record<string, any>) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] ? '1' : '0';
        }
      });
      return transformed;
    },

    // Convert "1"/"0" back to true/false for form display
    in: (formData: Record<string, any>) => {
      const transformed = { ...formData };
      CHECKBOX_KEYS.forEach((key) => {
        if (key in transformed) {
          transformed[key] = transformed[key] === '1';
        }
      });
      return transformed;
    },
  };

  useImperativeHandle(onRef, () => ({
    validateFields: async() => {
      try {
        await formRef.current?.validateFields();
        return true;
      } catch (error) {
        throw ("实验室概况");
      }
    },
    getFormData: () => {
      const formData = formRef.current?.getFieldsValue() || {};
      return transformCheckboxValues.out(formData);
    },
  }));

  useEffect(() => {
    if (initialData && formRef.current) {
      formRef.current.setFieldsValue(initialData);
    }
  }, [initialData]);

  const transform = {
    in: (value: number) => value === 1,
    out: (value: boolean) => (value ? 1 : 0),
  };

  return (
    <ProForm
      formRef={formRef}
      layout="horizontal"
      grid={false}
      submitter={false}
      readonly={readonly}
      initialValues={initialData}
    >
      <div className="flex flex-col gap-5">
        <BlockContainer title="实验室包括">
          <ProForm.Group>
            <ProFormCheckbox
              name="labPathogenicMicro"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              病原微生物实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labHealthMicro"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              卫生微生物实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labDisinfection"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              消毒鉴定实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labToxicology"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              毒理学实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labVectorSurveillance"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              病媒生物监测实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labPhysicalChemical"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              理化实验室
            </ProFormCheckbox>
            <ProFormCheckbox
              name="labRadiological"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              放射卫生实验室
            </ProFormCheckbox>
          </ProForm.Group>
          <ProForm.Group>
            <ProFormDigit
              name="bsl2LabCount"
              label="BSL-2实验室"
              width="sm"
              addonAfter="个"
            />
            <ProFormDigit
              name="bsl1LabCount"
              label="BSL-1实验室"
              width="sm"
              addonAfter="个"
            />
          </ProForm.Group>
        </BlockContainer>

        <BlockContainer title="实验室设施特点">
          <ProForm.Group>
            <ProFormCheckbox
              name="facilityFixed"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              固定
            </ProFormCheckbox>
            <ProFormCheckbox
              name="facilityOffSite"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              离开固定设施的现场
            </ProFormCheckbox>
            <ProFormCheckbox
              name="facilityMobile"
              transform={transform}
              fieldProps={{
                disabled: readonly,
              }}
            >
              可移动
            </ProFormCheckbox>
          </ProForm.Group>
        </BlockContainer>
        <BlockContainer title="实验室场所特点">
          <ProFormItem name="facilitySingleSite">
            <Radio.Group
              disabled={readonly}
              options={[
                { label: '单一场所', value: '0' },
                { label: '多场所', value: '1' },
              ]}
            />
          </ProFormItem>
        </BlockContainer>

        <BlockContainer title="实验室人员及设施">
          {/* 2. 使用 <Row> 和 <Col> 创建一个均匀的三列网格布局 */}
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="agencyEstablishedYear"
                label="疾控机构成立年份"
                width="sm"
                addonAfter="年"
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="labBuiltYear"
                label="实验室建于"
                width="sm"
                addonAfter="年"
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="professionalTechStaff"
                label="现配置专业技术人员"
                width="sm"
                addonAfter="名"
              />
            </Col>

            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="staffPercentage"
                label="占本中心总人数"
                width="sm"
                addonAfter="%"
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="labArea"
                label="实验室面积"
                width="sm"
                addonAfter="平方米"
              />
            </Col>
            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="registeredEquipment"
                label="登记仪器设备"
                width="sm"
                addonAfter="台 (套)"
              />
            </Col>

            <Col span={8}>
              <ProFormDigit
                {...layoutLabel}
                name="keyEquipmentCount"
                label="中大型/关键设备"
                width="sm"
                addonAfter="名 (套)"
                extra="采购单价20万元以上"
              />
            </Col>
          </Row>
        </BlockContainer>
      </div>
    </ProForm>
  );
};

export default React.memo(LabOverview);
