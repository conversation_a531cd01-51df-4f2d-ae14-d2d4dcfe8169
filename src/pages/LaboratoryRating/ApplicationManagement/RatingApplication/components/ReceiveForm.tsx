/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, InputNumber, message, Modal, Table, Typography } from 'antd';
import { getIndicesList } from '@/api/apply';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import { log } from 'console';
import BlockContainer from '@/components/BlockContainer';
import UploadList from './UploadList';

type TEditProps = {
  loading?: boolean;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  onRef,
  mode,
  detailInfo,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: (status: any = 'submit') => {
        // formRef.current?.submit();
        // console.log(idicesList, dataSourceArray, 1111);

        // dataSourceArrayb遍历，只要有一个selfScores不为空，就提示用户,然后停止循环
        const flattenedArray = dataSourceArray.flat();
        if (status === 'submit') {
          for (let index = 0; index < flattenedArray.length; index++) {
            if (
              !flattenedArray[index].selfScore &&
              isNaN(flattenedArray[index].selfScore)
            ) {
              message.error('请完善自评得分'); // 使用 message 提示用户
              return null;
            }
            if (
              (flattenedArray[index].selfScore > 0 ||
                flattenedArray[index].selfScore === 0) &&
              flattenedArray[index].selfScore > flattenedArray[index].score * 1
            ) {
              message.error('自评得分不得高于指标评分，请重新填写'); // 使用 message 提示用户
              return null;
            }
          }
        }
        const updatedIdicesList = idicesList.map((mainItem: any) => {
          const newMainItem = { ...mainItem };
          if (newMainItem.children) {
            newMainItem.children = newMainItem.children.map(
              (childItem: any) => {
                const newChildItem = { ...childItem };
                if (newChildItem.children) {
                  newChildItem.children = newChildItem.children.map(
                    (grandChildItem: any) => {
                      let obj = {};
                      dataSourceArray.flat().forEach((dsItem: any) => {
                        // console.log(dsItem.id, grandChildItem.id, grandChildItem.id === dsItem.id);
                        if (grandChildItem.id === dsItem.id) {
                          // console.log({ ...grandChildItem, ...dsItem }, 11111);
                          obj = { ...grandChildItem, ...dsItem };
                        }
                      });
                      // console.log(obj, 22222222);

                      return obj;
                    }
                  );
                }
                return newChildItem;
              }
            );
          }
          return newMainItem;
        });
        // console.log(updatedIdicesList, 111111111);

        return updatedIdicesList;
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);

  const tableRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<any>([[], []]);
  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [tableRefArray, setTableRefArray] = useState<any[]>([]);

  const [dataSourceArray, setDataSourceArray] = useState<any[]>([]);

  const [sampleNumArray, setSampleNumArray] = useState<any[]>([]);

  const [sourceTotalNumArray, setSourceTotalNumArray] = useState<any[]>([]);

  const [docs, setDocs] = useState<any>('');

  const [sampleNum, setSampleNum] = useState<any>(0);
  const [sourceTotal, setSourceTotal] = useState<any>(0);
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const formRef2 = useRef<any>(null);

  // 新增用于强制刷新表格的状态
  const [refreshKey, setRefreshKey] = useState(0);

  //当前上传附件的id
  const [curId, setCurId] = useState<any>('');

  const [tipsWord, setTipsWord] = useState<any>('');

  useEffect(() => {
    if (dialogTitletext == '新增') {
      getIndicesListData();
    }
  }, []);

  const [idicesList, setIndicesList] = useState<any>([]);

  // 解析三层嵌套数据

  // 解析三层嵌套数据
  const setTableRefArray1 = (data: any[], parentLabs: string[] = []) => {
    let result: any[] = [];
    if (Array.isArray(data)) {
      data.forEach((item) => {
        const currentParentLabs = [...parentLabs];
        if (item.lab) {
          currentParentLabs.push(item.lab);
        }
        if (item.children) {
          // 若当前项存在非空的 children 数组，递归调用函数处理子节点
          result = result.concat(
            setTableRefArray1(item.children, currentParentLabs)
          );
        } else {
          // 若当前项没有子节点或子节点为空，将各级父级 lab 添加到当前项并添加到结果数组
          const newItem = { ...item };
          newItem.parentLabs = currentParentLabs;
          result.push(newItem);
        }
      });
    }
    return result;
  };

  const getIndicesListData = async () => {
    try {
      const { code, data, msg } = await getIndicesList();
      const gradeListData = data;
      setIndicesList(gradeListData);
      const editId: any = [];
      const arrContainer: any = [];
      let tips: any = '';
      gradeListData.forEach((e: any, index: number) => {
        let arrData: any = [];
        e.children.forEach((e1: any, index1: number) => {
          const arrData2 = e1.children.map((e2: any, index2: number) => {
            tips = e2.tip;
            return {
              ...e2,
              parentLab: e1.lab,
            };
          });
          arrData.push(...arrData2);
        });
        arrContainer[index] = calculateRowSpan(arrData);
        setDataSourceArray(arrContainer);
        const sourceTotalNum = gradeListData.map((e: any) => {
          return e.score;
        });
        setSourceTotalNumArray(sourceTotalNum);
        if (dialogTitletext !== '查看') {
          const newKeyId = arrData.map((e: any) => {
            return e.id;
          });
          editId[index] = newKeyId;
        }
        setEditableRowKeys(editId);
      });
      const arr: any = [];
      data.forEach((e: any) => {
        arr.push(useRef<FormInstance>(null));
      });
      setTipsWord(tips);
      setTableRefArray(arr);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  // 计算 rowSpan 的函数
  const calculateRowSpan = (data: any[]) => {
    const result = [...data];
    const countMap = new Map<string, number>();
    const startIndexMap = new Map<string, number>();

    // 第一次遍历，记录每个 parentLab 首次出现的索引和出现次数
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      if (!countMap.has(parentLab)) {
        countMap.set(parentLab, 1);
        startIndexMap.set(parentLab, index);
      } else {
        countMap.set(parentLab, countMap.get(parentLab)! + 1);
      }
    });

    // 第二次遍历，设置 rowSpan
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      const startIndex = startIndexMap.get(parentLab)!;
      const count = countMap.get(parentLab)!;
      if (index === startIndex) {
        item.sampleCodeRowSpan = count;
      } else {
        item.sampleCodeRowSpan = 0;
      }
    });

    return result;
  };

  useEffect(() => {}, [dataSource, dataSource1]);

  const columns = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'selfScore',
      // 使用 renderFormItem 渲染数字输入框
      renderFormItem: (_: any, record: any) => (
        <InputNumber
          value={record.record?.selfScore}
          onChange={(value: any) => {
            // 添加输入值范围校验
            if (
              value !== undefined &&
              (value < 0 || value > record.record?.score * 1)
            ) {
              message.error('自评得分必须在 0 到 10 之间');
              return;
            }

            // 这里可以添加更新 record 数据的逻辑
            const newDataSourceArray = [...dataSourceArray];
            let curIndex: any;
            console.log(newDataSourceArray, 11111111);

            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.selfScore = value;
                  curIndex = index;
                }
              });
            });
            setDataSourceArray(newDataSourceArray);
            const total = dataSourceArray[curIndex].reduce(
              (total: any = 0, arr: any) => {
                if (arr.selfScore === undefined) {
                  arr.selfScore = 0;
                }
                return total + arr.selfScore * 1;
              },
              0
            );

            const newsampleNumArray = [...sampleNumArray];
            newsampleNumArray[curIndex] = total;
            setSampleNumArray(newsampleNumArray);
            const totals2 = newsampleNumArray.reduce(
              (total: any = 0, arr: any) => {
                if (arr === undefined) {
                  arr = 0;
                }
                return total + arr;
              },
              0
            );
            // console.log(dataSourceArray, 1111);
            setSampleNum(totals2);
          }}
          min={0} // 可根据需求设置最小值
          max={record.record?.score * 1} // 可根据需求设置最小值
          // 可根据需求添加其他属性，如 max 等
        />
      ),
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        //?打了这个log 支撑数据才能保存上？删除请慎重
        // console.log(record?.docs && record.docs?.length> 0);
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        //?打了这个log 支撑数据才能保存上？删除请慎重
        // console.log(record?.docs && record.docs?.length> 0);
        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },
  ];

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计

  useEffect(() => {
    if (dialogTitletext !== '新增' && detailInfo) {
      // console.log(detailInfo,detailInfo.indices, '详情数组数据');
      if (detailInfo.indices) {
        const gradeListData = detailInfo.indices;
        setIndicesList(gradeListData);
        const arrContainer: any = [];
        const editId: any = [];
        gradeListData.forEach((e: any, index: number) => {
          let arrData: any = [];
          e.children.forEach((e1: any, index1: number) => {
            const arrData2 = e1.children.map((e2: any, index2: number) => {
              return {
                ...e2,
                parentLab: e1.lab,
              };
            });
            arrData.push(...arrData2);
          });
          arrContainer[index] = calculateRowSpan(arrData);
          const sourceTotalNumArray: any = [];
          arrContainer.forEach((item: any, index: number) => {
            const total = item.reduce((total: any = 0, arr: any) => {
              if (arr.selfScore === undefined) {
                arr.selfScore = 0;
              }
              return total + arr.selfScore * 1;
            }, 0);
            sourceTotalNumArray.push(total);
          });
          setSampleNumArray(sourceTotalNumArray);
          const totals2 = sourceTotalNumArray.reduce(
            (total: any = 0, arr: any) => {
              if (arr === undefined) {
                arr = 0;
              }
              return total + arr;
            },
            0
          );
          console.log(dataSourceArray, 1111);
          setSampleNum(totals2);

          const sourceTotalNum = gradeListData.map((e: any) => {
            return e.score;
          });
          setDataSourceArray(arrContainer);
          setSourceTotalNumArray(sourceTotalNum);
          if (dialogTitletext !== '查看') {
            const newKeyId = arrData.map((e: any) => {
              return e.id;
            });
            editId[index] = newKeyId;
          }
          setEditableRowKeys(editId);
        });
        setTimeout(() => {
          const arr: any = [];
          gradeListData.forEach((e: any) => {
            arr.push(useRef<FormInstance>(null));
          });
          setTableRefArray(arr);
        });
      }
    }
  }, [detailInfo]);

  // 新增状态用于存储上传的附件数据
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 定义回调函数，用于更新上传的附件数据
  // console.log(111111111);

  const handleFileUpload = (files: any) => {
    console.log(files, '上传附件内容');
    setUploadedFiles(files);
    const newDataSourceArray = [...dataSourceArray];
    newDataSourceArray.forEach((tableArr, index) => {
      tableArr.forEach((item: any) => {
        if (item.id === curId) {
          item.docs = JSON.stringify(files);
        }
      });
    });
    setDataSourceArray(newDataSourceArray);
    // 更新 refreshKey 强制刷新表格
    setRefreshKey((prevKey) => prevKey + 1);
  };

  return (
    <>
      <BlockContainer title="指标明细">
        {idicesList.length &&
          idicesList.map((tableData: any, index: number) => (
            <BlockContainer title={tableData.lab}>
              <ProForm<{ table: Any[] }>
                formRef={formRef}
                initialValues={{ table: [] }}
                submitter={false}
                onFinish={handleSave}
              >
                <EditableProTable<Any>
                  rowKey="id"
                  name="table"
                  key={refreshKey}
                  editableFormRef={tableRefArray[index]}
                  loading={loading}
                  request={async () => ({
                    data: dataSourceArray[index],
                    total: dataSourceArray[index]?.length,
                    success: true,
                  })}
                  scroll={{ x: 960 }}
                  recordCreatorProps={false}
                  columns={columns}
                  editable={{
                    type: 'multiple',
                    editableKeys: editableKeys[index],
                  }}
                />
                <div className="total-num">
                  <div className="num-box">合计: </div>
                  <div className="num-box">
                    指标名称总分：{sourceTotalNumArray[index]}
                  </div>
                  <div className="num-box">
                    自评得分：{sampleNumArray[index]}
                  </div>
                </div>
              </ProForm>
            </BlockContainer>
          ))}
        <div className="total-num total-num-1">
          <div className="num-box">自评总得分: </div>
          <div className="num-box">{sampleNum}</div>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setOpenDetail(true);
            }}
          >
            查看等级划分
          </Button>
        </div>
      </BlockContainer>
      <Modal
        width="60%"
        title="支撑材料"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <UploadList
          tipsWord={tipsWord}
          dialogTitletext={dialogTitletext}
          loading={loading}
          onRef={formRef2}
          mode={mode}
          docs={docs}
          onUpload={handleFileUpload} // 传递回调函数
        />
      </Modal>
      {/* 录入查找样本编号 */}
      <Modal
        title="等级划分"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={450}
      >
        <div className="p-4">
          <div>一级：≥60分，且＜70分；</div>
          <div>二级乙等：≥70分，且＜80分；</div>
          <div>二级甲等：≥80分，且＜85分；</div>
          <div>三级乙等：≥85分，且＜95分；</div>
          <div>三级甲等：≥95分。</div>
        </div>
      </Modal>
    </>
  );
};

export default ReceiveForm;
