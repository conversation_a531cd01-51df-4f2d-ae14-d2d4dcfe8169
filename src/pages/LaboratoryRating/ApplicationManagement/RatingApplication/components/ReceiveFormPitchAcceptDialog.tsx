import { useRef, useState } from 'react';
import { Form, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import {
  ProForm,
  ProFormDatePicker,
} from '@ant-design/pro-components';
import CheckboxWithOther from '@/components/CheckboxWithOther';
import dayjs from 'dayjs';

const FormInfoInit = {};

type TRoleDetailProps = {
  open: boolean;
  onOk: (v: any) => void;
  onClose: () => void;
};

const ReceiveFormPitchAcceptDialog: React.FC<TRoleDetailProps> = ({
  open,
  onClose,
  onOk,
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    onClose();
    formRef.current?.resetFields();
  };

  const handleSave = async (values: any) => {
    onOk(values);
  };

  return (
    <Modal
      title="批量接收"
      width="400px"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        onFinish={handleSave}
        layout="horizontal"
        className="pt-4"
      >
        <ProFormDatePicker
          label="接收日期"
          name="receiptDate"
          initialValue={dayjs()}
          rules={[{ required: true, message: '请选择接收日期' }]}
          placeholder="请选择接收日期"
        />
        <Form.Item
          name="sampleStatus"
          label="样品状态"
          rules={[{ required: true, message: '请选择样品状态' }]}
        >
          <CheckboxWithOther></CheckboxWithOther>
        </Form.Item>
      </ProForm>
    </Modal>
  );
};

export default ReceiveFormPitchAcceptDialog;
