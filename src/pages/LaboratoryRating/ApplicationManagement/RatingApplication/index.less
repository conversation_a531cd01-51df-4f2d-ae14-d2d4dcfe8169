.link-btn {
  cursor: pointer;
  color: #409EFF;
}

.img-class{
  display: block;
  width: 100%;
}

.txt-1 {
  margin-bottom: 10px;
}


.d-flex {
  display: flex;
}

.mr-2 {
  margin-right: 10px;
}
.ml-2{
  margin-left: 20px;
}

.top-txt-wraper {
  margin-bottom: 10px;
}

.form-wrpaer-1，
.form-wrpaer-2  {
  position: relative;
}
.tipsText {
  position: absolute;
  top: 32px;
  right: 0;
  color: #409EFF;

}

.btn-1 {
  position: absolute;
  top: 0px;
  left: 290px;
}


.form-wrpaer-3 {
  position: relative;
}

.tips-1 {
  position: absolute;
  top: 100px;
  left: 65px; 
  color: rgba(0, 0, 0, 0.45);
}

.upload-icon {
  color: #44c76d;
  font-size: 20px;
}


.upload-icon-1 {
  color: #409EFF;
  font-size: 20px;
}

.total-num {
  width: 100%;
  display: flex;
  justify-content: start;
  margin-bottom: 10px; 
}

.num-box {
  margin-right: 30px;
}

.total-num-1 {
  justify-content: end;
 margin-top: 10px; 
}

.btn-waraper{
  display: flex;
  justify-content: center
}


.bottom-btn-wraper {
    position: fixed;
    bottom: 0;
    width: 100%;
}




.form-wrpaer-2-readonly {
  .ant-form-item-control{
    padding-left: 18px;
    position: relative;
      &::before { 
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        left: 0;
        top: 8px;
        border: 1px solid #409EFF;
        border-radius: 3px;
        background-color: #409EFF;
      }

      &::after {
          content: "";
          position: absolute;
          left: 5px;
          top: 9px;
          width: 6px;
          height: 12px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
      }

  }

}

.table-class-wraper .ant-table-content {
  .ant-btn {
    padding: 0 !important;
  }
}