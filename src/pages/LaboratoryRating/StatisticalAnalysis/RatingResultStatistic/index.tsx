/*
 * @Date: 2024-07-30 14:27:45
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 11:26:43
 * @FilePath: \xr-qc-jk-web\src\pages\LaboratoryRating\StatisticalAnalysis\RatingResultStatistic\index.tsx
 * @Description: 评定结果统计
 */
import { useCallback, useRef, useState } from 'react';
import { message } from 'antd';
import { rateResultStatisticApi } from '@/api/LaboratoryRating/api';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

const RatingResultStatistic: React.FC = () => {
  const [rowArr, setRowArr] = useState<any[]>([]);
  // 查询条件
  const [searchParams, setSearchParams] = useState<any>();

  const actionRef = useRef<ActionType>();

  const getColumns = useCallback((): ProColumns[] => {
    return [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 48,
        title: '序号',
      },
      {
        title: '区域',
        dataIndex: 'region',
        valueType: 'cascader',
        onCell: (record, rowIndex) => {
          return {
            rowSpan: rowArr.length ? rowArr[rowIndex!].region : 1,
            colSpan: 1,
          };
        },
        request: async () => {
          const { code, data, msg } = await getAllCityAreaList({});
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return [];
          }
          // 转换
          const _finalDataList = convertToCascading(data);
          return _finalDataList;
        },
      },
      {
        title: '实验室数量',
        dataIndex: 'labCount',
        hideInSearch: true,
        onCell: (record, rowIndex) => {
          return {
            rowSpan: rowArr.length ? rowArr[rowIndex!].labCount : 1,
            colSpan: 1,
          };
        },
      },
      {
        title: '评级年份',
        dataIndex: 'gradeYear',
        valueType: 'select',
        fieldProps: {
          options: yearListOnTable,
        },
        onCell: (record, rowIndex) => {
          return {
            rowSpan: rowArr.length ? rowArr[rowIndex!].gradeYear : 1,
            colSpan: 1,
          };
        },
      },
      {
        title: '评定等级',
        dataIndex: 'grade',
        hideInSearch: true,
      },
      {
        title: '该等级实验室数量',
        dataIndex: 'gradeCount',
        hideInSearch: true,
      },
      {
        title: '该等级实验室占比',
        dataIndex: 'gradePercent',
        hideInSearch: true,
      },
    ];
  }, [rowArr]);

  const spanMethod = (data: any[]) => {
    const baseKey = ['region', 'labCount', 'gradeYear'];
    const _rowArr = data.map((_item) =>
      baseKey.reduce((pre, current) => {
        pre[current] = 1;
        return pre;
      }, {} as Record<string, number>)
    );
    data.forEach((_item, _index) => {
      for (let _iIndex = _index + 1; _iIndex < data.length; _iIndex++) {
        const _i = data[_iIndex];
        baseKey.forEach((key) => {
          if (_rowArr[_index][key] !== 0 && _i[key] === _item[key]) {
            _rowArr[_index][key] = _rowArr[_index][key] + 1;
            _rowArr[_iIndex][key] = 0;
          }
        });
      }
    });
    setRowArr(_rowArr);
  };

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            cityId: '',
            areaId: '',
            gradeYear: params.gradeYear,
          };
          if (params.region && params.region.length) {
            _params.cityId = params.region[0];
            _params.areaId = params.region[1];
          }
          setSearchParams(_params);
          const { code, data, msg } = await rateResultStatisticApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          spanMethod(data);

          return {
            data: data ?? [],
            total: 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={false}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/gradeScore/stat/export"
            params={searchParams}
            method='get'
          >
            导出
          </DownloadButton>,
        ]}
      />
    </PageContainer>
  );
};

export default RatingResultStatistic;
