import React, { useEffect } from 'react';

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Popover } from 'antd';
import { getDict } from '@/api/dict';
import {
  deleteReviewApplication,
  getRageResultStatistic,
  getReviewApplicationList,
  ratingIndexListApi,
  TRatingIndexDetail,
  updateRatingIndexStatusApi,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useYearStore } from '@/store';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';
import DownloadButton from '@/components/DownloadButton';

type TRatingResultsStatisticsProps = {};

const RatingResultsStatistics: React.FC<TRatingResultsStatisticsProps> = () => {
  const [pageSize, setPageSize] = useState(10);
  const { yearList } = useYearStore();

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [curSelectRow, setCurSelectRow] = useState<TRatingIndexDetail>();

  const [openEdit, setOpenEdit] = useState<boolean>(false);

  // 实验室类型列表
  const [labList, setLabList] = useState<Record<string, any>[]>([]);
  const [searchParams, setSearchParams] = useState<Record<string, any>>({});

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '申请年份',
      dataIndex: 'year',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      render: (_, record) => <span>{record?.year}</span>,
    },
    {
      title: '所属区域',
      dataIndex: 'cityName',
      hideInSearch: true,
    },
    {
      title: '评审申请数量',
      dataIndex: 'applicationNum',
      hideInSearch: true,
    },
    {
      title: '拒绝评级申请数量',
      dataIndex: 'adjustNum',
      hideInSearch: true,
    },
    {
      title: '同意参与评级数量',
      dataIndex: 'agreeNum',
      hideInSearch: true,
    },
    {
      title: '待评定数量',
      dataIndex: 'awaitingNum',
      hideInSearch: true,
    },

    {
      title: '申请等级',
      dataIndex: 'applyLevel',
      hideInSearch: true,
    },

    {
      title: '已评定数量',
      dataIndex: 'assessedNum',
      hideInSearch: true,
      render: (_, record) => {
        return record?.assessedNum !== 0 ? (
          <Popover
            trigger="click"
            placement="bottomRight"
            title={null}
            content={
              <div className="w-[180px]">
                <div className="flex flex-row text-center bg-slate-100 h-[32px] items-center">
                  <div className="flex-1">等级</div>
                  <div className="flex-1">数量</div>
                </div>
                {record?.levels?.map(
                  (item: Record<string, any>, idx: number) => (
                    <div
                      className="flex flex-row text-center h-[32px] items-center"
                      key={idx}
                    >
                      <div className="flex-1">{item.level}</div>
                      <div className="flex-1">{item.num}</div>
                    </div>
                  )
                )}
              </div>
            }
          >
            <Button type="link">{record?.assessedNum}</Button>
          </Popover>
        ) : (
          <span>{record?.assessedNum}</span>
        );
      },
    },
  ];

  const closeEdit = () => {
    setDetailId('');
    setEditOpen(false);
    tableReload();
  };

  /**
   * 获取实验室类型列表数据
   */
  const queryLibList = async () => {
    const { code, data, msg } = await getDict('dept_type');
    if (code !== codeDefinition.QUERY_SUCCESS) {
      message.error(msg);
      return;
    }
    setLabList(data);
  };

  /**
   * 删除评审申请
   */
  const queryDeleteReviewApplication = async (id: string) => {
    try {
      const { code, msg } = await deleteReviewApplication({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  useEffect(() => {
    queryLibList();
  }, []);

  return (
    <>
      <PageContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            const _param = {
              ...params,
              pageNum: params.current!,
              pageSize: params.pageSize!,
            };
            delete _param.current;
            const { code, data, msg } = await getRageResultStatistic(_param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            setSearchParams(_param)
            return {
              data: data.rows ?? [],
              total: data.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [          
            <DownloadButton
              url="/grade/lab/assess/exportAna"
              params={searchParams}
              method="post"
            >
              导出
            </DownloadButton> ,]}
        />
      </PageContainer>
    </>
  );
};

export default RatingResultsStatistics;
