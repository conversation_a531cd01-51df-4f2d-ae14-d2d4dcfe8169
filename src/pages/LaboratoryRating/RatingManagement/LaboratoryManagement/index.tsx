/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测计划主入口
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Tag } from 'antd';
import { getDict } from '@/api/dict';
import {
  getCountStatistics,
  getLibManagementCount,
  getPendingReviewList,
  getReviewedList,
} from '@/api/LaboratoryRating/api';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

type TLaboratoryManagementProps = {};

const LaboratoryManagement: React.FC<TLaboratoryManagementProps> = () => {
  const actionRef = useRef<ActionType>();
  const { yearList } = useYearStore();
  const [pageSize, setPageSize] = useState<number>(10);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 不同状态计划数量
  const [countStatistics, setCountStatistics] = useState<Record<string, any>>();
  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('1');

  // 实验室类型列表
  const [labList, setLabList] = useState<Record<string, any>[]>([]);

  // 操作类型: edit | view
  const [operationType, setOperationType] = useState<'edit' | 'view'>('edit');

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '申请年份',
      dataIndex: 'year',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      render: (_, record) => <span>{record?.year}</span>,
    },
    {
      title: '实验室名称',
      dataIndex: 'labName',
    },
    {
      title: '实验室类型',
      dataIndex: 'labType',
      valueType: 'select',
      fieldProps: {
        options: labList,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => (
        <span>
          {labList?.find((_i) => _i?.dictValue === record?.labType)?.dictLabel}
        </span>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'person',
      hideInSearch: true,
    },
    {
      title: '所属区域',
      dataIndex: 'cityArea',
      hideInSearch: true,
      render: (_, record) => (
        <span>
          {record?.cityName}/{record?.areaName}
        </span>
      ),
    },
    {
      title: '详细地址',
      dataIndex: 'address',
      hideInSearch: true,
    },
    {
      title: '审核结果',
      dataIndex: 'status',
      hideInSearch: true,
      hideInTable: curStatus === '1',
      render: (_, record) => (
        <span>{record?.status === '2' ? '同意参与评级' : '拒绝评级申请'}</span>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) =>
        curStatus === '1'
          ? [
              <Button
                type="link"
                size="small"
                key="edit"
                onClick={() => {
                  setOperationType('edit');
                  setDetailId(record.id);
                  setOpenEdit(true);
                }}
              >
                资格审核
              </Button>,
            ]
          : [
              <Button
                type="link"
                size="small"
                key="edit"
                onClick={() => {
                  setOperationType('view');
                  setDetailId(record.id);
                  setOpenEdit(true);
                }}
              >
                详情
              </Button>,
            ],
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * 获取不同状态计划的数量
   */
  const queryLibManagementCount = async (params?: Record<string, any>) => {
    try {
      const { code, data, msg } = await getLibManagementCount(params || {});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCountStatistics(data);
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * 获取实验室类型列表数据
   */
  const queryLibList = async () => {
    const { code, data, msg } = await getDict('dept_type');
    if (code !== codeDefinition.QUERY_SUCCESS) {
      message.error(msg);
      return;
    }
    setLabList(data);
  };

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    queryLibList();
    queryLibManagementCount();
  }, []);

  return (
    <>
      <PageContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          toolbar={{
            menu: {
              type: 'tab',
              activeKey: curStatus,
              items: [
                {
                  key: '1',
                  label: (
                    <span>待审核({countStatistics?.awaitingAudit || 0})</span>
                  ),
                },
                {
                  key: '2',
                  label: <span>已审核({countStatistics?.audited || 0})</span>,
                },
              ],
              onChange: (key) => {
                setCurStatus(key as any);
              },
            },
          }}
          request={async (params, sort, filter) => {
            const param: any = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            if (curStatus !== null) {
              param['status'] = curStatus;
            }

            // 如果有日期区间查询
            if (params?.updateTime?.length) {
              param['updateStartTime'] = params?.updateTime[0];
              param['updateEndTime'] = params?.updateTime[1];
            }

            delete param.updateTime;

            await queryLibManagementCount({ ...params });

            const { code, data, msg } =
              curStatus === '1'
                ? await getPendingReviewList(param)
                : await getReviewedList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }

            return {
              data: data?.rows ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle="监测计划"
          toolBarRender={() => []}
        />
      </PageContainer>
      {/* 编辑&详情 */}
      <Drawer
        width="60%"
        title="资格审核"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={tableReload}
          operationType={operationType}
          labList={labList}
        />
      </Drawer>
    </>
  );
};
export default LaboratoryManagement;
