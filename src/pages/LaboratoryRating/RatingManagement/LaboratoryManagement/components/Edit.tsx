/* eslint-disable array-callback-return */

/**
 *  检测中状态的填报页面
 */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData } from '@/api/file';
import {
  getRatingTableList,
  getReviewDetaiils,
  handleOperationReview,
} from '@/api/LaboratoryRating/api';
import { ossObjectApi } from '@/api/oss';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { convertToCascading } from '@/utils';
import {
  ProForm,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: any;
  operationType: 'edit' | 'view';
  labList: Record<string, any>[];
};

const layoutProps = {
  colProps: { span: 6 },
  labelCol: { span: 7 },
};

// 创建Context
export const TaskContext = createContext<any>({});

const Edit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  operationType,
  labList,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  // 评审详情信息
  const [reviewDetails, setReviewDetails] = useState<Record<string, any>>();

  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 是否刷新
  const isRefresh = useRef(false);

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  /**
   * 审核
   */
  const postOperationReview = async (status: number) => {
    setLoading(true);
    try {
      // 表单检验
      await formRef.current.validateFields();

      const _formResult = formRef?.current?.getFieldsValue();

      const _params = {
        id: detailId,
        status,
        ..._formResult,
      };

      const { code, msg } = await handleOperationReview(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取详情
   */
  const queryReviewDetails = async () => {
    if (!detailId) return;

    try {
      const { code, data, msg } = await getReviewDetaiils({ id: detailId });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setReviewDetails(data);

      formRef?.current?.setFieldsValue({
        year: data?.year,
        labName: data?.labName,
        labType: labList?.find((_i) => _i?.dictValue === data?.labType)
          ?.dictLabel,
        cityArea: data?.cityName + '/' + data?.areaName,
        person: data?.person,
        phone: data?.phone,
        address: data?.reason,
        reason: data?.reason,
        auditName: data?.auditName,
        auditTime: data?.auditTime,
        remark: data?.remark,
        tableName: data?.tableName,
      });

      // 通过其他接口获取附件列表
      const _id = data?.id;
      if (_id && _id !== 'null') {
        const { code, data, msg } = await fileGroupApi({ businessId: _id });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.url,
          });
        });
        formRef?.current?.setFieldValue('fileWrapper', _result);
      }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('reports', _res);
      }
    } catch (err) {
      console.error('Error fetching sample details:', err);
      throw new Error(`Error: ${err}`);
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      queryReviewDetails(); // 获取详情
    }
  }, [detailId]);

  useEffect(() => {
    isRefresh.current = true;
    queryCityAreaList();
  }, []);

  return (
    <div className=" h-full w-full">
      <div className="w-full h-full flex flex-col flex-nowrap">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            onFinish={() => {}}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={false}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="申请信息">
              <ProFormText
                readonly
                name="year"
                label="申请年份"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="labName"
                label="实验室名称"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="labType"
                label="实验室类型"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="cityArea"
                label="所属区域"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="person"
                label="负责人"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="phone"
                label="负责人联系电话"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="address"
                label="详细地址"
                colProps={{ span: 12 }}
              />
              <ProFormText
                readonly
                name="reason"
                label="申请理由描述"
                colProps={{ span: 24 }}
              />
              <ProFormUploadButton
                readonly
                name="fileWrapper"
                label="计划附件"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  name: 'file',
                  listType: 'picture-card',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      const d = await getFileData(file.response.data.ossId);
                      if (type === 'Image') {
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        if (file.response.data.url) {
                          setFileUrl(file.response.data.url);
                          setOpenPreview(true);
                        } else {
                          downloadFile(
                            file.response.data.ossId,
                            file.response.data.fileName
                          );
                        }
                      }
                    } else if (file?.name && file?.uid && file?.url) {
                      setFileUrl(file?.url);
                      setOpenPreview(true);
                    }
                  },
                  disabled: true,
                }}
                action={''}
                wrapperCol={{
                  span: 24,
                }}
              />
            </EProFormGroup>
            <EProFormGroup title="审核信息">
              <ProFormText
                readonly={operationType === 'view'}
                name="auditName"
                label="审核人"
                placeholder="请输入"
                fieldProps={{}}
                rules={
                  operationType !== 'view'
                    ? [{ required: true, message: '这是必填项' }]
                    : []
                }
                {...layoutProps}
                labelCol={{ span: 8 }}
              />

              <ProFormDatePicker
                readonly={operationType === 'view'}
                name="auditTime"
                label="审核时间"
                placeholder="请输入"
                rules={
                  operationType !== 'view'
                    ? [{ required: true, message: '这是必填项' }]
                    : []
                }
                style={{ width: '100%' }}
                {...layoutProps}
                labelCol={{ span: 8 }}
              />
              <ProFormSelect
                readonly={operationType === 'view'}
                name={operationType === 'view' ? 'tableName' : 'tableId'}
                label="使用评审表"
                colProps={{ span: 8 }}
                rules={
                  operationType !== 'view'
                    ? [{ required: true, message: '请选择评审表' }]
                    : []
                }
                request={async () => {
                  const { code, data, msg } = await getRatingTableList({});
                  if (code === codeDefinition.QUERY_SUCCESS) {
                    const _options =
                      data && data.length
                        ? data.map((item: any) => ({
                            label: item.tableName,
                            value: item.tableId,
                          }))
                        : [];
                    return _options;
                  } else {
                    message.error(msg);
                    return [];
                  }
                }}
              />
              <ProFormTextArea
                readonly={operationType === 'view'}
                name="remark"
                label="备注"
                placeholder="请输入"
                colProps={{ span: 24 }}
                labelCol={{ offset: 1 }}
              />
            </EProFormGroup>
          </ProForm>
        </div>
        {operationType === 'edit' ? (
          <div className="h-12 bg-white flex justify-center items-center">
            <Space>
              <Button
                danger
                loading={loading}
                type="primary"
                onClick={() => postOperationReview(3)}
              >
                拒绝评级申请
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={() => postOperationReview(2)}
              >
                同意参与评级
              </Button>
            </Space>
          </div>
        ) : null}
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default Edit;
