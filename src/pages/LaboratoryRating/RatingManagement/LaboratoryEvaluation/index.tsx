/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getDict } from '@/api/dict';
import {
  getEvaluationCount,
  getLibEvaluationedList,
  getLibEvaluationList,
} from '@/api/LaboratoryRating/api';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

export type TDataSource = {
  id: number;
  gradeYear: number;
  labType: string;
  idxScore: number;
  adjustScore: number;
  resultScore: number;
  grade: string;
  status: number;
  gradeTime: string;
  createId: number;
  createName: string;
  createTime: string;
};

const LaboratoryEvaluation: React.FC = () => {
  const { yearList } = useYearStore();
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  // 操作类型
  const [operationType, setOperationType] = useState<'edit' | 'view'>('edit');

  // 是否打开评定 Drawer
  const [isOpenEditDrawer, setIsOpenEditDrawer] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();

  // 当前选择的待评定数据ID
  const [curSelectedEvalRowId, setCurSelectedEvalRowId] = useState<string>();

  // 待评定、已评定数量
  const [evaluationCount, setEvaluationCount] = useState<Record<string, any>>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '申请年份',
      dataIndex: 'year',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '实验室名称',
      dataIndex: 'labName',
    },
    {
      title: '实验室类型',
      dataIndex: 'labType',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('laboratory_type');
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '负责人',
      dataIndex: 'person',
      hideInSearch: true,
    },
    {
      title: '所属区域',
      dataIndex: 'cityArea',
      hideInSearch: true,
      render: (_, record) => (
        <span>
          {record?.cityName}/{record?.areaName}
        </span>
      ),
    },
    {
      title: '详细地址',
      dataIndex: 'address',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="score"
          onClick={() => {
            setOperationType(activeKey === '0' ? 'edit' : 'view');
            setCurSelectedEvalRowId(record?.id);
            setIsOpenEditDrawer(true);
          }}
        >
          {activeKey === '0' ? '评定' : '详情'}
        </Button>,
      ],
    },
  ];

  /**
   * 获取待评定、已评定数量
   */
  const queryEvaluationCount = async () => {
    try {
      const { code, data, msg } = await getEvaluationCount({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setEvaluationCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  useEffect(() => {
    queryEvaluationCount();
  }, []);

  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: (
                  <span>待评定({evaluationCount?.awaitingAudit || 0})</span>
                ),
              },
              {
                key: '1',
                label: <span>已评定({evaluationCount?.audited || 0})</span>,
              },
            ],
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
            status: activeKey,
          };

          const { code, data, msg } =
            activeKey === '0'
              ? await getLibEvaluationList(_params)
              : await getLibEvaluationedList(_params);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return [];
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => []}
      />

      {/* 评定 */}
      <Drawer
        width="70%"
        title={operationType === 'edit' ? '评定' : '详情'}
        onClose={() => {
          setIsOpenEditDrawer(false);
          tableReload();
        }}
        open={isOpenEditDrawer}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          operationType={operationType}
          tableReload={tableReload}
          close={() => setIsOpenEditDrawer(false)}
          curSelectedEvalRowId={curSelectedEvalRowId!}
          queryEvaluationCount={queryEvaluationCount}
        />
      </Drawer>
    </PageContainer>
  );
};

export default LaboratoryEvaluation;
