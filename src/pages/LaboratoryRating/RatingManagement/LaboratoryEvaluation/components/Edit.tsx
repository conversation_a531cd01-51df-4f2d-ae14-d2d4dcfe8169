/* eslint-disable array-callback-return */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, Collapse, message, Space, Table } from 'antd';
import {
  dynamicCalcAssessResult,
  getLibEvaluationDetails,
  postLibEvaluationResult,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import {
  ActionType,
  EditableProTable,
  ProForm,
  ProFormDatePicker,
  ProFormText,
} from '@ant-design/pro-components';
import { useDebounceFn } from 'ahooks';
import dayjs from 'dayjs';
import EProFormGroup from '@/components/EProFromGroup';

type TEditProps = {
  close: () => void;
  tableReload: any;
  operationType: 'edit' | 'view';
  curSelectedEvalRowId: string;
  queryEvaluationCount: any;
};

// 创建Context
export const TaskContext = createContext<any>({});

const Edit: React.FC<TEditProps> = ({
  close,
  tableReload,
  operationType,
  curSelectedEvalRowId,
  queryEvaluationCount,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  const { userInfo } = useInfoStore();

  const actionRef = useRef<ActionType>();

  // 详情数据
  const [dataSource, setDataSource] = useState<Record<string, any>>();
  // 评定信息原始数据
  const [evaluateInfoDataList, setEvaluateInfoDataList] = useState<
    Record<string, any>[]
  >([]);

  // 可编辑表格行ids
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // 所有折叠面板ids，默认全部打开
  const [defaultExapPanelIds, setDefaultExapPanelIds] = useState<string[]>([]);

  // 可编辑表格key
  const editableProTableKey = useRef<string>(Math.random().toString());

  /**
   *  获取评定详情
   */
  const queryLibEvaluationDetails = async (id: string) => {
    try {
      const { code, data, msg } = await getLibEvaluationDetails({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDataSource(data);
      setEvaluateInfoDataList(data?.details);

      // 设置可编辑行
      const _ids = extractInnerIds(data?.details);
      setEditableRowKeys(_ids);

      formRef?.current?.setFieldsValue(data);
      if (operationType === 'edit') {
        formRef?.current?.setFieldValue('assessName', userInfo?.user?.userName);
        formRef?.current?.setFieldValue('assessDate', dayjs());
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 构建嵌套折叠面板DOM
   */
  const renderEvaluationInfoList = () => {
    if (!evaluateInfoDataList?.length) return;
    const collapseItems = buildCollapseItems(evaluateInfoDataList);
    return <Collapse items={collapseItems} className="w-full" />;
  };

  const extractInnerIds = (data: any[]): string[] => {
    const result: string[] = [];

    const traverse = (items: any[]): void => {
      items.forEach((item) => {
        if (item.details && item.details.length > 0) {
          // 如果有子项，继续递归
          traverse(item.details);
        } else {
          // 没有子项时，取当前项的 id
          result.push(item.id);
        }
      });
    };

    traverse(data);
    return result;
  };

  /**
   * 递归构建折叠面板的函数
   * @param data
   * @returns
   */
  const buildCollapseItems = (data: Record<string, any>[]) => {
    return data.map((item) => {
      if (item.type === '3') {
        // 最内层为表格
        return {
          key: item.id,
          label: item.lab,
          children: (
            <EditableProTable
              key={editableProTableKey.current}
              actionRef={actionRef}
              scroll={{ x: 0 }}
              columns={[
                {
                  dataIndex: 'index',
                  valueType: 'indexBorder',
                  width: '5%',
                  title: '序号',
                },
                {
                  dataIndex: 'lab',
                  title: '评审要点',
                  editable: false,
                  width: '35%',
                  render: (_, record) => {
                    return record?.lab
                      ?.split('；')
                      ?.map((_i: string) => <div>{_i}</div>);
                  },
                },
                {
                  dataIndex: 'rule',
                  title: '评分规则',
                  editable: false,
                  width: '35%',
                  render: (_, record) => {
                    return record?.rule
                      ?.split('；')
                      ?.map((_i: string) => <div>{_i}</div>);
                  },
                },
                {
                  dataIndex: 'score',
                  title: '分值',
                  editable: false,
                  width: '10%',
                },
                {
                  dataIndex: 'result',
                  title: '得分',
                  width: '15%',
                  valueType: 'digit',
                  fieldProps: {
                    precision: 2,
                    style: { width: '100%' },
                  },
                  editable: (operationType === 'edit') as any,
                },
              ]}
              rowKey="id"
              value={item?.details}
              onChange={setDataSource}
              recordCreatorProps={false}
              toolBarRender={() => []}
              editable={{
                type: 'multiple',
                editableKeys,
                actionRender: (row, config, defaultDoms) => {
                  return [];
                },
                onValuesChange: (record, recordList) => {
                  // 判断输入的分数是否大于设定的分
                  if (record?.result > parseFloat(record?.score)) {
                    const targetItem = recordList.find(
                      (_item) => _item?.assessId === record?.assessId
                    );
                    if (targetItem) {
                      targetItem.result = record?.score;
                    }
                    setTimeout(() => {
                      editableProTableKey.current = Math.random().toString();
                    }, 0);
                  }

                  const _result = updateResult(
                    evaluateInfoDataList,
                    recordList
                  );
                  setEvaluateInfoDataList(_result);
                },
                // onChange: setEditableRowKeys,
              }}
            />
          ),
        };
      }

      return {
        key: item.id,
        label: item.lab,
        children:
          item.details && item.details.length > 0 ? (
            <Collapse
              defaultActiveKey={item.details[0]?.id}
              items={buildCollapseItems(item.details)}
            />
          ) : null,
      };
    });
  };

  /**
   *  保存&提交评定结果
   *  @param status 1: 保存 2: 提交
   */
  const handlePostLibEvaluationResult = async (status: '1' | '2') => {
    try {
      setLoading(true);

      // 如果是提交，校验所有参数必填
      if (status === '2') {
        if (!validateNestedItems(evaluateInfoDataList)) {
          message.error('请填写所有评分项');
          return;
        }
      }

      const _dataSource = formRef?.current?.getFieldsValue();

      const _params: Record<string, any> = {
        id: curSelectedEvalRowId,
        ..._dataSource,
        details: evaluateInfoDataList,
        status,
      };

      const { code, msg } = await postLibEvaluationResult(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      queryEvaluationCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 动态计算评定结果
   */
  const handleDynamicCalcAssessResult = async () => {
    if (!formRef.current?.getFieldsValue()) return;

    try {
      const _params: Record<string, any> = {
        details: evaluateInfoDataList,
      };

      const { code, data, msg } = await dynamicCalcAssessResult(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      formRef?.current?.setFieldValue('score', data?.score);
      formRef?.current?.setFieldValue('level', data?.level);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 递归更新数据
   */
  function updateResult(
    targetData: Record<string, any>[],
    sourceData: Record<string, any>[]
  ): Record<string, any>[] {
    const sourceMap = new Map(sourceData.map((item) => [item.id, item.result])); // 创建一个 ID->result 的映射

    const updateRecursively = (
      data: Record<string, any>[]
    ): Record<string, any>[] =>
      data.map((item) => {
        // 如果当前项的 id 在 sourceMap 中，更新 result
        if (sourceMap.has(item.id)) {
          return { ...item, result: sourceMap.get(item.id) ?? null };
        }
        // 如果有子项，则递归更新
        if (item.details) {
          return { ...item, details: updateRecursively(item.details) };
        }
        return item;
      });

    return updateRecursively(targetData);
  }

  /**
   * 判断结果数据Item中 type === ‘4’ 的数据的result 字段是否为空
   */
  function validateNestedItems(data: Record<string, any>[]): boolean {
    for (const item of data) {
      // 检查 type === "4" 且 result 为空
      if (item.type === '4' && (item.result === null || item.result === '')) {
        return false;
      }
      // 如果存在子项，则递归检查
      if (item.details && !validateNestedItems(item.details)) {
        return false;
      }
    }
    return true;
  }

  useEffect(() => {
    if (curSelectedEvalRowId) {
      queryLibEvaluationDetails(curSelectedEvalRowId);
    }
  }, [curSelectedEvalRowId]);

  /**
   * 评定结果变化时，动态计算评定结果
   */
  useEffect(() => {
    if (evaluateInfoDataList) {
      handleDynamicCalcAssessResult();
    }
  }, [evaluateInfoDataList]);

  return (
    <div className=" h-full w-full">
      <div className="w-full h-full flex flex-col flex-nowrap">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            onFinish={() => {}}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={false}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              console.log('values:', values);
            }}
          >
            <EProFormGroup title="申请信息">
              <ProFormText
                readonly
                name="year"
                label="申请年份"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="labName"
                label="实验室名称"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="person"
                label="负责人"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="phone"
                label="负责人联系电话"
                colProps={{ span: 6 }}
              />
            </EProFormGroup>
            <EProFormGroup title="评定信息">
              {renderEvaluationInfoList()}
            </EProFormGroup>
            <EProFormGroup title="评定结果">
              <ProFormText
                readonly={operationType === 'view'}
                name="assessName"
                label="评定人"
                placeholder="请输入"
                fieldProps={{}}
                rules={[{ required: true, message: '这是必填项' }]}
                colProps={{ span: 6 }}
                labelCol={{ span: 8 }}
              />
              <ProFormDatePicker
                readonly={operationType === 'view'}
                name="assessDate"
                label="评定日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                colProps={{ span: 6 }}
                labelCol={{ span: 8 }}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
              <ProFormText
                readonly={operationType === 'view'}
                name="score"
                label="评定分数"
                placeholder=""
                fieldProps={{}}
                colProps={{ span: 6 }}
                labelCol={{ span: 8 }}
                disabled
              />
              <ProFormText
                readonly={operationType === 'view'}
                name="level"
                label="评定等级"
                placeholder=""
                fieldProps={{}}
                colProps={{ span: 6 }}
                labelCol={{ span: 8 }}
                disabled
              />
            </EProFormGroup>
          </ProForm>
        </div>
        {operationType === 'edit' ? (
          <div className="h-12 bg-white flex justify-center items-center">
            <Space>
              <Button type="default" onClick={close}>
                取消
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={() => handlePostLibEvaluationResult('1')}
              >
                保存
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={() => handlePostLibEvaluationResult('2')}
              >
                提交
              </Button>
            </Space>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default Edit;
