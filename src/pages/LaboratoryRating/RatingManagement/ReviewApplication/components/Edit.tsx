/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, Collapse, message, Space } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import {
  getLibInfoByUser,
  getReviewApplicationDetails,
  postNewReviewApplication,
} from '@/api/LaboratoryRating/api';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore, useTokenStore, useYearStore } from '@/store';
import { convertToCascading } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  EditableProTable,
  ProDescriptions,
  ProForm,
  ProFormCascader,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
  labList?: Record<string, any>[];
  editType: 'edit' | 'add' | 'view';
  queryLibReviewCount: () => void;
  curStatus: string;
};

const layoutProps = {
  colProps: { span: 8 },
};

// 创建Context
export const TaskContext = createContext<any>({});

const ReviewAppEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  labList,
  editType,
  queryLibReviewCount,
  curStatus,
}) => {
  const { token } = useTokenStore();
  const { userInfo } = useInfoStore();
  const { yearList } = useYearStore();

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  // 计划详情数据
  const [reviewApplicationDetails, setReviewApplicationDetails] =
    useState<Record<string, any>>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 实验室信息
  const [libInfoWithCurrentUser, setLibInfoWithCurrentUser] =
    useState<Record<string, any>>();

  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  // 可编辑表格key
  const editableProTableKey = useRef<string>(Math.random().toString());

  // 可编辑表格行ids
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // 评定信息原始数据
  const [evaluateInfoDataList, setEvaluateInfoDataList] = useState<
    Record<string, any>[]
  >([]);

  /**
   * 保存提交新增申请
   * @param params 0: 保存 1: 提交
   * @returns
   */
  const handleSaveEvent = async (params: '0' | '1') => {
    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();
      if (libInfoWithCurrentUser) {
        _formResult.labType = libInfoWithCurrentUser.labType;
        _formResult.labId = libInfoWithCurrentUser.labId;
      }

      // 处理县市区数据
      if (_formResult?.cityArea) {
        _formResult['cityId'] = ~~_formResult?.cityArea[0];
        _formResult['areaId'] = ~~_formResult?.cityArea[1];

        // 获取原始数据
        const _cityAreaOriginList = JSON.parse(
          sessionStorage.getItem('cityAreaOriginList')!
        );
        if (!_cityAreaOriginList) {
          message.error('市县区域信息数据错误');
          return;
        }
        _cityAreaOriginList.forEach((_item: any) => {
          if (_formResult['cityId'] === _item?.cityId) {
            _formResult['cityName'] = _item?.cityName;
          }
          if (_formResult['areaId'] === _item?.areaId) {
            _formResult['areaName'] = _item?.areaName;
          }
        });
        delete _formResult?.cityArea;
      }

      // 处理上传附件
      if (_formResult?.fileWrapper?.length) {
        if (_formResult?.fileWrapper[0]?.response?.code === 200) {
          _formResult['attachments'] = [
            _formResult?.fileWrapper[0]?.response?.data,
          ];
        }
        if (_formResult?.fileWrapper?.uid) {
          _formResult['planAttachmentList'] = _formResult?.fileWrapper?.uid;
        }
        delete _formResult?.fileWrapper;
      }

      const _params = {
        ..._formResult,
        status: params,
      };

      if (detailId) {
        _params['id'] = detailId;
      }

      // 处理 params 中的 file 字段
      // 如果有值，则循环将其中的 uid 复制一份，key为 ossId
      if (_params?.file) {
        _params.file.forEach((_item: Record<string, any>) => {
          _item.ossId = _item.uid;
        });
      }

      const { code, msg } = await postNewReviewApplication(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      queryLibReviewCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取详情
   */
  const queryReviewApplicationDetails = async () => {
    if (!detailId) return;

    try {
      const { code, data, msg } = await getReviewApplicationDetails({
        id: detailId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setReviewApplicationDetails(data);
      formRef?.current?.setFieldsValue(data);
      setEvaluateInfoDataList(data?.details);

      // 设置实验室类型
      formRef?.current?.setFieldValue(
        'labType',
        labList?.find((_i) => _i?.dictValue === data?.labType)?.dictLabel
      );

      // 设置所属区域
      formRef?.current?.setFieldValue('cityArea', [
        String(data?.cityId),
        String(data?.areaId),
      ]);

      // 通过其他接口获取附件列表
      const _id = data?.id;
      if (_id && _id !== 'null') {
        const { code, data, msg } = await fileGroupApi({ businessId: _id });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.url,
          });
        });
        formRef?.current?.setFieldValue('file', _result);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        if (file?.status !== 'removed') {
          formRef?.current?.setFieldValue('file', [
            {
              uid: file?.response?.data?.ossId,
              name: file?.response?.data?.fileName,
              url: file?.response?.data?.url,
            },
          ]);
          message.success(file.response.msg);
        }
      }
    }
  };

  /**
   * 根据当前登录用户获取实验室信息
   */
  const queryLibInfoByCurrentUser = async () => {
    try {
      const { code, data, msg } = await getLibInfoByUser({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setLibInfoWithCurrentUser(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 构建嵌套折叠面板DOM
   */
  const renderEvaluationInfoList = () => {
    if (!evaluateInfoDataList?.length) return;
    const collapseItems = buildCollapseItems(evaluateInfoDataList);
    return <Collapse items={collapseItems} className="w-full" />;
  };

  /**
   * 递归构建折叠面板的函数
   * @param data
   * @returns
   */
  const buildCollapseItems = (data: Record<string, any>[]) => {
    return data.map((item) => {
      if (item.type === '3') {
        // 最内层为表格
        return {
          key: item.id,
          label: item.lab,
          children: (
            <EditableProTable
              key={editableProTableKey.current}
              actionRef={actionRef}
              scroll={{ x: 0 }}
              columns={[
                {
                  dataIndex: 'index',
                  valueType: 'indexBorder',
                  width: '5%',
                  title: '序号',
                },
                {
                  dataIndex: 'lab',
                  title: '评审要点',
                  editable: false,
                  width: '35%',
                  render: (_, record: any) => {
                    return record?.lab
                      ?.split('；')
                      ?.map((_i: string) => <div>{_i}</div>);
                  },
                },
                {
                  dataIndex: 'rule',
                  title: '评分规则',
                  editable: false,
                  width: '35%',
                  render: (_, record) => {
                    return record?.rule
                      ?.split('；')
                      ?.map((_i: string) => <div>{_i}</div>);
                  },
                },
                {
                  dataIndex: 'score',
                  title: '分值',
                  editable: false,
                  width: '10%',
                },
                {
                  dataIndex: 'result',
                  title: '得分',
                  width: '15%',
                  valueType: 'digit',
                  fieldProps: {
                    precision: 2,
                    style: { width: '100%' },
                  },
                  editable: (editType === 'edit') as any,
                },
              ]}
              rowKey="id"
              value={item?.details}
              // onChange={setDataSource}
              recordCreatorProps={false}
              toolBarRender={() => []}
              editable={{
                type: 'multiple',
                editableKeys,
                actionRender: (row, config, defaultDoms) => {
                  return [];
                },
                onValuesChange: (record, recordList) => {
                  // 判断输入的分数是否大于设定的分
                  // if (record?.result > parseFloat(record?.score)) {
                  //   const targetItem = recordList.find(
                  //     (_item) => _item?.assessId === record?.assessId
                  //   );
                  //   if (targetItem) {
                  //     targetItem.result = record?.score;
                  //   }
                  //   setTimeout(() => {
                  //     editableProTableKey.current = Math.random().toString();
                  //   }, 0);
                  // }
                  // const _result = updateResult(
                  //   evaluateInfoDataList,
                  //   recordList
                  // );
                  // setEvaluateInfoDataList(_result);
                },
                // onChange: setEditableRowKeys,
              }}
            />
          ),
        };
      }

      return {
        key: item.id,
        label: item.lab,
        children:
          item.details && item.details.length > 0 ? (
            <Collapse
              defaultActiveKey={item.details[0]?.id}
              items={buildCollapseItems(item.details)}
            />
          ) : null,
      };
    });
  };

  useEffect(() => {
    if (detailId) {
      queryReviewApplicationDetails(); // 获取详情
    }
  }, [detailId]);

  useEffect(() => {
    if (formRef?.current && libInfoWithCurrentUser) {
      // 设置默认的实验室名称
      formRef?.current?.setFieldValue(
        'labName',
        libInfoWithCurrentUser?.labName
      );
      // 设置默认的实验室类型
      formRef?.current?.setFieldValue(
        'labType',
        labList?.find((_i) => _i?.dictValue === libInfoWithCurrentUser?.labType)
          ?.dictLabel
      );
    }
  }, [formRef.current, libInfoWithCurrentUser]);

  useEffect(() => {
    queryLibInfoByCurrentUser();
    queryCityAreaList();
  }, []);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
        <ProForm
          onFinish={() => {}}
          formRef={formRef}
          formKey="base-form-use-demo"
          layout="horizontal"
          grid={true}
          rowProps={{
            gutter: [64, 0],
            justify: 'space-between',
          }}
          submitter={false}
          //@ts-ignore
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <EProFormGroup title="申请信息">
            <ProFormSelect
              readonly={editType === 'view'}
              name="year"
              options={yearList}
              rules={[{ required: true, message: '请选择年份' }]}
              label="申请年份"
              {...layoutProps}
              labelCol={{ span: 8 }}
            />
            <ProFormSelect
              readonly={editType === 'view'}
              disabled
              name="labName"
              label="实验室名称"
              {...layoutProps}
              labelCol={{ span: 10 }}
            />
            <ProFormSelect
              readonly={editType === 'view'}
              disabled
              name="labType"
              label="实验室类型"
              {...layoutProps}
              labelCol={{ span: 10 }}
            />
            <ProFormCascader
              readonly={editType === 'view'}
              name="cityArea"
              fieldProps={{ options: cityAreaList }}
              rules={[{ required: true, message: '请选择所属区域' }]}
              label="所属区域"
              {...layoutProps}
              labelCol={{ span: 8 }}
            />
            <ProFormText
              readonly={editType === 'view'}
              name="person"
              label="负责人"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入' }]}
              {...layoutProps}
              labelCol={{ span: 10 }}
            />
            <ProFormText
              readonly={editType === 'view'}
              name="phone"
              label="负责人联系电话"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入' }]}
              {...layoutProps}
              labelCol={{ span: 10 }}
            />
            <ProFormText
              readonly={editType === 'view'}
              name="address"
              label="详细地址"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入' }]}
              {...layoutProps}
              labelCol={{ span: 8 }}
            />

            <ProFormText
              readonly={editType === 'view'}
              name="applyLevel"
              label="申请等级"
              placeholder="请输入"
              {...layoutProps}
              labelCol={{ span: 8 }}
            />





            <ProFormTextArea
              readonly={editType === 'view'}
              name="reason"
              colProps={{ span: 24 }}
              label="申请理由描述"
              rules={[{ required: true, message: '请输入' }]}
              labelCol={{ flex: 0.008 }}
            />
            <ProFormUploadButton
              name="file"
              label="证明材料"
              colProps={{ span: 24 }}
              labelCol={{ flex: 0.05 }}
              max={editType === 'view' ? 1 : undefined}
              fieldProps={{
                iconRender: (file) => {
                  return (
                    <img
                      src={getIconByName(file.name)}
                      className="!w-[40px] !h-[40px] m-auto mt-2"
                      alt="logo"
                    />
                  );
                },
                name: 'file',
                listType: 'picture-card',
                onChange: (info) => {
                  handleUploadFiles(info);
                },
                headers: {
                  Authorization: `Bearer ${token}`,
                },
                async onPreview(file: any) {
                  if (
                    file.status === 'done' &&
                    file.response &&
                    file.response.data &&
                    file.response.data.ossId
                  ) {
                    const type = getFileTypeByName(file.response.data.fileName);
                    const d = await getFileData(file.response.data.ossId);
                    if (type === 'Image') {
                      setIsShowFileData({
                        name: file.response.data.fileName,
                        url: d,
                        ossId: file.response.data.ossId,
                      });
                      setIsShowFileView(true);
                    } else {
                      if (file.response.data.url) {
                        setFileUrl(file.response.data.url);
                        setOpenPreview(true);
                      } else {
                        downloadFile(
                          file.response.data.ossId,
                          file.response.data.fileName
                        );
                      }
                    }
                  } else if (file?.name && file?.uid && file?.url) {
                    setFileUrl(file?.url);
                    setOpenPreview(true);
                  }
                },
                disabled: editType === 'view',
              }}
              action={uploadFiles}
              wrapperCol={{
                span: 24,
              }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </ProFormUploadButton>
          </EProFormGroup>
          {curStatus === '2' || curStatus === '3' ? (
            <>
              <EProFormGroup title="审核信息">
                <ProDescriptions column={4}>
                  <ProDescriptions.Item label="审核结论">
                    {reviewApplicationDetails?.status === '2'
                      ? '同意参与评定'
                      : reviewApplicationDetails?.status === '3'
                      ? '拒绝参与评定'
                      : '-'}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="审核人">
                    {reviewApplicationDetails?.auditName}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="审核时间">
                    {reviewApplicationDetails?.auditTime}
                  </ProDescriptions.Item>
                </ProDescriptions>
                <ProDescriptions column={1}>
                  <ProDescriptions.Item label="审核备注" span={1}>
                    {reviewApplicationDetails?.remark}
                  </ProDescriptions.Item>
                </ProDescriptions>
              </EProFormGroup>
              {reviewApplicationDetails?.details?.length ? (
                <>
                  <EProFormGroup title="评定信息">
                    {renderEvaluationInfoList()}
                  </EProFormGroup>
                  <EProFormGroup title="评定结果">
                    <ProDescriptions column={4}>
                      <ProDescriptions.Item label="评定人">
                        {reviewApplicationDetails?.assessName}
                      </ProDescriptions.Item>
                      <ProDescriptions.Item label="评定日期">
                        {reviewApplicationDetails?.assessDate}
                      </ProDescriptions.Item>
                      <ProDescriptions.Item label="评定分数">
                        {reviewApplicationDetails?.score}
                      </ProDescriptions.Item>
                      <ProDescriptions.Item label="评定等级">
                        {reviewApplicationDetails?.level}
                      </ProDescriptions.Item>
                    </ProDescriptions>
                  </EProFormGroup>
                </>
              ) : null}
            </>
          ) : null}
        </ProForm>
      </div>
      {editType === 'add' || editType === 'edit' ? (
        <div className="h-12 bg-white flex justify-center items-center">
          <Space>
            <Button type="default" loading={loading} onClick={() => close()}>
              取消
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => handleSaveEvent('0')}
            >
              保存
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => handleSaveEvent('1')}
            >
              提交
            </Button>
          </Space>
        </div>
      ) : null}
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default ReviewAppEdit;
