import React, { useEffect } from 'react';

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm } from 'antd';
import { getDict } from '@/api/dict';
import {
  deleteReviewApplication,
  getReviewApplicationList,
  getReviewCount,
  TRatingIndexDetail,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useYearStore } from '@/store';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

type TReviewApplicationProps = {};

const ReviewApplication: React.FC<TReviewApplicationProps> = () => {
  const [pageSize, setPageSize] = useState(10);
  const { yearList } = useYearStore();

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 当前选择的行数据
  const [curSelectRow, setCurSelectRow] = useState<TRatingIndexDetail>();

  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');

  const [openEdit, setOpenEdit] = useState<boolean>(false);

  // 实验室类型列表
  const [labList, setLabList] = useState<Record<string, any>[]>([]);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('0');
  // 不同状态数量
  const [countStatistics, setCountStatistics] = useState<Record<string, any>>();
  // 操作类型
  const [operationType, setOperationType] = useState<'add' | 'edit' | 'view'>(
    'add'
  );

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '申请年份',
      dataIndex: 'year',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      render: (_, record) => <span>{record?.year}</span>,
    },
    {
      title: '实验室名称',
      dataIndex: 'labName',
    },
    {
      title: '实验室类型',
      dataIndex: 'labType',
      valueType: 'select',
      fieldProps: {
        options: labList,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => (
        <span>
          {labList?.find((_i) => _i?.dictValue === record?.labType)?.dictLabel}
        </span>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'person',
      hideInSearch: true,
    },
    {
      title: '所属区域',
      dataIndex: 'cityArea',
      hideInSearch: true,
      render: (_, record) => (
        <span>
          {record?.cityName}/{record?.areaName}
        </span>
      ),
    },
    {
      title: '详细地址',
      dataIndex: 'address',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: curStatus === '0' ? 160 : 80,
      render: (text, record, _, action) =>
        curStatus === '0'
          ? [
              <Button
                key="view"
                type="link"
                size="small"
                onClick={() => {
                  setEditType('view');
                  setDetailId(record?.id);
                  setCurSelectRow(record);
                  setOperationType('view');
                  setEditOpen(true);
                }}
              >
                查看
              </Button>,
              <Button
                key="update"
                type="link"
                size="small"
                onClick={() => {
                  setEditType('edit');
                  setDetailId(record?.id);
                  setCurSelectRow(record);
                  setOperationType('edit');
                  setEditOpen(true);
                }}
              >
                编辑
              </Button>,
              <Popconfirm
                title="删除"
                description="确认删除该项?"
                onConfirm={() => queryDeleteReviewApplication(record?.id)}
                key="del"
              >
                <Button type="link" size="small" danger>
                  删除
                </Button>
              </Popconfirm>,
            ]
          : [
              <Button
                key="view"
                type="link"
                size="small"
                onClick={() => {
                  setEditType('view');
                  setDetailId(record?.id);
                  setCurSelectRow(record);
                  setOperationType('view');
                  setEditOpen(true);
                }}
              >
                查看
              </Button>,
            ],
    },
  ];

  const closeEdit = () => {
    setDetailId('');
    setEditOpen(false);
    tableReload();
  };

  /**
   * 获取不同状态评级申请的数量
   */
  const queryLibReviewCount = async (params?: Record<string, any>) => {
    try {
      const { code, data, msg } = await getReviewCount(params || {});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCountStatistics(data);
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * 获取实验室类型列表数据
   */
  const queryLibList = async () => {
    const { code, data, msg } = await getDict('dept_type');
    if (code !== codeDefinition.QUERY_SUCCESS) {
      message.error(msg);
      return;
    }
    setLabList(data);
  };

  /**
   * 删除评审申请
   */
  const queryDeleteReviewApplication = async (id: string) => {
    try {
      const { code, msg } = await deleteReviewApplication({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 生成Drawer标题
   */
  const generateDrawerTitle = () => {
    if (!detailId) {
      return '新增评审申请';
    }

    if (detailId && operationType === 'edit') {
      return '编辑评审申请';
    }

    return '查看评审申请';
  };

  useEffect(() => {
    tableReload();
  }, [curStatus]);

  useEffect(() => {
    queryLibList();
    queryLibReviewCount();
  }, []);

  return (
    <>
      <PageContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          toolbar={{
            menu: {
              type: 'tab',
              activeKey: curStatus,
              items: [
                {
                  key: '0',
                  label: <span>草稿({countStatistics?.draftNum || 0})</span>,
                },
                {
                  key: '1',
                  label: (
                    <span>待审核({countStatistics?.awaitingAudit || 0})</span>
                  ),
                },
                {
                  key: '2',
                  label: (
                    <span>同意参与评级({countStatistics?.agreeNum || 0})</span>
                  ),
                },
                {
                  key: '3',
                  label: (
                    <span>拒绝参与评级({countStatistics?.rejectNum || 0})</span>
                  ),
                },
              ],
              onChange: (key) => {
                setCurStatus(key as any);
              },
            },
          }}
          request={async (params, sort, filter) => {
            const _param = {
              ...params,
              pageNum: params.current!,
              pageSize: params.pageSize!,
              status: curStatus,
            };
            delete _param.current;
            const { code, data, msg } = await getReviewApplicationList(_param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }

            return {
              data: data.rows ?? [],
              total: data.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              type="primary"
              onClick={() => {
                setEditType('add');
                setCurSelectRow(undefined);
                setEditOpen(true);
              }}
            >
              新增申请
            </Button>,
          ]}
        />
      </PageContainer>
      {/* 新增/编辑 */}
      <Drawer
        width="60%"
        title={generateDrawerTitle()}
        onClose={closeEdit}
        open={editOpen}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={tableReload}
          labList={labList}
          editType={editType}
          queryLibReviewCount={queryLibReviewCount}
          curStatus={curStatus}
        />
      </Drawer>
    </>
  );
};

export default ReviewApplication;
