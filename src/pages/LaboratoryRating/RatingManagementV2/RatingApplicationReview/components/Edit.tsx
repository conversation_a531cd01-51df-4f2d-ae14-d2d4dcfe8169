/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, DatePicker, Form, Input, message, Modal, Spin } from 'antd';
import { getGradeReviewDetail, reviewProcess } from '@/api/apply';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import dayjs from 'dayjs';
import { re } from 'mathjs';
import ProcessLogWrapper from '@/components/ProcessLogWrapper';
import LabApplicationForm from '@/pages/LaboratoryRating/ApplicationManagement/RatingApplication/components/LabApplicationForm';
import Attachment from './Attachment';
import BaseForm from './BaseForm';
import EditTool from './EditTool';
import ReceiveForm from './ReceiveForm';
import TableSelect from './TableSelect';
import TableSelect1 from './TableSelect1';
import TableSelect2 from './TableSelect2';

const { TextArea } = Input;

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  dataStatus?: string;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId,
  setIsUploadReceipt,
  dialogTitletext,
  dataStatus,
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  // 通过弹窗是否显示
  const [isShowPass, setIsShowPass] = useState<boolean>(false);

  const [isShowNotPass, setIsShowNotPass] = useState<boolean>(false);

  const [isShowSendBack, setIsShowSendBack] = useState<boolean>(false);

  const [isShowstopProcess, setIsShowstopProcess] = useState<boolean>(false);

  const [Id, setIds] = useState<any>(1);

  const [passValue, setPassValue] = useState<any>('');

  const [notPassValue, setNotPassValue] = useState<any>('');

  const [sendBackValue, setSendBackValue] = useState<any>('');

  const [docComplementDate, setDocComplementDate] = useState<any>('');

  const [stopProcessValue, setStopProcessValue] = useState<any>('');

  const formRef5 = useRef<any>(null);

  const formRef6 = useRef<any>(null);

  const formRef7 = useRef<any>(null);

  const formRef8 = useRef<any>(null);

  const formRef9 = useRef<any>(null);

  const formRef13 = useRef<any>(null);

  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getGradeReviewDetail(id);
        if (code === 200) {
          const data1 = {
            ...data,
            commitment: data.commitment === 1 ? [1] : [],
          };
          setBaseInfo(data);
          console.log(baseInfo, '详情1111');
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenPassDialog = async () => {
    const grounpData = formRef4.current.getDataConfig();
    if (grounpData && grounpData.members.length === 0) {
      message.error('请选择专家评审小组');
      return;
    }
    setPassValue('');
    formRef5.current?.setFieldsValue({ passRemark: '' });
    setIsShowPass(true);
  };
  const handleSubmitPass = async () => {
    if (!passValue) {
      message.error('请输入通过原因');
      return;
    }
    handleReview('submit', passValue);
  };
  // 不通过

  const handleOpenRejectDialog = async () => {
    setNotPassValue('');
    formRef6.current?.setFieldsValue({ notPassRemark: '' });
    setIsShowNotPass(true);
  };

  const handleOpenStopProcess = async () => {
    setStopProcessValue('');
    formRef8.current?.setFieldsValue({ stopProcessValue: '' });
    setIsShowstopProcess(true);
  };

  const handleSubmitReject = async () => {
    if (!notPassValue) {
      message.error('请输入不通过原因');
      return;
    }
    handleReview('reject', notPassValue);
  };
  // 退回
  const handleOpenSendBackDialog = async () => {
    setSendBackValue('');
    formRef7.current?.setFieldsValue({ sendBackRemark: '' });
    formRef7.current?.setFieldsValue({ docComplementDate: '' });
    setIsShowSendBack(true);
  };
  // 退回
  const handleSubmitSendBack = async () => {
    if (!sendBackValue) {
      message.error('请输入退回原因');
      return;
    }
    console.log(
      dayjs(formRef7.current.getFieldValue('docComplementDate')).format(
        'YYYY-MM-DD'
      )
    );

    if (!formRef7.current.getFieldValue('docComplementDate')) {
      message.error('请选择截止期限');
      return;
    }
    handleReview('back', sendBackValue);
  };

  const handleSave = async () => {
    handleReview('save', sendBackValue);
  };

  const handleStopProcess = async () => {
    handleReview('stop', stopProcessValue);
  };

  const handleReview = async (stauts: String, word: String) => {
    let grounpData;
    if (stauts !== 'stop') {
      grounpData = {
        ...baseInfo.group,
        ...formRef4.current.getDataConfig(),
      };
    }
    let realParams = {};
    if (stauts === 'submit') {
      realParams = {
        id,
        group: grounpData,
        status: 'initialReview',
        eventCode: stauts,
        reviewRemark: word,
      };
    } else if (stauts === 'back') {
      realParams = {
        id,
        status: 'initialReview',
        eventCode: stauts,
        reviewRemark: word,
        docComplementDate: dayjs(
          formRef7.current.getFieldValue('docComplementDate')
        ).format('YYYY-MM-DD'),
      };
    } else if (stauts === 'reject') {
      realParams = {
        id,
        status: 'initialReview',
        eventCode: stauts,
        reviewRemark: word,
      };
    } else if (stauts === 'stop') {
      realParams = {
        id,
        status: 'fieldReviewAwait',
        eventCode: stauts,
        reviewRemark: word,
      };
    } else {
      if (grounpData && grounpData.members.length === 0) {
        realParams = {
          id,
          status: 'initialReview',
          eventCode: stauts,
        };
      } else {
        realParams = {
          id,
          group: grounpData,
          status: 'initialReview',
          eventCode: stauts,
        };
      }
    }

    const { code, data, msg } = await reviewProcess(realParams);
    if (code === 200) {
      message.success('提交成功');
      setIsShowPass(false);
      setIsShowNotPass(false);
      setIsShowSendBack(false);
      close();
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formRef4 = useRef<any>(null);

  const formRef10 = useRef<any>(null);

  const handleSchedule = () => {
    if (!formRef9.current.getDate()) {
      message.error('请选择现场评审排期时间');
      return;
    }
    if (formRef9.current.getDate().length === 2) {
      const dateValue = formRef9.current.getDate();
      const realParams = {
        ...baseInfo,
        fieldViewStartDate: dayjs(dateValue[0].$d).format('YYYY-MM-DD'),
        fieldViewEndDate: dayjs(dateValue[1].$d).format('YYYY-MM-DD'),
        eventCode: 'submit',
      };
      Modal.confirm({
        title: '提示',
        content: '确认在设置的时间内进行现场评审吗？',
        onOk: async () => {
          try {
            const { code, msg } = await reviewProcess(realParams);
            if (code === 200) {
              message.success('排期成功');
              close();
            } else {
              message.error(msg);
              return;
            }
          } catch (error) {
            message.error('删除失败，请稍后重试');
          }
        },
        onCancel: () => {
          // 用户取消删除操作，可添加提示信息
          message.info('已取消删除操作');
        },
      });
    }
  };

  const handleConfirmResults = () => {
    console.log(1);

    if (!formRef10.current.getDate()) {
      message.error('请选择评审结果');
      return;
    }

    console.log(2);

    if (formRef10.current.getDate() === '1' && !formRef10.current.getDateId()) {
      message.error('请选择评级建议');
      return;
    }

    console.log(3);

    if (
      (formRef10.current.getDate() === '1' &&
        formRef10.current.getDateId() &&
        formRef10.current.getDateId().length > 0) ||
      formRef10.current.getDate() === '0'
    ) {
      const dateValue = formRef10.current.getDate();
      const dateValue1 = formRef10.current.getDateId();
      const realParams = {
        ...baseInfo,
        // reviewSetId:dateValue,
        eventCode: 'submit',
        reviewResult: dateValue,
        reviewSetId: dateValue1,
      };
      Modal.confirm({
        title: '提示',
        content: '确认提交对机构的评审结果吗？',
        onOk: async () => {
          try {
            const { code, msg } = await reviewProcess(realParams);
            if (code === 200) {
              message.success('评审定级成功');
              close();
            } else {
              message.error(msg);
              return;
            }
          } catch (error) {
            message.error('删除失败，请稍后重试');
          }
        },
        onCancel: () => {
          message.info('已取消删除操作');
        },
      });
    }
  };

  const handleConfirmResultsFinal = () => {
    if (!formRef10.current.getFinalSetId()) {
      message.error('请填写最终定级');
      return;
    }

    console.log(formRef10.current.getFieldValue());

    // 确认提交弹窗
    Modal.confirm({
      title: '提示',
      content: '确认提交评审定级？',
      onOk: async () => {
        try {
          const dateValue = formRef10.current.getDate();
          const dateValue1 = formRef10.current.getDateId();

          let attachmentIds: any;

          if (
            formRef10.current.getFieldValue() &&
            formRef10.current.getFieldValue().attachmentIds &&
            formRef10.current.getFieldValue().attachmentIds.length > 0
          ) {
            const formInfo = formRef10.current.getFieldValue();

            console.log(formInfo.attachmentIds, 11111);

            attachmentIds = formInfo.attachmentIds
              .filter(
                (item: any) =>
                  item.response &&
                  item.response.data &&
                  item.response.data.ossId
              )
              .map((item: any) => {
                return {
                  name: item.response.data.fileName,
                  id: item.response.data.ossId,
                  url: item.response.data.url,
                };
              });
          }

          console.log(attachmentIds);

          const realParams = {
            ...baseInfo,
            eventCode: 'submit',
            reviewResult: dateValue,
            reviewSetId: dateValue1,
            finalSetId: formRef10.current.getFinalSetId(),
            reviewFile: attachmentIds ? JSON.stringify(attachmentIds) : '',
          };

          const { code, msg } = await reviewProcess(realParams);
          if (code === 200) {
            message.success('评审定级成功');
            close();
          } else {
            message.error(msg);
          }
        } catch (error) {
          message.error('提交失败，请稍后重试');
        }
      },
      onCancel: () => {
        message.info('已取消提交操作');
      },
    });

    // if (formRef10.current.getFinalSetId().length > 0 ) {
    //     const dateValue = formRef10.current.getDate()
    //     const dateValue1 = formRef10.current.getDateId()
    //     const realParams = {
    //       ...baseInfo,
    //       eventCode: "submit",
    //       reviewResult: dateValue,
    //       reviewSetId: dateValue1,
    //     };
    //     Modal.confirm({
    //       title: '提示',
    //       content: '确认提交对机构的评审结果吗？',
    //       onOk: async () => {
    //         try {
    //           const { code, msg } = await reviewProcess(realParams);
    //           if (code === 200) {
    //             message.success('评审定级成功');
    //             close();
    //           }  else {
    //             message.error(msg);
    //             return;
    //           }
    //         } catch (error) {
    //           message.error('删除失败，请稍后重试');
    //         }
    //       },
    //       onCancel: () => {
    //         message.info('已取消删除操作');
    //       },
    //     });
    // }
  };

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="d-flex">
          <div className="flex-1 overflow-auto p-6 w-80">
            {/* <BaseForm
              readonlyAll={true}
              readonly={true}
              hideRank={true}
              isShowChar={false}
              dialogTitletext={dialogTitletext}
              onRef={formRef1}
              id={id}
              detailInfo={baseInfo}
            /> */}
            <LabApplicationForm
              readonly={true}
              dialogTitletext={dialogTitletext}
              id={id}
              initialData={baseInfo}
            />
            {dataStatus == 'initialReview' && (
              <div className="mt-4">
                <TableSelect
                  loading={loading}
                  onRef={formRef4}
                  dialogTitletext={dialogTitletext}
                  detailInfo={baseInfo}
                  mode={mode}
                  onSubmit={(val) => {
                    handleSaveSubmit(val);
                  }}
                />
              </div>
            )}

            <div className="mt-4">
              <ReceiveForm
                loading={loading}
                onRef={formRef2}
                dataStatus={dataStatus}
                dialogTitletext={dialogTitletext}
                detailInfo={baseInfo}
                mode={mode}
                onSubmit={(val) => {
                  handleSaveSubmit(val);
                }}
              />
            </div>

            {dataStatus == 'fieldReviewAwait' && (
              <div className="mt-4">
                <TableSelect1
                  loading={loading}
                  onRef={formRef9}
                  dialogTitletext={dialogTitletext}
                  detailInfo={baseInfo}
                  mode={mode}
                  onSubmit={(val) => {
                    handleSaveSubmit(val);
                  }}
                />
              </div>
            )}

            {/* {(dataStatus=='fieldReviewResultAwait' ||  
              dataStatus=='fieldReviewResult' ||
              dataStatus=='resultConfirm'
            )&& (
            <div className="mt-4">
              <EditTool
               dialogTitletext={dialogTitletext}
              baseInfo={baseInfo}
              onRef={formRef13}
               id={id}
              />
            </div>
          )}    */}

            {(dataStatus == 'fieldReviewResultAwait' ||
              dataStatus == 'fieldReviewResult' ||
              dataStatus == 'resultConfirm') && (
              <div className="mt-4">
                <TableSelect2
                  loading={loading}
                  onRef={formRef10}
                  id={id}
                  dialogTitletext={dialogTitletext}
                  detailInfo={baseInfo}
                  dataStatus={dataStatus}
                  mode={mode}
                  onSubmit={(val) => {
                    handleSaveSubmit(val);
                  }}
                />
              </div>
            )}
          </div>
          <div className="w-20">
            <ProcessLogWrapper documentNo={Id} detailInfo={baseInfo} />
          </div>
        </div>
      )}
      <span>{mainLoading}</span>
      <span>{readonly}</span>
      {dialogTitletext !== '查看' && dataStatus == 'initialReview' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3 bottom-btn-wraper">
          <Button
            onClick={() => {
              handleOpenPassDialog();
            }}
            loading={loading}
            type="primary"
          >
            通过
          </Button>
          <Button
            danger
            onClick={() => {
              handleOpenRejectDialog();
            }}
            loading={loading}
          >
            不通过
          </Button>
          <Button
            onClick={() => {
              handleOpenSendBackDialog();
            }}
            loading={loading}
          >
            退回
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              handleSave();
            }}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}
      {dialogTitletext !== '查看' && dataStatus == 'fieldReviewAwait' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3 bottom-btn-wraper">
          <Button
            onClick={() => {
              handleSchedule();
            }}
            loading={loading}
            type="primary"
          >
            确认排期
          </Button>
          <Button
            onClick={() => {
              handleOpenStopProcess();
            }}
            danger
            loading={loading}
          >
            终止评审
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext !== '查看' && dataStatus == 'fieldReviewResultAwait' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleConfirmResults();
            }}
            loading={loading}
            type="primary"
          >
            确认提交评审结果
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext !== '查看' && dataStatus == 'resultConfirm' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleConfirmResultsFinal();
            }}
            loading={loading}
            type="primary"
          >
            确认提交
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext == '查看' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {/*  通过弹窗 */}
      <Modal
        title="审核通过"
        open={isShowPass}
        footer={null}
        onCancel={() => {
          setIsShowPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef5}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="passRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={passValue}
              onChange={(e: any) => {
                setPassValue(e.target.value);
                formRef5.current?.setFieldsValue({
                  passRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            onClick={() => {
              handleSubmitPass();
            }}
            className="mr-2"
          >
            确认通过
          </Button>
          <Button
            onClick={() => {
              setIsShowPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>

      {/*  不通过弹窗 */}
      <Modal
        title="审核不通过"
        open={isShowNotPass}
        footer={null}
        onCancel={() => {
          setIsShowNotPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef6}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="notPassRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={notPassValue}
              onChange={(e: any) => {
                setNotPassValue(e.target.value);
                formRef6.current?.setFieldsValue({
                  notPassRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">提示：审核不通过后流程结束；</div>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleSubmitReject();
            }}
          >
            确认不通过
          </Button>
          <Button
            onClick={() => {
              setIsShowNotPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>

      {/*  退回弹窗 */}
      <Modal
        title="退回"
        open={isShowSendBack}
        footer={null}
        onCancel={() => {
          setIsShowSendBack(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef7}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="sendBackRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={sendBackValue}
              onChange={(e: any) => {
                setSendBackValue(e.target.value);
                formRef7.current?.setFieldsValue({
                  sendBackRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">
            提示：退回后申请人可直接修改材料重新提交。
          </div>
          <Form.Item
            label="补正材料重新提交截止期限设置:"
            name="docComplementDate"
            rules={[{ required: true, message: '请选择截止期限' }]}
          >
            <DatePicker
              value={docComplementDate}
              onChange={(e: any) => {
                setDocComplementDate(e.target.value);
                formRef7.current?.setFieldsValue({
                  docComplementDate: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">
            申请机构超期未重新提交，系统会自动终止流程。
          </div>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleSubmitSendBack();
            }}
          >
            确认退回
          </Button>
          <Button
            onClick={() => {
              setIsShowSendBack(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>

      {/*  流程终止 */}
      <Modal
        title="流程终止"
        open={isShowstopProcess}
        footer={null}
        onCancel={() => {
          setIsShowstopProcess(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef8}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="stopProcessValue"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={stopProcessValue}
              onChange={(e: any) => {
                setStopProcessValue(e.target.value);
                formRef8.current?.setFieldsValue({
                  stopProcessValue: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">提示：终止评审后流程结束；</div>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleStopProcess();
            }}
          >
            确认不通过
          </Button>
          <Button
            onClick={() => {
              setIsShowstopProcess(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default MenuDetail;
