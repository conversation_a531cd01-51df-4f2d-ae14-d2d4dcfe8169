/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Spin } from 'antd';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import BaseForm from './BaseForm';
import Attachment from './Attachment';
import ReceiveForm from './ReceiveForm';

type TEditProps = {
  close: () => void;
  detailId?: string;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId,
  setIsUploadReceipt,
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getReceptDetail(id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.sampleStatus = data.sampleStatus
            ? data.sampleStatus.split(',')
            : [];
          setBaseInfo(data);
          setReadonly(mode === 'view' || data.status === '1');
          // 确认该记录是否已上传收样回执单
          if (
            data.taskDetails &&
            data.taskDetails.length &&
            data.taskDetails[0].receiptFile
          ) {
            setIsUploadReceipt(true);
          }else{
            setIsUploadReceipt(false);
          }
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="flex-1 overflow-auto p-4">
          <BaseForm
            readonlyAll={true}
            readonly={true}
            hideRank={true}
            isShowChar={false}
            onRef={formRef1}
            id={id}
            detailInfo={baseInfo}
          />
          {baseInfo && baseInfo.receiptUnitId && (
            <div className="mt-4">
              <Attachment
                taskId={baseInfo.assessmentTaskId}
                assessedLabId={baseInfo.receiptUnitId}
                file={
                  baseInfo.taskDetails && baseInfo.taskDetails.length
                    ? baseInfo.taskDetails[0].receiptFile
                    : ''
                }
                setIsUploadReceipt={setIsUploadReceipt}
              />
            </div>
          )}
          <div className="mt-4">
            <ReceiveForm
              loading={loading}
              onRef={formRef2}
              baseInfo={baseInfo}
              mode={mode}
              onSubmit={(val) => {
                handleSaveSubmit(val);
              }}
            />
          </div>
        </div>
      )}
      <span>{mainLoading}</span>
      <span>{readonly}</span>
      {!mainLoading && readonly && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}
    </div>
  );
};

export default MenuDetail;
