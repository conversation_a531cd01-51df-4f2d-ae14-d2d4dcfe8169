/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  message,
  Modal,
  Spin,
  Table,
} from 'antd';
import {
  getExpertGroup,
  getExpertiseExpertArea,
  getGroupList,
  randomGroup,
} from '@/api/apply';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import BlockContainer from '@/components/BlockContainer';

const { RangePicker } = DatePicker;

const CheckboxGroup = Checkbox.Group;

type TEditProps = {
  loading?: boolean;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const TableSelect: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  detailInfo,
  onRef,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      getDate: () => {
        return docComplementDate;
      },
    };
  });
  const [form] = Form.useForm();

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [openRandomDetail, setOenRandomDetail] = useState<boolean>(false);

  const [tableShow, setTableShow] = useState<boolean>(true);
  const [tableShow1, setTableShow1] = useState<boolean>(true);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [selectOption, setSelectOption] = useState<any[]>([]);

  const [selectedRows1, setSelectedRows] = useState<any[]>([]);

  const [plainOptions, setPlainOptions] = useState<any[]>([]);

  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);

  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [grounpData, setGrounpData] = useState<any[]>([]);

  const formRef2 = useRef<any>(null);

  const formRef3 = useRef<any>(null);

  const [docComplementDate, setDocComplementDate] = useState<any>('');

  const [grounpConfigData, setGrounpConfigData] = useState<any>({});

  const [grounpConfigReturnData, setGrounpConfigReturnData] = useState<any>({});

  useEffect(() => {
    getExpertiseExpertAreaData();
    getGroupListData();
  }, []);
  useEffect(() => {
    if (detailInfo?.docReviews.length > 0) {
      setDataSource(detailInfo.docReviews);
    } else {
      setDataSource([]);
      setDataSource1([]);
    }
  }, [detailInfo]);
  useEffect(() => {
    setGrounpConfigReturnData({
      ...grounpConfigData,
      members: dataSource,
    });
  }, [grounpConfigData, dataSource]);

  const getExpertiseExpertAreaData = async () => {
    try {
      const { code, data, msg } = await getExpertiseExpertArea();
      console.log(data, 'msg');

      const gradeListData = data.map((item: any) => {
        return {
          label: item.dictLabel,
          value: item.dictValue,
        };
      });
      setPlainOptions(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const getGroupListData = async () => {
    try {
      const { code, data, msg } = await getGroupList({});
      const gradeListData = data.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      setSelectOption(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const getExpertGroupData = async (id: any) => {
    try {
      const { code, data, msg } = await getExpertGroup(id);
      setGrounpData(data?.members);
      setGrounpConfigData(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const columns: any = [
    {
      title: '专家姓名',
      dataIndex: 'auditName',
      hideInSearch: true,
    },
    {
      title: '文审结论',
      dataIndex: 'docAuditResult',
      hideInSearch: true,

      render: (_: any, record: any, index: any) => {
        if (record?.docAuditResult === '1') {
          return <div>符合</div>;
        } else if (record?.docAuditResult === '0') {
          return <div>不符合</div>;
        } else {
          return <div>-</div>;
        }
      },
    },

    {
      title: '文审提交状态',
      dataIndex: 'status',
      hideInSearch: true,

      render: (text: any, record: any, _: any, action: any) => {
        if (record.status === 1) {
          return <div>已提交</div>;
        }
        if (record.status === 0) {
          return (
            <div>
              <a className="text-red-500">未提交</a>
            </div>
          );
        }
      },
    },
  ];

  const rowSelection: any = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRows(selectedRows);
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows
      );
    },
    getCheckboxProps: (record: any) => ({
      name: record.name,
    }),
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  return (
    <>
      <BlockContainer title="评级文审">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          <Table dataSource={dataSource} columns={columns} pagination={false} />
        </ProForm>
      </BlockContainer>

      <BlockContainer className="mt-2" title="现场评审排期">
        <Form
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef3}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="开始与结束时间:"
            name="passRemark"
            rules={[{ required: true, message: '请选择时间' }]}
          >
            <RangePicker
              value={docComplementDate}
              disabled={dialogTitletext === '查看'}
              onChange={(e: any) => {
                setDocComplementDate(e);
                formRef3.current?.setFieldsValue({ docComplementDate: e });
              }}
            />
          </Form.Item>
        </Form>
      </BlockContainer>
    </>
  );
};

export default TableSelect;
