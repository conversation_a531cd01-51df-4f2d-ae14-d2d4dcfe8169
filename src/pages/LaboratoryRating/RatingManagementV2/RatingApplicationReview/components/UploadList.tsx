/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
  
import { Button, message,
  Typography,
  Table,Modal } from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import CheckboxWithOther from '@/components/CheckboxWithOther';
import RequiredTag from '@/components/RequiredTag';
import { Any } from '@react-spring/web';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import { log } from 'node:console';
import EFileView from '@/components/EFileView';
import { downloadFile } from '@/api/file';

import { getFileStream, getFileStreamForPreview } from '@/api/file';

import FileViewByStream from '@/components/FileViewByStream';


type TEditProps = {
  loading?: boolean;
  baseInfo: any;
  onSubmit: (params: any) => void;
  onRef: any;
  onUpload?: any;
  docs?: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const UploadList: React.FC<any> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  onUpload,
  onRef,
  mode,
  docs,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);


  const [fileUrl, setFileUrl] = useState<any>();


  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [previewFileId, setPreviewFileId] = useState<any>("");





  useEffect(() => {
    if (docs) {
      console.log(docs, 'docs');
 

      const fileList = JSON.parse(docs).map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));

      if (formRef.current) {
            formRef.current.setFieldValue('attachmentIds', fileList);
            setDataSource(fileList);
       }


    } else {
       if (formRef.current) {
            formRef.current.setFieldValue('attachmentIds', []);
       }
    }

  }, [docs]);

  const columns: ProColumns<Any>[] = [
    {
      title: '文件名称',
      dataIndex: 'name',
      readonly: true,
    
    },
    {
      title: '上传人',
      dataIndex: 'receiptUnitName',
      readonly: true,
    },
    {
      title: '上传时间',
      dataIndex: 'receiptDate',
      readonly: true,
    },
    {
      title: '操作',
      width: 160,
      renderFormItem: (text, record:any, _, action) =>{
          const btn1 =  [
            <Button
              type="link"
              size="small"
              onClick={() => {
    
              setIsShowFileView(true);
              setPreviewFileId(record.uid);
              }}
            >
              预览
            </Button>,
            <Button
            type="link"
            size="small"
            onClick={() => {
                downloadFile(
                  record.record.uid,
                  record.record.name
                )
            }}
            >
            下载
            </Button>,
            <Button
            type="link"
            size="small"
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>,
          ];

          const btn2 =  [
            <Button
            type="link"
            size="small"
            onClick={() => {
   console.log(record.uid, text, record, _, 555555);
              
              setIsShowFileView(true);
              setPreviewFileId(record.uid);
            }}
          >
            预览
          </Button>
          ]

          let result = [];
          if(dialogTitletext!=='查看'){
            result = btn1; 
          } else{
            result = btn2; 
          }
          return result;
      },
      render: (text, record:any, _:any, action) => {
        const btn1 =  [
          <Button
            type="link"
            size="small"
            onClick={() => {
              console.log(record.uid, text, record, _, 555555);
              
              setIsShowFileView(true);
              setPreviewFileId(record.uid);
            }}
          >
            预览
          </Button>,
        ];

        const btn2 =  [
          <Button
          type="link"
          size="small"
          onClick={() => {
  
              setIsShowFileView(true);
              setPreviewFileId(record.uid);
          }}
        >
          预览
        </Button>
        ]

        let result = [];
        if(dialogTitletext!=='查看'){
          result = btn1; 
        } else{
          result = btn2; 
        }
        return result;
    },
    },
  ];

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计

  // 删除当前行
  const handleDelete = (id: React.Key) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这一行吗？',
      onOk: async () => {
        try {
          // 调用删除接口
          // 这里需要替换为实际的删除接口调用
          // const { code, msg } = await deleteApi({ id }); 
          // if (code !== codeDefinition.QUERY_SUCCESS) {
          //   messageApi.error(msg);
          //   return;
          // }
          
          // 模拟删除成功，重新加载表格数据
          // tableReload();
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败，请稍后重试');
        }
      },
      onCancel: () => {
        // 用户取消删除操作，可添加提示信息
        message.info('已取消删除操作');
      },
    });
  };


  const tableReload = () => {
    // 重置分页状态
    // actionRef.current?.reset?.();
    // 然后重新加载数据
    // tableRef.current?.reload();
  };

  const onChange = (fileList: any) => {
    console.log(fileList, '文件', fileList.fileList.length > 0);
    setFileUrl(fileList.file);
    console.log(fileList.fileList && fileList.fileList.length > 0);
    
    if (fileList.fileList && fileList.fileList.length > 0) {
        const  fileList1 = fileList.fileList.map((_item: any) => {
        if (_item.response && _item.response.code === 200) {
              const obj = {
              url: _item.response.data.url,
              fileName: _item.response.data.fileName,
              size: _item.size,
              ossId: _item.response.data.ossId,
                }
              return  obj   
            }
          }).filter((item:any) => item !== undefined); // 过滤掉 undefined 值;
        if (fileList1.length > 0) {
          onUpload(fileList1)
        }

      // console.log("zhixingdaodzheli", fileList1);
      
    }
  };



  return (
    <>
      <BlockContainer title="">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >

        {(dialogTitletext!='查看' && mode !='view') &&   <EProFormUploadButton
            name="attachmentIds"
            label="支撑材料"
            // colProps={{ span: 24 }}
            // labelCol={{ flex: 0.005 }}
            max={10}
            // readonly={readonly}
            onChange={onChange}
        />}
  
 

          <EditableProTable<Any>

            rowKey="id"
            name="table"
            editableFormRef={tableRef}
            loading={loading}
            request={async () => ({
              data: dataSource,
              total: dataSource?.length,
              success: true,
            })}
            scroll={{ x: 960 }}
            recordCreatorProps={false}
            columns={columns}
            editable={{
              type: 'multiple',
              editableKeys,
              onSave: async (rowKey, data, row) => {},
              actionRender: (row: any, config, defaultDom) => [
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    if (!row.receiptDate) {
                      message.warning('请选择接收日期');
                      return;
                    }
                    if (!row.sampleStatus) {
                      message.warning('请选择样本状态');
                      return;
                    }
                    onSubmit([row]);
                  }}
                >
                  接收
                </Button>,
              ],
            }}
          />
        </ProForm>
      </BlockContainer>


      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setIsShowFileView(false)}
        open={isShowFileView}
        footer={null}
        destroyOnClose
      >
        <FileViewByStream fileId={previewFileId} isPreview />
      </Modal>



    </>
  );
};

export default UploadList;
