/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  InputNumber,
  message,
  Modal,
  Select,
  Table,
  Typography,
} from 'antd';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import { log } from 'console';
import BlockContainer from '@/components/BlockContainer';
import UploadList from './UploadList';

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  detailInfo?: any;
  dataStatus?: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  detailInfo,
  dialogTitletext,
  onRef,
  dataStatus,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<any>([[], []]);

  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [sampleNum, setSampleNum] = useState<any>(0);
  const [sourceTotal, setSourceTotal] = useState<any>(0);
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const formRef2 = useRef<any>(null);

  const [tableRefArray, setTableRefArray] = useState<any[]>([]);

  const [dataSourceArray, setDataSourceArray] = useState<any[]>([]);

  const [sampleNumArray, setSampleNumArray] = useState<any[]>([]);

  const [sourceTotalNumArray, setSourceTotalNumArray] = useState<any[]>([]);

  const [idicesList, setIndicesList] = useState<any>([]);

  const [docs, setDocs] = useState<any>('');
  //当前上传附件的id
  const [curId, setCurId] = useState<any>('');

  // 新增状态用于存储上传的附件数据
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 定义回调函数，用于更新上传的附件数据
  const handleFileUpload = (files: any) => {
    console.log(files, '上传附件内容');
    setUploadedFiles(files);
    const newDataSourceArray = [...dataSourceArray];
    newDataSourceArray.forEach((tableArr, index) => {
      tableArr.forEach((item: any) => {
        if (item.id === curId) {
          item.docs = JSON.stringify(files);
        }
      });
    });
    setDataSourceArray(newDataSourceArray);
  };

  const resultList = [
    { value: '1', label: '符合' },
    { value: '0', label: '不符合' },
  ];

  // 计算 rowSpan 的函数
  const calculateRowSpan = (data: any[]) => {
    const result = [...data];
    const countMap = new Map<string, number>();
    const startIndexMap = new Map<string, number>();

    // 第一次遍历，记录每个 parentLab 首次出现的索引和出现次数
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      if (!countMap.has(parentLab)) {
        countMap.set(parentLab, 1);
        startIndexMap.set(parentLab, index);
      } else {
        countMap.set(parentLab, countMap.get(parentLab)! + 1);
      }
    });

    // 第二次遍历，设置 rowSpan
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      const startIndex = startIndexMap.get(parentLab)!;
      const count = countMap.get(parentLab)!;
      if (index === startIndex) {
        item.sampleCodeRowSpan = count;
      } else {
        item.sampleCodeRowSpan = 0;
      }
    });

    return result;
  };

  const [columns, setColumns] = useState<any[]>([]);

  const columns1 = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'selfScore',
      // 使用 renderFormItem 渲染数字输入框
      renderFormItem: (_: any, record: any) => (
        <InputNumber
          value={record.record?.selfScore}
          onChange={(value: any) => {
            // 添加输入值范围校验
            if (
              value !== undefined &&
              (value < 0 || value > record.record?.score * 1)
            ) {
              message.error('自评得分必须在 0 到 10 之间');
              return;
            }

            // 这里可以添加更新 record 数据的逻辑
            const newDataSourceArray = [...dataSourceArray];
            // console.log(value, "value", _.index,record.record, newDataSourceArray, tableIndex);
            let curIndex: any;
            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.selfScore = value;
                  curIndex = index;
                }
              });
              //tableArr的selfScore求和
              // console.log(totalScore, "totalScore");
              // const newsampleNumArray = [...sampleNumArray];
              // newsampleNumArray[index] = totalScore
              // setSampleNumArray(newsampleNumArray)
            });
            setDataSourceArray(newDataSourceArray);
            const total = dataSourceArray[curIndex].reduce(
              (total: any = 0, arr: any) => {
                if (arr.selfScore === undefined) {
                  arr.selfScore = 0;
                }
                return total + arr.selfScore * 1;
              },
              0
            );

            const newsampleNumArray = [...sampleNumArray];
            newsampleNumArray[curIndex] = total;
            setSampleNumArray(newsampleNumArray);
            const totals2 = newsampleNumArray.reduce(
              (total: any = 0, arr: any) => {
                if (arr === undefined) {
                  arr = 0;
                }
                return total + arr;
              },
              0
            );
            // console.log(dataSourceArray, 1111);
            setSampleNum(totals2);
          }}
          min={0} // 可根据需求设置最小值
          max={record.record?.score * 1} // 可根据需求设置最小值
          // 可根据需求添加其他属性，如 max 等
        />
      ),
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      ellipsis: true,
      readonly: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },
  ];
  const columns2 = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'selfScore',
      // 使用 renderFormItem 渲染数字输入框
      renderFormItem: (_: any, record: any) => (
        <InputNumber
          value={record.record?.selfScore}
          onChange={(value: any) => {
            // 添加输入值范围校验
            if (
              value !== undefined &&
              (value < 0 || value > record.record?.score * 1)
            ) {
              message.error('自评得分必须在 0 到 10 之间');
              return;
            }

            // 这里可以添加更新 record 数据的逻辑
            const newDataSourceArray = [...dataSourceArray];
            // console.log(value, "value", _.index,record.record, newDataSourceArray, tableIndex);
            let curIndex: any;
            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.selfScore = value;
                  curIndex = index;
                }
              });
              //tableArr的selfScore求和
              // console.log(totalScore, "totalScore");
              // const newsampleNumArray = [...sampleNumArray];
              // newsampleNumArray[index] = totalScore
              // setSampleNumArray(newsampleNumArray)
            });
            setDataSourceArray(newDataSourceArray);
            const total = dataSourceArray[curIndex].reduce(
              (total: any = 0, arr: any) => {
                if (arr.selfScore === undefined) {
                  arr.selfScore = 0;
                }
                return total + arr.selfScore * 1;
              },
              0
            );

            const newsampleNumArray = [...sampleNumArray];
            newsampleNumArray[curIndex] = total;
            setSampleNumArray(newsampleNumArray);
            const totals2 = newsampleNumArray.reduce(
              (total: any = 0, arr: any) => {
                if (arr === undefined) {
                  arr = 0;
                }
                return total + arr;
              },
              0
            );
            // console.log(dataSourceArray, 1111);
            setSampleNum(totals2);
          }}
          min={0} // 可根据需求设置最小值
          max={record.record?.score * 1} // 可根据需求设置最小值
          // 可根据需求添加其他属性，如 max 等
        />
      ),
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },

    {
      title: '文审专家',
      dataIndex: 'docAuditUserName',
      readonly: true,
    },
    {
      title: '文审结论',
      dataIndex: 'docAuditResult',
      valueType: 'select',
      fieldProps: {
        options: resultList,
      },

      render: (_: any, record: any, index: any) => {
        if (record?.docAuditResult === '1') {
          return <div>符合</div>;
        } else if (record?.docAuditResult === '0') {
          return <div>不符合</div>;
        } else {
          return <div>-</div>;
        }
      },
    },

    {
      title: '文审备注',
      dataIndex: 'docAuditRemark',
    },
  ];

  const columns3 = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'selfScore',
      // 使用 renderFormItem 渲染数字输入框
      renderFormItem: (_: any, record: any) => (
        <InputNumber
          value={record.record?.selfScore}
          onChange={(value: any) => {
            // 添加输入值范围校验
            if (
              value !== undefined &&
              (value < 0 || value > record.record?.score * 1)
            ) {
              message.error('自评得分必须在 0 到 10 之间');
              return;
            }

            // 这里可以添加更新 record 数据的逻辑
            const newDataSourceArray = [...dataSourceArray];
            // console.log(value, "value", _.index,record.record, newDataSourceArray, tableIndex);
            let curIndex: any;
            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.selfScore = value;
                  curIndex = index;
                }
              });
              //tableArr的selfScore求和
              // console.log(totalScore, "totalScore");
              // const newsampleNumArray = [...sampleNumArray];
              // newsampleNumArray[index] = totalScore
              // setSampleNumArray(newsampleNumArray)
            });
            setDataSourceArray(newDataSourceArray);
            const total = dataSourceArray[curIndex].reduce(
              (total: any = 0, arr: any) => {
                if (arr.selfScore === undefined) {
                  arr.selfScore = 0;
                }
                return total + arr.selfScore * 1;
              },
              0
            );

            const newsampleNumArray = [...sampleNumArray];
            newsampleNumArray[curIndex] = total;
            setSampleNumArray(newsampleNumArray);
            const totals2 = newsampleNumArray.reduce(
              (total: any = 0, arr: any) => {
                if (arr === undefined) {
                  arr = 0;
                }
                return total + arr;
              },
              0
            );
            // console.log(dataSourceArray, 1111);
            setSampleNum(totals2);
          }}
          min={0} // 可根据需求设置最小值
          max={record.record?.score * 1} // 可根据需求设置最小值
          // 可根据需求添加其他属性，如 max 等
        />
      ),
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },

    {
      title: '专家评分',
      dataIndex: 'finalScore',
      readonly: true,
    },

    {
      title: '现场评审专家',
      dataIndex: 'fieldAuditUserName',
      readonly: true,
    },

    {
      title: '现场评审结论',
      dataIndex: 'docAuditResult',
      valueType: 'select',
      fieldProps: {
        options: resultList,
      },
    },

    {
      title: '现场评审备注',
      dataIndex: 'fieldAuditRemark',
    },
  ];

  useEffect(() => {
    if (detailInfo) {
      if (detailInfo.indices) {
        const gradeListData = detailInfo.indices;
        setIndicesList(gradeListData);
        const arrContainer: any = [];
        const editId: any = [];
        console.log(gradeListData, 'gradeListData');

        gradeListData.forEach((e: any, index: number) => {
          let arrData: any = [];
          e.children.forEach((e1: any, index1: number) => {
            const arrData2 = e1.children.map((e2: any, index2: number) => {
              return {
                ...e2,
                parentLab: e1.lab,
              };
            });
            arrData.push(...arrData2);
          });
          arrContainer[index] = calculateRowSpan(arrData);
          const sourceTotalNumArray: any = [];
          arrContainer.forEach((item: any, index: number) => {
            const total = item.reduce((total: any = 0, arr: any) => {
              if (arr.selfScore === undefined) {
                arr.selfScore = 0;
              }
              return total + arr.selfScore * 1;
            }, 0);
            sourceTotalNumArray.push(total);
          });
          setSampleNumArray(sourceTotalNumArray);
          const totals2 = sourceTotalNumArray.reduce(
            (total: any = 0, arr: any) => {
              if (arr === undefined) {
                arr = 0;
              }
              return total + arr;
            },
            0
          );
          setSampleNum(totals2);

          const sourceTotalNum = gradeListData.map((e: any) => {
            return e.score;
          });

          setDataSourceArray(arrContainer);
          setSourceTotalNumArray(sourceTotalNum);
          // if(dialogTitletext!=='查看'){
          //   const newKeyId = arrData.map((e:any) => {
          //     return e.id
          //   })
          //    editId[index] = newKeyId
          // }
          // setEditableRowKeys(editId)
        });
        setTimeout(() => {
          const arr: any = [];
          gradeListData.forEach((e: any) => {
            arr.push(useRef<FormInstance>(null));
          });
          setTableRefArray(arr);
        });
      }
    }
  }, [detailInfo]);

  useEffect(() => {
    console.log(dataStatus, 'dataStatus');

    if (dataStatus === 'fieldReviewAwait') {
      setColumns(columns2);
    } else if (dataStatus === 'fieldReviewResultAwait') {
      setColumns(columns3);
    } else {
      setColumns(columns1);
    }
  }, [dataStatus]);

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计

  return (
    <>
      <BlockContainer title="指标明细">
        {idicesList.length > 0 &&
          idicesList.map((tableData: any, index: number) => (
            <BlockContainer title={tableData.lab}>
              <ProForm<{ table: Any[] }>
                formRef={formRef}
                initialValues={{ table: [] }}
                submitter={false}
                onFinish={handleSave}
              >
                <EditableProTable<Any>
                  rowKey="id"
                  name="table"
                  editableFormRef={tableRefArray[index]}
                  loading={loading}
                  request={async () => ({
                    data: dataSourceArray[index],
                    total: dataSourceArray[index]?.length,
                    success: true,
                  })}
                  scroll={{ x: 960 }}
                  recordCreatorProps={false}
                  columns={columns}
                  editable={{
                    type: 'multiple',
                    editableKeys: editableKeys[index],
                  }}
                />
                <div className="total-num">
                  <div className="num-box">合计: </div>
                  <div className="num-box">
                    指标名称总分：{sourceTotalNumArray[index]}
                  </div>
                  <div className="num-box">
                    自评得分：{sampleNumArray[index]}
                  </div>
                </div>
              </ProForm>
            </BlockContainer>
          ))}

        {idicesList.length > 0 && (
          <div className="total-num total-num-1">
            <div className="num-box">自评总得分: </div>
            <div className="num-box">{sampleNum}</div>
            <Button
              type="link"
              size="small"
              onClick={() => {
                setOpenDetail(true);
              }}
            >
              查看等级划分
            </Button>
          </div>
        )}

        {idicesList.length == 0 && <div className="no-data">暂无数据</div>}
      </BlockContainer>

      <Modal
        width="60%"
        title="支撑材料"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <UploadList
          dialogTitletext={dialogTitletext}
          loading={loading}
          onRef={formRef2}
          mode="view"
          docs={docs}
          onUpload={handleFileUpload} // 传递回调函数
        />
      </Modal>

      {/* 录入查找样本编号 */}
      <Modal
        title="等级划分"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={450}
      >
        <div className="p-4">
          <div>一级：≥60分，且＜70分；</div>
          <div>二级乙等：≥70分，且＜80分；</div>
          <div>二级甲等：≥80分，且＜85分；</div>
          <div>三级乙等：≥85分，且＜95分；</div>
          <div>三级甲等：≥95分。</div>
        </div>
      </Modal>
    </>
  );
};

export default ReceiveForm;
