/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
// 引入 react-quill
import 'react-quill/dist/quill.snow.css';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  Spin,
  Table,
} from 'antd';
import {
  downloadReportPdf,
  downloadReportWord,
  generateReport,
  uploadElectronicReport,
  uploadSignedReport,
} from '@/api/apply';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { FormInstance, ProForm } from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import { clone } from 'lodash';
// 引入样式
import BlockContainer from '@/components/BlockContainer';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import FileViewByStreamByUrl from '@/components/FileViewByStreamByUrl';
import download from '@/utils/download';

const { TextArea } = Input;

type TEditProps = {
  close?: () => void;
  id?: string;
  onRef: any;
  baseInfo?: any;
  setIsUploadReceipt?: (isUploadReceipt: boolean) => void;
  dialogTitletext?: string;
  mode?: 'edit' | 'view'; // 编辑模式或查看模式
};

const Task: React.FC<TEditProps> = ({
  close,
  id,
  onRef,
  baseInfo,
  setIsUploadReceipt,
  dialogTitletext,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleGetData: () => {
        return content;
      },
    };
  });

  useEffect(() => {
    if (baseInfo && baseInfo?.report && baseInfo?.report?.length > 0) {
      setContent(baseInfo.report);
    }
  }, [baseInfo]);

  const [content, setContent] = useState(''); // 存储富文本内容
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>('');

  const [isUpload, setIsUpload] = useState<boolean>(false);

  const formRef = useRef<FormInstance>(null);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    console.log(baseInfo, 'baseInfo222222222');

    try {
      if (baseInfo && baseInfo.electronicReport) {
        const p = clone(baseInfo);
        // try {
        const electronicReport = baseInfo.electronicReport;
        p.file = [
          {
            uid: electronicReport.ossId,
            name: electronicReport.fileName,
            status: 'done',
            type: 'application/msword',
            url: electronicReport.url,
            response: {
              code: 200,
              data: {
                fileName: electronicReport.fileName,
                ossId: electronicReport.ossId,
                url: electronicReport.url,
              },
            },
          },
        ];
        formRef.current?.setFieldValue('attachmentIds', p.file);
      }

      if (baseInfo && baseInfo.signedReport) {
        const p = clone(baseInfo);
        // try {
        const signedReport = baseInfo.signedReport;
        p.file = [
          {
            uid: signedReport.ossId,
            name: signedReport.fileName,
            status: 'done',
            type: 'application/msword',
            url: signedReport.url,
            response: {
              code: 200,
              data: {
                fileName: signedReport.fileName,
                ossId: signedReport.ossId,
                url: signedReport.url,
              },
            },
          },
        ];

        formRef.current?.setFieldValue('attachmentIds1', p.file);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    getDetailData();
  }, [baseInfo]);

  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ['bold', 'italic', 'underline', 'strike', 'blockquote'],
      [
        { list: 'ordered' },
        { list: 'bullet' },
        { indent: '-1' },
        { indent: '+1' },
      ],
      ['link', 'image'],
      ['clean'],
    ],
  };

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'blockquote',
    'list',
    'bullet',
    'indent',
    'link',
    'image',
  ];

  const onChange1 = async (file: any) => {
    if (file.fileList && file.fileList.length > 0) {
      let attachmentIds = file.fileList
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            ossId: item.response.data.ossId,
            url: item.response.data.url,
          };
        });

      console.log(attachmentIds[0], 9999);
      if (attachmentIds[0]) {
        const params = {
          ...attachmentIds[0],
          id,
        };
        const data = await uploadElectronicReport(params);
      }
    }
  };

  const onChange2 = async (file: any) => {
    if (file.fileList && file.fileList.length > 0) {
      let attachmentIds = file.fileList
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            ossId: item.response.data.ossId,
            url: item.response.data.url,
          };
        });

      console.log(attachmentIds[0], 9999);
      if (attachmentIds[0]) {
        const params = {
          ...attachmentIds[0],
          id,
        };
        const data = await uploadSignedReport(params);
      }
    }
  };

  return (
    <div className="flex flex-col h-full w-full">
      <BlockContainer title="评审报告">
        {/* <div className='btn-wraper'>
          <Button type="primary" 
            className='mr-10'
            onClick={() => {
              handlePreview();
            }}
        >
              预览报告
            </Button>
          <Button 
            className='mr-10'
            onClick={() => {
              handleDownload();
            }}
        >
              下载pdf报告
            </Button>
          <Button 
            onClick={() => {
              handleDownloadWord();
            }}>
              下载word报告
            </Button>
      </div> */}
        {/* <div className='mb-2'>以下评审报告根据模板自动生成供你参考，你可以在线修改。</div>
      <ReactQuill
        theme="snow"
         readOnly={dialogTitletext === '查看'}
        value={content}
        onChange={setContent}
        modules={modules}
        style={quillReadOnlyStyle}
        formats={formats}/> */}

        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          readonly={true}
          // onFinish={handleSave}
        >
          <EProFormUploadButton
            name="attachmentIds"
            label="评审报告（电子版）:"
            labelCol={{ flex: 0.032 }}
            max={1}
            readonly={true}
            defaultExtraTextHide={true}
            onChange={onChange1}
          />
          <EProFormUploadButton
            name="attachmentIds1"
            label="评审报告（签字版）:"
            labelCol={{ flex: 0.032 }}
            max={1}
            readonly={true}
            defaultExtraTextHide={true}
            onChange={onChange2}
          />
        </ProForm>
      </BlockContainer>
      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <FileViewByStreamByUrl fileUrl1={fileUrl} isPreview />
      </Modal>
    </div>
  );
};

export default Task;
