/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Table,
} from 'antd';
import {
  getExpertiseExpertArea,
  getGroupList,
  gradeConfigLevelNotSets,
} from '@/api/apply';
import { gradeConfigAllSet } from '@/api/apply';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import { values } from 'lodash';
import { cloneDeep } from 'lodash';
import { log } from 'mathjs';
import BlockContainer from '@/components/BlockContainer';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import EditTool from './EditTool';

const { RangePicker } = DatePicker;

const CheckboxGroup = Checkbox.Group;

type TEditProps = {
  loading?: boolean;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dataStatus?: any;
  id?: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const TableSelect: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  detailInfo,
  dataStatus,
  onRef,
  mode,
  id,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      getDate: () => {
        return reviewResult;
      },

      getDateId: () => {
        return reviewSetId;
      },

      getFinalSetId: () => {
        return finalSetId;
      },

      getFieldValue: () => {
        let formInfo = cloneDeep(formRef3.current?.getFieldsValue());
        console.log(formInfo);
        return formInfo;
      },
    };
  });
  const [form] = Form.useForm();

  // 表单实例
  const formRef = useRef<FormInstance>(null);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [selectOption, setSelectOption] = useState<any[]>([]);

  const [selectedRows1, setSelectedRows] = useState<any[]>([]);

  const [plainOptions, setPlainOptions] = useState<any[]>([]);

  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [gradeList, setGradeList] = useState<any>([]);

  const formRef2 = useRef<any>(null);

  const [resultOptions, setResultOptions] = useState<any[]>([]);

  const [docComplementDate, setDocComplementDate] = useState<any>('');

  const [grounpConfigData, setGrounpConfigData] = useState<any>({});

  const [grounpConfigReturnData, setGrounpConfigReturnData] = useState<any>({});

  const [openDetail1, setOpenDetail1] = useState<boolean>(false);

  const [result, setResult] = useState<any>('');

  const [reviewResult, setReviewResult] = useState<any>('');
  const [reviewSetId, setReviewSetId] = useState<any>('');

  const [finalSetId, setFinalSetId] = useState<any>('');

  const formRef3 = useRef<FormInstance>(null);
  const [problemAuditResult, setProblemAuditResult] = useState<any>('');

  const [adviceAuditResult, setAdviceAuditResult] = useState<any>('');

  const resultOptions1 = [
    {
      label: '合格',
      value: '1',
    },
    {
      label: '不合格',
      value: '0',
    },
  ];

  useEffect(() => {
    gradeConfigData();
  }, []);
  const gradeConfigData = async () => {
    try {
      const { code, data, msg } = await gradeConfigAllSet();

      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setGradeList(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getExpertiseExpertAreaData();
    getGroupListData();
  }, []);
  useEffect(() => {
    console.log(detailInfo, 'detailInfo');

    if (detailInfo?.docReviews.length > 0) {
      setDataSource(detailInfo.docReviews);
      setDataSource1(detailInfo.fieldReviews);
    } else {
      setDataSource([]);
      setDataSource1([]);
    }

    gradeConfigLevelData(detailInfo?.id);

    // console.log(detailInfo && detailInfo.reviewFile && detailInfo.reviewFile?.length > 0, 777777777, detailInfo?.reviewFile);

    // console.log(JSON.parse(detailInfo.reviewFile));

    if (
      detailInfo &&
      detailInfo.reviewFile &&
      detailInfo.reviewFile?.length > 0 &&
      Array.isArray(JSON.parse(detailInfo.reviewFile))
    ) {
      console.log(JSON.parse(detailInfo.reviewFile));

      let reviewFile = JSON.parse(detailInfo.reviewFile);

      reviewFile = reviewFile.map((item: any) => {
        return {
          uid: item.id,
          name: item.name,
          status: 'done',
          type: 'application/msword',
          url: item.url,
          response: {
            code: 200,
            data: {
              fileName: item.name,
              ossId: item.id,
              url: item.url,
            },
          },
        };
      });

      console.log(reviewFile);

      if (formRef3.current) {
        formRef3.current?.setFieldsValue({ attachmentIds: reviewFile });
      }
    }

    if (formRef2.current) {
      formRef2.current?.setFieldsValue({
        reviewResult: detailInfo?.reviewResult,
      });
      formRef2.current?.setFieldsValue({
        reviewSetId: detailInfo?.reviewSetId,
      });
      formRef2.current?.setFieldsValue({ finalSetId: detailInfo?.finalSetId });
    }

    setReviewResult(detailInfo?.reviewResult);
    setReviewSetId(detailInfo?.reviewSetId);
  }, [detailInfo]);
  useEffect(() => {
    setGrounpConfigReturnData({
      ...grounpConfigData,
      members: dataSource,
    });
  }, [grounpConfigData, dataSource]);

  const getExpertiseExpertAreaData = async () => {
    try {
      const { code, data, msg } = await getExpertiseExpertArea();
      const gradeListData = data.map((item: any) => {
        return {
          label: item.dictLabel,
          value: item.dictValue,
        };
      });
      setPlainOptions(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const getGroupListData = async () => {
    try {
      const { code, data, msg } = await getGroupList({});
      const gradeListData = data.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      setSelectOption(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const gradeConfigLevelData = async (id: any) => {
    try {
      const { code, data, msg } = await gradeConfigLevelNotSets(id);
      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setResultOptions(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const columns: any = [
    {
      title: '专家姓名',
      dataIndex: 'auditName',
      hideInSearch: true,
    },
    {
      title: '文审结论',
      dataIndex: 'docAuditResult',
      hideInSearch: true,

      // render: (_:any,record:any,index:any) =>{

      //   if(record?.docAuditResult === '1'){
      //     return (
      //       <div>符合</div>
      //     )
      //   } else if(record?.docAuditResult === '0'){
      //     return (
      //       <div>不符合</div>
      //     )
      //   } else {
      //         return (
      //       <div>-</div>
      //     )
      //   }
      // },
    },

    {
      title: '文审提交状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (text: any, record: any, _: any, action: any) => {
        if (record.status === 1) {
          return <div>已提交</div>;
        }
        if (record.status === 0) {
          return (
            <div>
              <a className="text-red-500">未提交</a>
            </div>
          );
        }
      },
    },
  ];

  const columns1: any = [
    {
      title: '专家姓名',
      dataIndex: 'auditName',
      hideInSearch: true,
    },
    {
      title: '文审结专家总结（发现的问题和工作建议）',
      dataIndex: 'docAuditResult',
      hideInSearch: true,

      render: (text: any, record: any, _: any, action: any) => {
        return (
          <div
            className="link-btn"
            onClick={() => {
              setProblemAuditResult(record.problem);
              setAdviceAuditResult(record.advice);
              setOpenDetail1(true);
            }}
          >
            专家总结内容展示，点击可以查看全部内容
          </div>
        );
      },
    },

    {
      title: '现场评审提交状态',
      dataIndex: 'status',
      hideInSearch: true,

      render: (text: any, record: any, _: any, action: any) => {
        if (record.status === 1) {
          return <div>已提交</div>;
        }
        if (record.status === 0) {
          return (
            <div>
              <a className="text-red-500">未提交</a>
            </div>
          );
        }
      },
    },
  ];

  const rowSelection: any = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRows(selectedRows);
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows
      );
    },
    getCheckboxProps: (record: any) => ({
      name: record.name,
    }),
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 根据 setId 查找对应的中文标签
  const getGradeLabel = () => {
    const setId = detailInfo?.setId;
    console.log(setId, gradeList);
    if (setId && gradeList.length > 0) {
      const item = gradeList.find((grade: any) => grade.value === setId);
      return item ? item.label : '';
    }
    return '';
  };

  const formRef13 = useRef<any>(null);

  return (
    <>
      <BlockContainer title="文审结果">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          readonly={dialogTitletext == '查看'}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          <Table dataSource={dataSource} columns={columns} pagination={false} />
        </ProForm>
      </BlockContainer>

      <BlockContainer className="mt-5" title="现场评审">
        <Table dataSource={dataSource1} columns={columns1} pagination={false} />
      </BlockContainer>

      {/* <BlockContainer   className='mt-5' title="评审报告">
        <div className='btn-wraper-1'>
           <Button  className='mr-2' onClick={() => {
            }} loading={loading} >
            预览报告
          </Button>
          <Button   className='mr-2' onClick={() => {
            
            }}  loading={loading}> 
            下载pdf报告
          </Button>

          <Button  loading={loading} type="primary"
          onClick={() => {
           }}
          >
            下载word报告
          </Button>
        </div>
      </BlockContainer> */}

      {(dataStatus == 'fieldReviewResultAwait' ||
        dataStatus == 'fieldReviewResult' ||
        dataStatus == 'resultConfirm') && (
        <div className="mt-4">
          <EditTool
            dialogTitletext={dialogTitletext}
            baseInfo={detailInfo}
            onRef={formRef13}
            id={id}
          />
        </div>
      )}

      <BlockContainer className="mt-5" title="评审定级">
        <Form
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 8 }}
          layout="horizontal"
          ref={formRef2}
          readonly={dialogTitletext == '查看'}
          style={{ maxWidth: 600 }}
        >
          <Row>
            <Col span={12}>
              <Form.Item label="自评得分:" name="passRemark">
                {' '}
                <span className="orange-word">
                  {detailInfo?.selfScore || 0}
                </span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="专家评分:" name="finalScore">
                <span className="blue-word">{detailInfo?.finalScore || 0}</span>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item label="申请类型:" name="type">
                {detailInfo?.type === 1 && <span>初次评审</span>}
                {detailInfo?.type === 2 && <span>等级复核</span>}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="申请等级:" name="setId">
                {' '}
                {getGradeLabel() || '-'}
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="评审结果:"
            className="form-list"
            rules={[{ required: true, message: '请选择评审结果' }]}
            name="reviewResult"
          >
            <Select
              disabled={dialogTitletext == '查看'}
              size="small"
              onChange={(e: any) => {
                setReviewResult(e);
                formRef2.current?.setFieldsValue({ reviewResult: e });
              }}
              value={reviewResult}
              style={{ width: '100%' }}
              options={resultOptions1}
            />
          </Form.Item>
          <Form.Item
            label="评级建议:"
            className="form-list"
            rules={[{ required: true, message: '请选择评级建议' }]}
            name="reviewSetId"
          >
            <Select
              disabled={reviewResult == '0' || dialogTitletext == '查看'}
              size="small"
              onChange={(e: any) => {
                setReviewSetId(e);
                formRef2.current?.setFieldsValue({ reviewSetId: e });
              }}
              value={reviewSetId}
              style={{ width: '100%' }}
              options={resultOptions}
            />
          </Form.Item>
          {(dataStatus === 'resultConfirm' ||
            dataStatus === 'fieldReviewResult') && (
            <Form.Item
              label="最终定级:"
              className="form-list"
              rules={[{ required: true, message: '请选择最终定级' }]}
              name="finalSetId"
            >
              <Select
                size="small"
                disabled={dialogTitletext == '查看'}
                onChange={(e: any) => {
                  setFinalSetId(e);
                  formRef2.current?.setFieldsValue({ finalSetId: e });
                }}
                value={finalSetId}
                style={{ width: '100%' }}
                options={resultOptions}
              />
            </Form.Item>
          )}
        </Form>
      </BlockContainer>
      {(dataStatus === 'resultConfirm' ||
        dataStatus === 'fieldReviewResult') && (
        <BlockContainer className="mt-5" title="上会结论附件">
          <ProForm<{ table: Any[] }>
            formRef={formRef3}
            initialValues={{ table: [] }}
            submitter={false}
            readonly={dialogTitletext == '查看'}
            // onFinish={handleSave}
          >
            <EProFormUploadButton
              name="attachmentIds"
              label=""
              labelCol={{ flex: 0.032 }}
              max={10}
              defaultExtraTextHide={true}
            />
          </ProForm>
        </BlockContainer>
      )}
      <Modal
        title="详情"
        open={openDetail1}
        footer={null}
        onCancel={() => {
          setOpenDetail1(false);
        }}
        width={600}
      >
        <div className="p-4">
          <div className="detail-title">发现问题：</div>
          <div className="mb-2">{problemAuditResult}</div>
          <div className="detail-title">工作建议：</div>
          <div className="mb-2">{adviceAuditResult}</div>
        </div>
      </Modal>
    </>
  );
};

export default TableSelect;
