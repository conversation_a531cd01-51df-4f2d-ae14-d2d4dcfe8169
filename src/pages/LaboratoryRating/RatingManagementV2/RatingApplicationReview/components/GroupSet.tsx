/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, message, Modal, Table, Typography } from 'antd';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import CheckboxWithOther from '@/components/CheckboxWithOther';
import RequiredTag from '@/components/RequiredTag';
import ReceiveFormPitchAcceptDialog from './ReceiveFormPitchAcceptDialog';
import UploadList from './UploadList';

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  onRef,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [isEdit, setIsEdit] = useState<boolean>(mode === 'edit');

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [sampleNum, setSampleNum] = useState<any>(0);
  const [sourceTotal, setSourceTotal] = useState<any>(0);
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const formRef2 = useRef<any>(null);

  // 计算 rowSpan 的函数
  const calculateRowSpan = (data: any[]) => {
    const result = [...data];
    for (let i = 0; i < result.length; i++) {
      if (i > 0 && result[i].sampleCode === result[i - 1].sampleCode) {
        result[i].sampleCodeRowSpan = 0;
        continue;
      }
      let rowSpan = 1;
      for (let j = i + 1; j < result.length; j++) {
        if (result[j].sampleCode === result[i].sampleCode) {
          result[j].sampleCodeRowSpan = 0;
          rowSpan++;
        } else {
          break;
        }
      }
      result[i].sampleCodeRowSpan = rowSpan;
    }
    return result;
  };

  useEffect(() => {
    const initialData = [
      {
        sampleCode: '123',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 1,
      },
      {
        sampleCode: '123',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 2,
      },
      {
        sampleCode: '123',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 3,
      },
      {
        sampleCode: '12322',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 4,
      },
      {
        sampleCode: '12322',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 5,
      },
      {
        sampleCode: '12322',
        receiptUnitName: 123,
        receiptDate: 0,
        sampleStatus: '123',
        id: 6,
      },
    ];
    const processedData = calculateRowSpan(initialData);
    setDataSource(processedData);
    setDataSource1(processedData);
    if (dialogTitletext !== '查看') {
      setEditableRowKeys([1, 2, 3, 4, 5, 6]);
      setEditableRowKeys1([1, 2, 3, 4, 5, 6]);
    }
  }, [dataSource, dataSource1]);

  const columns: ProColumns<Any>[] = [
    {
      title: '二级类别',
      dataIndex: 'sampleCode',
      readonly: true,
    },
    {
      title: '指标名称',
      dataIndex: 'receiptUnitName',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'receiptDate',
      // readonly: true,
    },
    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: () => (
        <CloudUploadOutlined
          className="upload-icon"
          onClick={() => {
            setOpenPreview(true);
          }}
        />
      ),
      render: (value) => (
        <CloudUploadOutlined
          className="upload-icon"
          onClick={() => {
            setOpenPreview(true);
          }}
        />
      ),
    },
  ];

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计

  return (
    <>
      <BlockContainer title="指标明细">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          <EditableProTable<Any>
            rowKey="id"
            name="table"
            editableFormRef={tableRef}
            loading={loading}
            request={async () => ({
              data: dataSource,
              total: dataSource?.length,
              success: true,
            })}
            scroll={{ x: 960 }}
            recordCreatorProps={false}
            columns={columns}
            editable={{
              type: 'multiple',
              editableKeys,
            }}
          />
          <div className="total-num">
            <div className="num-box">合计: </div>
            <div className="num-box">指标名称总分：{sampleNum}</div>
            <div className="num-box">自评得分：{sourceTotal}</div>
          </div>

          <EditableProTable<Any>
            rowKey="id"
            name="table"
            editableFormRef={tableRef1}
            request={async () => ({
              data: dataSource1,
              total: dataSource1?.length,
              success: true,
            })}
            scroll={{ x: 960 }}
            recordCreatorProps={false}
            columns={columns}
            editable={{
              type: 'multiple',
              editableKeys: editableKeys1,
            }}
          />
          <div className="total-num">
            <div className="num-box">合计: </div>
            <div className="num-box">指标名称总分：{sampleNum}</div>
            <div className="num-box">自评得分：{sourceTotal}</div>
          </div>

          <div className="total-num total-num-1">
            <div className="num-box">自评总得分: </div>
            <div className="num-box">{sampleNum}</div>
            <Button
              type="link"
              size="small"
              onClick={() => {
                setOpenDetail(true);
              }}
            >
              查看等级划分
            </Button>
          </div>
        </ProForm>
      </BlockContainer>
      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <UploadList
          dialogTitletext={dialogTitletext}
          loading={loading}
          onRef={formRef2}
          mode={mode}
        />
      </Modal>

      {/* 录入查找样本编号 */}
      <Modal
        title="等级划分"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={450}
      >
        <div className="p-4">
          <div>一级：≥60分，且＜70分；</div>
          <div>二级乙等：≥70分，且＜80分；</div>
          <div>二级甲等：≥80分，且＜85分；</div>
          <div>三级乙等：≥85分，且＜95分；</div>
          <div>三级甲等：≥95分。</div>
        </div>
      </Modal>
    </>
  );
};

export default ReceiveForm;
