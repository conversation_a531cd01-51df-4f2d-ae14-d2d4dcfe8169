/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  Checkbox,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Spin,
  Table,
} from 'antd';
import {
  getExpertGroup,
  getExpertiseExpertArea,
  getGroupList,
  randomGroup,
} from '@/api/apply';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import BlockContainer from '@/components/BlockContainer';

const CheckboxGroup = Checkbox.Group;

type TEditProps = {
  loading?: boolean;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const TableSelect: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  detailInfo,
  onRef,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      getDataConfig: () => {
        return grounpConfigReturnData;
      },
    };
  });
  const [form] = Form.useForm();

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [openRandomDetail, setOenRandomDetail] = useState<boolean>(false);

  const [tableShow, setTableShow] = useState<boolean>(true);
  const [tableShow1, setTableShow1] = useState<boolean>(true);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [selectOption, setSelectOption] = useState<any[]>([]);

  const [selectedRows1, setSelectedRows] = useState<any[]>([]);

  const [plainOptions, setPlainOptions] = useState<any[]>([]);

  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);

  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [grounpData, setGrounpData] = useState<any[]>([]);

  const formRef2 = useRef<any>(null);

  const [grounpConfigData, setGrounpConfigData] = useState<any>({});

  const [grounpConfigReturnData, setGrounpConfigReturnData] = useState<any>({});

  useEffect(() => {
    getExpertiseExpertAreaData();
    getGroupListData();
  }, []);
  useEffect(() => {
    if (detailInfo?.group?.members?.length > 0) {
      setDataSource(detailInfo?.group?.members);
    } else {
      setDataSource([]);
      setDataSource1([]);
    }
  }, [detailInfo]);
  useEffect(() => {
    setGrounpConfigReturnData({
      ...grounpConfigData,
      members: dataSource,
    });
  }, [grounpConfigData, dataSource]);

  const getExpertiseExpertAreaData = async () => {
    try {
      const { code, data, msg } = await getExpertiseExpertArea();
      console.log(data, 'msg');

      const gradeListData = data.map((item: any) => {
        return {
          label: item.dictLabel,
          value: item.dictValue,
        };
      });
      setPlainOptions(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const getGroupListData = async () => {
    try {
      const { code, data, msg } = await getGroupList({});
      const gradeListData = data.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      setSelectOption(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const getExpertGroupData = async (id: any) => {
    try {
      const { code, data, msg } = await getExpertGroup(id);
      setGrounpData(data?.members);
      setGrounpConfigData(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const columns: any = [
    // {
    //   dataIndex: 'index',
    //   width: 48,
    //   title: '序号',
    // },
    {
      title: '专家姓名',
      dataIndex: 'name',
      hideInSearch: true,
      render: (text: any, record: any, _: any) => {
        if (record.leaderFlag === 1) {
          return <span>{record.name}(组长)</span>; // 红色字体
        } else {
          return <span>{record.name}</span>; // 红色字体; // 正常显示
        }
      },
    },
    {
      title: '专家领域',
      dataIndex: 'professionalFieldCh',
      hideInSearch: true,
    },
  ];

  //选择小组表格渲染
  const columns1: any = [
    {
      title: '专家姓名',
      dataIndex: 'name',
      render: (text: any, record: any, _: any) => {
        if (record.leaderFlag === 1) {
          return <span>{record.name}(组长)</span>; // 红色字体
        } else {
          return <span>{record.name}</span>; // 红色字体; // 正常显示
        }
      },
    },
    {
      title: '所属单位',
      dataIndex: 'deptName',
    },
    {
      title: '专家领域',
      dataIndex: 'professionalFieldCh',
    },
  ];

  const columns2: any = [
    {
      title: '专家姓名',
      dataIndex: 'name',
      readonly: true,
    },
    {
      title: '设置组长',
      dataIndex: 'leaderFlag',
      renderFormItem: (text: any, record: any, _: any, action: any) => (
        <Checkbox
          checked={record.record.leaderFlag}
          onChange={(e) => setLeader(e, record.record)}
        />
      ),
    },
    {
      title: '所属单位',
      dataIndex: 'deptName',
      readonly: true,
    },

    {
      title: '专业领域',
      dataIndex: 'professionalFieldCh',
      readonly: true,
    },
  ];

  const rowSelection: any = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRows(selectedRows);
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows
      );
    },
    getCheckboxProps: (record: any) => ({
      name: record.name,
    }),
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 选择
  const onChange = (value: any) => {
    console.log(`selected ${value}`);
  };

  // 选择小组
  const onChangeGroppUsername = (value: any) => {
    console.log(`selected ${value}`);
    if (value) {
      getExpertGroupData(value);
    }
  };
  const selectGroupData = () => {
    if (grounpData.length > 0) {
      setDataSource(grounpData);
      setOpenDetail(false);
    } else {
      message.error('请选择至少一个专家');
    }
  };

  const randomSelectGroupData = async () => {
    form
      .validateFields()
      .then((values: any) => {
        console.log(11111);
        const vaule = form.getFieldsValue();

        const params = {
          ...vaule,
          labId: detailInfo.labId,
        };
        if (params.exclude) {
          params.exclude = 1;
        } else {
          params.exclude = 0;
        }
        console.log(params, 'params');

        randomGroup(params).then((res: any) => {
          console.log(res, 'res');
          setTableShow1(false);
          const list = res.data.members.map((item: any) => {
            return {
              ...item,
              leaderFlag: false,
              id: item.userId,
            };
          });
          setGrounpConfigData(res.data);
          const ids = list.map((item: any) => item.id);
          setEditableRowKeys1(ids);
          setDataSource1(list);
          setTimeout(() => {
            setTableShow1(true);
          });
        });
      })
      .catch((error: any) => {
        console.log('表单校验失败', error);
      });
    console.log(form, 'form');
  };

  const randomSelectGroupData1 = () => {
    //判断dataSource1里面其中一个为true 其他为false
    const leaderFlag = dataSource1.some((item: any) => item.leaderFlag);
    if (leaderFlag) {
      setDataSource(dataSource1);
      setOenRandomDetail(false);
    } else {
      message.error('请设置组长');
    }
  };

  // 设置组长,选中当前行 其他行取消选中
  const setLeader = (value: any, row: any) => {
    if (row) {
      const updatedRows = dataSource1.map((row1: any) => {
        if (row1.id === row.id) {
          return { ...row1, leaderFlag: true }; // 选中的行设置为组长
        } else {
          return { ...row1, leaderFlag: false }; // 其他行取消选中
        }
      });
      setTableShow1(false);
      setDataSource1(updatedRows);
      setTimeout(() => {
        setTableShow1(true);
      });
    }
  };

  return (
    <>
      <BlockContainer title="评审小组">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          {dialogTitletext !== '查看' && (
            <div className="btn-wraper">
              <Button
                type="primary"
                className="mr-2"
                onClick={() => setOpenDetail(true)}
              >
                选择
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  setDataSource1([]);
                  setOenRandomDetail(true);
                }}
              >
                随机生成
              </Button>
            </div>
          )}

          {tableShow && (
            <Table
              dataSource={dataSource}
              columns={columns}
              pagination={false}
            />
          )}
        </ProForm>
      </BlockContainer>

      <Modal
        title="评审小组选择"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={800}
      >
        <div className="p-4">
          <Form
            name="basic"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 600 }}
          >
            <Form.Item<any> label="小组名称" name="username">
              <Select onChange={onChangeGroppUsername} options={selectOption} />
            </Form.Item>
          </Form>

          <Table
            // rowSelection={{ type: 'checkbox', ...rowSelection }}
            columns={columns1}
            rowKey="id"
            dataSource={grounpData}
          />
          <div className="flex justify-end space-x-2">
            <Button type="primary" onClick={() => selectGroupData()}>
              确认选择
            </Button>
            <Button onClick={() => setOpenDetail(false)}>取消</Button>
          </div>
        </div>
      </Modal>

      <Modal
        title="随机生成评审小组"
        open={openRandomDetail}
        footer={null}
        onCancel={() => {
          setOenRandomDetail(false);
        }}
        width={1200}
      >
        <div className="p-4">
          <Form
            name="basic2"
            form={form}
            formRef={formRef2}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 600 }}
          >
            <Form.Item<any>
              label="专家数量"
              name="num"
              className="num-wraper"
              rules={[
                { required: true, message: '请输入专家数量' },
                {
                  type: 'number',
                  min: 5,
                  max: 9,
                  message: '专家数量必须在 5 - 9 之间!',
                },
              ]}
            >
              <InputNumber
                className="num-input"
                min={5}
                placeholder="专家数量必须在 5 - 9 之间!"
                max={9}
              />
            </Form.Item>
            {/* <div className='tipsName'>请输入数字5-9</div> */}
            <Form.Item<any>
              label=" 排除规则"
              name="exclude"
              valuePropName="checked"
              // rules={[{ required: true, message: '请选择排除规则!' }]}
            >
              <Checkbox value={1}>自动排除在本申请机构任职专家</Checkbox>
            </Form.Item>

            <Form.Item<any> label=" 必须包含专业领域" name="professionalFields">
              <CheckboxGroup options={plainOptions} />
            </Form.Item>
            <Form.Item<any>
              label="小组名称"
              name="name"
              rules={[{ required: true, message: '请输入小组名称!' }]}
            >
              <Input />
            </Form.Item>
          </Form>
          {tableShow1 && (
            <EditableProTable<Any>
              rowKey="id"
              name="table"
              editableFormRef={tableRef1}
              request={async () => ({
                data: dataSource1,
                total: dataSource1?.length,
                success: true,
              })}
              scroll={{ x: 960 }}
              recordCreatorProps={false}
              columns={columns2}
              editable={{
                type: 'multiple',
                editableKeys: editableKeys1,
              }}
              // 添加 key 属性，确保能正确识别数据变化
              key={dataSource1.length}
            />
          )}
          {!tableShow1 && (
            <Spin
              tip="加载中"
              size="large"
              className="mt-[40vh] w-100 loading-warper"
            >
              {/* <div className="content" /> */}
            </Spin>
          )}
          <div className="flex justify-end space-x-2">
            <Button type="primary" onClick={() => randomSelectGroupData()}>
              随机生成
            </Button>
            {dataSource1?.length > 0 && (
              <Button onClick={() => randomSelectGroupData1()}>确定使用</Button>
            )}
            <Button onClick={() => setOenRandomDetail(false)}>取消</Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default TableSelect;
