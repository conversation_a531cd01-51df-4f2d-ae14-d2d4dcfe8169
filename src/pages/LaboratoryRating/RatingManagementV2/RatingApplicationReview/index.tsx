/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Alert, Button, Drawer, Form, message, Modal, Input,Rate} from 'antd';

import {
  getGradeReviewList, getGradeConfig, gradeConfigAllSet,
} from '@/api/apply';

import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import './index.less';
import img5 from '@/assets/process.png';
const { TextArea } = Input;
export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();

  const [messageApi, contextHolder] = message.useMessage();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();

  const [pageSize, setPageSize] = useState<number>(10);

  // 接收
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const [isAdd, setIsAdd] = useState<boolean>(false);

  

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');


  const [dialogTitletext, setDialogTitletext] = useState<string>('新增');


  // 数据状态
  const [dataStatus, setSataStatus] = useState<string>('');




  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  // 是否已上传回执单
  const [isUploadReceipt, setIsUploadReceipt] = useState<boolean>(false);
  // 是否打开上传回执单询问 Modal
  const [openUploadReceiptModal, setOpenUploadReceiptModal] =
    useState<boolean>(false);

  // 录入样品编号相关状态
  const [openInputSampleCodeModal, setOpenInputSampleCodeModal] =
    useState<boolean>(false);

  //评价弹窗是否显示设置
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);

  const [sampleCode, setSampleCode] = useState<string>('');
  const [searchResult, setSearchResult] = useState<Array<QualityTaskItem>>([]);
  const [searchError, setSearchError] = useState<string>('');
  const [showSearchResult, setShowSearchResult] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<Record<string, any>>({});
 

  const [activeKey, setActiveKey] = useState('0');
  const [stauteList, setStauteList] = useState<any>([]);

  const [gradeList, setGradeList] = useState<any>([]);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index', 
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '进度状态',
      dataIndex: 'status',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: stauteList,
      },
      render: (text, record, _, action) => {
        if(record.status === 'draft'){
          return (
              <div>草稿</div>
          )
        }
        if(record.status === 'initialReview'){
          return (
              <div>待初审</div>
          )
        }
        if(record.status === 'docReview'){
          return (
              <div>待文审</div>
          )
        }
        if(record.status === 'initialReviewBack'){
          return (
              <div>初审退回</div>
          )
        }
        if(record.status === 'initialReviewRejected'){
          return (
              <div>初审不通过</div>
          )
        }
        if(record.status === 'docReviewBack'){
          return (
              <div>文审退回</div>
          )
        }
        if(record.status === 'docReviewRejected'){
          return (
              <div>文审不通过</div>
          )
        }
        if(record.status === 'fieldReviewAwait'){
          return (
              <div>现场评审待排期</div>
          )
        }
        if(record.status === 'fieldReview'){
          return (
              <div>待现场评审</div>
          )
        }
        if(record.status === 'fieldReviewResultAwait'){
          return (
              <div>待出评审结果</div>
          )
        }
        if(record.status === 'fieldReviewResult'){
          return (
              <div>已最终定级</div>
          )
        }
        if(record.status === 'fieldStop'){
          return (
              <div>现场评审终止</div>
          )
        }
        if(record.status === 'timeoutStop'){
          return (
              <div>超时自动终止</div>
          )
        }

        if(record.status === 'resultConfirm'){
          return (
              <div>待最终定级</div>
          )
        }
      },
    },
    {
      title: '申请编号',
      dataIndex: 'code',
      render: (text, record, _, action) => {
        return (
          <div className='link-btn' 
          onClick={() => {
              if(record.status === 'initialReview'){
                setSataStatus('initialReview')
              } else if(record.status === 'fieldReviewAwait'){
                setSataStatus('fieldReviewAwait')
              }else if(record.status === 'fieldReviewResultAwait'){
              setDialogTitletext('现场评审完成 - 定级'); 
            } else if(record.status === 'resultConfirm'){
              setDialogTitletext('最终定级'); 
            } 
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
          }}
          >{record.code}</div>
        )
      },

    },

    {
      title: '申请机构',
      dataIndex: 'labName',
      disable: true,
    },
    {
      disable: true,
      title: '申请等级',
      dataIndex: 'setId',
          valueType: 'select',
      fieldProps: {
        options: gradeList,
      },
    },
    {
      disable: true,
      title: '自评得分',
      hideInSearch: true,
      dataIndex: 'selfScore',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交日期',
      dataIndex: 'submitDate',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交人',
      dataIndex: 'submitter',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '办理日期',
      dataIndex: 'processDate',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        const btns = [
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              setSataStatus(record.status)
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>
        ];
        const btns3 = [
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              setSataStatus(record.status)
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>,
          <Button
          type="link"
          size="small"
          key="edit"
          onClick={() => {
            if(record.status === 'initialReview'){
              setDialogTitletext('评审办公室初审');
            } else if(record.status === 'fieldReviewAwait'){
              setDialogTitletext('文审合格-现场评审排期');
            } else if(record.status === 'fieldReviewResultAwait'){
              setDialogTitletext(' 现场评审结果确认'); 
            } else if(record.status === 'resultConfirm'){
              setDialogTitletext(' 最终定级'); 
            } 
            setSataStatus(record.status)
            setDetailId(record.id);
            setOpenEdit(true);
          }}
          >
          办理
          </Button>,
        ];
        if(activeKey==='1'){
          return btns 
        } else if(activeKey==='0'){
          return btns3
        }
      },
    },
  ];

  useEffect(() => {
    tableReload();
  }, [activeKey]);


  useEffect(() => {
    getGradeConfigData();
    gradeConfigData();
  }, []);
  const gradeConfigData = async () => {
    try {
      const { code, data, msg } = await gradeConfigAllSet();
      
      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setGradeList(gradeListData)
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }

  }
  const getGradeConfigData = async () => {
    try {
      const { code, data, msg } = await getGradeConfig();
      const stauteListData = data.map((item: any) => {
        return {
          label: item.name,
          value: item.statusId,
        };
      });
      setStauteList(stauteListData)
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  }


  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    setParamsCache('refreshCount');
    tableReload();
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    // 重置分页状态
    // actionRef.current?.reset?.();
    // 然后重新加载数据
    actionRef.current?.reload();
  };



  useEffect(() => {
    tableReload();
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);





  // 添加useEffect监听Modal打开状态变化
  useEffect(() => {
    if (openInputSampleCodeModal) {
      setSampleCode('');
      setSearchResult([]);
      setSearchError('');
      setShowSearchResult(false);
      setSelectedTask({});
    }
  }, [openInputSampleCodeModal]);

  return (
    <>
      {contextHolder}
      <PageContainer>
        <ProTable<QualityTaskItem>
          columns={columns}
          actionRef={actionRef}
          // key={activeKey}
      
          toolbar={{
            menu: {
              type: 'tab',
              activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待办理</span>,
                  // 统计功能先不要了（{counts[0]}）
                },
                {
                  key: '1',
                  label: <span>已办理</span>,
                  //  统计功能先不要了（{counts[1]}）
                },
              ],
              onChange: (key) => setActiveKey(key as string),
            },
          }}

          cardBordered
          bordered
          request={async (params, sort, filter) => {
             await waitTime(100);
                        const param = {
                          ...params,
                          pageNum: params.current || 1,
                          pageSize: params.pageSize || pageSize,
                          current: undefined,
                          processed: activeKey,
                        };
                        const resDATA = await getGradeReviewList(param);
                        const { code, rows, total, msg } = resDATA.data;
            
                        console.log(resDATA, "rows");
                        
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          messageApi.error(msg);
                        }
            
                        // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
                        const paramsStr = JSON.stringify({
                          ...params,
                          current: undefined,
                          pageSize: undefined,
                        });
                        if (paramsCache !== paramsStr) {
                          setParamsCache(paramsStr);
                          const { total: totalAnother } = await getGradeReviewList({
                            ...params,
                            pageNum: 1,
                            pageSize: 0,
                            current: undefined,
                            processed: activeKey,
                          });
                        }
            
                        return {
                          data: rows ?? [],
                          total: total ?? 0,
                          success: true,
                        };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 120,
          }}
          form={{
            syncToUrl: false,
            resetOnTabChange: true,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            defaultPageSize: 10,
            defaultCurrent: 1,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle=""
        />


        {/* 新增,详情，编辑 */}
        <Drawer
          width="90%"
          title={'评级申请 - '+dialogTitletext}
          onClose={() =>
            closeEdit() 
          }
          open={openEdit}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Edit
            close={() =>
              closeEdit() 
            }
            detailId={detailId}
            dataStatus={dataStatus}
            dialogTitletext={dialogTitletext}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={isAdd === false ? 'edit' : 'view'}
          />
        </Drawer>

        <Modal
          title="操作提示"
          centered
          width={360}
          open={openUploadReceiptModal}
          okText="继续上传回执单"
          cancelText="不需要上传回执单"
          onOk={() => {
            setOpenUploadReceiptModal(false);
          }}
          onCancel={() => {
            setOpenUploadReceiptModal(false);
            closeEdit();
          }}
          zIndex={9999}
          maskClosable={false}
        >
          <div>请再次确认本任务是否需要上传回执单</div>
        </Modal>
        {/* 录入查找样本编号 */}
        <Modal
          title="实验室等级评审流程"
          open={openInputSampleCodeModal}
          footer={null}
          onCancel={() => {
            setOpenInputSampleCodeModal(false);
          }}
          width={800}
        >
            <div className="p-4">
      
            <img alt="" src={img5} className="w-100 img-class" />
              <div className="flex justify-end space-x-2">
                <Button onClick={() => setOpenInputSampleCodeModal(false)}>
                  知道了
                </Button>
              </div>
            </div>
        </Modal>
        {/* 评价弹窗 */}
        <Modal
          title="满意度评价"
          open={openConfirmModal}
          footer={null}
          onCancel={() => {
            setOpenConfirmModal(false);
          }}
          width={600}
        >
         <Form
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 14 }}
        layout="horizontal"
        style={{ maxWidth: 600 }}
      >
       <Form.Item label="专业性评价:">
          <Rate />
        </Form.Item>
        <Form.Item label="客观公正性评价:">
          <Rate />
        </Form.Item>
        <Form.Item label="其他建议或意见:">
          <TextArea rows={4} />
        </Form.Item>
        </Form>
        <div className='btn-waraper'>
          <Button type="primary">保存</Button>
        </div>
        </Modal>
      </PageContainer>
    </>
  );
};
export default QualityTask;
