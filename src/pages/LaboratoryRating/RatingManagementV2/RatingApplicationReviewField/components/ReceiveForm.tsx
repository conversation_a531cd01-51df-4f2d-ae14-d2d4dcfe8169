/* eslint-disable eqeqeq */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, InputNumber, message, Modal, Select } from 'antd';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import BlockContainer from '@/components/BlockContainer';
import UploadList from './UploadList';

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  detailInfo?: any;
  staute?: any;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  detailInfo,
  onRef,
  staute,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: (status: any) => {
        const flattenedArray = submitData.flat();
        if (status === 'submit') {
          console.log(flattenedArray, 'flattenedArray', dataSourceArray);
          for (let index = 0; index < flattenedArray.length; index++) {
            console.log(
              flattenedArray[index].finalScore,
              'finalScore',
              !flattenedArray[index].finalScore
            );
            // if(flattenedArray[index].fieldAuditResult!=0 && flattenedArray[index].fieldAuditResult!=1) {
            //   message.error('请完善现场评审结论');
            //   return false;
            // }
            if (
              !flattenedArray[index].finalScore &&
              isNaN(flattenedArray[index].finalScore)
            ) {
              message.error('请完善专家评分');
              return false;
            }
          }
        }
        const updatedIdicesList = idicesList.map((mainItem: any) => {
          const newMainItem = { ...mainItem };
          if (newMainItem.children) {
            newMainItem.children = newMainItem.children.map(
              (childItem: any) => {
                const newChildItem = { ...childItem };
                if (newChildItem.children) {
                  newChildItem.children = newChildItem.children.map(
                    (grandChildItem: any) => {
                      let obj = {};
                      submitData.flat().forEach((dsItem: any) => {
                        // console.log(dsItem.id, grandChildItem.id, grandChildItem.id === dsItem.id);
                        if (grandChildItem.id === dsItem.id) {
                          // console.log({ ...grandChildItem, ...dsItem }, 11111);
                          obj = { ...grandChildItem, ...dsItem };
                        }
                      });
                      // console.log(obj, 22222222);

                      return obj;
                    }
                  );
                }
                return newChildItem;
              }
            );
          }
          return newMainItem;
        });

        return updatedIdicesList;
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [columns, setColumns] = useState<any>([]); // 先初始化为空数组，后续通过useEffect设置

  const [isEdit, setIsEdit] = useState<boolean>(mode === 'edit');

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [dataSource1, setDataSource1] = useState<any[]>([]);

  const [sampleNum, setSampleNum] = useState<any>(0);
  const [sourceTotal, setSourceTotal] = useState<any>(0);
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const formRef2 = useRef<any>(null);

  const [editableKeys, setEditableRowKeys] = useState<any>([[], []]);

  const [tableRefArray, setTableRefArray] = useState<any[]>([]);

  const [dataSourceArray, setDataSourceArray] = useState<any[]>([]);

  const [sampleNumArray, setSampleNumArray] = useState<any[]>([]);

  const [sourceTotalNumArray, setSourceTotalNumArray] = useState<any[]>([]);

  const [idicesList, setIndicesList] = useState<any>([]);

  const [submitData, setSubmitData] = useState<any>([[], []]);
  const [docs, setDocs] = useState<any>('');
  //当前上传附件的id
  const [curId, setCurId] = useState<any>('');

  // 新增状态用于存储上传的附件数据
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 定义回调函数，用于更新上传的附件数据
  const handleFileUpload = (files: any) => {
    console.log(files, '上传附件内容');
    setUploadedFiles(files);
    const newDataSourceArray = [...dataSourceArray];
    newDataSourceArray.forEach((tableArr, index) => {
      tableArr.forEach((item: any) => {
        if (item.id === curId) {
          item.docs = JSON.stringify(files);
        }
      });
    });
    setDataSourceArray(newDataSourceArray);
  };

  // 深层比较函数
  const isDeepEqual = (obj1: any, obj2: any): boolean => {
    if (obj1 === obj2) return true;

    if (
      typeof obj1 !== 'object' ||
      obj1 === null ||
      typeof obj2 !== 'object' ||
      obj2 === null
    ) {
      return false;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!isDeepEqual(obj1[key], obj2[key])) return false;
    }

    return true;
  };
  const resultList = [
    { value: '1', label: '合格' },
    { value: '0', label: '不合格' },
  ];

  // 计算 rowSpan 的函数
  const calculateRowSpan = (data: any[]) => {
    const result = [...data];
    for (let i = 0; i < result.length; i++) {
      if (i > 0 && result[i].sampleCode === result[i - 1].sampleCode) {
        result[i].sampleCodeRowSpan = 0;
        continue;
      }
      let rowSpan = 1;
      for (let j = i + 1; j < result.length; j++) {
        if (result[j].sampleCode === result[i].sampleCode) {
          result[j].sampleCodeRowSpan = 0;
          rowSpan++;
        } else {
          break;
        }
      }
      result[i].sampleCodeRowSpan = rowSpan;
    }
    return result;
  };

  const columns1: ProColumns<Any>[] = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      readonly: true,
      dataIndex: 'selfScore',
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      readonly: mode === 'view' || dialogTitletext === '查看',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        console.log(record?.docs && record.docs?.length > 0, 9999999);

        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },
    {
      title: '*专家评分',
      dataIndex: 'finalScore',
      readonly: mode === 'view' || dialogTitletext === '查看',
      // 使用 renderFormItem 渲染数字输入框
      renderFormItem: (_: any, record: any) => (
        <InputNumber
          value={record.record?.finalScore}
          onChange={(value: any) => {}}
          min={0} // 可根据需求设置最小值
          max={record.record?.score * 1} // 可根据需求设置最小值
          disabled={mode === 'view' || dialogTitletext === '查看'}
          // 可根据需求添加其他属性，如 max 等
        />
      ),
    },
    {
      title: '现场评审专家',
      readonly: true,
      dataIndex: 'fieldAuditUserName',
    },
    // {
    //   title: '*现场评审结论',
    //   dataIndex: 'fieldAuditResult',
    //   valueType: 'select',
    //   fieldProps: {
    //     options: resultList,
    //   },
    //   renderFormItem: (_:any,record:any) => (
    //           <Select
    //             className="!w-56  selectWidth"
    //             options={resultList}
    //             placeholder="请选择"
    //             optionFilterProp="name"
    //             value={record.record?.fieldAuditResult}
    //             onChange={(value) => {
    //               // console.log(dataSourceArray, 2222222222);

    //               // const newDataSourceArray = [...dataSourceArray];
    //               // let curIndex:any;
    //               // newDataSourceArray.forEach((tableArr, index) => {
    //               //     tableArr.forEach((item:any) => {
    //               //       if (item.id === record.record.id) {
    //               //         item.fieldAuditResult = value;
    //               //         curIndex = index;
    //               //       }
    //               //     });

    //               // });
    //               // console.log(newDataSourceArray, 11111111111);

    //               // setDataSourceArray(newDataSourceArray);
    //             }}
    //             allowClear
    //           ></Select>
    //   ),

    // },
    {
      title: '现场评审备注',
      dataIndex: 'fieldAuditRemark',
      readonly: mode === 'view' || dialogTitletext === '查看',
    },
  ];

  const columns2: ProColumns<Any>[] = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      readonly: true,
      dataIndex: 'selfScore',
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      readonly: mode === 'view' || dialogTitletext === '查看',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        console.log(record?.docs && record.docs?.length > 0, 88888888);
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        console.log(record?.docs && record.docs?.length > 0, 9999999);

        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },

    {
      title: '现场评审专家',
      dataIndex: 'fieldAuditUserName',
      readonly: true,
    },
    // {
    //   title: '现场评审结论',
    //   dataIndex: 'fieldAuditResult',
    //         valueType: 'select',
    //   fieldProps: {
    //     options: resultList,
    //   },

    // },
    {
      title: '现场评审备注',
      dataIndex: 'fieldAuditRemark',
      readonly: mode === 'view' || dialogTitletext === '查看',
    },
  ];

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计
  
  // 组件初始化时设置默认columns
  useEffect(() => {
    setColumns(columns1);
  }, []);

  useEffect(() => {
    if (staute === 1 || staute === '1') {
      setColumns(columns1);
    } else if (staute === 0 || staute === '0') {
      setColumns(columns2);
    }
  }, [staute]);

  useEffect(() => {
    if (detailInfo) {
      const indicesData =
        detailInfo.indices || detailInfo.gradeList || detailInfo.indexList;

      if (indicesData && Array.isArray(indicesData) && indicesData.length > 0) {
        const gradeListData = indicesData;
        setIndicesList(gradeListData);
        const arrContainer: any = [];
        const editId: any = [];

        gradeListData.forEach((e: any, index: number) => {
          let arrData: any = [];

          // 修复：安全检查 children 属性
          if (e.children && Array.isArray(e.children)) {
            e.children.forEach((e1: any, index1: number) => {
              // 修复：安全检查嵌套的 children
              if (e1.children && Array.isArray(e1.children)) {
                const arrData2 = e1.children.map((e2: any, index2: number) => {
                  return {
                    ...e2,
                    parentLab: e1.lab,
                  };
                });
                arrData.push(...arrData2);
              }
            });
          }

          if (arrData.length > 0) {
            arrContainer[index] = calculateRowSpan(arrData);

            // 为所有模式设置编辑键，确保表格能正常渲染数据
            const newKeyId = arrData
              .map((e: any) => e.id)
              .filter((id: any) => id !== undefined);
            editId[index] = newKeyId;

          } else {
            arrContainer[index] = [];
            editId[index] = [];
          }
        });

        // 修复：重构统计逻辑，移到循环外部
        const sourceTotalNumArray: any = [];
        arrContainer.forEach((item: any, index: number) => {
          const total = item.reduce((total: any = 0, arr: any) => {
            const score = arr.selfScore || 0;
            return (
              total +
              (typeof score === 'number' ? score : parseFloat(score) || 0)
            );
          }, 0);
          sourceTotalNumArray.push(total);
        });

        setSampleNumArray(sourceTotalNumArray);
        const totals2 = sourceTotalNumArray.reduce(
          (total: any = 0, arr: any) => {
            return total + (typeof arr === 'number' ? arr : 0);
          },
          0
        );
        setSampleNum(totals2);

        const sourceTotalNum = gradeListData.map((e: any) => e.score || 0);

        // 修复：确保数据设置顺序正确
        setDataSourceArray(arrContainer);
        setSubmitData(arrContainer);
        setSourceTotalNumArray(sourceTotalNum);

        // 强制组件重新渲染，确保表格显示数据
        setTimeout(() => {
          setDataSourceArray([...arrContainer]);
        }, 100);


        // 为查看模式也设置编辑键以确保正常渲染
        if (dialogTitletext === '现场评审-组员评审' && mode === 'edit') {
          setEditableRowKeys(editId);
        } else {
          // 查看模式下也设置编辑键以确保表格正常渲染数据
          setEditableRowKeys(editId);
        }

        // 修复：优化表格引用设置
        setTimeout(() => {
          const arr: any = [];
          gradeListData.forEach((e: any) => {
            arr.push(useRef<FormInstance>(null));
          });
          setTableRefArray(arr);
        }, 100); // 减少延迟时间
      } else {
        // 重置状态
        setIndicesList([]);
        setDataSourceArray([]);
        setSubmitData([]);
      }
    }
  }, [detailInfo, dialogTitletext, mode]);



  return (
    <>
      <BlockContainer title="指标明细">
        {idicesList.length > 0 ? (
          idicesList.map((tableData: any, index: number) => {
            return (
              <BlockContainer
                key={`table-${index}-${dataSourceArray[index]?.length || 0}`}
                title={tableData.lab}
              >
                {columns && columns.length > 0 ? (
                  <ProForm<{ table: Any[] }>
                    key={`form-${index}-${dataSourceArray[index]?.length || 0}`}
                    formRef={formRef}
                    initialValues={{ table: dataSourceArray[index] || [] }}
                    submitter={false}
                    onFinish={handleSave}
                  >
                    <EditableProTable<Any>
                    key={`editable-table-${index}-${
                      dataSourceArray[index]?.length || 0
                    }`}
                    rowKey="id"
                    name="table"
                    editableFormRef={tableRefArray[index] || undefined}
                    loading={loading}
                    dataSource={dataSourceArray[index] || []}
                    value={dataSourceArray[index] || []}
                    onChange={(value) => {
                      const submitDataValue = [...submitData];
                      submitDataValue[index] = value;
                      setSubmitData(submitDataValue);
                    }}
                    scroll={{ x: 960 }}
                    recordCreatorProps={false}
                    columns={columns}
                    editable={{
                      type: 'multiple',
                      editableKeys: editableKeys[index] || [],
                      onValuesChange: (record: any, recordList: any[]) => {
                        // console.log(
                        //   `Table ${index} values changed:`,
                        //   record,
                        //   recordList
                        // );
                        // 使用深层比较检查数据是否发生变化
                        if (!isDeepEqual(submitData[index], recordList)) {
                          const submitDataValue = [...submitData];
                          submitDataValue[index] = recordList;
                          setSubmitData(submitDataValue);
                          // console.log(
                          //   `Table ${index} submitData updated:`,
                          //   submitDataValue[index]
                          // );
                        }
                      },
                    }}
                    // 修复：添加额外的配置确保表格正常渲染
                    search={false}
                    pagination={false}
                    toolBarRender={false}
                    options={false}
                  />
                  <div className="total-num">
                    <div className="num-box">合计: </div>
                    <div className="num-box">
                      指标名称总分：{sourceTotalNumArray[index]}
                    </div>
                    <div className="num-box">
                      自评得分：{sampleNumArray[index]}
                    </div>
                  </div>
                </ProForm>
                ) : (
                  <div>等待columns初始化...</div>
                )}
              </BlockContainer>
            );
          })
        ) : (
          <></>
        )}
        {idicesList.length > 0 && (
          <div className="total-num total-num-1">
            <div className="num-box">自评总得分: </div>
            <div className="num-box">{sampleNum}</div>
            <Button
              type="link"
              size="small"
              onClick={() => {
                setOpenDetail(true);
              }}
            >
              查看等级划分
            </Button>
          </div>
        )}
      </BlockContainer>

      <Modal
        width="60%"
        title="支撑材料"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <UploadList
          dialogTitletext={dialogTitletext}
          loading={loading}
          onRef={formRef2}
          mode="view"
          docs={docs}
          onUpload={handleFileUpload} // 传递回调函数
        />
      </Modal>
      {/* 录入查找样本编号 */}
      <Modal
        title="等级划分"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={450}
      >
        <div className="p-4">
          <div>一级：≥60分，且＜70分；</div>
          <div>二级乙等：≥70分，且＜80分；</div>
          <div>二级甲等：≥80分，且＜85分；</div>
          <div>三级乙等：≥85分，且＜95分；</div>
          <div>三级甲等：≥95分。</div>
        </div>
      </Modal>
    </>
  );
};

export default ReceiveForm;
