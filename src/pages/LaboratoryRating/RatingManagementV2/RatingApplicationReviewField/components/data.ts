/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-15 16:07:05
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-25 09:44:45
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\Task\data.ts
 * @Description: 
 */
/**
 * @description 详情表单布局
 */
export const formItemLayout = {
  span: 8,
};

/**
 * @description 详情表单默认值
 */
export const FormInitVal = {
  // 上级菜单
  parentId: null,
  // 菜单类型
  menuType: 'M',
  // 菜单图标
  icon: '',
  // 菜单名称
  menuName: '',
  // 排序
  orderNum: '',
  // 菜单状态
  status: 0,
};

/**
 * @description 年份下拉框枚举
 */
export const yearList = [
  {
    value: '2025',
    label: '2025',
  },
  {
    value: '2024',
    label: '2024',
  },
  {
    value: '2023',
    label: '2023',
  },
];
const getYearList = () => {
  const currentYear = new Date().getFullYear();
  // 创建一个空数组用于存放年份选项
  const yearsArray: { value: number; label: number }[] = [];
  // 往前推算四年
  for (let i = 0; i <= 4; i++) {
    const year = currentYear - i;
    yearsArray.push({
      value: year,
      label: year,
    });
  }
  return yearsArray;
};
export const yearListOnTable = getYearList();

// 哨点监测范围：
export const pointRanger = [
  '鼠疫杆菌',
  '霍乱弧菌',
  '甲型流感病毒',
  'SARS冠状病毒',
  '艾滋病毒',
  '肝炎病毒',
  '脊髓灰质炎病毒',
  '禽甲型流感病毒',
  '麻疹病毒',
  '流行性出血热病毒',
  '狂犬病毒',
  '乙脑病毒',
  '登革热病毒',
  '炭疽芽孢杆菌',
  '痢疾杆菌',
  '阿米巴原虫',
  '结核分枝杆菌',
  '伤寒杆菌',
  '副伤寒杆菌',
  '脑膜炎双球菌',
  '百日咳杆菌',
];
