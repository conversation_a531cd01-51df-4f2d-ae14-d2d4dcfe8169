/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-throw-literal */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { But<PERSON>, Col, Drawer, message, Modal, Row, Tooltip } from 'antd';
import { gradeConfig, gradeConfigTip } from '@/api/apply';
import { downloadFile } from '@/api/file';
import { getYearList } from '@/api/settings';
import { getTempList } from '@/api/temp';
import taskTypeImge from '@/assets/taskType.png';
import { codeDefinition } from '@/constants';
import { useTokenStore } from '@/store';
import { useQualityStore } from '@/store/quality';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormCheckbox,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import EFileView from '@/components/EFileView';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import FileView from '@/components/FileView';
import FileViewByStream from '@/components/FileViewByStream';
import FileViewByStreamByUrl from '@/components/FileViewByStreamByUrl';
import downloadLocalFile from '@/utils/downloadLocalFile';
import AllImg from './AllImg';
import { FormInitVal, formItemLayout } from './data';

const layoutProps = {
  colProps: { ...formItemLayout },
};
type TEditProps = {
  id?: any;
  detailInfo?: any;
  onRef?: any;
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isShowChar?: boolean; // 是否显示使用模板和任务类型字段
  taskType?: string;
  dialogTitletext?: string;
};

type Options = {
  label: string;
  value: string;
};

const BaseForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onRef,
  readonly = false,
  dialogTitletext,
}) => {
  const [templateOnform, setTemplateOnform] = useState<any>([]);
  // 表单实例
  const formRef = useRef<FormInstance>(null);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  // word等文件预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  const [openAllImg, setOpenAllImg] = useState<boolean>(false);

  const [fileUrl, setFileUrl] = useState<any>();
  // 是否是草稿
  const [isDraft, setIsDraft] = useState<boolean>(false);
  // 模板预览弹窗
  const [openPreviewTemplate, setOpenPreviewTemplate] =
    useState<boolean>(false);
  // 考核类型ID
  const [assessmentTypeId, setAssessmentTypeId] = useState<string>('');
  const { token } = useTokenStore();

  // 当前需要预览的文件ID
  // const [previewFileId, setPreviewFileId] = useState<string>('');

  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [openDetailMsg, setOpenDetailMsg] = useState<boolean>(false);

  const [gradeList, setGradeList] = useState<any>([]);

  const [tipsValue, setTipsValue] = useState<any>('');

  const getGradeConfigTip = async () => {
    try {
      const { code, data, msg } = await gradeConfigTip();
      setTipsValue(msg);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const formRef2 = useRef<any>(null);

  useEffect(() => {
    gradeConfigData();
    getGradeConfigTip();
  }, []);
  const gradeConfigData = async () => {
    try {
      const { code, data, msg } = await gradeConfig();

      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setGradeList(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  // 获取table中需要的枚举
  const {
    assessmentTypesOnForm, //所有
    assessmentTypesOnForm2, // 带权限
    getAssessmentTypes,
    getAssessmentTaskTypes,
    getAssessmentTypesOnForm2,
  } = useQualityStore();

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSave,
      getTaskType: async () => {
        const task = await formRef.current?.getFieldValue('taskType');
        return task;
      },
      handleParams: async () => {
        const formDataInfo = formRef.current?.getFieldsValue();
        let attachmentIds = [];
        if (
          formDataInfo.attachmentIds &&
          formDataInfo.attachmentIds.length > 0
        ) {
          attachmentIds = formDataInfo.attachmentIds
            .filter(
              (item: any) =>
                item.response && item.response.data && item.response.data.ossId
            )
            .map((item: any) => {
              return {
                name: item.response.data.fileName,
                id: item.response.data.ossId,
                url: item.response.data.url,
              };
            });
        }
        return {
          ...formDataInfo,
          attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
        };
      },
      handleValidateFields: async () => {
        try {
          await formRef.current?.validateFields();
        } catch (error) {
          throw '请完善基本信息';
        }
      },
    };
  });

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    try {
      if (id && detailInfo) {
        const p = clone(detailInfo);
        console.log(p, 'p');
        // try {
        p.file = p.file
          ? JSON.parse(p.file).map((item: any) => {
              return {
                uid: item.ossId,
                name: item.fileName,
                status: 'done',
                type: 'application/msword',
                url: item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: item.fileName,
                    ossId: item.ossId,
                    url: item.url,
                  },
                },
              };
            })
          : [];
        setIsDraft(detailInfo.status === '0');

        const p1 = {
          ...p,
          commitment: p.commitment === 1 ? ['1'] : [],
        };

        formRef.current?.setFieldsValue(p1);
        formRef.current?.setFieldValue('name', p1.labName);
        setAssessmentTypeId(detailInfo.assessmentType);
      } else {
        formRef.current?.setFieldsValue({
          particularYear: new Date().getFullYear(),
        });
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  const handleLookPdf = () => {
    setOpenPreview(true);
    setFileUrl('/gradeConfig/previewGradeFile');
  };

  const handleLookAllImg = () => {
    setOpenAllImg(true);
  };

  /**
   * @TODO 获取使用模板
   */
  const getTemplateOnform = async (templateTypeId: number | string) => {
    formRef.current?.setFieldValue('templateType', null);
    if (!templateTypeId) {
      setTemplateOnform([]);
      return;
    }
    try {
      const params = {
        assessmentType: templateTypeId,
        current: 1,
        size: 100,
        status: 1,
      };
      const { code, rows } = await getTempList(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        const newData = rows?.map((_item: any) => ({
          label: _item.name,
          value: _item.id,
          code: _item.code,
        }));
        setTemplateOnform(newData);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async () => {
    await formRef.current?.validateFields();
    let formInfo = cloneDeep(formRef.current?.getFieldsValue());
    // 模板名称-模板code
    const templateObj =
      templateOnform.filter(
        (_item: Options) => formInfo.templateId === _item.value
      )![0] || {};
    let attachmentIds = [];
    if (formInfo.attachmentIds && formInfo.attachmentIds.length > 0) {
      attachmentIds = formInfo.attachmentIds
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            id: item.response.data.ossId,
            url: item.response.data.url,
          };
        });
    }
    const params = {
      ...formInfo,
      templateName: templateObj.label,
      templateCode: templateObj.code,
      attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
    };
    return params;
  };
  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };
  /**
   * @TODO 预览模板
   */
  const handlePreviewTemplate = (info: any) => {
    setOpenPreviewTemplate(true);
  };

  /**
   * @TODO 任务类型图示下载
   */
  const handleDownLoadPhoto = (info: any) => {
    downloadLocalFile(taskTypeImge, '考核任务类型图示.png');
  };

  /**
   * 处理文件下载
   * @param info 文件信息
   */
  const handleDownload = (info: any) => {
    // 检查文件信息
    if (!info) {
      message.error('文件信息不完整，无法下载');
      return;
    }

    // 获取文件ID
    const fileId = info.response?.data?.id || info.id || info.uid;
    if (!fileId) {
      message.error('文件ID不存在，无法下载');
      return;
    }

    // 获取文件名
    const fileName = info.name || '附件';

    // 调用API下载文件
    downloadFile(fileId, fileName)
      .then(() => {
        message.success(`${fileName} 下载成功`);
      })
      .catch((error) => {
        console.error('下载文件失败:', error);
        message.error('下载文件失败，请稍后重试');
      });
  };

  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  useEffect(() => {
    queryYearList();
    getAssessmentTaskTypes();
    if (!readonly && !isDraft) {
      getAssessmentTypesOnForm2();
    } else {
      getAssessmentTypes();
    }
  }, []);

  useEffect(() => {
    assessmentTypeId && getTemplateOnform(assessmentTypeId);
  }, [assessmentTypeId]);

  /**
   * 获取年份List
   */
  const [yearList, setYearList] = useState<any>([]);
  const queryYearList = async () => {
    try {
      const { data } = await getYearList();
      setYearList(data || []);
    } catch (err) {
      throw new Error(`Error: ${err}`);
    }
  };

  return (
    <>
      <div className="d-flex top-txt-wraper">
        <div className="btn-wraper">
          <Button
            onClick={() => handleLookPdf()}
            type="primary"
            className="mr-2 ml-2"
          >
            等级评审指标文件
          </Button>
          <Button onClick={() => handleLookAllImg()}>支持材料集</Button>
        </div>
      </div>
      <BlockContainer title="基本信息">
        <ProForm
          readonly={true}
          formRef={formRef}
          {...formItemLayout}
          layout="horizontal"
          grid={true}
          submitter={false}
          initialValues={FormInitVal}
          onValuesChange={(_: any, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <Col span={8} className="form-wrpaer-1">
            <ProFormSelect
              // readonly={readonly || isDraft}

              options={gradeList}
              rules={[{ required: true, message: '请选择申请等级' }]}
              name="setId"
              label="申请等级"
              labelCol={{ flex: 0.08 }}
              // {...layoutProps}
            />
          </Col>
          <Col span={8} className="form-wrpaer-2  form-wrpaer-2-readonly">
            <ProFormCheckbox.Group
              name="commitment"
              layout="horizontal"
              label="申请承诺"
              colProps={{ span: 24 }}
              labelCol={{ flex: 0.08 }}
              rules={[{ required: true, message: '请勾选承诺要求' }]}
              options={[
                {
                  label: '承诺满足必备要求',
                  value: '1',
                },
              ]}
            />
            <Button className="btn-1" onClick={() => setOpenDetailMsg(true)}>
              阅读要求
            </Button>
            {/* <div className='tipsText'>注：原则上以评审周期为限。</div> */}
          </Col>
          <Col span={8}>
            <ProFormText name="name" label="机构名称" readonly={true} />
          </Col>
          <Col span={8}>
            <ProFormText
              name="person"
              label=" 负责人"
              labelCol={{ flex: 0.015 }}
              placeholder="请输入负责人"
              rules={[
                {
                  required: true,
                  validator(rule, value, callback) {
                    if (!value) {
                      callback('请输入负责人');
                    }
                  },
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              name="areaName"
              label="所属区域"
              labelCol={{ flex: 0.015 }}
              readonly={true}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              name="phone"
              label=" 联系电话"
              rules={[{ required: true, message: '请输入联系电话' }]}
              placeholder="请输入联系电话"
              // {...layoutProps}
            />
          </Col>

          <Col span={8}>
            <ProFormText
              name="selfScore"
              label=" 自评得分"
              rules={[{ required: true, message: '请输入自评得分' }]}
              placeholder="请输入自评得分"
              // {...layoutProps}
            />
          </Col>
          <Col span={8}>
            <ProFormTextArea
              name="remark"
              label=" 申请备注"
              // {...layoutProps}
            />
          </Col>

          <Col span={24} className="form-wrpaer-3">
            <EProFormUploadButton
              name="file"
              label="申请书"
              labelCol={{ flex: 0.032 }}
              max={10}
              defaultExtraTextHide={true}
            />
          </Col>
        </ProForm>
      </BlockContainer>
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />

      {/* 预览模板 */}
      <Drawer
        width="85%"
        title={formRef.current?.getFieldValue('templateName')}
        onClose={() => setOpenPreviewTemplate(false)}
        open={openPreviewTemplate}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      ></Drawer>

      {/* 录入查找样本编号 */}
      <Modal
        title="温馨提示"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={1200}
      >
        <div className="p-4">
          <div
            dangerouslySetInnerHTML={{
              __html: tipsValue.replace(/\n/g, '<br>'),
            }}
          />
          <div className="flex justify-end space-x-2">
            <Button onClick={() => setOpenDetail(false)}>知道了</Button>
          </div>
        </div>
      </Modal>

      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <FileViewByStreamByUrl fileUrl1={fileUrl} isPreview />
      </Modal>
      {/* 预览模板 */}
      <Drawer
        width="85%"
        title={'支撑材料集'}
        onClose={() => setOpenAllImg(false)}
        open={openAllImg}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <AllImg onRef={formRef2} onSubmit={() => {}} />
      </Drawer>

      {/* 阅读要求要求详情 */}
      <Modal
        title="必备要求"
        open={openDetailMsg}
        footer={null}
        onCancel={() => {
          setOpenDetailMsg(false);
        }}
        width={1200}
      >
        <div className="p-4">
          <div>
            <div className="title">第一部分 必备要求</div>
            <div className="title-1">一、依法设置与规范从业</div>
            <p className="text">
              （一）严格遵守《中华人民共和国传染病防治法》，未造成传染病传播、流行或其他严重后果；且未发生其他重大卫生违规事件，未造成严重后果或情节严重；且近两年来卫生健康、疾控行政部门或监督执法机构对其进行传染病防治分类监督综合评价未被列为重点监督单位。
            </p>
            <p className="text">
              （二）严格遵守《中华人民共和国疫苗管理法》，未出现违法违规采购或使用疫苗并造成严重后果的情况。
            </p>
            <p className="text">
              （三）严格遵守《中华人民共和国职业病防治法》及其配套法规，未出现违规开展职业卫生技术、放射卫生服务等情况并造成严重后果的情况。
            </p>
            <p className="text">
              （四）严格遵守《中华人民共和国食品安全法》及其配套法规，未出现履职不到位而导致严重后果的情况。
            </p>
            <p className="text">
              （五）未发生过其他造成严重后果或情节严重的重大违法、违规事件。
            </p>
            <div className="title-1">二、党风行风诚信和公益责任</div>
            <p className="text">
              （一）
              全面加强党的领导，全面履行从严治党主体责任、监督责任，未受到过上级党组织的问责处理。
            </p>
            <p className="text">
              （二）领导班子发生严重职务犯罪或严重违纪事件少于1起，且未出现过严重违反职业道德的群体性事件等情况。
            </p>
            <p className="text">
              （三）严格遵守《中华人民共和国统计法》、《医学科研诚信和相关行为规范》等相关法律法规，未出现统计和报告造假、虚假提供申报材料和科研成果等情况。
            </p>
            <p className="text">
              （四）按照国家和省疾控行政部门要求，按时完成对口支援、中国援外医疗队、突发公共事件医疗救援、公共卫生任务等政府指令性工作。
            </p>
            <div className="title-1">三、安全管理与重大事件</div>
            <p className="text">
              （一）未发生过实验室感染等重大生物安全事件并造成严重后果。
            </p>
            <p className="text">
              （二）未发生过火灾、爆炸、放射源泄漏及丢失、有害气体泄漏、菌（毒）株丢失等情况。
            </p>
            <p className="text">
              （三）未出现过瞒报、漏报重大公共卫生过失事件的行为。
            </p>
            <p className="text">
              （四）未发生过泄密、大规模公共卫生数据泄露或其他重大网络安全事件并造成严重后果。
            </p>
            <p className="text">注：原则上以评审周期为限。</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button onClick={() => setOpenDetailMsg(false)}>
              我已知晓，并且满足必备要求
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default memo(BaseForm);
