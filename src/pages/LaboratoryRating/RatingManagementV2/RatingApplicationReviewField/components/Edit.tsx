/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable eqeqeq */

/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Form, Input, message, Modal, Spin } from 'antd';
import { fieldProcess, getFieldReviewLookAt } from '@/api/apply';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import BlockContainer from '@/components/BlockContainer';
import ProcessLogWrapper from '@/components/ProcessLogWrapper';
import LabApplicationForm from '@/pages/LaboratoryRating/ApplicationManagement/RatingApplication/components/LabApplicationForm';
import Attachment from './Attachment';
import BaseForm from './BaseForm';
import EditTool from './EditTool';
import ReceiveForm from './ReceiveForm';
import TableSelect from './TableSelect';

const { TextArea } = Input;

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  staute?: any;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId,
  setIsUploadReceipt,
  dialogTitletext,
  staute,
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  // 通过弹窗是否显示
  const [isShowPass, setIsShowPass] = useState<boolean>(false);

  const [isShowNotPass, setIsShowNotPass] = useState<boolean>(false);

  const [isShowSendBack, setIsShowSendBack] = useState<boolean>(false);

  const [passValue, setPassValue] = useState<any>('');

  const [notPassValue, setNotPassValue] = useState<any>('');

  const [docComplementDate, setDocComplementDate] = useState<any>('');

  const [sendBackValue, setSendBackValue] = useState<any>('');

  const formRef5 = useRef<any>(null);

  const formRef6 = useRef<any>(null);

  const formRef7 = useRef<any>(null);

  const [problemValue, setProblemValue] = useState<any>('');

  const [adviceValue, setAdviceValue] = useState<any>('');

  const formRef12 = useRef<any>(null);

  // 定义复制函数
  const handleCopy = () => {
    const textToCopy =
      '经评审专家组对提交的实验室等级申请材料进行全面审核，确认材料完整、数据真实、内容符合《XXX实验室等级评定标准》（标准编号：XXX-202X）的相关要求，予以审核通过，进入下一阶段现场评审环节。';
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        message.success('复制成功');
      })
      .catch((error) => {
        message.error('复制失败，请重试');
        console.error('复制失败:', error);
      });
  };

  const [Id, setIds] = useState<any>(1);

  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getFieldReviewLookAt(id);
        if (code === 200) {
          setBaseInfo(data);
          console.log(
            data.problem && data.problem !== '',
            111111114,
            data.problem
          );
          setTimeout(() => {
            if (data.problem && data.problem !== '' && formRef12.current) {
              formRef12.current?.setFieldsValue({ problemValue: data.problem });
              setProblemValue(data.problem);
            }
            if (data.advice && data.advice !== '' && formRef12.current) {
              formRef12.current?.setFieldsValue({ adviceValue: data.advice });
              setAdviceValue(data.advice);
            }
          }, 1000);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode, formRef12]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formRef3 = useRef<any>(null);

  const formRef13 = useRef<any>(null);

  const handleConfirmResults = () => {
    if (!formRef2.current.handleSubmit('submit')) {
      return;
    }

    if (problemValue === '') {
      message.error('请完善发现的问题');
      return;
    }

    if (adviceValue === '') {
      message.error('请完善工作建议');
      return;
    }

    const dateValue = formRef2.current.handleSubmit('submit');

    const realParams = {
      ...baseInfo,
      indices: dateValue,
      eventCode: 'submit',
      problem: problemValue,
      advice: adviceValue,
    };
    Modal.confirm({
      title: '提示',
      content: '确认提交数据？',
      onOk: async () => {
        try {
          const { code, msg } = await fieldProcess(realParams);
          if (code === 200) {
            message.success('提交成功');
            close();
          } else {
            message.error(msg);
            return;
          }
        } catch (error) {
          message.error('删除失败，请稍后重试');
        }
      },
      onCancel: () => {
        message.info('已取消删除操作');
      },
    });
  };

  // 引用备注功能
  const handleQuoteRemarks = () => {
    try {
      // 从ReceiveForm获取当前的表格数据
      const formData = formRef2.current?.handleSubmit();
      if (!formData || !Array.isArray(formData)) {
        message.warning('暂无备注数据可引用');
        return;
      }

      // 收集所有的备注信息
      const remarks: string[] = [];
      let sequenceNumber = 1;

      // 遍历嵌套的数据结构：mainItem -> children -> grandChildren
      formData.forEach((mainItem: any) => {
        if (mainItem.children && Array.isArray(mainItem.children)) {
          mainItem.children.forEach((childItem: any) => {
            if (childItem.children && Array.isArray(childItem.children)) {
              childItem.children.forEach((grandChildItem: any) => {
                // 检查现场评审专家备注
                if (grandChildItem.fieldAuditRemark && grandChildItem.fieldAuditRemark.trim()) {
                  remarks.push(`${sequenceNumber}. ${grandChildItem.fieldAuditRemark.trim()}`);
                  sequenceNumber++;
                }
                
                // 检查其他可能的备注字段
                if (grandChildItem.remark && grandChildItem.remark.trim()) {
                  remarks.push(`${sequenceNumber}. ${grandChildItem.remark.trim()}`);
                  sequenceNumber++;
                }
              });
            }
          });
        }
      });

      if (remarks.length === 0) {
        message.warning('暂无备注信息可引用');
        return;
      }

      // 将备注信息格式化为列表形式
      const remarksText = remarks.join('\n');
      
      // 填充到"发现的问题"文本框中
      const currentProblem = problemValue ? problemValue + '\n\n' : '';
      const newProblemValue = currentProblem + remarksText;
      
      setProblemValue(newProblemValue);
      formRef12.current?.setFieldsValue({
        problemValue: newProblemValue,
      });

      message.success(`成功引用 ${remarks.length} 条备注信息`);
    } catch (error) {
      console.error('引用备注失败:', error);
      message.error('引用备注失败，请重试');
    }
  };

  const handleSave = async (status: any) => {
    const dateValue = formRef2.current.handleSubmit();

    const realParams = {
      ...baseInfo,
      indices: dateValue,
      eventCode: 'save',
      problem: problemValue,
      advice: adviceValue,
    };
    if (status === 2) {
      delete realParams.problem;
      delete realParams.adviceValue;
      realParams.report = formRef13.current?.handleGetData();
    }
    const { code, msg } = await fieldProcess(realParams);
    if (code === 200) {
      message.success('保存成功');
      close();
    } else {
      message.error(msg);
      return;
    }
  };
  const handleSubmitPass = async () => {
    if (!passValue) {
      message.error('请输入审核意见');
      return;
    }
    handleReview('submit', passValue);
  };
  // 不通过

  const handleSubmitReject = async () => {
    if (!notPassValue) {
      message.error('请输入终止评审原因');
      return;
    }
    handleReview('stop', notPassValue);
  };

  const handleReview = async (stauts: String, word: any) => {
    setMainLoading(true);
    let realParams = {};
    const dateValue = formRef2.current.handleSubmit('submit1');
    realParams = {
      ...baseInfo,
      indices: dateValue,
      eventCode: stauts,
      reviewRemark: word,
      report: formRef13.current?.handleGetData(),
    };

    const { code, data, msg } = await fieldProcess(realParams);
    if (code === 200) {
      message.success('提交成功');
      close();
    } else {
      message.error(msg);
    }
    setMainLoading(false);
    setIsShowPass(false);
    setIsShowNotPass(false);
    setIsShowSendBack(false);
  };

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="flex-1 overflow-auto flex pb-[50px]">
          <div className="flex-1 overflow-auto p-6 w-[80%]">
            {/* <BaseForm
              readonlyAll={true}
              readonly={true}
              hideRank={true}
              isShowChar={false}
              dialogTitletext={dialogTitletext}
              onRef={formRef1}
              id={id}
              detailInfo={baseInfo}
            /> */}
            <LabApplicationForm
              readonly={true}
              dialogTitletext={dialogTitletext}
              id={id}
              initialData={baseInfo}
            />
            <div className="mt-4">
              <ReceiveForm
                loading={loading}
                onRef={formRef2}
                dialogTitletext={dialogTitletext}
                baseInfo={baseInfo}
                staute={staute}
                detailInfo={baseInfo}
                mode={mode}
                onSubmit={(val) => {
                  handleSaveSubmit(val);
                }}
              />
            </div>

            {staute == 1 && (
              <BlockContainer className="mt-2" title="评审总结" 
                extra={
                  <Button 
                    type="primary" 
                    size="small"
                    onClick={handleQuoteRemarks}
                    disabled={dialogTitletext == '查看'}
                  >
                    引用备注
                  </Button>
                }
              >
                <div className="mb-2">发现的问题和工作建议：</div>
                <Form
                  ref={formRef12}
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 14 }}
                  layout="horizontal"
                >
                  <Form.Item
                    label="发现的问题:"
                    name="problemValue"
                    rules={[{ required: true, message: '请输入发现的问题' }]}
                  >
                    <TextArea
                      rows={4}
                      className="textarea-style"
                      disabled={dialogTitletext == '查看'}
                      value={problemValue}
                      onChange={(e: any) => {
                        setProblemValue(e.target.value);
                        formRef12.current?.setFieldsValue({
                          problemValue: e.target.value,
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    label="工作建议:"
                    name="adviceValue"
                    className="textarea-style"
                    rules={[{ required: true, message: '请输入工作建议' }]}
                  >
                    <TextArea
                      disabled={dialogTitletext == '查看'}
                      value={adviceValue}
                      onChange={(e: any) => {
                        setAdviceValue(e.target.value);
                        formRef12.current?.setFieldsValue({
                          adviceValue: e.target.value,
                        });
                      }}
                      rows={4}
                    />
                  </Form.Item>
                </Form>
              </BlockContainer>
            )}
            {staute === 0 && (
              <div className="mt-4">
                <TableSelect
                  loading={loading}
                  onRef={formRef3}
                  dialogTitletext={dialogTitletext}
                  baseInfo={baseInfo}
                  mode={mode}
                  onSubmit={(val) => {
                    handleSaveSubmit(val);
                  }}
                />
              </div>
            )}
            {staute === 0 && (
              <div className="mt-4">
                <EditTool
                  dialogTitletext={dialogTitletext}
                  baseInfo={baseInfo}
                  onRef={formRef13}
                  id={id}
                />
              </div>
            )}
          </div>
          <div className="w-[20%]">
            <ProcessLogWrapper documentNo={Id} detailInfo={baseInfo} />
          </div>
        </div>
      )}
      {/* <span>{mainLoading}</span>
      <span>{readonly}</span> */}
      {dialogTitletext !== '查看' && (staute === 0 || staute === '0') && (
        <div className="h-[50px]  bottom-btn-wraper  bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              if (formRef13.current?.handleGetData()) {
                setIsShowPass(true);
              } else {
                message.error('请完善评审报告');
              }
            }}
            loading={loading}
            type="primary"
          >
            完成现场评审
          </Button>
          <Button
            onClick={() => {
              setNotPassValue('');
              formRef6.current?.setFieldsValue({ notPassRemark: '' });
              setIsShowNotPass(true);
            }}
            loading={loading}
          >
            终止评审
          </Button>
          <Button
            onClick={() => {
              handleSave(2);
            }}
            loading={loading}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext !== '查看' && (staute === 1 || staute === '1') && (
        <div className="  bottom-btn-wraper h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleConfirmResults();
            }}
            loading={loading}
            type="primary"
          >
            提交
          </Button>
          <Button
            onClick={() => {
              handleSave(1);
            }}
            loading={loading}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext == '查看' && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {/*  通过弹窗 */}
      <Modal
        title="完成现场评审"
        open={isShowPass}
        footer={null}
        onCancel={() => {
          setIsShowPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef5}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="passRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={passValue}
              onChange={(e: any) => {
                setPassValue(e.target.value);
                formRef5.current?.setFieldsValue({
                  passRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
        </Form>
        <div className="tips red">
          提示：评审专家组组内不得统分、不得交流评分情况，不得向申报评审的疾控中心透露评分情况。
        </div>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleSubmitPass();
            }}
          >
            确认提交
          </Button>
          <Button
            onClick={() => {
              setIsShowPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>
      {/*  终止评审弹窗 */}
      <Modal
        title="终止评审"
        open={isShowNotPass}
        footer={null}
        onCancel={() => {
          setIsShowNotPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef6}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="notPassRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={notPassValue}
              onChange={(e: any) => {
                setNotPassValue(e.target.value);
                formRef6.current?.setFieldsValue({
                  notPassRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">
            提示：如果发现该疾控中心有违背规定的，可以终止评审程序。
          </div>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleSubmitReject();
            }}
          >
            确认终止
          </Button>
          <Button
            onClick={() => {
              setIsShowNotPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default MenuDetail;
