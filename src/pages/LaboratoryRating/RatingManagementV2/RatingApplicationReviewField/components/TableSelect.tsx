/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Upload,
} from 'antd';
import { downloadExpertsReportWord } from '@/api/apply';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import BlockContainer from '@/components/BlockContainer';
import download from '@/utils/download';
import Task from './Task';

const CheckboxGroup = Checkbox.Group;

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const TableSelect: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  onRef,
  baseInfo,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [openRandomDetail, setOenRandomDetail] = useState<boolean>(false);

  const [tableShow, setTableShow] = useState<boolean>(true);
  const [tableShow1, setTableShow1] = useState<boolean>(true);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [selectOption, setSelectOption] = useState<any[]>([]);

  const [selectedRows1, setSelectedRows] = useState<any[]>([]);

  const [plainOptions, setPlainOptions] = useState<any[]>([]);

  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);

  const [detailId, setDetailId] = useState<any[]>([]);

  const [openDetail1, setOpenDetail1] = useState<boolean>(false);

  const [advice, setAdvice] = useState<any>('');
  const [problem, setProblem] = useState<any>('');

  //任务分配弹窗是否显示
  const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);

  useEffect(() => {
    if (baseInfo?.fieldReviews && baseInfo?.fieldReviews?.length > 0) {
      const newData = baseInfo.fieldReviews.map((item: any, index: any) => ({
        ...item,
        index: index + 1,
      }));
      setDataSource(newData);
    }
  }, [baseInfo]);

  /**
   * @TODO 关闭任务分配
   */
  const closeEdit1 = () => {
    setOpenAssignModal(false);
    // tableReload();
  };

  const columns: any = [
    {
      dataIndex: 'index',
      width: 60,
      title: '序号',
    },
    {
      title: '专家姓名',
      dataIndex: 'auditName',
      hideInSearch: true,
    },
    {
      title: '专家总结（发现的问题和工作建议）',
      dataIndex: 'problem',
      hideInSearch: true,
      render: (text: any, record: any, _: any, action: any) => {
        return (
          <div
            className="link-btn"
            onClick={() => {
              setDetailId(record.id);
              setOpenDetail1(true);
              setAdvice(record.advice);
              setProblem(record.problem);
            }}
          >
            专家总结内容展示，点击可以查看全部内容
          </div>
        );
      },
    },
    {
      title: '现场评审提交状态',
      dataIndex: 'address',
      hideInSearch: true,

      render: (text: any, record: any, _: any, action: any) => {
        if (record.status === 1) {
          return <div>已提交</div>;
        }
        if (record.status === 0) {
          return (
            <div>
              <a className="text-red-500">未提交</a>
            </div>
          );
        }
      },
    },
  ];

  //选择小组表格渲染

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 选择

  const selectGroupData = () => {
    if (selectedRows1.length > 0) {
      setDataSource(selectedRows1);
      setOpenDetail(false);
    } else {
      message.error('请选择至少一个专家');
    }
  };

  const [loadingWord, setLoadingWord] = useState<boolean>(false);

  // 下载专家总结为word
  const handleDownLoadWord = async () => {
    try {
      setLoadingWord(true);
      const data = await downloadExpertsReportWord(baseInfo?.id);
      if (data) {
        await download(data, '专家总结.docx');
      }
    } catch (error) {
      console.error('下载专家总结为word失败:', error);
    } finally {
      setLoadingWord(false);
    }
  };

  return (
    <>
      <BlockContainer title="">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          <div className="btn-wraper">
            <Button className="mr-2" onClick={() => setOpenAssignModal(true)}>
              任务明细
            </Button>
            <Button
              type="primary"
              loading={loadingWord}
              className="mr-2"
              onClick={handleDownLoadWord}
            >
              下载专家总结为word
            </Button>
          </div>
          {tableShow && (
            <Table
              dataSource={dataSource}
              columns={columns}
              pagination={false}
            />
          )}
        </ProForm>
      </BlockContainer>

      <Modal
        title="详情"
        open={openDetail1}
        footer={null}
        onCancel={() => {
          setOpenDetail1(false);
        }}
        width={600}
      >
        <div className="p-4">
          <div className="detail-title">发现的问题：</div>
          <div className="detail-txt">
            {problem !== '' && problem !== null ? problem : '无'}
          </div>
          <div className="detail-title">工作建议：</div>
          <div className="detail-txt">
            {advice !== '' && advice !== null ? advice : '无'}
          </div>
        </div>
      </Modal>

      {/* 任务分配 */}
      <Drawer
        width="90%"
        title="任务分配"
        onClose={() => closeEdit1()}
        open={openAssignModal}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
        maskClosable={false}
      >
        <Task close={() => closeEdit1()} detailId={baseInfo?.id} />
      </Drawer>
    </>
  );
};

export default TableSelect;
