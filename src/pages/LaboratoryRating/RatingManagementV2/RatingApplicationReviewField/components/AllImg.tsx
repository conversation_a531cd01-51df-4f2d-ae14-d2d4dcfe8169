/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { message, Modal, Tree } from 'antd';
import type { DataNode, TreeProps } from 'antd/es/tree';
import {
  FormInstance,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import EFileView from '@/components/EFileView';

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit?: (params: any) => void;
  onRef?: any;
  mode?: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<any> = ({
  baseInfo,
  onSubmit,
  onRef,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);

  type DataSourceType = {
    canEdit: boolean;
    id: React.Key;
    title?: string;
    readonly?: string;
    receiveMan?: string;
    decs?: string;
    state?: number;
    created_at?: number;
    update_at?: number;
    children?: DataSourceType[];
  };

  const defaultData: DataSourceType[] = [];

  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const [isEdit, setIsEdit] = useState<boolean>(mode === 'edit');



  // 树形菜单数据
  const treeData: DataNode[] = [
    {
      title: 'parent 1',
      key: '0-0',
      children: [
        {
          title: 'parent 1-0',
          key: '0-0-0',
          children: [
            {
              title: 'leaf',
              key: '0-0-0-0',
            },
            {
              title: 'leaf',
              key: '0-0-0-1',
            },
          ],
        },
        {
          title: 'parent 1-1',
          key: '0-0-1',
          children: [{ title: <span style={{ color: '#1890ff' }}>sss</span>, key: '0-0-1-0' }],
        },
      ],
    },
  ];
  function init() {
    const editKeys = baseInfo?.taskDetails
      .filter((item: any) => item.status !== '1' && item.status !== 1)
      .map((item: any) => item.id);
    setEditableRowKeys(editKeys);
    setIsEdit(mode === 'edit' && editKeys.length > 0);
    formRef.current?.setFieldValue(
      'table',
      baseInfo?.taskDetails?.map((item: any) => {
        item.receiptDate = item.receiptDate ?? dayjs();
        return item;
      }) || []
    );
  }


  // 选择表头多选框
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  const [fileUrl, setFileUrl] = useState<any>();

  const onSelect: TreeProps['onSelect'] = (selectedKeys, info) => {
    console.log('selected', selectedKeys, info);
  };

  const onCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
    console.log('onCheck', checkedKeys, info);
  };



  useEffect(() => {
   
  }, []);

  return (
    <>
      <BlockContainer>
        <div className='d-flex'>
          <div>
          <Tree
            
            defaultExpandedKeys={['0-0-0', '0-0-1']}
            defaultSelectedKeys={['0-0-0', '0-0-1']}
            defaultCheckedKeys={['0-0-0', '0-0-1']}
            onSelect={onSelect}
            onCheck={onCheck}
            treeData={treeData}
          />
          </div>
          <div>
   
          </div>
        </div>
      </BlockContainer>
    
    </>
  );
};

export default ReceiveForm;
