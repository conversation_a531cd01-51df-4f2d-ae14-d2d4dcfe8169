/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { message, Modal, Spin, Upload } from 'antd';
import {
  downloadFile,
  getFileData,
  getFileOssObjApi,
  uploadFiles,
} from '@/api/file';
import { attachment } from '@/api/recept';
import { useTokenStore } from '@/store';
import {
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import BlockContainer from '@/components/BlockContainer';
import FileViewByStream from '@/components/FileViewByStream';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TRoleDetailProps = {
  taskId: string;
  assessedLabId: string;
  file?: string;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
};

const Attachment: React.FC<TRoleDetailProps> = ({
  taskId,
  assessedLabId,
  file = '',
  setIsUploadReceipt,
}) => {
  const f = file && file.includes('?') ? file.split('?') : '';
  const [fileList, setFileList] = useState<any>(
    f
      ? [
          {
            uid: '1',
            name: f[1],
            status: 'done',
            url: '',
            response: {
              data: {
                ossId: f[0],
                fileName: f[1],
              },
            },
          },
        ]
      : []
  );
  // 文件预览
  const [downloading, setDownloading] = useState<any>(false);
  const [previewFile, setPreviewFile] = useState<any>('');
  const [openPreview, setOpenPreview] = useState<any>(false);
  const { token } = useTokenStore();

  const [previewFileId, setPreviewFileId] = useState<string>('');

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = async (info: any) => {
    const { file, fileList } = info;
    setFileList(fileList);
    if (file.status === 'removed') {
      await attachment({
        receiptFile: '',
        taskId: taskId,
        assessedLabId: assessedLabId,
      });
      setIsUploadReceipt(false);
    }
    if (file.status === 'done') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        if (file.status === 'done') {
          file.status = 'uploading';
          setFileList([file]);
          const res = await attachment({
            receiptFile: `${file.response.data.ossId}?${file.response.data.fileName}`, // 格式： ossId?fileName
            taskId: taskId,
            assessedLabId: assessedLabId,
          });
          if (res.code === 200) {
            message.success(res.msg);
            file.status = 'done';
            setFileList([file]);
            setIsUploadReceipt(true);
          } else {
            setFileList([]);
            message.error(res.msg);
          }
        }
      }
    }
  };
  return (
    <>
      <BlockContainer title="收样回执单">
        <Upload
          accept="image/*,.pdf"
          action={uploadFiles}
          capture={false}
          listType="picture"
          itemRender={(
            originNode: any,
            file: any,
            fileList: object[],
            actions: { download: any; preview: any; remove: any }
          ) => {
            return (
              <div
                className="relative flex flex-col items-center justify-center border rounded text-xs cursor-pointer py-2 max-w-[200px] group"
                style={{
                  border: '1px solid #eee',
                }}
              >
                {file.status === 'uploading' && (
                  <div className="w-full h-full absolute flex items-center justify-around bg-[rgba(0,0,0,0.2)]">
                    <Spin />
                  </div>
                )}
                <img
                  src={getIconByName(file.name)}
                  className="!w-[40px] !h-[40px] m-auto mt-2"
                  alt="logo"
                />
                <div className="mt-2 overflow-hidden break-all px-1 text-gray-600">
                  {file.name}
                </div>
                {file.status === 'done' && (
                  <div className="hidden w-full h-full absolute  items-center justify-around bg-[rgba(0,0,0,0.2)] group-hover:flex">
                    {downloading ? (
                      <LoadingOutlined />
                    ) : (
                      <>
                        <EyeOutlined
                          className="text-white text-xl hover:text-blue-500"
                          onClick={() => {
                            actions.preview();
                          }}
                        />
                        <DownloadOutlined
                          className="text-white text-xl hover:text-blue-500"
                          onClick={() => {
                            actions.download();
                          }}
                        />
                        <DeleteOutlined
                          className="text-white text-xl hover:text-blue-500"
                          onClick={() => {
                            actions.remove();
                          }}
                        />
                      </>
                    )}
                  </div>
                )}
              </div>
            );
          }}
          maxCount={1}
          fileList={fileList}
          onDownload={async (file: any) => {
            if (
              file.status === 'done' &&
              file.response &&
              file.response.data &&
              file.response.data.ossId
            ) {
              setDownloading(true);
              const { ossId, fileName } = file.response.data;
              downloadFile(ossId, fileName);
              setTimeout(() => {
                setDownloading(false);
              }, 3000);
            }
          }}
          onPreview={async (file: any) => {
            if (
              file.status === 'done' &&
              file.response &&
              file.response.data &&
              file.response.data.ossId
            ) {
              setDownloading(true);
              const type = getFileTypeByName(file.response.data.fileName);
              console.log('file', file);
              setPreviewFileId(file?.response?.data?.ossId);

              if (type === 'Image') {
                const d = await getFileData(file.response.data.ossId);
                setPreviewFile({
                  url: d,
                  name: file.response.data.fileName,
                  ossId: file.response.data.ossId,
                });
                setOpenPreview('img');
              } else {
                const { code, data, msg } = await getFileOssObjApi(
                  file.response.data.ossId
                );
                if (code === 200) {
                  if (data && data.length) {
                    setPreviewFile(data[0].url);
                    setOpenPreview('pdf');
                  } else {
                    message.error(msg);
                  }
                }
                // const url = await getFileData(file.response.data.ossId);
                // setPreviewFile(url);
                // setOpenPreview('pdf');
              }
              setDownloading(false);
            }
          }}
          onChange={(info) => {
            handleUploadFiles(info);
          }}
          headers={{
            Authorization: `Bearer ${token}`,
          }}
          iconRender={(file) => {
            return (
              <img
                src={getIconByName(file.name)}
                className="!w-[40px] !h-[40px] m-auto mt-2"
                alt="logo"
              />
            );
          }}
          className="upload-list-inline"
        >
          {fileList.length >= 1 ? null : (
            <div
              className="flex flex-col items-center justify-center border w-[100px] h-[100px] rounded text-xs cursor-pointer"
              style={{
                border: '1px solid #eee',
              }}
            >
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>点击上传</div>
            </div>
          )}
        </Upload>
        <div className="pt-3 text-[red] text-sm">
          注：请上传回执单扫描件，支持上传PDF或图片文件，最多支持上传1份文件，文件大小不得超过10M；
        </div>
        <Modal
          width="60%"
          title="文件预览"
          onCancel={() => setOpenPreview(false)}
          open={openPreview}
          footer={null}
          destroyOnClose
        >
          <FileViewByStream fileId={previewFileId} isPreview />
        </Modal>
      </BlockContainer>
    </>
  );
};

export default Attachment;
