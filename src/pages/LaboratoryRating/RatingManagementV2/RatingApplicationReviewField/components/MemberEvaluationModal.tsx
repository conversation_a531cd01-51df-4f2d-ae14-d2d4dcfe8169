import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Rate, Space, Table } from 'antd';
import type { TableProps } from 'antd';
import { queryEvaluationList, submitEvaluationList } from '@/api/recept';

// 定义表格每一行的数据结构
interface EvaluationDataType {
  cooperationLevel: number; // 配合度评分
  professionalism: number; // 专业性评分
  objectivityScore: number; // 客观性评分
  remarks: string | null; // 备注
  memberId: string; // 组员ID
  memberName: string; // 组员姓名
  docTaskId: string; // 任务ID
  evaluationDate?: string | null; // 评价日期
  leaderId: string; // 组长ID
  leaderName: string; // 组长姓名
}

// 定义组件的 Props 接口
export interface MemberEvaluationModalProps {
  open: boolean;
  taskId: string;
  onCancel: () => void;
  onSave: (data: EvaluationDataType[]) => void;
}

const MemberEvaluationModal: React.FC<MemberEvaluationModalProps> = ({
  open,
  taskId,
  onCancel,
  onSave,
}) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();
  // 原始数据
  const [originalData, setOriginalData] = useState<EvaluationDataType[]>([]);

  // 初始化或重置表格数据
  const initializeData = async () => {
    try {
      const { code, data, msg } = await queryEvaluationList(taskId);
      if (code === 200) {
        setOriginalData(data); // 保存原始数据
        // 直接设置 Antd Form 的值，这是唯一的数据源
        form.setFieldsValue({ evaluations: data });
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 使用 useEffect 在模态框打开时初始化数据
  useEffect(() => {
    if (open) {
      initializeData();
    }
  }, [open]);

  // 点击“保存”按钮的处理器
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formData = values.evaluations as Partial<EvaluationDataType>[];
      // 合并表单数据和原始数据
      const mergedData = originalData.map((item, index) => ({
        ...item,
        ...formData[index], // 表单中更新的字段（会覆盖原始数据中的同名字段）
      }));
      const { code, data, msg } = await submitEvaluationList(mergedData);
      if (code === 200) {
        messageApi.success('评价成功');
        onCancel();
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      console.log('评价失败:', error);
    }
  };

  // 定义表格的列
  const columns: TableProps<EvaluationDataType>['columns'] = [
    {
      title: '序号',
      dataIndex: 'serial',
      key: 'serial',
      width: 70,
      render: (_, record, index) => <span>{index + 1}</span>,
    },
    {
      title: '组员',
      dataIndex: 'memberName',
      key: 'memberName',
      width: 200,
    },
    {
      title: '配合度',
      dataIndex: 'cooperationLevel',
      key: 'cooperationLevel',
      width: 200,
      render: (_, record, index) => (
        <div className="flex items-center justify-start gap-2 min-w-[120px] whitespace-nowrap">
          {/* 将 Rate 包裹在 Form.Item 中，由 Form 自动处理状态 */}
          <Form.Item name={['evaluations', index, 'cooperationLevel']} noStyle>
            <Rate count={5} className="text-[18px]" />
          </Form.Item>
          {/* 使用 Form.Item 的 shouldUpdate 功能，实现局部高效更新 */}
          <Form.Item
            noStyle
            shouldUpdate={(prev, cur) =>
              prev.evaluations[index]?.cooperationLevel !==
              cur.evaluations[index]?.cooperationLevel
            }
          >
            {({ getFieldValue }) => {
              const score = getFieldValue([
                'evaluations',
                index,
                'cooperationLevel',
              ]);
              return score > 0 ? <span>{score}星</span> : null;
            }}
          </Form.Item>
        </div>
      ),
    },
    {
      title: '专业性',
      dataIndex: 'professionalism',
      key: 'professionalism',
      width: 200,
      render: (_, record, index) => (
        <div className="flex items-center justify-start gap-2 min-w-[120px] whitespace-nowrap">
          <Form.Item name={['evaluations', index, 'professionalism']} noStyle>
            <Rate count={5} className="text-[18px]" />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prev, cur) =>
              prev.evaluations[index]?.professionalism !==
              cur.evaluations[index]?.professionalism
            }
          >
            {({ getFieldValue }) => {
              const score = getFieldValue([
                'evaluations',
                index,
                'professionalism',
              ]);
              return score > 0 ? <span>{score}星</span> : null;
            }}
          </Form.Item>
        </div>
      ),
    },
    {
      title: '客观性',
      dataIndex: 'objectivityScore',
      key: 'objectivityScore',
      width: 200,
      render: (_, record, index) => (
        <div className="flex items-center justify-start gap-2 min-w-[120px] whitespace-nowrap">
          <Form.Item name={['evaluations', index, 'objectivityScore']} noStyle>
            <Rate count={5} className="text-[18px]" />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prev, cur) =>
              prev.evaluations[index]?.objectivityScore !==
              cur.evaluations[index]?.objectivityScore
            }
          >
            {({ getFieldValue }) => {
              const score = getFieldValue([
                'evaluations',
                index,
                'objectivityScore',
              ]);
              return score > 0 ? <span>{score}星</span> : null;
            }}
          </Form.Item>
        </div>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      render: (_, record, index) => (
        <Form.Item name={['evaluations', index, 'remarks']} noStyle>
          <Input placeholder="请输入" allowClear />
        </Form.Item>
      ),
    },
  ];

  return (
    <>
      {contextHolder}
      <Modal
        title="组员评价"
        open={open}
        onCancel={onCancel}
        width={1200}
        destroyOnClose
        footer={
          <Space className="flex justify-center">
            <Button type="primary" onClick={handleSubmit}>
              保存
            </Button>
            <Button onClick={onCancel}>关闭</Button>
          </Space>
        }
      >
        {/* 
        使用 component={false} 让 Form 不渲染额外的 DOM 节点。
        Table 的 dataSource 将由 Form 内部的 shouldUpdate 机制驱动。
      */}
        <Form form={form} component={false}>
          <Form.Item shouldUpdate>
            {({ getFieldValue }) => {
              const dataSource = getFieldValue('evaluations') || [];
              return (
                <Table
                  columns={columns}
                  dataSource={dataSource}
                  bordered
                  pagination={false}
                  rowKey="docTaskId"
                  scroll={{ x: 500 }}
                />
              );
            }}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default MemberEvaluationModal;
