/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message,  Form,  Modal,Spin,Input, Select,Col,Table, Row, Space} from 'antd';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormText,ProFormSelect,
  FormInstance,ProTable,
} from '@ant-design/pro-components';

import { FormInitVal, formItemLayout } from './data';
import { Any } from '@react-spring/web';

import {
  getTaskAllocationBasicMsg,gradeConfig,
  getUnAllocationIndex,getAllocationIndex,
  selectAllocationGroup,confirmAllocation,
} from '@/api/apply';


const { TextArea } = Input;

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  setIsUploadReceipt?: (isUploadReceipt: boolean) => void;
  mode?: 'edit' | 'view'; // 编辑模式或查看模式
};

const Task: React.FC<TEditProps> = ({
  close,
  detailId,
  setIsUploadReceipt,
  dialogTitletext,
  
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  // 通过弹窗是否显示
  const [isShowPass, setIsShowPass] = useState<boolean>(false);
  const [gradeList, setGradeList] = useState<any>([]);


  const [isShowNotPass, setIsShowNotPass] = useState<boolean>(false);


  const [isShowSendBack, setIsShowSendBack] = useState<boolean>(false);


  const [activeKey, setActiveKey] = useState('0');

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const actionRef = useRef<any>();
  const columns = [
    {
      disable: true,
      title: '一级类别',
      dataIndex: 'lab1',
    },

    {
      title: '二级类别',
      dataIndex: 'lab2',
    },

    {
      title: '指标名称',
      dataIndex: 'lab',
      disable: true,
    },
    {
      disable: true,
      title: '指标分数',
      hideInSearch: true,
      dataIndex: 'score',
    },

    {
      disable: true,
      title: '自评得分',
      hideInSearch: true,
      dataIndex: 'selfScore',
    },
    

 
  ];

  useEffect(() => {
    gradeConfigData();
    // getUnAllocationIndexData()
  }, []);
    const gradeConfigData = async () => {
      try {
        const { code, data, msg } = await gradeConfig();
        
        const gradeListData = data.map((item: any) => {
          return {
            label: item.grade,
            value: item.id,
          };
        });
        setGradeList(gradeListData)
      } catch (err) {
        throw new Error(`Error: err`);
      } finally {
      }
    }




const columns1: any = [
  {
    title: '评审小组名称',
    dataIndex: 'name',
    // render: (text: string) => <a>{text}</a>,
  },
  {
    title: '专家姓名',
    dataIndex: 'auditName',
  },
  {
    title: '专家领域',
    dataIndex: 'professionalFieldCh',
  },
];


  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
  });


  const [pageSize, setPageSize] = useState<number>(10);
  const [selectedRows1, setSelectedRows] = useState<any[]>([]);

  const [selectedRows2, setSelectedRows2] = useState<any[]>([]);



const rowSelection:any = {
  onChange: (selectedRowKeys:any, selectedRows: any) => {
    setSelectedRows(selectedRows)
    // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
  },
  getCheckboxProps: (record: any) => ({
    name: record.name,
  }),
};


const rowSelection2:any = {
  onChange: (selectedRowKeys:any, selectedRows: any) => {
    setSelectedRows2(selectedRows)
  },
  getCheckboxProps: (record: any) => ({
    name: record.name,
  }),
};

  const selectGroupData = async () => {
    if (selectedRows1.length > 0) {
      setDataSource(selectedRows1); 
      const params = {
        subtask: selectedRows2[0],
        indexes: selectedRows1,
      };
       const { code, data, msg } = await confirmAllocation(params);
       if (code === 200) {
         message.success('分配成功');
          setOpenDetail(false);
          tableReload();
       }
      // setOpenDetail(false);
    } else {
      message.error('请选择至少一个专家');
    }
  }


  const [grounpData, setGrounpData] = useState<any[]>([]);

  const [expertName, setExpertName] = useState<any>("");




  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const [mainLoading1, setMainLoading1] = useState<boolean>(false);



  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getTaskAllocationBasicMsg(id);
      if (data) {
        // 确保 formRef 已经挂载
        setTimeout(() => {
        console.log(formRef.current, 111111);

           if (formRef.current) {
          // 检查 data 数据格式
          const formData = {
            labName: data.labName, // 假设 data 中有 labName 属性
            code: data.code, // 假设 data 中有 code 属性
            setId: data.setId // 假设 data 中有 setId 属性
          };
          formRef.current.setFieldsValue(formData);
        }
        }, 1000);
       
      }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode]);



const getSelectAllocationGroup = useCallback(async () => {
  setMainLoading1(true);
  try {
    if (id) {
      const params = {
        auditName: expertName,
      };
      const { code, data, msg } = await selectAllocationGroup(id, params);
      if (data) {
        // 确保 formRef 已经挂载
        setGrounpData(data);
      }
    }
  } catch (error) {
    throw new Error(`Error: ${error}`);
  } finally {
    setMainLoading1(false);
  }
}, [id, mode, expertName]); // 添加 expertName 到依赖项



  useEffect(() => {
    getDetailData();
    getSelectAllocationGroup()
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  const tableReload = () => {
    actionRef.current?.reload();
  };
  useEffect(() => {
    tableReload();
  }, [activeKey]);


  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  // 选择
  const onChange = (value: any) => {
    console.log(`selected ${value}`);
  };
  const [dataSource, setDataSource] = useState<any[]>([]);


const options = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
  },
];

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
      <div className="flex-1 overflow-auto p-4">
        <div className="mt-4">
          <ProForm
          readonly={true}
          formRef={formRef}
          {...formItemLayout}
          layout="horizontal"
          grid={true}
          submitter={false}
          initialValues={FormInitVal}
         
        >
          <Col  span={8}>
            <ProFormText
              name="labName"
              label="申请机构名称"
              readonly={true}
            />
          </Col >
          <Col  span={8}>
            <ProFormText
              name="code"
              label="申请编号"
              readonly={true}
            />
          </Col >
          <Col  span={8}>
            <ProFormSelect
              name="setId"
              options={gradeList}
              label="申请等级"
              readonly={true}
            />
          </Col >
        </ProForm>

        
        <ProTable
          rowSelection={{ type: 'checkbox', ...rowSelection }}
          actionRef={actionRef}
          columns={columns}
          toolbar={{
            menu: {
              type: 'tab',
              activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待分配</span>,
                  // （{counts[0]}）
                },
                {
                  key: '1',
                  label: <span>已分配</span>,
                  // （{counts[1]}）
                },
              ],
              onChange: (key) => setActiveKey(key as string),
            },
          }}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            let datas:any;
            if (activeKey === '0') {
            const { code, data, msg } = await getUnAllocationIndex(id, params);
              datas = data
            } else {
              const { code, data, msg } = await getAllocationIndex(id, params);
              datas = data
            }
         
            return {
              data: datas.rows ?? [],
              total: datas.length ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 120,
          }}
          form={{
            syncToUrl: false,
            resetOnTabChange: true,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            defaultPageSize: 10,
            defaultCurrent: 1,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="input-sample"
              type="primary"
              onClick={() => {
                setOpenDetail(true);
              }}
              disabled={selectedRows1.length === 0}
            >
              任务分配
            </Button>,
            <Button
            onClick={() => {
            }}
            >
            导出明细
            </Button>,
          ]}
        />
        </div>
      </div>
   
      )}

     <Modal
        title="任务分配"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={900}>
          <div className="p-4">
            <div className='mb-2'>提示：每次只能同时对一家申请机构进行分配任务，以下专家数据来源于评审小组。</div>
            <Form
              name="basic"
              labelCol={{ span: 0}}
              wrapperCol={{ span: 16 }}
              style={{ maxWidth: 900 }}
            >
              <Form.Item<any>
                label=""
                name="username"
                className='form-wraper'
              >
                <div className='d-flex w-100'>
                  <div className='label-width'>专家名称：</div>
                  
                  <Input  value={expertName}  
                    size='small'
                    onChange={(e:any) => {
                     const value = e.target.value;
                      console.log(value, "e.target.value");
                      setExpertName(value)
                    }} />
                  <Button type="primary"  className='btn-search' onClick={() => {
                    getSelectAllocationGroup()
                  }}>
                    搜索
                  </Button>
                  </div>
              </Form.Item>
            </Form>
            {mainLoading1 ? (
              <Spin tip="加载中" size="large" className="mt-[5vh]">
                <div className="content" />
              </Spin>
            ) : (

                  <Table
                    rowSelection={{ type: 'radio', ...rowSelection2 }}
                    columns={columns1}
                    dataSource={grounpData}
                    rowKey="id"
                  />
            )}
            <div className="flex justify-end space-x-2">
              <Button type="primary" 
              disabled={selectedRows2.length === 0}
              onClick={() => selectGroupData()}>
                确认选择
              </Button>
              <Button onClick={() => setOpenDetail(false)}>
                取消
              </Button>
            </div>
          </div>

      </Modal>

     
    </div>


  );
};

export default Task;
