/* eslint-disable eqeqeq */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Alert, Button, Drawer, Form, Input, message, Modal, Rate } from 'antd';
import {
  fieldReviewPage,
  getGradeConfig,
  gradeConfigAllSet,
} from '@/api/apply';
import { getTaskBySampleNum, handleConfirmTask } from '@/api/recept';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import MemberEvaluationModal from './components/MemberEvaluationModal';
import Task from './components/Task';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import './index.less';

const { TextArea } = Input;
export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();

  const [messageApi, contextHolder] = message.useMessage();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();

  const [pageSize, setPageSize] = useState<number>(10);

  // 接收
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const [isAdd, setIsAdd] = useState<boolean>(false);

  //任务分配弹窗是否显示
  const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const [dialogTitletext, setDialogTitletext] = useState<string>('文审终核');

  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  // 是否已上传回执单
  const [isUploadReceipt, setIsUploadReceipt] = useState<boolean>(false);
  // 是否打开上传回执单询问 Modal
  const [openUploadReceiptModal, setOpenUploadReceiptModal] =
    useState<boolean>(false);

  // 录入样品编号相关状态
  const [openInputSampleCodeModal, setOpenInputSampleCodeModal] =
    useState<boolean>(false);

  //评价弹窗是否显示设置
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);

  const [sampleCode, setSampleCode] = useState<string>('');
  const [searchResult, setSearchResult] = useState<Array<QualityTaskItem>>([]);
  const [searchError, setSearchError] = useState<string>('');
  const [showSearchResult, setShowSearchResult] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<Record<string, any>>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [activeKey, setActiveKey] = useState('0');
  const [gradeList, setGradeList] = useState<any>([]);

  const [staute, setStaute] = useState<any>('');

  useEffect(() => {
    gradeConfigData();
  }, []);
  const gradeConfigData = async () => {
    try {
      const { code, data, msg } = await gradeConfigAllSet();

      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setGradeList(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '进度状态',
      hideInSearch: true,
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
      render: (text, record, _, action) => {
        if (activeKey === '0') {
          if (record.subtaskFlag === '1') {
            return <div>现场评审_待组员评审</div>;
          } else if (record.subtaskFlag === '0') {
            return <div>现场评审_待组长确认</div>;
          }
        } else {
          if (record.subtaskFlag === '1') {
            return <div>现场评审_组员已提交</div>;
          } else if (record.subtaskFlag === '0') {
            return <div>现场评审_组长已确认</div>;
          }
        }
      },
    },

    {
      title: '申请编号',
      dataIndex: 'endDate',
      render: (text, record, _, action) => {
        return (
          <div
            className="link-btn"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            {record.code}
          </div>
        );
      },
    },
    {
      disable: true,
      title: '申请机构',
      dataIndex: 'labName',
    },
    {
      disable: true,
      title: '申请等级',
      dataIndex: 'setId',
      valueType: 'select',
      fieldProps: {
        options: gradeList,
      },
    },
    {
      disable: true,
      title: '自评得分',
      hideInSearch: true,
      dataIndex: 'selfScore',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交日期',
      dataIndex: 'submitDate',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交人',
      dataIndex: 'submitter',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        let btns = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
              setStaute(record.subtaskFlag);
            }}
          >
            查看
          </Button>,
        ];
        if (record.subtaskFlag == 0) {
          btns.push(
            <Button
              type="link"
              size="small"
              key="evaluation"
              onClick={() => {
                setDetailId(record.id);
                setIsEvaluation(true);
              }}
            >
              组员评价
            </Button>
          );
        }
        const btns3 = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDialogTitletext('查看');
              setStaute(record.subtaskFlag);
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>,
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              if (record.subtaskFlag == 1) {
                setDialogTitletext('现场评审-组员评审');
              } else if (record.subtaskFlag == 0) {
                setDialogTitletext('现场评审-组长确认');
              }
              setDetailId(record.id);
              setStaute(record.subtaskFlag);
              setOpenEdit(true);
            }}
          >
            办理
          </Button>,
        ];
        if (activeKey == '1') {
          return btns;
        } else if (activeKey == '0') {
          return btns3;
        }
      },
    },
  ];

  // 评价弹窗
  const [isEvaluation, setIsEvaluation] = useState<boolean>(false);

  // 打开评价弹窗
  const handleEvaluation = () => {
    setIsEvaluation(false);
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    setParamsCache('refreshCount');
    tableReload();
  };
  /**
   * @TODO 关闭任务分配
   */
  const closeEdit1 = () => {
    setOpenAssignModal(false);
    setParamsCache('refreshCount');
    tableReload();
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    // 重置分页状态
    // actionRef.current?.reset?.();
    // 然后重新加载数据
    actionRef.current?.reload();
  };

  useEffect(() => {
    tableReload();
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);

  // 查询样品编号
  const handleSearchSample = async () => {
    if (!sampleCode.trim()) {
      messageApi.error('请输入样品编号');
      return;
    }

    try {
      // 清除之前的错误信息
      setSearchError('');

      // 这里应该调用实际的API查询样品编号
      const { code, data, msg } = await getTaskBySampleNum({
        sampleCode,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
      }

      setSearchResult(data);
      setShowSearchResult(true);
    } catch (error) {
      setSearchResult([]);
      setShowSearchResult(true);
      setSearchError('编号确实不存在，请确认编号是否输入正确');
    }
  };

  // 确认任务
  const confirmTask = async (currentTask: Record<string, any>) => {
    setConfirmLoading(true); // 开始loading
    try {
      const { code, msg } = await handleConfirmTask({
        ...currentTask,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('任务确认成功');
      setShowSearchResult(false);
      setOpenInputSampleCodeModal(false);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setConfirmLoading(false); // 结束loading
      // finally todo ...
    }
  };

  // 添加useEffect监听Modal打开状态变化
  useEffect(() => {
    if (openInputSampleCodeModal) {
      setSampleCode('');
      setSearchResult([]);
      setSearchError('');
      setShowSearchResult(false);
      setSelectedTask({});
    }
  }, [openInputSampleCodeModal]);

  return (
    <>
      {contextHolder}
      <PageContainer>
        <ProTable<QualityTaskItem>
          columns={columns}
          actionRef={actionRef}
          // key={activeKey}

          toolbar={{
            menu: {
              type: 'tab',
              activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待办理</span>,
                  // （{counts[0]}）
                },
                {
                  key: '1',
                  label: <span>已办理</span>,
                  // （{counts[1]}）
                },
              ],
              onChange: (key) => setActiveKey(key as string),
            },
          }}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            await waitTime(100);

            const param = {
              ...params,
              pageNum: params.current || 1,
              pageSize: params.pageSize || pageSize,
              current: undefined,
              processed: activeKey,
            };
            const { code, data, msg } = await fieldReviewPage(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              messageApi.error(msg);
            }
            return {
              data: data.rows ?? [],
              total: data.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 120,
          }}
          form={{
            syncToUrl: false,
            resetOnTabChange: true,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            defaultPageSize: 10,
            defaultCurrent: 1,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle=""
        />

        {/* 新增,详情，编辑 */}
        <Drawer
          width="90%"
          title={dialogTitletext}
          onClose={() => closeEdit()}
          open={openEdit}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Edit
            close={() => closeEdit()}
            staute={staute}
            detailId={detailId}
            dialogTitletext={dialogTitletext}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={isAdd === false ? 'edit' : 'view'}
          />
        </Drawer>

        {/* 任务分配 */}
        <Drawer
          width="90%"
          title="任务分配"
          onClose={() => closeEdit1()}
          open={openAssignModal}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Task
            close={() => closeEdit1()}
            detailId={detailId}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={isAdd === false ? 'edit' : 'view'}
          />
        </Drawer>

        <Modal
          title="操作提示"
          centered
          width={360}
          open={openUploadReceiptModal}
          okText="继续上传回执单"
          cancelText="不需要上传回执单"
          onOk={() => {
            setOpenUploadReceiptModal(false);
          }}
          onCancel={() => {
            setOpenUploadReceiptModal(false);
            closeEdit();
          }}
          zIndex={9999}
          maskClosable={false}
        >
          <div>请再次确认本任务是否需要上传回执单</div>
        </Modal>
      </PageContainer>
      {/* 组员评价弹窗 */}
      <MemberEvaluationModal
        open={isEvaluation}
        taskId={detailId}
        onCancel={() => setIsEvaluation(false)}
        onSave={() => tableReload()}
      />
    </>
  );
};
export default QualityTask;
