/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Upload,
} from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import BlockContainer from '@/components/BlockContainer';
import { backDocTask } from '@/api/apply';
import { codeDefinition } from '@/constants';
import Task from './Task';

const CheckboxGroup = Checkbox.Group;

type TEditProps = {
  loading?: boolean;
  detailInfo?: any;
  id?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const TableSelect: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  onRef,
  detailInfo,
  id,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });
  const actionRef = useRef<Any>();

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [openRandomDetail, setOenRandomDetail] = useState<boolean>(false);

  const [tableShow, setTableShow] = useState<boolean>(true);
  const [tableShow1, setTableShow1] = useState<boolean>(true);

  const [dataSource, setDataSource] = useState<any[]>([]);

  const [selectOption, setSelectOption] = useState<any[]>([]);

  const [selectedRows1, setSelectedRows] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowsData, setSelectedRowsData] = useState<any[]>([]);

  const [plainOptions, setPlainOptions] = useState<any[]>([]);

  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);

  const [dataSource1, setDataSource1] = useState<any[]>([]);

  //任务分配弹窗是否显示
  const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);
  //退回给组员弹窗显示
  const [openBackModal, setOpenBackModal] = useState<boolean>(false);
  //退回原因
  const [backReason, setBackReason] = useState<string>('');
  //退回加载状态
  const [backLoading, setBackLoading] = useState<boolean>(false);

  useEffect(() => {}, []);

  useEffect(() => {
    const initialData1 = [
      {
        name: '123',
        age: false,
        address: 0,
        address1: '123',
        id: 1,
      },
      {
        name: '123',
        age: false,
        address: 0,
        address1: '0',
        id: 2,
      },
      {
        name: '123',
        age: false,
        address: 0,
        address1: '123',
        id: 3,
      },
    ];
    setDataSource1(initialData1);
    if (detailInfo?.docReviews?.length > 0) {
      console.log(detailInfo.docReviews, 555555555);
      setDataSource(detailInfo.docReviews);
    }
  }, [detailInfo]);
  const columns: any = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '专家姓名',
      dataIndex: 'auditName',
      hideInSearch: true,
    },
    {
      title: '文审结论',
      dataIndex: 'docAuditResult',
      hideInSearch: true,
    },
    {
      title: '文审提交状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (text: any, record: any, _: any, action: any) => {
        if (record.status === 1) {
          return <div>已提交</div>;
        }
        if (record.status === 0) {
          return (
            <div>
              <a className="text-red-500">未提交</a>
            </div>
          );
        }
      },
    },
    {
      title: '退回原因',
      dataIndex: 'backReason',
      hideInSearch: true,
      render: (text: string) => text || '-',
    },
  ];

  //选择小组表格渲染

  const columns1: any = [
    {
      title: '专家姓名',
      dataIndex: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: '所属单位',
      dataIndex: 'age',
    },
    {
      title: '专家领域',
      dataIndex: 'address',
    },
  ];

  const columns2: any = [
    {
      title: '专家姓名',
      dataIndex: 'name',
      readonly: true,
    },
    {
      title: '设置组长',
      dataIndex: 'age',

      renderFormItem: (text: any, record: any, _: any, action: any) => (
        <Checkbox
          checked={record.record.age}
          onChange={(e) => setLeader(e, record.record)}
        />
      ),
      // render: (text:any, record:any, _:any, action:any)  => <Checkbox/>,
    },
    {
      title: '所属单位',
      dataIndex: 'address',
      readonly: true,
    },

    {
      title: '专业领域',
      dataIndex: 'address1',
      readonly: true,
    },
  ];

  const grounpData: any = [
    {
      key: '1',
      name: 'John Brown',
      age: 32,
      address: 'New York No. 1 Lake Park',
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address: 'London No. 1 Lake Park',
    },
    {
      key: '3',
      name: 'Joe Black',
      age: 32,
      address: 'Sydney No. 1 Lake Park',
    },
    {
      key: '4',
      name: 'Disabled User',
      age: 99,
      address: 'Sydney No. 1 Lake Park',
    },
  ];

  const rowSelectionForModal: any = {
    onChange: (selectedRowKeysParam: any, selectedRows: any) => {
      setSelectedRows(selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeysParam}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record: any) => ({
      name: record.name,
    }),
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 选择
  const onChange = (value: any) => {
    console.log(`selected ${value}`);
  };

  const selectGroupData = () => {
    if (selectedRows1.length > 0) {
      setDataSource(selectedRows1);
      setOpenDetail(false);
    } else {
      message.error('请选择至少一个专家');
    }
  };

  // 设置组长,选中当前行 其他行取消选中
  const setLeader = (value: any, row: any) => {
    if (row) {
      const updatedRows = dataSource1.map((row1: any) => {
        if (row1.id === row.id) {
          return { ...row1, age: true }; // 选中的行设置为组长
        } else {
          return { ...row1, age: false }; // 其他行取消选中
        }
      });
      setTableShow1(false);
      setDataSource1(updatedRows);
      setTimeout(() => {
        setTableShow1(true);
      });
    }
  };

  /**
   * @TODO 关闭任务分配
   */
  const closeEdit1 = () => {
    setOpenAssignModal(false);
    // tableReload();
  };

  /**
   * @TODO 退回给组员
   */
  const handleBackToMember = async () => {
    if (!backReason.trim()) {
      message.error('请填写退回原因');
      return;
    }
    
    if (selectedRowKeys.length === 0) {
      message.error('请选择要退回的数据');
      return;
    }
    
    setBackLoading(true);
    try {
      // 构造List结构的数据
      const backList = selectedRowKeys.map(key => ({
        id: key,
        backReason: backReason.trim()
      }));
      
      const { code, msg } = await backDocTask(backList);
      
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('退回成功');
        setOpenBackModal(false);
        setBackReason('');
        setSelectedRowKeys([]);
        setSelectedRowsData([]);
        // 刷新页面数据
        window.location.reload();
      } else {
        message.error(msg || '退回失败');
      }
    } catch (error) {
      message.error('退回失败，请稍后重试');
      console.error('退回错误:', error);
    } finally {
      setBackLoading(false);
    }
  };

  /**
   * @TODO 关闭退回弹窗
   */
  const closeBackModal = () => {
    setOpenBackModal(false);
    setBackReason('');
  };

  return (
    <>
      <BlockContainer title="材料文审">
        <ProForm<{ table: Any[] }>
          formRef={formRef}
          initialValues={{ table: [] }}
          submitter={false}
          onFinish={handleSave}
        >
          <div className="btn-wraper">
            <Button
              className="mr-2"
              disabled={selectedRowKeys.length === 0}
              onClick={() => setOpenBackModal(true)}
            >
              退回给组员
            </Button>
            <Button
              type="primary"
              className="mr-2"
              onClick={() => setOpenAssignModal(true)}
            >
              任务明细
            </Button>
          </div>
          {tableShow && (
            <Table
              rowKey="id"
              dataSource={dataSource}
              columns={columns}
              pagination={false}
              rowSelection={{
                selectedRowKeys,
                onChange: (keys: string[], rows: any[]) => {
                  setSelectedRowKeys(keys);
                  setSelectedRowsData(rows);
                },
              }}
            />
          )}
        </ProForm>
      </BlockContainer>

      <Modal
        title="评审小组选择"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={600}
      >
        <div className="p-4">
          <Form
            name="basic"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 600 }}
          >
            <Form.Item<any> label="小组名称" name="username">
              <Select onChange={onChange} options={selectOption} />
            </Form.Item>
          </Form>

          <Table
            rowSelection={{ type: 'checkbox', ...rowSelectionForModal }}
            columns={columns1}
            dataSource={grounpData}
          />

          <div className="flex justify-end space-x-2">
            <Button type="primary" onClick={() => selectGroupData()}>
              确认选择
            </Button>
            <Button onClick={() => setOpenDetail(false)}>取消</Button>
          </div>
        </div>
      </Modal>
      {/* 任务分配 */}
      <Drawer
        width="90%"
        title="任务分配"
        onClose={() => closeEdit1()}
        open={openAssignModal}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
        maskClosable={false}
      >
        <Task close={() => closeEdit1()} detailId={id} />
      </Drawer>

      {/* 退回给组员弹窗 */}
      <Modal
        title="退回给组员"
        open={openBackModal}
        onOk={handleBackToMember}
        onCancel={closeBackModal}
        confirmLoading={backLoading}
        okText="确认退回"
        cancelText="取消"
        width={500}
        maskClosable={false}
      >
        <div className="py-4">
          <div className="mb-3">
            <span className="text-gray-600">已选择 {selectedRowKeys.length} 条数据</span>
          </div>
          <div className="mb-3">
            <span className="text-red-500">*</span>
            <span className="ml-1">退回原因：</span>
          </div>
          <Input.TextArea
            value={backReason}
            onChange={(e) => setBackReason(e.target.value)}
            placeholder="请填写退回原因"
            rows={4}
            maxLength={500}
            showCount
          />
          <div className="mt-3 text-gray-500 text-sm">
            <span className="text-blue-500">提示：</span>
            退回后组员可以修改已经提交的数据并重新提交
          </div>
        </div>
      </Modal>
    </>
  );
};

export default TableSelect;
