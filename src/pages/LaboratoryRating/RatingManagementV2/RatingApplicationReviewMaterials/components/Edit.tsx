/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, DatePicker, Form, Input, message, Modal, Spin } from 'antd';
import { docProcess, getGradedocReviewDetail } from '@/api/apply';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import dayjs from 'dayjs';
import ProcessLogWrapper from '@/components/ProcessLogWrapper';
import LabApplicationForm from '@/pages/LaboratoryRating/ApplicationManagement/RatingApplication/components/LabApplicationForm';
import Attachment from './Attachment';
import BaseForm from './BaseForm';
import ReceiveForm from './ReceiveForm';
import TableSelect from './TableSelect';

const { TextArea } = Input;

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  staute?: any;
  setIsUploadReceipt: (isUploadReceipt: boolean) => void;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId,
  setIsUploadReceipt,
  dialogTitletext,
  staute,
  mode,
}) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(mode === 'view');
  const [baseInfo, setBaseInfo] = useState<any>();
  // 通过弹窗是否显示
  const [isShowPass, setIsShowPass] = useState<boolean>(false);

  const [isShowNotPass, setIsShowNotPass] = useState<boolean>(false);

  const [isShowSendBack, setIsShowSendBack] = useState<boolean>(false);

  const [passValue, setPassValue] = useState<any>('');

  const [notPassValue, setNotPassValue] = useState<any>('');

  const [docComplementDate, setDocComplementDate] = useState<any>('');

  const [sendBackValue, setSendBackValue] = useState<any>('');

  const formRef5 = useRef<any>(null);

  const formRef6 = useRef<any>(null);

  const formRef7 = useRef<any>(null);

  // 定义复制函数
  const handleCopy = () => {
    const textToCopy =
      '经评审专家组对提交的实验室等级申请材料进行全面审核，确认材料完整、数据真实、内容符合《XXX实验室等级评定标准》（标准编号：XXX-202X）的相关要求，予以审核通过，进入下一阶段现场评审环节。';
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        message.success('复制成功');
      })
      .catch((error) => {
        message.error('复制失败，请重试');
        console.error('复制失败:', error);
      });
  };

  const [Id, setIds] = useState<any>(1);

  /**
   * @TODO 获取详情数据
   */
  const [mainLoading, setMainLoading] = useState<boolean>(false);

  const getDetailData = useCallback(async () => {
    setMainLoading(true);
    try {
      if (id) {
        const { code, data, msg } = await getGradedocReviewDetail(id);
        if (code === 200) {
          setBaseInfo(data);
          console.log(baseInfo, '详情1111');
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setMainLoading(false);
    }
  }, [id, mode]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  const handleSaveSubmit = async (list: any) => {
    try {
      setLoading(true);
      list.forEach((item: any) => {
        item.status = '1';
      });
      const realParams = {
        id: baseInfo.id,
        list: list,
      };
      const { code, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        getDetailData();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formRef3 = useRef<any>(null);

  const handleOpenPassDialog = async () => {
    if (formRef2.current.handleSubmit('submit')) {
      setIsShowPass(true);
      setPassValue('');
      formRef5.current?.setFieldsValue({ passRemark: '' });
    }
  };

  const handleSubmitPass = async () => {
    if (!passValue) {
      message.error('请输入通过原因');
      return;
    }
    handleReview('submit', passValue);
  };
  // 不通过

  const handleOpenRejectDialog = async () => {
    setNotPassValue('');
    formRef6.current?.setFieldsValue({ notPassRemark: '' });
    setIsShowNotPass(true);
  };

  const handleSubmitReject = async () => {
    if (!notPassValue) {
      message.error('请输入不通过原因');
      return;
    }
    handleReview('reject', notPassValue);
  };
  // 退回
  const handleOpenSendBackDialog = async () => {
    setSendBackValue('');
    formRef7.current?.setFieldsValue({ sendBackRemark: '' });
    formRef7.current?.setFieldsValue({ docComplementDate: '' });
    setIsShowSendBack(true);
  };
  // 退回
  const handleSubmitSendBack = async () => {
    if (!sendBackValue) {
      message.error('请输入退回原因');
      return;
    }

    if (!formRef7.current.getFieldValue('docComplementDate')) {
      message.error('请选择截止期限');
      return;
    }
    handleReview('back', sendBackValue);
  };

  const handleSave = async () => {
    handleReview('save', '');
  };

  const handleSaveSubmit1 = async () => {
    if (formRef2.current.handleSubmit('submit')) {
      handleReview('submit', '');
    }
  };

  const handleReview = async (stauts: String, word: any) => {
    setMainLoading(true);
    let realParams = {};
    const tableData = formRef2.current.handleSubmit();
    realParams = {
      ...baseInfo,
      status: 'docReview',
      indices: tableData,
      eventCode: stauts,
      reviewRemark: word,
    };
    if (stauts === 'back') {
      realParams = {
        ...baseInfo,
        status: 'docReview',
        indices: tableData,
        eventCode: stauts,
        reviewRemark: word,
        docComplementDate: dayjs(
          formRef7.current.getFieldValue('docComplementDate')
        ).format('YYYY-MM-DD'),
      };
    }
    const { code, data, msg } = await docProcess(realParams);
    if (code === 200) {
      if (stauts === 'save') {
        message.success('保存成功');
      } else {
        message.success('提交成功');
      }

      close();
    } else {
      message.error(msg);
    }
    setMainLoading(false);
    setIsShowPass(false);
    setIsShowNotPass(false);
    setIsShowSendBack(false);
  };

  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="flex flex-1 pb-[30px]">
          <div className="flex-1 overflow-auto p-6 w-80">
            {/* <BaseForm
              readonlyAll={true}
              readonly={true}
              hideRank={true}
              isShowChar={false}
              dialogTitletext={dialogTitletext}
              onRef={formRef1}
              id={id}
              detailInfo={baseInfo}
            /> */}
            <LabApplicationForm
              readonly={true}
              dialogTitletext={dialogTitletext}
              id={id}
              initialData={baseInfo}
            />
            <div className="mt-4">
              <ReceiveForm
                staute={staute}
                loading={loading}
                onRef={formRef2}
                dialogTitletext={dialogTitletext}
                detailInfo={baseInfo}
                mode={mode}
                onSubmit={(val) => {
                  handleSaveSubmit(val);
                }}
              />
            </div>
            {staute == 0 && (
              <div className="mt-4">
                <TableSelect
                  loading={loading}
                  onRef={formRef3}
                  dialogTitletext={dialogTitletext}
                  detailInfo={baseInfo}
                  mode={mode}
                  id={id}
                  onSubmit={(val) => {
                    handleSaveSubmit(val);
                  }}
                />
              </div>
            )}
          </div>
          <div className="w-20">
            <ProcessLogWrapper documentNo={Id} detailInfo={baseInfo} />
          </div>
        </div>
      )}
      {/* <span>{mainLoading}</span>
      <span>{readonly}</span> */}
      {dialogTitletext !== '查看' && staute == 0 && (
        <div className=" bottom-btn-wraper h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleOpenPassDialog();
            }}
            loading={loading}
            type="primary"
          >
            通过
          </Button>
          <Button
            onClick={() => {
              setIsShowNotPass(true);
            }}
            loading={loading}
          >
            不通过
          </Button>
          <Button
            onClick={() => {
              handleOpenSendBackDialog();
            }}
            loading={loading}
          >
            退回
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              handleSave();
            }}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext !== '查看' && staute == 1 && (
        <div className="bottom-btn-wraper  h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleSaveSubmit1();
            }}
            loading={loading}
          >
            提交
          </Button>
          <Button
            onClick={() => {
              handleSave();
            }}
            loading={loading}
          >
            保存草稿
          </Button>
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {dialogTitletext == '查看' && (
        <div className=" bottom-btn-wraper h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={close} loading={loading}>
            关闭
          </Button>
        </div>
      )}

      {/*  通过弹窗 */}
      <Modal
        title="审核通过"
        open={isShowPass}
        footer={null}
        onCancel={() => {
          setIsShowPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef5}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="passRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={passValue}
              onChange={(e: any) => {
                setPassValue(e.target.value);
                formRef5.current?.setFieldsValue({
                  passRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
        </Form>
        <div className="tips mb-5">
          提示：材料文审通过后，待评审办公室排期后进入现场评审环节。
        </div>
        {/* <div>审核意见示例：</div>
        <div className='txt-1'>经评审专家组对提交的实验室等级申请材料进行全面审核，确认材料完整、数据真实、内容符合《XXX实验室等级评定标准》（标准编号：XXX-202X）的相关要求，予以审核通过，进入下一阶段现场评审环节。
            <Button  type='link' onClick={() => handleCopy()}>复制</Button>


        </div> */}
        <div className="btn-waraper">
          <Button
            type="primary"
            onClick={() => {
              handleSubmitPass();
            }}
            className="mr-2"
          >
            确认通过
          </Button>
          <Button
            onClick={() => {
              setIsShowPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>

      {/*  不通过弹窗 */}
      <Modal
        title="审核不通过"
        open={isShowNotPass}
        footer={null}
        onCancel={() => {
          setIsShowNotPass(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          ref={formRef6}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="notPassRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={notPassValue}
              onChange={(e: any) => {
                setNotPassValue(e.target.value);
                formRef6.current?.setFieldsValue({
                  notPassRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">提示：审核不通过后流程结束；</div>
        </Form>
        <div className="btn-waraper">
          <Button
            onClick={() => {
              handleSubmitReject();
            }}
            type="primary"
            className="mr-2"
          >
            确认不通过
          </Button>
          <Button
            onClick={() => {
              setIsShowNotPass(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>
      {/*  退回弹窗 */}
      <Modal
        title="退回"
        open={isShowSendBack}
        footer={null}
        onCancel={() => {
          setIsShowSendBack(false);
        }}
        width={600}
      >
        <Form
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 14 }}
          ref={formRef7}
          layout="horizontal"
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="审核意见:"
            name="sendBackRemark"
            rules={[{ required: true, message: '请输入审核意见' }]}
          >
            <TextArea
              rows={4}
              value={sendBackValue}
              onChange={(e: any) => {
                setSendBackValue(e.target.value);
                formRef7.current?.setFieldsValue({
                  sendBackRemark: e.target.value,
                });
              }}
            />
          </Form.Item>
          <div className="tips-2">
            提示：退回后申请人可直接修改材料重新提交。
          </div>

          <Form.Item
            label="补正材料重新提交截止期限设置:"
            name="docComplementDate"
            rules={[{ required: true, message: '请选择截止期限' }]}
          >
            <DatePicker
              value={docComplementDate}
              onChange={(e: any) => {
                setDocComplementDate(e.target.value);
                formRef7.current?.setFieldsValue({
                  docComplementDate: e.target.value,
                });
              }}
            />
          </Form.Item>

          <div className="tips-2">设置截止期限后，超时申请机构将无法提交。</div>
        </Form>
        <div className="btn-waraper">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              handleSubmitSendBack();
            }}
          >
            确认退回
          </Button>
          <Button
            onClick={() => {
              setIsShowSendBack(false);
            }}
          >
            取消
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default MenuDetail;
