/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, Input, message, Modal, Select } from 'antd';
import Icon, { CloudUploadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import { Any } from '@react-spring/web';
import classNames from 'classnames';
import BlockContainer from '@/components/BlockContainer';
import UploadList from './UploadList';

type TEditProps = {
  loading?: boolean;
  baseInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  dialogTitletext?: string;
  detailInfo?: any;
  staute?: any;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  onSubmit,
  dialogTitletext,
  detailInfo,
  onRef,
  staute,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: (status: any) => {
        // formRef.current?.submit();
        console.log(idicesList, dataSourceArray, 1111);

        // dataSourceArrayb遍历，只要有一个selfScores不为空，就提示用户,然后停止循环
        const flattenedArray = dataSourceArray.flat();
        if (status === 'submit') {
          for (let index = 0; index < flattenedArray.length; index++) {
            if (
              flattenedArray[index].docAuditResult !== 0 &&
              flattenedArray[index].docAuditResult !== 1
            ) {
              message.error('请完善文审结论'); // 使用 message 提示用户
              return false; // 捕获异常后返回，避免后续代码执行
            }

            console.log(flattenedArray[index].docAuditRemark);

            if (
              flattenedArray[index].docAuditResult == 0 &&
              (!flattenedArray[index].docAuditRemark ||
                flattenedArray[index].docAuditRemark?.length == 0)
            ) {
              message.error('文审结论不符合时，请添加备注'); // 使用 message 提示用户
              return false; // 捕获异常后返回，避免后续代码执行
            }
          }
        }
        const updatedIdicesList = idicesList.map((mainItem: any) => {
          const newMainItem = { ...mainItem };
          if (newMainItem.children) {
            newMainItem.children = newMainItem.children.map(
              (childItem: any) => {
                const newChildItem = { ...childItem };
                if (newChildItem.children) {
                  newChildItem.children = newChildItem.children.map(
                    (grandChildItem: any) => {
                      let obj = {};
                      dataSourceArray.flat().forEach((dsItem: any) => {
                        // console.log(dsItem.id, grandChildItem.id, grandChildItem.id === dsItem.id);
                        if (grandChildItem.id === dsItem.id) {
                          // console.log({ ...grandChildItem, ...dsItem }, 11111);
                          obj = { ...grandChildItem, ...dsItem };
                        }
                      });
                      // console.log(obj, 22222222);

                      return obj;
                    }
                  );
                }
                return newChildItem;
              }
            );
          }
          return newMainItem;
        });
        // console.log(updatedIdicesList, 111111111);

        return updatedIdicesList;
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);
  const tableRef1 = useRef<FormInstance>(null);

  //  配置表格数据
  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>(() => []);
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [isEdit, setIsEdit] = useState<boolean>(mode === 'edit');

  const [sampleNum, setSampleNum] = useState<any>(0);
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const formRef2 = useRef<any>(null);

  const [editableKeys, setEditableRowKeys] = useState<any>([[], []]);

  const [tableRefArray, setTableRefArray] = useState<any[]>([]);

  const [dataSourceArray, setDataSourceArray] = useState<any[]>([]);

  const [sampleNumArray, setSampleNumArray] = useState<any[]>([]);

  const [sourceTotalNumArray, setSourceTotalNumArray] = useState<any[]>([]);

  const [idicesList, setIndicesList] = useState<any>([]);

  const [docs, setDocs] = useState<any>('');
  //当前上传附件的id
  const [curId, setCurId] = useState<any>('');

  // 新增状态用于存储上传的附件数据
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 定义回调函数，用于更新上传的附件数据
  const handleFileUpload = (files: any) => {
    console.log(files, '上传附件内容');
    setUploadedFiles(files);
    const newDataSourceArray = [...dataSourceArray];
    newDataSourceArray.forEach((tableArr, index) => {
      tableArr.forEach((item: any) => {
        if (item.id === curId) {
          item.docs = JSON.stringify(files);
        }
      });
    });
    setDataSourceArray(newDataSourceArray);
  };

  const resultList = [
    { value: '1', label: '符合' },
    { value: '0', label: '不符合' },
  ];

  // 计算 rowSpan 的函数
  const calculateRowSpan = (data: any[]) => {
    const result = [...data];
    const countMap = new Map<string, number>();
    const startIndexMap = new Map<string, number>();

    // 第一次遍历，记录每个 parentLab 首次出现的索引和出现次数
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      if (!countMap.has(parentLab)) {
        countMap.set(parentLab, 1);
        startIndexMap.set(parentLab, index);
      } else {
        countMap.set(parentLab, countMap.get(parentLab)! + 1);
      }
    });

    // 第二次遍历，设置 rowSpan
    result.forEach((item, index) => {
      const parentLab = item.parentLab;
      const startIndex = startIndexMap.get(parentLab)!;
      const count = countMap.get(parentLab)!;
      if (index === startIndex) {
        item.sampleCodeRowSpan = count;
      } else {
        item.sampleCodeRowSpan = 0;
      }
    });

    return result;
  };

  const columns: ProColumns<Any>[] = [
    {
      title: '二级类别',
      dataIndex: 'parentLab',
      readonly: true,
      onCell: (_: any, index: any) => {
        return { rowSpan: _.sampleCodeRowSpan };
      },
    },
    {
      title: '指标名称',
      dataIndex: 'lab',
      readonly: true,
    },
    {
      title: '自评得分',
      dataIndex: 'selfScore',
      readonly: true,
    },

    {
      title: '指标提示',
      dataIndex: 'tip',
      readonly: true,
      ellipsis: true,
    },

    {
      title: '支撑材料',
      dataIndex: 'sampleStatus',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      renderFormItem: (_: any, record: any, index: any) => {
        if (record.record.docs && record.record.docs.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.record.id);
                setDocs(record.record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
      render: (_: any, record: any, index: any) => {
        if (record?.docs && record.docs?.length > 0) {
          return (
            <CloudUploadOutlined
              className="upload-icon"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        } else {
          return (
            <CloudUploadOutlined
              className="upload-icon upload-icon-1"
              onClick={() => {
                setCurId(record.id);
                setDocs(record.docs);
                setOpenPreview(true);
              }}
            />
          );
        }
      },
    },

    {
      title: '文审专家',
      dataIndex: 'docAuditUserName',
      readonly: true,
    },
    {
      title: '文审结论',
      dataIndex: 'docAuditResult',
      renderFormItem: (_: any, record: any) => (
        <Select
          className="!w-40 selectWidth"
          options={resultList}
          placeholder="请选择"
          value={record.record?.docAuditResult}
          onChange={(value) => {
            const newDataSourceArray = [...dataSourceArray];
            let curIndex: any;
            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.docAuditResult = value;
                  curIndex = index;
                }
              });
            });
            setDataSourceArray(newDataSourceArray);
          }}
          allowClear
        ></Select>
      ),

      render: (_: any, record: any, index: any) => {
        if (record?.docAuditResult === '1') {
          return <div>符合</div>;
        } else if (record?.docAuditResult === '0') {
          return <div>不符合</div>;
        } else {
          return <div>-</div>;
        }
      },
    },
    {
      title: '文审备注',
      dataIndex: 'docAuditRemark',
      width: 320,

      renderFormItem: (_: any, record: any) => (
        <Input.TextArea
          className={{ 'red-border': record.record?.docAuditResult === 0 }}
          value={record.record?.docAuditRemark}
          onChange={(value: any) => {
            // 这里可以添加更新 record 数据的逻辑
            classNames;
            const newDataSourceArray = [...dataSourceArray];
            let curIndex: any;
            newDataSourceArray.forEach((tableArr, index) => {
              tableArr.forEach((item: any) => {
                if (item.id === record.record.id) {
                  item.docAuditRemark = value.target.value;
                  curIndex = index;
                }
              });
            });
            setDataSourceArray(newDataSourceArray);
          }}
          rows={3}
          style={{ minWidth: '300px' }}
          placeholder="请输入文审备注"
        />
      ),
    },
  ];

  useEffect(() => {
    if (detailInfo) {
      // console.log(detailInfo,detailInfo.indices, '详情数组数据');
      if (detailInfo.indices) {
        const gradeListData = detailInfo.indices;
        setIndicesList(gradeListData);
        const arrContainer: any = [];
        const editId: any = [];
        gradeListData.forEach((e: any, index: number) => {
          let arrData: any = [];
          e.children.forEach((e1: any, index1: number) => {
            const arrData2 = e1.children.map((e2: any, index2: number) => {
              return {
                ...e2,
                parentLab: e1.lab,
              };
            });
            arrData.push(...arrData2);
          });
          arrContainer[index] = calculateRowSpan(arrData);
          const sourceTotalNumArray: any = [];
          arrContainer.forEach((item: any, index: number) => {
            const total = item.reduce((total: any = 0, arr: any) => {
              if (arr.selfScore === undefined) {
                arr.selfScore = 0;
              }
              return total + arr.selfScore * 1;
            }, 0);
            sourceTotalNumArray.push(total);
          });
          setSampleNumArray(sourceTotalNumArray);
          const totals2 = sourceTotalNumArray.reduce(
            (total: any = 0, arr: any) => {
              if (arr === undefined) {
                arr = 0;
              }
              return total + arr;
            },
            0
          );
          console.log(dataSourceArray, 1111);
          setSampleNum(totals2);

          const sourceTotalNum = gradeListData.map((e: any) => {
            return e.score;
          });
          setDataSourceArray(arrContainer);
          setSourceTotalNumArray(sourceTotalNum);
          if (
            dialogTitletext !== '查看' &&
            dialogTitletext !== '查看详情' &&
            staute !== 0
          ) {
            const newKeyId = arrData.map((e: any) => {
              return e.id;
            });
            editId[index] = newKeyId;
          }
          setEditableRowKeys(editId);
        });
        setTimeout(() => {
          const arr: any = [];
          gradeListData.forEach((e: any) => {
            arr.push(useRef<FormInstance>(null));
          });
          setTableRefArray(arr);
        });
      }
    }
  }, [detailInfo]);

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 计算合计

  // 批量符合
  const handlePass = async (values: any) => {
    Modal.confirm({
      title: '确认批量符合？',
      content: '确定要将所有文审结论设置为符合吗？',
      onOk: async () => {
        try {
          const newDataSourceArray = [...dataSourceArray];
          newDataSourceArray.forEach((tableArr, index) => {
            tableArr.forEach((item: any) => {
              item.docAuditResult = '1';
            });
          });
          setDataSourceArray(newDataSourceArray);
        } catch (error) {}
      },
      onCancel: () => {
        console.log('用户取消批量符合操作');
      },
    });
  };

  // 批量不符合
  const handleNotPass = async (values: any) => {
    Modal.confirm({
      title: '确认批量不符合？',
      content: '确定要将所有文审结论设置为不符合吗？',
      onOk: async () => {
        try {
          const newDataSourceArray = [...dataSourceArray];
          newDataSourceArray.forEach((tableArr, index) => {
            tableArr.forEach((item: any) => {
              item.docAuditResult = '0';
            });
          });
          setDataSourceArray(newDataSourceArray);
        } catch (error) {}
      },
      onCancel: () => {
        console.log('用户取消批量符合操作');
      },
    });
  };

  return (
    <>
      <BlockContainer title="指标明细">
        {staute !== 0 &&
          mode === 'edit' &&
          dialogTitletext !== '查看' &&
          dialogTitletext !== '查看详情' && (
            <div className="handle-box">
              <Button className="mr-2 mb-2" type="primary" onClick={handlePass}>
                批量符合
              </Button>
              <Button className=" mb-2" onClick={handleNotPass}>
                批量不符合
              </Button>
            </div>
          )}

        {idicesList.length > 0 &&
          idicesList.map((tableData: any, index: number) => (
            <BlockContainer title={tableData.lab}>
              <ProForm<{ table: Any[] }>
                formRef={formRef}
                initialValues={{ table: [] }}
                submitter={false}
                onFinish={handleSave}
              >
                <EditableProTable<Any>
                  rowKey="id"
                  name="table"
                  editableFormRef={tableRefArray[index]}
                  loading={loading}
                  request={async () => ({
                    data: dataSourceArray[index],
                    total: dataSourceArray[index]?.length,
                    success: true,
                  })}
                  scroll={{ x: 960 }}
                  recordCreatorProps={false}
                  columns={columns}
                  editable={{
                    type: 'multiple',
                    editableKeys: editableKeys[index],
                  }}
                />
                <div className="total-num">
                  <div className="num-box">合计: </div>
                  <div className="num-box">
                    指标名称总分：{sourceTotalNumArray[index]}
                  </div>
                  <div className="num-box">
                    自评得分：{sampleNumArray[index]}
                  </div>
                </div>
              </ProForm>
            </BlockContainer>
          ))}
        {idicesList.length > 0 && (
          <div className="total-num total-num-1">
            <div className="num-box">自评总得分: </div>
            <div className="num-box">{sampleNum}</div>
            <Button
              type="link"
              size="small"
              onClick={() => {
                setOpenDetail(true);
              }}
            >
              查看等级划分
            </Button>
          </div>
        )}

        {idicesList.length == 0 && <div className="no-data">暂无数据</div>}
      </BlockContainer>
      <Modal
        width="60%"
        title="支撑材料"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <UploadList
          dialogTitletext={dialogTitletext}
          loading={loading}
          onRef={formRef2}
          mode="view"
          docs={docs}
          onUpload={handleFileUpload} // 传递回调函数
        />
      </Modal>

      {/* 录入查找样本编号 */}
      <Modal
        title="等级划分"
        open={openDetail}
        footer={null}
        onCancel={() => {
          setOpenDetail(false);
        }}
        width={450}
      >
        <div className="p-4">
          <div>一级：≥60分，且＜70分；</div>
          <div>二级乙等：≥70分，且＜80分；</div>
          <div>二级甲等：≥80分，且＜85分；</div>
          <div>三级乙等：≥85分，且＜95分；</div>
          <div>三级甲等：≥95分。</div>
        </div>
      </Modal>
    </>
  );
};

export default ReceiveForm;
