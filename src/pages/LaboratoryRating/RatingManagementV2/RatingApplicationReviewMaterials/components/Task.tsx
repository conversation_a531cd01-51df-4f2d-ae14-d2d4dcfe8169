/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Button,
  Card,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Select,
  Space,
  Spin,
  Table,
} from 'antd';
import {
  assignExpertToIndex,
  confirmAllocation,
  getAllocationIndex,
  getTaskAllocationBasicMsg,
  getUnAllocationIndex,
  gradeConfig,
  reConfirmAllocation,
  selectAllocationGroup,
} from '@/api/apply';
import {
  ActionType,
  EditableProTable,
  FormInstance, // 注意：在新的实现中，我们更多地依赖 renderFormItem 提供的 form 实例
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import type { EditableFormInstance } from '@ant-design/pro-components';
import { FormInitVal, formItemLayout } from './data';

type TEditProps = {
  close: () => void;
  detailId?: string;
  dialogTitletext?: string;
  setIsUploadReceipt?: (isUploadReceipt: boolean) => void;
  mode?: 'edit' | 'view';
};

// 定义行数据类型，便于维护
type TableRowData = {
  id: React.Key;
  lab1: string;
  lab2: string;
  lab: string;
  score: number;
  selfScore: number;
  docTaskId?: string; // 专家ID，可选
  [key: string]: any;
};

const Task: React.FC<TEditProps> = ({
  close,
  detailId,
  dialogTitletext,
  mode,
}) => {
  // 所有的 State 和 Ref 定义都必须在组件顶层
  const [id, setId] = useState<any>('');
  const [gradeList, setGradeList] = useState<any[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isTableShow, setIsTableShow] = useState<boolean>(true);
  const [grounpData, setGrounpData] = useState<any[]>([]);
  const [expertName, setExpertName] = useState<string>('');
  const [activeKey, setActiveKey] = useState('0');
  const [dataSource, setDataSource] = useState<TableRowData[]>([]);

  // 【优化】不再需要 latestDataSourceRef，因为新的“同上”逻辑不依赖它
  // const latestDataSourceRef = useRef<TableRowData[]>([]);

  const formRef = useRef<FormInstance>(null);
  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance>(); // editableFormRef 仍然有用，用于访问整个可编辑表格的API
  const [mainLoading, setMainLoading] = useState<boolean>(false);
  const [grounpDataLoading, setGrounpDataLoading] = useState<boolean>(false);
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [selectedRows1, setSelectedRows1] = useState<TableRowData[]>([]);
  const [selectedRows2, setSelectedRows2] = useState<any[]>([]);
  const [pageSize, setPageSize] = useState<number>(10);
  const [basicInfo, setBasicInfo] = useState<any>(null);
  // 使用一个 ref 来存储所有行的 select 实例
  const selectRefs = useRef<Map<React.Key, any>>(new Map());

  // --- API 调用 (使用 useCallback 稳定) ---
  const getDetailData = useCallback(async () => {
    if (!id) return;
    setMainLoading(true);
    try {
      const { data } = await getTaskAllocationBasicMsg(id);
      if (data) {
        setBasicInfo(data);
      }
    } catch (error) {
      console.error('获取基础信息失败:', error);
      message.error('获取基础信息失败');
    } finally {
      setMainLoading(false);
    }
  }, [id]);

  useEffect(() => {
    // 只有在 basicInfo 有数据，并且 formRef.current 也准备好时，才执行赋值
    if (basicInfo && formRef.current) {
      formRef.current.setFieldsValue({
        labName: basicInfo.labName,
        code: basicInfo.code,
        setId: basicInfo.setId,
      });
    }
  }, [basicInfo]);

  const getSelectAllocationGroup = useCallback(async () => {
    if (!id) return;
    setGrounpDataLoading(true);
    try {
      const params = { auditName: expertName };
      const { data } = await selectAllocationGroup(id, params);
      setGrounpData(data || []);
    } catch (error) {
      console.error('获取评审小组失败:', error);
      message.error('获取评审小组失败');
    } finally {
      setGrounpDataLoading(false);
    }
  }, [id, expertName]);

  const gradeConfigData = useCallback(async () => {
    try {
      const { data } = await gradeConfig();
      const gradeListData = (data || []).map((item: any) => ({
        label: item.grade,
        value: item.id,
      }));
      setGradeList(gradeListData);
    } catch (err) {
      console.error('获取等级配置失败:', err);
    }
  }, []);

  // --- useEffects (所有 useEffect 都在顶层) ---
  useEffect(() => {
    if (detailId) setId(detailId);
  }, [detailId]);
  useEffect(() => {
    getDetailData();
    getSelectAllocationGroup();
  }, [id]);
  useEffect(() => {
    gradeConfigData();
  }, []);
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [activeKey]);

  // --- 表格相关逻辑 ---
  const tableReload = () => {
    setSelectedRows1([]);
    setSelectedRows2([]);
    actionRef.current?.reload();
  };

  // 保存选择的专家
  const handleAssignExpert = async (status: string, arr: any[]) => {
    try {
      const params = { status, indexes: arr };
      const { code } = await assignExpertToIndex(params);
      return code === 200;
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  // --- 列定义 (使用 useMemo 稳定引用) ---
  const columns1 = useMemo(
    () => [
      { title: '评审小组名称', dataIndex: 'name' },
      {
        title: '专家姓名',
        dataIndex: 'auditName',
        render: (_: any, record: any) =>
          record.leaderFlag === 1 ? (
            <span style={{ color: 'red' }}>{record.auditName}（组长）</span>
          ) : (
            <span>{record.auditName}</span>
          ),
      },
      { title: '专家领域', dataIndex: 'professionalFieldCh' },
    ],
    []
  );

  // 【优化】这是本次修改的核心部分
  const columns = useMemo(() => {
    const baseColumns: any[] = [
      { title: '一级类别', dataIndex: 'lab1', editable: false },
      { title: '二级类别', dataIndex: 'lab2', editable: false },
      { title: '指标名称', dataIndex: 'lab', editable: false, width: '25%' },
    ];
    if (activeKey === '0') {
      return [
        ...baseColumns,
        {
          title: '指标分数',
          dataIndex: 'score',
          hideInSearch: true,
          editable: false,
        },
        {
          title: '分配评审专家',
          dataIndex: 'docTaskId', // 1. 绑定到 docTaskId 字段
          width: '20%',
          valueType: 'select',
          hideInSearch: true,
          renderFormItem: (
            { index }: { index: number },
            { record }: { record: any },
            form: any
          ) => {
            return (
              <ProFormSelect
                options={grounpData.map((expert) => ({
                  value: expert.id,
                  label:
                    expert.leaderFlag === 1
                      ? `${expert.auditName} (组长)`
                      : expert.auditName,
                }))}
                fieldProps={{
                  // 1. 将 select 实例存入 ref Map 中
                  ref: (el) => {
                    if (el) {
                      selectRefs.current.set(record.id, el);
                    } else {
                      selectRefs.current.delete(record.id);
                    }
                  },

                  placeholder: '请选择评审专家',
                  showSearch: true,
                  onChange: async (e) => {
                    const recordNew = {
                      ...record,
                      docTaskId: e,
                    };
                    await handleAssignExpert('0', [recordNew]);
                  },
                  dropdownRender: (menu) => (
                    <>
                      {menu}
                      <Divider style={{ margin: '4px 0' }} />
                      <Button
                        className="m-2"
                        type="primary"
                        size="small"
                        disabled={index === 0}
                        onClick={async (e: any) => {
                          e.preventDefault();
                          if (index > 0) {
                            // 从 dataSource state 中获取上一行的 ID
                            const prevRowId = dataSource[index - 1].id;
                            // 使用 form 实例获取上一行在表单中的实时数据，避免状态陈旧问题
                            const prevRowValues = form.getFieldValue(prevRowId);
                            if (prevRowValues?.docTaskId) {
                              const recordNew = {
                                ...record,
                                docTaskId: prevRowValues?.docTaskId,
                              };
                              const data = await handleAssignExpert('0', [
                                recordNew,
                              ]);
                              if (data) {
                                // 赋值
                                editableFormRef.current?.setRowData &&
                                  editableFormRef.current?.setRowData(
                                    record.id,
                                    recordNew
                                  );
                                const dataSourceNew = dataSource.map(
                                  (item: any) => {
                                    if (item.id === record.id) {
                                      item = recordNew;
                                    }
                                    return item;
                                  }
                                );
                                setDataSource(dataSourceNew);
                                // 关闭下拉框
                                // 2. 从 ref Map 中获取当前行的 select 实例
                                const currentSelect = selectRefs.current.get(
                                  record.id
                                );
                                // console.log(currentSelect);
                                // 3. 调用 blur() 方法
                                if (currentSelect) {
                                  // 使用 setTimeout 确保在 DOM 更新后再执行 blur
                                  setTimeout(() => {
                                    currentSelect.blur();
                                  }, 0);
                                }
                              }
                            } else {
                              message.warning('上一行未分配评审专家');
                            }
                          }
                        }}
                      >
                        同上一行
                      </Button>
                    </>
                  ),
                }}
                formItemProps={{ noStyle: true }} // 必须设置，使其无缝融入单元格
              />
            );
          },
          // render 用于非编辑状态下的显示
          render: (text: any, record: TableRowData) => {
            const expert = grounpData.find((e) => e.id === record.docTaskId);
            if (!expert) return '-';
            // 优化：在单元格显示时也高亮组长
            return expert.leaderFlag === 1 ? (
              <span style={{ color: 'red' }}>{expert.auditName} (组长)</span>
            ) : (
              expert.auditName
            );
          },
        },
        {
          title: '自评得分',
          dataIndex: 'selfScore',
          hideInSearch: true,
          editable: false,
        },
      ];
    }
    // activeKey === '1' (已分配) 的列定义保持不变
    return [
      {
        title: '评审专家',
        dataIndex: 'auditName',
        editable: false,
        hideInSearch: true,
      },
      {
        title: '文审结果',
        hideInSearch: true,
        dataIndex: 'docAuditResult',
        editable: false,
        render: (text: string, record: any) => {
          if (record.docAuditResult === '1') {
            return <span>符合</span>;
          } else if (record.docAuditResult === '0') {
            return <span>不符合</span>;
          } else {
            return <span>-</span>;
          }
        },
      },
      {
        hideInSearch: true,
        title: '文审备注',
        dataIndex: 'docAuditRemark',
        editable: false,
      },
      {
        title: '现场评审结果',
        hideInSearch: true,
        dataIndex: 'fieldAuditResult',
        editable: false,
      },
      {
        title: '现场评审备注',
        dataIndex: 'fieldAuditRemark',
        editable: false,
      },
      ...baseColumns,
      {
        title: '指标分数',
        hideInSearch: true,
        dataIndex: 'score',
        editable: false,
      },

      {
        title: '自评得分',
        hideInSearch: true,
        dataIndex: 'selfScore',
        editable: false,
      },

      {
        title: '分配操作人',
        hideInSearch: true,
        dataIndex: 'allocationName',
        editable: false,
      },
      {
        title: '分配时间',
        dataIndex: 'allocationTime',
        editable: false,
        hideInSearch: true,
      },
    ];
  }, [activeKey, grounpData, dataSource]); // 5.【重要】把 dataSource 加入依赖项，确保“同上”逻辑能访问到最新的行数据

  // --- 批量分配弹窗提交 ---
  const handleBatchAssigned = async () => {
    if (selectedRows1.length === 0 || selectedRows2.length === 0) {
      message.error('请先选择要分配的指标和目标专家小组');
      return;
    }
    const params = {
      subtask: selectedRows2[0],
      indexes: selectedRows1,
    };
    try {
      if (activeKey === '1') {
        reConfirmAllocation;
        await reConfirmAllocation(params);
      } else {
        await confirmAllocation(params);
      }
      message.success('批量分配成功');
      setOpenDetail(false);
      tableReload();
    } catch (error) {
      message.error('批量分配失败');
      console.error(error);
    }
  };

  // 直接分配
  const handleDirectlyAssigned = async () => {
    // 获取当前表格的所有行数据
    const currentDataSource = [...dataSource];

    // 合并选中的行与表格的最新数据，以确保获取到最新的状态
    const selectedWithUpdatedData = selectedRows1.map((selectedRow) => {
      const currentRow = currentDataSource.find(
        (row) => row.id === selectedRow.id
      );
      return currentRow ? { ...selectedRow, ...currentRow } : selectedRow;
    });

    // 检查是否有未分配专家的行
    const invalidRows = selectedWithUpdatedData.filter((row) => !row.docTaskId);

    if (invalidRows.length > 0) {
      message.error(
        `有 ${invalidRows.length} 个选中项未分配专家，请全部分配后再提交。`
      );
      return;
    }

    if (selectedWithUpdatedData.length === 0) {
      message.warning('请至少选择一个已分配的指标进行提交。');
      return;
    }

    try {
      const tempArr = selectedWithUpdatedData;
      const data = await handleAssignExpert('1', tempArr);
      if (data) {
        message.success('直接分配成功！');
        tableReload();
      }
    } catch (error) {
      message.error('直接分配失败！');
    }
  };

  // --- 返回的 JSX。这里不能再有 Hooks 调用 ---
  return (
    <div className="flex flex-col h-full w-full">
      {mainLoading ? (
        <Spin tip="加载中" size="large" className="mt-[40vh]">
          <div className="content" />
        </Spin>
      ) : (
        <div className="flex-1 overflow-auto p-4">
          <div className="mt-4">
            <Card className="mb-[16px]">
              <ProForm
                formRef={formRef}
                {...formItemLayout}
                layout="horizontal"
                grid={true}
                submitter={false}
                readonly
                initialValues={FormInitVal}
              >
                <Col span={8}>
                  <ProFormText name="labName" label="申请机构名称" />
                </Col>
                <Col span={8}>
                  <ProFormText name="code" label="申请编号" />
                </Col>
                <Col span={8}>
                  <ProFormSelect
                    name="setId"
                    options={gradeList}
                    label="申请等级"
                  />
                </Col>
              </ProForm>
            </Card>
            {isTableShow && (
              <EditableProTable<TableRowData>
                rowKey="id"
                actionRef={actionRef}
                editableFormRef={editableFormRef}
                columns={columns}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys: selectedRows1.map((row) => row.id),
                  onChange: (keys, rows) =>
                    setSelectedRows1(rows as TableRowData[]),
                }}
                recordCreatorProps={false}
                toolbar={{
                  menu: {
                    activeKey,
                    items: [
                      { key: '0', label: <span>待分配</span> },
                      { key: '1', label: <span>已分配</span> },
                    ],
                    onChange: (key) => setActiveKey(key as string),
                  },
                }}
                cardBordered
                bordered
                request={async (params) => {
                  const queryParams: any = {
                    ...params,
                    pageNum: params.current,
                    pageSize: params.pageSize,
                    processed: activeKey,
                  };
                  delete queryParams.current;

                  const apiCall =
                    activeKey === '0'
                      ? getUnAllocationIndex(id, queryParams)
                      : getAllocationIndex(id, queryParams);

                  try {
                    const { data } = await apiCall;
                    const rows = data?.rows || [];
                    setEditableKeys(rows.map((item: any) => item.id)); // 保持所有行可编辑
                    setDataSource(rows); // 更新 dataSource state，供“同上”逻辑使用
                    return {
                      data: rows,
                      total: data?.total || 0,
                      success: true,
                    };
                  } catch (e) {
                    setDataSource([]); // 出错时清空
                    return { data: [], total: 0, success: false };
                  }
                }}
                editable={{
                  type: 'multiple',
                  editableKeys,
                  onValuesChange: (record, recordList) => {
                    setDataSource(recordList);
                  },
                }}
                pagination={{
                  pageSize,
                  showSizeChanger: true,
                  onChange: (page, size) => setPageSize(size),
                }}
                search={{ defaultCollapsed: false, labelWidth: 120 }}
                toolBarRender={() => {
                  const buttons = [
                    <Button
                      key="batch-assigned"
                      onClick={() => setOpenDetail(true)}
                      disabled={selectedRows1.length === 0}
                    >
                      {activeKey === '1' ? '重新批量分配' : '批量分配'}
                    </Button>,
                    <Button key="export">导出明细</Button>,
                  ];
                  if (activeKey === '0') {
                    buttons.unshift(
                      <Button
                        key="directly-assigned"
                        type="primary"
                        onClick={handleDirectlyAssigned}
                        disabled={selectedRows1.length === 0}
                      >
                        直接分配
                      </Button>
                    );
                  }
                  return buttons;
                }}
              />
            )}
          </div>
        </div>
      )}

      <Modal
        title={activeKey === '1' ? '重新批量分配' : '批量分配'}
        open={openDetail}
        footer={null}
        onCancel={() => setOpenDetail(false)}
        width={900}
      >
        <div className="p-4">
          <div className="mb-2">
            提示：为所有选中的指标分配给同一个专家小组。
          </div>
          <Form
            name="basic"
            onFinish={getSelectAllocationGroup}
            style={{ marginBottom: 16 }}
          >
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="请输入专家名称搜索"
                value={expertName}
                onChange={(e: any) => setExpertName(e.target.value)}
              />
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Space.Compact>
          </Form>
          {grounpDataLoading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Spin />
            </div>
          ) : (
            <Table
              rowSelection={{
                type: 'radio',
                selectedRowKeys: selectedRows2.map((row) => row.id),
                onChange: (keys, rows) => setSelectedRows2(rows),
              }}
              columns={columns1}
              dataSource={grounpData}
              rowKey="id"
              scroll={{ x: 800 }}
            />
          )}
          <div className="flex justify-end space-x-2 mt-4">
            <Button
              type="primary"
              disabled={selectedRows2.length === 0}
              onClick={handleBatchAssigned}
            >
              确认选择
            </Button>
            <Button onClick={() => setOpenDetail(false)}>取消</Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Task;
