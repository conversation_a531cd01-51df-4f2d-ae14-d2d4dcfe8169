/* eslint-disable eqeqeq */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Al<PERSON>, Button, Drawer, Form, Input, message, Modal, Rate } from 'antd';
import {
  getGradeConfig,
  getGradeDocReviewList,
  gradeConfigAllSet,
} from '@/api/apply';
import { getTaskBySampleNum, handleConfirmTask } from '@/api/recept';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import Task from './components/Task';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import './index.less';

const { TextArea } = Input;
export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();

  const [messageApi, contextHolder] = message.useMessage();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();

  const [pageSize, setPageSize] = useState<number>(10);

  // 接收
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const [isAdd, setIsAdd] = useState<boolean>(false);

  //任务分配弹窗是否显示
  const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const [staute, setStaute] = useState<any>('');

  const [dialogTitletext, setDialogTitletext] = useState<string>('待组长确认');

  const [dialogTitletext1, setDialogTitletext1] = useState<string>('材料文审-');

  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  // 是否已上传回执单
  const [isUploadReceipt, setIsUploadReceipt] = useState<boolean>(false);
  // 是否打开上传回执单询问 Modal
  const [openUploadReceiptModal, setOpenUploadReceiptModal] =
    useState<boolean>(false);

  // 录入样品编号相关状态
  const [openInputSampleCodeModal, setOpenInputSampleCodeModal] =
    useState<boolean>(false);

  //评价弹窗是否显示设置

  const [sampleCode, setSampleCode] = useState<string>('');
  const [searchResult, setSearchResult] = useState<Array<QualityTaskItem>>([]);
  const [searchError, setSearchError] = useState<string>('');
  const [showSearchResult, setShowSearchResult] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<Record<string, any>>({});
  const [stauteList, setStauteList] = useState<any>([]);

  const [gradeList, setGradeList] = useState<any>([]);
  const [activeKey, setActiveKey] = useState('0');

  // 获取申请等级
  const gradeConfigData = async () => {
    try {
      const { code, data, msg } = await gradeConfigAllSet();

      const gradeListData = data.map((item: any) => {
        return {
          label: item.grade,
          value: item.id,
        };
      });
      setGradeList(gradeListData);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    setStauteList([
      {
        label: '文审_待组长确认',
        value: 0,
      },
      {
        label: '文审_待组员审核',
        value: 1,
      },
    ]);
    gradeConfigData();
  }, []);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
    },
    {
      title: '进度状态',
      dataIndex: 'subtaskFlag',
      hideInSearch: true,

      render: (text, record, _, action) => {
        console.log(record.subtaskFlag, activeKey, 11111111);
        if (record.subtaskFlag === 1 && activeKey === '0') {
          return <div>文审_待组员审核</div>;
        }
        if (record.subtaskFlag === 1 && activeKey === '1') {
          return <div>已提交</div>;
        }
        if (record.subtaskFlag === 0 && activeKey === '0') {
          return <div>文审_待组长确认</div>;
        }
        if (record.subtaskFlag === 0 && activeKey === '1') {
          return <div>审核通过</div>;
        }
      },
    },

    {
      title: '申请编号',
      dataIndex: 'code',
      render: (text, record, _, action) => {
        return (
          <div
            className="link-btn"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            {record.code}
          </div>
        );
      },
    },
    {
      title: '申请机构',
      dataIndex: 'labName',
      disable: true,
    },
    {
      disable: true,
      title: '申请等级',
      dataIndex: 'setId',
      valueType: 'select',
      fieldProps: {
        options: gradeList,
      },
    },
    {
      disable: true,
      title: '自评得分',
      hideInSearch: true,
      dataIndex: 'selfScore',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交日期',
      dataIndex: 'submitDate',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '申请提交人',
      dataIndex: 'submitter',
    },
    {
      disable: true,
      hideInSearch: true,
      title: '办理日期',
      dataIndex: 'auditTime',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        const btns = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>,
        ];
        const btns2 = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>,
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              setDetailId(record.id);
              setOpenAssignModal(true);
            }}
          >
            任务分配
          </Button>,
          <Button
            type="link"
            size="small"
            key="handle"
            onClick={() => {
              if (record.subtaskFlag == 1) {
                setDialogTitletext('待组员审核');
              } else if (record.subtaskFlag == 0) {
                setDialogTitletext('待组长确认');
              }
              setDetailId(record.id);
              setStaute(record.subtaskFlag);
              setOpenEdit(true);
            }}
          >
            办理
          </Button>,
        ];

        const btns3 = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDialogTitletext('查看');
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            查看
          </Button>,
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              if (record.subtaskFlag == 1) {
                setDialogTitletext('待组员审核');
              } else if (record.subtaskFlag == 0) {
                setDialogTitletext('待组长确认');
              }
              setStaute(record.subtaskFlag);
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            办理
          </Button>,
        ];
        if (activeKey === '0' && record.subtaskFlag == 1) {
          return btns3;
        } else if (activeKey === '0' && record.subtaskFlag == 0) {
          return btns2;
        } else if (activeKey === '1') {
          return btns;
        }
      },
    },
  ];

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    setParamsCache('refreshCount');
    tableReload();
  };
  /**
   * @TODO 关闭任务分配
   */
  const closeEdit1 = () => {
    setOpenAssignModal(false);
    setParamsCache('refreshCount');
    tableReload();
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    // 重置分页状态
    // actionRef.current?.reset?.();
    // 然后重新加载数据
    actionRef.current?.reload();
  };

  useEffect(() => {
    tableReload();
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);

  // 添加useEffect监听Modal打开状态变化
  useEffect(() => {
    if (openInputSampleCodeModal) {
      setSampleCode('');
      setSearchResult([]);
      setSearchError('');
      setShowSearchResult(false);
      setSelectedTask({});
    }
  }, [openInputSampleCodeModal]);

  return (
    <>
      {contextHolder}
      <PageContainer>
        <ProTable<QualityTaskItem>
          columns={columns}
          actionRef={actionRef}
          // key={activeKey}

          toolbar={{
            menu: {
              type: 'tab',
              activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待办理</span>,
                  // （{counts[0]}）
                },
                {
                  key: '1',
                  label: <span>已办理</span>,
                  // （{counts[1]}）
                },
              ],
              onChange: (key) => setActiveKey(key as string),
            },
          }}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            await waitTime(100);
            const param = {
              ...params,
              pageNum: params.current || 1,
              pageSize: params.pageSize || pageSize,
              current: undefined,
              processed: activeKey,
            };
            const { code, data, msg } = await getGradeDocReviewList(param);

            if (code !== codeDefinition.QUERY_SUCCESS) {
              messageApi.error(msg);
            }

            return {
              data: data.rows ?? [],
              total: data.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 120,
          }}
          form={{
            syncToUrl: false,
            // resetOnTabChange: true,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            defaultPageSize: 10,
            defaultCurrent: 1,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle=""
        />

        {/* 新增,详情，编辑 */}
        <Drawer
          width="90%"
          title={dialogTitletext1 + dialogTitletext}
          onClose={() => closeEdit()}
          open={openEdit}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Edit
            close={() => closeEdit()}
            detailId={detailId}
            staute={staute}
            dialogTitletext={dialogTitletext}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={isAdd === false ? 'edit' : 'view'}
          />
        </Drawer>

        {/* 任务分配 */}
        <Drawer
          width="90%"
          title="任务分配"
          onClose={() => closeEdit1()}
          open={openAssignModal}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Task
            close={() => closeEdit1()}
            detailId={detailId}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={isAdd === false ? 'edit' : 'view'}
          />
        </Drawer>

        <Modal
          title="操作提示"
          centered
          width={360}
          open={openUploadReceiptModal}
          okText="继续上传回执单"
          cancelText="不需要上传回执单"
          onOk={() => {
            setOpenUploadReceiptModal(false);
          }}
          onCancel={() => {
            setOpenUploadReceiptModal(false);
            closeEdit();
          }}
          zIndex={9999}
          maskClosable={false}
        >
          <div>请再次确认本任务是否需要上传回执单</div>
        </Modal>
      </PageContainer>
    </>
  );
};
export default QualityTask;
