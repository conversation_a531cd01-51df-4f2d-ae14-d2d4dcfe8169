/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Dropdown, message, Modal, Space, Tag } from 'antd';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import ReviewModal from './components/ReviewModal';
import PageContainer from '@/components/PageContainer';
import request from '@/utils/request';
import './index.less';

// 模拟数据 - 仅用于开发阶段，实际环境中使用API调用
const mockData = [
  {
    id: '1',
    name: '张三',
    contactNumber: '18215882922',
    organization: '贵州省疾病预防控制中心',
    position: '高级',
    expertise: '理化领域',
    province: '云南',
    city: '大理',
    registerDate: '2025-03-31',
    status: '0', // 0: 待审核, 1: 已通过, 2: 已驳回
    createdBy: '管理员',
    createdTime: '2023-06-01 10:00:00',
    updatedBy: '管理员',
    updatedTime: '2023-06-01 10:00:00',
  },
  {
    id: '2',
    name: '李四',
    contactNumber: '18215682921',
    organization: '贵州省疾病预防控制中心',
    position: '中级',
    expertise: '微生物领域',
    province: '四川',
    city: '成都',
    registerDate: '2025-03-31',
    status: '0',
    createdBy: '管理员',
    createdTime: '2023-06-02 11:00:00',
    updatedBy: '管理员',
    updatedTime: '2023-06-02 11:00:00',
  },
];

// 更多模拟数据，分已通过和已驳回的记录
const approvedData = [
  {
    id: '3',
    name: '王五',
    contactNumber: '18215882923',
    organization: '云南省疾病预防控制中心',
    position: '高级',
    expertise: '理化领域',
    province: '云南',
    city: '昆明',
    registerDate: '2025-03-30',
    status: '1', // 已通过
    reviewStatus: '通过',
    reviewDate: '2025-03-31',
    reviewer: '张三',
    reviewRemark: '同意',
    createdBy: '管理员',
    createdTime: '2023-06-03 10:00:00',
    updatedBy: '管理员',
    updatedTime: '2023-06-03 10:00:00',
  },
  {
    id: '4',
    name: '赵六',
    contactNumber: '18215682924',
    organization: '四川省疾病预防控制中心',
    position: '中级',
    expertise: '微生物领域',
    province: '重庆',
    city: '重庆',
    registerDate: '2025-03-31',
    status: '2', // 已驳回
    reviewStatus: '驳回',
    reviewDate: '2025-03-31',
    reviewer: '张三',
    reviewRemark: '不符合要求',
    createdBy: '管理员',
    createdTime: '2023-06-04 11:00:00',
    updatedBy: '管理员',
    updatedTime: '2023-06-04 11:00:00',
  },
];

// API接口定义
const API = {
  GET_EXPERT_REVIEW_LIST: '/gradeConfig/expert/list',
  AUDIT_EXPERT: '/gradeConfig/expert/auditBatch',
  EXPORT_DATA: '/gradeConfig/export',
  COUNT_EXPERT_STATUS: '/gradeConfig/expert/countExpertStatus',
};

const ExpertReview: React.FC = () => {
  const [activeKey, setActiveKey] = useState<string>('0'); // 默认选中"待审核"
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  // 审核Modal相关状态
  const [reviewModalVisible, setReviewModalVisible] = useState<boolean>(false);
  const [reviewType, setReviewType] = useState<'approve' | 'reject'>('approve');
  const [isBatch, setIsBatch] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);

  // 统计数据
  const [counts, setCounts] = useState<Record<string, number>>({
    '0': 0, // 待审核数量
    '1': 0, // 已审核数量 (包括通过和驳回)
  });

  // 当切换到已审核标签时，获取已审核数据的统计
  useEffect(() => {
    // 不需要在这里调用fetchCounts，handleTabChange已经会调用了
  }, [activeKey]);

  // 初始获取统计数据
  useEffect(() => {
    let isMounted = true;

    const loadCounts = async () => {
      try {
        // 使用正确的API接口获取专家状态数量统计
        const response = await request.get(API.COUNT_EXPERT_STATUS);

        if (response.code === codeDefinition.QUERY_SUCCESS && isMounted) {
          const statusData = response.data || {};
          setCounts({
            '0': parseInt(statusData.unAuditTotal || '0'), // 待审核数量
            '1': parseInt(statusData.auditedTotal || '0'), // 已审核数量
          });
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    };

    loadCounts();

    return () => {
      isMounted = false;
    };
  }, []);

  // 实际API调用函数
  const fetchExpertList = async (params: any) => {
    try {
      setLoading(true);
      // 构建请求参数
      const requestParams: Record<string, any> = {
        pageNum: params.current,
        pageSize: params.pageSize,
        status: params.status || activeKey, // 确保使用当前activeKey
      };

      // 添加搜索条件
      if (params.nickName) {
        requestParams.nickName = params.nickName;
      }

      if (params.phonenumber) {
        requestParams.phonenumber = params.phonenumber;
      }

      if (params.deptName) {
        requestParams.deptName = params.deptName;
      }

      // 处理日期范围（只在已审核状态下）
      if (
        params.status === '1' &&
        params.registerDateStart &&
        params.registerDateEnd
      ) {
        requestParams.beginDate = params.registerDateStart;
        requestParams.endDate = params.registerDateEnd;
      }

      // 调用API
      const { code, data, msg } = await request.get(
        API.GET_EXPERT_REVIEW_LIST,
        {
          params: requestParams,
        }
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取数据失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      // 打印第一条数据，查看ID字段
      if (data.rows && data.rows.length > 0) {
        console.log('专家数据示例:', data.rows[0]);
      }

      // 不再在这里更新统计，由fetchCounts统一处理

      return {
        data: data.rows || [],
        success: true,
        total: data.total || 0,
      };
    } catch (error) {
      console.error('查询失败:', error);
      message.error('查询失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据函数
  const fetchCounts = async () => {
    try {
      // 使用正确的API接口获取专家状态数量统计
      const response = await request.get(API.COUNT_EXPERT_STATUS);

      if (response.code === codeDefinition.QUERY_SUCCESS) {
        const statusData = response.data || {};
        setCounts({
          '0': parseInt(statusData.unAuditTotal || '0'), // 待审核数量
          '1': parseInt(statusData.auditedTotal || '0'), // 已审核数量
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 刷新表格
  const tableReload = () => {
    actionRef.current?.reload();
    setSelectedRowKeys([]);
  };

  // 打开单条审核Modal
  const handleSingleReview = (record: any, type: 'approve' | 'reject') => {
    console.log('单条审核记录:', record);
    setCurrentRecord(record);
    setReviewType(type);
    setIsBatch(false);
    setReviewModalVisible(true);
  };

  // 打开批量审核Modal
  const handleBatchReview = (type: 'approve' | 'reject') => {
    if (selectedRowKeys.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }
    setCurrentRecord(null);
    setReviewType(type);
    setIsBatch(true);
    setReviewModalVisible(true);
  };

  // 处理审核提交
  const handleReviewSubmit = async (remark: string) => {
    try {
      setLoading(true);

      // 获取审核的ID列表
      let idList = [];

      if (isBatch) {
        // 批量审核使用已选择的行keys
        console.log('批量审核选中的行:', selectedRowKeys);

        // 从选中行中获取正确的ID字段
        idList = selectedRowKeys.filter(
          (id) => id !== null && id !== undefined
        );

        console.log('处理后的批量审核ID列表:', idList);
      } else {
        // 单条审核从currentRecord中获取userId或id
        // 根据实际后端返回数据结构选择正确的ID字段
        const expertId = currentRecord?.userId || currentRecord?.id;
        console.log('单条审核专家ID:', expertId, '完整记录:', currentRecord);

        if (!expertId) {
          message.warning('获取专家ID失败，请重试');
          return;
        }

        idList = [expertId];
      }

      if (!idList || idList.length === 0) {
        message.warning('请选择要审核的专家');
        return;
      }

      // 设置审核状态 1-通过，2-驳回
      const auditStatus = reviewType === 'approve' ? '1' : '2';

      // 构建请求数据
      const requestData = {
        ids: idList,
        remark: remark || '',
      };

      console.log('提交审核数据:', requestData);

      // 调用审核API
      const { code, msg } = await request.post(
        `${API.AUDIT_EXPERT}/${auditStatus}`,
        {
          data: requestData,
        }
      );

      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(reviewType === 'approve' ? '审核通过成功' : '驳回成功');
        setReviewModalVisible(false);

        // 刷新表格数据
        tableReload();

        // 刷新统计数据
        fetchCounts();
      } else {
        message.error(msg || '审核操作失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核操作失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    setSelectedRowKeys([]);
    // 手动触发表格数据刷新 - 这会更新对应状态的统计数据
    actionRef.current?.reload();
    // 刷新统计数据
    fetchCounts();
  };

  // 处理导出数据
  const handleExportData = async () => {
    try {
      setLoading(true);
      // 调用导出API，type=3表示已审核专家导出
      const { data, fileName } = await request.post(API.EXPORT_DATA, {
        params: { type: 3 },
      });

      // 创建可读流
      const reader = data.getReader();
      const stream = new ReadableStream({
        start(controller) {
          // 声明一个 pumpRead 方法读取片段
          const pumpRead = (): any =>
            reader.read().then(({ done, value }: any) => {
              if (done) {
                // 结束读取 关闭读取流
                controller.close();
                return;
              }
              // 每次推入读取队列 并链式执行
              controller.enqueue(value);
              return pumpRead();
            });
          // 开始读取
          return pumpRead();
        },
      });

      // 转换为 blob
      const blob = await new Response(stream).blob();
      const objUrl = window.URL.createObjectURL(blob);

      // 创建下载链接
      const aLink = document.createElement('a');
      aLink.style.display = 'none';
      aLink.href = objUrl;
      aLink.setAttribute(
        'download',
        decodeURI(
          fileName || `已审核专家_${new Date().toLocaleDateString()}.xlsx`
        )
      );
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);

      // 释放URL对象
      window.URL.revokeObjectURL(objUrl);

      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 更多操作菜单项
  const moreMenuItems = [
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: '数据导出',
    },
  ];

  // 列定义
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '专家姓名',
      dataIndex: 'nickName', // 修改为后端接口字段
      ellipsis: true,
      fieldProps: {
        placeholder: '请输入专家姓名',
      },
    },
    {
      title: '手机号码',
      dataIndex: 'phonenumber', // 修改为后端接口字段
      ellipsis: true,
      fieldProps: {
        placeholder: '请输入手机号码',
      },
    },
    {
      title: '所属单位',
      dataIndex: 'deptName', // 修改为后端接口字段
      ellipsis: true,
      fieldProps: {
        placeholder: '请输入所属单位',
      },
    },
    {
      title: '职称',
      dataIndex: 'professor', // 修改为后端接口字段
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '专业领域',
      dataIndex: 'professionalFieldCh', // 修改为后端接口字段
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '所在省',
      dataIndex: 'province',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '所在市',
      dataIndex: 'city',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '注册日期',
      dataIndex: 'createTime', // 修改为后端接口字段
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '注册日期',
      dataIndex: 'registerDateRange',
      valueType: 'dateRange',
      hideInTable: true,
      hideInSearch: activeKey === '0', // 只在已审核状态显示日期筛选
      search: {
        transform: (value) => {
          return {
            registerDateStart: value[0],
            registerDateEnd: value[1],
          };
        },
      },
    },
    {
      title: '审核状态',
      dataIndex: 'reviewStatus',
      hideInTable: activeKey === '0', // 在待审核tab中隐藏状态列
      hideInSearch: true,
      render: (_, record) => {
        const status = record.status;
        if (status === '1') {
          return <Tag color="success">通过</Tag>;
        } else if (status === '2') {
          return <Tag color="error">驳回</Tag>;
        }
        return <Tag color="default">待审核</Tag>;
      },
    },
    {
      title: '审核日期',
      dataIndex: 'reviewDate',
      ellipsis: true,
      hideInTable: activeKey === '0', // 在待审核tab中隐藏
      hideInSearch: true,
    },
    {
      title: '审核人',
      dataIndex: 'reviewer',
      ellipsis: true,
      hideInTable: activeKey === '0', // 在待审核tab中隐藏
      hideInSearch: true,
    },
    {
      title: '审核意见',
      dataIndex: 'remark',
      hideInTable: activeKey === '0', // 在待审核tab中隐藏审核意见列
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      hideInTable: activeKey === '1', // 在已审核tab中隐藏操作列
      render: (_, record) => {
        // 待审核状态下显示通过/驳回按钮
        if (activeKey === '0') {
          return [
            <Button
              key="approve"
              type="link"
              size="small"
              onClick={() => handleSingleReview(record, 'approve')}
            >
              通过
            </Button>,
            <Button
              key="reject"
              type="link"
              size="small"
              danger
              onClick={() => handleSingleReview(record, 'reject')}
            >
              驳回
            </Button>,
          ];
        }
        return [];
      },
    },
  ];

  return (
    <PageContainer>
      <div className="expert-review-container">
        {/* 表格 */}
        <ProTable
          actionRef={actionRef}
          columns={columns}
          request={fetchExpertList}
          params={{ status: activeKey }} // 确保总是带上当前的status参数
          revalidateOnFocus={false} // 防止窗口获取焦点时重新加载
          manualRequest={false} // 第一次渲染时自动请求
          rowKey={(record) => record.userId || record.id} // 动态获取ID字段
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
          }}
          toolbar={{
            menu: {
              type: 'tab',
              activeKey: activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待审核 ({counts['0']})</span>,
                },
                {
                  key: '1',
                  label: <span>已审核 ({counts['1']})</span>,
                },
              ],
              onChange: (key) => handleTabChange(key as string),
            },
          }}
          rowSelection={
            activeKey === '0'
              ? {
                  selectedRowKeys,
                  onChange: (keys) => setSelectedRowKeys(keys),
                }
              : undefined
          }
          tableAlertRender={
            activeKey === '0'
              ? ({ selectedRowKeys, onCleanSelected }) => (
                  <Space size={24}>
                    <span>已选 {selectedRowKeys.length} 项</span>
                    <a onClick={onCleanSelected}>取消选择</a>
                  </Space>
                )
              : undefined
          }
          toolBarRender={() =>
            activeKey === '0'
              ? [
                  <Button
                    key="batchApprove"
                    type="primary"
                    onClick={() => handleBatchReview('approve')}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量通过
                  </Button>,
                  <Button
                    key="batchReject"
                    danger
                    onClick={() => handleBatchReview('reject')}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量驳回
                  </Button>,
                ]
              : [
                  <Dropdown
                    key="moreActions"
                    className="more-operate__btn"
                    arrow
                    menu={{
                      items: moreMenuItems,
                      onClick: ({ key }: { key: string }) => {
                        if (key === 'export') {
                          handleExportData();
                        }
                      },
                    }}
                  >
                    <Button>更多操作</Button>
                  </Dropdown>,
                ]
          }
          cardBordered
          bordered
        />

        {/* 审核弹窗 */}
        <ReviewModal
          visible={reviewModalVisible}
          type={reviewType}
          isBatch={isBatch}
          loading={loading}
          onCancel={() => setReviewModalVisible(false)}
          onSubmit={handleReviewSubmit}
        />
      </div>
    </PageContainer>
  );
};

export default ExpertReview;
