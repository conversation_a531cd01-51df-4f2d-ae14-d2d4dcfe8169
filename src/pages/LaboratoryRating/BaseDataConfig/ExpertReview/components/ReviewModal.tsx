import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal } from 'antd';

const { TextArea } = Input;

interface ReviewModalProps {
  visible: boolean;
  type: 'approve' | 'reject';
  isBatch: boolean;
  loading: boolean;
  onCancel: () => void;
  onSubmit: (remark: string) => void;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  visible,
  type,
  isBatch,
  loading,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [remark, setRemark] = useState<string>('');

  // 每次Modal显示时重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      setRemark('');
    }
  }, [visible, form]);

  // 处理提交
  const handleSubmit = () => {
    form.validateFields().then(() => {
      onSubmit(remark);
    });
  };

  // 弹窗关闭时重置表单
  const handleCancel = () => {
    form.resetFields();
    setRemark('');
    onCancel();
  };

  return (
    <Modal
      title={`${isBatch ? '批量' : ''}审核${
        type === 'approve' ? '通过' : '驳回'
      }`}
      open={visible}
      onCancel={handleCancel}
      maskClosable={false}
      destroyOnClose
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type={type === 'approve' ? 'primary' : 'primary'}
          danger={type === 'reject'}
          loading={loading}
          onClick={handleSubmit}
        >
          确认{type === 'approve' ? '通过' : '驳回'}
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          name="remark"
          label={
            <span style={{ color: type === 'reject' ? '#ff4d4f' : undefined }}>
              审核意见
            </span>
          }
          rules={[{ required: true, message: '请输入审核意见' }]}
        >
          <TextArea
            placeholder={`请输入${isBatch ? '批量' : ''}${
              type === 'approve' ? '通过' : '驳回'
            }意见`}
            rows={4}
            value={remark}
            onChange={(e: {
              target: { value: React.SetStateAction<string> };
            }) => setRemark(e.target.value)}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReviewModal;
