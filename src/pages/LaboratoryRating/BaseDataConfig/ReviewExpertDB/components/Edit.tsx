/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
} from 'antd';
import { getDict } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  EditableProTable,
  ProForm,
  ProFormInstance,
} from '@ant-design/pro-components';
import request from '@/utils/request';

type EditProps = {
  editType: 'add' | 'edit' | 'view';
  currentRecord?: any;
  onClose: () => void;
};

// 定义专家数据类型
interface ExpertData {
  id: string;
  name: string;
  contactNumber: string;
  organization: string;
  position: string;
  expertise: string[] | string; // 修改为字符串数组或字符串，支持多选
  remarks?: string;
  editable?: boolean;
  province?: string;
  city?: string;
  area?: string;
  provinceId?: number; // 添加省份ID
  cityId?: number; // 添加城市ID
  areaId?: number; // 添加区域ID
  rawData?: any; // 添加rawData字段
}

// 删除模拟专家数据（不再需要）

const Edit: React.FC<EditProps> = ({ editType, currentRecord, onClose }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance>();

  // 专家表格数据
  const [dataSource, setDataSource] = useState<ExpertData[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // 添加专业领域枚举数据
  const [expertiseAreaList, setExpertiseAreaList] = useState<
    Record<string, any>[]
  >([]);

  // 人员选择Modal相关状态
  const [expertSelectVisible, setExpertSelectVisible] =
    useState<boolean>(false);
  const [nameKeyword, setNameKeyword] = useState<string>('');
  const [organizationKeyword, setOrganizationKeyword] = useState<string>('');
  const [selectedExperts, setSelectedExperts] = useState<any[]>([]);
  const [expertList, setExpertList] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [expertTableLoading, setExpertTableLoading] = useState(false);

  // 添加获取专业领域枚举数据的函数
  /**
   * 获取专业领域枚举
   */
  const queryExpertiseAreaEnums = async () => {
    try {
      const { code, data, msg } = await getDict('expertise_expert_area');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setExpertiseAreaList(data);
    } catch (error) {
      console.error('获取专业领域枚举失败:', error);
      message.error('获取专业领域枚举失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    if (editType === 'edit' || editType === 'view') {
      if (currentRecord) {
        console.log('当前记录数据:', currentRecord);

        // 处理expertise字段，如果是逗号分隔的字符串，转换为数组
        let expertiseValue = currentRecord.professionalField || '';
        if (
          typeof expertiseValue === 'string' &&
          expertiseValue.includes(',')
        ) {
          expertiseValue = expertiseValue.split(',');
        } else if (!expertiseValue || expertiseValue === '') {
          // 确保空值时是空数组
          expertiseValue = [];
        } else if (typeof expertiseValue === 'string') {
          // 如果是单个值的字符串，转为数组
          expertiseValue = [expertiseValue];
        }

        // 将currentRecord转换为表格数据格式
        setDataSource([
          {
            id: currentRecord.userId || 'temp-id',
            name: currentRecord.nickName || '',
            contactNumber: currentRecord.phonenumber || '',
            organization: currentRecord.deptName || '',
            position: currentRecord.professor || '',
            expertise: expertiseValue,
            remarks: currentRecord.remark || '',
            editable: editType !== 'view',
            province: currentRecord.provinceName || '',
            city: currentRecord.cityName || '',
            area: currentRecord.area || '',
            provinceId: currentRecord.provinceId || undefined,
            cityId: currentRecord.cityId || undefined,
            areaId: currentRecord.areaId || undefined,
            rawData: currentRecord,
          },
        ]);

        if (editType !== 'view') {
          setEditableKeys([currentRecord.userId || 'temp-id']);
        }
      }
    } else {
      // 新增模式，重置所有状态
      setDataSource([]);
      setEditableKeys([]);
      setSelectedRowKeys([]);
      setSelectedExperts([]);
    }
  }, [currentRecord, editType]);

  // 获取专家列表
  const fetchExpertList = async (
    page = 1,
    pageSize = 10,
    name = '',
    organization = ''
  ) => {
    setExpertTableLoading(true);
    try {
      // 调用真实API接口
      const params = {
        pageNum: page,
        pageSize: pageSize,
        nickName: name, // 按姓名筛选
        orgName: organization, // 按机构名称筛选
      };

      const { code, rows, total, msg } = await request.get(
        '/system/user/listNotInLibrary',
        { params }
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取专家列表失败');
        return;
      }

      // 转换响应数据为组件所需格式
      const expertData = rows?.map((item: any) => {
        // 处理专业领域字段
        let expertiseValue =
          item.professionalFieldCh || item.professionalField || '';
        // 将字符串类型的专业领域转换为数组
        if (
          typeof expertiseValue === 'string' &&
          expertiseValue.includes(',')
        ) {
          expertiseValue = expertiseValue.split(',');
        } else if (!expertiseValue || expertiseValue === '') {
          expertiseValue = []; // 空值时确保是空数组
        } else if (typeof expertiseValue === 'string') {
          expertiseValue = [expertiseValue]; // 单值字符串转为数组
        }

        return {
          id: item.userId,
          name: item.nickName,
          contactNumber: item.phonenumber,
          organization: item.dept?.deptName || '',
          position: item.professor || '',
          expertise: expertiseValue,
          remarks: item.remark || '',
          // 地区信息
          province: item.dept?.province_name || '',
          city: item.dept?.city_name || '',
          area: item.dept?.area_name || '',
          // 地区ID信息
          provinceId: item.dept?.provinceId || item.provinceId,
          cityId: item.dept?.cityId || item.cityId,
          areaId: item.dept?.areaId || item.areaId,
          // 保存原始数据，以便后续处理
          rawData: item,
        };
      });

      setExpertList(expertData || []);
      setPagination({
        current: page,
        pageSize,
        total: total || 0,
      });
    } catch (error) {
      console.error('获取专家列表失败:', error);
      message.error('获取专家列表失败');
    } finally {
      setExpertTableLoading(false);
    }
  };

  // 初始加载专家列表
  useEffect(() => {
    if (expertSelectVisible) {
      // 当打开选择Modal时，将已经在表格中的专家同步到selectedExperts中
      if (dataSource.length > 0 && selectedExperts.length === 0) {
        // 将已有表格数据转换为选择项
        const existingExperts = dataSource.map((item) => ({
          id: item.id,
          name: item.name,
          contactNumber: item.contactNumber,
          organization: item.organization,
          position: item.position,
          expertise: item.expertise,
          remarks: item.remarks,
          province: item.province,
          city: item.city,
          area: item.area,
          provinceId: item.provinceId,
          cityId: item.cityId,
          areaId: item.areaId,
          rawData: item.rawData,
        }));
        setSelectedExperts(existingExperts);
      }

      fetchExpertList(
        pagination.current,
        pagination.pageSize,
        nameKeyword,
        organizationKeyword
      );
    }
  }, [expertSelectVisible]);

  // 处理重置
  const handleReset = () => {
    setNameKeyword('');
    setOrganizationKeyword('');
    // 重置搜索条件并获取第一页数据，但不清除已选择的专家
    fetchExpertList(1, pagination.pageSize, '', '');
  };

  // 处理搜索
  const handleSearch = () => {
    fetchExpertList(1, pagination.pageSize, nameKeyword, organizationKeyword);
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
    fetchExpertList(
      pagination.current,
      pagination.pageSize,
      nameKeyword,
      organizationKeyword
    );
  };

  // 处理选择专家
  const handleSelectExperts = (
    selectedRowKeys: React.Key[],
    selectedRows: any[]
  ) => {
    // 创建一个Map用于快速查找已有的选择项
    const existingSelectMap = new Map(
      selectedExperts.map((item) => [item.id, item])
    );

    // 获取当前页选择的专家数据
    const currentPageSelected = selectedRows;

    // 构建更新后的已选择专家列表
    const updatedExperts = [...selectedExperts];

    // 添加新选择的专家
    currentPageSelected.forEach((expert) => {
      if (!existingSelectMap.has(expert.id)) {
        updatedExperts.push(expert);
      }
    });

    // 移除取消选择的专家
    const updatedExpertsFiltered = updatedExperts.filter((expert) =>
      selectedRowKeys.includes(expert.id)
    );

    // 更新状态
    setSelectedExperts(updatedExpertsFiltered);
  };

  // 处理确认选择
  const handleConfirmSelect = () => {
    if (selectedExperts.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }

    // 添加调试日志
    console.log('已选择专家:', selectedExperts);

    // 选中的专家信息已经是正确的格式，直接使用
    const newData = selectedExperts.map((expert) => {
      // 处理expertise字段，如果是逗号分隔的字符串，转换为数组
      let expertiseValue = expert.expertise;
      if (typeof expertiseValue === 'string' && expertiseValue.includes(',')) {
        expertiseValue = expertiseValue.split(',');
      } else if (!expertiseValue || expertiseValue === '') {
        // 确保空值时是空数组而非空字符串
        expertiseValue = [];
      } else if (typeof expertiseValue === 'string') {
        // 如果是单个值的字符串，转为数组
        expertiseValue = [expertiseValue];
      }

      // 返回包含所有字段的完整数据
      return {
        id: expert.id,
        name: expert.name,
        contactNumber: expert.contactNumber,
        organization: expert.organization,
        // 默认空字符串，这些字段需要用户填写
        position: expert.position || '',
        expertise: expertiseValue, // 确保这里是数组
        remarks: expert.remarks || '',
        // 完整地区信息
        province: expert.province || '',
        city: expert.city || '',
        area: expert.area || '',
        // 完整ID信息
        provinceId: expert.provinceId,
        cityId: expert.cityId,
        areaId: expert.areaId,
        // 设置为可编辑
        editable: true,
        // 保存rawData供后续处理
        rawData: expert.rawData,
      };
    });

    // 添加调试日志
    console.log('转换后的表格数据:', newData);

    // 创建一个Map用于快速查找现有表格数据中的专家
    const existingExpertsMap = new Map(
      dataSource.map((item) => [item.id, item])
    );

    // 合并现有数据和新选择的数据，避免重复
    const mergedData = [...dataSource];

    // 只添加表格中不存在的专家
    newData.forEach((expert) => {
      if (!existingExpertsMap.has(expert.id)) {
        mergedData.push(expert);
      }
    });

    // 设置可编辑行，确保所有行都可编辑
    const newEditableKeys = mergedData.map((item) => item.id);

    // 更新状态
    setDataSource(mergedData);
    setEditableKeys(newEditableKeys);

    // 添加确认日志
    console.log('已更新dataSource:', mergedData);
    console.log('已更新editableKeys:', newEditableKeys);

    // 关闭选择Modal
    setExpertSelectVisible(false);
  };

  // 处理删除专家
  const handleDeleteExperts = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的专家');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除已选择的${selectedRowKeys.length}条专家数据吗？`,
      onOk: () => {
        // 从dataSource中过滤掉已选中的行
        const newDataSource = dataSource.filter(
          (item) => !selectedRowKeys.includes(item.id)
        );
        setDataSource(newDataSource);
        setSelectedRowKeys([]);
      },
    });
  };

  // 添加行选择处理方法
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys);
    },
  };

  // 保存专家信息
  const handleSave = async () => {
    if (dataSource.length === 0) {
      message.warning('请先选择或填写专家信息');
      return;
    }

    try {
      setLoading(true);

      // 确保获取到最新的dataSource数据
      console.log('保存按钮点击时的dataSource:', dataSource);

      // 打印每个专家的详细信息，用于调试
      dataSource.forEach((expert, index) => {
        console.log(`专家 ${index + 1} 详情:`, {
          id: expert.id,
          name: expert.name,
          position: expert.position,
          expertise: expert.expertise,
          remarks: expert.remarks,
        });
      });

      // 处理所有专家数据
      const requestData = dataSource.map((expertData) => {
        // 确保所有关键字段都有值
        const position = expertData.position || '';

        // 转换为API需要的格式 - 处理expertise可能为undefined或空数组的情况
        const expertiseValue = Array.isArray(expertData.expertise)
          ? expertData.expertise.join(',')
          : typeof expertData.expertise === 'string'
          ? expertData.expertise
          : '';

        // 获取专业领域的标签文本
        let expertiseLabels = '';
        if (
          Array.isArray(expertData.expertise) &&
          expertData.expertise.length > 0
        ) {
          // 将数值字典值转换为显示文本
          const labelList = expertData.expertise.map((value: string) => {
            const item = expertiseAreaList.find(
              (area) => area.dictValue === value
            );
            return item ? item.dictLabel : value;
          });
          expertiseLabels = labelList.join(',');
        }

        const remarks = expertData.remarks || '';

        console.log('处理专家详细字段:', {
          id: expertData.id,
          name: expertData.name,
          职称: position,
          专业领域值: expertiseValue,
          专业领域标签: expertiseLabels,
          原始专业领域: expertData.expertise,
          备注: remarks,
        });

        // 构建请求参数
        const requestItem = {
          userId: expertData.id, // 专家ID，使用id字段
          nickName: expertData.name, // 专家姓名
          phonenumber: expertData.contactNumber, // 联系方式
          deptName: expertData.organization, // 所属单位
          professor: position, // 职称（可选）
          professionalField: expertiseValue, // 专业领域编码（可选）
          professionalFieldCh: expertiseLabels, // 专业领域标签
          provinceId: expertData.provinceId || undefined,
          provinceName: expertData.province || '',
          cityId: expertData.cityId || undefined,
          cityName: expertData.city || '',
          remark: remarks, // 备注（可选）
        };

        // 打印单个请求项，用于调试
        console.log('请求项详情:', requestItem);
        return requestItem;
      });

      // 输出请求数据以便调试
      console.log('保存专家数据:', requestData);

      // 根据编辑类型选择不同的API路径和数据格式
      const apiUrl =
        editType === 'add'
          ? '/gradeConfig/expert/add'
          : '/gradeConfig/expert/edit';

      // 准备发送的数据 - 新增模式用数组，编辑模式用单个对象
      const postData = editType === 'add' ? requestData : requestData[0];

      console.log('调用API:', apiUrl);
      console.log('发送数据格式:', editType === 'add' ? '数组' : '对象');
      console.log('发送数据:', postData);

      // 调用API
      const { code, msg } = await request.post(apiUrl, {
        data: postData,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '保存失败');
        return;
      }

      message.success(QUERY_SUCCESS_MSG);

      // 重置所有状态
      setDataSource([]);
      setEditableKeys([]);
      setSelectedRowKeys([]);
      setSelectedExperts([]);

      onClose();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 定义表格列
  const columns = [
    {
      title: '专家姓名',
      dataIndex: 'name',
      formItemProps: {
        rules: [{ required: true, message: '请输入专家姓名' }],
      },
      width: '15%',
      editable: () => false, // 设置为不可编辑
    },
    {
      title: '联系方式',
      dataIndex: 'contactNumber',
      formItemProps: {
        rules: [
          { required: true, message: '请输入联系方式' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
        ],
      },
      width: '15%',
      editable: () => false, // 设置为不可编辑
    },
    {
      title: '所属单位',
      dataIndex: 'organization',
      formItemProps: {
        rules: [{ required: true, message: '请输入所属单位' }],
      },
      width: '20%',
      editable: () => false, // 设置为不可编辑
    },
    {
      title: '职称',
      dataIndex: 'position',
      valueType: 'text', // 修改为普通文本输入框
      formItemProps: {
        rules: [], // 可选字段
      },
      width: '10%',
      editable: (_: any, record: ExpertData, index: number) => true, // 总是可以编辑
    },
    {
      title: '专业领域',
      dataIndex: 'expertise',
      valueType: 'select',
      fieldProps: {
        mode: 'multiple', // 添加多选模式
        placeholder: '请选择专业领域',
        maxTagCount: 'responsive', // 自适应标签显示数量
      },
      valueEnum: expertiseAreaList?.reduce((prev, curr) => {
        prev[curr.dictValue] = { text: curr.dictLabel };
        return prev;
      }, {}), // 使用字典数据生成下拉选项
      formItemProps: {
        rules: [], // 可选字段
      },
      width: '15%',
      editable: (_: any, record: ExpertData, index: number) => true, // 总是可以编辑
      render: (_: unknown, record: ExpertData) => {
        // 如果expertise是数组，显示以逗号分隔的标签
        if (Array.isArray(record.expertise)) {
          // 将数值字典值转换为显示文本
          const expertiseLabels = record.expertise.map((value: string) => {
            const item = expertiseAreaList.find(
              (area) => area.dictValue === value
            );
            return item ? item.dictLabel : value;
          });
          return expertiseLabels.join('、');
        }
        // 如果是字符串，可能是从后端直接获取的已格式化文本
        return record.expertise;
      },
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      valueType: 'text', // 修改为普通文本输入框
      width: '25%',
      editable: (_: any, record: ExpertData, index: number) => true, // 总是可以编辑
    },
  ];

  // 专家选择表格的列
  const expertColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '机构名称',
      dataIndex: 'organization',
      key: 'organization',
      ellipsis: true,
    },
  ];

  // 添加调试用的副作用，监控dataSource变化
  useEffect(() => {
    console.log('dataSource已更新:', dataSource);
  }, [dataSource]);

  // 在useEffect中调用获取专业领域枚举函数
  useEffect(() => {
    queryExpertiseAreaEnums(); // 添加获取专业领域枚举数据
  }, []);

  // 处理Modal取消
  const handleModalCancel = () => {
    setExpertSelectVisible(false);
    // 不清空已选专家数据，保留给用户确认选择
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 p-6 overflow-y-auto bg-[#F5F5F5]">
        <div className="bg-white p-6 rounded-md mb-4">
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-medium m-0">专家明细</h3>
            <Space>
              {editType === 'add' && (
                <>
                  <Button
                    type="primary"
                    onClick={() => setExpertSelectVisible(true)}
                  >
                    选择
                  </Button>
                  <Button
                    danger
                    onClick={handleDeleteExperts}
                    disabled={selectedRowKeys.length === 0}
                  >
                    删除
                    {selectedRowKeys.length > 0
                      ? `(${selectedRowKeys.length})`
                      : ''}
                  </Button>
                </>
              )}
            </Space>
          </div>

          <EditableProTable
            rowKey="id"
            columns={columns}
            value={dataSource}
            onChange={(value) => {
              console.log('表格数据变化:', value);
              // 深度克隆数据以避免引用问题
              const newData = JSON.parse(JSON.stringify(value));
              setDataSource(newData);
            }}
            recordCreatorProps={false}
            rowSelection={rowSelection}
            editable={{
              type: 'multiple',
              editableKeys,
              onChange: setEditableKeys,
              actionRender: (row, config, defaultDoms) => {
                console.log('编辑行:', row);
                return [defaultDoms.save, defaultDoms.cancel];
              },
              onValuesChange: (record, recordList) => {
                console.log('行数据变化, 记录:', record);
                console.log('所有行数据:', recordList);
                // 使用recordList更新dataSource
                setDataSource([...recordList]);
              },
            }}
            bordered
            pagination={false}
          />
        </div>
      </div>

      {editType !== 'view' && (
        <div className="p-4 border-t bg-white flex justify-center">
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSave}>
              保存
            </Button>
          </Space>
        </div>
      )}

      {editType === 'view' && (
        <div className="p-4 border-t bg-white flex justify-center">
          <Button onClick={onClose}>关闭</Button>
        </div>
      )}

      {/* 人员选择Modal */}
      <Modal
        title="人员选择"
        open={expertSelectVisible}
        onCancel={handleModalCancel}
        footer={[
          <Button key="cancel" onClick={handleModalCancel}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            onClick={handleConfirmSelect}
            disabled={selectedExperts.length === 0}
          >
            确认
          </Button>,
        ]}
        width={750}
        destroyOnClose
      >
        <div className="my-4">
          <Space className="w-full flex flex-row justify-between">
            <Space className="flex-1 flex flex-row gap-4">
              <div className="flex-1">
                <span>姓名：</span>
                <Input
                  placeholder="请输入姓名"
                  value={nameKeyword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setNameKeyword(e.target.value)
                  }
                  onPressEnter={handleSearch}
                  style={{ width: 150 }}
                />
              </div>
              <div className="flex-1">
                <span>机构名称：</span>
                <Input
                  placeholder="请输入机构名称"
                  value={organizationKeyword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setOrganizationKeyword(e.target.value)
                  }
                  onPressEnter={handleSearch}
                  style={{ width: 150 }}
                />
              </div>
            </Space>
            <Space className="w-[135px]">
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
            </Space>
          </Space>
        </div>

        <Table
          rowKey="id"
          columns={expertColumns}
          dataSource={expertList}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          loading={expertTableLoading}
          onChange={handleTableChange}
          rowSelection={{
            type: 'checkbox',
            onChange: handleSelectExperts,
            preserveSelectedRowKeys: true,
            selectedRowKeys: selectedExperts.map((item) => item.id),
          }}
          size="small"
        />
      </Modal>
    </div>
  );
};

export default Edit;
