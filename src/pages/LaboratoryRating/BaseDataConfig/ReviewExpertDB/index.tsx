/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Button, Drawer, Dropdown, message, Modal, Popconfirm } from 'antd';
import {
  exportExpertDataApi,
  getExpertListApi,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { DeleteOutlined, ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import request from '@/utils/request';
import './index.less';

const ReviewExpertDB: React.FC = () => {
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  // 选中的行的key
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 搜索条件
  const [expertName, setExpertName] = useState<string>('');
  const [organization, setOrganization] = useState<string>('');
  const [contactNumber, setContactNumber] = useState<string>('');

  // 列定义
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '专家姓名',
      dataIndex: 'name',
      ellipsis: true,
      render: (_, record) => (
        <Button key={record?.id} type="link" onClick={() => handleView(record)}>
          {record.name}
        </Button>
      ),
    },
    {
      title: '联系方式',
      dataIndex: 'contactNumber',
      ellipsis: true,
    },
    {
      title: '所属单位',
      dataIndex: 'organization',
      ellipsis: true,
    },
    {
      title: '职称',
      dataIndex: 'position',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '专业领域',
      dataIndex: 'expertise',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '添加人',
      dataIndex: 'createdBy',
      hideInSearch: true,
    },
    {
      title: '添加时间',
      dataIndex: 'createdTime',
      hideInSearch: true,
    },
    {
      title: '最后更新人',
      dataIndex: 'updatedBy',
      hideInSearch: true,
    },
    {
      title: '最后更新时间',
      dataIndex: 'updatedTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="update"
          type="link"
          size="small"
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该专家?"
          onConfirm={() => handleDelete(record?.id)}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 刷新表格
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 处理搜索
  const handleSearch = () => {
    tableReload();
  };

  // 处理重置
  const handleReset = () => {
    setExpertName('');
    setOrganization('');
    setContactNumber('');
    tableReload();
  };

  // 处理新增
  const handleAdd = () => {
    setEditType('add');
    setCurrentRecord(null);
    setDrawerVisible(true);
  };

  // 处理编辑
  const handleEdit = async (record: any) => {
    try {
      setLoading(true);

      // 调用获取专家详情API
      const { code, data, msg } = await request.post(
        `/gradeConfig/expert/${record.id}`
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取专家详情失败');
        return;
      }

      // 设置编辑类型和当前记录
      setEditType('edit');
      setCurrentRecord(data); // 使用API返回的详情数据
      setDrawerVisible(true);
    } catch (error) {
      console.error('获取专家详情失败:', error);
      message.error('获取专家详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除
  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      // 使用真实API调用，传入单个ID组成的数组
      // 注意：id 是从userId映射来的，直接使用即可
      const { code, msg } = await request.post('/gradeConfig/expert/delete', {
        data: [id],
      });

      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 抽屉关闭后的回调
  const afterDrawerClose = () => {
    tableReload();
  };

  // 处理导出
  const handleExportData = async () => {
    try {
      setLoading(true);
      message.loading('正在导出数据，请稍候...');

      // 调用导出API，type=1表示评审专家库导出
      const { data, fileName } = await request.post('/gradeConfig/export', {
        params: { type: 1 },
      });

      if (!data || !data.getReader) {
        throw new Error('无法获取数据流');
      }

      const reader = data.getReader();
      // 手动控制读取流以便回传下载进度
      const stream = new ReadableStream({
        start(controller) {
          // 声明一个 pumpRead 方法读取片段
          // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
          const pumpRead = (): any =>
            reader.read().then(({ done, value }: any) => {
              // done  - 当 stream 传完所有数据时则变成 true
              // value - 数据片段 - done 为 true 时为 undefined
              if (done) {
                // 结束读取 关闭读取流
                controller.close();
                return;
              }
              // 每次推入读取队列 并链式执行
              controller.enqueue(value);
              return pumpRead();
            });
          // 开始读取
          return pumpRead();
        },
      });

      // 最后我们通过 Response 函数接收这个文件流 并转为 blob
      const blob = await new Response(stream).blob();
      const objUrl = window.URL.createObjectURL(blob);
      const aLink = document.createElement('a');
      aLink.style.display = 'none';
      aLink.href = objUrl;
      aLink.setAttribute('download', decodeURI(fileName || '评审专家库.xlsx'));
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(objUrl);

      setLoading(false);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后再试');
      setLoading(false);
    }
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          // 使用真实API调用，传入多个ID组成的数组
          // selectedRowKeys已经是从userId映射的id数组，直接使用即可
          const { code, msg } = await request.post(
            '/gradeConfig/expert/delete',
            {
              data: selectedRowKeys,
            }
          );

          if (code === codeDefinition.QUERY_SUCCESS) {
            message.success(QUERY_SUCCESS_MSG);
            setSelectedRowKeys([]);
            tableReload();
          } else {
            message.error(msg || '批量删除失败');
          }
          // 返回一个resolved Promise，确保Modal知道异步操作已完成
          return Promise.resolve();
        } catch (error) {
          console.error('批量删除错误:', error);
          message.error('批量删除失败，请检查后再试');
          // 返回一个rejected Promise，确保Modal知道异步操作已失败
          return Promise.reject(error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 更多操作菜单项
  const moreMenuItems = [
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: '数据导出',
    },
    {
      key: 'batchDelete',
      icon: <DeleteOutlined />,
      label: '批量删除',
      disabled: selectedRowKeys.length === 0,
    },
  ];

  // 处理查看
  const handleView = async (record: any) => {
    try {
      setLoading(true);

      // 调用获取专家详情API
      const { code, data, msg } = await request.post(
        `/gradeConfig/expert/${record.id}`
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取专家详情失败');
        return;
      }

      // 设置编辑类型和当前记录
      setEditType('view');
      setCurrentRecord(data); // 使用API返回的详情数据
      setDrawerVisible(true);
    } catch (error) {
      console.error('获取专家详情失败:', error);
      message.error('获取专家详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer>
      <div className="review-expert-db-container">
        {/* 表格 */}
        <ProTable
          actionRef={actionRef}
          cardBordered
          bordered
          columns={columns}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys),
          }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          request={async (params) => {
            setLoading(true);
            try {
              // 转换参数
              const requestParams = {
                pageNum: params.current,
                pageSize: params.pageSize,
                nickName: params.name, // 专家姓名
                deptName: params.organization, // 机构名称
                phonenumber: params.contactNumber, // 联系方式
              };

              const { code, data, msg } = await getExpertListApi(requestParams);

              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              // 转换响应数据为组件所需格式
              const records = data.rows?.map((item: any) => ({
                id: item.userId,
                name: item.nickName,
                contactNumber: item.phonenumber,
                organization: item.deptName,
                position: item.professor,
                expertise: item.professionalFieldCh || item.professionalField,
                remarks: item.remark,
                createdBy: item.createName,
                createdTime: item.createTime,
                updatedBy: item.updateName,
                updatedTime: item.updateTime,
                // 添加额外的字段映射
                province: item.provinceName,
                city: item.cityName,
              }));

              return {
                data: records || [],
                success: true,
                total: data.total || 0,
              };
            } catch (error) {
              console.error('获取数据失败:', error);
              message.error('获取数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            } finally {
              setLoading(false);
            }
          }}
          toolBarRender={() => [
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>,
            <Dropdown
              className="more-operate__btn"
              arrow
              menu={{
                items: moreMenuItems,
                onClick: ({ key }: { key: string }) => {
                  if (key === 'export') {
                    handleExportData();
                  } else if (key === 'batchDelete') {
                    handleBatchDelete();
                  }
                },
              }}
            >
              <Button>更多操作</Button>
            </Dropdown>,
          ]}
        />

        {/* 抽屉 */}
        <Drawer
          title={`${
            editType === 'add' ? '新增' : editType === 'edit' ? '编辑' : '查看'
          }专家`}
          placement="right"
          width="60%"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          afterOpenChange={(visible) => {
            if (!visible) {
              afterDrawerClose();
            }
          }}
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
        >
          <Edit
            editType={editType}
            currentRecord={currentRecord}
            onClose={() => setDrawerVisible(false)}
          />
        </Drawer>
      </div>
    </PageContainer>
  );
};

export default ReviewExpertDB;
