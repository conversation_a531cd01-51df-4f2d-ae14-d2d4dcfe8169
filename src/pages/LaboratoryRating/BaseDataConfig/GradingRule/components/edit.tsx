/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Card, Form, Input, message, Radio } from 'antd';
import {
  addGradingRule<PERSON>pi,
  grading<PERSON>uleList<PERSON>pi,
  TGradingRuleDetail,
  updateGradingRuleApi,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { EditableFormInstance, ProColumns } from '@ant-design/pro-components';
import EditableProTable from '@/components/EditableProTable';
import request from '@/utils/request';

// 定义API接口
const API = {
  ADD_GROUP: '/gradeConfig/addGroup',
  UPDATE_GROUP: '/gradeConfig/editGroup', // 修改为正确的更新接口地址
  GET_GRADE_DETAIL: '/gradeConfig/grade', // 添加获取等级详情的API
};

// 定义Props类型
interface EditProps {
  editType?: 'add' | 'edit' | 'view';
  currentRecord?: any;
  onClose?: () => void;
}

// 定义等级数据类型
interface GradeSet {
  id?: string | number;
  grade: string;
  floorScore: number;
  topScore: number;
}

// 定义提交数据类型
interface SubmitData {
  id?: string | number;
  name: string;
  status: string;
  remark: string;
  sets: GradeSet[];
}

const Edit: React.FC<EditProps> = ({
  editType = 'add',
  currentRecord,
  onClose,
}) => {
  const [dataSource, setDataSource] = useState<GradeSet[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [curSelectId, setCurSelectId] = useState('');
  const initialDataRef = useRef<Record<string, any>>({}); // 使用 ref 保存每行的初始数据
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);

  const editableFormRef = useRef<EditableFormInstance>();

  // 存储的原始数据
  const [originalDataSource, setOriginalDataSource] = useState<GradeSet[]>([]);

  const editableProTableKey = useRef(Math.random().toString());

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '实验室等级',
      dataIndex: 'grade',
      ellipsis: true,
      formItemProps: {
        rules: [{ required: true, message: '实验室等级必填' }],
      },
    },
    {
      title: '分数下限（含）',
      dataIndex: 'floorScore',
      valueType: 'digit',
      fieldProps: {
        max: 100,
        min: 0,
        precision: 2,
      },
      formItemProps: {
        rules: [{ required: true, message: '分数下限必填' }],
      },
    },
    {
      title: '分数上限（不含）',
      dataIndex: 'topScore',
      valueType: 'digit',
      fieldProps: {
        max: 100,
        min: 0,
        precision: 2,
      },
      formItemProps: {
        rules: [{ required: true, message: '分数上限必填' }],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      render: (text, record, _, action) => {
        // 在查看模式下不显示操作
        if (editType === 'view') return null;

        return [
          <Button
            key="edit"
            type="link"
            onClick={() => {
              setCurSelectId(record.id);
              initialDataRef.current[record.id] = { ...record }; // 保存当前行的初始数据
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </Button>,
          <Button
            key="delete"
            type="link"
            danger
            onClick={() => {
              // 删除当前行
              const newDataSource = dataSource.filter(
                (item) => item.id !== record.id
              );
              setDataSource(newDataSource);
            }}
          >
            删除
          </Button>,
        ];
      },
      fixed: 'right',
    },
  ];

  // 获取数据源
  const getDataSource = async () => {
    try {
      setLoading(true);
      // 判断是否是编辑模式且有currentRecord
      if (editType === 'edit' && currentRecord?.id) {
        // 获取详情数据
        const { code, data, msg } = await request.get(
          `${API.GET_GRADE_DETAIL}/${currentRecord.id}`
        );
        if (code === codeDefinition.QUERY_SUCCESS) {
          setEditableRowKeys([]);
          // 设置等级数据
          if (data && data.sets) {
            setDataSource(data.sets);
            setOriginalDataSource(data.sets);
          } else {
            setDataSource([]);
            setOriginalDataSource([]);
          }

          // 设置基本表单数据
          form.setFieldsValue({
            name: data.name,
            remark: data.remark,
            status: data.status || '0',
          });
        } else {
          message.error(msg || '获取详情失败');
        }
      } else if (editType === 'view' && currentRecord?.id) {
        // 查看模式
        const { code, data, msg } = await request.get(
          `${API.GET_GRADE_DETAIL}/${currentRecord.id}`
        );
        if (code === codeDefinition.QUERY_SUCCESS) {
          setEditableRowKeys([]);
          // 设置等级数据
          if (data && data.sets) {
            setDataSource(data.sets);
            setOriginalDataSource(data.sets);
          } else {
            setDataSource([]);
            setOriginalDataSource([]);
          }

          // 设置基本表单数据
          form.setFieldsValue({
            name: data.name,
            remark: data.remark,
            status: data.status || '0',
          });
        } else {
          message.error(msg || '获取详情失败');
        }
      } else {
        // 新增模式下清空数据源
        setDataSource([]);
        setOriginalDataSource([]);
        setEditableRowKeys([]);
        // 新增模式下设置默认值
        form.setFieldsValue({
          name: '',
          remark: '',
          status: '0', // 默认未使用
        });
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 调用获取数据源方法
    getDataSource();
  }, [editType, currentRecord, form]);

  // 本地保存行数据，不再调用API
  const handleSaveRow = async (row: any) => {
    try {
      // 获取表单数据
      const values = await editableFormRef.current?.validateFields();

      // 更新本地数据源
      const updatedDataSource = [...dataSource];
      const index = updatedDataSource.findIndex((item) => item.id === row.id);

      if (index > -1) {
        // 更新现有行
        updatedDataSource[index] = {
          ...updatedDataSource[index],
          ...values[row.id],
        };
      } else {
        // 添加新行
        updatedDataSource.push({
          id: row.id,
          ...values[row.id],
        });
      }

      // 设置数据源
      setDataSource(updatedDataSource);

      // 退出编辑状态
      setEditableRowKeys([]);

      // 重置表格键以避免重复行问题
      editableProTableKey.current = Math.random().toString();
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('请填写完整的表单信息');
    }
  };

  // 修改 recordCreatorProps 以防止自动添加重复行
  const handleRecordCreatorClick = () => {
    // 生成新记录
    const newRecord = {
      id: `temp-${Date.now()}`,
      grade: '',
      floorScore: 0,
      topScore: 0,
    };

    // 将新记录添加到数据源
    setDataSource([...dataSource, newRecord]);

    // 立即进入编辑模式
    setEditableRowKeys([newRecord.id]);
  };

  // 保存整个表单
  const handleSubmit = async () => {
    try {
      setLoading(true);
      await form.validateFields();
      const formValues = form.getFieldsValue();

      // 检查是否有等级数据
      if (dataSource.length === 0) {
        message.error('请至少添加一个等级');
        return;
      }

      // 检查等级数据是否有重叠或冲突
      const sortedData = [...dataSource].sort(
        (a, b) => a.floorScore - b.floorScore
      );
      for (let i = 0; i < sortedData.length - 1; i++) {
        if (sortedData[i].topScore > sortedData[i + 1].floorScore) {
          message.error('等级分数范围存在重叠，请检查');
          return;
        }
        if (sortedData[i].topScore < sortedData[i].floorScore) {
          message.error(
            `等级 ${sortedData[i].grade} 的上限分数小于下限分数，请检查`
          );
          return;
        }
      }

      // 构建API请求数据
      let submitData: any = {
        name: formValues.name,
        status: formValues.status,
        remark: formValues.remark || '',
        sets: dataSource.map((item) => ({
          grade: item.grade,
          topScore: item.topScore,
          floorScore: item.floorScore,
          // 如果是编辑模式且等级有ID，则保留ID
          ...(item.id && !item.id.toString().startsWith('temp-')
            ? { id: item.id }
            : {}),
          // 如果是编辑模式且有groupId，则保留groupId
          ...(currentRecord?.id ? { groupId: currentRecord.id } : {}),
        })),
      };

      // 如果是编辑模式，添加ID和其他必要字段
      if (editType === 'edit' && currentRecord) {
        // 保留原有数据中的这些字段
        const {
          id,
          createId,
          createName,
          createTime,
          updateId,
          updateName,
          updateTime,
          delFlag,
        } = currentRecord;

        submitData = {
          ...submitData,
          id,
          // 保留创建信息
          createId: createId || null,
          createName: createName || '',
          createTime: createTime || '',
          // 更新信息可能会由后端自动生成
          updateId: updateId || null,
          updateName: updateName || '',
          updateTime: updateTime || '',
          delFlag: delFlag || '',
        };
      }

      // 调用API
      const apiUrl = editType === 'add' ? API.ADD_GROUP : API.UPDATE_GROUP;
      console.log('提交数据:', submitData);

      const response = await request.post(apiUrl, { data: submitData });

      if (response.code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        if (onClose) onClose();
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交失败，请检查表单');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative w-full h-full flex flex-col">
      <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
        {/* 基本信息区域 */}
        <Card
          title="基本信息"
          className="mb-4"
          styles={{
            header: {
              backgroundColor: 'white',
              borderBottom: '1px solid #f0f0f0',
            },
            body: { padding: '24px' },
          }}
        >
          <Form form={form} layout="vertical" disabled={editType === 'view'}>
            <Form.Item
              name="name"
              label="名称"
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>

            <div className="text-blue-500 mb-4">
              注：名称不允许重复，如果有正在进行中的评级申请使用了此等级划分，则不能删除。
            </div>

            <Form.Item name="remark" label="备注">
              <Input.TextArea placeholder="请输入" rows={4} />
            </Form.Item>
          </Form>
        </Card>

        {/* 等级信息区域 */}
        <Card
          title="等级信息"
          styles={{
            header: {
              backgroundColor: 'white',
              borderBottom: '1px solid #f0f0f0',
            },
            body: { padding: '24px' },
          }}
        >
          <div className="text-red-500 mb-4">
            注：实验室等级根据分数自动计算，分数采用百分制
          </div>
          <EditableProTable
            key={editableProTableKey.current}
            columns={columns}
            rowKey="id"
            scroll={{
              x: 'max-content',
            }}
            value={dataSource}
            onChange={setDataSource}
            recordCreatorProps={
              editType === 'view'
                ? false
                : {
                    record: (index, dataSource) => {
                      // 返回新记录对象，符合GradeSet类型
                      return {
                        id: `temp-${Date.now()}`,
                        grade: '',
                        floorScore: 0,
                        topScore: 0,
                      };
                    },
                    creatorButtonText: '增加一个等级',
                  }
            }
            editableFormRef={editableFormRef}
            editable={
              editType === 'view'
                ? false
                : {
                    type: 'multiple',
                    editableKeys,
                    actionRender: (row: any, config: any, defaultDoms: any) => {
                      return [
                        <Button
                          key="save"
                          type="link"
                          onClick={() => {
                            handleSaveRow(row);
                          }}
                        >
                          保存
                        </Button>,
                        defaultDoms.cancel,
                      ];
                    },
                    onValuesChange: (record: any, recordList: any[]) => {
                      // 可以在这里处理数据变化
                    },
                    onChange: setEditableRowKeys,
                    onCancel: (key: React.Key) => {
                      setEditableRowKeys([]);
                    },
                  }
            }
          />
        </Card>
      </div>

      {/* 底部固定按钮 */}
      {editType !== 'view' && (
        <div className="h-12 bg-white flex justify-center items-center sticky bottom-0 z-10 w-full m-0 p-0">
          <Button
            type="default"
            onClick={() => {
              // 在取消时清空数据
              setDataSource([]);
              setOriginalDataSource([]);
              setEditableRowKeys([]);
              form.resetFields();
              if (onClose) onClose();
            }}
            disabled={loading}
            className="mx-2"
          >
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            保存
          </Button>
        </div>
      )}
    </div>
  );
};

export default Edit;
