/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm } from 'antd';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/edit';
import PageContainer from '@/components/PageContainer';
import request from '@/utils/request';

// API接口定义
const API = {
  GET_GRADING_RULE_LIST: '/gradeConfig/grades',
  DELETE_GRADING_RULE: '/gradeConfig/delGroup', // 更新为正确的删除接口
};

// 获取等级划分列表
const fetchGradingRuleList = async (params: any) => {
  try {
    const { name, status, current, pageSize } = params;
    const queryParams = {
      name: name || '',
      status: status || '',
      // 可以根据需要添加分页参数
      // current,
      // pageSize,
    };

    const response = await request.get(API.GET_GRADING_RULE_LIST, {
      params: queryParams,
    });

    return response;
  } catch (error) {
    console.error('获取等级划分列表失败:', error);
    throw error;
  }
};

// 删除等级划分
const deleteGradingRule = async (id: string | number) => {
  try {
    // 使用GET方法，通过查询参数传递id
    return await request.get(API.DELETE_GRADING_RULE, {
      params: { id },
    });
  } catch (error) {
    console.error('删除等级划分失败:', error);
    throw error;
  }
};

// 定义搜索参数类型
interface SearchParams {
  name?: string;
  status?: string;
}

const GradingRule: React.FC = () => {
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  // 列定义
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: false,
      width: 200,
      render: (_, record) => (
        <Button
          key={record?.id}
          type="link"
          onClick={() => {
            setEditType('view');
            setCurrentRecord(record);
            setDrawerVisible(true);
          }}
          style={{
            whiteSpace: 'normal',
            height: 'auto',
            padding: '4px 8px',
            textAlign: 'left',
            wordBreak: 'break-all',
          }}
        >
          {record.name}
        </Button>
      ),
    },
    {
      title: '使用状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        '1': { text: '使用中', status: '1' },
        '0': { text: '未使用', status: '0' },
      },
      // 状态(1使用中，0未使用)
      render: (_, record) => (record?.status === '0' ? '未使用' : '使用中'),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '添加人',
      dataIndex: 'createName',
      hideInSearch: true,
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '最后更新人',
      dataIndex: 'updateName',
      hideInSearch: true,
    },
    {
      title: '最后更新时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="update"
          type="link"
          size="small"
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该项?"
          onConfirm={() => handleDelete(record?.id)}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 刷新表格
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 处理搜索
  const handleSearch = (values: SearchParams) => {
    setSearchParams(values);
    actionRef.current?.reload();
  };

  // 处理重置
  const handleReset = () => {
    setSearchParams({});
    actionRef.current?.reload();
  };

  // 处理新增
  const handleAdd = () => {
    setEditType('add');
    setCurrentRecord(null);
    setDrawerVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: any) => {
    setEditType('edit');
    setCurrentRecord(record);
    setDrawerVisible(true);
  };

  // 处理删除
  const handleDelete = async (id: string | number) => {
    try {
      setLoading(true);
      const { code, msg } = await deleteGradingRule(id);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 抽屉关闭后的回调
  const afterDrawerClose = () => {
    tableReload();
  };

  return (
    <PageContainer>
      {/* 表格 */}
      <ProTable
        actionRef={actionRef}
        cardBordered
        bordered
        columns={columns}
        rowKey="id"
        loading={loading}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        request={async (params) => {
          setLoading(true);
          try {
            // 合并表单参数和已保存的搜索参数
            const { code, data, msg } = await fetchGradingRuleList({
              ...params,
              ...searchParams,
            });

            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
              return {
                data: [],
                success: false,
              };
            }

            return {
              data,
              success: true,
            };
          } catch (error) {
            console.error('获取数据失败:', error);
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        toolBarRender={() => [
          <Button type="primary" onClick={() => handleAdd()}>
            新增
          </Button>,
        ]}
        // 配置搜索表单
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={{
          // search: true,
          reload: () => {
            actionRef.current?.reload();
          },
        }}
        // form={{
        //   // 由于值改变就会触发搜索，这里需要设置防抖
        //   onValuesChange: (_: any, values: Record<string, any>) => {
        //     setSearchParams(values as SearchParams);
        //   },
        //   // 点击搜索按钮时执行
        //   onFinish: (values: Record<string, any>) => {
        //     setSearchParams(values as SearchParams);
        //     actionRef.current?.reload();
        //   },
        //   // 重置时清空所有字段
        //   onReset: () => {
        //     setSearchParams({});
        //     actionRef.current?.reload();
        //   },
        // }}
      />

      {/* 抽屉 */}
      <Drawer
        title={`${
          editType === 'add' ? '新增' : editType === 'edit' ? '编辑' : '查看'
        }等级划分`}
        placement="right"
        width="60%"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        afterOpenChange={(visible) => {
          if (!visible) {
            afterDrawerClose();
          }
        }}
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          editType={editType}
          currentRecord={currentRecord}
          onClose={() => setDrawerVisible(false)}
        />
      </Drawer>
    </PageContainer>
  );
};

export default GradingRule;
