/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Space,
  Table,
} from 'antd';
import type { FormInstance } from 'antd/es/form';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { EditableFormInstance, ProColumns } from '@ant-design/pro-components';
import EditableProTable from '@/components/EditableProTable';
import request from '@/utils/request';

// API接口定义
const API = {
  GET_EXPERT_LIST: '/gradeConfig/expert/lib',
  ADD_EXPERT_GROUP: '/gradeConfig/expertGroup/add',
  UPDATE_EXPERT_GROUP: '/gradeConfig/expertGroup/edit',
  GET_EXPERT_GROUP_DETAIL: '/gradeConfig/expertGroup',
  DELETE_EXPERT_GROUP: '/gradeConfig/expertGroup/delete',
};

// 定义Props类型
interface EditProps {
  editType?: 'add' | 'edit' | 'view';
  currentRecord?: any;
  onClose?: () => void;
}

// 定义专家数据类型
interface ExpertData {
  id: string;
  userId?: string | number;
  name: string;
  contactNumber: string;
  phonenumber?: string;
  organization: string;
  deptId?: string | number;
  deptName?: string;
  position?: string; // 职称
  professor?: string;
  expertise?: string; // 专业领域
  professionalField?: string;
  isLeader?: boolean;
  leaderFlag?: string;
  editable?: boolean;
  remark?: string;
}

// 模拟专家数据 - 仅用于开发阶段，实际环境中使用API调用
const mockExperts = Array(20)
  .fill(null)
  .map((_, index) => ({
    id: `${index + 1}`,
    name: index % 2 === 0 ? `张三${index + 1}` : `李四${index + 1}`,
    organization: `展示机构/单位名称${index + 1}`,
    contactNumber: '18215882922',
    position: index % 3 === 0 ? '高级' : index % 3 === 1 ? '中级' : '初级', // 添加职称
    expertise: index % 2 === 0 ? '理化领域' : '微生物领域', // 添加专业领域
  }));

const Edit: React.FC<EditProps> = ({ editType, currentRecord, onClose }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<FormInstance>();
  const [form] = Form.useForm();

  // 专家表格数据
  const [dataSource, setDataSource] = useState<ExpertData[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 人员选择Modal相关状态
  const [expertSelectVisible, setExpertSelectVisible] =
    useState<boolean>(false);
  const [nameKeyword, setNameKeyword] = useState<string>('');
  const [organizationKeyword, setOrganizationKeyword] = useState<string>('');
  const [selectedExperts, setSelectedExperts] = useState<any[]>([]);
  const [expertList, setExpertList] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [expertTableLoading, setExpertTableLoading] = useState(false);
  // 添加selectedExpertKeys状态，用于控制专家表格选中状态
  const [selectedExpertKeys, setSelectedExpertKeys] = useState<React.Key[]>([]);

  // 获取专家列表
  const fetchExpertList = async (
    page = 1,
    pageSize = 10,
    name = '',
    organization = ''
  ) => {
    setExpertTableLoading(true);
    try {
      // 调用真实API获取专家库列表
      const params: Record<string, any> = {
        pageNum: page,
        pageSize: pageSize,
        status: 1,
      };

      // 添加搜索条件
      if (name) {
        params.nickName = name;
      }

      if (organization) {
        params.deptName = organization;
      }

      const response = await request.get(API.GET_EXPERT_LIST, { params });
      const { code, data, msg } = response as any;

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取专家列表失败');
        return;
      }

      // 转换返回数据为组件所需格式
      const experts = data.rows?.map((item: any) => ({
        id: item.userId,
        userId: item.userId,
        name: item.nickName,
        contactNumber: item.phonenumber,
        phonenumber: item.phonenumber,
        organization: item.deptName,
        deptId: item.deptId,
        deptName: item.deptName,
        position: item.professor,
        professor: item.professor,
        expertise: item.professionalFieldCh || item.professionalField,
        professionalField: item.professionalField,
        remark: item.remark,
      }));

      setExpertList(experts || []);
      setPagination({
        current: page,
        pageSize,
        total: data.total || 0,
      });
    } catch (error) {
      console.error('获取专家列表失败:', error);
      message.error('获取专家列表失败');
    } finally {
      setExpertTableLoading(false);
    }
  };

  // 获取专家组详情
  const fetchGroupDetail = async (groupId: string) => {
    try {
      setLoading(true);
      // 调用获取专家组详情API
      const response = await request.get(
        `${API.GET_EXPERT_GROUP_DETAIL}/${groupId}`
      );
      const { code, data, msg } = response as any;

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取专家组详情失败');
        return;
      }

      // 设置表单数据
      form.setFieldsValue({
        name: data.name,
        remark: data.remark,
      });

      // 转换成员数据为组件所需格式
      if (data.members && Array.isArray(data.members)) {
        const groupMembers = data.members.map((item: any) => ({
          id: item.userId,
          userId: item.userId,
          name: item.name,
          contactNumber: item.phonenumber,
          phonenumber: item.phonenumber,
          organization: item.deptName,
          deptId: item.deptId,
          deptName: item.deptName,
          position: item.professor,
          professor: item.professor,
          expertise: item.professionalFieldCh || item.professionalField,
          professionalField: item.professionalField,
          isLeader: item.leaderFlag === '1',
          remark: item.remark,
        }));

        setDataSource(groupMembers);
      }
    } catch (error) {
      console.error('获取专家组详情失败:', error);
      message.error('获取专家组详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化表单数据
  useEffect(() => {
    if (editType === 'edit' || editType === 'view') {
      if (currentRecord) {
        // 设置基本信息
        form.setFieldsValue({
          name: currentRecord.name,
          remark: currentRecord.remark,
        });

        // 在编辑或查看模式下，获取小组成员详情
        fetchGroupDetail(currentRecord.id);
      }
    } else {
      // 新增模式下清空数据
      form.resetFields();
      setDataSource([]);
    }
  }, [editType, currentRecord, form]);

  // 初始加载专家列表
  useEffect(() => {
    if (expertSelectVisible) {
      fetchExpertList(
        pagination.current,
        pagination.pageSize,
        nameKeyword,
        organizationKeyword
      );
    }
  }, [expertSelectVisible]);

  // 处理重置
  const handleReset = () => {
    setNameKeyword('');
    setOrganizationKeyword('');
    fetchExpertList(1, pagination.pageSize, '', '');
  };

  // 处理搜索
  const handleSearch = () => {
    fetchExpertList(1, pagination.pageSize, nameKeyword, organizationKeyword);
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
    fetchExpertList(
      pagination.current,
      pagination.pageSize,
      nameKeyword,
      organizationKeyword
    );
  };

  // 处理选择专家
  const handleSelectExperts = (
    selectedRowKeys: React.Key[],
    selectedRows: any[]
  ) => {
    setSelectedExpertKeys(selectedRowKeys);
    setSelectedExperts(selectedRows);
  };

  // 处理确认选择
  const handleConfirmSelect = () => {
    if (selectedExperts.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }

    // 选中的专家信息转换为表格数据
    const newData = selectedExperts.map((expert) => ({
      id: expert.id || `temp-${Date.now()}`,
      userId: expert.userId || expert.id, // 添加userId字段
      name: expert.name || '',
      contactNumber: expert.contactNumber || '',
      phonenumber: expert.phonenumber || expert.contactNumber || '',
      organization: expert.organization || '',
      deptId: expert.deptId || '',
      deptName: expert.deptName || expert.organization || '',
      position: expert.position || '',
      professor: expert.professor || expert.position || '',
      expertise: expert.expertise || '',
      professionalField: expert.professionalField || '',
      isLeader: false, // 默认不是组长
      remark: expert.remark || '',
    }));

    // 更新状态
    setDataSource((prevData) => {
      // 过滤掉已经存在的专家，避免重复添加
      const existingIds = prevData.map((item) => item.id);
      const uniqueNewData = newData.filter(
        (item) => !existingIds.includes(item.id)
      );
      return [...prevData, ...uniqueNewData];
    });

    // 关闭选择Modal
    setExpertSelectVisible(false);

    // 清空选择状态，避免下次打开Modal时保留上次选择
    setSelectedExperts([]);
  };

  // 处理删除专家
  const handleDeleteExperts = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的专家');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除已选择的${selectedRowKeys.length}条专家数据吗？`,
      onOk: () => {
        // 从dataSource中过滤掉已选中的行
        const newDataSource = dataSource.filter(
          (item) => !selectedRowKeys.includes(item.id)
        );
        setDataSource(newDataSource);
        setSelectedRowKeys([]);
      },
    });
  };

  // 保存专家信息
  const handleSave = async () => {
    try {
      setLoading(true);
      // 获取表单数据
      const values = await form.validateFields();

      // 检查是否有组员
      if (dataSource.length === 0) {
        message.error('请至少添加一名成员');
        setLoading(false);
        return;
      }

      // 检查是否有组长
      const hasLeader = dataSource.some((item) => item.isLeader);
      if (!hasLeader) {
        message.error('请至少指定一名组长');
        setLoading(false);
        return;
      }

      // 如果有多个组长，提示错误
      const leaders = dataSource.filter((item) => item.isLeader);
      if (leaders.length > 1) {
        message.error('每个小组只能指定一名组长');
        setLoading(false);
        return;
      }

      // 获取组长信息
      const leader = leaders[0];

      // 构建提交数据 - 按照API要求格式化
      const submitData: Record<string, any> = {
        name: values.name,
        leader: leader.userId, // 组长ID
        leaderName: leader.name, // 组长名称
        remark: values.remark || '',
        members: dataSource.map((expert) => ({
          userId: expert.userId,
          name: expert.name,
          leaderFlag: expert.isLeader ? '1' : '0',
          phonenumber: expert.phonenumber || expert.contactNumber,
          deptId: expert.deptId,
          deptName: expert.deptName || expert.organization,
          professor: expert.professor || expert.position,
          professionalField: expert.professionalField || '',
          remark: expert.remark || '',
        })),
      };

      // 根据编辑模式选择不同的API
      let response;
      if (editType === 'edit' && currentRecord) {
        // 编辑模式，添加ID并调用更新API
        submitData.id = currentRecord.id;
        response = await request.post(API.UPDATE_EXPERT_GROUP, {
          data: submitData,
        });
      } else {
        // 新增模式
        response = await request.post(API.ADD_EXPERT_GROUP, {
          data: submitData,
        });
      }

      const { code, msg } = response as any;
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        if (onClose) onClose();
      } else {
        message.error(msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请检查表单');
    } finally {
      setLoading(false);
    }
  };

  // 设置组长
  const handleSetLeader = (record: ExpertData) => {
    // 先将所有成员的isLeader设为false
    const updatedDataSource = dataSource.map((item) => ({
      ...item,
      isLeader: item.id === record.id, // 只有当前选中的成员设为组长
    }));
    setDataSource(updatedDataSource);
  };

  // 专家选择表格的列
  const expertColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '机构名称',
      dataIndex: 'organization',
      key: 'organization',
    },
  ];

  // 定义表格列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '专家姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '联系方式',
      dataIndex: 'contactNumber',
      key: 'contactNumber',
      width: 120,
    },
    {
      title: '所属单位',
      dataIndex: 'organization',
      key: 'organization',
      width: 150,
    },
    {
      title: '组长',
      dataIndex: 'isLeader',
      key: 'isLeader',
      width: 80,
      render: (_: any, record: ExpertData) => (
        <Radio
          checked={record.isLeader}
          disabled={editType === 'view'}
          onChange={() => handleSetLeader(record)}
        />
      ),
    },
    {
      title: '职称',
      dataIndex: 'position',
      key: 'position',
      width: 100,
    },
    {
      title: '专业领域',
      dataIndex: 'expertise',
      key: 'expertise',
      width: 100,
    },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* 基本信息区域 */}
      <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
        <Card
          title="基本信息"
          className="mb-4"
          styles={{
            header: {
              backgroundColor: 'white',
              borderBottom: '1px solid #f0f0f0',
            },
            body: { padding: '24px' },
          }}
        >
          <Form form={form} layout="vertical" disabled={editType === 'view'}>
            <Form.Item
              name="name"
              label="小组名称"
              rules={[{ required: true, message: '请输入小组名称' }]}
            >
              <Input placeholder="请输入小组名称" />
            </Form.Item>

            <Form.Item name="remark" label="备注">
              <Input.TextArea placeholder="请输入备注信息" rows={4} />
            </Form.Item>
          </Form>
        </Card>

        {/* 组内成员区域 */}
        <Card
          title="组内成员"
          extra={
            editType !== 'view' ? (
              <Space>
                <Button
                  onClick={() => {
                    setExpertSelectVisible(true);
                    // 清空选择状态
                    setSelectedExperts([]);
                    setSelectedExpertKeys([]);
                  }}
                >
                  选择
                </Button>
                <Button
                  danger
                  onClick={handleDeleteExperts}
                  disabled={selectedRowKeys.length === 0}
                >
                  删除
                </Button>
              </Space>
            ) : null
          }
          styles={{
            header: {
              backgroundColor: 'white',
              borderBottom: '1px solid #f0f0f0',
            },
            body: { padding: '24px' },
          }}
        >
          <Table
            rowKey="id"
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            size="middle"
            rowSelection={
              editType !== 'view'
                ? {
                    type: 'checkbox',
                    selectedRowKeys,
                    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
                  }
                : undefined
            }
          />
        </Card>
      </div>

      {/* 底部按钮 */}
      {editType !== 'view' && (
        <div className="h-12 bg-white flex justify-center items-center sticky bottom-0 z-10 w-full m-0 p-0">
          <Button
            type="default"
            onClick={() => {
              // 在取消时清空数据
              setDataSource([]);
              setSelectedRowKeys([]);
              form.resetFields();
              if (onClose) onClose();
            }}
            disabled={loading}
            className="mx-2"
          >
            取消
          </Button>
          <Button type="primary" onClick={handleSave} loading={loading}>
            保存
          </Button>
        </div>
      )}

      {/* 专家选择Modal */}
      <Modal
        title="选择专家"
        open={expertSelectVisible}
        onCancel={() => {
          setExpertSelectVisible(false);
          // 关闭时也清空选择状态
          setSelectedExperts([]);
          setSelectedExpertKeys([]);
        }}
        width={800}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setExpertSelectVisible(false);
              // 关闭时也清空选择状态
              setSelectedExperts([]);
              setSelectedExpertKeys([]);
            }}
          >
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirmSelect}>
            确定
          </Button>,
        ]}
      >
        <div className="my-4">
          <Space className="w-full flex flex-row justify-between">
            <Space className="flex-1 flex flex-row gap-4">
              <div className="flex-1">
                <span>姓名：</span>
                <Input
                  placeholder="请输入姓名"
                  value={nameKeyword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setNameKeyword(e.target.value)
                  }
                  onPressEnter={handleSearch}
                  style={{ width: 150 }}
                />
              </div>
              <div className="flex-1">
                <span>机构名称：</span>
                <Input
                  placeholder="请输入机构名称"
                  value={organizationKeyword}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setOrganizationKeyword(e.target.value)
                  }
                  onPressEnter={handleSearch}
                  style={{ width: 150 }}
                />
              </div>
            </Space>
            <Space className="w-[135px]">
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
            </Space>
          </Space>
        </div>

        <Table
          rowKey="id"
          columns={expertColumns}
          dataSource={expertList}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          loading={expertTableLoading}
          onChange={handleTableChange}
          rowSelection={{
            type: 'checkbox',
            onChange: handleSelectExperts,
            selectedRowKeys: selectedExpertKeys, // 使用专用的state控制选中状态
          }}
          size="small"
        />
      </Modal>
    </div>
  );
};

export default Edit;
