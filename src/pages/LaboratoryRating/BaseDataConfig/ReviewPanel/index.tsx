/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import {
  Button,
  Col,
  Drawer,
  Dropdown,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
} from 'antd';
import { Menu } from 'antd';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  DeleteOutlined,
  DownOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import download from '@/utils/download';
import request from '@/utils/request';
import './index.less';

// API接口定义
const API = {
  GET_PANEL_LIST: '/gradeConfig/expertGroup/page',
  DELETE_PANEL: '/gradeConfig/expertGroup/delete',
  GET_PANEL_DETAIL: '/gradeConfig/expertGroup',
  EXPORT_DATA: '/gradeConfig/export',
};

// API调用函数
const getPanelListApi = async (params: any) => {
  return request.get(API.GET_PANEL_LIST, { params });
};

// 导出数据
const exportPanelDataApi = async () => {
  return request.post(API.EXPORT_DATA, {
    params: { type: 2 }, // type=2表示评审小组导出
  });
};

const ReviewPanel: React.FC = () => {
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  // 选中的行的key
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 搜索条件
  const [panelName, setPanelName] = useState<string>('');
  const [leaderName, setLeaderName] = useState<string>('');
  const [memberName, setMemberName] = useState<string>('');

  // 列定义
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '小组名称',
      dataIndex: 'name',
      ellipsis: true,
      render: (_, record) => (
        <Button
          key={record?.id}
          type="link"
          onClick={() => {
            handleView(record);
          }}
        >
          {record.name}
        </Button>
      ),
    },
    {
      title: '组长',
      dataIndex: 'leader',
      ellipsis: true,
    },
    {
      title: '成员',
      dataIndex: 'flower',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '添加人',
      dataIndex: 'createdBy',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '添加时间',
      dataIndex: 'createdTime',
      width: 160,
      hideInSearch: true,
    },
    {
      title: '最后更新人',
      dataIndex: 'updatedBy',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '最后更新时间',
      dataIndex: 'updatedTime',
      width: 160,
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="update"
          type="link"
          size="small"
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该评审小组?"
          onConfirm={() => handleDelete(record?.id)}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 刷新表格
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 处理搜索
  const handleSearch = () => {
    tableReload();
  };

  // 处理重置
  const handleReset = () => {
    setPanelName('');
    setLeaderName('');
    setMemberName('');
    tableReload();
  };

  // 处理新增
  const handleAdd = () => {
    setEditType('add');
    setCurrentRecord(null);
    setDrawerVisible(true);
  };

  // 处理查看
  const handleView = async (record: any) => {
    try {
      setLoading(true);

      // 调用获取评审小组详情API
      const { code, data, msg } = await request.get(
        `${API.GET_PANEL_DETAIL}/${record.id}`
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取评审小组详情失败');
        return;
      }

      // 设置查看类型和当前记录
      setEditType('view');
      setCurrentRecord(data); // 使用API返回的详情数据
      setDrawerVisible(true);
    } catch (error) {
      console.error('获取评审小组详情失败:', error);
      message.error('获取评审小组详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理编辑
  const handleEdit = async (record: any) => {
    try {
      setLoading(true);

      // 调用获取评审小组详情API
      const { code, data, msg } = await request.get(
        `${API.GET_PANEL_DETAIL}/${record.id}`
      );

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg || '获取评审小组详情失败');
        return;
      }

      // 设置编辑类型和当前记录
      setEditType('edit');
      setCurrentRecord(data); // 使用API返回的详情数据
      setDrawerVisible(true);
    } catch (error) {
      console.error('获取评审小组详情失败:', error);
      message.error('获取评审小组详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除
  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      // 调用删除API
      const { code, msg } = await request.get(`${API.DELETE_PANEL}/${id}`);

      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);

          // 处理多条记录的删除
          // 这里需要根据接口调整，如果批量删除仍然需要一个个调用，就需要使用Promise.all
          const deletePromises = selectedRowKeys.map((id) =>
            request.get(`${API.DELETE_PANEL}/${id}`)
          );

          const results = await Promise.all(deletePromises);

          // 检查所有删除操作是否成功
          const allSuccess = results.every(
            (res: any) => res.code === codeDefinition.QUERY_SUCCESS
          );

          if (allSuccess) {
            message.success(QUERY_SUCCESS_MSG);
            setSelectedRowKeys([]);
            tableReload();
          } else {
            message.error('部分记录删除失败');
          }

          return Promise.resolve();
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
          return Promise.reject(error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 导出数据
  const handleExport = async () => {
    try {
      setLoading(true);
      // 调用导出API
      const { data, fileName } = await exportPanelDataApi();

      if (!data || !data.getReader) {
        throw new Error('无法获取数据流');
      }

      // 创建可读流
      const reader = data.getReader();
      // 手动控制读取流以便回传下载进度
      const stream = new ReadableStream({
        start(controller) {
          // 声明一个 pumpRead 方法读取片段
          const pumpRead = (): any =>
            reader.read().then(({ done, value }: any) => {
              if (done) {
                // 结束读取 关闭读取流
                controller.close();
                return;
              }
              // 每次推入读取队列 并链式执行
              controller.enqueue(value);
              return pumpRead();
            });
          // 开始读取
          return pumpRead();
        },
      });

      // 最后我们通过 Response 函数接收这个文件流 并转为 blob
      const blob = await new Response(stream).blob();
      const objUrl = window.URL.createObjectURL(blob);
      const aLink = document.createElement('a');
      aLink.style.display = 'none';
      aLink.href = objUrl;
      aLink.setAttribute(
        'download',
        decodeURI(
          fileName || `评审小组_${new Date().toLocaleDateString()}.xlsx`
        )
      );
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(objUrl);

      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 更多操作菜单项定义
  const moreMenuItems = [
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: '数据导出',
    },
    {
      key: 'batchDelete',
      icon: <DeleteOutlined />,
      label: '批量删除',
      disabled: selectedRowKeys.length === 0,
    },
  ];

  // 更多操作菜单
  const moreActionsMenu = (
    <Menu>
      <Menu.Item key="export" icon={<ExportOutlined />} onClick={handleExport}>
        数据导出
      </Menu.Item>
      <Menu.Item
        key="batchDelete"
        icon={<DeleteOutlined />}
        onClick={handleBatchDelete}
        danger
      >
        批量删除
      </Menu.Item>
    </Menu>
  );

  // 添加行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys);
    },
  };

  // 抽屉关闭后的回调
  const afterDrawerClose = () => {
    tableReload();
  };

  // 处理表格请求
  const handleTableRequest = async (params: any) => {
    setLoading(true);
    try {
      // 转换参数
      const requestParams: Record<string, any> = {
        pageNum: params.current,
        pageSize: params.pageSize,
      };

      // 添加搜索条件
      if (params?.name) {
        requestParams.name = params.name;
      }

      if (params?.leader) {
        requestParams.leader = params.leader;
      }

      if (params?.flower) {
        requestParams.flower = params.flower;
      }

      const response = await getPanelListApi(requestParams);
      const { code, data, msg } = response as any;

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      const recordsData = data.rows || data || [];

      // 转换响应数据为组件所需格式
      const records = recordsData.map((item: any) => ({
        id: item.id,
        name: item.name,
        leader: item.leaderName || item.leader,
        flower: item.flower,
        remark: item.remark,
        createdBy: item.createName,
        createdTime: item.createTime,
        updatedBy: item.updateName,
        updatedTime: item.updateTime,
      }));

      return {
        data: records || [],
        success: true,
        total: data.total || 0,
      };
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer>
      <div className="review-panel-container">
        {/* 表格 */}
        <ProTable
          actionRef={actionRef}
          cardBordered
          bordered
          columns={columns}
          rowKey="id"
          loading={loading}
          request={handleTableRequest}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
          }}
          rowSelection={rowSelection}
          tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (
            <Space size={24}>
              <span>已选 {selectedRowKeys.length} 项</span>
              <a onClick={onCleanSelected}>取消选择</a>
            </Space>
          )}
          toolbar={{
            actions: [
              <Button key="add" type="primary" onClick={handleAdd}>
                新增
              </Button>,
              <Dropdown
                className="more-operate__btn"
                arrow
                menu={{
                  items: moreMenuItems,
                  onClick: ({ key }: { key: string }) => {
                    if (key === 'export') {
                      handleExport();
                    } else if (key === 'batchDelete') {
                      handleBatchDelete();
                    }
                  },
                }}
              >
                <Button>更多操作</Button>
              </Dropdown>,
            ],
          }}
        />

        {/* 抽屉 */}
        <Drawer
          title={
            editType === 'add'
              ? '新增评审小组'
              : editType === 'edit'
              ? '编辑评审小组'
              : '查看评审小组'
          }
          width="60%"
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          afterOpenChange={(visible) => {
            if (!visible) {
              afterDrawerClose();
            }
          }}
        >
          <Edit
            editType={editType}
            currentRecord={currentRecord}
            onClose={() => {
              setDrawerVisible(false);
              tableReload();
            }}
          />
        </Drawer>
      </div>
    </PageContainer>
  );
};

export default ReviewPanel;
