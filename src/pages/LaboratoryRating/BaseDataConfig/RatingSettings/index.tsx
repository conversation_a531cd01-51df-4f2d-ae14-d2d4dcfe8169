/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import {
  Affix,
  Button,
  Card,
  Form,
  Input,
  message,
  Select,
  Space,
  Typography,
  Upload,
} from 'antd';
import type { UploadProps } from 'antd';
import { downloadFile, getFileData } from '@/api/file';
import { codeDefinition } from '@/constants';
import {
  DownloadOutlined,
  EyeOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import request from '@/utils/request';
import { getFileTypeByName } from '@/utils/upload';
import { useInfoStore, useTokenStore, useYearStore } from '@/store';
import EProFormUploadButton from '@/components/EProFormUploadButton';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 定义API接口
const API = {
  GET_RATING_SETTINGS: '/gradeConfig/grade/config', // 获取设置
  SAVE_RATING_SETTINGS: '/gradeConfig/grade/defaultConfig', // 保存设置，修正为正确的API
  GET_RATING_TABLES: '/gradeTable/listTable', // 获取评审表列表
  GET_GRADING_RULES: '/gradeConfig/grades', // 获取等级划分规则列表
  UPLOAD_FILE: '/api/system/oss/upload', // 文件上传接口
  DOWNLOAD_FILE: '/system/oss/download', // 文件下载接口
};

// 接口返回的设置数据类型
interface IRatingSettings {
  id?: number; // 设置ID
  tableId?: string; // 评审表ID
  groupId?: string; // 等级划分规则ID
  defaul?: string; // 默认值
  tip?: string; // 申请提示文字
  files?: Array<{
    ossId: string;
    fileName?: string;
  }>; // 文件信息
}

// 评审表和等级划分规则项类型
interface SelectOption {
  id: string;
  name: string;
}

const RatingSettings: React.FC = () => {
  const { token } = useTokenStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false); // 编辑模式状态
  const [reviewTables, setReviewTables] = useState<SelectOption[]>([]);
  const [gradingRules, setGradingRules] = useState<SelectOption[]>([]);
  const [fileUploading, setFileUploading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>({});

  // 获取评级设置数据
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const { code, data } = await request.get(API.GET_RATING_SETTINGS);
      console.log('获取到的设置数据:', data);

      if (code === codeDefinition.QUERY_SUCCESS && data) {
        form.setFieldsValue({
          reviewTableId: data.tableId, // 更新字段名与API一致
          gradingRuleId: data.groupId, // 更新字段名与API一致
          applicationNotice: data.tip, // 更新字段名与API一致
        });

        // 如果有文件，设置文件列表
        if (data.files && data.files.length > 0 && data.files[0].ossId) {
          const fileInfo = data.files[0];
          setFileList([
            {
              uid: fileInfo.ossId,
              name: fileInfo.fileName || '指标体系文件', // 使用文件名或默认名称
              status: 'done',
              ossId: fileInfo.ossId,
            },
          ]);
        } else {
          setFileList([]);
        }
      }
    } catch (error) {
      console.error('获取评级设置失败:', error);
      message.error('获取评级设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取评审表列表
  const fetchReviewTables = async () => {
    try {
      const { code, data } = await request.get(API.GET_RATING_TABLES, {
        params: {
          pageSize: 9999,
          pageNum: 1,
        },
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setReviewTables(data);
      }
    } catch (error) {
      console.error('获取评审表列表失败:', error);
      message.error('获取评审表列表失败');
    }
  };

  // 获取等级划分规则列表
  const fetchGradingRules = async () => {
    try {
      const { code, data } = await request.get(API.GET_GRADING_RULES);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setGradingRules(data);
      }
    } catch (error) {
      console.error('获取等级划分规则列表失败:', error);
      message.error('获取等级划分规则列表失败');
    }
  };

  // 启用编辑模式
  const handleEdit = () => {
    setEditMode(true);
  };

  // 保存设置
  const saveSettings = async () => {
    try {
      setLoading(true);
      await form.validateFields();
      const values = form.getFieldsValue();

      // 构建符合API要求的参数格式
      const requestData = {
        id: 1, // 假设ID为1，如果有动态ID应从设置中获取
        tableId: values.reviewTableId, // 评审表ID
        groupId: values.gradingRuleId, // 等级划分规则ID
        defaul: '', // 默认值，按API要求设置
        tip: values.applicationNotice, // 申请提示文字
        files: [] as { ossId: string }[], // 文件信息
      };

      console.log(fileList);
      

      // 添加文件信息
      if (fileList.length > 0 && fileList[0].ossId) {
        requestData.files = [
          {
            ossId: fileList[0].ossId,
          },
        ];
      }

      console.log('保存设置请求数据:', requestData);

      const { code, msg } = await request.post(API.SAVE_RATING_SETTINGS, {
        data: requestData,
      });

      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('保存成功');
        setEditMode(false); // 保存成功后退出编辑模式
      } else {
        message.error(msg || '保存失败');
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消操作，重置表单并重新加载数据
  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    fetchSettings();
    setEditMode(false); // 取消后退出编辑模式
    message.info('已取消操作');
  };

  // 文件上传属性
  const uploadProps: UploadProps | any = {
    name: 'file',
    action: API.UPLOAD_FILE,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    accept: '.doc,.docx,.pdf',
    beforeUpload: (file: File) => {
      const isDocOrPdf =
        file.name.endsWith('.doc') ||
        file.name.endsWith('.docx') ||
        file.name.endsWith('.pdf');
      if (!isDocOrPdf) {
        message.error('只能上传 .doc, .docx, .pdf 格式的文件!');
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB!');
      }
      return isDocOrPdf && isLt10M;
    },
    onChange: (info: any) => {
      console.log(info.file.status, info.file, info);
    
      console.log(info.fileList);
      
      if (info.fileList && info.fileList.length > 0) {
        setFileUploading(false);
        if (
          info.file.response &&
          info.file.response.code === codeDefinition.QUERY_SUCCESS
        ) {
          setFileUploading(false);
          const fileData = info.file.response.data;
          const newFileList = [
            {
              uid: fileData.ossId,
              name: fileData.fileName,
              status: 'done',
              ossId: fileData.ossId,
            },
          ];
          setFileList(newFileList);
          message.success(`${info.file.name} 上传成功`);
        } else {
          message.error(
            info.file.response?.msg || `${info.file.name} 上传失败`
          );
        }
      } else if (info.file.status === 'error') {
        setFileUploading(false);
        message.error(`${info.file.name} 上传失败`);
      }
    },
    onRemove: () => {
      setFileList([]);
      return true;
    },
    fileList,
    showUploadList: {
      showDownloadIcon: true,
      showRemoveIcon: editMode, // 只在编辑模式下显示删除按钮
      showPreviewIcon: true,
      downloadIcon: <DownloadOutlined />,
      removeIcon: (
        <Button type="text" size="small" danger>
          删除
        </Button>
      ),
      previewIcon: <EyeOutlined />,
    },
    onDownload: (file: any) => {
      if (file.ossId) {
        downloadFile(file.ossId, file.name);
      }
    },
    onPreview: async (file: any) => {
      if (file.ossId) {
        try {
          const type = getFileTypeByName(file.name);
          if (type === 'Image') {
            const url = await getFileData(file.ossId);
            setIsShowFileData({
              name: file.name,
              url,
              ossId: file.ossId,
            });
            setIsShowFileView(true);
          } else {
            // 对于非图片文件，直接下载查看
            downloadFile(file.ossId, file.name);
          }
        } catch (error) {
          console.error('预览文件失败:', error);
          message.error('预览文件失败');
        }
      }
    },
    disabled: !editMode, // 非编辑模式下禁用上传
  };



  const onChange1 = async (file:any)=> {
    console.log(file, 88888);
    
    if (file.fileList && file.fileList.length>0) {
         let attachmentIds = file.fileList
            .filter(
              (item: any) =>
                item.response && item.response.data && item.response.data.ossId
            )
            .map((item: any) => {
              return {
                name: item.response.data.fileName,
                ossId: item.response.data.ossId,
                url: item.response.data.url,
              };
            });

        console.log(attachmentIds, 9999);
        if(attachmentIds[0]) {
          setIsShowFileData({...attachmentIds[0]})
          setFileList(attachmentIds)
        }

    }
  }

  useEffect(() => {
    fetchSettings();
    fetchReviewTables();
    fetchGradingRules();
    
  }, []);

  return (
    <div className="w-full flex flex-col h-full overflow-hidden">
      {/* 按钮区域 */}
      <div className="w-full h-[32px] flex items-center">
        <Space>
          {editMode ? (
            <>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" onClick={saveSettings} loading={loading}>
                保存
              </Button>
            </>
          ) : (
            <Button type="primary" onClick={handleEdit}>
              编辑
            </Button>
          )}
        </Space>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto mt-[10px]">
        <Form
          form={form}
          layout="vertical"
          disabled={!editMode} // 非编辑模式下表单为禁用状态
          initialValues={{
            applicationNotice:
              '原则上，评审周期为3年（2024、2025年获得等级评定的，周期可缩短为1年；2026年获得等级评定的，周期可缩短为2年）。在评审周期内各院校应定期组织对等级实验室进行抽查，若出现实验室能力明显滑坡等情况，应组织重新确定等级。',
          }}
        >
          <Card title="评级申请评审表设置" className="mb-4">
            <Form.Item
              name="reviewTableId"
              label="评审表名称"
              rules={[{ required: true, message: '请选择评审表' }]}
            >
              <Select placeholder="请选择评审表">
                {reviewTables.map((table: any) => (
                  <Option key={table.tableId} value={table.tableId}>
                    {table.tableName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Paragraph className="text-gray-500">
              评级申请发起时会以此表做该评级指标。
            </Paragraph>
          </Card>

          <Card title="评级等级划分设置" className="mb-4">
            <Form.Item
              name="gradingRuleId"
              label="等级划分"
              rules={[{ required: true, message: '请选择等级划分' }]}
            >
              <Select placeholder="请选择等级划分">
                {gradingRules.map((rule) => (
                  <Option key={rule.id} value={rule.id}>
                    {rule.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Paragraph className="text-gray-500">
              评级申请发起时会以此表做该评级指标。
            </Paragraph>
          </Card>

          <Card title="实验室等级评审指标体系文件" className="mb-4">
            {/* <Upload {...uploadProps}>
              {editMode && (
                <Button
                  icon={<UploadOutlined />}
                  loading={fileUploading}
                  disabled={!editMode}
                >
                  {fileList.length === 0 ? '上传文件' : '重新上传'}
                </Button>
              )}
            </Upload> */}

                   
          <EProFormUploadButton
            name="file"
            label=""
            labelCol={{ flex:0.032 }}
            max={10}
            readonly={!editMode}
            onChange={onChange1}
            defaultExtraTextHide={true}
          />

            <div className="mt-4">
              <Paragraph className="text-gray-500">
                只允许上传一份文件，支持扩展名：.doc .docx .pdf
              </Paragraph>
              <Paragraph className="text-gray-500">
                在评级申请，现场评审阶段可以直接预览此文件。
              </Paragraph>
            </div>
          </Card>

          <Card title="评级申请提示文字配置" className="mb-4">
            <Form.Item
              name="applicationNotice"
              rules={[{ required: true, message: '请输入提示文字' }]}
            >
              <TextArea rows={6} />
            </Form.Item>
          </Card>
        </Form>
      </div>
    </div>
  );
};

export default RatingSettings;
