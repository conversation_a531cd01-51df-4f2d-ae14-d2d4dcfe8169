/*
 * @Date: 2024-07-30 10:40:13
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-05 15:56:55
 * @FilePath: \xr-qc-jk-web\src\pages\LaboratoryRating\BaseDataConfig\LaboratoryList\index.tsx
 * @Description: 实验室名录
 */
import { useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { getDict } from '@/api/dict';
import {
  deleteLaboratoryApi,
  laboratoryListApi,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { isGradeOptions } from '../data';

const LaboratoryList: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [curSelectId, setCurSelectId] = useState('');

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const handleDelete = async (id: string) => {
    try {
      const { code, msg } = await deleteLaboratoryApi(id);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const handleDetailOpen = (id: string, type: 'add' | 'edit' | 'view') => {
    setCurSelectId(id);
    setEditType(type);
    setEditOpen(true);
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '实验室名称',
      dataIndex: 'labName',
    },
    {
      title: '实验室类型',
      dataIndex: 'labType',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('laboratory_type');
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '负责人',
      dataIndex: 'manager',
    },
    {
      title: '区域',
      key: 'area',
      hideInSearch: true,
      renderText(text, record, index, action) {
        return (
          (record?.cityName || '') +
          (record?.areaName ? '/' + record?.areaName : '')
        );
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      hideInSearch: true,
    },
    {
      title: '是否纳入评级管理',
      dataIndex: 'isGrade',
      valueType: 'select',
      fieldProps: {
        options: isGradeOptions,
      },
      formItemProps: {
        labelCol: { span: 12 },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          key="view"
          type="link"
          size="small"
          onClick={() => handleDetailOpen(record.id, 'view')}
        >
          查看
        </Button>,
        <Button
          key="update"
          type="link"
          size="small"
          onClick={() => handleDetailOpen(record.id, 'edit')}
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该草稿?"
          onConfirm={() => handleDelete(record.id)}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _param = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _param.current
          const { code, data, msg } = await laboratoryListApi(_param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button type="primary" onClick={() => handleDetailOpen('', 'add')}>
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          if (editType !== 'view') {
            tableReload();
          }
          setEditOpen(val);
        }}
        id={curSelectId}
        type={editType}
      />
    </PageContainer>
  );
};

export default LaboratoryList;
