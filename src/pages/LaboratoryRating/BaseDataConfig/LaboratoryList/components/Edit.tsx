import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Form, message } from 'antd';
import { getDict } from '@/api/dict';
import {
  addLaboratoryApi,
  laboratoryDetailApi,
  updateLaboratoryApi,
} from '@/api/LaboratoryRating/api';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { convertToCascading } from '@/utils';
import {
  ProForm,
  ProFormCascader,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { isGradeOptions } from '../../data';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
  type: 'add' | 'edit' | 'view';
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({ open, setOpen, id, type }) => {
  const [btnLoading, setBtnLoading] = useState(false);

  const formRef = useRef<ProFormInstance>(null);

  const [selectArea, setSelectArea] = useState<{
    cityId: string;
    cityName: string;
    areaId: string;
    areaName: string;
  }>();

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };
  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await laboratoryDetailApi(id);
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _formVal = { ...data };
        if (_formVal.cityId && _formVal.areaId) {
          _formVal.area = [_formVal.cityId + '', _formVal.areaId + ''];
          setSelectArea({
            cityId: _formVal.cityId + '',
            cityName: _formVal.cityName,
            areaId: _formVal.areaId + '',
            areaName: _formVal.areaName,
          });
        }
        _formVal.labType = _formVal.labType + ''
        formRef.current?.setFieldsValue(_formVal);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && type !== 'add' && getDetail(id);
  }, [open, id]);

  const fetchSave = () => {
    if (type === 'add') {
      return addLaboratoryApi;
    } else {
      return updateLaboratoryApi;
    }
  };

  const handleSubmit = async () => {
    try {
      setBtnLoading(true);
      let values = await formRef.current?.validateFields();
      values = {
        ...values,
        ...selectArea,
      };
      delete values.area;
      if (type === 'edit') {
        values.id = id;
      }
      // console.log(values);
      // return;
      const { code, msg } = await fetchSave()(values);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  const getTitle = () => {
    if (type === 'add') {
      return '新增';
    } else if (type === 'edit') {
      return '编辑';
    } else {
      return '详情';
    }
  };

  return (
    <Drawer
      width="40%"
      title={getTitle()}
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
            readonly={type === 'view'}
          >
            <ProFormText
              name="labName"
              label="实验室名称"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '实验室名称必填！' }]}
            />
            <ProFormSelect
              name="labType"
              label="实验室类型"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '实验室类型必填！' }]}
              request={async () => {
                const { code, data, msg } = await getDict('laboratory_type');
                if (code === codeDefinition.QUERY_SUCCESS) {
                  return data;
                } else {
                  message.error(msg);
                  return [];
                }
              }}
              fieldProps={{
                fieldNames: {
                  label: 'dictLabel',
                  value: 'dictValue',
                },
              }}
            />
            <ProFormText
              name="manager"
              label="负责人"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '负责人必填！' }]}
            />
            <ProFormCascader
              name="area"
              label="区域"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '区域必选！' }]}
              fieldProps={{
                onChange: (value: any, selectedOptions: any) => {
                  if (selectedOptions && selectedOptions.length) {
                    setSelectArea({
                      cityId: selectedOptions[0].value,
                      cityName: selectedOptions[0].label,
                      areaId: selectedOptions[1].value,
                      areaName: selectedOptions[1].label,
                    });
                  } else {
                    setSelectArea(undefined);
                  }
                },
              }}
              request={async () => {
                const { code, data, msg } = await getAllCityAreaList({});
                if (code !== codeDefinition.QUERY_SUCCESS) {
                  message.error(msg);
                  return [];
                }
                // 转换
                const _finalDataList = convertToCascading(data);
                return _finalDataList;
              }}
            />
            <ProFormText name="address" label="地址" colProps={{ span: 24 }} />
            <ProFormRadio.Group
              name="isGrade"
              label="是否纳入评级管理"
              colProps={{ span: 24 }}
              options={isGradeOptions}
              fieldProps={{
                defaultValue: '1',
              }}
            />
          </ProForm>
        </div>

        {type !== 'view' ? (
          <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
            <Button type="default" onClick={close}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
              保存
            </Button>
          </div>
        ) : null}
      </div>
    </Drawer>
  );
};

export default Edit;
