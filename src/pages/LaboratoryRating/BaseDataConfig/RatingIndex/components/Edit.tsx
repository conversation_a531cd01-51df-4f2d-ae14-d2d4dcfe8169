/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  createNewRatingTable,
  getRatingDetailsById,
  getRatingNumberCode,
  getTotalScore,
  modifiedRatingTableStatus,
  TRatingIndexDetail,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import EProFormGroup from '@/components/EProFromGroup';
import KeyPointConfig from './KeyPointConfig';
import MetricConfig from './MetricConfig';

export const RatingIndexNewContext = createContext({});

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  detailInfo?: TRatingIndexDetail;
  type: 'add' | 'edit' | 'view';
  tableReload: any;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  detailInfo,
  type,
  tableReload,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const formRef = useRef<ProFormInstance>(null);

  // 当前获取的新的评审表编号
  const [newRatingCode, setNewRatingCode] = useState<string>();

  // 当前输入的评审表名称
  const [curInputTableName, setCurInputTableName] = useState<string>();

  // 当前的评审表ID
  const [curRatingTableId, setCurRatingTableId] = useState<string>();

  // 当前在指标配置中选择的一行数据，可能是条件、类别或指标
  const [
    curSelectedRowDataInMetricConfig,
    setCurSelectedRowDataInMetricConfig,
  ] = useState<Record<string, any>>();

  // 左侧指标配置表格刷新方法
  const [metricConfigReload, setMetricConfigReload] = useState<() => void>();

  // 右侧要点配置表格刷新方法
  const [keyPointConfigReload, setKeyPointConfigReload] =
    useState<() => void>();

  // 用于防止 React 严格模式下重复执行
  const hasInitializedRef = useRef(false);

  /**
   * 新建时获取评审表编号和ID
   */
  const queryRatingNumberCode = async () => {
    try {
      console.log('获取新的评审表编号');
      const { code, data, msg } = await getRatingNumberCode({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      console.log('获取评审表编号成功:', data);
      setNewRatingCode(data?.code);
      setCurRatingTableId(data?.tableId);
      console.log('设置 curRatingTableId 为:', data?.tableId);
      formRef?.current?.setFieldValue('code', data?.code);
      formRef?.current?.setFieldValue('id', data?.tableId);
      formRef?.current?.setFieldValue('name', data?.name);
      setCurInputTableName(data?.name);
    } catch (err) {
      console.error('获取评审表编号失败:', err);
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 新建评审表
   */
  const handleCreateNewRatingTable = async () => {
    if (!formRef?.current?.getFieldValue('name')) return;

    try {
      const _params: Record<string, any> = {
        id: curRatingTableId,
        name: formRef?.current?.getFieldValue('name'),
        code: newRatingCode,
        status: '0',
      };

      const { code, data, msg } = await createNewRatingTable(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 动态实时的获取当前评审表的总分值  getTotalScore
   * @returns
   */
  const queryTotalScore = async (id: string) => {
    if (!id) return;

    try {
      const { code, data, msg } = await getTotalScore({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      formRef?.current?.setFieldValue('score', data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 获取评审表详情
   * @returns
   */
  const getDetails = async (id: string) => {
    if (!id) return;

    try {
      console.log('开始获取评审表详情，ID:', id);
      const { code, data, msg } = await getRatingDetailsById({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      console.log('获取评审表详情成功:', data);
      formRef?.current?.setFieldsValue(data);
      setCurInputTableName(data?.name);
      setCurRatingTableId(data?.id);
      console.log('设置 curRatingTableId 为:', data?.id);
    } catch (err) {
      console.error('获取评审表详情失败:', err);
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 修改评审表状态
   * @returns
   */
  const handleModifiedTableStatus = async () => {
    const _code = formRef?.current?.getFieldValue('code');
    if (!_code) {
      message.error('操作失败,缺少评审表编号');
      return;
    }

    try {
      setLoading(true);
      const _params: Record<string, any> = {
        id: curRatingTableId,
        code: _code,
        status: 1,
      };

      const { code, msg } = await modifiedRatingTableStatus(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
    // 重置初始化标记，以便下次打开时能正常执行
    hasInitializedRef.current = false;
  };

  useEffect(() => {
    console.log('Edit useEffect 触发:', { detailInfo, type });

    // 防止严格模式下重复执行
    if (hasInitializedRef.current) {
      console.log('已经初始化过，跳过重复执行');
      return;
    }

    if (!detailInfo) {
      console.log('新增模式，获取新的评审表编号');
      hasInitializedRef.current = true;
      queryRatingNumberCode();
    } else {
      if (detailInfo?.id) {
        console.log('编辑/查看模式，获取评审表详情:', detailInfo.id);
        hasInitializedRef.current = true;
        // 确保 ID 转换为字符串格式
        getDetails(String(detailInfo.id));
      }
    }
  }, [detailInfo]);

  useEffect(() => {
    queryTotalScore(curRatingTableId!);
  }, []);

  // 监听 curRatingTableId 的变化
  useEffect(() => {
    console.log('curRatingTableId 变化:', curRatingTableId);
  }, [curRatingTableId]);

  /**
   * 刷新两个子组件的数据
   */
  const refreshBothTables = () => {
    metricConfigReload && metricConfigReload();
    keyPointConfigReload && keyPointConfigReload();
  };

  const initValue = {
    formRef,
    curInputTableName,
    type,
    curRatingTableId,
    curSelectedRowDataInMetricConfig,
    setCurSelectedRowDataInMetricConfig,
    queryTotalScore,
    getDetails,
    setMetricConfigReload,
    setKeyPointConfigReload,
    refreshBothTables,
  };

  return (
    <RatingIndexNewContext.Provider value={initValue}>
      <div className="relative w-full h-full flex flex-col">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
            initialValues={{}}
            onValuesChange={(values: Record<string, any>) => {
              setCurInputTableName(values?.name);
            }}
          >
            <EProFormGroup title="评审表基本信息">
              <ProFormText
                name="name"
                label="评审表名称"
                placeholder="请先填写评审表名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '评审表名称必填！' }]}
                labelCol={{ span: 8 }}
                fieldProps={{
                  onBlur: handleCreateNewRatingTable,
                }}
                disabled={type === 'view'}
              />
              <ProFormText
                name="code"
                label="评审表编号"
                colProps={{ span: 8 }}
                labelCol={{ span: 8 }}
                disabled
              />
              <ProFormText
                name="score"
                label="评审表总分值"
                placeholder=""
                colProps={{ span: 8 }}
                labelCol={{ span: 8 }}
                disabled
              />
            </EProFormGroup>
            <EProFormGroup title="评审指标配置">
              <div className="w-full flex flex-row flex-nowrap justify-start items-center gap-2">
                <div className="w-[25%] h-full">
                  <MetricConfig />
                </div>
                <div className="flex-1 h-full">
                  <KeyPointConfig />
                </div>
              </div>
            </EProFormGroup>
          </ProForm>
        </div>
        {type !== 'view' ? (
          <div className="h-12 bg-white flex justify-center items-center z-10 gap-3">
            <Button type="default" onClick={close}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                setOpen(false);
                tableReload();
              }}
              loading={loading}
              disabled={!curInputTableName}
            >
              保存
            </Button>
            {/* <Button
              type="primary"
              onClick={handleModifiedTableStatus}
              loading={loading}
              disabled={!curInputTableName}
            >
              启用
            </Button> */}
          </div>
        ) : null}
      </div>
    </RatingIndexNewContext.Provider>
  );
};

export default Edit;
