/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  指标配置
 */
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Button, Input, message, Modal, Popconfirm, Space } from 'antd';
import {
  createNewConfiguate,
  deleteConfiguate,
  editConfiguate,
  getFlushTree,
  getMetricConfigData,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { RatingIndexNewContext } from './Edit';
import './style.less';

type TMetricConfigProps = {};

const MetricConfig: React.FC<TMetricConfigProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  const {
    formRef,
    curInputTableName,
    type,
    curRatingTableId,
    curSelectedRowDataInMetricConfig,
    setCurSelectedRowDataInMetricConfig,
    curMetricConfigDataList,
    getDetails,
    setMetricConfigReload,
  } = useContext<Record<string, any>>(RatingIndexNewContext);

  // 是否展开/折叠
  const [isExpansion, setIsExpansion] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  // 展开的行
  const [expandKeys, setExpandKeys] = useState<React.Key[]>([]);

  // 当前的操作类型，condition/classify/metric/point
  const [curOperationType, setCurOperationType] = useState<
    'condition' | 'classify' | 'metric' | 'point'
  >('condition');

  // ================= 条件 ======================
  // 新增或编辑条件
  const [addAndEditConditionModal, setAddAndEditConditionModal] =
    useState<boolean>(false);
  // 当前条件的操作类型 - 新增&编辑
  const [handleConditonType, setHandleConditonType] = useState<
    'add' | 'edit' | undefined
  >('add');
  // 当前选择的条件名称
  const [curSelectConditionName, setCurSelectConditionName] =
    useState<string>();

  // 当前选中的指标ID
  const [curSelectedMetricId, setCurSelectedMetricId] = useState<string>();

  // 当前输入的提交名称
  let newConditionNameRef = useRef('');

  // =================== 类别 ==========================
  // 当前添加&编辑条件时选择的条件ID
  const [
    curAddAndEditClassifyWithCondtionId,
    setCurAddAndEditClassifyWithCondtionId,
  ] = useState<string>();
  // 新增&编辑类别 Modal
  const [addAndEditClassifyModal, setAddAndEditClassifyModal] =
    useState<boolean>(false);

  // 当前类别的操作类型 - 新增&编辑
  const [handleClassifyType, setHandleClassifyType] = useState<
    'add' | 'edit' | undefined
  >('add');

  // 当前选择的类别名称
  const [curSelectedClassifyName, setCurSelectedClassifyName] =
    useState<string>();

  // 当前选择的类别ID
  const [curSelectedClassifyId, setCurSelectedClassifyId] = useState<string>();

  // 当前输入的提交名称
  let newClassifyNameRef = useRef('');

  // ==================== 指标 ===========================
  // 当前添加&编辑指标时选择的类别ID
  const [
    curAddAndEditMetricWithClassifyId,
    setCurAddAndEditMetricWithClassifyId,
  ] = useState<string>();

  // 新增&编辑指标 Modal
  const [addAndEditMetricModal, setAddAndEditMetricModal] =
    useState<boolean>(false);

  // 当前指标的操作类型 - 新增&编辑
  const [handleMetricType, setHandleMetricType] = useState<
    'add' | 'edit' | undefined
  >('add');

  // 当前选择的指标名称
  const [curSelectedMetricName, setCurSelectedMetricName] = useState<string>();

  // 当前选择的指标ID
  const [curSelectMetricId, setCurSelectMetricId] = useState<string>();

  // 当前输入的指标名称
  let newMetricNameRef = useRef('');

  /**
   * 刷新表格数据
   * @returns
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * 新增条件
   */
  const createNewCondition = async () => {
    if (!newConditionNameRef?.current) return;
    if (!curRatingTableId) {
      message.error('缺少评审表ID');
      return;
    }

    try {
      const _params: Record<string, any> = {
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 1,
        lab: newConditionNameRef?.current,
      };

      const { code, data, msg } = await createNewConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditConditionModal(false);
      newConditionNameRef.current = '';
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 编辑条件
   */
  const editCondition = async () => {
    if (!curSelectedMetricId) return;

    try {
      const _params: Record<string, any> = {
        id: curSelectedMetricId,
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 1,
        lab: newConditionNameRef?.current,
      };

      const { code, data, msg } = await editConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditConditionModal(false);
      newConditionNameRef.current = '';
      setCurSelectConditionName('');
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 删除条件
   */
  const handleDeleteConfiguate = async (id: string) => {
    try {
      const { code, data, msg } = await deleteConfiguate({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditConditionModal(false);
      newConditionNameRef.current = '';
      setCurSelectedClassifyId('');
      setCurSelectMetricId('');
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 新增类别
   */
  const createNewClassify = async () => {
    if (!newClassifyNameRef?.current) return;

    if (!curRatingTableId) {
      message.error('缺少评审表ID');
      return;
    }

    if (!curAddAndEditClassifyWithCondtionId) {
      message.error('添加&编辑类别错误, 缺少条件ID');
      return;
    }

    try {
      const _params: Record<string, any> = {
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 2,
        lab: newClassifyNameRef?.current,
        parentId: curAddAndEditClassifyWithCondtionId,
      };

      const { code, data, msg } = await createNewConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditClassifyModal(false);
      newClassifyNameRef.current = '';
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 编辑类别
   * @returns
   */
  const editNewClassify = async () => {
    if (!curSelectedClassifyId) return;

    try {
      const _params: Record<string, any> = {
        id: curSelectedClassifyId,
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 2,
        lab: newClassifyNameRef?.current,
        parentId: curAddAndEditClassifyWithCondtionId,
      };

      const { code, data, msg } = await editConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditClassifyModal(false);
      newClassifyNameRef.current = '';
      setCurSelectedClassifyName('');
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 新增指标
   */
  const createNewMetric = async () => {
    if (!newMetricNameRef?.current) return;

    if (!curRatingTableId) {
      message.error('缺少评审表ID');
      return;
    }

    if (!curAddAndEditMetricWithClassifyId) {
      message.error('添加&编辑指标错误, 缺少类型ID');
      return;
    }

    try {
      const _params: Record<string, any> = {
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 3,
        lab: newMetricNameRef?.current,
        parentId: curAddAndEditMetricWithClassifyId,
      };

      const { code, data, msg } = await createNewConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditMetricModal(false);
      newMetricNameRef.current = '';
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 编辑指标
   * @returns
   */
  const editNewMetric = async () => {
    if (!curSelectMetricId) return;

    try {
      const _params: Record<string, any> = {
        id: curSelectMetricId,
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 3,
        lab: newMetricNameRef?.current,
        parentId: curAddAndEditMetricWithClassifyId,
      };

      const { code, data, msg } = await editConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setAddAndEditMetricModal(false);
      newMetricNameRef.current = '';
      setCurSelectedMetricName('');
      tableReload();
      // 刷新评审表详情
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  useEffect(() => {
    console.log('curMetricConfigDataList:', curMetricConfigDataList);
  }, [curMetricConfigDataList]);

  useEffect(() => {
    if (curRatingTableId) {
      actionRef?.current?.reload();
    }
  }, [curRatingTableId]);

  // 将刷新方法注册到父组件
  useEffect(() => {
    if (setMetricConfigReload) {
      setMetricConfigReload(() => () => tableReload());
    }
  }, [setMetricConfigReload]);

  return (
    <>
      <div className="metric-config__wrapper w-full">
        <ProTable
          loading={loading}
          columns={
            type !== 'view'
              ? [
                  {
                    key: 'lab',
                    title: '名称',
                    dataIndex: 'lab',
                    render: (_: any, record: any) => {
                      // 如果是根节点，显示带橙色方块的样式
                      if (record.isRoot) {
                        return (
                          <div className="flex items-center">
                            <div className="w-4 h-4 bg-orange-400 rounded mr-2"></div>
                            <span className="font-medium">{record.lab}</span>
                          </div>
                        );
                      }
                      return record.lab;
                    },
                  },
                  {
                    key: 'option',
                    title: '操作',
                    valueType: 'option',
                    width: 100,
                    align: 'right',
                    render: (_, record: any) => {
                      // 如果是根节点，显示新增按钮
                      if (record.isRoot) {
                        return (
                          <div className="flex justify-end">
                            <Button
                              type="primary"
                              size="small"
                              icon={<PlusOutlined />}
                              onClick={() => {
                                setHandleConditonType('add');
                                setAddAndEditConditionModal(true);
                              }}
                              disabled={!curInputTableName}
                            >
                              新增
                            </Button>
                          </div>
                        );
                      }

                      return record?.type === '1' || record?.type === '2' ? (
                        <Space size={0} className="flex justify-end">
                          <Button
                            key="edit"
                            type="link"
                            icon={<EditOutlined />}
                            size="small"
                            className="px-1"
                            onClick={() => {
                              if (record?.type === '1') {
                                setHandleConditonType('edit');
                                setCurSelectedMetricId(record?.id);
                                //@ts-ignore
                                setCurSelectConditionName(record?.lab);
                                setAddAndEditConditionModal(true);
                              } else if (record?.type === '2') {
                                setHandleClassifyType('edit');
                                setCurSelectedClassifyName(record?.lab);
                                setCurSelectedClassifyId(record?.id);
                                setCurAddAndEditClassifyWithCondtionId(
                                  record?.parentId
                                );
                                setAddAndEditClassifyModal(true);
                              } else if (record?.type === '3') {
                                setHandleMetricType('edit');
                                setCurSelectedMetricName(record?.lab);
                                setCurSelectMetricId(record?.id);
                                setCurAddAndEditMetricWithClassifyId(
                                  record?.parentId
                                );
                                setAddAndEditMetricModal(true);
                              }
                            }}
                          />
                          {record?.type === '1' && (
                            <Button
                              key="add"
                              type="link"
                              icon={<PlusOutlined />}
                              size="small"
                              className="px-1"
                              onClick={() => {
                                if (record?.type === '1') {
                                  setCurOperationType('classify');
                                  setCurAddAndEditClassifyWithCondtionId(
                                    record?.id
                                  );
                                  setAddAndEditClassifyModal(true);
                                } else if (record?.type === '2') {
                                  setCurOperationType('metric');
                                  setCurAddAndEditMetricWithClassifyId(
                                    record?.id
                                  );
                                  setAddAndEditMetricModal(true);
                                }
                              }}
                            />
                          )}
                          <Popconfirm
                            title="删除此行？"
                            onConfirm={() => handleDeleteConfiguate(record?.id)}
                            okText="确定"
                            cancelText="取消"
                            key="del"
                          >
                            <Button
                              type="link"
                              icon={<DeleteOutlined className="text-red-500" />}
                              size="small"
                              className="px-1"
                            />
                          </Popconfirm>
                        </Space>
                      ) : (
                        <Space size={4} className="flex justify-end">
                          <Button
                            key="edit"
                            type="link"
                            icon={<EditOutlined />}
                            size="small"
                            className="px-1"
                            onClick={() => {
                              if (record?.type === '1') {
                                setHandleConditonType('edit');
                                setCurSelectedMetricId(record?.id);
                                //@ts-ignore
                                setCurSelectConditionName(record?.lab);
                                setAddAndEditConditionModal(true);
                              } else if (record?.type === '2') {
                                setHandleClassifyType('edit');
                                setCurSelectedClassifyName(record?.lab);
                                setCurSelectedClassifyId(record?.id);
                                setCurAddAndEditClassifyWithCondtionId(
                                  record?.parentId
                                );
                                setAddAndEditClassifyModal(true);
                              } else if (record?.type === '3') {
                                setHandleMetricType('edit');
                                setCurSelectedMetricName(record?.lab);
                                setCurSelectMetricId(record?.id);
                                setCurAddAndEditMetricWithClassifyId(
                                  record?.parentId
                                );
                                setAddAndEditMetricModal(true);
                              }
                            }}
                          />
                          <Popconfirm
                            title="删除此行？"
                            onConfirm={() => handleDeleteConfiguate(record?.id)}
                            okText="确定"
                            cancelText="取消"
                            key="del"
                          >
                            <Button
                              type="link"
                              icon={<DeleteOutlined className="text-red-500" />}
                              size="small"
                              className="px-1"
                            />
                          </Popconfirm>
                        </Space>
                      );
                    },
                  },
                ]
              : [
                  {
                    key: 'lab',
                    title: '名称',
                    dataIndex: 'lab',
                    render: (_: any, record: any) => {
                      // 如果是根节点，显示带橙色方块的样式
                      if (record.isRoot) {
                        return (
                          <div className="flex items-center">
                            <div className="w-4 h-4 bg-orange-400 rounded mr-2"></div>
                            <span className="font-medium">{record.lab}</span>
                          </div>
                        );
                      }
                      return record.lab;
                    },
                  },
                ]
          }
          actionRef={actionRef}
          search={false}
          request={async (params: any = {}, sort, filter) => {
            if (!curRatingTableId) return { data: [], success: true };

            setLoading(true);
            try {
              const _params: Record<string, any> = {
                tableId: curRatingTableId,
              };

              // 使用新的树结构接口
              const { code, data, msg } = await getFlushTree(_params);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg || '获取分类树数据失败');
                return { data: [], success: false };
              }

              // 创建"指标分类"根节点，将获取的数据作为其子节点
              const rootNode = {
                id: 'root-category',
                lab: '指标分类',
                type: 'root',
                children: data || [],
                isRoot: true,
              };

              const treeData = [rootNode];
              setDataSource(treeData);

              // 确保根节点默认展开
              setExpandKeys(['root-category']);

              return {
                data: treeData,
                success: true,
              };
            } catch (error) {
              console.error('获取分类树数据失败:', error);
              message.error('获取分类树数据失败');
              return { data: [], success: false };
            } finally {
              setLoading(false);
            }
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="id"
          options={false}
          pagination={false}
          toolBarRender={
            type === 'view'
              ? () => [
                  <Button
                    key="handle"
                    type="default"
                    onClick={() => {
                      const val = !isExpansion;
                      if (val) {
                        const keys: any = [];
                        function getKeys(data: any) {
                          data.forEach((item: any) => {
                            keys.push(item.id);
                            if (item.children) {
                              getKeys(item.children);
                            }
                          });
                        }
                        getKeys(dataSource);
                        setExpandKeys(['root-category', ...keys]);
                      } else {
                        setExpandKeys(['root-category']);
                      }
                      setIsExpansion(val);
                    }}
                    size="small"
                  >
                    {isExpansion ? '折叠' : '展开'}
                    所有
                  </Button>,
                ]
              : false
          }
          expandable={{
            indentSize: 32,
            expandedRowKeys: expandKeys,
            onExpandedRowsChange: (expandKeys) => {
              // 确保根节点始终保持展开状态
              const filteredKeys = expandKeys.filter(
                (key) => key !== 'root-category'
              );
              setExpandKeys(['root-category', ...filteredKeys]);
            },
            childrenColumnName: 'children',
          }}
          onRow={(record) => ({
            onClick: () => {
              // 如果是根节点，清空选中状态，让右侧显示所有指标
              if (record.isRoot) {
                setCurSelectedRowDataInMetricConfig(null);
                return;
              }
              setCurSelectedRowDataInMetricConfig(record);
            },
          })}
          rowClassName={(record: any, index: number, indent: number) => {
            // 根节点的高亮逻辑：当没有选中任何分类节点时高亮根节点
            if (record.isRoot) {
              return !curSelectedRowDataInMetricConfig
                ? 'bg-blue-100 cursor-pointer'
                : 'cursor-pointer';
            }
            // 普通节点的高亮逻辑：当前选中节点高亮
            return record?.id === curSelectedRowDataInMetricConfig?.id
              ? 'bg-blue-100 cursor-pointer'
              : 'cursor-pointer';
          }}
          className="metric-config-table"
        />
      </div>
      {/* 新增或编辑条件 */}
      <Modal
        title={handleConditonType === 'add' ? '新增' : '编辑'}
        open={addAndEditConditionModal}
        width={300}
        centered
        onOk={handleConditonType === 'add' ? createNewCondition : editCondition}
        onCancel={() => {
          setCurSelectConditionName('');
          setAddAndEditConditionModal(false);
        }}
        destroyOnClose
      >
        <Input
          placeholder="请输入"
          defaultValue={curSelectConditionName}
          onChange={(e: any) =>
            (newConditionNameRef.current = e?.target?.value)
          }
        />
      </Modal>
      {/* 新增&编辑类别 */}
      <Modal
        title={handleClassifyType === 'add' ? '新增' : '编辑'}
        open={addAndEditClassifyModal}
        width={300}
        centered
        onOk={
          handleClassifyType === 'add' ? createNewClassify : editNewClassify
        }
        onCancel={() => {
          setCurSelectedClassifyName('');
          setAddAndEditClassifyModal(false);
        }}
        destroyOnClose
      >
        <Input
          placeholder="请输入"
          defaultValue={curSelectedClassifyName}
          onChange={(e: any) => (newClassifyNameRef.current = e?.target?.value)}
        />
      </Modal>
      {/* 新增&编辑指标 */}
      <Modal
        title={handleMetricType === 'add' ? '新增指标' : '编辑指标'}
        open={addAndEditMetricModal}
        width={300}
        centered
        onOk={handleMetricType === 'add' ? createNewMetric : editNewMetric}
        onCancel={() => {
          setCurSelectedMetricName('');
          setAddAndEditMetricModal(false);
        }}
        destroyOnClose
      >
        <Input
          placeholder="请输入"
          defaultValue={curSelectedMetricName}
          onChange={(e: any) => (newMetricNameRef.current = e?.target?.value)}
        />
      </Modal>
    </>
  );
};

export default MetricConfig;
