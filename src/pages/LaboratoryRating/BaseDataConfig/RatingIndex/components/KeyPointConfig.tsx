/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */

/**
 *  要点配置
 */
import React, {
  ChangeEvent,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Col,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Upload,
} from 'antd';
import {
  createNewConfiguate,
  deleteConfiguate,
  deleteIdxBatch,
  downloadImportTemplate,
  exportRatingTable,
  getListIndex,
  getRatingPointById,
  importRatingTable,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  CloudUploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  ExportOutlined,
  ImportOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import download from '@/utils/download';
import { RatingIndexNewContext } from './Edit';

type TKeyPointConfigProps = {};

const KeyPointConfig: React.FC<TKeyPointConfigProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  const {
    formRef,
    type,
    curInputTableName,
    curRatingTableId,
    curSelectedRowDataInMetricConfig,
    queryTotalScore,
    getDetails,
    setKeyPointConfigReload,
    refreshBothTables,
  } = useContext<Record<string, any>>(RatingIndexNewContext);

  const pointFormRef = useRef<ProFormInstance>(null);

  // 是否打开新增&编辑配置要点
  const [isOpenKeyPointConfig, setIsOpenKeyPointConfig] =
    useState<boolean>(false);

  // 当前选中的要点配置id
  const [curSelectedKeyPointId, setCurSelectedKeyPointId] = useState<string>();

  // 当前选择的要点行数据集合
  const [curSelectedPointRowData, setCurSelectedPointRowData] =
    useState<Record<string, any>>();

  // 搜索字段类型
  const [searchType, setSearchType] = useState<string>('指标名称');

  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  // 选中的行的key
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 导入模态框状态
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);

  /**
   * 保存新的要点配置
   */
  const handleSaveNewPointConfig = async () => {
    if (!curRatingTableId) {
      message.error('保存失败, 缺少评审表ID');
      return;
    }

    try {
      setLoading(true);

      const _formValues = pointFormRef?.current?.getFieldsValue();

      const _params: Record<string, any> = {
        tableId: curRatingTableId,
        // 指标类别(1 条件，2 类别，3 指标，4评审要点)
        type: 3,
        parentId: curSelectedRowDataInMetricConfig?.id,
        ..._formValues,
      };

      if (curSelectedPointRowData) {
        _params['id'] = curSelectedPointRowData?.id;
      }

      const { code, data, msg } = await createNewConfiguate(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      setIsOpenKeyPointConfig(false);
      setCurSelectedPointRowData({});
      actionRef.current?.reload();
      queryTotalScore(curRatingTableId);
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 删除要点
   * @param id
   * @returns
   */
  const handleDeleteConfiguate = async (id: string) => {
    try {
      const { code, data, msg } = await deleteConfiguate({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      actionRef?.current?.reload();
      queryTotalScore(curRatingTableId);
      getDetails && getDetails(curRatingTableId);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请至少选择一条记录');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          // 调用批量删除API
          const { code, msg } = await deleteIdxBatch(
            selectedRowKeys as string[]
          );

          if (code === codeDefinition.QUERY_SUCCESS) {
            message.success(`已删除 ${selectedRowKeys.length} 条记录`);
            setSelectedRowKeys([]);
            actionRef?.current?.reload();
            // 更新总分
            curRatingTableId && queryTotalScore(curRatingTableId);
            // 刷新评审表详情
            curRatingTableId && getDetails && getDetails(curRatingTableId);
          } else {
            message.error(msg || '批量删除失败');
          }
        } catch (error) {
          console.error('批量删除错误:', error);
          message.error('批量删除失败，请检查后再试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    actionRef?.current?.reload();
  };

  /**
   * 处理重置
   */
  const handleReset = () => {
    setSearchKeyword('');
    actionRef?.current?.reload();
  };

  // 下载模板功能
  const handleDownloadTemplate = async () => {
    try {
      setLoading(true);
      // 使用封装好的 API 请求下载模板
      const response = await downloadImportTemplate();

      // 直接使用项目中的 download 工具函数（它已经处理了响应拦截器返回的特殊格式）
      await download(response, '导入模板.xlsx');
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败，请检查后再试');
    } finally {
      setLoading(false);
    }
  };

  // 导出评审表功能
  const handleExportRatingTable = async () => {
    if (!curRatingTableId) {
      message.error('导出失败，缺少评审表ID');
      return;
    }

    try {
      setLoading(true);
      // 调用导出API
      const response = await exportRatingTable(curRatingTableId);

      // 使用download工具函数下载文件
      await download(response, '评审表.xlsx');
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请检查后再试');
    } finally {
      setLoading(false);
    }
  };

  // 上传文件前的校验
  const beforeUpload = (file: File) => {
    const isExcel =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('文件不能超过5MB!');
      return Upload.LIST_IGNORE;
    }

    setImportFile(file);
    return false; // 阻止自动上传
  };

  // 处理导入
  const handleImport = async () => {
    if (!importFile) {
      message.error('请先选择文件');
      return;
    }

    if (!curRatingTableId) {
      message.error('导入失败，缺少评审表ID');
      return;
    }

    setUploading(true);

    try {
      // 调用导入API
      const { code, msg } = await importRatingTable(
        importFile,
        curRatingTableId
      );

      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('导入成功');
        setImportModalVisible(false);
        setImportFile(null);

        // 刷新当前表格
        actionRef.current?.reload();

        // 刷新左右两个表格数据
        refreshBothTables && refreshBothTables();

        // 更新总分
        queryTotalScore && queryTotalScore(curRatingTableId);

        // 刷新评审表详情
        getDetails && getDetails(curRatingTableId);
      } else {
        message.error(msg || '导入失败');
      }
    } catch (error) {
      console.error('导入错误:', error);
      message.error('导入失败，请检查后再试');
    } finally {
      setUploading(false);
    }
  };

  useEffect(() => {
    actionRef?.current?.reload();
  }, [curSelectedRowDataInMetricConfig]);

  // 当 curRatingTableId 初始化或变化时，触发表格加载
  useEffect(() => {
    if (curRatingTableId) {
      console.log('curRatingTableId changed:', curRatingTableId);
      actionRef?.current?.reload();
    }
  }, [curRatingTableId]);

  // 将刷新方法注册到父组件
  useEffect(() => {
    if (setKeyPointConfigReload) {
      setKeyPointConfigReload(() => () => actionRef?.current?.reload());
    }
  }, [setKeyPointConfigReload]);

  return (
    <>
      <div className="keypoint-config-wrapper w-full">
        <div className="search-header mb-3">
          <Row gutter={16} align="middle">
            <Col span={8}>
              <Input.Group compact>
                <Select
                  defaultValue="指标名称"
                  style={{ width: '120px' }}
                  options={[
                    { value: '指标名称', label: '指标名称' },
                    { value: '评审要点', label: '评审要点' },
                    { value: '评审规则', label: '评审规则' },
                  ]}
                  onChange={(value) => setSearchType(value)}
                />
                <Input
                  placeholder="请输入"
                  value={searchKeyword}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setSearchKeyword(e.target.value)
                  }
                  style={{ width: 'calc(100% - 120px)' }}
                  onPressEnter={handleSearch}
                />
              </Input.Group>
            </Col>
            <Col span={5}>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={11} style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    setIsOpenKeyPointConfig(true);
                  }}
                  disabled={
                    !curInputTableName ||
                    !curSelectedRowDataInMetricConfig ||
                    curSelectedRowDataInMetricConfig?.type === '1'
                  }
                >
                  新增
                </Button>
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => setImportModalVisible(true)}
                >
                  导入
                </Button>
                <Button
                  icon={<ExportOutlined />}
                  onClick={handleExportRatingTable}
                  disabled={!curRatingTableId}
                >
                  导出
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                  disabled={selectedRowKeys.length === 0}
                >
                  删除
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <ProTable
          loading={loading}
          columns={
            type !== 'view'
              ? [
                  {
                    dataIndex: 'index',
                    valueType: 'indexBorder',
                    width: 48,
                    title: '序号',
                  },
                  {
                    key: 'lab',
                    title: '指标名称',
                    dataIndex: 'lab',
                    ellipsis: true,
                  },
                  {
                    key: 'point',
                    title: '评审要点',
                    dataIndex: 'point',
                    ellipsis: true,
                  },
                  {
                    key: 'rule',
                    title: '评审规则',
                    dataIndex: 'rule',
                    ellipsis: true,
                  },
                  {
                    key: 'score',
                    title: '分值',
                    dataIndex: 'score',
                    width: 80,
                  },


                  {
                    key: 'tip',
                    title: '指标提示',
                    dataIndex: 'tip',
                    ellipsis: true,
                    width: 180,
                  },

                  {
                    key: 'option',
                    title: '操作',
                    valueType: 'option',
                    width: 120,
                    fixed: 'right',
                    align: 'center',
                    render: (text, record, _, action) => (
                      <Space size={0}>
                        <Button
                          key="edit"
                          type="link"
                          icon={<EditOutlined />}
                          size="small"
                          className="px-1"
                          onClick={() => {
                            setCurSelectedKeyPointId(record?.id);
                            setCurSelectedPointRowData(record);
                            setIsOpenKeyPointConfig(true);
                          }}
                        />
                        <Popconfirm
                          title="删除此行？"
                          onConfirm={() => handleDeleteConfiguate(record?.id)}
                          okText="确定"
                          cancelText="取消"
                          key="del"
                        >
                          <Button
                            type="link"
                            icon={<DeleteOutlined className="text-red-500" />}
                            size="small"
                            className="px-1"
                          />
                        </Popconfirm>
                      </Space>
                    ),
                  },
                ]
              : [
                  {
                    dataIndex: 'index',
                    valueType: 'indexBorder',
                    width: 48,
                    title: '序号',
                  },
                  {
                    key: 'lab',
                    title: '指标名称',
                    dataIndex: 'lab',
                    ellipsis: true,
                  },
                  {
                    key: 'point',
                    title: '评审要点',
                    dataIndex: 'point',
                    ellipsis: true,
                  },
                  {
                    key: 'rule',
                    title: '评审规则',
                    dataIndex: 'rule',
                    ellipsis: true,
                  },
               {
                    key: 'tip',
                    title: '指标提示',
                    dataIndex: 'tip',
                    ellipsis: true,
                    width: 180,
                  },


                  {
                    key: 'score',
                    title: '分值',
                    dataIndex: 'score',
                    width: 80,
                  },
                ]
          }
          actionRef={actionRef}
          search={false}
          request={async (params: any = {}, sort, filter) => {
            try {
              setLoading(true);

              // 构建请求参数 - 基础分页参数
              const _params: Record<string, any> = {
                pageSize: params.pageSize || 10,
                pageNum: params.current || 1,
              };

              console.log('KeyPointConfig request 调用参数:', {
                curSelectedRowDataInMetricConfig,
                curRatingTableId,
                params,
              });

              // 判断调用场景：
              // 1. 如果选择了左侧的分类节点，使用 id 参数（分类ID）
              // 2. 如果没有选择分类或需要获取所有指标，使用 tableId 参数（评审表ID）
              if (curSelectedRowDataInMetricConfig?.id) {
                // 选择了分类节点，传入分类 ID
                _params.id = curSelectedRowDataInMetricConfig.id;
                console.log(
                  '使用分类 ID:',
                  curSelectedRowDataInMetricConfig.id
                );
              } else if (curRatingTableId) {
                // 没有选择分类或需要获取所有指标，传入评审表 ID
                _params.tableId = curRatingTableId;
                console.log('使用评审表 ID:', curRatingTableId);
              } else {
                // 既没有分类ID也没有评审表ID，返回空数据
                console.log('没有分类ID和评审表ID，返回空数据');
                return {
                  data: [],
                  success: true,
                  total: 0,
                };
              }

              // 如果有搜索关键词，根据搜索类型添加对应的参数
              if (searchKeyword) {
                // 根据searchType确定搜索字段
                if (searchType === '指标名称') {
                  _params.lab = searchKeyword;
                } else if (searchType === '评审要点') {
                  _params.point = searchKeyword;
                } else if (searchType === '评审规则') {
                  _params.rule = searchKeyword;
                }
              }

              console.log('最终请求参数:', _params);

              // 使用新的指标接口
              const { code, data, msg } = await getListIndex(_params);
              console.log('接口返回结果:', { code, data, msg });

              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              // 根据新的接口返回数据结构适配
              // 新接口返回格式：data.rows 包含数据列表，data.total 包含总数
              const records = data?.rows || [];
              const total = data?.total || 0;

              console.log('处理后的数据:', { records, total, rawData: data });

              return {
                data: records,
                success: true,
                total: total,
              };
            } catch (err) {
              console.error('获取指标数据失败:', err);
              message.error('获取指标数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            } finally {
              setLoading(false);
            }
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys),
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="id"
          options={false}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          toolBarRender={false}
        />
      </div>

      <Modal
        width={600}
        title={curSelectedKeyPointId ? '编辑' : '新增'}
        open={isOpenKeyPointConfig}
        onCancel={() => {
          setCurSelectedKeyPointId(undefined);
          setCurSelectedPointRowData({});
          setIsOpenKeyPointConfig(false);
        }}
        footer={false}
        centered
        destroyOnClose
      >
        <div className="flex-1 p-4 overflow-x-hidden">
          <ProForm
            onFinish={() => {}}
            formRef={pointFormRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={{
              render: (_, dom) => (
                <div className="flex justify-center items-center mb-4">
                  <Space>
                    <Button
                      type="default"
                      loading={loading}
                      onClick={() => setIsOpenKeyPointConfig(false)}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      loading={loading}
                      onClick={handleSaveNewPointConfig}
                    >
                      保存
                    </Button>
                  </Space>
                </div>
              ),
            }}
            //@ts-ignore
            onValuesChange={(_, values: any) => {}}
            initialValue={{ lab: '', point: '', rule: '', score: '' }}
          >
            <ProFormText
              name="lab"
              label="指标名称"
              placeholder="请输入指标名称"
              rules={[{ required: true, message: '请输入指标名称' }]}
              labelCol={{ span: 4 }}
              initialValue={curSelectedPointRowData?.lab}
            />
            <ProFormTextArea
              name="point"
              label="评审要点"
              placeholder="请输入评审要点"
              rules={[{ required: true, message: '请输入评审要点' }]}
              labelCol={{ span: 4 }}
              initialValue={curSelectedPointRowData?.point}
            />
            <ProFormTextArea
              name="rule"
              label="评分规则"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入评分规则' }]}
              labelCol={{ span: 4 }}
              initialValue={curSelectedPointRowData?.rule}
            />
            <ProFormDigit
              name="score"
              label="分值"
              labelCol={{ span: 4 }}
              rules={[{ required: true, message: '请输入分值' }]}
              fieldProps={{ precision: 2 }}
              initialValue={curSelectedPointRowData?.score}
            />
            <ProFormTextArea
              name="tip"
              label="指标提示"
              placeholder="请输入"
              labelCol={{ span: 4 }}
              initialValue={curSelectedPointRowData?.tip}
            />

          </ProForm>
        </div>
      </Modal>

      {/* 导入模态框 */}
      <Modal
        title="导入"
        open={importModalVisible}
        onCancel={() => {
          setImportModalVisible(false);
          setImportFile(null);
        }}
        footer={null}
        centered
        width={650}
        destroyOnClose
      >
        <div className="mt-8 px-6">
          <div className="mb-4">
            <div className="flex items-start mb-2">
              <div className="bg-blue-100 rounded-full w-6 h-6 flex justify-center items-center text-blue-500 mr-2">
                1
              </div>
              <span>导入模板中有字段说明，必填字段必须填写；</span>
            </div>
            <div className="flex items-start mb-2">
              <div className="bg-blue-100 rounded-full w-6 h-6 flex justify-center items-center text-blue-500 mr-2">
                2
              </div>
              <span>当你多次重复导入会叠加数据，不会覆盖数据；</span>
            </div>
            <div className="flex items-start mb-4">
              <div className="bg-blue-100 rounded-full w-6 h-6 flex justify-center items-center text-blue-500 mr-2">
                3
              </div>
              <span>单次导入的文件大小不能超过5M；</span>
            </div>
          </div>

          <div className=" mb-4 text-center">
            <Upload.Dragger
              name="file"
              beforeUpload={beforeUpload}
              maxCount={1}
              showUploadList={false}
              accept=".xlsx,.xls"
              capture={false as any}
            >
              <CloudUploadOutlined
                style={{ fontSize: 36, color: '#1890ff', marginBottom: 8 }}
              />
              <p>点击或拖拽Excel文件到这里</p>
              {importFile && (
                <p className="text-green-600 mt-2">
                  已上传文件: {importFile.name}
                </p>
              )}
            </Upload.Dragger>
          </div>

          <div className="text-center mb-4">
            <Button type="link" onClick={handleDownloadTemplate}>
              点击下载模板
            </Button>
          </div>

          <div className="flex justify-end mt-8">
            <Space>
              <Button onClick={() => setImportModalVisible(false)}>取消</Button>
              <Button
                type="primary"
                loading={uploading}
                onClick={handleImport}
                disabled={!importFile}
              >
                开始导入
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default KeyPointConfig;
