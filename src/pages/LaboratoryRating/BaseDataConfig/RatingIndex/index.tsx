/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm } from 'antd';
import {
  copyAddNewRecord,
  deleteRatingIndex,
  getRatingIndexListNew,
  ratingIndexListApi,
  TRatingIndexDetail,
  updateRatingIndexStatusApi,
} from '@/api/LaboratoryRating/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { indexStatusOptions } from '../data';

const RatingIndex: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit' | 'view'>('add');
  const [curSelectRow, setCurSelectRow] = useState<TRatingIndexDetail>();

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '评审表名称',
      dataIndex: 'name',
    },
    {
      title: '条件数',
      dataIndex: 'conditionNum',
      hideInSearch: true,
    },
    {
      title: '类别数',
      dataIndex: 'typeNum',
      hideInSearch: true,
    },
    {
      title: '指标数',
      dataIndex: 'indexNum',
      hideInSearch: true,
    },
    {
      title: '评审要点数',
      dataIndex: 'importantNum',
      hideInSearch: true,
    },
    {
      title: '总分值',
      dataIndex: 'score',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'createId',
      hideInSearch: true,
      render: (_, record) => <span>{record?.createName}</span>,
    },
    {
      title: '最后更新日期',
      dataIndex: 'updateTime',
      hideInSearch: true,
    },
    // 评审表状态  1：启用 0：草稿
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (_, record) => (
        <span>{record?.status === '1' ? '已启用' : '草稿'}</span>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (text, record, _, action) =>
        record?.status === '0'
          ? [
              <Button
                key="update"
                type="link"
                size="small"
                onClick={() => {
                  setEditType('edit');
                  setCurSelectRow(record);
                  setEditOpen(true);
                }}
              >
                编辑
              </Button>,
              <Popconfirm
                title="删除"
                description="确认删除该项?"
                onConfirm={() => handleDeleteRatingIndex(record?.id)}
                key="del"
              >
                <Button type="link" size="small" danger>
                  删除
                </Button>
              </Popconfirm>,
            ]
          : [
              <Button
                key="view"
                type="link"
                size="small"
                onClick={() => {
                  setEditType('view');
                  setCurSelectRow(record);
                  setEditOpen(true);
                }}
              >
                查看
              </Button>,
              <Popconfirm
                title=""
                description="确认复制新增该项?"
                onConfirm={() => handleCopyAddNewRecord(record?.id)}
                key="del"
              >
                <Button key="copy" type="link" size="small">
                  复制新增
                </Button>
              </Popconfirm>,
            ],
    },
  ];

  /**
   * 删除评审表
   */
  const handleDeleteRatingIndex = async (id: string) => {
    try {
      const { code, msg } = await deleteRatingIndex({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * 复制新增操作
   */
  const handleCopyAddNewRecord = async (id: string) => {
    try {
      const { code, data, msg } = await copyAddNewRecord({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _param = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _param.current;
          const { code, data, msg } = await getRatingIndexListNew(_param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            type="primary"
            onClick={() => {
              setEditType('add');
              setCurSelectRow(undefined);
              setEditOpen(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Drawer
        width="85%"
        title={
          editType === 'add' ? '新增' : editType === 'edit' ? '编辑' : '详情'
        }
        onClose={() => {
          setEditOpen(false);
          tableReload();
        }}
        open={editOpen}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          open={editOpen}
          setOpen={(val) => {
            tableReload();
            setEditOpen(val);
          }}
          detailInfo={curSelectRow}
          type={editType}
          tableReload={tableReload}
        />
      </Drawer>
    </PageContainer>
  );
};

export default RatingIndex;
