/*
 * @Date: 2024-08-20 08:49:13
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:33:35
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\CaseInfo\MedicalActivityInfo\index.tsx
 * @Description: 诊疗活动信息卡
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  deviceCalibrateRecordListApi,
  medicalActivityInfoListApi,
} from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const MedicalActivityInfo: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '诊疗活动类型',
      dataIndex: 'activityTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '就诊流水号',
      dataIndex: 'serialNumber',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊疗活动发生日期时间',
      dataIndex: 'activityTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '身份证件类别',
      dataIndex: 'idCardTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '身份证件号码',
      dataIndex: 'idCard',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '主诉',
      dataIndex: 'chiefComplaint',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '现病史/入院情况',
      dataIndex: 'presentIllnessHis',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '体格检查',
      dataIndex: 'physicalExamination',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '辅助检查',
      dataIndex: 'studiesSummaryResult',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '疾病诊断日期',
      dataIndex: 'diagnoseTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '传染病诊断',
      dataIndex: 'diseaseName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '西医疾病诊断',
      dataIndex: 'wmDiseaseName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '中医病名',
      dataIndex: 'tcmDiseaseName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '中医证候',
      dataIndex: 'tcmSyndromeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊断医生',
      dataIndex: 'fillDoctor',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '科室',
      dataIndex: 'deptName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '医疗机构',
      dataIndex: 'orgName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作人ID',
      dataIndex: 'operatorId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>
      ),
      fixed: 'right',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await medicalActivityInfoListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
        // toolBarRender={() => [
        //   <ListImport
        //     children={
        //       <Button type="primary" onClick={() => setListImportOpen(true)}>
        //         导入
        //       </Button>
        //     }
        //     open={listImportOpen}
        //     setOpen={(val) => {
        //       tableReload();
        //       setListImportOpen(val);
        //     }}
        //     downLoadUrl={'/data/emrActivity/info/downloadModel'}
        //     importUrl={
        //       import.meta.env.VITE_URL + '/data/emrActivity/info/importDate'
        //     }
        //   ></ListImport>,
        // ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={setDetailOpen}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default MedicalActivityInfo;
