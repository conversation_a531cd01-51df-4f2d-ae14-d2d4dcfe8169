import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { checkoutReportDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';
import DetailText from './DetailText';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async () => {
    try {
      const { code, data, msg } = await checkoutReportDetailApi(
        curSelectedRowId
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId) queryDetailsById();
  }, [curSelectedRowId]);

  return (
    <Drawer width="80%" title="详情" onClose={close} open={open} destroyOnClose>
      <DetailText rowDetails={rowDetails} />
      {/* <div className=" flex flex-col w-full h-full gap-4 p-4">
        <BlockContainer title="检验报告">
          <Descriptions>
            <Descriptions.Item label="ID">{rowDetails?.id}</Descriptions.Item>
            <Descriptions.Item label="患者基本信息ID">
              {rowDetails?.patientId}
            </Descriptions.Item>
            <Descriptions.Item label="就诊记录类型代码">
              {rowDetails?.activityTypeCode}
            </Descriptions.Item>
            <Descriptions.Item label="就诊记录类型名称">
              {rowDetails?.activityTypeName}
            </Descriptions.Item>
            <Descriptions.Item label="就诊流水号">
              {rowDetails?.serialNumber}
            </Descriptions.Item>
            <Descriptions.Item label="患者姓名">
              {rowDetails?.patientName}
            </Descriptions.Item>
            <Descriptions.Item label="身份证件类别代码">
              {rowDetails?.idCardTypeCode}
            </Descriptions.Item>
            <Descriptions.Item label="身份证件类别名称">
              {rowDetails?.idCardTypeName}
            </Descriptions.Item>
            <Descriptions.Item label="身份证件号码">
              {rowDetails?.idCard}
            </Descriptions.Item>
            <Descriptions.Item label="病房号">
              {rowDetails?.wardNo}
            </Descriptions.Item>
            <Descriptions.Item label="病区名称">
              {rowDetails?.wardName}
            </Descriptions.Item>
            <Descriptions.Item label="病床号">
              {rowDetails?.bedNo}
            </Descriptions.Item>
            <Descriptions.Item label="电子申请单编号">
              {rowDetails?.applicationFormNo}
            </Descriptions.Item>
            <Descriptions.Item label="检验申请科室代码">
              {rowDetails?.applyDeptCode}
            </Descriptions.Item>
            <Descriptions.Item label="检验申请科室名称">
              {rowDetails?.applyDeptName}
            </Descriptions.Item>
            <Descriptions.Item label="检验申请机构代码">
              {rowDetails?.applyOrgCode}
            </Descriptions.Item>
            <Descriptions.Item label="检验申请机构名称">
              {rowDetails?.applyOrgName}
            </Descriptions.Item>
            <Descriptions.Item label="检验申请医师">
              {rowDetails?.applyPhysicianId}
            </Descriptions.Item>
            <Descriptions.Item label="标本类别代码">
              {rowDetails?.specimenCategoryCode}
            </Descriptions.Item>
            <Descriptions.Item label="标本类别名称">
              {rowDetails?.specimenCategoryName}
            </Descriptions.Item>
            <Descriptions.Item label="检验标本号">
              {rowDetails?.specimenNo}
            </Descriptions.Item>
            <Descriptions.Item label="标本采样日期时间">
              {rowDetails?.specimenSamplingDate}
            </Descriptions.Item>
            <Descriptions.Item label="接收标本日期时间">
              {rowDetails?.specimenReceivingDate}
            </Descriptions.Item>
            <Descriptions.Item label="检验医师">
              {rowDetails?.examinationPhysicianId}
            </Descriptions.Item>
            <Descriptions.Item label="检验日期">
              {rowDetails?.examinationDate}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告单编号">
              {rowDetails?.examinationReportNo}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告结果-客观所见">
              {rowDetails?.examinationObjectiveDesc}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告结果-主观提示">
              {rowDetails?.examinationSubjectiveDesc}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告备注">
              {rowDetails?.examinationNotes}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告日期">
              {rowDetails?.examinationReportDate}
            </Descriptions.Item>
            <Descriptions.Item label="报告医师">
              {rowDetails?.examinationReportId}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告机构代码">
              {rowDetails?.orgCode}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告机构名称">
              {rowDetails?.orgName}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告科室代码">
              {rowDetails?.deptCode}
            </Descriptions.Item>
            <Descriptions.Item label="检验报告科室名称">
              {rowDetails?.deptName}
            </Descriptions.Item>
            <Descriptions.Item label="操作人ID">
              {rowDetails?.operatorId}
            </Descriptions.Item>
            <Descriptions.Item label="操作时间">
              {rowDetails?.operationTime}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        {rowDetails?.emrExLabItemList && rowDetails?.emrExLabItemList.length
          ? rowDetails?.emrExLabItemList.map((_item: any) => (
              <BlockContainer title="检验报告项目">
                <Descriptions>
                  <Descriptions.Item label="院内检验项目代码">
                    {_item?.itemCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="院内检验项目名称">
                    {_item?.itemName}
                  </Descriptions.Item>
                  <Descriptions.Item label="院内检验定性结果代码">
                    {_item?.sourceExaminationResultCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="院内检验定性结果名称">
                    {_item?.sourceExaminationResultName}
                  </Descriptions.Item>
                  <Descriptions.Item label="标化检验定性结果代码">
                    {_item?.examinationResultCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="标化检验定性结果名称">
                    {_item?.examinationResultName}
                  </Descriptions.Item>
                  <Descriptions.Item label="检验定量结果">
                    {_item?.examinationQuantification}
                  </Descriptions.Item>
                  <Descriptions.Item label="检验定量结果计量单位">
                    {_item?.examinationQuantificationUnit}
                  </Descriptions.Item>
                  <Descriptions.Item label="检验定量结果参考区间-下限">
                    {_item?.examinationQuantificationLower}
                  </Descriptions.Item>
                  <Descriptions.Item label="检验定量结果参考区间-上限">
                    {_item?.examinationQuantificationUpper}
                  </Descriptions.Item>
                  <Descriptions.Item label="检验定量结果超出或低于参考值">
                    {_item?.examinationQuantificationRi}
                  </Descriptions.Item>
                  <Descriptions.Item label="操作人ID">
                    {_item?.operatorId}
                  </Descriptions.Item>
                  <Descriptions.Item label="操作时间">
                    {_item?.operationTime}
                  </Descriptions.Item>
                </Descriptions>
              </BlockContainer>
            ))
          : null}
      </div> */}
    </Drawer>
  );
};

export default Detail;
