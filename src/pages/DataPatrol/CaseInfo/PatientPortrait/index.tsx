/*
 * @Date: 2024-11-29 14:01:09
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-09 09:33:34
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\CaseInfo\PatientPortrait\index.tsx
 * @Description: 患者画像
 */
import { useState } from 'react';
import { Button, Card, Form, Input, message } from 'antd';
import CheckDetailText from '../CheckoutReport/components/DetailText';
import InfectiousDetailText from '../InfectiousDiseaseReportCard/components/DetailText';
import InspectionDetailText from '../InspectionReport/components/DetailText';
import MedicalDetailText from '../MedicalActivityInfo/components/DetailText';
import BaseDetailText from '../PatientBaseInfo/components/DetailText';
import { patientBaseInfoApi, patientCheckInfoApi, patientInfectiousInfoApi, patientInspectionInfo<PERSON><PERSON>, patientMedicalInfoApi } from '@/api/DataReporting/caseInfo';
import { codeDefinition } from '@/constants';

const PatientPortrait: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState<string>();

  const [baseInfo, setBaseInfo] = useState<Record<string, any>>()
  const getBaseInfo = (name: string) => {
    setLoading(true)
    patientBaseInfoApi(name).then(res => {
      if(res.code === codeDefinition.QUERY_SUCCESS) {
        setBaseInfo(res.data)
        if(res.data.id) {
          getMedicalInfo(res.data.id)
          getInfectiousInfo(res.data.id)
          getInspectionInfo(res.data.id)
          getCheckInfo(res.data.id)
        }
      } else {
        message.error(res.msg)
      }
    }).finally(() => {
      
    setLoading(false)
    })
  }
  const [medicalInfo, setMedicalInfo] = useState<Record<string, any>>()
  const getMedicalInfo = (name: string) => {
    patientMedicalInfoApi(name).then(res => {
      if(res.code === codeDefinition.QUERY_SUCCESS) {
        setMedicalInfo(res.data)
      } else {
        message.error(res.msg)
      }
    })
  }
  const [infectiousInfo, setInfectiousInfo] = useState<Record<string, any>>()
  const getInfectiousInfo = (name: string) => {
    patientInfectiousInfoApi(name).then(res => {
      if(res.code === codeDefinition.QUERY_SUCCESS) {
        setInfectiousInfo(res.data)
      } else {
        message.error(res.msg)
      }
    })
  }
  const [inspectionInfo, setInspectionInfo] = useState<Record<string, any>>()
  const getInspectionInfo = (name: string) => {
    patientInspectionInfoApi(name).then(res => {
      if(res.code === codeDefinition.QUERY_SUCCESS) {
        setInspectionInfo(res.data)
      } else {
        message.error(res.msg)
      }
    })
  }
  const [checkInfo, setCheckInfo] = useState<Record<string, any>>()
  const getCheckInfo = (name: string) => {
    patientCheckInfoApi(name).then(res => {
      if(res.code === codeDefinition.QUERY_SUCCESS) {
        setCheckInfo(res.data)
      } else {
        message.error(res.msg)
      }
    })
  }

  const handleSearch = () => {
    if(name)  {
      getBaseInfo(name)
    } else {
      handleReset()
    }
  };
  const handleReset = () => {
    setName(undefined)
    setBaseInfo(undefined)
    setMedicalInfo(undefined)
    setInfectiousInfo(undefined)
    setInspectionInfo(undefined)
    setCheckInfo(undefined)
  };

  return (
    <div className=" flex flex-col gap-5">
      <Card title="筛选条件">
        <div className="flex gap-5 justify-between">
          <Form submitter={false} autoComplete="off" layout="inline">
            <Form.Item label="患者姓名">
              <Input
                className="!w-56"
                value={name}
                onChange={(e: any) => setName(e.target.value)}
                allowClear
                placeholder="请输入"
              />
            </Form.Item>
          </Form>
          <div className="flex gap-3">
            <Button type="primary" onClick={handleSearch} loading={loading}>
              查询
            </Button>
            <Button loading={loading} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>
      </Card>
      <Card title="基本信息">
        <BaseDetailText rowDetails={baseInfo} />
      </Card>
      <Card title="诊疗活动信息">
        <MedicalDetailText rowDetails={medicalInfo} />
      </Card>
      <Card title="传染病报告卡">
        <InfectiousDetailText rowDetails={infectiousInfo} />
      </Card>
      <Card title="检查报告">
        <InspectionDetailText rowDetails={inspectionInfo} />
      </Card>
      <Card title="检验报告">
        <CheckDetailText rowDetails={checkInfo} />
      </Card>
    </div>
  );
};

export default PatientPortrait;
