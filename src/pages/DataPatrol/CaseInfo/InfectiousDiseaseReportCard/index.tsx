/*
 * @Date: 2024-08-20 09:21:41
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:33:19
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\CaseInfo\InfectiousDiseaseReportCard\index.tsx
 * @Description: 传染病报告卡
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { infectiousDiseaseReportCardListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const InfectiousDiseaseReportCard: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '就诊流水号',
      dataIndex: 'serialNumber',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊疗活动类型',
      dataIndex: 'activityTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '身份证件类别',
      dataIndex: 'idCardTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '身份证件号码',
      dataIndex: 'idCard',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '性别',
      dataIndex: 'genderName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '出生日期',
      dataIndex: 'birthDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '国籍/地区',
      dataIndex: 'nationalityName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '民族',
      dataIndex: 'nationName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '户籍地址',
      dataIndex: 'permanentAddrName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '户籍详细地址',
      dataIndex: 'permanentAddrDetail',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '现住地区',
      dataIndex: 'currentAddrName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '现住详细地址',
      dataIndex: 'currentAddrDetail',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '工作单位/学校名称',
      dataIndex: 'workunit',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '婚姻状况',
      dataIndex: 'maritalStatusName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '学历',
      dataIndex: 'educationName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '人群分类',
      dataIndex: 'nultitudeTypeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '人群分类其他',
      dataIndex: 'nultitudeTypeOther',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '患者电话号码',
      dataIndex: 'tel',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '联系人/监护人姓名',
      dataIndex: 'contacts',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '联系人/监护人电话号码',
      dataIndex: 'contactsTel',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '发病日期',
      dataIndex: 'onsetDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊断时间',
      dataIndex: 'diagnoseTime',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '疾病诊断名称',
      dataIndex: 'diseaseName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '其他具体疾病名称/病毒分型',
      dataIndex: 'diseaseOther',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '病例分类',
      dataIndex: 'diagnoseStateName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊断状态',
      dataIndex: 'caseTypeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '死亡日期',
      dataIndex: 'deadDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '死亡是否与此病有关',
      dataIndex: 'isDeadByThisName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '直接死亡诊断名称',
      dataIndex: 'symptomsName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '实验室检测结论',
      dataIndex: 'laboratoryDetectionVerdictName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '确认（替代策略、核酸）检测阳性日期',
      dataIndex: 'detectionPositiveDate',
      hideInSearch: true,
      width: 260,
    },
    {
      title: '确认（替代策略、核酸）检测单位',
      dataIndex: 'detectionOrgCode',
      hideInSearch: true,
      width: 240,
    },
    {
      title: '艾滋实验室确诊日期',
      dataIndex: 'dtDiagnose',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '病人所属地类型',
      dataIndex: 'afpAreatype1Name',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '麻痹日期',
      dataIndex: 'afpPalsyDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '就诊日期',
      dataIndex: 'afpDoctorDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '就诊地址类型',
      dataIndex: 'afpAreatype2Name',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '就诊地址名称',
      dataIndex: 'afpAddrcodeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '就诊地址',
      dataIndex: 'afpAddr',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '麻痹症状',
      dataIndex: 'afpPalsySymptom',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '报告日期',
      dataIndex: 'reportDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '发现方式',
      dataIndex: 'discoveryModeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '发现方式其他',
      dataIndex: 'discoveryModeOther',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '性病史',
      dataIndex: 'venerealDisName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '感染途径',
      dataIndex: 'bsTransmissionName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '感染途径其他',
      dataIndex: 'bsTransmissionOther',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '接触方式',
      dataIndex: 'contactTypeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '注射毒品史与病人共用过注射器的人数',
      dataIndex: 'injectCount',
      hideInSearch: true,
      width: 260,
    },
    {
      title: '非婚异性性接触史与病人有非婚性行为的人数',
      dataIndex: 'nonwebCount',
      hideInSearch: true,
      width: 300,
    },
    {
      title: '男男性行为史发生同性性行为的人数',
      dataIndex: 'smCount',
      hideInSearch: true,
      width: 260,
    },
    {
      title: '接触史其他',
      dataIndex: 'contactOther',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '生殖道沙眼衣原体感染名称',
      dataIndex: 'sinfectName',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '是否重症',
      dataIndex: 'serverityName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '手足口病实验室结果',
      dataIndex: 'labResultName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '乙肝HBsAg阳性时间',
      dataIndex: 'hbsagName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '首次出现乙肝症状和体征时间',
      dataIndex: 'hbsagFirst',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '无症状/不详',
      dataIndex: 'hbsagBuxiang',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '乙肝本次ALT',
      dataIndex: 'hbsagAlt',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '抗 -HBcIgM1：1000 检测结果',
      dataIndex: 'hbcigResultName',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '肝穿结果（急慢性）',
      dataIndex: 'hbliverPunctureName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '恢复期血清HBsAg阴转，抗HBs阳转',
      dataIndex: 'hbsagChangeName',
      hideInSearch: true,
      width: 240,
    },
    {
      title: '亲密接触者有无同症',
      dataIndex: 'contactflagName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '填卡医生',
      dataIndex: 'fillDoctor',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'notes',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '新冠临床严重程度',
      dataIndex: 'ncvSeverityName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '输入病例类型',
      dataIndex: 'foreignTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '输入来源地',
      dataIndex: 'placeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报告地区',
      dataIndex: 'reportZoneName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '报告单位机构',
      dataIndex: 'reportOrgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '科室',
      dataIndex: 'deptName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作人ID',
      dataIndex: 'operatorId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>
      ),
      fixed: 'right',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await infectiousDiseaseReportCardListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
        // toolBarRender={() => [
        //   <ListImport
        //     children={
        //       <Button type="primary" onClick={() => setListImportOpen(true)}>
        //         导入
        //       </Button>
        //     }
        //     open={listImportOpen}
        //     setOpen={(val) => {
        //       tableReload();
        //       setListImportOpen(val);
        //     }}
        //     downLoadUrl={'/data/emrInf/report/downloadModel'}
        //     importUrl={import.meta.env.VITE_URL + '/data/emrInf/report/importDate'}
        //   ></ListImport>,
        // ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={setDetailOpen}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default InfectiousDiseaseReportCard;
