/*
 * @Date: 2024-08-19 17:13:10
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:33:43
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\CaseInfo\PatientBaseInfo\index.tsx
 * @Description: 患者基本信息
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { patientBaseInfoListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const PatientBaseInfo: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '身份证件类别',
      dataIndex: 'idCardTypeName',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '身份证件号码',
      dataIndex: 'idCard',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '性别',
      dataIndex: 'genderName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '出生日期',
      dataIndex: 'birthDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '国籍/地区',
      dataIndex: 'nationalityName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '民族',
      dataIndex: 'nationName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '户籍地址',
      dataIndex: 'permanentAddrName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '户籍详细地址',
      dataIndex: 'permanentAddrDetail',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '现住地区',
      dataIndex: 'currentAddrName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '现住详细地址',
      dataIndex: 'currentAddrDetail',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '工作单位/学校',
      dataIndex: 'workunit',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '婚姻状况',
      dataIndex: 'maritalStatusName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '学历',
      dataIndex: 'educationName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '人群分类',
      dataIndex: 'nultitudeTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '人群分类其他',
      dataIndex: 'nultitudeTypeOther',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '患者电话号码',
      dataIndex: 'tel',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '联系人/监护人姓名',
      dataIndex: 'contacts',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '联系人/监护人电话号码',
      dataIndex: 'contactsTel',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '医疗机构',
      dataIndex: 'orgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作人ID',
      dataIndex: 'operatorId',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>
      ),
      fixed: 'right',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await patientBaseInfoListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
        // toolBarRender={() => [
        //   <ListImport
        //     children={
        //       <Button type="primary" onClick={() => setListImportOpen(true)}>
        //         导入
        //       </Button>
        //     }
        //     open={listImportOpen}
        //     setOpen={(val) => {
        //       tableReload();
        //       setListImportOpen(val);
        //     }}
        //     downLoadUrl={'/data/emrPatient/info/downloadModel'}
        //     importUrl={import.meta.env.VITE_URL + '/data/emrPatient/info/importDate'}
        //   ></ListImport>,
        // ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={setDetailOpen}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default PatientBaseInfo;
