import { Descriptions } from 'antd';
import BlockContainer from '@/components/BlockContainer';

const DetailText: React.FC<{ rowDetails?: Record<string, any> }> = ({
  rowDetails,
}) => {
  return (
    <div className=" flex flex-col w-full h-full gap-4 p-4">
      <BlockContainer title="检查报告">
        <Descriptions>
          <Descriptions.Item label="ID">{rowDetails?.id}</Descriptions.Item>
          <Descriptions.Item label="患者基本信息ID">
            {rowDetails?.patientId}
          </Descriptions.Item>
          <Descriptions.Item label="就诊记录类型代码">
            {rowDetails?.activityTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="就诊记录类型名称">
            {rowDetails?.activityTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="就诊流水号">
            {rowDetails?.serialNumber}
          </Descriptions.Item>
          <Descriptions.Item label="患者姓名">
            {rowDetails?.patientName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别代码">
            {rowDetails?.idCardTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别名称">
            {rowDetails?.idCardTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件号码">
            {rowDetails?.idCard}
          </Descriptions.Item>
          <Descriptions.Item label="病区名称">
            {rowDetails?.wardName}
          </Descriptions.Item>
          <Descriptions.Item label="病房号">
            {rowDetails?.wardNo}
          </Descriptions.Item>
          <Descriptions.Item label="病床号">
            {rowDetails?.bedNo}
          </Descriptions.Item>
          <Descriptions.Item label="电子申请单编号">
            {rowDetails?.applicationFormNo}
          </Descriptions.Item>
          <Descriptions.Item label="检查申请机构代码">
            {rowDetails?.applyOrgCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查申请机构名称">
            {rowDetails?.applyOrgName}
          </Descriptions.Item>
          <Descriptions.Item label="检查申请科室代码">
            {rowDetails?.applyDeptCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查申请科室名称">
            {rowDetails?.applyDeptName}
          </Descriptions.Item>
          <Descriptions.Item label="症状开始时间">
            {rowDetails?.symptomStartDate}
          </Descriptions.Item>
          <Descriptions.Item label="症状结束时间">
            {rowDetails?.symptomEndDate}
          </Descriptions.Item>
          <Descriptions.Item label="症状描述">
            {rowDetails?.symptomDesc}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗过程描述">
            {rowDetails?.treatmentDesc}
          </Descriptions.Item>
          <Descriptions.Item label="特殊检查标志">
            {rowDetails?.specialExaminationCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查类别代码">
            {rowDetails?.examinationTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查类别名称">
            {rowDetails?.examinationTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告结果-客观所见">
            {rowDetails?.examinationObjectiveDesc}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告结果-主观提示">
            {rowDetails?.examinationSubjectiveDesc}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告备注">
            {rowDetails?.examinationNotes}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告单编号">
            {rowDetails?.examinationReportNo}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告日期">
            {rowDetails?.examinationReportDate}
          </Descriptions.Item>
          <Descriptions.Item label="报告医师">
            {rowDetails?.examinationReportId}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告机构代码">
            {rowDetails?.orgCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告机构名称">
            {rowDetails?.orgName}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告科室代码">
            {rowDetails?.deptCode}
          </Descriptions.Item>
          <Descriptions.Item label="检查报告科室名称">
            {rowDetails?.deptName}
          </Descriptions.Item>
          <Descriptions.Item label="操作人ID">
            {rowDetails?.operatorId}
          </Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {rowDetails?.operationTime}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>

      {rowDetails?.emrExClinicalItemList &&
      rowDetails?.emrExClinicalItemList.length
        ? rowDetails?.emrExClinicalItemList.map((_item: any) => (
            <BlockContainer title="检查报告项目">
              <Descriptions>
                <Descriptions.Item label="检查项目代码">
                  {_item?.itemCode}
                </Descriptions.Item>
                <Descriptions.Item label="检查项目名称">
                  {_item?.itemName}
                </Descriptions.Item>
                <Descriptions.Item label="检查结果代码">
                  {_item?.examinationResultCode}
                </Descriptions.Item>
                <Descriptions.Item label="检查结果名称">
                  {_item?.examinationResultName}
                </Descriptions.Item>
                <Descriptions.Item label="检查定量结果">
                  {_item?.examinationQuantification}
                </Descriptions.Item>
                <Descriptions.Item label="检查定量结果计量单位">
                  {_item?.examinationQuantificationUnit}
                </Descriptions.Item>
              </Descriptions>
            </BlockContainer>
          ))
        : null}

              <BlockContainer >
              <Descriptions>
              <Descriptions.Item label="数据完整性校验结果">
                    {rowDetails?.msg}
              </Descriptions.Item>
              </Descriptions>
              </BlockContainer>

    </div>
  );
};

export default DetailText;
