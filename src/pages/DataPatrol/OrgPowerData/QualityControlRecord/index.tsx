/*
 * @Date: 2024-08-13 08:58:14
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 15:44:06
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\OrgPowerData\QualityControlRecord\index.tsx
 * @Description: 质量控制记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { qualityControlRecordListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const QualityControlRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '检测项目',
      dataIndex: 'inspectionItem',
    },
    {
      title: '内部质量控制方式',
      dataIndex: 'wayInsideControl',
      hideInSearch: true,
    },
    {
      title: '检测方法',
      dataIndex: 'inspectionMethod',
    },
    {
      title: '结果',
      dataIndex: 'result',
    },
    {
      title: '提交日期',
      dataIndex: 'submitDate',
      hideInSearch: true,
    },
    {
      title: '提交日期',
      dataIndex: 'submitDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '报告日期',
      dataIndex: 'reportDate',
      hideInSearch: true,
    },
    {
      title: '完成日期',
      dataIndex: 'finishDate',
      hideInSearch: true,
    },
    {
      title: '完成日期',
      dataIndex: 'finishDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            ...params,
          };
          delete _params.current;
          if (_params.submitDate && _params.submitDate.length) {
            _params.submitStartDate = _params.submitDate[0];
            _params.submitEndDate = _params.submitDate[1];
            delete _params.submitDate;
          }
          if (_params.finishDate && _params.finishDate.length) {
            _params.finishStartDate = _params.finishDate[0];
            _params.finishEndDate = _params.finishDate[1];
            delete _params.finishDate;
          }
          const { code, data, msg } = await qualityControlRecordListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default QualityControlRecord;
