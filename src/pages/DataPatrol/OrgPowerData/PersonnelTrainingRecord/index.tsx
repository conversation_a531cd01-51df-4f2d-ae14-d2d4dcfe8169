/*
 * @Date: 2024-07-25 18:06:03
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:30:02
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\OrgPowerData\PersonnelTrainingRecord\index.tsx
 * @Description: 人员培训记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { personnelTrainingRecordListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';
import Detail from './Components/Detail';

const PersonnelTrainingRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '培训开始时间',
      dataIndex: 'startTime',
      hideInSearch: true,
    },
    {
      title: '培训开始时间',
      dataIndex: 'startTime',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '培训结束时间',
      dataIndex: 'endTime',
      hideInSearch: true,
    },
    {
      title: '培训结束时间',
      dataIndex: 'endTime',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '培训名称',
      dataIndex: 'name',
    },
    {
      title: '培训内容',
      dataIndex: 'content',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            ...params,
          };
          delete _params.current;

          if (_params.startTime && _params.startTime.length) {
            _params.startStartTime = _params.startTime[0];
            _params.startEndTime = _params.startTime[1];
            delete _params.startTime;
          }

          if (_params.endTime && _params.endTime.length) {
            _params.endStartTime = _params.endTime[0];
            _params.endEndTime = _params.endTime[1];
            delete _params.endTime;
          }
          const { code, data, msg } = await personnelTrainingRecordListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default PersonnelTrainingRecord;
