import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { personnelTrainingRecordDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };
  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await personnelTrainingRecordDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="培训开始时间">
            {rowDetails?.startTime}
          </Descriptions.Item>
          <Descriptions.Item label="培训结束时间">
            {rowDetails?.endTime}
          </Descriptions.Item>
          <Descriptions.Item label="培训分类">
            {rowDetails?.classify}
          </Descriptions.Item>
          <Descriptions.Item label="培训名称">
            {rowDetails?.name}
          </Descriptions.Item>
          <Descriptions.Item label="培训内容">
            {rowDetails?.content}
          </Descriptions.Item>
          <Descriptions.Item label="培训单位（组织单位）">
            {rowDetails?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="评分">
            {rowDetails?.score}
          </Descriptions.Item>
          <Descriptions.Item label="培训效果评价">
            {rowDetails?.resultEvaluate}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
