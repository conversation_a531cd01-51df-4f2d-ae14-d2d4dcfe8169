import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { testMemberDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await testMemberDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="姓名">{rowDetails?.name}</Descriptions.Item>
          <Descriptions.Item label="人员分类">
            {rowDetails?.peopleClassify}
          </Descriptions.Item>
          <Descriptions.Item label="人员编号">
            {rowDetails?.peopleNo}
          </Descriptions.Item>
          <Descriptions.Item label="性别">
            {rowDetails?.sex + '' === '0' ? '男' : '女'}
          </Descriptions.Item>
          <Descriptions.Item label="出生年月">
            {rowDetails?.birthDate}
          </Descriptions.Item>
          <Descriptions.Item label="年龄">{rowDetails?.age}</Descriptions.Item>
          <Descriptions.Item label="所在部门">
            {rowDetails?.dept}
          </Descriptions.Item>
          <Descriptions.Item label="现任职务">
            {rowDetails?.post}
          </Descriptions.Item>
          <Descriptions.Item label="职称">
            {rowDetails?.postCall}
          </Descriptions.Item>
          {/* <Descriptions.Item label="毕业日期">
            {rowDetails?.graduationDate}
          </Descriptions.Item>
          <Descriptions.Item label="参加工作日期">
            {rowDetails?.joinWorkDate}
          </Descriptions.Item>
          <Descriptions.Item label="进入本单位日期">
            {rowDetails?.joinUnitDate}
          </Descriptions.Item>
          <Descriptions.Item label="身份证号">
            {rowDetails?.idCard}
          </Descriptions.Item>
          <Descriptions.Item label="政治面貌">
            {rowDetails?.politicsStatus}
          </Descriptions.Item>
          <Descriptions.Item label="毕业院校">
            {rowDetails?.graduationSchool}
          </Descriptions.Item>
          <Descriptions.Item label="所学专业">
            {rowDetails?.major}
          </Descriptions.Item>
          <Descriptions.Item label="文化程度">
            {rowDetails?.cultureLevel}
          </Descriptions.Item> */}
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
