import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { qualityInfoDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await qualityInfoDetailApi(id);

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={close}
      open={open}
      destroyOnClose
      // classNames={{
      //   body: 'bg-[#F5F5F5] !p-0',
      // }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="检测项目名称">
            {rowDetails?.projectName}
          </Descriptions.Item>
          <Descriptions.Item label="检测方法">
            {rowDetails?.methodAll}
          </Descriptions.Item>
          <Descriptions.Item label="方法简称">
            {rowDetails?.methodShort}
          </Descriptions.Item>
          <Descriptions.Item label="资质">
            {rowDetails?.certification}
          </Descriptions.Item>
          <Descriptions.Item label="是否具备检测资质">
            {rowDetails?.isCertification + '' === '0' ? '不具备' : '具备'}
          </Descriptions.Item>
          <Descriptions.Item label="备注">
            {rowDetails?.remark}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
