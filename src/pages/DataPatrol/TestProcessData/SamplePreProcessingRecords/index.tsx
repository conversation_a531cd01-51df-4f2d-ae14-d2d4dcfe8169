/*
 * @Date: 2024-07-17 11:03:42
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-11 15:41:30
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\TestProcessData\SamplePreProcessingRecords\index.tsx
 * @Description: 样本前期处理记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSamplePreviousDisposeRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const SamplePreProcessingRecords: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '检验分组',
      dataIndex: 'checkGroup',
    },
    {
      title: '制样人',
      dataIndex: 'makeSamplePeople',
      hideInSearch: true,
    },
    {
      title: '制样日期',
      dataIndex: 'makeSampleDate',
      hideInSearch: true,
    },
    {
      title: '制样日期',
      key: 'makeSampleDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '制样方式',
      dataIndex: 'makeSampleWay',
      hideInSearch: true,
    },
    {
      title: '制样后状态',
      dataIndex: 'makeSampleStatus',
    },
    {
      title: '制样数量',
      dataIndex: 'makeSampleNumber',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.makeSampleDate && _params.makeSampleDate.length) {
            _params.makeSampleStartDate = _params.makeSampleDate[0];
            _params.makeSampleEndDate = _params.makeSampleDate[1];
            delete _params.makeSampleDate;
          }
          const { code, data, msg } =
            await getSamplePreviousDisposeRecordDataList(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SamplePreProcessingRecords;
