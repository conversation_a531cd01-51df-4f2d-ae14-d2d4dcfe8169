import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { getSamplePreviousDisposeRecordDetailsById } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } =
        await getSamplePreviousDisposeRecordDetailsById({
          id,
        });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <BlockContainer title="样品基本信息">
          <Descriptions>
            <Descriptions.Item label="样品名称">
              {rowDetails?.sampleName}
            </Descriptions.Item>
            <Descriptions.Item label="样品编号">
              {rowDetails?.sampleTaskNumber}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="制样记录">
          <Descriptions>
            <Descriptions.Item label="检验分组">
              {rowDetails?.checkGroup}
            </Descriptions.Item>
            <Descriptions.Item label="制样人">
              {rowDetails?.makeSamplePeople}
            </Descriptions.Item>
            <Descriptions.Item label="制样日期">
              {rowDetails?.makeSampleDate}
            </Descriptions.Item>
            <Descriptions.Item label="制样方式">
              {rowDetails?.makeSampleWay}
            </Descriptions.Item>
            <Descriptions.Item label="制样后状态">
              {rowDetails?.makeSampleStatus}
            </Descriptions.Item>
            <Descriptions.Item label="制样数量">
              {rowDetails?.makeSampleNumber}
            </Descriptions.Item>
            <Descriptions.Item label="制样单位">
              {rowDetails?.makeSampleUnit}
            </Descriptions.Item>
            <Descriptions.Item label="制样场所">
              {rowDetails?.makeSamplePlace}
            </Descriptions.Item>
            <Descriptions.Item label="储存条件">
              {rowDetails?.storageCondition}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
