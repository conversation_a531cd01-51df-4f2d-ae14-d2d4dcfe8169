/*
 * @Date: 2024-07-17 14:31:31
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-11 15:44:37
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\TestProcessData\SampleHandlingRecord\index.tsx
 * @Description: 样本处置记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSampleHandlingRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const SampleHandlingRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '处理样品数量',
      dataIndex: 'disposeSampleNumber',
      hideInSearch: true,
    },
    {
      title: '处理原因',
      dataIndex: 'disposeReason',
    },
    {
      title: '处理日期',
      dataIndex: 'disposeDate',
      hideInSearch: true,
    },
    {
      title: '处理日期',
      key: 'disposeDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '处理方式',
      dataIndex: 'disposeWay',
    },
    {
      title: '处理人',
      dataIndex: 'disposePeople',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.applyDate && _params.applyDate.length) {
            _params.applyStartDate = _params.applyDate[0];
            _params.applyEndDate = _params.applyDate[1];
            delete _params.applyDate;
          }
          if (params.disposeDate && _params.disposeDate.length) {
            _params.disposeStartDate = _params.disposeDate[0];
            _params.disposeEndDate = _params.disposeDate[1];
            delete _params.disposeDate;
          }
          const { code, data, msg } = await getSampleHandlingRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SampleHandlingRecord;
