import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { getSampleHandlingRecordDetailsById } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getSampleHandlingRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <BlockContainer title="样品基本信息">
          <Descriptions>
            <Descriptions.Item label="样品名称">
              {rowDetails?.sampleName}
            </Descriptions.Item>
            <Descriptions.Item label="样品编号">
              {rowDetails?.sampleTaskNumber}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="处置申请信息">
          <Descriptions>
            <Descriptions.Item label="申请人">
              {rowDetails?.applyPeople}
            </Descriptions.Item>
            <Descriptions.Item label="申请日期">
              {rowDetails?.applyDate}
            </Descriptions.Item>
            <Descriptions.Item label="处理样品数量">
              {rowDetails?.disposeSampleNumber}
            </Descriptions.Item>
            <Descriptions.Item label="存储期限">
              {rowDetails?.storageDeadline}
            </Descriptions.Item>
            <Descriptions.Item label="处理原因">
              {rowDetails?.disposeReason}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="处置信息">
          <Descriptions>
            <Descriptions.Item label="处理日期">
              {rowDetails?.disposeDate}
            </Descriptions.Item>
            <Descriptions.Item label="处理方式">
              {rowDetails?.disposeWay}
            </Descriptions.Item>
            <Descriptions.Item label="处理人">
              {rowDetails?.disposePeople}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
