/*
 * @Date: 2024-11-04 10:29:53
 * @LastEditors: Liu<PERSON>hen
 * @LastEditTime: 2024-12-05 15:01:01
 * @FilePath: /xr-qc-jk-web/src/pages/DataPatrol/TestProcessData/TestProcessRecord/index.tsx
 * @Description: 检测过程记录
 */
import { useRef, useState } from 'react';
import { Button, message, Modal } from 'antd';
import { getTestProcessRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import CheckItem from './components/CheckItem';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const TestProcessRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');
  // 当前查看的检测项目
  const [checkItemOpen, setCheckItemOpen] = useState(false);
  const [curCheckItem, setCurCheckItem] = useState<Record<string, any>[]>([]);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '检测项目',
      dataIndex: 'inspectionItem',
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => {
            setCurCheckItem(record.insideRecords);
            setCheckItemOpen(true);
          }}
        >
          查看
        </Button>
      ),
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.resultSubmitTime && _params.resultSubmitTime.length) {
            _params.resultSubmitStartTime = _params.resultSubmitTime[0];
            _params.resultSubmitEndTime = _params.resultSubmitTime[1];
            delete _params.resultSubmitTime;
          }
          const { code, data, msg } = await getTestProcessRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
      {/* 检测项目列表 */}
      <Modal
        title="检测项目"
        width="80%"
        open={checkItemOpen}
        onCancel={() => {
          setCheckItemOpen(false);
          setCurCheckItem([]);
        }}
        footer={null}
        destroyOnClose
      >
        <CheckItem dataSource={curCheckItem} />
      </Modal>
    </PageContainer>
  );
};

export default TestProcessRecord;
