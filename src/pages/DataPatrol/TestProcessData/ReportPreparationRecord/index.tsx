/*
 * @Date: 2024-07-17 11:18:47
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-11 15:43:18
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\TestProcessData\ReportPreparationRecord\index.tsx
 * @Description: 报告编制记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getReportPreparationRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const ReportPreparationRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '报告编号',
      dataIndex: 'reportCode',
    },
    {
      title: '编制时间',
      dataIndex: 'preparationDate',
      hideInSearch: true,
    },
    {
      title: '编制时间',
      key: 'preparationDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '报告审核人',
      dataIndex: 'reportPreparationPeople',
      hideInSearch: true,
    },
    {
      title: '报告批准人',
      dataIndex: 'reportAllowPeople',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.preparationDate && _params.preparationDate.length) {
            _params.preparationStartDate = _params.preparationDate[0];
            _params.preparationEndDate = _params.preparationDate[1];
            delete _params.preparationDate;
          }
          const { code, data, msg } = await getReportPreparationRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default ReportPreparationRecord;
