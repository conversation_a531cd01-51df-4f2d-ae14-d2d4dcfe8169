/*
 * @Date: 2024-07-17 10:47:27
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-11 15:23:32
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\TestProcessData\SampleCollectionRecord\index.tsx
 * @Description: 检测过程数据 - 样本采集记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSampleCollectionRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';
import DownloadButton from '@/components/DownloadButton';

const SampleCollectionRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '样品数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      key: 'sampleDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '采样单位',
      dataIndex: 'sampleUnit',
    },
    {
      title: '采样人员',
      dataIndex: 'samplePeople',
      hideInSearch: true,
    },

    {
      title: '采集方式',
      dataIndex: 'collectPattern',
      hideInSearch: true,
    },

    {
      title: '受检单位',
      dataIndex: 'examinedUnit',
    },
    {
      title: '检测类型',
      dataIndex: 'detectionType',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleStartDate = _params.sampleDate[0];
            _params.sampleEndDate = _params.sampleDate[1];
            delete _params.sampleDate;
          }
          setSearchParams(_params);
          const { code, data, msg } = await getSampleCollectionRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"

          toolBarRender={() => [
          <DownloadButton
            url="/data/report/process/sampleCollectionRecord/exportCollectRecord"
            params={searchParams}
            method="post"
          >
            导出
          </DownloadButton>,
          ]}

      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SampleCollectionRecord;
