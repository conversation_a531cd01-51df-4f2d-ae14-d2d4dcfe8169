import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { getSampleCollectionRecordDetailsById } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getSampleCollectionRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <BlockContainer title="样品基本信息">
          <Descriptions>
            <Descriptions.Item label="样品名称">
              {rowDetails?.sampleName}
            </Descriptions.Item>
            <Descriptions.Item label="样品编号">
              {rowDetails?.sampleTaskNumber}
            </Descriptions.Item>
            <Descriptions.Item label="样品数量">
              {rowDetails?.sampleCount}
            </Descriptions.Item>
            <Descriptions.Item label="样品包装">
              {rowDetails?.samplePack}
            </Descriptions.Item>
            <Descriptions.Item label="标本类型">
              {rowDetails?.sampleType}
            </Descriptions.Item>
            <Descriptions.Item label="生产日期">
              {rowDetails?.producedDate}
            </Descriptions.Item>
            <Descriptions.Item label="批号">
              {rowDetails?.batchNumber}
            </Descriptions.Item>
            <Descriptions.Item label="规格型号">
              {rowDetails?.specificationModel}
            </Descriptions.Item>
            <Descriptions.Item label="物态颜色">
              {rowDetails?.sampleColour}
            </Descriptions.Item>
            <Descriptions.Item label="保质期">
              {rowDetails?.expirationDate}
            </Descriptions.Item>
            <Descriptions.Item label="样品状态">
              {rowDetails?.sampleStatus}
            </Descriptions.Item>
            <Descriptions.Item label="执行标准">
              {rowDetails?.executiveStandard}
            </Descriptions.Item>
            <Descriptions.Item label="储存条件">
              {rowDetails?.storageCondition}
            </Descriptions.Item>
            <Descriptions.Item label="采样日期">
              {rowDetails?.sampleDate}
            </Descriptions.Item>

     

          </Descriptions>
        </BlockContainer>

        <BlockContainer title="采样单位信息">
          <Descriptions>
            <Descriptions.Item label="采样单位">
              {rowDetails?.sampleUnit}
            </Descriptions.Item>
            <Descriptions.Item label="采样人">
              {rowDetails?.samplePeople}
            </Descriptions.Item>
       <Descriptions.Item label="采集方式">
              {rowDetails?.collectPattern}
            </Descriptions.Item>


            <Descriptions.Item label="联系电话">
              {rowDetails?.samplePhone}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="受检单位信息">
          <Descriptions>
            <Descriptions.Item label="受检单位">
              {rowDetails?.examinedUnit}
            </Descriptions.Item>
            <Descriptions.Item label="联系人">
              {rowDetails?.examinedPeople}
            </Descriptions.Item>
            <Descriptions.Item label="联系电话">
              {rowDetails?.examinedPhone}
            </Descriptions.Item>
            <Descriptions.Item label="单位地址">
              {rowDetails?.examinedUnitAddr}
            </Descriptions.Item>
            <Descriptions.Item label="检测类型">
              {rowDetails?.detectionType}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
