/*
 * @Date: 2024-07-17 11:00:15
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-05 10:01:54
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\TestProcessData\SampleAcquisitionRecord\index.tsx
 * @Description: 领样记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSampleRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const SampleAcquisitionRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '检验分组',
      dataIndex: 'checkGroup',
    },
    {
      title: '领样人',
      dataIndex: 'sampleGetPeople',
      hideInSearch: true,
    },
    {
      title: '领样日期',
      dataIndex: 'sampleGetDate',
      hideInSearch: true,
    },
    {
      title: '领样日期',
      dataIndex: 'sampleGetDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '领样数量',
      dataIndex: 'sampleGetNumber',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.sampleGetDate && _params.sampleGetDate.length) {
            _params.sampleGetStartDate = _params.sampleGetDate[0];
            _params.sampleGetEndDate = _params.sampleGetDate[1];
            delete _params.sampleGetDate;
          }
          const { code, data, msg } = await getSampleRecordDataList(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SampleAcquisitionRecord;
