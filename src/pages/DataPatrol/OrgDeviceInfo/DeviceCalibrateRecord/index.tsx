/*
 * @Date: 2024-07-25 18:22:28
 * @LastEditors: Liu<PERSON>hen
 * @LastEditTime: 2025-01-10 16:18:11
 * @FilePath: /xr-qc-jk-web/src/pages/DataPatrol/OrgDeviceInfo/DeviceCalibrateRecord/index.tsx
 * @Description: 设备检定校准记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { deviceCalibrateRecordListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const DeviceCalibrateRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '检定证书编号',
      dataIndex: 'certNo',
    },
    {
      title: '检定时间',
      dataIndex: 'judgeDate',
      valueType: 'dateRange',
      render: (_, record) => {
        return record.judgeDate;
      },
    },

    {
      title: '检定结果',
      dataIndex: 'judgeResult',
    },
    {
      title: '检定/校准单位',
      dataIndex: 'unit',
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            ...params,
          };
          delete _params.current;
          if (_params.judgeDate && _params.judgeDate.length) {
            _params.judgeStartTime = _params.judgeDate[0];
            _params.judgeEndTime = _params.judgeDate[1];
            delete _params.judgeDate;
          }

          const { code, data, msg } = await deviceCalibrateRecordListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default DeviceCalibrateRecord;
