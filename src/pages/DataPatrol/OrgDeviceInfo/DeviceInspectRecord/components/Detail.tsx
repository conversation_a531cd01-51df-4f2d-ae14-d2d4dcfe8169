import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import {
  deviceInspectRecordDetailApi,
} from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await deviceInspectRecordDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          {/* <Descriptions.Item label="核查计划名称">
            {rowDetails?.planName}
          </Descriptions.Item>
          <Descriptions.Item label="核查单位">
            {rowDetails?.checkDept}
          </Descriptions.Item>
          <Descriptions.Item label="核查申请编号">
            {rowDetails?.applyNo}
          </Descriptions.Item> */}
          <Descriptions.Item label="仪器名称">
            {rowDetails?.equName}
          </Descriptions.Item>
          <Descriptions.Item label="仪器财政编号">
            {rowDetails?.equCode}
          </Descriptions.Item>
          <Descriptions.Item label="核查结论">
            {rowDetails?.result}
          </Descriptions.Item>
          <Descriptions.Item label="核查人">
            {rowDetails?.checkMan}
          </Descriptions.Item>
          {/* <Descriptions.Item label="核查周期（月）">
            {rowDetails?.period}
          </Descriptions.Item> */}
          <Descriptions.Item label="核查日期">
            {rowDetails?.checkDate}
          </Descriptions.Item>
          {/* <Descriptions.Item label="备注">
            {rowDetails?.remark}
          </Descriptions.Item> */}
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
