/*
 * @Date: 2024-07-25 18:25:09
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:29:53
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\OrgDeviceInfo\DeviceInspectRecord\index.tsx
 * @Description: 设备核查记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { deviceInspectRecordListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

const DeviceInspectRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '仪器名称',
      dataIndex: 'equName',
    },
    {
      title: '仪器财政编号',
      dataIndex: 'equCode',
    },
    {
      title: '核查结论',
      dataIndex: 'result',
    },
    {
      title: '核查日期',
      dataIndex: 'checkDate',
      hideInSearch: true,
    },
    {
      title: '核查日期',
      dataIndex: 'checkDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            ...params,
          };
          delete _params.current;
          if (_params.checkDate && _params.checkDate.length) {
            _params.checkStartTime = _params.checkDate[0];
            _params.checkEndTime = _params.checkDate[1];
            delete _params.checkDate;
          }
          const { code, data, msg } = await deviceInspectRecordListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default DeviceInspectRecord;
