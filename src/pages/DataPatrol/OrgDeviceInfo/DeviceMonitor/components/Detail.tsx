import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { deviceMonitorDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await deviceMonitorDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="所属区域">
            {rowDetails?.equArea}
          </Descriptions.Item>
          <Descriptions.Item label="设备名称">
            {rowDetails?.equName}
          </Descriptions.Item>
          <Descriptions.Item label="设备编号">
            {rowDetails?.equCode}
          </Descriptions.Item>
          <Descriptions.Item label="温度数值">
            {rowDetails?.temperature}
          </Descriptions.Item>
          <Descriptions.Item label="湿度数值">
            {rowDetails?.humidity}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {rowDetails?.freshDate}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
