/*
 * @Date: 2024-07-29 13:10:29
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 13:08:27
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\OrgDeviceInfo\DeviceMonitor\index.tsx
 * @Description: 温湿度监控
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { deviceMonitorListApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';
import Edit from './components/Edit';

const DeviceMonitor: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState('add');

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '设备名称',
      dataIndex: 'equName',
    },
    {
      title: '设备编号',
      dataIndex: 'equCode',
    },
    {
      title: '温度数值',
      dataIndex: 'temperature',
      hideInSearch: true,
    },
    {
      title: '湿度数值',
      dataIndex: 'humidity',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'freshDate',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'freshDate',
      hideInTable: true,
      valueType: 'dateRange'
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        // <Button
        //   type="link"
        //   size="small"
        //   onClick={() => {
        //     setCurSelectedRowId(record?.id);
        //     setEditType('edit');
        //     setEditOpen(true);
        //   }}
        // >
        //   编辑
        // </Button>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          let _params:any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            equName: params.equName,
            equCode: params.equCode,
            ...params,
          };
          if (params.freshDate && params.freshDate.length > 0) {
            _params.freshDateStart = params.freshDate[0];
            _params.freshDateEnd = params.freshDate[1];
          }
          delete _params.freshDate;
          const { code, data, msg } = await deviceMonitorListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        // toolBarRender={() => [
        //   <Button
        //     key="button"
        //     type="primary"
        //     onClick={() => {
        //       setEditType('add');
        //       setEditOpen(true);
        //     }}
        //   >
        //     新建
        //   </Button>,
        // ]}
        // toolBarRender={() => [
        //   <ListImport
        //     children={
        //       <Button type="primary" onClick={() => setListImportOpen(true)}>
        //         导入
        //       </Button>
        //     }
        //     open={listImportOpen}
        //     setOpen={(val) => {
        //       tableReload();
        //       setListImportOpen(val);
        //     }}
        //     downLoadUrl={'/data/report/process/reportPreparation/downloadModel'}
        //     importUrl={import.meta.env.VITE_URL + '/data/report/equip/checks/import'}
        //   ></ListImport>,
        // ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
      {/* 新增、编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
          setCurSelectedRowId('');
        }}
        type={editType}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default DeviceMonitor;
