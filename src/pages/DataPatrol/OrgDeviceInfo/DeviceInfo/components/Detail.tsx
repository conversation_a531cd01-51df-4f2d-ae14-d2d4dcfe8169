import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { deviceInfoDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await deviceInfoDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="仪器名称">
            {rowDetails?.equName}
          </Descriptions.Item>
          <Descriptions.Item label="仪器财政编号">
            {rowDetails?.codeFinance}
          </Descriptions.Item>
          <Descriptions.Item label="规格型号">
            {rowDetails?.specs}
          </Descriptions.Item>
          {/* <Descriptions.Item label="出厂编号">
            {rowDetails?.productNo}
          </Descriptions.Item>
          <Descriptions.Item label="供应商">
            {rowDetails?.supply}
          </Descriptions.Item> */}
          <Descriptions.Item label="生产厂家">
            {rowDetails?.producer}
          </Descriptions.Item>
          <Descriptions.Item label="购买日期">
            {rowDetails?.purchaseDate}
          </Descriptions.Item>
          <Descriptions.Item label="启用日期">
            {rowDetails?.enableDate}
          </Descriptions.Item>
          {/* <Descriptions.Item label="采购费用">{rowDetails?.fee}</Descriptions.Item> */}
          <Descriptions.Item label="仪器状态">
            {rowDetails?.status}
          </Descriptions.Item>
          <Descriptions.Item label="备注">
            {rowDetails?.remark}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
