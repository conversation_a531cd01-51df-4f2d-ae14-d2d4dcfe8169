/*
 * @Date: 2024-08-01 13:42:55
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-28 13:51:55
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\ExceptionData\DataStatistics\index.tsx
 * @Description: 异常数据统计
 */
import { useState } from 'react';
import { Radio, RadioChangeEvent } from 'antd';
import ExceptionTable from './components/ExceptionTable';
import OrgTable from './components/OrgTable';
import PageContainer from '@/components/PageContainer';

const DataStatistics: React.FC = () => {
  const [mode, setMode] = useState('exception');
  const handleModeChange = (e: RadioChangeEvent) => {
    setMode(e.target.value);
  };
  return (
    <PageContainer>
      <header className=" p-4 bg-white">
        <Radio.Group
          onChange={handleModeChange}
          value={mode}
          style={{ marginBottom: 8 }}
        >
          <Radio.Button value="exception">异常类型统计</Radio.Button>
          <Radio.Button value="org">机构统计</Radio.Button>
        </Radio.Group>
      </header>
      <section>
        {mode === 'exception' ? <ExceptionTable mode={mode} /> : <OrgTable mode={mode} />}
      </section>
    </PageContainer>
  );
};

export default DataStatistics;
