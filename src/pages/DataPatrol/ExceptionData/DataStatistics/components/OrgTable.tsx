import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { orgStatisticsApi } from '@/api/DataReporting/exceptionData';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';

type TOrgTableProps = {
  mode: string;
};

const OrgTable: React.FC<TOrgTableProps> = ({mode}) => {
  const [pageSize, setPageSize] = useState(10);
  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '机构名称',
      dataIndex: 'orgName',
    },
    {
      title: '异常总数',
      dataIndex: 'abnormalNum',
      hideInSearch: true,
    },
    {
      title: '整改完成数',
      dataIndex: 'rectificationNum',
      hideInSearch: true,
    },
    {
      title: '整改完成率',
      dataIndex: 'rectificationRate',
      hideInSearch: true,
    },
  ];

  useEffect(() => {
    tableReload()
  }, [mode])

  return (
    <ProTable
      columns={columns}
      actionRef={actionRef}
      cardBordered
      bordered
      request={async (params, sort, filter) => {
        const _params = {
          ...params,
          pageNum: params.current,
          current: undefined,
        };
        const { code, data, msg } = await orgStatisticsApi(_params);
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
        }

        return {
          data: data.rows ?? [],
          total: data.total ?? 0,
          success: true,
        };
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
      }}
      rowKey="id"
      search={{
        defaultCollapsed: false,
        labelWidth: 80,
      }}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      pagination={{
        size: 'default',
        showSizeChanger: true,
        pageSize: pageSize,
        onShowSizeChange: (current, size) => {
          setPageSize(size);
        },
      }}
      dateFormatter="string"
    />
  );
};

export default OrgTable;
