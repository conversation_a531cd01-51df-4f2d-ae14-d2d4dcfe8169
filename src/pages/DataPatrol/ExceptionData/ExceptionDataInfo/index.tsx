/*
 * @Date: 2024-08-01 13:30:20
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 15:09:54
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\ExceptionData\ExceptionDataInfo\index.tsx
 * @Description: 异常数据信息
 */
import { useState } from 'react';
import { Button, message, Popover } from 'antd';
import { exceptionDataInfoListApi } from '@/api/DataReporting/exceptionData';
import { codeDefinition } from '@/constants';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';
import dayjs from 'dayjs';

const ExceptionDataInfo: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
      width: 100,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
      width: 100,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      key: 'sampleDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '异常问题数量',
      dataIndex: 'abnormalNum',
      hideInSearch: true,
      render: (_, record) => (
        <Popover
          content={
            <div>
              {record.abnormalList && record.abnormalList.length
                ? record.abnormalList.map(
                    (_item: { sampleCode: string; ruleDesc: string }) => (
                      <div key={_item.sampleCode}>
                        {_item.sampleCode}: {_item.ruleDesc}
                      </div>
                    )
                  )
                : null}
            </div>
          }
          title=""
          trigger="click"
        >
          <Button type="link">{record.abnormalNum}</Button>
        </Popover>
      ),
    },
    {
      title: '异常问题核实情况',
      hideInSearch: true,
      children: [
        {
          title: '已忽略数量',
          dataIndex: 'verifyNoNum',
          hideInSearch: true,
        },
        {
          title: '已核实数量',
          dataIndex: 'verifyNum',
          hideInSearch: true,
        },
      ],
    },
    {
      title: '异常问题整改情况',
      hideInSearch: true,
      children: [
        {
          title: '待整改数量',
          dataIndex: 'waitRectificationNum',
          hideInSearch: true,
        },
        {
          title: '已整改数量',
          dataIndex: 'rectificationNum',
          hideInSearch: true,
        },
      ],
    },
    {
      title: '异常问题审核情况',
      hideInSearch: true,
      children: [
        {
          title: '待审核数量',
          dataIndex: 'waitAuditNum',
          hideInSearch: true,
        },
        {
          title: '已审核数量',
          dataIndex: 'auditNum',
          hideInSearch: true,
        },
      ],
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   key: 'option',
    //   width: 80,
    //   render: (text, record, _, action) => [
    //     <Button type="link" size="small">
    //       禁用
    //     </Button>,
    //   ],
    // },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params:any = {
            ...params,
            pageNum: params.current,
            current: undefined,
          };
          if(_params.sampleDate && _params.sampleDate.length) {
            _params.sampleDateBegin = dayjs(_params.sampleDate[0]).format('YYYY-MM-DD')
            _params.sampleDateEnd = dayjs(_params.sampleDate[1]).format('YYYY-MM-DD')
            delete _params.sampleDate
          }
          const { code, data, msg } = await exceptionDataInfoListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />

      <div className=" leading-10 bg-white px-4">
        <ExclamationCircleOutlined className=" mr-4" />
        根据数据校验规则，运用大数据分析手段，对各疾控中心或第三方检测机构实验室提交的传染病病原检测过程数据进行分析，输出记录不完整、重复提交、上报不及时、时序异常、人员分配异常、多次检验异常等问题数据清单。
      </div>
    </PageContainer>
  );
};

export default ExceptionDataInfo;
