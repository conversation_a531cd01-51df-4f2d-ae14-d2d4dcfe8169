/*
 * @Date: 2024-08-01 13:31:05
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 15:30:19
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\ExceptionData\SampleTestData\index.tsx
 * @Description: 样品检测数据管理
 */
import { useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { sampleTestDataListApi } from '@/api/DataReporting/exceptionData';
import { codeDefinition } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BaseInfo from './components/BaseInfo';
import PageContainer from '@/components/PageContainer';

const SampleTestData: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [sampleCode, setSampleCode] = useState('');

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '采样单位',
      dataIndex: 'sampleUnit',
    },
    {
      title: '受检单位',
      dataIndex: 'examinedUnit',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setSampleCode(record.sampleTaskNumber);
            setDetailOpen(true);
          }}
        >
          查看详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            current: undefined,
          };
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleStartDate = dayjs(_params.sampleDate[0]).format(
              'YYYY-MM-DD'
            );
            _params.sampleEndDate = dayjs(_params.sampleDate[1]).format(
              'YYYY-MM-DD'
            );
            delete _params.sampleDate;
          }
          const { code, data, msg } = await sampleTestDataListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Drawer
        width="90%"
        title="详情"
        onClose={() => setDetailOpen(false)}
        open={detailOpen}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <div className=" flex flex-col w-full h-full ">
          <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
            <BaseInfo code={sampleCode} />
          </div>
        </div>
      </Drawer>
    </PageContainer>
  );
};

export default SampleTestData;
