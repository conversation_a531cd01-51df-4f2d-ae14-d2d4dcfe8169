import { useEffect, useState } from 'react';
import { Descriptions } from 'antd';
import {
  reportPreparationRecordDetail<PERSON>pi,
  sampleAcquisitionRecordDetailApi,
  sampleCollectionRecordDetailApi,
  sampleHandingRecordDetailApi,
  samplePreProcessingRecordDetailApi,
  sampleReceiptRecordDetailApi,
  testProcessRecordDetailApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';
import CheckItem from './CheckItem';

type TBaseInfoProps = {
  code: string;
};

const BaseInfo: React.FC<TBaseInfoProps> = ({ code }) => {
  const [list, setList] = useState<Record<string, any>[]>([]);
  const [detailInfo1, setDetailInfo1] = useState<Record<string, any>>({});
  const getDetail1 = (sampleCode: string) => {
    sampleCollectionRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo1(res.data);
      }
    });
  };
  const [detailInfo2, setDetailInfo2] = useState<Record<string, any>>({});
  const getDetail2 = (sampleCode: string) => {
    sampleReceiptRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo2(res.data);
      }
    });
  };
  const [detailInfo3, setDetailInfo3] = useState<Record<string, any>>({});
  const getDetail3 = (sampleCode: string) => {
    sampleAcquisitionRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo3(res.data);
      }
    });
  };
  const [detailInfo4, setDetailInfo4] = useState<Record<string, any>>({});
  const getDetail4 = (sampleCode: string) => {
    samplePreProcessingRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo4(res.data);
      }
    });
  };
  const getDetail5 = (sampleCode: string) => {
    testProcessRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setList(res.data);
      }
    });
  };
  const [detailInfo6, setDetailInfo6] = useState<Record<string, any>>({});
  const getDetail6 = (sampleCode: string) => {
    reportPreparationRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo6(res.data);
      }
    });
  };
  const [detailInfo7, setDetailInfo7] = useState<Record<string, any>>({});
  const getDetail7 = (sampleCode: string) => {
    sampleHandingRecordDetailApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo7(res.data);
      }
    });
  };

  useEffect(() => {
    if (code) {
      getDetail1(code);
      getDetail2(code);
      getDetail3(code);
      getDetail4(code);
      getDetail5(code);
      getDetail6(code);
      getDetail7(code);
    }
  }, [code]);
  return (
    <>
      <BlockContainer title="样品基本信息">
        <Descriptions column={4}>
          <Descriptions.Item label="样品名称">
            {detailInfo1?.sampleName}
          </Descriptions.Item>
          <Descriptions.Item label="样品编号">
            {detailInfo1?.sampleTaskNumber}
          </Descriptions.Item>
          <Descriptions.Item label="样品数量">
            {detailInfo1?.sampleCount}
          </Descriptions.Item>
          <Descriptions.Item label="样品包装">
            {detailInfo1?.samplePack}
          </Descriptions.Item>
          <Descriptions.Item label="样品类型">
            {detailInfo1?.sampleType}
          </Descriptions.Item>
          <Descriptions.Item label="生产日期">
            {detailInfo1?.producedDate}
          </Descriptions.Item>
          <Descriptions.Item label="批号">
            {detailInfo1?.batchNumber}
          </Descriptions.Item>
          <Descriptions.Item label="规格型号">
            {detailInfo1?.specificationModel}
          </Descriptions.Item>
          <Descriptions.Item label="物态颜色">
            {detailInfo1?.sampleColour}
          </Descriptions.Item>
          <Descriptions.Item label="保质期">
            {detailInfo1?.expirationDate}
          </Descriptions.Item>
          <Descriptions.Item label="样品状态">
            {detailInfo1?.sampleStatus}
          </Descriptions.Item>
          <Descriptions.Item label="执行标准">
            {detailInfo1?.executiveStandard}
          </Descriptions.Item>
          <Descriptions.Item label="储存条件">
            {detailInfo1?.storageCondition}
          </Descriptions.Item>
          <Descriptions.Item label="采样日期">
            {detailInfo1?.sampleDate}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="采样单位信息">
        <Descriptions column={4}>
          <Descriptions.Item label="采样单位">
            {detailInfo1?.sampleUnit}
          </Descriptions.Item>
          <Descriptions.Item label="采样人">
            {detailInfo1?.samplePeople}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {detailInfo1?.samplePhone}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="受检单位信息">
        <Descriptions column={4}>
          <Descriptions.Item label="受检单位">
            {detailInfo1?.examinedUnit}
          </Descriptions.Item>
          <Descriptions.Item label="联系人">
            {detailInfo1?.examinedPeople}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {detailInfo1?.examinedPhone}
          </Descriptions.Item>
          <Descriptions.Item label="单位地址">
            {detailInfo1?.examinedUnitAddr}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="接样信息">
        <Descriptions column={4}>
          <Descriptions.Item label="接收日期">
            {detailInfo2?.acceptDate}
          </Descriptions.Item>
          <Descriptions.Item label="接收场所">
            {detailInfo2?.acceptPlace}
          </Descriptions.Item>
          <Descriptions.Item label="接收位置">
            {detailInfo2?.acceptLocation}
          </Descriptions.Item>
          <Descriptions.Item label="接样人">
            {detailInfo2?.acceptPeople}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="领样信息">
        <Descriptions column={4}>
          <Descriptions.Item label="检验分组">
            {detailInfo3?.checkGroup}
          </Descriptions.Item>
          <Descriptions.Item label="领样人">
            {detailInfo3?.sampleGetPeople}
          </Descriptions.Item>
          <Descriptions.Item label="领样日期">
            {detailInfo3?.sampleGetDate}
          </Descriptions.Item>
          <Descriptions.Item label="领样数量">
            {detailInfo3?.sampleGetNumber}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="样品前期处理信息">
        <Descriptions column={4}>
          <Descriptions.Item label="检验分组">
            {detailInfo4?.checkGroup}
          </Descriptions.Item>
          <Descriptions.Item label="制样人">
            {detailInfo4?.makeSamplePeople}
          </Descriptions.Item>
          <Descriptions.Item label="制样日期">
            {detailInfo4?.makeSampleDate}
          </Descriptions.Item>
          <Descriptions.Item label="制样方式">
            {detailInfo4?.makeSampleWay}
          </Descriptions.Item>
          <Descriptions.Item label="制样后状态">
            {detailInfo4?.makeSampleStatus}
          </Descriptions.Item>
          <Descriptions.Item label="制样数量">
            {detailInfo4?.makeSampleNumber}
          </Descriptions.Item>
          <Descriptions.Item label="制样单位">
            {detailInfo4?.makeSampleUnit}
          </Descriptions.Item>
          <Descriptions.Item label="制样场所">
            {detailInfo4?.makeSamplePlace}
          </Descriptions.Item>
          <Descriptions.Item label="储存条件">
            {detailInfo4?.storageCondition}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="检测过程记录">
        <CheckItem dataSource={list} />
        {/* {[1, 2].map((_item) => (
          <div key={_item} className=" shadow-md mb-6">
            <Descriptions column={4} title={`子样品${_item}基本信息`}>
              <Descriptions.Item label="子样品编号">编号</Descriptions.Item>
              <Descriptions.Item label="子样品名称">名称</Descriptions.Item>
              <Descriptions.Item label="科室">科室</Descriptions.Item>
              <Descriptions.Item label="检测员">检测员</Descriptions.Item>
              <Descriptions.Item label="检测开始时间">时</Descriptions.Item>
              <Descriptions.Item label="检测结束时间">时</Descriptions.Item>
              <Descriptions.Item label="结果提交时间">时</Descriptions.Item>
              <Descriptions.Item label="校核人">校核人</Descriptions.Item>
              <Descriptions.Item label="校核日期">日期</Descriptions.Item>
              <Descriptions.Item label="复核人">复核人</Descriptions.Item>
              <Descriptions.Item label="复核日期">日期</Descriptions.Item>
            </Descriptions>
            <Descriptions column={1} title={`子样品${_item}检测项目信息`}>
              <ProTable
                search={false}
                options={false}
                columns={[
                  {
                    title: '检测项目',
                    dataIndex: 'inspectionItem',
                  },
                  {
                    title: '检验依据',
                    dataIndex: 'inspectionStandard',
                  },
                  {
                    title: '判定标准',
                    dataIndex: 'determiningStandard',
                  },
                  {
                    title: '检测说明',
                    dataIndex: 'checkLimit',
                  },
                  {
                    title: '最小限',
                    dataIndex: 'checkLimitUnit',
                  },
                  {
                    title: '最小限单位',
                    dataIndex: 'testResult',
                  },
                  {
                    title: '最大限',
                    dataIndex: 'checkLimitUnit',
                  },
                  {
                    title: '最大限单位',
                    dataIndex: 'testResult',
                  },
                  {
                    title: '备注（限值类别）',
                    dataIndex: 'testResultUnit',
                  },
                  {
                    title: '方法检出限',
                    dataIndex: 'checkLimitUnit',
                  },
                  {
                    title: '检出限单位',
                    dataIndex: 'testResult',
                  },
                  {
                    title: '检测结果',
                    dataIndex: 'checkLimitUnit',
                  },
                  {
                    title: '结果单位',
                    dataIndex: 'testResult',
                  },
                ]}
                dataSource={[]}
                cardBordered
                bordered
                rowKey="id"
                pagination={false}
                dateFormatter="string"
                scroll={{
                  x: 'max-content',
                }}
              />
            </Descriptions>
          </div>
        ))} */}
      </BlockContainer>
      <BlockContainer title="报告编制记录">
        <Descriptions column={4}>
          <Descriptions.Item label="报告编号">
            {detailInfo6?.reportCode}
          </Descriptions.Item>
          <Descriptions.Item label="编制时间">
            {detailInfo6?.preparationDate}
          </Descriptions.Item>
          <Descriptions.Item label="报告编制人">
            {detailInfo6?.reportPreparationPeople}
          </Descriptions.Item>
          <Descriptions.Item label="报告批准人">
            {detailInfo6?.reportAllowPeople}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="处置申请信息">
        <Descriptions column={4}>
          <Descriptions.Item label="申请人">
            {detailInfo7?.applyPeople}
          </Descriptions.Item>
          <Descriptions.Item label="申请日期">
            {detailInfo7?.applyDate}
          </Descriptions.Item>
          <Descriptions.Item label="处理样品数量">
            {detailInfo7?.disposeSampleNumber}
          </Descriptions.Item>
          <Descriptions.Item label="存储期限">
            {detailInfo7?.storageDeadline}
          </Descriptions.Item>
          <Descriptions.Item label="处理原因">
            {detailInfo7?.disposeReason}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
      <BlockContainer title="处置信息">
        <Descriptions column={4}>
          <Descriptions.Item label="处理日期">
            {detailInfo7?.disposeDate}
          </Descriptions.Item>
          <Descriptions.Item label="处理方式">
            {detailInfo7?.disposeWay}
          </Descriptions.Item>
          <Descriptions.Item label="处理人">
            {detailInfo7?.disposePeople}
          </Descriptions.Item>
        </Descriptions>
      </BlockContainer>
    </>
  );
};

export default BaseInfo;
