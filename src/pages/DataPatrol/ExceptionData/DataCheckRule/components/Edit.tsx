import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addDataCheckRuleApi,
  updateDataCheckRuleApi,
} from '@/api/DataReporting/exceptionData';
import { getDict } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  row: Record<string, any>;
  type: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const Edit: React.FC<TEditProps> = ({ open, setOpen, type, row }) => {
  useEffect(() => {
    if (open && type === 'edit') {
      formRef.current?.setFieldsValue(row);
    }
  }, [row, open, type]);

  const [btnLoading, setBtnLoading] = useState(false);

  const formRef = useRef<ProFormInstance>(null);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const handleSubmit = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      console.log(values);
      if (type === 'edit') {
        values.id = row.id;
      }
      const _saveFetch =
        type === 'add' ? addDataCheckRuleApi : updateDataCheckRuleApi;
      const { code, msg } = await _saveFetch(values);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };
  return (
    <Drawer
      width="40%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormText
              name="tableName"
              label="表名"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '表名必填' }]}
            />
            <ProFormText
              name="filedName"
              label="限制字段"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '限制字段必填' }]}
            />
            <ProFormText
              name="filedValue"
              label="限制字段注释"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '限制字段注释必填' }]}
            />
            <ProFormSelect
              name="abnormalTypeId"
              label="异常类型"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '异常类型必填' }]}
              request={async () => {
                const { code, data, msg } = await getDict('abnormal_type');
                if (code === codeDefinition.QUERY_SUCCESS) {
                  const _options =
                    data && data.length
                      ? data.map((item: any) => ({
                          label: item.dictLabel,
                          value: item.dictValue,
                        }))
                      : [];
                  return _options;
                } else {
                  message.error(msg);
                  return [];
                }
              }}
            />
            <ProFormText
              name="ruleCode"
              label="规则编号"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '规则编号必填' }]}
            />
            <ProFormText
              name="ruleDesc"
              label="规则描述"
              colProps={{ span: 24 }}
              rules={[{ required: true, message: '规则描述必填' }]}
            />
            <ProFormRadio.Group
              name="isUse"
              label="状态"
              colProps={{ span: 24 }}
              options={[
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 },
              ]}
              initialValue={1}
            />
          </ProForm>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
