import { useRef, useState } from 'react';
import { <PERSON><PERSON>, Drawer } from 'antd';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';

type TSettingsProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const Settings: React.FC<TSettingsProps> = ({ open, setOpen }) => {
  const [btnLoading, setBtnLoading] = useState(false);

  const formRef = useRef<ProFormInstance>(null);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await formRef.current?.validateFields();
      console.log(values);
    } catch (error) {}
  };
  return (
    <Drawer
      width="40%"
      title="配置"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormText name="code" label="规则编号" colProps={{ span: 24 }} />
            <ProFormText name="type" label="异常类型" colProps={{ span: 24 }} />
            <ProFormText name="word" label="限制字段" colProps={{ span: 24 }} />
            <ProFormText
              name="description"
              label="规则描述"
              colProps={{ span: 24 }}
            />
            <ProFormRadio.Group
              name="status"
              label="状态"
              colProps={{ span: 24 }}
              options={[
                { label: '启用', value: '1' },
                { label: '禁用', value: '0' },
              ]}
              fieldProps={{
                defaultValue: '1',
              }}
            />
          </ProForm>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Settings;
