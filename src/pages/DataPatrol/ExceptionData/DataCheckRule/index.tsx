/*
 * @Date: 2024-08-01 13:28:22
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 10:07:41
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\ExceptionData\DataCheckRule\index.tsx
 * @Description: 异常数据管理 - 数据校验规则
 */
import { useRef, useState } from 'react';
import { But<PERSON>, Drawer, message, Popconfirm } from 'antd';
import {
  dataCheckRuleDisableApi,
  dataCheckRuleEnableApi,
  dataCheckRuleListApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Edit from './components/Edit';
import Settings from './components/Settings';
import PageContainer from '@/components/PageContainer';
import { getDict } from '@/api/dict';

const DataCheckRule: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  // 配置
  const [settingsOpen, setSettingsOpen] = useState(false);
  // 新增/编辑
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState('');
  const [editRow, setEditRow] = useState<Record<string, any>>({});

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 启用禁用
  const handleUpdateStatus = async (status: number, ids: string) => {
    try {
      const _handleFetch =
        status === 1 ? dataCheckRuleDisableApi : dataCheckRuleEnableApi;
      const { code, msg } = await _handleFetch(ids);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '规则编号',
      dataIndex: 'ruleCode',
    },
    {
      title: '异常类型',
      dataIndex: 'abnormalType',
      valueType: 'select',
      request: async () => {
        const {code,data, msg} = await getDict('abnormal_type')
        if (code === codeDefinition.QUERY_SUCCESS) {
          const _options =
            data && data.length
              ? data.map((item: any) => ({
                  label: item.dictLabel,
                  value: item.dictValue,
                }))
              : [];
          return _options;
        } else {
          message.error(msg);
          return [];
        }
      }
    },
    {
      title: '限制字段',
      dataIndex: 'filedName',
    },
    {
      title: '规则描述',
      dataIndex: 'ruleDesc',
    },
    {
      title: '更新日期',
      dataIndex: 'updateDate',
      hideInSearch: true,
    },
    {
      title: '更新日期',
      dataIndex: 'updateDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '状态',
      dataIndex: 'isUse',
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => [
        <Popconfirm
          title={record.isUse === 1 ? '禁用' : '启用'}
          description={
            record.isUse === 1 ? '确认禁用该规则?' : '确认启用该规则?'
          }
          onConfirm={() => handleUpdateStatus(record.isUse, record.id + '')}
          key="del"
        >
          <Button type="link" size="small">
            {record.isUse === 1 ? '禁用' : '启用'}
          </Button>
        </Popconfirm>,
        <Button
          type="link"
          size="small"
          key="update"
          onClick={() => {
            setEditType('edit');
            setEditRow(record);
            setEditOpen(true);
          }}
        >
          编辑
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
          };
          delete _params.current;
          if (_params.updateDate && _params.updateDate.length) {
            _params.updateDateBegin = dayjs(_params.updateDate[0]).format(
              'YYYY-MM-DD'
            );
            _params.updateDateEnd = dayjs(_params.updateDate[1]).format(
              'YYYY-MM-DD'
            );
          }
          const { code, data, msg } = await dataCheckRuleListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            type="primary"
            onClick={() => {
              setEditType('add');
              setEditRow({});
              setEditOpen(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
        }}
        row={editRow}
        type={editType}
      />
      {/* 配置 */}
      <Settings
        open={settingsOpen}
        setOpen={(val) => {
          tableReload();
          setSettingsOpen(val);
        }}
      />
    </PageContainer>
  );
};

export default DataCheckRule;
