import { useEffect, useState } from 'react';
import { Button, Drawer, Input, message } from 'antd';
import {
  exceptionDataListApi,
  saveExceptionVerifyApi,
  submitExceptionVerifyApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import BaseInfo from '../../SampleTestData/components/BaseInfo';
import BlockContainer from '@/components/BlockContainer';

type TVerificationProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  code: string;
};
const Verification: React.FC<TVerificationProps> = ({
  open,
  setOpen,
  code,
}) => {
  const [btnLoading, setBtnLoading] = useState(false);

  const close = () => {
    setDataSource([]);
    setOpen(false);
  };

  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);

  const columns: ProColumns[] = [
    {
      title: '数据类名',
      dataIndex: 'tableName',
    },
    {
      title: '数据名称',
      dataIndex: 'filedName',
    },
    {
      title: '错误描述',
      dataIndex: 'ruleDesc',
    },
    {
      title: '修改建议',
      dataIndex: 'verifySuggestion',
      render: (_, record) =>
        record.verifyResult === 1 ? (
          <Input
            value={record.verifySuggestion}
            placeholder="请输入"
            onChange={(e: any) => {
              setDataSource(
                dataSource.map((item) => ({
                  ...item,
                  verifySuggestion:
                    item.id === record.id
                      ? e.target.value
                      : item.verifySuggestion,
                }))
              );
            }}
          />
        ) : (
          record.verifySuggestion
        ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Button
          key="edit"
          type="link"
          onClick={() => {
            const _verifyResult = record.verifyResult ? 0 : 1;
            setDataSource(
              dataSource.map((item) => ({
                ...item,
                verifyResult:
                  record.id === item.id ? _verifyResult : item.verifyResult,
                verifySuggestion:
                  _verifyResult === 0 ? '' : item.verifySuggestion,
              }))
            );
          }}
        >
          {record.verifyResult ? '忽略错误' : '取消忽略'}
        </Button>
      ),
    },
  ];

  const getList = (sampleCode: string) => {
    exceptionDataListApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        res.data.forEach((item: any) => {
          if(![0,1].includes(item.verifyResult)) {
            item.verifyResult = 1;
          }
        });
        setDataSource(res.data);
      } else {
        message.error(res.data);
      }
    });
  };

  useEffect(() => {
    if (open && code) {
      getList(code);
    }
  }, [open, code]);

  const handleSave = async (type: number) => {
    try {
      setBtnLoading(true);
      const _fetchApi =
        type === 1 ? saveExceptionVerifyApi : submitExceptionVerifyApi;
      const { code, msg } = await _fetchApi(dataSource);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title="核实"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full ">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          <BaseInfo code={code} />
          <BlockContainer title="核实建议">
            <ProTable
              columns={columns}
              rowKey="id"
              scroll={{
                x: 'max-content',
              }}
              dataSource={dataSource}
              search={false}
              options={false}
            />
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => {
              if (dataSource.length) {
                setDataSource(
                  dataSource.map((item) => ({
                    ...item,
                    verifyResult: 0,
                    verifySuggestion: '',
                  }))
                );
              }
            }}
          >
            忽略全部问题
          </Button>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(1)}
          >
            保存核实信息
          </Button>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(2)}
          >
            提交核实信息
          </Button>
          <Button type="default" onClick={close}>
            取消
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Verification;
