import { useEffect, useState } from 'react';
import { Drawer, message } from 'antd';
import { exceptionDataListApi } from '@/api/DataReporting/exceptionData';
import { codeDefinition } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import BaseInfo from '../../SampleTestData/components/BaseInfo';
import BlockContainer from '@/components/BlockContainer';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  code: string;
};
const Detail: React.FC<TDetailProps> = ({ open, setOpen, code }) => {
  const close = () => {
    setOpen(false);
  };

  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);

  const columns: ProColumns[] = [
    {
      title: '数据类名',
      dataIndex: 'tableName',
    },
    {
      title: '数据名称',
      dataIndex: 'filedName',
    },
    {
      title: '错误描述',
      dataIndex: 'ruleDesc',
    },
    {
      title: '修改建议',
      dataIndex: 'verifySuggestion',
    },
  ];

  const getList = (sampleCode: string) => {
    exceptionDataListApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(res.data);
      } else {
        message.error(res.data);
      }
    });
  };

  useEffect(() => {
    if (open && code) {
      getList(code);
    }
  }, [open, code]);

  return (
    <Drawer
      width="90%"
      title="核实"
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full ">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          <BaseInfo code={code} />
          <BlockContainer title="核实建议">
            <ProTable
              columns={columns}
              rowKey="id"
              scroll={{
                x: 'max-content',
              }}
              dataSource={dataSource}
              pagination={false}
            />
          </BlockContainer>
        </div>
      </div>
    </Drawer>
  );
};

export default Detail;
