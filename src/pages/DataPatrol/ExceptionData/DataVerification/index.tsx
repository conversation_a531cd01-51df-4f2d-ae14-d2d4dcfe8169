/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/*
 * @Date: 2024-08-01 13:31:55
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 18:12:20
 * @FilePath: \xr-qc-jk-web\src\pages\DataPatrol\ExceptionData\DataVerification\index.tsx
 * @Description: 异常数据核实
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import {
  exceptionVerifyIgnoreAllApi,
  exceptionVerifyListApi,
  exceptionVerifyPendingListApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Detail from './components/Detail';
import Verification from './components/Verification';
import PageContainer from '@/components/PageContainer';

const DataVerification: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [sampleCode, setSampleCode] = useState('');
  // 核实
  const [verificationOpen, setVerificationOpen] = useState(false);

  // 统计数量
  const [counts, setCounts] = useState([0, 0]);
  const [init, setInit] = useState(true);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '异常问题数量',
      dataIndex: 'abnormalNum',
      hideInSearch: true,
    },
  ];

  const handleIgnore = (code: string) => {
    exceptionVerifyIgnoreAllApi(code).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(res.msg);
      }
    });
  };

  const getColumns = useCallback(() => {
    let _columns: ProColumns[] = [];
    if (activeKey === '0') {
      _columns = [
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 80,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="verification"
              onClick={() => {
                setSampleCode(record.sampleCode);
                setVerificationOpen(true);
              }}
            >
              核实
            </Button>,

            <Popconfirm
              title="确定忽略全部？"
              onConfirm={() => handleIgnore(record.sampleCode)}
              okText="确定"
              cancelText="取消"
              key="ignore"
            >
              <Button type="link" size="small" key="all">
                忽略全部
              </Button>
            </Popconfirm>,
          ],
        },
      ];
    } else {
      _columns = [
        {
          title: '已核实数量',
          dataIndex: 'verifiedNum',
          hideInSearch: true,
        },
        {
          title: '已忽略数量',
          dataIndex: 'verifiedNoNum',
          hideInSearch: true,
        },
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 80,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setSampleCode(record.sampleCode);
                setDetailOpen(true);
              }}
            >
              详情
            </Button>,
          ],
        },
      ];
    }

    return [...columns, ..._columns];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: <span>待核实（{counts[0]}）</span>,
              },
              {
                key: '1',
                label: <span>已核实（{counts[1]}）</span>,
              },
            ],
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            current: undefined,
          };
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleDateBegin = dayjs(_params.sampleDate[0]).format(
              'YYYY-MM-DD'
            );
            _params.sampleDateEnd = dayjs(_params.sampleDate[1]).format(
              'YYYY-MM-DD'
            );
            delete _params.sampleDate;
          }
          const _fetchApi =
            activeKey === '0'
              ? exceptionVerifyPendingListApi
              : exceptionVerifyListApi;
          const { code, data, msg } = await _fetchApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
          if (init) {
            const res = await (activeKey === '1'
              ? exceptionVerifyPendingListApi
              : exceptionVerifyListApi)(_params);
            if (res.code === codeDefinition.QUERY_SUCCESS) {
              const _counts = [...counts];
              if (activeKey === '0') {
                _counts[0] = data.total || 0;
                _counts[1] = res.data.total || 0;
              } else {
                _counts[1] = data.total || 0;
                _counts[0] = res.data.total || 0;
              }
              setCounts(_counts);
            }
            setInit(false);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 核实 */}
      <Verification
        open={verificationOpen}
        setOpen={(val) => {
          setInit(true);
          tableReload();
          setVerificationOpen(val);
        }}
        code={sampleCode}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} code={sampleCode} />
    </PageContainer>
  );
};

export default DataVerification;
