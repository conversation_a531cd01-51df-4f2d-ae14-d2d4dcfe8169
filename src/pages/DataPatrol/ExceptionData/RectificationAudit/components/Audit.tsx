import { useEffect, useState } from 'react';
import { Button, Descriptions, Drawer, message, Radio } from 'antd';
import {
  exceptionDataListApi,
  saveExceptionAuditApi,
  submitExceptionAuditApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import BaseInfo from '../../SampleTestData/components/BaseInfo';
import BlockContainer from '@/components/BlockContainer';
import EditableProTable from '@/components/EditableProTable';

type TAuditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  code: string;
};
const Audit: React.FC<TAuditProps> = ({ open, setOpen, code }) => {
  const [btnLoading, setBtnLoading] = useState(false);

  const close = () => {
    setOpen(false);
  };

  const [dataSource, setDataSource] = useState<any[]>([]);

  const columns: ProColumns[] = [
    {
      title: '登记信息',
      dataIndex: 'tableName',
    },
    {
      title: '数据名称',
      dataIndex: 'filedName',
    },
    {
      title: '错误描述',
      dataIndex: 'ruleDesc',
    },
    {
      title: '修改建议',
      dataIndex: 'verifySuggestion',
    },
    {
      title: '登记类型',
      dataIndex: 'rectificationType',
    },
    {
      title: '登记信息',
      dataIndex: 'rectificationRecord',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (text, record, _, action) => (
        <Radio.Group
          options={
            record.rectificationType === 1
              ? [
                  { label: '认可申诉信息', value: 1 },
                  { label: '不认可申诉信息', value: 0 },
                ]
              : [
                  { label: '审核通过', value: 1 },
                  { label: '审核不通过', value: 0 },
                ]
          }
          onChange={(e) => {
            setDataSource(
              dataSource.map((item) => ({
                ...item,
                auditResult:
                  item.id === record.id ? e.target.value : item.auditResult,
              }))
            );
          }}
          value={record.auditResult}
        ></Radio.Group>
      ),
      fixed: 'right',
    },
  ];

  const getList = (sampleCode: string) => {
    exceptionDataListApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        res.data.forEach((item: any) => {
          if(![0,1].includes(item.verifyResult)) {
            item.auditResult = 1;
          }
        });
        setDataSource(res.data);
      } else {
        message.error(res.data);
      }
    });
  };

  useEffect(() => {
    if (open && code) {
      getList(code);
    }
  }, [open, code]);

  const handleSave = async (type: number) => {
    try {
      setBtnLoading(true);
      const _fetchApi =
        type === 1 ? saveExceptionAuditApi : submitExceptionAuditApi;
      const { code, msg } = await _fetchApi(dataSource);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full ">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          <BaseInfo code={code} />
          <BlockContainer title="整改登记">
            <ProTable
              columns={columns}
              rowKey="id"
              scroll={{
                x: 'max-content',
              }}
              dataSource={dataSource}
              search={false}
              options={false}
            />
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(1)}
          >
            保存登记整改信息
          </Button>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(2)}
          >
            提交登记整改信息
          </Button>
          <Button type="default" onClick={close}>
            取消
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Audit;
