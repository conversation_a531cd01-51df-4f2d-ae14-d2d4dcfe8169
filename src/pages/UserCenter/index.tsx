import { useEffect, useRef, useState } from 'react';
import { Avatar, Col, message, Row } from 'antd';
import { FormInstance } from 'antd/es/form';
import { ISaveEmployeeParam, updateUserInfo } from '@/api/personnel';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import { UserOutlined } from '@ant-design/icons';
import { ProForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { FormInitVal, SexOptions } from '@/pages/System/User/data';
import { TFormInfo } from '@/pages/System/User/type';

const UserCenter: React.FC<any> = () => {
  const { userInfo, setUserInfo } = useInfoStore();
  const [userInfos, setUserInfos] = useState<any>({});
  useEffect(() => {
    setUserInfos(userInfo.user);
    formRef.current?.setFieldsValue(userInfo.user);
  }, [userInfo]);
  // loading
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  /**
   * @TODO 保存详情
   */
  const handleSave = async (values: any) => {
    const publicParam = {
      ...values,
    };
    setSubmitLoading(true);
    try {
      const { code, msg } = await saveFetch(publicParam);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        setUserInfo({
          ...userInfo,
          user: { ...userInfo.user, ...publicParam },
        });
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setSubmitLoading(false);
    }
  };
  // 请求
  const saveFetch = async (params: ISaveEmployeeParam) => {
    params.userId = userInfos.userId;
    return updateUserInfo(params);
  };

  return (
    <div className="flex justify-center">
      <Row gutter={20} className="flex-1 justify-center">
        <Col span={10} className="max-w-[450px]">
          <div className=" bg-white rounded shadow p-4 h-[88vh]">
            <div className="text-lg">个人信息</div>
            <div className="text-center py-4">
              <Avatar icon={<UserOutlined />} size="large" />
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">登录账号：</div>
              <div>{userInfos.userName}</div>
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">手机号码：</div>
              <div>{userInfos.phonenumber}</div>
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">用户邮箱：</div>
              <div>{userInfos.email}</div>
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">所属部门：</div>
              <div>{userInfos.dept?.deptName}</div>
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">所属角色：</div>
              <div>
                {(userInfos.roles || [])
                  .map((item: any) => item.roleName)
                  .join(',')}
              </div>
            </div>
            <div className="flex py-2">
              <div className="w-[80px] flex-none">创建日期：</div>
              <div>{userInfos.createTime}</div>
            </div>
          </div>
        </Col>
        <Col span={14}>
          <div className=" bg-white rounded shadow p-4 h-[88vh]">
            <div className="text-lg">修改信息</div>
            <ProForm<TFormInfo>
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              initialValues={FormInitVal}
              onFinish={handleSave} 
              loading={submitLoading}
              onValuesChange={(_, values: any) => {
                for (const key in values) {
                  if (typeof values[key] === 'string') {
                    values[key] = values[key].trim();
                  }
                }
                formRef.current?.setFieldsValue(values);
              }}
            >
              <ProFormText
                width="md"
                name="nickName"
                label="用户姓名"
                placeholder="请输入"
                rules={[{ required: true, message: '请输入' }]}
                colProps={{
                  span: 12,
                }}
              />

              <ProFormText
                width="md"
                name="phonenumber"
                label="手机号码"
                placeholder="请输入"
                rules={[{ required: true, message: '请输入' }]}
                colProps={{
                  span: 12,
                }}
              />
              <ProFormText
                width="md"
                name="email"
                label="电子邮箱"
                placeholder="请输入"
                rules={[{ required: true, message: '请输入' }]}
                colProps={{
                  span: 12,
                }}
              />
              <ProFormRadio.Group
                width="md"
                name="sex"
                label="用户性别"
                options={SexOptions}
                rules={[{ required: true, message: '请选择' }]}
                colProps={{
                  span: 12,
                }}
              />
            </ProForm>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default UserCenter;
