import { Key, useCallback, useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import { addDictType, editDictType, queryDictType } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ProForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';

const FormInfoInit = {
  roleName: null,
  status: 0,
  menuIds: [],
  roleKey: null,
  roleType: '01',
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
  detailType: string;
  detailId: number;
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  detailType,
  detailId,
}) => {
  // 获取table中需要的枚举
  const {
    sysNormalDisableOnTable,
    sysNormalDisableOnForm,
    getSysNormalDisable,
  } = useDictStore();
  useEffect(() => {
    getSysNormalDisable();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // title
  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增配置';
    } else if (detailType === 'edit') {
      return '编辑配置';
    } else {
      return '配置详情';
    }
  }, [detailType]);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
  };

  /**
   * @TODO 获取角色详情
   */
  const getDetail = async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryDictType(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
      } else {
        formRef.current?.resetFields();
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    getDetail();
  }, [open]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await saveFetch(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (params: any) => {
    if (detailType === 'add') {
      return addDictType(params);
    } else {
      params.dictId = detailId;
      return editDictType(params);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title={detailTitleComputed()}
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        readonly={detailType === 'view'}
        onFinish={handleSave}
        labelCol={{
          span: 6,
        }}
        wrapperCol={{
          span: 18,
        }}
        grid={true}
        rowProps={{ gutter: [24, 0] }}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          label="字典名称"
          name="dictName"
          rules={[{ required: true, message: '请输入字典名称' }]}
          placeholder="请输入字典名称"
        />
        <ProFormText
          label="字典类型"
          name="dictType"
          rules={[{ required: true, message: '请输入字典类型' }]}
          placeholder="请输入字典类型"
        />
        <ProFormRadio.Group
          label="状态"
          name="status"
          options={sysNormalDisableOnForm}
          rules={[{ required: true, message: '请选择状态' }]}
        />
        <ProFormText label="备注" name="remark" placeholder="请输入备注" />
      </ProForm>
    </Modal>
  );
};

export default RoleDetail;
