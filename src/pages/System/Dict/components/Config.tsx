/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm, Space } from 'antd';
import { deleteDictDetailType, getDictDetailList } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useDictStore } from '@/store/dict';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import EProTable from '@/components/EProTable';
import ConfigDetail from './ConfigDetail';

type TConfigProps = {
  configType: any;
};

const ConfigDetailComponent: React.FC<TConfigProps> = ({ configType }) => {
  // 获取table中需要的枚举
  const {
    sysNormalDisableOnTable,
    sysNormalDisableOnForm,
    getSysNormalDisable,
  } = useDictStore();
  useEffect(() => {
    getSysNormalDisable();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  const [pageSize, setPageSize] = useState<number>(10);

  // 新增设置
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [detailType, setDetailType] = useState<string>('add');
  const [detailId, setDetailId] = useState<number>(-1);

  // 表头
  const columns: ProColumns<any>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '字典编码',
      dataIndex: 'dictCode',
      key: 'dictCode',
      hideInSearch: true,
    },
    {
      title: '字典标签',
      dataIndex: 'dictLabel',
      key: 'dictLabel',
    },
    {
      title: '字典键值',
      dataIndex: 'dictValue',
      key: 'dictValue',
      hideInSearch: true,
    },
    {
      title: '字典排序',
      dataIndex: 'dictSort',
      key: 'dictSort',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: sysNormalDisableOnTable,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (text, record, _, action) => [
        <a
          key="edit"
          onClick={() => {
            setDetailId(record.dictCode);
            setDetailType('edit');
            setOpenDetail(true);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.dictCode)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
      ],
      key: 'option',
    },
  ];

  /**
   * @TODO 删除设置
   */
  const handleDelete = async (id: number) => {
    try {
      const { code, msg } = await deleteDictDetailType(id + '');
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  return (
    <>
      <EProTable<any>
        className="spec-table"
        scroll={{
          x: true,
        }}
        loading={loading}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params: any = {}, sort, filter) => {
          setLoading(true);
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            dictType: configType,
          };
          delete param.current;
          const { code, rows, total, msg } = await getDictDetailList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          setLoading(false);
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
        }}
        rowKey="dictValue"
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于字典了 transform，提交的参与与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: pageSize,
          showSizeChanger: true,
          onShowSizeChange(current, size) {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => {
              setDetailType('add');
              setOpenDetail(true);
            }}
          >
            新增
          </Button>,
        ]}
        tableAlertRender={false}
        tableAlertOptionRender={false}
      />
      <ConfigDetail
        open={openDetail}
        closeDetail={() => {
          setOpenDetail(false);
          tableReload();
        }}
        detailType={detailType}
        detailId={detailId}
        dictType={configType}
      />
    </>
  );
};

export default ConfigDetailComponent;
