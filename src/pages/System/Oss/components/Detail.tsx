import { useCallback, useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import {
  addConfigDetail,
  editConfigDetail,
  queryConfigDetail,
} from '@/api/oss';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useConfigStore } from '@/store/config';
import { ProForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { TMenuList } from '../../Menu/type';

const FormInfoInit = {
  roleName: null,
  status: 0,
  menuIds: [],
  roleKey: null,
  roleType: '01',
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
  detailType: string;
  detailId: number;
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  detailType,
  detailId,
}) => {
  // 获取table中需要的枚举
  const { getSysYesNo, sysYesNoOnForm } = useConfigStore();
  useEffect(() => {
    getSysYesNo();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // title
  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增配置';
    } else if (detailType === 'edit') {
      return '编辑配置';
    } else {
      return '配置详情';
    }
  }, [detailType]);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
  };

  /**
   * @TODO
   * tree组件父子联动问题，回显时处理父节点是否展示
   */
  const handelEchoParentNode = (dataSource: TMenuList[], keys: number[]) => {
    let newKeys = [...keys];
    dataSource.forEach((_treeNode) => {
      if (_treeNode.children.length) {
        if (_treeNode.children.some((_i) => _i.children.length)) {
          // console.log(_treeNode.menuId);

          newKeys = handelEchoParentNode(_treeNode.children, newKeys);
        }

        if (
          !_treeNode.children.every((_i) => newKeys.includes(_i.menuId)) &&
          newKeys.includes(_treeNode.menuId)
        ) {
          newKeys = newKeys.filter((_i) => _i !== _treeNode.menuId);
        }
      } else {
      }
    });
    return newKeys;
  };

  /**
   * @TODO 获取角色详情
   */
  const getDetail = async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryConfigDetail(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.status = Number(data.status);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
      } else {
        formRef.current?.resetFields();
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    getDetail();
  }, [open]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await saveFetch(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (params: any) => {
    if (detailType === 'add') {
      return addConfigDetail(params);
    } else {
      params.ossConfigId = detailId;
      return editConfigDetail(params);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title={detailTitleComputed()}
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        readonly={detailType === 'view'}
        onFinish={handleSave}
        labelCol={{
          span: 6,
        }}
        wrapperCol={{
          span: 18,
        }}
        grid={true}
        rowProps={{ gutter: [24, 0] }}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          label="配置key"
          name="configKey"
          rules={[{ required: true, message: '请输入配置key' }]}
          placeholder="请输入配置key"
        />
        <ProFormText
          label="访问站点"
          name="endpoint"
          rules={[{ required: true, message: '请输入访问站点' }]}
          placeholder="请输入访问站点"
        />
        <ProFormText
          label="自定义域名"
          name="domain"
          placeholder="请输入自定义域名"
        />
        <ProFormText
          label="accessKey"
          name="accessKey"
          rules={[{ required: true, message: '请输入accessKey' }]}
          placeholder="请输入accessKey"
        />
        <ProFormText
          label="secretKey"
          name="secretKey"
          rules={[{ required: true, message: '请输入秘钥' }]}
          placeholder="请输入秘钥"
        />
        <ProFormText
          label="桶名称"
          name="bucketName"
          rules={[{ required: true, message: '请输入桶名称' }]}
          placeholder="请输入桶名称"
        />
        <ProFormText label="前缀" name="prefix" placeholder="请输入前缀" />
        <ProFormRadio.Group
          label="是否HTTPS"
          name="isHttps"
          options={sysYesNoOnForm}
        />
        <ProFormRadio.Group
          label="桶权限类型"
          name="accessPolicy"
          options={[
            { label: 'private', value: '0' },
            { label: 'public', value: '1' },
            { label: 'custom', value: '2' },
          ]}
          rules={[{ required: true, message: '请选择桶权限类型' }]}
        />
        <ProFormText label="域" name="region" placeholder="请输入域" />
        <ProFormText label="备注" name="remark" placeholder="请输入备注" />
      </ProForm>
    </Modal>
  );
};

export default RoleDetail;
