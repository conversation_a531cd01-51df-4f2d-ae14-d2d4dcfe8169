/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import {
  ISaveMenuParam,
  queryAddMenu,
  queryEditMenu,
  queryMenuDetail,
} from '@/api/menu';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  FormInstance,
  ProForm,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { FormInitVal, formItemLayout, MenuTypeEnum } from '../data';

type TMenuDetailProps = {
  close: () => void;
  detailType: string;
  detailId: number | undefined;
  detailParentId: number;
  isDirectory: boolean;
};

const MenuDetail: React.FC<TMenuDetailProps> = ({
  close,
  detailType,
  detailId,
  detailParentId,
  isDirectory,
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>(null);

  const [menuType, setMenuType] = useState<string>('M');

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = useCallback(async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryMenuDetail(detailId as number);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.status = Number(data.status);
          formRef.current?.setFieldsValue(data);
          setMenuType(data.menuType);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [detailType, detailId]);

  useEffect(() => {
    getDetailData();
  }, [getDetailData]);

  /**
   * @TODO 新增/编辑
   */
  const handleSava = async (values: any) => {
    const publicParam = {
      ...values,
      parentId: detailParentId,
    };
    if (detailType === 'edit') publicParam.menuId = detailId;

    // 如果是外链，则需要判断外链地址是否合法
    if (values.is_frame === 0) {
      if (!values.path) {
        message.error('请输入外链地址');
        return;
      }
    }

    setLoading(true);
    try {
      const { code, msg } = await saveFetch(publicParam);
      if (code === codeDefinition.QUERY_SUCCESS) {
        close();
        message.success(QUERY_SUCCESS_MSG);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (data: ISaveMenuParam) => {
    if (detailType === 'add') {
      return queryAddMenu(data);
    } else {
      return queryEditMenu(data);
    }
  };

  // 页脚
  const Footer = (
    <div className="flex flex-row flex-nowrap justify-center items-center">
      <Space size="large">
        <Button
          type="primary"
          loading={loading}
          htmlType="submit"
          disabled={detailType === 'view'}
        >
          保存
        </Button>
        <Button type="default" onClick={close}>
          关闭
        </Button>
      </Space>
    </div>
  );

  return (
    <>
      <ProForm
        formRef={formRef}
        {...formItemLayout}
        layout="horizontal"
        grid={true}
        rowProps={{
          gutter: [24, 0],
        }}
        onFinish={handleSava}
        submitter={{
          render: (_, dom) => Footer,
        }}
        initialValues={FormInitVal}
        readonly={detailType === 'view'}
        //@ts-ignore
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormRadio.Group
          width="md"
          colProps={{
            span: 24,
          }}
          name="menuType"
          label="菜单类型"
          options={MenuTypeEnum}
          fieldProps={{
            onChange: (e) => {
              setMenuType(e.target.value);
            },
          }}
        />
        <ProFormText
          width="md"
          name="menuName"
          label="菜单名称"
          placeholder="请输入"
          colProps={{
            span: 24,
          }}
          rules={[{ required: true, message: '请输入' }]}
        />
        <ProFormText
          width="md"
          name="icon"
          label="图标"
          placeholder="请输入"
          colProps={{
            span: 24,
          }}
        />
        <ProFormDigit
          width="md"
          name="orderNum"
          label="排序"
          placeholder="请输入"
          colProps={{
            span: 24,
          }}
          rules={[{ required: true, message: '请输入' }]}
        />
        <ProFormText
          width="md"
          name="perms"
          label="权限字符"
          placeholder="请输入"
          colProps={{
            span: 24,
          }}
        />
        {menuType === 'F' ? (
          <ProFormText
            width="md"
            name="buttonCode"
            label="按钮编码"
            placeholder="请输入"
            colProps={{
              span: 24,
            }}
          />
        ) : (
          <>
            <ProFormText
              width="md"
              name="path"
              label="路由地址"
              placeholder="请输入"
              colProps={{
                span: 24,
              }}
              rules={[{ required: true, message: '请输入' }]}
            />
            <ProFormRadio.Group
              width="md"
              colProps={{
                span: 24,
              }}
              name="status"
              label="菜单状态"
              options={[
                {
                  label: '正常',
                  value: 0,
                },
                {
                  label: '停用',
                  value: 1,
                },
              ]}
            />
          </>
        )}
        {menuType === 'C' ? (
          <>
            <ProFormRadio.Group
              width="md"
              colProps={{
                span: 24,
              }}
              name="is_frame"
              label="是否外链"
              options={[
                {
                  label: '是',
                  value: 0,
                },
                {
                  label: '否',
                  value: 1,
                },
              ]}
              initialValue={1}
            />
          </>
        ) : null}
      </ProForm>
    </>
  );
};

export default MenuDetail;
