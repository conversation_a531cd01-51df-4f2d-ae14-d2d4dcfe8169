/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useCallback, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Select } from 'antd';
import { queryDeleteMenu, queryMenuList } from '@/api/menu';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import MenuDetail from './components/MenuDetail';
import EProTable from '@/components/EProTable';
import PageContainer from '@/components/PageContainer';
import { handleTree } from '@/utils/index';
import { TMenuStatusEnum, TTableData } from './type';

const MenuManagement: React.FC = () => {
  // 绑定表格
  const actionRef = useRef<ActionType>();

  // 是否展开/折叠
  const [isExpansion, setIsExpansion] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<TTableData[]>([]);

  // 展开的行
  const [expandKeys, setExpandKeys] = useState<React.Key[]>([]);

  // 详情ID
  const [detailId, setDetailId] = useState<number>();

  // 详情类型
  const [detailType, setDetailType] = useState<string>('add');

  // 菜单详情
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增菜单';
    } else if (detailType === 'edit') {
      return '编辑菜单';
    } else {
      return '菜单详情';
    }
  }, [detailType]);

  const [detailParentId, setDetailParentId] = useState<number>(0);

  // 菜单状态
  const [menuStatusEnum] = useState<TMenuStatusEnum[]>([
    {
      dictLabel: '启用',
      dictSort: 0,
      dictValue: '0',
      isDefault: '',
      remark: '',
    },
    {
      dictLabel: '停用',
      dictSort: 1,
      dictValue: '1',
      isDefault: '',
      remark: '',
    },
  ]);

  //
  const [isDirectory, setIsDirectory] = useState<boolean>(false);

  /**
   * @TODO 删除菜单
   */
  const handleDelete = async (menuId: number) => {
    try {
      const { code, msg } = await queryDeleteMenu({ menuId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        tableReload();
        message.success(QUERY_SUCCESS_MSG);
      } else {
        message.error(msg);
      }
    } catch (error) {
      console.error('Delete menu error:', error);
      message.error('删除失败，请重试');
    }
  };

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 80,
      key: 'index',
      hideInTable: true,
    },
    {
      title: '菜单名称',
      dataIndex: 'menuName',
      key: 'menuName',
      width: 220,
    },
    {
      title: '图标',
      dataIndex: 'icon',
      ellipsis: true,
      hideInSearch: true,
      key: 'icon',
    },
    {
      title: '排序',
      dataIndex: 'orderNum',
      ellipsis: true,
      hideInSearch: true,
      key: 'orderNum',
    },
    {
      title: '组件路径',
      dataIndex: 'path',
      ellipsis: true,
      hideInSearch: true,
      key: 'path',
    },
    {
      title: '权限字符',
      dataIndex: 'perms',
      ellipsis: true,
      hideInSearch: true,
      key: 'perms',
    },
    {
      title: '状态',
      dataIndex: 'status',
      ellipsis: true,
      key: 'status',
      renderFormItem: () => {
        return (
          <Select
            options={menuStatusEnum}
            placeholder="请选择"
            allowClear
            fieldNames={{ label: 'dictLabel', value: 'dictValue' }}
          />
        );
      },
      renderText: (text, record) =>
        Number(record.status) === 0 ? '正常' : '停用',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
      hideInSearch: true,
      key: 'createTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => {
            setDetailType('view');
            setDetailId(record.menuId);
            setDetailParentId(record.parentId);
            setOpenDetail(true);
          }}
        >
          查看
        </a>,
        <a
          key="edit"
          onClick={() => {
            setDetailType('edit');
            setDetailId(record.menuId);
            setDetailParentId(record.parentId);
            setOpenDetail(true);
            // 编辑的是否为目录
            setIsDirectory(record.menuType === 'M');
          }}
        >
          编辑
        </a>,
        <a
          key="add"
          onClick={() => {
            setDetailType('add');
            setDetailParentId(record.menuId);
            setOpenDetail(true);
            // 新增菜单/按钮
            setIsDirectory(false);
          }}
          style={{ display: record.menuType === 'F' ? 'none' : 'block' }}
        >
          新增
        </a>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.menuId)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
      ],
      key: 'option',
    },
  ];

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeDetail = () => {
    setOpenDetail(false);
    tableReload();
  };

  return (
    <PageContainer>
      <div>
        <EProTable<TTableData>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params: any = {}, sort, filter) => {
            try {
              const param = {
                ...params,
              };
              delete param.current;
              const { code, data, msg } = await queryMenuList(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
                return {
                  data: [],
                  success: false,
                };
              }
              const d = handleTree(data, 'menuId');
              setDataSource(d);
              return {
                data: d ?? [],
                success: true,
              };
            } catch (error) {
              console.error('Menu data loading error:', error);
              return {
                data: [],
                success: false,
              };
            }
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="menuId"
          options={{
            setting: {
              // listsHeight: 400,
            },
          }}
          form={{
            // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
            //@ts-ignore
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                };
              }
              return values;
            },
          }}
          pagination={false}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="handle"
              type="primary"
              //@ts-ignores
              onClick={(e, val = !isExpansion) => {
                if (val) {
                  const keys: any = [];
                  function getKeys(data: any) {
                    data.forEach((item: any) => {
                      keys.push(item.menuId);
                      if (item.children) {
                        getKeys(item.children);
                      }
                    });
                  }
                  getKeys(dataSource);
                  setExpandKeys([...keys]);
                } else {
                  setExpandKeys([]);
                }
                setIsExpansion(val);
              }}
            >
              {isExpansion ? '折叠' : '展开'}
              所有
            </Button>,
            <Button
              key="button"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setDetailParentId(0);
                setOpenDetail(true);
                // 新增目录
                setIsDirectory(true);
              }}
            >
              新增菜单
            </Button>,
          ]}
          expandable={{
            indentSize: 32,
            expandedRowKeys: expandKeys,
            onExpandedRowsChange: (expandKeys) => {
              setExpandKeys([...expandKeys]);
            },
            childrenColumnName: 'children',
          }}
        />
      </div>
      {/* 详情 */}
      <Drawer
        width="600px"
        title={detailTitleComputed()}
        onClose={closeDetail}
        open={openDetail}
        destroyOnClose
      >
        <MenuDetail
          close={closeDetail}
          detailType={detailType}
          detailId={detailId}
          detailParentId={detailParentId}
          isDirectory={isDirectory}
        />
      </Drawer>
    </PageContainer>
  );
};

export default MenuManagement;
