/**
 * @type 菜单管理列表
 */
export type TTableData = {
  menuId: number;
  id: number;
  icon: string;
  orderNum: number;
  perms: string;
  path: string;
  status: number;
  createTime: string;
  children: TTableData[];
  sysMenuList: TTableData[];
  menuType: string;
  parentId: number;
};

/**
 * @type 菜单状态字典值类型
 */
export type TMenuStatusEnum = {
  dictLabel: string;
  dictSort: number;
  dictValue: string;
  isDefault: string;
  remark: string;
};

export type TMenuList = TTableData;
