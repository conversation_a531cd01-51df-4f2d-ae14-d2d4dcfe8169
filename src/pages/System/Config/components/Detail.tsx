import { Key, useCallback, useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import {
  addConfigDetail,
  editConfigDetail,
  queryConfigDetail,
} from '@/api/config';
import { queryMenuSelectList } from '@/api/menu';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useConfigStore } from '@/store/config';
import { ProForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { TMenuList } from '../../Menu/type';

const FormInfoInit = {
  roleName: null,
  status: 0,
  menuIds: [],
  roleKey: null,
  roleType: '01',
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
  detailType: string;
  detailId: number;
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  detailType,
  detailId,
}) => {
  // 获取table中需要的枚举
  const { getSysYesNo, sysYesNoOnForm } = useConfigStore();
  useEffect(() => {
    getSysYesNo();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // title
  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增配置';
    } else if (detailType === 'edit') {
      return '编辑配置';
    } else {
      return '配置详情';
    }
  }, [detailType]);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
  };

  /**
   * @TODO
   * tree组件父子联动问题，回显时处理父节点是否展示
   */
  const handelEchoParentNode = (dataSource: TMenuList[], keys: number[]) => {
    let newKeys = [...keys];
    dataSource.forEach((_treeNode) => {
      if (_treeNode.children.length) {
        if (_treeNode.children.some((_i) => _i.children.length)) {
          // console.log(_treeNode.menuId);

          newKeys = handelEchoParentNode(_treeNode.children, newKeys);
        }

        if (
          !_treeNode.children.every((_i) => newKeys.includes(_i.menuId)) &&
          newKeys.includes(_treeNode.menuId)
        ) {
          newKeys = newKeys.filter((_i) => _i !== _treeNode.menuId);
        }
      } else {
      }
    });
    return newKeys;
  };

  /**
   * @TODO 获取角色详情
   */
  const getDetail = async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryConfigDetail(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.status = Number(data.status);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
      } else {
        formRef.current?.resetFields();
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    getDetail();
  }, [open]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values };
    setLoading(true);
    try {
      const { code, msg } = await saveFetch(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (params: any) => {
    if (detailType === 'add') {
      return addConfigDetail(params);
    } else {
      params.configId = detailId;
      return editConfigDetail(params);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title={detailTitleComputed()}
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        readonly={detailType === 'view'}
        onFinish={handleSave}
        labelCol={{
          span: 6,
        }}
        wrapperCol={{
          span: 18,
        }}
        grid={true}
        rowProps={{ gutter: [24, 0] }}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          label="参数名称"
          name="configName"
          rules={[{ required: true, message: '请输入参数名称' }]}
          placeholder="请输入参数名称"
        />
        <ProFormText
          label="参数键名"
          name="configKey"
          rules={[{ required: true, message: '请输入参数键名' }]}
          placeholder="请输入参数键名"
        />
        <ProFormText
          label="参数键值"
          name="configValue"
          rules={[{ required: true, message: '请输入参数键值' }]}
          placeholder="请输入参数键值"
        />
        <ProFormRadio.Group
          label="系统内置"
          name="configType"
          options={sysYesNoOnForm}
          rules={[{ required: true, message: '请选择是否系统内置' }]}
        />
        <ProFormText label="备注" name="remark" placeholder="请输入备注" />
      </ProForm>
    </Modal>
  );
};

export default RoleDetail;
