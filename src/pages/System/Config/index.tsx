/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { DeleteConfig, queryConfigList } from '@/api/config';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useConfigStore } from '@/store/config';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import Detail from './components/Detail';
import EProTable from '@/components/EProTable';
import PageContainer from '@/components/PageContainer';
import { TTableData } from './type';

const RoleManagement: React.FC = () => {
  // 获取table中需要的枚举
  const { getSysYesNo, sysYesNoOnTable } = useConfigStore();
  useEffect(() => {
    getSysYesNo();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  const [pageSize, setPageSize] = useState<number>(10);

  // 新增设置
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [detailType, setDetailType] = useState<string>('add');
  const [detailId, setDetailId] = useState<number>(-1);

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '参数主键',
      dataIndex: 'configId',
      key: 'configId',
      hideInSearch: true,
    },
    {
      title: '参数名称',
      dataIndex: 'configName',
      key: 'configName',
    },
    {
      title: '参数键名',
      dataIndex: 'configKey',
      key: 'configKey',
    },
    {
      title: '参数键值',
      dataIndex: 'configValue',
      key: 'configValue',
      hideInSearch: true,
    },
    {
      title: '系统内置',
      dataIndex: 'configType',
      key: 'configType',
      valueType: 'select',
      valueEnum: sysYesNoOnTable,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => {
            setDetailId(record.configId);
            setDetailType('view');
            setOpenDetail(true);
          }}
        >
          查看
        </a>,
        <a
          key="edit"
          onClick={() => {
            setDetailId(record.configId);
            setDetailType('edit');
            setOpenDetail(true);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.configId)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
      ],
      key: 'option',
    },
  ];

  /**
   * @TODO 删除设置
   */
  const handleDelete = async (id: number) => {
    try {
      const { code, msg } = await DeleteConfig(id + '');
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  return (
    <PageContainer>
      <div>
        <EProTable<TTableData>
          className="spec-table"
          scroll={{
            x: true,
          }}
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params: any = {}, sort, filter) => {
            setLoading(true);
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;
            const { code, rows, total, msg } = await queryConfigList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            setLoading(false);
            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="configId"
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                };
              }
              return values;
            },
          }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange(current, size) {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setOpenDetail(true);
              }}
            >
              新增
            </Button>,
          ]}
          tableAlertRender={false}
          tableAlertOptionRender={false}
        />
      </div>

      {/* 设置详情 */}
      <Detail
        open={openDetail}
        closeDetail={() => {
          setOpenDetail(false);
          tableReload();
        }}
        detailType={detailType}
        detailId={detailId}
      />
    </PageContainer>
  );
};

export default RoleManagement;
