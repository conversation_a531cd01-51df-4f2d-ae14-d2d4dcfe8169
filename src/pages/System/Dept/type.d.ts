/**
 * @type 机构管理列表
 */
export type TTableData = {
  deptId: number;
  deptName: string;
  leader: string;
  remark: string;
  children: TTableData[];
  parentId: number;
  status: number;
  createTime: string;
};

/**
 * @type 机构详情表单
 */
export type TFormInfo = {};

/**
 * @type 负责人
 */
export type TLeader = {
  userId: number;
  nickName: string;
};

/**
 * @type 组织架构树类型
 */
export interface IDeptTree {
  id: number;
  label: string;
  children: IDeptTree[];
}
