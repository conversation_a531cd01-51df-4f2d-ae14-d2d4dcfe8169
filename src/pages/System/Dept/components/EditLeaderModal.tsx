/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useRef, useState } from 'react';
import { Input, Modal, Select, Space } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import EProTable from '@/components/EProTable';

type TEditLeaderProps = {
  open: boolean;
  close: () => void;
  onOk: (val: string[]) => void;
  dataSource: any[];
  selectLeader: string;
};

const EditLeaderModal: React.FC<TEditLeaderProps> = ({
  open,
  close,
  onOk,
  dataSource,
  selectLeader,
}) => {
  useEffect(() => {
    if(selectLeader) {
      setSelectedKeys([selectLeader]);
    }else {
      setSelectedKeys([])
    }
    setSearchVal('');
    tableReload();
  }, [open, selectLeader]);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  // 选中的项目key
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 搜索
  const [searchName, setSearchName] = useState<string>('name');
  const [searchVal, setSearchVal] = useState<string>();

  // 表头
  const columns: ProColumns[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      key: 'index',
    },
    {
      title: '姓名',
      dataIndex: 'nickName',
      ellipsis: true,
      // hideInSearch: true,
      formItemProps: {
        label: '',
      },
      renderFormItem: (parentItem: any, { isEditable }) => {
        return (
          <Input.Group compact style={{ width: '300px' }}>
            <Select
              style={{ width: '35%' }}
              onChange={(value) => {
                setSearchName(value);
              }}
              value={searchName}
            >
              <Select.Option value="name">姓名</Select.Option>
            </Select>
            <Input
              style={{ width: '65%' }}
              allowClear
              value={searchVal}
              onChange={(e) => {
                setSearchVal(e.target.value);
              }}
              placeholder="请输入姓名"
            />
          </Input.Group>
        );
      },
    },
    {
      title: '机构',
      dataIndex: 'dept',
      ellipsis: true,
      hideInSearch: true,
      render: (_, record) => <span>{record.dept && record.dept.deptName}</span>,
    },
  ];

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 表格选中方法
   */
  // 由于cancelRowKeys不影响dom，所以不使用useState定义
  let cancelRowKeys: any = []; // 取消选择的项目
  const onSelect = (record: any, selected: any) => {
    if (!selected) {
      cancelRowKeys = [record.userId];
    }
  };
  const onMulSelect = (selected: any, selectedRows: any, changeRows: any) => {
    if (!selected) {
      cancelRowKeys = changeRows.map((item: any) => item.userId);
    }
  };
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    // 多选
    // if (cancelRowKeys.length) {
    //   const keys = selectedKeys.filter(
    //     (item: any) => !cancelRowKeys.includes(item)
    //   );
    //   setSelectedKeys(keys);
    //   cancelRowKeys = [];
    // } else {
    //   setSelectedKeys([...new Set(selectedKeys.concat(selectedRowKeys))]);
    // }
    // 单选
    setSelectedKeys([...selectedRowKeys]);
  };

  return (
    <Modal
      title="人员选择"
      width="50%"
      open={open}
      onCancel={close}
      onOk={() => onOk(selectedKeys)}
    >
      <EProTable
        actionRef={actionRef}
        columns={columns}
        request={async (params: any = {}, sort, filter) => {
          let arr: any[] = [];
          if (searchVal) {
            arr = [...dataSource].filter((item) => item.nickName === searchVal);
          } else {
            arr = [...dataSource];
          }
          return {
            data: arr,
            success: true,
          };
        }}
        onReset={() => {
          setSearchVal('');
          tableReload();
        }}
        rowKey="userId"
        scroll={{ y: 300 }}
        options={false}
        rowSelection={{
          selectedRowKeys: selectedKeys,
          onSelect,
          // onSelectMultiple: onMulSelect,
          // onSelectAll: onMulSelect,
          onChange: onSelectChange,
          type: 'radio',
        }}
        tableAlertRender={({ selectedRowKeys, selectedRows }) => (
          <Space size={24}>
            <span>已选 {selectedRowKeys.length} 项</span>
          </Space>
        )}
        tableAlertOptionRender={({ selectedRowKeys, selectedRows }) => (
          <Space size={24}>
            <span>
              <a
                style={{ marginInlineStart: 10 }}
                onClick={() => {
                  // 选中效果
                  setSelectedKeys([]);
                }}
              >
                取消选择
              </a>
            </span>
          </Space>
        )}
        search={{
          resetText: '',
        }}
      />
    </Modal>
  );
};

export default EditLeaderModal;
