/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, CheckboxOptionType, message, Space } from 'antd';
import {
  getDepartmentDetail,
  ISaveDeptParam,
  postAddDepartment,
  postEditDepartment,
} from '@/api/department';
import { getDict } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  FormInstance,
  ProForm,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { FormInitVal, formItemLayout, statusRadioOptions } from '../data';

type TDetailProps = {
  close: () => void;
  detailType: string;
  isView: boolean;
  detailDeptId: number;
  detailParentId: number;
};

const DepartmentDetail: React.FC<TDetailProps> = ({
  isView,
  detailType,
  close,
  detailDeptId,
  detailParentId,
}) => {
  // loading
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>(null);

  // 机构类型列表
  const [orgTypeList, setOrgTypeList] = useState<
    (string | number | CheckboxOptionType)[]
  >([]);

  /**
   * @TODO 提交表单
   */
  const handleSave = async (values: any) => {
    const publicParam = {
      ...values,
      parentId: detailParentId,
    };
    if (detailType === 'edit') publicParam.deptId = detailDeptId;
    setSubmitLoading(true);
    try {
      const { code, msg } = await saveFetch(publicParam);
      if (code === codeDefinition.QUERY_SUCCESS) {
        close();
        message.success(QUERY_SUCCESS_MSG);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setSubmitLoading(false);
    }
  };

  const saveFetch = useCallback(
    (data: ISaveDeptParam) => {
      if (detailType === 'add') {
        return postAddDepartment(data);
      } else {
        return postEditDepartment(data);
      }
    },
    [detailType]
  );

  /**
   * 从字典表获取机构类型列表
   */
  const queryOrgTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('dept_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setOrgTypeList(
        data?.map((item: Record<string, any>) => ({
          label: item.dictLabel,
          value: ~~item.dictValue,
        }))
      );
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = useCallback(async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await getDepartmentDetail(detailDeptId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.status = Number(data.status);
          data.level = Number(data.level);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [detailType, detailDeptId]);

  useEffect(() => {
    getDetailData();
    queryOrgTypeEnums();
  }, [getDetailData]);

  // 页脚
  const Footer = (
    <div className="flex flex-row flex-nowrap justify-center items-center">
      <Space size="large">
        <Button
          type="primary"
          loading={submitLoading}
          htmlType="submit"
          disabled={isView}
        >
          保存
        </Button>
        <Button type="default" onClick={close}>
          关闭
        </Button>
      </Space>
    </div>
  );

  return (
    <div className="px-6">
      <ProForm
        formRef={formRef}
        {...formItemLayout}
        layout="horizontal"
        grid={true}
        rowProps={{
          gutter: [24, 0],
        }}
        submitter={{
          render: (_, dom) => Footer,
        }}
        initialValues={FormInitVal}
        onFinish={handleSave}
        readonly={isView}
        //@ts-ignore
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          width="md"
          name="deptName"
          label="机构名称"
          placeholder="请输入机构名称"
          rules={[{ required: true, message: '请输入机构名称' }]}
        />
        <ProFormText
          width="md"
          name="deptCode"
          label="机构编码"
          placeholder="请输入机构编码"
          rules={[{ required: true, message: '请输入机构编码' }]}
        />
        <ProFormRadio.Group
          label="是否是实验室"
          rules={[{ required: true, message: '请选择是否是实验室' }]}
          name="labTag"
          options={[
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ]}
        />
        <ProFormRadio.Group
          label="机构状态"
          name="status"
          options={statusRadioOptions}
          rules={[{ required: true, message: '请选择机构状态' }]}
        />
        <ProFormRadio.Group
          label="机构类型"
          name="level"
          options={orgTypeList}
          rules={[{ required: true, message: '请选择机构类型' }]}
        />
        <ProFormText
          width="md"
          name="orderNum"
          label="机构排序"
          placeholder="请输入机构排序"
          rules={[{ required: true, message: '请输入机构排序' }]}
        />
        <ProFormText
          width="md"
          name="leader"
          label="负责人"
          placeholder="请输入负责人"
        />
        <ProFormText
          width="md"
          name="phone"
          label="联系电话"
          placeholder="请输入联系电话"
        />
        <ProFormText
          width="md"
          name="email"
          label="邮箱"
          placeholder="请输入邮箱"
        />
      </ProForm>
    </div>
  );
};

export default DepartmentDetail;
