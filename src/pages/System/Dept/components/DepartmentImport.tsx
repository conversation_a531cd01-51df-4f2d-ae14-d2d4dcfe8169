import React, { useEffect, useState } from 'react';
import { Button, message, Space, Upload } from 'antd';
import type { UploadProps } from 'antd';
import { postImportDepartment } from '@/api/department';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { CloudUploadOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Dragger } = Upload;

const DepartmentImport: React.FC<{ close: () => void; open: boolean }> = ({
  close,
  open,
}) => {
  useEffect(() => {
    open && setFileList([]);
  }, [open]);

  //上传文件
  const [fileList, setFileList] = useState<any[]>([]);

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    fileList: fileList,
    action: postImportDepartment,
    onChange: async (info) => {
      const { file, fileList } = info;
      setFileList(fileList);
      // 状态必须在完成或者移除时
      if (file.status === 'done' || file.status === 'removed') {
        // 上传文件失败
        if (file.response.code !== codeDefinition.QUERY_SUCCESS) {
          message.warning(file.response.msg);
          // 删除掉上传失败的文件
          const fileData: any = fileList.filter(
            (item) => item.response.code === codeDefinition.QUERY_SUCCESS
          );
          setFileList(fileData);
          return;
        } else {
          message.success(QUERY_SUCCESS_MSG);
        }
      }
    },
  };

  /**
   * @TODO 下载模板
   */
  const handleDownloadSpecTemplate = async () => {
    const url = `${import.meta.env.VITE_URL}/system/dept/deptDownload`;
    fetch(url + '', {
      method: 'GET',
      // body: JSON.stringify({ filename }),  //fetch get请求不可以包含body!!
      credentials: 'include',
      headers: new Headers({
        'Content-Type': 'application/json',
      }),
    })
      .then((response) => {
        response.blob().then((blob) => {
          const aLink = document.createElement('a');
          document.body.appendChild(aLink);
          aLink.style.display = 'none';
          aLink.href = window.URL.createObjectURL(blob);
          aLink.download = '机构导入模板.xlsx';
          aLink.click();
          document.body.removeChild(aLink);
        });
      })
      .catch((error) => {
        throw new Error(`Error: ${error}`);
      });
  };

  return (
    <div className="import-data-wrapper w-full flex flex-col justify-center items-center">
      <div className="flex flex-row flex-nowrap gap-3">
        <InfoCircleOutlined
          className="text-2xl text-[#40a9ff] relative transform translate-y-1"
          style={{ color: '#40a9ff' }}
        />
        <div>
          <p className="!mb-0">
            1.导入模板中有字段导入说明，必填字段必须填写；
          </p>
          <p className="!mb-0">
            2.当你多次重复导入会时追加数据，不会覆盖数据；
          </p>
          <p className="!mb-0">3.单次导入的文件大小不能超过5M；</p>
        </div>
      </div>
      <div className="mt-8">
        <Dragger {...props} className="px-32">
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined
              className=" text-6xl"
              style={{ color: '#40a9ff' }}
            />
          </p>
          <p className="ant-upload-text">
            Click or drag file to this area to upload
          </p>
          <p className="ant-upload-hint">点击或拖拽 Excel 文件到这里</p>
        </Dragger>
      </div>
      <div className="mt-8">
        <Button type="text" onClick={handleDownloadSpecTemplate}>
          点击下载模板
        </Button>
      </div>
      <div className="w-full flex justify-end">
        <Space>
          <Button onClick={close}>关闭</Button>
        </Space>
      </div>
    </div>
  );
};

export default DepartmentImport;
