/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */

/*
 * @Description: 设置 - 组织 - 机构管理
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-01-28 10:55:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-11 14:01:35
 * @FilePath: /xr-qc-jk-web/src/pages/System/Dept/index.tsx
 */
import React, { useCallback, useRef, useState } from 'react';
import {
  Button,
  Drawer,
  Input,
  MenuProps,
  message,
  Modal,
  Popconfirm,
  Select,
} from 'antd';
import {
  getDepartmentListByPage,
  postDeleteDepartment,
  postExportDepartment,
  TDeptListByPageParams,
} from '@/api/department';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  PlusOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import DepartmentDetail from './components/DepartmentDetail';
import DepartmentImport from './components/DepartmentImport';
import EProTable from '@/components/EProTable';
import PageContainer from '@/components/PageContainer';
import download from '@/utils/download';
import { statusRadioOptions } from './data';
import { TTableData } from './type';

const DepartmentManagement: React.FC = () => {
  //loading
  const [loading, setLoading] = useState<boolean>(false);
  // 当前页记录数量 pageSize
  const [pageSize, setPageSize] = useState<number>(10);
  const [pageNum, setPageNum] = useState<number>(1);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  // 搜索
  const [searchVal, setSearchVal] = useState<string>('');
  const [searchName, setSearchName] = useState<string>('deptName');
  const [searchStatus, setSearchStatus] = useState<number>();

  // 详情类型
  const [detailType, setDetailType] = useState<string>('add');

  // 机构详情
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  // 详情上级机构ID
  const [detailParentId, setDetailParentId] = useState<number>(0);

  // 详情ID
  const [detailDeptId, setDetailDeptId] = useState<number>(-1);

  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增机构';
    } else if (detailType === 'edit') {
      return '编辑机构';
    } else {
      return '机构详情';
    }
  }, [detailType]);

  // 数据导入
  const [openImport, setOpenImport] = useState<boolean>(false);

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '机构名称',
      dataIndex: 'deptName',
      formItemProps: {
        label: '',
        wrapperCol: { span: 24 },
      },
      renderFormItem: () => {
        return (
          <Input
            value={searchVal}
            //@ts-ignore
            onChange={(e) => setSearchVal(e.target.value.trim())}
            addonBefore={
              <Select
                value={searchName}
                onChange={(e) => setSearchName(e)}
                options={[{ label: '机构名称', value: 'deptName' }]}
              />
            }
            allowClear
            placeholder="请输入名称，支持拼音首字母搜索"
          />
        );
      },
      width: 300,
      fixed: 'left',
    },
    {
      title: '负责人',
      dataIndex: 'leader',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      ellipsis: true,
      renderFormItem: () => {
        return (
          <Select
            onChange={(value) => {
              setSearchStatus(value);
            }}
            value={searchStatus}
            options={statusRadioOptions}
            placeholder="请选择"
            allowClear
          />
        );
      },
      renderText: (text, record) =>
        Number(record.status) === 0 ? '正常' : '停用',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => {
            setDetailType('view');
            setDetailDeptId(record.deptId);
            setDetailParentId(record.parentId);
            setOpenDetail(true);
          }}
        >
          查看
        </a>,
        <a
          key="edit"
          onClick={() => {
            setDetailType('edit');
            setDetailDeptId(record.deptId);
            setDetailParentId(record.parentId);
            setOpenDetail(true);
          }}
        >
          编辑
        </a>,
        <a
          key="add"
          onClick={() => {
            setDetailType('add');
            setDetailParentId(record.deptId);
            setOpenDetail(true);
          }}
        >
          新增
        </a>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.deptId + '')}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
      ],
      fixed: 'right',
    },
  ];

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeDetail = () => {
    setOpenDetail(false);
    tableReload();
  };

  /**
   * @TODO 重置表格
   */
  const handleReset = () => {
    setSearchVal('');
    setSearchStatus(undefined);
  };

  /**
   * @TODO 删除机构
   */
  const handleDelete = async (ids: string) => {
    setLoading(true);
    try {
      const { code, msg } = await postDeleteDepartment({ ids });
      if (code === codeDefinition.QUERY_SUCCESS) {
        tableReload();
        message.success(QUERY_SUCCESS_MSG);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 请求参数
  const [tableParams, setTableParams] = useState({});

  /**
   * @TODO 导出数据
   */
  const handleExport = async () => {
    const params = JSON.parse(JSON.stringify(tableParams));
    delete params.current;
    delete params.pageSize;
    const data = await postExportDepartment(params);
    download(data, '机构'); //导出方法，传入后端返回的文件流
  };

  // 下拉按钮列表
  const items: MenuProps['items'] = [
    {
      label: (
        <a onClick={handleExport}>
          <VerticalAlignTopOutlined /> 数据导出
        </a>
      ),
      key: 'export',
    },
    {
      label: (
        <a onClick={() => setOpenImport(true)}>
          <VerticalAlignBottomOutlined /> 数据导入
        </a>
      ),
      key: 'import',
    },
  ];

  /**
   * @TODO 关闭数据导入
   */
  const handleCloseImport = () => {
    setOpenImport(false);
    tableReload();
  };

  // 展开的行
  const [expandKeys, setExpandKeys] = useState<React.Key[]>([]);
  // 是否展开/折叠
  const [isExpansion, setIsExpansion] = useState<boolean>(false);
  const [currentAllKeys, setCurrentAllKeys] = useState<string[]>([]);

  // 获取当前页所有的key
  const getCurrentExpandKeys = (data: any[]) => {
    const _newData: any[] = data.reduce(
      (pre, cur) =>
        cur.children && cur.children.length
          ? pre.concat([cur, ...getCurrentExpandKeys(cur.children)])
          : pre.concat(cur),
      []
    );
    return _newData;
  };

  // 将空children设置为undefined
  const setDataSourceChildren = (data: any[]) => {
    return data.map((item) => {
      if (item.children && !item.children.length) {
        delete item.children;
      }
      if (item.children && item.children.length) {
        item.children = setDataSourceChildren(item.children);
      }
      return item;
    });
  };

  return (
    <PageContainer>
      <div>
        <EProTable<TTableData>
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          scroll={{
            x: 1200,
          }}
          request={async (params, sort, filter) => {
            setLoading(true);
            const param: TDeptListByPageParams = {
              pageNum: params.current!,
              pageSize: params.pageSize!,
            };
            if (searchVal) param.deptName = searchVal;
            if (searchStatus || searchStatus === 0) param.status = searchStatus;
            setTableParams(param);
            const { code, data, msg } = await getDepartmentListByPage(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            let _keys = [];
            if (data.rows && data.rows.length) {
              data.rows.forEach((item: any) => {
                item.deptId = item.deptId.toString();
              });
              data.rows = setDataSourceChildren(data.rows);
              _keys = getCurrentExpandKeys(data.rows).map(
                (item) => item.deptId
              );
            }
            setCurrentAllKeys(_keys);
            setExpandKeys(_keys);
            setIsExpansion(_keys.length > data.rows.length);

            setLoading(false);
            return {
              data: data?.rows ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          onReset={handleReset}
          rowKey="deptId"
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
            //@ts-ignore
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                };
              }
              return values;
            },
          }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange(current, size) {
              setPageNum(current);
              setPageSize(size);
            },
            current: pageNum,
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="expanded"
              type="primary"
              //@ts-ignore
              onClick={(e, val = !isExpansion) => {
                if (val) {
                  setExpandKeys([...currentAllKeys]);
                } else {
                  setExpandKeys([]);
                }
                setIsExpansion(val);
              }}
            >
              {isExpansion ? '折叠' : '展开'}
              所有
            </Button>,
            <Button
              key="add"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setDetailParentId(100);
                setOpenDetail(true);
              }}
            >
              新增机构
            </Button>,
            // <Dropdown key="menu" menu={{ items }}>
            //   <Button>更多操作</Button>
            // </Dropdown>,
          ]}
          expandable={{
            indentSize: 32,
            expandedRowKeys: expandKeys,
            onExpandedRowsChange: (expandedRowKeys) => {
              setExpandKeys([...expandedRowKeys]);
            },
            childrenColumnName: 'children',
          }}
        />
      </div>
      {/* 详情 */}
      <Drawer
        width="700"
        title={detailTitleComputed()}
        onClose={closeDetail}
        open={openDetail}
        destroyOnClose
      >
        <DepartmentDetail
          detailType={detailType}
          close={closeDetail}
          detailDeptId={detailDeptId}
          isView={detailType === 'view'}
          detailParentId={detailParentId}
        />
      </Drawer>
      {/* 导入 */}
      <Modal
        title="导入"
        width="40%"
        open={openImport}
        onCancel={() => setOpenImport(false)}
        footer={null}
      >
        <DepartmentImport open={openImport} close={handleCloseImport} />
      </Modal>
    </PageContainer>
  );
};

export default DepartmentManagement;
