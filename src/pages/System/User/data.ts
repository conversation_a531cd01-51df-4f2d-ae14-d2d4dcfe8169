/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-04-26 11:27:59
 * @LastEditors: LGX
 * @LastEditTime: 2024-02-27 15:50:18
 * @FilePath: \xr-qc-jk-web\src\pages\System\User\data.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @description 账户状态枚举
 */
export const statusEnum = [
  { id: 1, label: '正常', value: '0' },
  { id: 2, label: '停用', value: '1' },
];

/**
 * @description 列表搜索枚举
 */
export const searchEnum = [
  { id: 1, label: '姓名', value: 'nickName' },
  { id: 2, label: '手机', value: 'phonenumber' },
  { id: 3, label: '登陆账号', value: 'userName' },
];

/**
 * @description 初始密码
 */
export const numCode = 123456;

/**
 * @description 表单布局
 */
export const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

/**
 * @description 详情表单初始值
 */
export const FormInitVal = {
  deptId: null,
  nickName: null,
  sex: '0',
  userName: null,
  password: null,
  phonenumber: null,
  ebsAccount: null,
  email: null,
  professionalTitle: null,
  position: null,
  status: '0',
  roleIds: [],
  notPassword: null
};

/**
 * @description 详情性别枚举
 */
export const SexOptions = [
  { label: '男', value: '0' },
  { label: '女', value: '1' },
];
