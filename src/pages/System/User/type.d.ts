/**
 * @type 表格数据类型
 */
export type TTableData = {
  status: string;
  roles: number[];
  sex: string;
  nickName: string;
  dept: IDept;
  phonenumber: string;
  userName: string;
  email: string;
  userId: number;
  roleIds: number[];
  // 是否被锁定
  loginLocked: boolean
};

export interface IDept {
  deptId: number;
  deptName: string;
  leader: string;
  remark: string;
  parentId: number;
  status: number;
  createTime: string;
}

/**
 * @type 员工详情表单类型
 */
export type TFormInfo = {
  deptId: number;
  nickName: string;
  sex: number;
  userName: string;
  password: string;
  phonenumber: string;
  ebsAccount: number;
  email: string;
  professionalTitle: number;
  position: number;
  status: number;
  roles: string[];
};

/**
 * @type 角色列表类型
 */
export type TRole = {
  roleName: string;
  roleId: number;
};
