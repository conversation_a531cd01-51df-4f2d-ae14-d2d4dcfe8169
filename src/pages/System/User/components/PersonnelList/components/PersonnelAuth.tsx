import { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { queryUserAuthRoleList } from '@/api/personnel';
import { queryRoleSelectList } from '@/api/role';
import { codeDefinition } from '@/constants';
import {
  ProForm,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import EditPersonnelAuth from './EditPersonnelAuth';

type TPersonnelAuthProps = {
  userId: number;
  userName: string;
  deptName: string;
};

const PersonnelAuth: React.FC<TPersonnelAuthProps> = ({
  userId,
  userName,
  deptName,
}) => {
  const [openEditAuth, setOpenEditAuth] = useState<boolean>(false);

  const [openCopyAuth, setOpenCopyAuth] = useState<boolean>(false);

  const [roleIds, setRoleIds] = useState<number[]>([]);

  /**
   * @TODO 获取该用户被授权的角色
   */
  const getUserRoles = async () => {
    try {
      const { code, data, msg } = await queryUserAuthRoleList(userId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setRoleIds([...data].map((i: any) => i.roleId));
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    getUserRoles();
  }, []);

  return (
    <div className="p-6">
      <ProForm readonly={true} submitter={false} layout="horizontal">
        <ProFormText
          label="姓名"
          name="nickName"
          initialValue={userName}
        ></ProFormText>
        <ProFormText
          label="所属部门"
          name="deptName"
          initialValue={deptName}
        ></ProFormText>
        <ProFormCheckbox.Group
          label="已选角色"
          name="roles"
          fieldProps={{
            value: roleIds,
          }}
          request={async () => {
            let res: any = [];
            const { code, data, msg } = await queryRoleSelectList();
            if (code === codeDefinition.QUERY_SUCCESS) {
              res = data.map((item: any) => ({
                label: item.roleName,
                value: item.roleId,
              }));
            } else {
              message.error(msg);
            }
            return res;
          }}
        ></ProFormCheckbox.Group>
      </ProForm>
      <div>
        <Button
          type="primary"
          size="large"
          className="mr-6"
          onClick={() => setOpenEditAuth(true)}
        >
          选择角色
        </Button>
        <Button
          type="primary"
          size="large"
          onClick={() => {
            setOpenCopyAuth(true);
          }}
        >
          复制其他用户权限
        </Button>
      </div>
      {/* 选择角色 */}
      <EditPersonnelAuth
        open={openEditAuth}
        close={() => {
          setOpenEditAuth(false);
          getUserRoles();
        }}
        userId={userId}
      />
    </div>
  );
};

export default PersonnelAuth;
