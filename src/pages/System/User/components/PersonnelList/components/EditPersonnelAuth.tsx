import React, { useEffect, useState } from 'react';
import { message, Modal, Transfer } from 'antd';
import { TransferDirection } from 'antd/lib/transfer';
import { queryUserAuthRole, queryUserAuthRoleList } from '@/api/personnel';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { TRole } from '../../../type';

type TEditPersonnelAuthProps = {
  open: boolean;
  close: () => void;
  userId: number;
};

const EditPersonnelAuth: React.FC<TEditPersonnelAuthProps> = ({
  open,
  close,
  userId,
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 数据源
  const [dataSource, setDataSource] = useState<TRole[]>([]);

  // 显示在穿梭框右侧的数据key
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  // 选中项
  const [selectedKeys, setSelectKeys] = useState<string[]>([]);

  /**
   * @description 穿梭框转移时的回调函数
   */
  const onChange = (
    targetKeys: string[],
    direction: TransferDirection,
    moveKeys: string[]
  ) => {
    setTargetKeys(targetKeys);
  };

  /**
   * @description 选中项改变时的回调函数
   */
  const onSelectChange = (
    sourceSelectedKeys: string[],
    targetSelectedKeys: string[]
  ) => {
    setSelectKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  /**
   * @TODO 获取角色列表
   */
  const getRoleList = async () => {
    try {
      const { code, data, msg } = await queryUserAuthRoleList(userId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        data.roles.forEach((i: any) => i.roleId + '');
        setDataSource(data.roles);
        setTargetKeys(data.user.roles.map((i: any) => i.roleId + ''));
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    open && getRoleList();
  }, [open]);

  /**
   * @TODO 提交/批量提交授权用户
   */
  const handleSave = async () => {
    // console.log(targetKeys, 'targetKeys');
    // return;
    setLoading(true);
    try {
      const { code, msg } = await queryUserAuthRole({
        userId,
        roleIds: targetKeys.join(','),
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };
  return (
    <Modal
      title="添加授权角色"
      width="60%"
      open={open}
      destroyOnClose
      onCancel={() => {
        // setDataSource([]);
        setTargetKeys([]);
        setSelectKeys([]);
        close();
      }}
      onOk={handleSave}
      confirmLoading={loading}
    >
      <Transfer
        className="w-full"
        dataSource={dataSource}
        titles={['角色', '授权角色']}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        onChange={onChange}
        onSelectChange={onSelectChange}
        listStyle={{ width: '50%', minHeight: '400px' }}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          simple: true,
        }}
        render={(item) => item.roleName}
        rowKey={(item) => item.roleId + ''}
      />
    </Modal>
  );
};

export default EditPersonnelAuth;
