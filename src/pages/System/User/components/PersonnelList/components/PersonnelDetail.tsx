/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import { FormInstance } from 'antd/es/form';
import { getParentDept } from '@/api/department';
import {
  ISaveEmployeeParam,
  queryAddUser,
  queryEditUser,
  queryUserDetail,
} from '@/api/personnel';
import { queryRoleSelectList } from '@/api/role';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import {
  ProForm,
  ProFormCheckbox,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import {
  FormInitVal,
  formItemLayout,
  SexOptions,
  statusEnum,
} from '../../../data';
import { TFormInfo } from '../../../type';
import './personnelDetail.less';

type TDetailProps = {
  close: () => void;
  detailType: string;
  isView: boolean;
  userId: number;
};

const PersonnelDetail: React.FC<TDetailProps> = ({
  isView,
  close,
  detailType,
  userId,
}) => {
  // loading
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  /**
   * @TODO 获取用户详情
   */
  const getUserDetail = useCallback(async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryUserDetail(userId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.user.roleIds = data.roleIds;
          formRef.current?.setFieldsValue(data.user);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  }, [detailType, userId]);

  useEffect(() => {
    getUserDetail();
  }, [getUserDetail]);

  // 页脚
  const Footer = (
    <div className="flex flex-row flex-nowrap justify-center items-center">
      <Space size="large">
        <Button
          type="primary"
          loading={submitLoading}
          htmlType="submit"
          disabled={isView}
        >
          保存
        </Button>
        <Button type="default" onClick={close}>
          关闭
        </Button>
      </Space>
    </div>
  );

  /**
   * @TODO 保存详情
   */
  const handleSave = async (values: any) => {
    const publicParam = {
      ...values,
    };
    setSubmitLoading(true);
    try {
      const { code, msg } = await saveFetch(publicParam);
      if (code === codeDefinition.QUERY_SUCCESS) {
        close();
        message.success(QUERY_SUCCESS_MSG);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setSubmitLoading(false);
    }
  };
  // 请求
  const saveFetch = async (params: ISaveEmployeeParam) => {
    if (detailType === 'add') {
      return queryAddUser(params);
    } else {
      params.userId = userId;
      return queryEditUser(params);
    }
  };

  return (
    <div>
      <ProForm<TFormInfo>
        className="p-6"
        formRef={formRef}
        {...formItemLayout}
        layout="horizontal"
        grid={true}
        rowProps={{
          gutter: [24, 0],
        }}
        submitter={{
          render: (_, dom) => Footer,
        }}
        initialValues={FormInitVal}
        onFinish={handleSave}
        readonly={isView}
        //@ts-ignore
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          width="md"
          name="userName"
          label="登录账号"
          placeholder="请输入"
          disabled={detailType !== 'add'}
          rules={[{ required: true, message: '请输入' }]}
          colProps={{
            span: 12,
          }}
        />
        {detailType === 'add' ? (
          <ProFormText.Password
            width="md"
            name="password"
            label="登录密码"
            placeholder="请输入"
            rules={[{ required: true, message: '请输入' }]}
            colProps={{
              span: 12,
            }}
          />
        ) : undefined}
        <ProFormTreeSelect
          width="md"
          name="deptId"
          label="所在部门"
          placeholder="请选择"
          rules={[{ required: true, message: '请选择' }]}
          colProps={{
            span: 12,
          }}
          request={async () => {
            let result: any[] = [];
            const { code, data, msg } = await getParentDept();
            if (code === codeDefinition.QUERY_SUCCESS) {
              result = [...data];
            } else {
              message.error(msg);
            }
            return result;
          }}
          fieldProps={{
            fieldNames: { value: 'id' },
            showSearch: true,
            // filterTreeNode: true,
            treeNodeFilterProp: 'label',
          }}
        />
        <ProFormText
          width="md"
          name="nickName"
          label="用户姓名"
          placeholder="请输入"
          rules={[{ required: true, message: '请输入' }]}
          colProps={{
            span: 12,
          }}
        />

        <ProFormText
          width="md"
          name="phonenumber"
          label="手机号码"
          placeholder="请输入"
          rules={[{ required: true, message: '请输入' }]}
          colProps={{
            span: 12,
          }}
        />
        <ProFormText
          width="md"
          name="email"
          label="电子邮箱"
          placeholder="请输入"
          colProps={{
            span: 12,
          }}
        />

        <ProFormRadio.Group
          width="md"
          name="sex"
          label="用户性别"
          options={SexOptions}
          rules={[{ required: true, message: '请选择' }]}
          colProps={{
            span: 12,
          }}
        />
        <ProFormText
          width="md"
          name="ssoUserName"
          label="省平台账号"
          placeholder="请输入"
          colProps={{
            span: 12,
          }}
        />
        <ProFormRadio.Group
          width="md"
          name="status"
          label="账户状态"
          placeholder="请选择"
          colProps={{
            span: 12,
          }}
          options={statusEnum as any}
        />
        <ProFormCheckbox.Group
          colProps={{
            span: 24,
          }}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 20 }}
          name="roleIds"
          label="用户角色"
          request={async () => {
            let res: any = [];
            const { code, data, msg } = await queryRoleSelectList();
            if (code === codeDefinition.QUERY_SUCCESS) {
              res = data.roles.map((item: any) => ({
                label: item.roleName,
                value: item.roleId,
              }));
            } else {
              message.error(msg);
            }
            return res;
          }}
        />
      </ProForm>
    </div>
  );
};

export default PersonnelDetail;
