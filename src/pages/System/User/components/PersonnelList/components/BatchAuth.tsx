import { useRef, useState } from 'react';
import { FormInstance, message, Modal } from 'antd';
import { queryRoleList } from '@/api/role';
import { codeDefinition } from '@/constants';
import { ProForm, ProFormCheckbox } from '@ant-design/pro-components';

// import { queryBatchAuth } from '@/api/personnel';

type TBatchAuthProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  userIds: React.Key[];
};

const BatchAuth: React.FC<TBatchAuthProps> = ({ open, setOpen, userIds }) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 表单实例
  const formRef = useRef<FormInstance>();

  return (
    <Modal
      title="批量授权"
      width="40%"
      open={open}
      destroyOnClose
      onCancel={() => setOpen(false)}
      confirmLoading={loading}
      onOk={() => formRef.current?.submit()}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        layout="horizontal"
        onFinish={async (values) => {
          setLoading(true);
          try {
            // const { code, msg } = await queryBatchAuth({
            //   userIds: userIds.map((i) => Number(i)),
            //   roleIds: values.roles,
            // });
            // if (code === codeDefinition.QUERY_SUCCESS) {
            //   message.success(msg);
            //   setOpen(false);
            // } else {
            //   message.error(msg);
            // }
          } catch (error) {
            throw new Error(`${error}`);
          } finally {
            setLoading(false);
          }
        }}
      >
        <ProFormCheckbox.Group
          colProps={{
            span: 24,
          }}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 20 }}
          name="roles"
          label="用户角色"
          request={async () => {
            let res: any = [];
            const { code, data, msg } = await queryRoleList({});
            if (code === codeDefinition.QUERY_SUCCESS) {
              res = data.rows.map((item: any) => ({
                label: item.roleName,
                value: item.roleId,
              }));
            } else {
              message.error(msg);
            }
            return res;
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default BatchAuth;
