/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Button,
  Drawer,
  Dropdown,
  Input,
  MenuProps,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
} from 'antd';
import {
  queryDeleteUser,
  queryResetUserPassword,
  queryUserList,
  unlockUserApi,
} from '@/api/personnel';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import BatchAuth from './components/BatchAuth';
import EditPersonnelAuth from './components/EditPersonnelAuth';
import PersonnelDetail from './components/PersonnelDetail';
import EProTable from '@/components/EProTable';
import { numCode, searchEnum, statusEnum } from '../../data';
import { TTableData } from '../../type';

type TPersonnelListProps = {
  selectedDept: string;
};

const PersonnelList: React.FC<TPersonnelListProps> = ({ selectedDept }) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  // 当前页记录数量 pageSize
  const [pageSize, setPageSize] = useState<number>(10);
  const [pageNum, setPageNum] = useState<number>(1);

  // 详情类型
  const [detailType, setDetailType] = useState<string>('add');

  // 人员详情
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增用户';
    } else if (detailType === 'edit') {
      return '编辑用户';
    } else {
      return '用户详情';
    }
  }, [detailType]);

  // 搜索
  const [searchVal, setSearchVal] = useState<string>('');
  const [searchName, setSearchName] = useState('nickName');

  // 请求参数
  const [tableParams, setTableParams] = useState({});

  // 选中的项目key
  const [selectedKeys, setSelectedKeys] = useState([]);

  // 重置
  const [isModalResetOpen, setIsModalResetOpen] = useState<boolean>(false);

  // 批量修改授权
  const [openBatchAuth, setOpenBatchAuth] = useState<boolean>(false);

  // 用户授权
  const [openPersonnelAuth, setOpenPersonnelAuth] = useState<boolean>(false);

  // 当前点击用户信息
  const [nowUserInfo, setNowUserInfo] = useState<any>({});

  // 选择授权的用户id
  const [selectUserId, setSelectUserId] = useState<number>(-1);
  const [selectUserName, setSelectUserName] = useState<string>('');
  const [selectUserDept, setSelectUserDept] = useState<string>('');

  /**
   * 表格选中方法
   */
  // 由于cancelRowKeys不影响dom，所以不使用useState定义
  let cancelRowKeys: any = []; // 取消选择的项目
  const onSelect = (record: any, selected: any) => {
    if (!selected) {
      cancelRowKeys = [record.userId];
    }
  };
  const onMulSelect = (selected: any, selectedRows: any, changeRows: any) => {
    if (!selected) {
      cancelRowKeys = changeRows.map((item: any) => item.userId);
    }
  };
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    if (cancelRowKeys.length) {
      const keys = selectedKeys.filter(
        (item: any) => !cancelRowKeys.includes(item)
      );
      setSelectedKeys(keys);
      cancelRowKeys = [];
    } else {
      setSelectedKeys([...new Set(selectedKeys.concat(selectedRowKeys))]);
    }
  };

  const handleUnlock = async (userId: number) => {
    try {
      const { code, msg } = await unlockUserApi({
        userId,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        tableReload();
        message.success('解锁成功');
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '账户状态',
      dataIndex: 'status',
      width: 100,
      key: 'status',
      valueType: 'select',
      renderText: (text, record) => (record.status === '0' ? '正常' : '停用'),
      fieldProps: {
        options: statusEnum,
      },
    },
    // 搜索
    {
      title: '机构名称',
      key: 'orgName',
      hideInTable: true,
    },
    {
      title: '姓名',
      dataIndex: 'nickName',
      width: 120,
      ellipsis: true,
      key: 'nickName',
      formItemProps: {
        label: '',
        wrapperCol: {
          span: 24,
        },
      },
      renderFormItem: () => {
        return (
          <Input
            value={searchVal}
            //@ts-ignore
            onChange={(e) => setSearchVal(e.target.value.trim())}
            addonBefore={
              <Select
                className=" w-[100px]"
                value={searchName}
                onChange={(e) => setSearchName(e)}
                options={searchEnum}
              />
            }
            allowClear
            placeholder="请输入名称，支持拼音首字母搜索"
          />
        );
      },
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
      key: 'sex',
      renderText: (text, record) => (record.sex === '0' ? '男' : '女'),
    },
    {
      title: '所在部门',
      dataIndex: 'dept',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
      key: 'dept',
      renderText: (text, record) => record.dept?.deptName || '-',
    },
    {
      title: '手机号码',
      dataIndex: 'phonenumber',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
      key: 'phonenumber',
    },
    {
      title: '登录账号',
      dataIndex: 'userName',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
      key: 'userName',
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      width: 180,
      ellipsis: true,
      hideInSearch: true,
      key: 'email',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 220,
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => {
            setDetailType('view');
            setSelectUserId(record.userId);
            setOpenDetail(true);
          }}
        >
          查看
        </a>,
        <a
          key="edit"
          onClick={() => {
            setDetailType('edit');
            setSelectUserId(record.userId);
            setOpenDetail(true);
          }}
        >
          编辑
        </a>,
        <a
          key="auth"
          onClick={() => {
            setSelectUserId(record.userId);
            setSelectUserName(record.nickName);
            setSelectUserDept(record?.dept?.deptName);
            setOpenPersonnelAuth(true);
          }}
        >
          授权
        </a>,
        <Popconfirm
          title="重置密码？"
          onConfirm={() => handleResetPassword(record)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a>重置密码</a>
        </Popconfirm>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.userId + '')}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
        record.loginLocked ? (
          <Button type="link" onClick={() => handleUnlock(record.userId)}>
            解锁
          </Button>
        ) : null,
      ],
      key: 'option',
    },
  ];

  // 下拉按钮列表
  const items: MenuProps['items'] = [
    // {
    //   label: (
    //     <div onClick={() => {}}>
    //       <VerticalAlignTopOutlined /> 数据导出
    //     </div>
    //   ),
    //   key: '1',
    // },
    // {
    //   label: (
    //     <div onClick={() => {}}>
    //       <VerticalAlignBottomOutlined /> 数据导入
    //     </div>
    //   ),
    //   key: '2',
    // },
    {
      label: (
        <a
          onClick={() =>
            Modal.confirm({
              title: '确认批量删除已选择的项目?',
              icon: <ExclamationCircleOutlined />,
              onOk() {
                if (selectedKeys.length < 1) {
                  message.warning('请先选择需要删除的用户');
                  return;
                }
                const ids = selectedKeys.join(',');
                handleDelete(ids);
                console.log(ids);
              },
              onCancel() {
                return false;
              },
            })
          }
        >
          <DeleteOutlined /> 批量删除
        </a>
      ),
      key: '3',
    },
  ];

  /**
   * @TODO 刷新表格
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeDetail = () => {
    setOpenDetail(false);
    tableReload();
  };

  useEffect(() => {
    setPageNum(1);
    tableReload();
  }, [selectedDept]);

  /**
   * @TODO 删除用户
   */
  const handleDelete = async (ids: string) => {
    try {
      const { code, msg } = await queryDeleteUser({ ids });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 重置用户密码
   */
  const handleResetPassword = async (userObj: any) => {
    try {
      console.log(userObj);
      setNowUserInfo(userObj);
      const { code, msg } = await queryResetUserPassword({
        userId: userObj?.userId,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        setIsModalResetOpen(true);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 重置表格
   */
  const handleReset = () => {
    setSearchVal('');
    setSearchName('nickName');
    tableReload();
  };

  return (
    <>
      <div>
        <EProTable<TTableData>
          className="spec-table"
          scroll={{
            x: 500,
          }}
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          bordered
          request={async (params: any = {}, sort, filter) => {
            setLoading(true);
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
              deptId: selectedDept,
            };

            delete param.current;
            if (searchVal) param[searchName] = searchVal;
            setTableParams(param);
            const { code, rows, total, msg } = await queryUserList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            setLoading(false);
            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          onReset={handleReset}
          rowKey="userId"
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
            //@ts-ignore
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                };
              }
              return values;
            },
          }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange(current, size) {
              setPageNum(current);
              setPageSize(size);
            },
            current: pageNum,
          }}
          dateFormatter="string"
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onSelect,
            onSelectMultiple: onMulSelect,
            onSelectAll: onMulSelect,
            onChange: onSelectChange,
          }}
          toolBarRender={() => [
            <Button
              key="addUser"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setOpenDetail(true);
              }}
            >
              新增用户
            </Button>,
            <Dropdown key="menu" menu={{ items }}>
              <Button>更多操作</Button>
            </Dropdown>,
          ]}
          tableAlertRender={({ selectedRowKeys, selectedRows }) => (
            <Space size={24}>
              <span>已选 {selectedRowKeys.length} 项</span>
            </Space>
          )}
          tableAlertOptionRender={({ selectedRowKeys, selectedRows }) => (
            <Space size={24}>
              <span>
                <a
                  style={{ marginInlineStart: 10 }}
                  onClick={() => {
                    // 选中效果
                    setSelectedKeys([]);
                  }}
                >
                  取消选择
                </a>
              </span>
            </Space>
          )}
        />
      </div>
      {/* 详情 */}
      <Drawer
        width="60%"
        title={detailTitleComputed()}
        onClose={closeDetail}
        open={openDetail}
        destroyOnClose
      >
        <PersonnelDetail
          detailType={detailType}
          close={closeDetail}
          isView={detailType === 'view'}
          userId={selectUserId}
        />
      </Drawer>
      {/* 批量修改授权 */}
      <BatchAuth
        open={openBatchAuth}
        setOpen={(val: boolean) => {
          setOpenBatchAuth(val);
          tableReload();
        }}
        userIds={selectedKeys}
      />
      {/* 授权用户 */}
      {/* 选择角色 */}
      <EditPersonnelAuth
        open={openPersonnelAuth}
        close={() => {
          setOpenPersonnelAuth(false);
          tableReload();
        }}
        userId={selectUserId}
      />
      {/* 重置成功提示 */}
      <Modal
        title="重置成功"
        open={isModalResetOpen}
        onCancel={() => setIsModalResetOpen(false)}
        footer={[
          <Button key="back" onClick={() => setIsModalResetOpen(false)}>
            关闭
          </Button>,
        ]}
      >
        <p>
          所在部门为
          <span className="font-semibold mx-1">
            {nowUserInfo?.dept?.deptName}
          </span>
          的人员
          <span className="font-semibold mx-1">
            {nowUserInfo?.nickName}&nbsp;
          </span>
          ，登录账号
          <span className="font-semibold mx-1">{nowUserInfo?.userName}</span>
          重置初始密码成功！
          <p>
            初始密码为：
            <span className="font-bold">{numCode}</span>
          </p>
        </p>
      </Modal>
    </>
  );
};

export default PersonnelList;
