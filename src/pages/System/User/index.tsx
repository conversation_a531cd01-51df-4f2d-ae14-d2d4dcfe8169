/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Description: 设置 - 组织 - 人员管理
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-01-28 10:55:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-18 14:17:27
 * @FilePath: /xr-qc-jk-web/src/pages/System/User/index.tsx
 */
import React, { useState } from 'react';
import { RightCircleOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import PersonnelList from './components/PersonnelList';
import DeptTree from '@/components/DeptTree';
import PageContainer from '@/components/PageContainer';

const PersonnelManagement: React.FC = () => {
  const [classifyTreeIsShow, setClassifyTreeIsShow] = useState<boolean>(true);

  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  return (
    <PageContainer>
      <div className="relative overflow-hidden">
        <div className="w-full h-full flex flex-row flex-nowrap gap-2">
          {/* 产品让先注释掉 */}
          {/* <div
            className={classnames(
              'relative  border-r border-solid border-r-slate-100 border-l-white border-y-white transition-all duration-150 ease-in',
              classifyTreeIsShow
                ? 'w-[280px] flex-none'
                : 'w-0 border-0 transform -translate-x-[280px] overflow-hidden'
            )}
          >
            {classifyTreeIsShow && (
              <DeptTree
                selectedKeys={selectedKeys}
                setSelectedKeys={setSelectedKeys}
              />
            )}
          </div> */}
          <div className="flex-1 relative overflow-hidden">
            <PersonnelList
              selectedDept={
                selectedKeys.length > 0 ? selectedKeys[0].toString() : ''
              }
            />
            {!classifyTreeIsShow && (
              <div className="absolute top-1/2 -left-4 -translate-y-1/2 cursor-pointer transition-all duration-500 hover:translate-x-3 hover:scale-110">
                <RightCircleOutlined
                  className="text-[18px]"
                  style={{ color: '#afafaf' }}
                  onClick={() => setClassifyTreeIsShow(true)}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default PersonnelManagement;
