/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm } from 'antd';
import { queryDeleteRole, queryRoleList } from '@/api/role';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import RoleData from './components/RoleData';
import RoleDetail from './components/RoleDetail';
import PageContainer from '@/components/PageContainer';
import EProTable from '@/components/EProTable';
import { RoleStatusEnum } from './data';
import { TTableData } from './type';

const RoleManagement: React.FC = () => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  // 请求参数
  const [tableParams, setTableParams] = useState({});

  const [pageSize, setPageSize] = useState<number>(10);

  // 新增角色
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [detailType, setDetailType] = useState<string>('add');
  const [detailRoleId, setDetailRoleId] = useState<number>(-1);

  // 数据权限分配
  const [openAuthUser, setOpenAuthUser] = useState<boolean>(false);
  const [selectedRoleId, setSelectRoleId] = useState<number>(-1);

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
    },
    {
      title: '角色标识',
      dataIndex: 'roleKey',
      key: 'roleKey',
      hideInSearch: true,
    },
    {
      title: '角色状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      fieldProps: {
        options: RoleStatusEnum,
      },
      renderText: (text, record) => (record.status === '0' ? '正常' : '停用'),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => {
            setDetailRoleId(record.roleId);
            setDetailType('view');
            setOpenDetail(true);
          }}
        >
          查看
        </a>,
        <a
          key="edit"
          onClick={() => {
            setDetailRoleId(record.roleId);
            setDetailType('edit');
            setOpenDetail(true);
          }}
        >
          编辑
        </a>,
        <a
          key="auth"
          onClick={() => {
            setSelectRoleId(record.roleId);
            setOpenAuthUser(true);
          }}
        >
          数据权限
        </a>,
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.roleId)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <a className="text-red-500">删除</a>
        </Popconfirm>,
      ],
      key: 'option',
    },
  ];

  /**
   * @TODO 删除角色
   */
  const handleDelete = async (id: number) => {
    try {
      const { code, msg } = await queryDeleteRole(id + '');
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  return (
    <PageContainer>
      <div>
        <EProTable<TTableData>
          className="spec-table"
          scroll={{
            x: true,
          }}
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params: any = {}, sort, filter) => {
            setLoading(true);
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;
            setTableParams(param);
            const { code, rows, total, msg } = await queryRoleList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            setLoading(false);
            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="roleId"
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                };
              }
              return values;
            },
          }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange(current, size) {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setOpenDetail(true);
              }}
            >
              新增角色
            </Button>,
          ]}
          tableAlertRender={false}
          tableAlertOptionRender={false}
        />
      </div>

      {/* 角色详情 */}
      <RoleDetail
        open={openDetail}
        closeDetail={() => {
          setOpenDetail(false);
          tableReload();
        }}
        detailType={detailType}
        detailRoleId={detailRoleId}
      />

      {/* 授权用户 */}
      <RoleData
        open={openAuthUser}
        closeDetail={() => {
          setOpenAuthUser(false);
          tableReload();
        }}
        detailRoleId={selectedRoleId}
      />
    </PageContainer>
  );
};

export default RoleManagement;
