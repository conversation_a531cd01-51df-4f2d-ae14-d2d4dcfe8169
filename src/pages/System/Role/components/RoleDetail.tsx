import { Key, useCallback, useEffect, useRef, useState } from 'react';
import { Col, message, Modal, Row, Skeleton, Tree } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import { queryMenuSelectList } from '@/api/menu';
import {
  ISaveRoleParam,
  queryAddRole,
  queryEditRole,
  queryEditRoleMenu,
  queryRoleDetail,
} from '@/api/role';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { TMenuList } from '../../Menu/type';
import { RoleStatusEnum } from '../data';

const FormInfoInit = {
  roleName: null,
  status: 0,
  menuIds: [],
  roleKey: null,
  roleType: '01',
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
  detailType: string;
  detailRoleId: number;
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  detailType,
  detailRoleId,
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // title
  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增角色';
    } else if (detailType === 'edit') {
      return '编辑角色';
    } else {
      return '角色详情';
    }
  }, [detailType]);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const [permission, setPermission] = useState<Key[]>([0, 2]);

  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [checkedKeys, setCheckedKeys] = useState<any>([]);
  const [treeData, setTreeData] = useState<TMenuList[]>([]);

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
    setCheckedKeys([]);
    setExpandedKeys([]);
    setAutoExpandParent(true);
    setPermission([2]);
  };

  /**
   * @TODO 获取菜单树
   */
  const getMenuTree = async () => {
    try {
      const { code, data, msg } = await queryMenuSelectList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setTreeData(data);
        const allKeys = getAllKeys(data);
        setExpandedKeys(allKeys);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    if (open) {
      getMenuTree();
      getRoleDetail();
    }
  }, [open]);

  /**
   * @TODO
   * tree组件父子联动问题，回显时处理父节点是否展示
   */
  const handelEchoParentNode = (dataSource: TMenuList[], keys: number[]) => {
    let newKeys = [...keys];
    dataSource.forEach((_treeNode) => {
      if (_treeNode.children.length) {
        if (_treeNode.children.some((_i) => _i.children.length)) {
          // console.log(_treeNode.menuId);

          newKeys = handelEchoParentNode(_treeNode.children, newKeys);
        }

        if (
          !_treeNode.children.every((_i) => newKeys.includes(_i.menuId)) &&
          newKeys.includes(_treeNode.menuId)
        ) {
          newKeys = newKeys.filter((_i) => _i !== _treeNode.menuId);
        }
      } else {
      }
    });
    return newKeys;
  };

  /**
   * @TODO 获取角色详情
   */
  const getRoleDetail = async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await queryRoleDetail(detailRoleId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.status = Number(data.status);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
        const {
          code: code2,
          data: data2,
          msg: msg2,
        } = await queryEditRoleMenu(detailRoleId + '');
        if (code2 === codeDefinition.QUERY_SUCCESS) {
          setCheckedKeys(data2.checkedKeys || []);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg2);
        }
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  const onExpand = (expandedKeysValue: Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck = (checked: any) => {
    setCheckedKeys(checked);
  };

  const getAllKeys = (data: any): number[] => {
    let allKeys: number[] = [];
    data.forEach((item: any) => {
      allKeys.push(item.id);
      if (item.children) {
        const keys = getAllKeys(item.children);
        allKeys = [...allKeys, ...keys];
      }
    });
    return allKeys;
  };

  /**
   * @TODO 添加父节点
   */
  const addParentNode = (dataSource: TMenuList[], keys: number[]) => {
    let newKeys = [...keys];
    dataSource.forEach((_treeNode) => {
      if (_treeNode.children && _treeNode.children.length) {
        if (
          _treeNode.children.some((_i) => newKeys.includes(_i.id)) &&
          !newKeys.includes(_treeNode.id)
        ) {
          newKeys.push(_treeNode.id);
        }
        newKeys = addParentNode(_treeNode.children, newKeys);
      }
    });
    return newKeys;
  };

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values };
    let keys = [];
    if (checkedKeys.checked) {
      keys = checkedKeys.checked;
    } else {
      keys = checkedKeys;
    }
    // keys = addParentNode(treeData, keys);
    params.menuIds = keys;
    params.menuCheckStrictly = false;
    setLoading(true);
    try {
      const { code, msg } = await saveFetch(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (params: ISaveRoleParam) => {
    if (detailType === 'add') {
      return queryAddRole(params);
    } else {
      params.roleId = detailRoleId;
      return queryEditRole(params);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title={detailTitleComputed()}
      width="600px"
      style={{
        top: 20,
      }}
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        readonly={detailType === 'view'}
        onFinish={handleSave}
        labelCol={{
          span: 6,
        }}
        wrapperCol={{
          span: 18,
        }}
        grid={true}
        rowProps={{ gutter: [24, 0] }}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormText
          label="角色名称"
          name="roleName"
          rules={[{ required: true, message: '请输入角色名称' }]}
          placeholder="请输入角色名称"
        />
        <ProFormText
          label="角色标识"
          name="roleKey"
          rules={[{ required: true, message: '请输入角色标识' }]}
          placeholder="请输入角色标识"
        />
        <ProFormDigit
          name="roleSort"
          label="排序"
          rules={[{ required: true, message: '请输入' }]}
        />
        <ProFormRadio.Group
          label="角色状态"
          name="status"
          options={RoleStatusEnum}
        />
        <ProFormCheckbox.Group
          label="菜单权限"
          fieldProps={{
            value: permission as any,
            options: [
              { label: '展开/折叠', value: 0 },
              { label: '全选/全不选', value: 1 },
            ],
            onChange: (e: any) => {
              const allKeys = getAllKeys(treeData);
              if (e.includes(0) && !permission.includes(0)) {
                // 全选
                setExpandedKeys(allKeys);
              } else if (!e.includes(0) && permission.includes(0)) {
                // 全不选
                setExpandedKeys([]);
              } else if (e.includes(1) && !permission.includes(1)) {
                // 全展开
                setCheckedKeys(allKeys);
              } else if (!e.includes(1) && permission.includes(1)) {
                // 全折叠
                setCheckedKeys([]);
              }
              setPermission(e);
            },
          }}
        />
      </ProForm>

      <Row className="max-h-[50vh] overflow-auto">
        <Col span={6}></Col>
        <Col span={18}>
          {treeData && treeData.length ? (
            <Tree
              checkable
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              defaultExpandAll={true}
              autoExpandParent={autoExpandParent}
              onCheck={onCheck}
              checkStrictly={true}
              checkedKeys={checkedKeys}
              treeData={treeData}
              fieldNames={{
                children: 'children',
                title: 'label',
                key: 'id',
              }}
              selectable={false}
              disabled={detailType === 'view'}
            />
          ) : (
            <Skeleton active />
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default RoleDetail;
