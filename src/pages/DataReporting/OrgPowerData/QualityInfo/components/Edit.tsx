import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addQualityInfoApi,
  qualityInfoDetailApi,
  updateQualityInfoApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const formRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await qualityInfoDetailApi(id);

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      formRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (open && curSelectedRowId) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);
  const saveFetch = () => {
    if (type === 'add') {
      return addQualityInfoApi;
    } else {
      return updateQualityInfoApi;
    }
  };
  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      const _params = { ...values };
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormText
              name="projectName"
              label="检测项目"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '检测项目必填！' }]}
            />
            <ProFormText
              name="methodAll"
              label="检测方法"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '检测方法必填！' }]}
            />
            <ProFormText
              name="methodShort"
              label="方法简称"
              colProps={{ span: 8 }}
            />
            <ProFormText
              name="certification"
              label="资质"
              colProps={{ span: 8 }}
            />
            <ProFormRadio.Group
              name="isCertification"
              label="是否具备检测资质"
              colProps={{ span: 8 }}
              options={[
                { label: '具备', value: 1 },
                { label: '不具备', value: 0 },
              ]}
              // fieldProps={{
              //   defaultValue: '1',
              // }}
            />
            <ProFormText name="remark" label="备注" colProps={{ span: 8 }} />
          </ProForm>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
