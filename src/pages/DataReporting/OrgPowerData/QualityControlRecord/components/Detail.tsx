import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { qualityControlRecordDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await qualityControlRecordDetailApi(id);

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={close}
      open={open}
      destroyOnClose
      // classNames={{
      //   body: 'bg-[#F5F5F5] !p-0',
      // }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="检测项目">
            {rowDetails?.inspectionItem}
          </Descriptions.Item>
          <Descriptions.Item label="内部质量控制方式">
            {rowDetails?.wayInsideControl}
          </Descriptions.Item>
          <Descriptions.Item label="检测方法">
            {rowDetails?.inspectionMethod}
          </Descriptions.Item>
          <Descriptions.Item label="结果">
            {rowDetails?.result}
          </Descriptions.Item>
          <Descriptions.Item label="提交日期">
            {rowDetails?.submitDate}
          </Descriptions.Item>
          <Descriptions.Item label="报告日期">
            {rowDetails?.reportDate}
          </Descriptions.Item>
          <Descriptions.Item label="完成日期">
            {rowDetails?.finishDate}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
