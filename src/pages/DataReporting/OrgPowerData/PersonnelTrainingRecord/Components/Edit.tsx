import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Drawer, message } from 'antd';
import {
  addPersonnelTrainingRecordApi,
  personnelTrainingRecordDetailApi,
  updatePersonnelTrainingRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const formRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await personnelTrainingRecordDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      formRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    }
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);
  const saveFetch = () => {
    if (type === 'add') {
      return addPersonnelTrainingRecordApi;
    } else {
      return updatePersonnelTrainingRecordApi;
    }
  };
  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      const _params = { ...values };
      const timeKey = ['startTime', 'endTime'];
      timeKey.forEach((item) => {
        if (_params[item]) {
          _params[item] = dayjs(_params[item]).format('YYYY-MM-DD HH:mm:ss');
        }
      });
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormDateTimePicker
              name="startTime"
              label="培训开始时间"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '必填！' }]}
              fieldProps={{
                style: {
                  width: '100%',
                },
              }}
            />
            <ProFormDatePicker
              name="endTime"
              label="培训结束时间"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '培训结束时间必填！' }]}
              fieldProps={{
                style: {
                  width: '100%',
                },
              }}
            />
            <ProFormText
              name="classify"
              label="培训分类"
              colProps={{ span: 8 }}
            />
            <ProFormText
              name="name"
              label="培训名称"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '培训名称必填！' }]}
            />
            <ProFormText
              name="content"
              label="培训内容"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '培训内容必填！' }]}
            />
            <ProFormText
              name="unit"
              label="培训单位（组织单位）"
              colProps={{ span: 8 }}
            />
            <ProFormDigit name="score" label="评分" colProps={{ span: 8 }} />
            <ProFormText
              name="resultEvaluate"
              label="培训效果评价"
              colProps={{ span: 8 }}
            />
          </ProForm>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
