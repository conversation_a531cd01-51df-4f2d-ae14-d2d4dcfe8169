/*
 * @Date: 2024-07-25 17:58:02
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 13:11:25
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\OrgPowerData\TestMember\index.tsx
 * @Description: 检测人员
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { testMemberListApi } from '@/api/DataReporting/api';
import { getDict } from '@/api/dict';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const TestMember: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState('add');

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '人员分类',
      dataIndex: 'peopleClassify',
    },
    {
      title: '人员编号',
      dataIndex: 'peopleNo',
    },
    {
      title: '性别',
      dataIndex: 'sex',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('sys_user_sex');
        if (code === codeDefinition.QUERY_SUCCESS) {
          const _options =
            data && data.length
              ? data.map((item: any) => ({
                  label: item.dictLabel,
                  value: item.dictValue,
                }))
              : [];
          return _options;
        } else {
          message.error(msg);
          return [];
        }
      },
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setEditType('edit');
            setEditOpen(true);
          }}
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            ...params
          };
          delete _params.current
          const { code, data, msg } = await testMemberListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新建
          </Button>,
          <ListImport
            children={
              <Button type="primary" onClick={() => setListImportOpen(true)}>
                导入
              </Button>
            }
            open={listImportOpen}
            setOpen={(val) => {
              tableReload();
              setListImportOpen(val);
            }}
            downLoadUrl={'/data/report/power/person/info/downloadModel'}
            importUrl={
              import.meta.env.VITE_URL + '/report/power/person/info/importDate'
            }
          ></ListImport>,
        ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
      {/* 新增、编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
          setCurSelectedRowId('');
        }}
        type={editType}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default TestMember;
