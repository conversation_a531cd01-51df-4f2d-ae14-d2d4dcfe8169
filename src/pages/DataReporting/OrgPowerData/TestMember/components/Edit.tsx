import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Drawer, message } from 'antd';
import {
  addTestMemberApi,
  testMemberDetailApi,
  updateTestMemberApi,
} from '@/api/DataReporting/api';
import { getDict } from '@/api/dict';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const formRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await testMemberDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      formRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    }
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);
  const saveFetch = () => {
    if (type === 'add') {
      return addTestMemberApi;
    } else {
      return updateTestMemberApi;
    }
  };
  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      const _params = {
        ...values,
      };
      const timeKey = ['birthDate'];
      timeKey.forEach((item) => {
        if (_params[item]) {
          _params[item] = dayjs(_params[item]).format('YYYY-MM-DD HH:mm:ss');
        }
      });
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormText
              name="name"
              label="姓名"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '姓名必填！' }]}
            />
            <ProFormText
              name="peopleClassify"
              label="人员分类"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '人员分类必填！' }]}
            />
            <ProFormText
              name="peopleNo"
              label="人员编号"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '人员编号必填！' }]}
            />
            <ProFormRadio.Group
              name="sex"
              label="性别"
              colProps={{ span: 8 }}
              request={async () => {
                const { code, data, msg } = await getDict('sys_user_sex');
                if (code === codeDefinition.QUERY_SUCCESS) {
                  const _options =
                    data && data.length
                      ? data.map((item: any) => ({
                          label: item.dictLabel,
                          value: item.dictValue,
                        }))
                      : [];
                  return _options;
                } else {
                  message.error(msg);
                  return [];
                }
              }}
              rules={[{ required: true, message: '性别必填！' }]}
            />
            <ProFormDatePicker
              name="birthDate"
              label="出生年月"
              colProps={{ span: 8 }}
              fieldProps={{
                style: {
                  width: '100%',
                },
              }}
            />
            <ProFormDigit name="age" label="年龄" colProps={{ span: 8 }} />
            <ProFormText name="dept" label="所在部门" colProps={{ span: 8 }} />
            <ProFormText name="post" label="现任职务" colProps={{ span: 8 }} />
            <ProFormText name="postCall" label="职称" colProps={{ span: 8 }} />
          </ProForm>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
