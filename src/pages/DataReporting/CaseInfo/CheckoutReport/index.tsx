/*
 * @Date: 2024-08-21 13:01:49
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-27 16:31:53
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\CaseInfo\CheckoutReport\index.tsx
 * @Description: 检验报告
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  checkoutReportListApi,
  inspectionReportListApi,
} from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const CheckoutReport: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);
  const [listItemImportOpen, setListItemImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '检验报告ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '就诊记录类型',
      dataIndex: 'activityTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '就诊流水号',
      dataIndex: 'serialNumber',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '身份证件类别',
      dataIndex: 'idCardTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '身份证件号码',
      dataIndex: 'idCard',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '病房号',
      dataIndex: 'wardNo',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '病区',
      dataIndex: 'wardName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '病床号',
      dataIndex: 'bedNo',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '电子申请单编号',
      dataIndex: 'applicationFormNo',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验申请科室',
      dataIndex: 'applyDeptName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验申请机构',
      dataIndex: 'applyOrgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验申请医师',
      dataIndex: 'applyPhysicianId',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '标本类别',
      dataIndex: 'specimenCategoryName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检验标本号',
      dataIndex: 'specimenNo',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '标本采样日期时间',
      dataIndex: 'specimenSamplingDate',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '接收标本日期时间',
      dataIndex: 'specimenReceivingDate',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '检验医师',
      dataIndex: 'examinationPhysicianId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检验日期',
      dataIndex: 'examinationDate',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检验报告单编号',
      dataIndex: 'examinationReportNo',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验报告结果-客观所见',
      dataIndex: 'examinationObjectiveDesc',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '检验报告结果-主观提示',
      dataIndex: 'examinationSubjectiveDesc',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '检验报告备注',
      dataIndex: 'examinationNotes',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验报告日期',
      dataIndex: 'examinationReportDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报告医师',
      dataIndex: 'examinationReportId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检验报告机构',
      dataIndex: 'orgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验报告科室',
      dataIndex: 'deptName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作人ID',
      dataIndex: 'operatorId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>
      ),
      fixed: 'right',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await checkoutReportListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
        toolBarRender={() => [
          <ListImport
            children={
              <Button type="primary" onClick={() => setListImportOpen(true)}>
                导入检验报告
              </Button>
            }
            open={listImportOpen}
            setOpen={(val) => {
              tableReload();
              setListImportOpen(val);
            }}
            downLoadUrl={'/data/emrEx/lab/downloadModel'}
            importUrl={import.meta.env.VITE_URL + '/emrEx/lab/importDate'}
          ></ListImport>,
          <ListImport
            children={
              <Button
                type="primary"
                onClick={() => setListItemImportOpen(true)}
              >
                导入检验报告项目
              </Button>
            }
            open={listItemImportOpen}
            setOpen={(val) => {
              tableReload();
              setListItemImportOpen(val);
            }}
            downLoadUrl={'/data/emrExLab/item/downloadModel'}
            importUrl={
              import.meta.env.VITE_URL + '/emrExLab/item/importDate'
            }
          ></ListImport>,
        ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val)
          setCurSelectedRowId('')
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default CheckoutReport;
