import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { infectiousDiseaseReportCardDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async () => {
    try {
      const { code, data, msg } = await infectiousDiseaseReportCardDetailApi(
        curSelectedRowId
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId) queryDetailsById();
  }, [curSelectedRowId]);

  return (
    <Drawer width="80%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="传报卡ID">
            {rowDetails?.id}
          </Descriptions.Item>
          <Descriptions.Item label="患者基本信息ID">
            {rowDetails?.patientId}
          </Descriptions.Item>
          <Descriptions.Item label="就诊流水号">
            {rowDetails?.serialNumber}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗活动类型代码">
            {rowDetails?.activityTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗活动类型名称">
            {rowDetails?.activityTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="患者姓名">
            {rowDetails?.patientName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别代码">
            {rowDetails?.idCardTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别名称">
            {rowDetails?.idCardTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件号码">
            {rowDetails?.idCard}
          </Descriptions.Item>
          <Descriptions.Item label="性别代码">
            {rowDetails?.genderCode}
          </Descriptions.Item>
          <Descriptions.Item label="性别名称">
            {rowDetails?.genderName}
          </Descriptions.Item>
          <Descriptions.Item label="出生日期">
            {rowDetails?.birthDate}
          </Descriptions.Item>
          <Descriptions.Item label="国籍/地区代码">
            {rowDetails?.nationalityCode}
          </Descriptions.Item>
          <Descriptions.Item label="国籍/地区名称">
            {rowDetails?.nationalityName}
          </Descriptions.Item>
          <Descriptions.Item label="民族代码">
            {rowDetails?.nationCode}
          </Descriptions.Item>
          <Descriptions.Item label="民族名称">
            {rowDetails?.nationName}
          </Descriptions.Item>
          <Descriptions.Item label="户籍地址代码">
            {rowDetails?.permanentAddrCode}
          </Descriptions.Item>
          <Descriptions.Item label="户籍地址名称">
            {rowDetails?.permanentAddrName}
          </Descriptions.Item>
          <Descriptions.Item label="户籍详细地址">
            {rowDetails?.permanentAddrDetail}
          </Descriptions.Item>
          <Descriptions.Item label="现住地区代码">
            {rowDetails?.currentAddrCode}
          </Descriptions.Item>
          <Descriptions.Item label="现住地区名称">
            {rowDetails?.currentAddrName}
          </Descriptions.Item>
          <Descriptions.Item label="现住详细地址">
            {rowDetails?.currentAddrDetail}
          </Descriptions.Item>
          <Descriptions.Item label="工作单位/学校名称">
            {rowDetails?.workunit}
          </Descriptions.Item>
          <Descriptions.Item label="婚姻状况代码">
            {rowDetails?.maritalStatusCode}
          </Descriptions.Item>
          <Descriptions.Item label="婚姻状况名称">
            {rowDetails?.maritalStatusName}
          </Descriptions.Item>
          <Descriptions.Item label="学历代码">
            {rowDetails?.educationCode}
          </Descriptions.Item>
          <Descriptions.Item label="学历名称">
            {rowDetails?.educationName}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类代码">
            {rowDetails?.nultitudeTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类名称">
            {rowDetails?.nultitudeTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类其他">
            {rowDetails?.nultitudeTypeOther}
          </Descriptions.Item>
          <Descriptions.Item label="患者电话号码">
            {rowDetails?.tel}
          </Descriptions.Item>
          <Descriptions.Item label="联系人/监护人姓名">
            {rowDetails?.contacts}
          </Descriptions.Item>
          <Descriptions.Item label="联系人/监护人电话号码">
            {rowDetails?.contactsTel}
          </Descriptions.Item>
          <Descriptions.Item label="发病日期">
            {rowDetails?.onsetDate}
          </Descriptions.Item>
          <Descriptions.Item label="诊断时间">
            {rowDetails?.diagnoseTime}
          </Descriptions.Item>
          <Descriptions.Item label="疾病诊断代码">
            {rowDetails?.diseaseCode}
          </Descriptions.Item>
          <Descriptions.Item label="疾病诊断名称">
            {rowDetails?.diseaseName}
          </Descriptions.Item>
          <Descriptions.Item label="其他具体疾病名称/病毒分型">
            {rowDetails?.diseaseOther}
          </Descriptions.Item>
          <Descriptions.Item label="病例分类代码">
            {rowDetails?.diagnoseStateCode}
          </Descriptions.Item>
          <Descriptions.Item label="病例分类名称">
            {rowDetails?.diagnoseStateName}
          </Descriptions.Item>
          <Descriptions.Item label="诊断状态代码">
            {rowDetails?.caseTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="诊断状态名称">
            {rowDetails?.caseTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="死亡日期">
            {rowDetails?.deadDate}
          </Descriptions.Item>
          <Descriptions.Item label="死亡是否与此病有关代码">
            {rowDetails?.isDeadByThisCode}
          </Descriptions.Item>
          <Descriptions.Item label="死亡是否与此病有关名称">
            {rowDetails?.isDeadByThisName}
          </Descriptions.Item>
          <Descriptions.Item label="直接死亡诊断编码">
            {rowDetails?.symptomsCode}
          </Descriptions.Item>
          <Descriptions.Item label="直接死亡诊断名称">
            {rowDetails?.symptomsName}
          </Descriptions.Item>
          <Descriptions.Item label="实验室检测结论代码">
            {rowDetails?.laboratoryDetectionVerdictCode}
          </Descriptions.Item>
          <Descriptions.Item label="实验室检测结论名称">
            {rowDetails?.laboratoryDetectionVerdictName}
          </Descriptions.Item>
          <Descriptions.Item label="确认（替代策略、核酸）检测阳性日期">
            {rowDetails?.detectionPositiveDate}
          </Descriptions.Item>
          <Descriptions.Item label="确认（替代策略、核酸）检测单位">
            {rowDetails?.detectionOrgCode}
          </Descriptions.Item>
          <Descriptions.Item label="艾滋实验室确诊日期">
            {rowDetails?.dtDiagnose}
          </Descriptions.Item>
          <Descriptions.Item label="病人所属地类型代码">
            {rowDetails?.afpAreatype1Code}
          </Descriptions.Item>
          <Descriptions.Item label="病人所属地类型名称">
            {rowDetails?.afpAreatype1Name}
          </Descriptions.Item>
          <Descriptions.Item label="麻痹日期">
            {rowDetails?.afpPalsyDate}
          </Descriptions.Item>
          <Descriptions.Item label="就诊日期">
            {rowDetails?.afpDoctorDate}
          </Descriptions.Item>
          <Descriptions.Item label="就诊地址类型代码">
            {rowDetails?.afpAreatype2Code}
          </Descriptions.Item>
          <Descriptions.Item label="就诊地址类型名称">
            {rowDetails?.afpAreatype2Name}
          </Descriptions.Item>
          <Descriptions.Item label="就诊地址编码">
            {rowDetails?.afpAddrcodeCode}
          </Descriptions.Item>
          <Descriptions.Item label="就诊地址名称">
            {rowDetails?.afpAddrcodeName}
          </Descriptions.Item>
          <Descriptions.Item label="就诊地址">
            {rowDetails?.afpAddr}
          </Descriptions.Item>
          <Descriptions.Item label="麻痹症状">
            {rowDetails?.afpPalsySymptom}
          </Descriptions.Item>
          <Descriptions.Item label="报告日期">
            {rowDetails?.reportDate}
          </Descriptions.Item>
          <Descriptions.Item label="发现方式代码">
            {rowDetails?.discoveryModeCode}
          </Descriptions.Item>
          <Descriptions.Item label="发现方式名称">
            {rowDetails?.discoveryModeName}
          </Descriptions.Item>
          <Descriptions.Item label="发现方式其他">
            {rowDetails?.discoveryModeOther}
          </Descriptions.Item>
          <Descriptions.Item label="性病史代码">
            {rowDetails?.venerealDisCode}
          </Descriptions.Item>
          <Descriptions.Item label="性病史名称">
            {rowDetails?.venerealDisName}
          </Descriptions.Item>
          <Descriptions.Item label="感染途径代码">
            {rowDetails?.bsTransmissionCode}
          </Descriptions.Item>
          <Descriptions.Item label="感染途径名称">
            {rowDetails?.bsTransmissionName}
          </Descriptions.Item>
          <Descriptions.Item label="感染途径其他">
            {rowDetails?.bsTransmissionOther}
          </Descriptions.Item>
          <Descriptions.Item label="接触方式代码">
            {rowDetails?.contactTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="接触方式名称">
            {rowDetails?.contactTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="注射毒品史与病人共用过注射器的人数">
            {rowDetails?.injectCount}
          </Descriptions.Item>
          <Descriptions.Item label="非婚异性性接触史与病人有非婚性行为的人数">
            {rowDetails?.nonwebCount}
          </Descriptions.Item>
          <Descriptions.Item label="男男性行为史发生同性性行为的人数">
            {rowDetails?.smCount}
          </Descriptions.Item>
          <Descriptions.Item label="接触史其他">
            {rowDetails?.contactOther}
          </Descriptions.Item>
          <Descriptions.Item label="生殖道沙眼衣原体感染代码">
            {rowDetails?.sinfectCode}
          </Descriptions.Item>
          <Descriptions.Item label="生殖道沙眼衣原体感染名称">
            {rowDetails?.sinfectName}
          </Descriptions.Item>
          <Descriptions.Item label="是否重症代码">
            {rowDetails?.serverityCode}
          </Descriptions.Item>
          <Descriptions.Item label="是否重症名称">
            {rowDetails?.serverityName}
          </Descriptions.Item>
          <Descriptions.Item label="手足口病实验室结果代码">
            {rowDetails?.labResultCode}
          </Descriptions.Item>
          <Descriptions.Item label="手足口病实验室结果名称">
            {rowDetails?.labResultName}
          </Descriptions.Item>
          <Descriptions.Item label="乙肝HBsAg阳性时间代码">
            {rowDetails?.hbsagCode}
          </Descriptions.Item>
          <Descriptions.Item label="乙肝HBsAg阳性时间名称">
            {rowDetails?.hbsagName}
          </Descriptions.Item>
          <Descriptions.Item label="首次出现乙肝症状和体征时间">
            {rowDetails?.hbsagFirst}
          </Descriptions.Item>
          <Descriptions.Item label="无症状/不详">
            {rowDetails?.hbsagBuxiang}
          </Descriptions.Item>
          <Descriptions.Item label="乙肝本次ALT">
            {rowDetails?.hbsagAlt}
          </Descriptions.Item>
          <Descriptions.Item label="抗 -HBcIgM1：1000 检测结果代码">
            {rowDetails?.hbcigResultCode}
          </Descriptions.Item>
          <Descriptions.Item label="抗 -HBcIgM1：1000 检测结果名称">
            {rowDetails?.hbcigResultName}
          </Descriptions.Item>
          <Descriptions.Item label="肝穿结果（急慢性）代码">
            {rowDetails?.hbliverPunctureCode}
          </Descriptions.Item>
          <Descriptions.Item label="肝穿结果（急慢性）名称">
            {rowDetails?.hbliverPunctureName}
          </Descriptions.Item>
          <Descriptions.Item label="恢复期血清HBsAg阴转，抗HBs阳转">
            {rowDetails?.hbsagChangeCode}
          </Descriptions.Item>
          <Descriptions.Item label="恢复期血清HBsAg阴转，抗HBs阳转">
            {rowDetails?.hbsagChangeName}
          </Descriptions.Item>
          <Descriptions.Item label="亲密接触者有无同症状代码">
            {rowDetails?.contactflagCode}
          </Descriptions.Item>
          <Descriptions.Item label="亲密接触者有无同症名称">
            {rowDetails?.contactflagName}
          </Descriptions.Item>
          <Descriptions.Item label="填卡医生">
            {rowDetails?.fillDoctor}
          </Descriptions.Item>
          <Descriptions.Item label="备注">
            {rowDetails?.notes}
          </Descriptions.Item>
          <Descriptions.Item label="新冠临床严重程度代码">
            {rowDetails?.ncvSeverityCode}
          </Descriptions.Item>
          <Descriptions.Item label="新冠临床严重程度名称">
            {rowDetails?.ncvSeverityName}
          </Descriptions.Item>
          <Descriptions.Item label="输入病例类型代码">
            {rowDetails?.foreignTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="输入病例类型名称">
            {rowDetails?.foreignTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="输入来源地代码">
            {rowDetails?.placeCode}
          </Descriptions.Item>
          <Descriptions.Item label="输入来源地名称">
            {rowDetails?.placeName}
          </Descriptions.Item>
          <Descriptions.Item label="报告地区代码">
            {rowDetails?.reportZoneCode}
          </Descriptions.Item>
          <Descriptions.Item label="报告地区名称">
            {rowDetails?.reportZoneName}
          </Descriptions.Item>
          <Descriptions.Item label="报告单位机构代码">
            {rowDetails?.reportOrgCode}
          </Descriptions.Item>
          <Descriptions.Item label="报告单位机构名称">
            {rowDetails?.reportOrgName}
          </Descriptions.Item>
          <Descriptions.Item label="科室代码">
            {rowDetails?.deptCode}
          </Descriptions.Item>
          <Descriptions.Item label="科室名称">
            {rowDetails?.deptName}
          </Descriptions.Item>
          <Descriptions.Item label="操作人ID">
            {rowDetails?.operatorId}
          </Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {rowDetails?.operationTime}
          </Descriptions.Item>


          <Descriptions.Item label="数据完整性校验结果">
            {rowDetails?.msg}
          </Descriptions.Item>

          

        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
