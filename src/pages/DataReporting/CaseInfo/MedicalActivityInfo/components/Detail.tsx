import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import {
  deviceCalibrateRecordDetailApi,
  getReportPreparationRecordDetailsById,
  medicalActivityInfoDetailApi,
} from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async () => {
    try {
      const { code, data, msg } = await medicalActivityInfoDetailApi(
        curSelectedRowId
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    }
  };

  useEffect(() => {
    if (open && curSelectedRowId) queryDetailsById();
  }, [open, curSelectedRowId]);

  return (
    <Drawer width="80%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="ID">{rowDetails?.id}</Descriptions.Item>
          <Descriptions.Item label="患者基本信息ID">
            {rowDetails?.patientId}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗活动类型代码">
            {rowDetails?.activityTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗活动类型名称">
            {rowDetails?.activityTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="就诊流水号">
            {rowDetails?.serialNumber}
          </Descriptions.Item>
          <Descriptions.Item label="诊疗活动发生日期时间">
            {rowDetails?.activityTime}
          </Descriptions.Item>
          <Descriptions.Item label="患者姓名">
            {rowDetails?.patientName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别代码">
            {rowDetails?.idCardTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别名称">
            {rowDetails?.idCardTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件号码">
            {rowDetails?.idCard}
          </Descriptions.Item>
          <Descriptions.Item label="主诉">
            {rowDetails?.chiefComplaint}
          </Descriptions.Item>
          <Descriptions.Item label="现病史/入院情况">
            {rowDetails?.presentIllnessHis}
          </Descriptions.Item>
          <Descriptions.Item label="体格检查">
            {rowDetails?.physicalExamination}
          </Descriptions.Item>
          <Descriptions.Item label="辅助检查">
            {rowDetails?.studiesSummaryResult}
          </Descriptions.Item>
          <Descriptions.Item label="疾病诊断日期">
            {rowDetails?.diagnoseTime}
          </Descriptions.Item>
          <Descriptions.Item label="传染病诊断代码">
            {rowDetails?.diseaseCode}
          </Descriptions.Item>
          <Descriptions.Item label="传染病诊断名称">
            {rowDetails?.diseaseName}
          </Descriptions.Item>
          <Descriptions.Item label="西医疾病诊断编码">
            {rowDetails?.wmDiseaseCode}
          </Descriptions.Item>
          <Descriptions.Item label="西医疾病诊断名称">
            {rowDetails?.wmDiseaseName}
          </Descriptions.Item>
          <Descriptions.Item label="中医病名代码">
            {rowDetails?.tcmDiseaseCode}
          </Descriptions.Item>
          <Descriptions.Item label="中医病名名称">
            {rowDetails?.tcmDiseaseName}
          </Descriptions.Item>
          <Descriptions.Item label="中医证候编码">
            {rowDetails?.tcmSyndromeCode}
          </Descriptions.Item>
          <Descriptions.Item label="中医证候名称">
            {rowDetails?.tcmSyndromeName}
          </Descriptions.Item>
          <Descriptions.Item label="诊断医生">
            {rowDetails?.fillDoctor}
          </Descriptions.Item>
          <Descriptions.Item label="科室代码">
            {rowDetails?.deptCode}
          </Descriptions.Item>
          <Descriptions.Item label="科室名称">
            {rowDetails?.deptName}
          </Descriptions.Item>
          <Descriptions.Item label="医疗机构代码">
            {rowDetails?.orgCode}
          </Descriptions.Item>
          <Descriptions.Item label="医疗机构名称">
            {rowDetails?.orgName}
          </Descriptions.Item>
          <Descriptions.Item label="操作人ID">
            {rowDetails?.operatorId}
          </Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {rowDetails?.operationTime}
          </Descriptions.Item>
          <Descriptions.Item label="数据完整性校验结果">
            {rowDetails?.msg}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
