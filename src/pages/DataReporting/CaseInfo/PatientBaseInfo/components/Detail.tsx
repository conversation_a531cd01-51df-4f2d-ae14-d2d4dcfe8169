import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { patientBaseInfoDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async () => {
    try {
      const { code, data, msg } = await patientBaseInfoDetailApi(
        curSelectedRowId
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    }
  };

  useEffect(() => {
    if (open && curSelectedRowId) queryDetailsById();
  }, [open, curSelectedRowId]);

  return (
    <Drawer width="80%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="ID">{rowDetails?.id}</Descriptions.Item>
          <Descriptions.Item label="患者姓名">
            {rowDetails?.patientName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别代码">
            {rowDetails?.idCardTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件类别名称">
            {rowDetails?.idCardTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="身份证件号码">
            {rowDetails?.idCard}
          </Descriptions.Item>
          <Descriptions.Item label="性别代码">
            {rowDetails?.genderCode}
          </Descriptions.Item>
          <Descriptions.Item label="性别名称">
            {rowDetails?.genderName}
          </Descriptions.Item>
          <Descriptions.Item label="出生日期">
            {rowDetails?.birthDate}
          </Descriptions.Item>
          <Descriptions.Item label="国籍/地区代码">
            {rowDetails?.nationalityCode}
          </Descriptions.Item>
          <Descriptions.Item label="国籍/地区名称">
            {rowDetails?.nationalityName}
          </Descriptions.Item>
          <Descriptions.Item label="民族代码">
            {rowDetails?.nationCode}
          </Descriptions.Item>
          <Descriptions.Item label="民族名称">
            {rowDetails?.nationName}
          </Descriptions.Item>
          <Descriptions.Item label="户籍地址代码">
            {rowDetails?.permanentAddrCode}
          </Descriptions.Item>
          <Descriptions.Item label="户籍地址名称">
            {rowDetails?.permanentAddrName}
          </Descriptions.Item>
          <Descriptions.Item label="户籍详细地址">
            {rowDetails?.permanentAddrDetail}
          </Descriptions.Item>
          <Descriptions.Item label="现住地区代码">
            {rowDetails?.currentAddrCode}
          </Descriptions.Item>
          <Descriptions.Item label="现住地区名称">
            {rowDetails?.currentAddrName}
          </Descriptions.Item>
          <Descriptions.Item label="现住详细地址">
            {rowDetails?.currentAddrDetail}
          </Descriptions.Item>
          <Descriptions.Item label="工作单位/学校名称">
            {rowDetails?.workunit}
          </Descriptions.Item>
          <Descriptions.Item label="婚姻状况代码">
            {rowDetails?.maritalStatusCode}
          </Descriptions.Item>
          <Descriptions.Item label="婚姻状况名称">
            {rowDetails?.maritalStatusName}
          </Descriptions.Item>
          <Descriptions.Item label="学历代码">
            {rowDetails?.educationCode}
          </Descriptions.Item>
          <Descriptions.Item label="学历名称">
            {rowDetails?.educationName}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类代码">
            {rowDetails?.nultitudeTypeCode}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类名称">
            {rowDetails?.nultitudeTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="人群分类其他">
            {rowDetails?.nultitudeTypeOther}
          </Descriptions.Item>
          <Descriptions.Item label="患者电话号码">
            {rowDetails?.tel}
          </Descriptions.Item>
          <Descriptions.Item label="联系人/监护人姓名">
            {rowDetails?.contacts}
          </Descriptions.Item>
          <Descriptions.Item label="联系人/监护人电话号码">
            {rowDetails?.contactsTel}
          </Descriptions.Item>
          <Descriptions.Item label="医疗机构代码">
            {rowDetails?.orgCode}
          </Descriptions.Item>
          <Descriptions.Item label="医疗机构名称">
            {rowDetails?.orgName}
          </Descriptions.Item>
          <Descriptions.Item label="操作人ID">
            {rowDetails?.operatorId}
          </Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {rowDetails?.operationTime}
          </Descriptions.Item>

          <Descriptions.Item label="数据完整性校验结果">
            {rowDetails?.msg}
          </Descriptions.Item>

        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
