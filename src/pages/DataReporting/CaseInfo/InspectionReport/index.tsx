/*
 * @Date: 2024-08-21 09:15:15
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 13:17:04
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\CaseInfo\InspectionReport\index.tsx
 * @Description: 检查报告
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  deviceCalibrateRecordListApi,
  inspectionReportListApi,
} from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const InspectionReport: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);
  const [listItemImportOpen, setListItemImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '检查报告ID',
      dataIndex: 'id',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '就诊记录类型',
      dataIndex: 'activityTypeName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '就诊流水号',
      dataIndex: 'serialNumber',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '身份证件类别',
      dataIndex: 'idCardTypeName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '身份证件号码',
      dataIndex: 'idCard',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '病区',
      dataIndex: 'wardName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '病房号',
      dataIndex: 'wardNo',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '病床号',
      dataIndex: 'bedNo',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '电子申请单编号',
      dataIndex: 'applicationFormNo',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查申请机构',
      dataIndex: 'applyOrgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查申请科室',
      dataIndex: 'applyDeptName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '症状开始时间',
      dataIndex: 'symptomStartDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '症状结束时间',
      dataIndex: 'symptomEndDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '症状描述',
      dataIndex: 'symptomDesc',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '诊疗过程描述',
      dataIndex: 'treatmentDesc',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '特殊检查标志',
      dataIndex: 'specialExaminationCode',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查类别',
      dataIndex: 'examinationTypeName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检查报告结果-客观所见',
      dataIndex: 'examinationObjectiveDesc',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '检查报告结果-主观提示',
      dataIndex: 'examinationSubjectiveDesc',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '检查报告备注',
      dataIndex: 'examinationNotes',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查报告单编号',
      dataIndex: 'examinationReportNo',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查报告日期',
      dataIndex: 'examinationReportDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报告医师',
      dataIndex: 'examinationReportId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '检查报告机构',
      dataIndex: 'orgName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检查报告科室',
      dataIndex: 'deptName',
      hideInSearch: true,
      width: 120,
    },
    // {
    //   title: '检查项目',
    //   dataIndex: 'itemName',
    //   hideInSearch: true,
    //   width: 100,
    // },
    // {
    //   title: '检查结果',
    //   dataIndex: 'examinationResultName',
    //   hideInSearch: true,
    //   width: 100,
    // },
    // {
    //   title: '检查定量结果',
    //   dataIndex: 'examinationQuantification',
    //   hideInSearch: true,
    //   width: 120,
    // },
    // {
    //   title: '检查定量结果计量单位',
    //   dataIndex: 'examinationQuantificationUnit',
    //   hideInSearch: true,
    //   width: 180,
    // },
    {
      title: '操作人ID',
      dataIndex: 'operatorId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>
      ),
      fixed: 'right',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await inspectionReportListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
        toolBarRender={() => [
          <ListImport
            children={
              <Button type="primary" onClick={() => setListImportOpen(true)}>
                导入检查报告
              </Button>
            }
            open={listImportOpen}
            setOpen={(val) => {
              tableReload();
              setListImportOpen(val);
            }}
            downLoadUrl={'/data/emrEx/clinical/downloadModel'}
            importUrl={import.meta.env.VITE_URL + '/emrEx/clinical/importDate'}
          ></ListImport>,
          <ListImport
            children={
              <Button
                type="primary"
                onClick={() => setListItemImportOpen(true)}
              >
                导入检查报告项目
              </Button>
            }
            open={listItemImportOpen}
            setOpen={(val) => {
              tableReload();
              setListItemImportOpen(val);
            }}
            downLoadUrl={'/data/emrExClinical/item/downloadModel'}
            importUrl={
              import.meta.env.VITE_URL + '/emrExClinical/item/importDate'
            }
          ></ListImport>,
        ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val)
          setCurSelectedRowId('')
        }}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default InspectionReport;
