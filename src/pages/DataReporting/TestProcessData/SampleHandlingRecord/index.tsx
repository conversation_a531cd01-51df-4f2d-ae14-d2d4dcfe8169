/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Date: 2024-07-17 14:31:31
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-11 11:01:36
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\TestProcessData\SampleHandlingRecord\index.tsx
 * @Description: 样本处置记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSampleHandlingRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';

const SampleHandlingRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState<string>('');
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState('add');

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '处理样品数量',
      dataIndex: 'disposeSampleNumber',
      hideInSearch: true,
    },
    {
      title: '处理原因',
      dataIndex: 'disposeReason',
    },
    {
      title: '处理日期',
      dataIndex: 'disposeDate',
      hideInSearch: true,
    },
    {
      title: '处理日期',
      key: 'disposeDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '处理方式',
      dataIndex: 'disposeWay',
    },
    {
      title: '处理人',
      dataIndex: 'disposePeople',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'orgName',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setEditType('edit');
            setEditOpen(true);
          }}
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.applyDate && _params.applyDate.length) {
            _params.applyStartDate = _params.applyDate[0];
            _params.applyEndDate = _params.applyDate[1];
            delete _params.applyDate;
          }
          if (params.disposeDate && _params.disposeDate.length) {
            _params.disposeStartDate = _params.disposeDate[0];
            _params.disposeEndDate = _params.disposeDate[1];
            delete _params.disposeDate;
          }
          const { code, data, msg } = await getSampleHandlingRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新建
          </Button>,
          <ListImport
            children={
              <Button type="primary" onClick={() => setListImportOpen(true)}>
                导入
              </Button>
            }
            open={listImportOpen}
            setOpen={(val) => {
              tableReload();
              setListImportOpen(val);
            }}
            downLoadUrl={'/data/report/process/sampleDisposeRecord/downloadModel'}
            importUrl={
              import.meta.env.VITE_URL +
              '/report/process/sampleDisposeRecord/importDate'
            }
          ></ListImport>,
        ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
      {/* 新增、编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
          setCurSelectedRowId('')
        }}
        type={editType}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SampleHandlingRecord;
