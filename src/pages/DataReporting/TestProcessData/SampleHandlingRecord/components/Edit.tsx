import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addSampleHandingRecordApi,
  getSampleHandlingRecordDetailsById,
  updateSampleHandingRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const baseFormRef = useRef<ProFormInstance>(null);
  const applyFormRef = useRef<ProFormInstance>(null);
  const disposeFormRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getSampleHandlingRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseFormRef.current?.setFieldsValue(data);
      applyFormRef.current?.setFieldsValue(data);
      disposeFormRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);

  const saveFetch = () => {
    if (type === 'add') {
      return addSampleHandingRecordApi;
    } else {
      return updateSampleHandingRecordApi;
    }
  };

  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const baseVal = await baseFormRef.current?.validateFields();
      const applyVal = await applyFormRef.current?.validateFields();
      const disposeVal = await disposeFormRef.current?.validateFields();
      const _params = {
        ...baseVal,
        ...applyVal,
        ...disposeVal,
      };
      const timeKey = ['applyDate', 'disposeDate'];
      timeKey.forEach((item) => {
        if (_params[item]) {
          _params[item] = dayjs(_params[item]).format('YYYY-MM-DD HH:mm:ss');
        }
      });
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto flex flex-col gap-4">
          <BlockContainer title="样品基本信息">
            <ProForm
              className="p-6"
              formRef={baseFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleName"
                label="样品名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品名称必填！' }]}
              />
              <ProFormText
                name="sampleTaskNumber"
                label="样品编号"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品编号必填！' }]}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="处置申请信息">
            <ProForm
              className="p-6"
              formRef={applyFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="applyPeople"
                label="申请人"
                colProps={{ span: 8 }}
              />
              <ProFormDatePicker
                name="applyDate"
                label="申请日期"
                colProps={{ span: 8 }}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
              <ProFormDigit
                name="disposeSampleNumber"
                label="处理样品数量"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '处理样品数量必填！' }]}
              />
              <ProFormText
                name="storageDeadline"
                label="存储期限"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="disposeReason"
                label="处理原因"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '处理原因必填！' }]}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="处置信息">
            <ProForm
              className="p-6"
              formRef={disposeFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormDatePicker
                name="disposeDate"
                label="处理日期"
                colProps={{ span: 8 }}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
                rules={[{ required: true, message: '处理日期必填！' }]}
              />
              <ProFormText
                name="disposeWay"
                label="处理方式"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '处理方式必填！' }]}
              />
              <ProFormText
                name="disposePeople"
                label="处理人"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '处理人必填！' }]}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
