import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addSampleReceiptRecordApi,
  getSampleReceiptRecordDetailsById,
  updateSampleReceiptRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const baseFormRef = useRef<ProFormInstance>(null);
  const acceptFormRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getSampleReceiptRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseFormRef.current?.setFieldsValue(data);
      acceptFormRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);
  const saveFetch = () => {
    if (type === 'add') {
      return addSampleReceiptRecordApi;
    } else {
      return updateSampleReceiptRecordApi;
    }
  };
  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const baseVal = await baseFormRef.current?.validateFields();
      const samplingVal = await acceptFormRef.current?.validateFields();
      const _params = {
        ...baseVal,
        ...samplingVal,
      };
      if (_params.acceptDate) {
        _params.acceptDate = dayjs(_params.acceptDate).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto flex flex-col gap-4">
          <BlockContainer title="样品基本信息">
            <ProForm
              className="p-6"
              formRef={baseFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleName"
                label="样品名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品名称必填！' }]}
              />
              <ProFormText
                name="sampleTaskNumber"
                label="样品编号"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品编号必填！' }]}
              />

              <ProFormText
                name="sampleStatus"
                label="样本状态"
                colProps={{ span: 8 }}
              />

            </ProForm>
          </BlockContainer>
          <BlockContainer title="接样信息">
            <ProForm
              className="p-6"
              formRef={acceptFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormDatePicker
                name="acceptDate"
                label="接收日期"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '接收日期必填！' }]}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
              <ProFormText
                name="acceptPlace"
                label="接收场所"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="acceptLocation"
                label="接收位置"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '接收位置必填！' }]}
              />
              <ProFormText
                name="acceptPeople"
                label="接样人"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '接样人必填！' }]}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" loading={btnLoading} onClick={handleSave}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
