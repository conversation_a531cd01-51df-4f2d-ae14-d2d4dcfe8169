/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Date: 2024-07-17 10:54:22
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-08 17:35:18
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\TestProcessData\SampleReceiptRecord\index.tsx
 * @Description: 样本接收记录
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getSampleReceiptRecordDataList } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import ListImport from '@/components/ListImport';
import PageContainer from '@/components/PageContainer';
import Edit from './components/Edit';
import DownloadButton from '@/components/DownloadButton';

const SampleReceiptRecord: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [listImportOpen, setListImportOpen] = useState(false);

  const actionRef = useRef<ActionType>();

  // 当前选中的行数据id
  const [curSelectedRowId, setCurSelectedRowId] = useState('');
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState('add');

  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleTaskNumber',
    },
    {
      title: '接收时间',
      dataIndex: 'acceptDate',
      hideInSearch: true,
    },
    {
      title: '接收时间',
      key: 'acceptDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '接收单位',
      dataIndex: 'acceptLocation',
      // hideInSearch: true,
    },
    {
      title: '接收人员',
      dataIndex: 'acceptPeople',
      hideInSearch: true,
    },
    {
      title: '发送单位',
      dataIndex: 'orgName',
    },


    {
      title: '样本状态',
      hideInSearch: true,
      dataIndex: 'sampleStatus',
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setEditType('edit');
            setEditOpen(true);
          }}
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectedRowId(record?.id);
            setDetailOpen(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete _params.current;
          if (_params.acceptDate && _params.acceptDate.length) {
            _params.acceptStartDate = _params.acceptDate[0];
            _params.acceptEndDate = _params.acceptDate[1];
            delete _params.acceptDate;
          }
          setSearchParams(_params);
          const { code, data, msg } = await getSampleReceiptRecordDataList(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新建
          </Button>,
          <ListImport
            children={
              <Button type="primary" onClick={() => setListImportOpen(true)}>
                导入
              </Button>
            }
            open={listImportOpen}
            setOpen={(val) => {
              tableReload();
              setListImportOpen(val);
            }}
            downLoadUrl={'/data/report/process/sampleAcceptRecord/downloadModel'}
            importUrl={
              import.meta.env.VITE_URL +
              '/report/process/sampleAcceptRecord/importDate'
            }
          ></ListImport>,

           <DownloadButton
              url="/data/report/process/sampleAcceptRecord/exportAcceptRecord"
              params={searchParams}
              method="post"
            >
              导出
            </DownloadButton> ,



        ]}
      />
      {/* 详情 */}
      <Detail
        open={detailOpen}
        setOpen={(val) => {
          setDetailOpen(val);
          setCurSelectedRowId('');
        }}
        curSelectedRowId={curSelectedRowId}
      />
      {/* 新增、编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
          setCurSelectedRowId('')
        }}
        type={editType}
        curSelectedRowId={curSelectedRowId}
      />
    </PageContainer>
  );
};

export default SampleReceiptRecord;
