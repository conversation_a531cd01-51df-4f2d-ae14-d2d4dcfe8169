import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addSampleCollectionRecordApi,
  getSampleCollectionRecordDetailsById,
  updateSampleCollectionRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import { phoneReg } from '@/utils/validators';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const baseFormRef = useRef<ProFormInstance>(null);
  const samplingFormRef = useRef<ProFormInstance>(null);
  const checkFormRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getSampleCollectionRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseFormRef.current?.setFieldsValue(data);
      samplingFormRef.current?.setFieldsValue(data);
      checkFormRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);

  const saveFetch = () => {
    if (type === 'add') {
      return addSampleCollectionRecordApi;
    } else {
      return updateSampleCollectionRecordApi;
    }
  };

  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const baseVal = await baseFormRef.current?.validateFields();
      const samplingVal = await samplingFormRef.current?.validateFields();
      const checkVal = await checkFormRef.current?.validateFields();
      const _params = {
        ...baseVal,
        ...samplingVal,
        ...checkVal,
      };
      if (_params.producedDate) {
        _params.producedDate = dayjs(_params.producedDate).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
      if (_params.sampleDate) {
        _params.sampleDate = dayjs(_params.sampleDate).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto flex flex-col gap-4">
          <BlockContainer title="样品基本信息">
            <ProForm
              className="p-6"
              formRef={baseFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleName"
                label="样本名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样本名称必填！' }]}
              />
              <ProFormText
                name="sampleTaskNumber"
                label="样本编号"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样本编号必填！' }]}
              />
              <ProFormText
                name="sampleCount"
                label="样品数量"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品数量必填！' }]}
              />
              <ProFormText
                name="samplePack"
                label="样品包装"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="sampleType"
                label="标本类型"
                colProps={{ span: 8 }}
              />

              <ProFormDatePicker
                name="producedDate"
                label="生产日期"
                colProps={{ span: 8 }}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
              <ProFormText
                name="batchNumber"
                label="批号"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="specificationModel"
                label="规格型号"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="sampleColour"
                label="物态颜色"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="expirationDate"
                label="保质期"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="sampleStatus"
                label="样品状态"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="executiveStandard"
                label="执行标准"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="storageCondition"
                label="储存条件"
                colProps={{ span: 8 }}
              />
              <ProFormDatePicker
                name="sampleDate"
                label="采集时间"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '采集时间必填！' }]}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="采样单位信息">
            <ProForm
              className="p-6"
              formRef={samplingFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleUnit"
                label="采集单位"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '采集单位必填！' }]}
              />
              <ProFormText
                name="samplePeople"
                label="采集人员"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '采样单位必填！' }]}
              />
              <ProFormText
                name="collectPattern"
                label="采集方式"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="samplePhone"
                label="联系电话"
                colProps={{ span: 8 }}
                rules={[
                  {
                    validator(rule, value, callback) {
                      if (value && !phoneReg.test(value)) {
                        callback('电话格式不正确');
                      } else {
                        callback();
                      }
                    },
                  },
                ]}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="受检单位信息">
            <ProForm
              className="p-6"
              formRef={checkFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="examinedUnit"
                label="受检单位"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '受检单位必填！' }]}
              />
              <ProFormText
                name="examinedPeople"
                label="联系人"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="examinedPhone"
                label="联系电话"
                colProps={{ span: 8 }}
                rules={[
                  {
                    validator(rule, value, callback) {
                      if (value && !phoneReg.test(value)) {
                        callback('电话格式不正确');
                      } else {
                        callback();
                      }
                    },
                  },
                ]}
              />
              <ProFormText
                name="examinedUnitAddr"
                label="单位地址"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="detectionType"
                label="检测类型"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '检测类型必填！' }]}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
