import { ProColumns } from '@ant-design/pro-components';
import EProTable from '@/components/EProTable';

type TCheckItemProps = {
  dataSource: Record<string, any>[];
};
const CheckItem: React.FC<TCheckItemProps> = ({ dataSource }) => {
  const columns: ProColumns[] = [
    {
      title: '子样品编号',
      dataIndex: 'sonSampleTaskNumber',
    },
    {
      title: '子样品名称',
      dataIndex: 'sonSampleName',
    },
    {
      title: '科室',
      dataIndex: 'administrative',
    },
    {
      title: '检测员',
      dataIndex: 'inspector',
    },
    {
      title: '检测开始时间',
      dataIndex: 'detectionStartTime',
    },
    {
      title: '检测结束时间',
      dataIndex: 'detectionEndTime',
    },
    {
      title: '结果提交时间',
      dataIndex: 'resultSubmitTime',
    },
    {
      title: '校核人',
      dataIndex: 'checkPeople',
    },
    {
      title: '校核日期',
      dataIndex: 'check',
    },
    {
      title: '复核人',
      dataIndex: 'recheckPeople',
    },
    {
      title: '复核日期',
      dataIndex: 'recheckDate',
    },
    {
      title: '检测项目',
      dataIndex: 'inspectionItem',
    },
    {
      title: '检验依据',
      dataIndex: 'inspectionStandard',
    },
    {
      title: '检测结果',
      dataIndex: 'testResult',
    },
  ];
  return (
    <EProTable
      columns={columns}
      cardBordered
      bordered
      dataSource={dataSource}
      rowKey="id"
      search={false}
      options={false}
      pagination={false}
      scroll={{
        x: 'max-content'
      }}
    />
  );
};

export default CheckItem;
