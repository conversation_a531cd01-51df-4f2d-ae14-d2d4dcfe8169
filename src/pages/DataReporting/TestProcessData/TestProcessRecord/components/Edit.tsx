import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  addTestProcessRecordApi,
  getTestProcessRecordDetailsById,
  updateTestProcessRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  EditableFormInstance,
  EditableProTable,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setDataSource([]);
    setOpen(false);
  };

  const baseFormRef = useRef<ProFormInstance>(null);

  const [dataSource, setDataSource] = useState<readonly any[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const editableFormRef = useRef<EditableFormInstance>();
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '子样品编号',
      dataIndex: 'sonSampleTaskNumber',
    },
    {
      title: '子样品名称',
      dataIndex: 'sonSampleName',
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 科室
        </div>
      ),
      dataIndex: 'administrative',
      formItemProps: {
        rules: [{ required: true, message: '科室必填！' }],
      },
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 检测员
        </div>
      ),
      dataIndex: 'inspector',
      formItemProps: {
        rules: [{ required: true, message: '检测员必填！' }],
      },
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 检测开始时间
        </div>
      ),
      dataIndex: 'detectionStartTime',
      valueType: 'date',
      formItemProps: {
        rules: [{ required: true, message: '检测开始时间必填！' }],
      },
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 检测结束时间
        </div>
      ),
      dataIndex: 'detectionEndTime',
      valueType: 'date',
      formItemProps: {
        rules: [{ required: true, message: '检测结束时间必填！' }],
      },
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 结果提交时间
        </div>
      ),
      dataIndex: 'resultSubmitTime',
      valueType: 'date',
      formItemProps: {
        rules: [{ required: true, message: '结果提交时间必填！' }],
      },
    },
    {
      title: '校核人',
      dataIndex: 'checkPeople',
    },
    {
      title: '校核日期',
      dataIndex: 'check',
      valueType: 'dateTime',
    },
    {
      title: '复核人',
      dataIndex: 'recheckPeople',
    },
    {
      title: '复核日期',
      dataIndex: 'recheckDate',
      valueType: 'date',
    },
    {
      title: (
        <div>
          <span className=" text-red-500">*</span> 检测项目
        </div>
      ),
      dataIndex: 'inspectionItem',
      formItemProps: {
        rules: [{ required: true, message: '检测项目必填！' }],
      },
    },
    {
      title: '检验依据',
      dataIndex: 'inspectionStandard',
    },
    {
      title: '检测结果',
      dataIndex: 'testResult',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      render: (text, record, _, action) => [
        // <Button
        //   key="edit"
        //   type="link"
        //   onClick={() => {
        //     action?.startEditable?.(record.id);
        //   }}
        // >
        //   编辑
        // </Button>,
        // <Button key="delete" type="link" danger>
        //   删除
        // </Button>,
      ],
      fixed: 'right',
    },
  ];

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await getTestProcessRecordDetailsById({
        id,
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseFormRef.current?.setFieldsValue(data);
      if (data.insideRecords && data.insideRecords.length) {
        setEditableRowKeys(data.insideRecords.map((item: any) => item.id));
        setDataSource(data.insideRecords);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);
  const saveFetch = () => {
    if (type === 'add') {
      return addTestProcessRecordApi;
    } else {
      return updateTestProcessRecordApi;
    }
  };
  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const baseVal = await baseFormRef.current?.validateFields();
      if (!dataSource.length) {
        message.error('请新增一条检验信息');
        return;
      }
      await editableFormRef.current?.validateFields();
      const timeKey = [
        'detectionStartTime',
        'detectionEndTime',
        'resultSubmitTime',
        'checkDate',
        'recheckDate',
      ];
      dataSource.forEach((item) => {
        timeKey.forEach((i) => {
          if (item[i]) {
            item[i] = dayjs(item[i]).format('YYYY-MM-DD HH:mm:ss');
          }
        });
      });
      const _params = {
        ...baseVal,
        insideRecords: dataSource,
      };
      // console.log(dataSource, _params);
      // return
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto flex flex-col gap-4">
          <BlockContainer title="样品基本信息">
            <ProForm
              className="p-6"
              formRef={baseFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleName"
                label="样品名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品名称必填！' }]}
              />
              <ProFormText
                name="sampleTaskNumber"
                label="样品编号"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品编号必填！' }]}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="检验基本信息">
            <EditableProTable
              columns={columns}
              rowKey="id"
              scroll={{
                x: 'max-content',
              }}
              value={dataSource}
              onChange={(val) => setDataSource(val)}
              recordCreatorProps={{
                record: () => ({
                  id: Date.now() + '',
                }),
                creatorButtonText: '新增一条',
                newRecordType: 'dataSource',
              }}
              editableFormRef={editableFormRef}
              editable={{
                type: 'multiple',
                editableKeys,
                actionRender: (row: any, config: any, defaultDoms: any) => {
                  return [defaultDoms.delete];
                },
                onValuesChange: (record: any, recordList: any[]) => {
                  setDataSource(recordList);
                },
                onChange: setEditableRowKeys,
              }}
            />
          </BlockContainer>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
