import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  addSamplePreProcessingRecordApi,
  getSamplePreviousDisposeRecordDetailsById,
  updateSamplePreProcessingRecordApi,
} from '@/api/DataReporting/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: string;
  curSelectedRowId: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  curSelectedRowId,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const baseFormRef = useRef<ProFormInstance>(null);
  const samplingFormRef = useRef<ProFormInstance>(null);

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } =
        await getSamplePreviousDisposeRecordDetailsById({
          id,
        });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseFormRef.current?.setFieldsValue(data);
      samplingFormRef.current?.setFieldsValue(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) queryDetailsById(curSelectedRowId);
  }, [curSelectedRowId, open]);

  const [btnLoading, setBtnLoading] = useState(false);

  const saveFetch = () => {
    if (type === 'add') {
      return addSamplePreProcessingRecordApi;
    } else {
      return updateSamplePreProcessingRecordApi;
    }
  };

  const handleSave = async () => {
    try {
      setBtnLoading(true);
      const baseVal = await baseFormRef.current?.validateFields();
      const samplingVal = await samplingFormRef.current?.validateFields();
      const _params = {
        ...baseVal,
        ...samplingVal,
      };
      if (_params.makeSampleDate) {
        _params.makeSampleDate = dayjs(_params.makeSampleDate).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
      if (type === 'edit') _params.id = curSelectedRowId;
      const { code, msg } = await saveFetch()(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        handleClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={handleClose}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className=" flex-1 p-4 overflow-y-auto flex flex-col gap-4">
          <BlockContainer title="样品基本信息">
            <ProForm
              className="p-6"
              formRef={baseFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="sampleName"
                label="样品名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品名称必填！' }]}
              />
              <ProFormText
                name="sampleTaskNumber"
                label="样品编号"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '样品编号必填！' }]}
              />
            </ProForm>
          </BlockContainer>
          <BlockContainer title="制样记录">
            <ProForm
              className="p-6"
              formRef={samplingFormRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="checkGroup"
                label="检验分组"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '检验分组必填！' }]}
              />
              <ProFormText
                name="makeSamplePeople"
                label="制样人"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '制样人必填！' }]}
              />
              <ProFormDatePicker
                name="makeSampleDate"
                label="制样日期"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '制样日期必填！' }]}
                fieldProps={{
                  style: {
                    width: '100%',
                  },
                }}
              />
              <ProFormText
                name="makeSampleWay"
                label="制样方式"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '制样方式必填！' }]}
              />
              <ProFormText
                name="makeSampleStatus"
                label="制样后状态"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '制样后状态必填！' }]}
              />
              <ProFormDigit
                name="makeSampleNumber"
                label="制样数量"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '制样数量必填！' }]}
              />
              <ProFormText
                name="makeSampleUnit"
                label="制样单位"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="makeSamplePlace"
                label="制样场所"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="storageCondition"
                label="储存条件"
                colProps={{ span: 8 }}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={handleClose}>
            关闭
          </Button>
          <Button type="primary" onClick={handleSave} loading={btnLoading}>
            保存
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
