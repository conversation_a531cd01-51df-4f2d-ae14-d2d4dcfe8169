import { useEffect, useState } from 'react';
import { Button, Drawer, Input, message, Radio } from 'antd';
import {
  exceptionDataListApi,
  saveExceptionRectificationApi,
  submitExceptionRectificationApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import BaseInfo from './BaseInfo';

type TRectificationSingInProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  code: string;
};
const RectificationSingIn: React.FC<TRectificationSingInProps> = ({
  open,
  setOpen,
  code,
}) => {
  const [btnLoading, setBtnLoading] = useState(false);

  const close = () => {
    setOpen(false);
  };

  const [dataSource, setDataSource] = useState<any[]>([]);

  const columns: ProColumns[] = [
    {
      title: '数据类名',
      dataIndex: 'tableName',
    },
    {
      title: '数据名称',
      dataIndex: 'filedName',
    },
    {
      title: '错误描述',
      dataIndex: 'ruleDesc',
    },
    {
      title: '修改建议',
      dataIndex: 'verifySuggestion',
    },
    {
      title: '登记类型',
      render: (_, record) => (
        <Radio.Group
          onChange={(e) => {
            setDataSource(
              dataSource.map((item) => ({
                ...item,
                rectificationType:
                  item.id === record.id
                    ? e.target.value
                    : item.rectificationType,
                rectificationRecord:
                  item.id === record.id ? '' : item.rectificationRecord,
              }))
            );
          }}
          value={record.rectificationType}
        >
          <Radio value={1}>申诉</Radio>
          <Radio value={2}>更新数据</Radio>
        </Radio.Group>
      ),
    },
    {
      title: '登记信息',
      render: (_, record) => (
        <Input
          value={record.rectificationRecord}
          placeholder={
            record.rectificationType === 1 ? '请输入申诉原因' : '请输入最新数据'
          }
          onChange={(e: any) => {
            setDataSource(
              dataSource.map((item) => ({
                ...item,
                rectificationRecord:
                  item.id === record.id
                    ? e.target.value
                    : item.rectificationType,
              }))
            );
          }}
        />
      ),
    },
  ];

  const getList = (sampleCode: string) => {
    exceptionDataListApi(sampleCode).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        res.data.forEach((item: any) => {
          if (![1, 2].includes(item.rectificationType)) {
            item.rectificationType = 1;
          }
        });
        setDataSource(res.data);
      } else {
        message.error(res.data);
      }
    });
  };

  useEffect(() => {
    if (open && code) {
      getList(code);
    }
  }, [open, code]);

  const handleSave = async (type: number) => {
    try {
      setBtnLoading(true);
      const _fetchApi =
        type === 1
          ? saveExceptionRectificationApi
          : submitExceptionRectificationApi;
      const { code, msg } = await _fetchApi(dataSource);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full ">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          <BaseInfo code={code} />
          <BlockContainer title="整改登记">
            <ProTable
              columns={columns}
              rowKey="id"
              scroll={{
                x: 'max-content',
              }}
              dataSource={dataSource}
              search={false}
              options={false}
            />
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(1)}
          >
            保存登记整改信息
          </Button>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => handleSave(2)}
          >
            提交登记整改信息
          </Button>
          <Button type="default" onClick={close}>
            取消
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default RectificationSingIn;
