/*
 * @Date: 2024-08-01 09:36:58
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 18:34:25
 * @FilePath: \xr-qc-jk-web\src\pages\DataReporting\ExceptionData\DataRectification\index.tsx
 * @Description: 异常数据整改
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  exceptionRectificationListApi,
  exceptionRectificationPendingListApi,
} from '@/api/DataReporting/exceptionData';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import RectificationSingIn from './components/RectificationSingIn';
import PageContainer from '@/components/PageContainer';

const DataRectification: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  // 统计数量
  const [counts, setCounts] = useState([0, 0]);
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curSelectId, setCurSelectId] = useState('');
  const [sampleCode, setSampleCode] = useState('');

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
  ];

  const getColumns = useCallback((): ProColumns[] => {
    let _columns: ProColumns[] = [];
    if (activeKey === '0') {
      _columns = [
        {
          title: '待整改问题数量',
          dataIndex: 'abnormalNum',
          hideInSearch: true,
        },
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 80,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setSampleCode(record.sampleCode);
                setDetailOpen(true);
              }}
            >
              问题整改登记
            </Button>,
          ],
        },
      ];
    } else {
      _columns = [
        {
          title: '整改问题数量',
          dataIndex: 'abnormalNum',
          hideInSearch: true,
        },
        {
          title: '申诉审核情况',
          hideInSearch: true,
          children: [
            {
              title: '申诉数量',
              dataIndex: 'appealNum',
              hideInSearch: true,
            },
            {
              title: '申诉认可数',
              dataIndex: 'approvalAppealNum',
              hideInSearch: true,
            },
            {
              title: '申诉不认可数',
              dataIndex: 'rejectionAppealNum',
              hideInSearch: true,
            },
          ],
        },
        {
          title: '更新审核情况',
          hideInSearch: true,
          children: [
            {
              title: '更新数据数量',
              dataIndex: 'updateNum',
              hideInSearch: true,
            },
            {
              title: '更新通过数',
              dataIndex: 'updateApprovalNum',
              hideInSearch: true,
            },
            {
              title: '更新不通过数',
              dataIndex: 'updateNolNum',
              hideInSearch: true,
            },
          ],
        },
      ];
    }
    return [...columns, ..._columns];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: <span>待整改（{counts[0]}）</span>,
              },
              {
                key: '1',
                label: <span>已整改（{counts[1]}）</span>,
              },
            ],
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
            current: undefined,
          };
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleDateBegin = dayjs(_params.sampleDate[0]).format(
              'YYYY-MM-DD'
            );
            _params.sampleDateEnd = dayjs(_params.sampleDate[1]).format(
              'YYYY-MM-DD'
            );
            delete _params.sampleDate;
          }
          const _fetchApi =
            activeKey === '0'
              ? exceptionRectificationPendingListApi
              : exceptionRectificationListApi;
          const { code, data, msg } = await _fetchApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
          if (init) {
            const res = await (activeKey === '1'
              ? exceptionRectificationPendingListApi
              : exceptionRectificationListApi)(_params);
            if (res.code === codeDefinition.QUERY_SUCCESS) {
              const _counts = [...counts];
              if (activeKey === '0') {
                _counts[0] = data.total || 0;
                _counts[1] = res.data.total || 0;
              } else {
                _counts[1] = data.total || 0;
                _counts[0] = res.data.total || 0;
              }
              setCounts(_counts);
            }
            setInit(false);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 问题整改登记 */}
      <RectificationSingIn
        open={detailOpen}
        setOpen={(val) => {
          tableReload();
          setDetailOpen(val);
        }}
        code={sampleCode}
      />
    </PageContainer>
  );
};

export default DataRectification;
