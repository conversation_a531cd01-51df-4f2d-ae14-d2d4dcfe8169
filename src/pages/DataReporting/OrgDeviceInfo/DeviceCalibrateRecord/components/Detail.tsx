import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { deviceCalibrateRecordDetailApi } from '@/api/DataReporting/api';
import { codeDefinition } from '@/constants';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  curSelectedRowId: string;
};
const Detail: React.FC<TDetailProps> = ({
  open,
  setOpen,
  curSelectedRowId,
}) => {
  // 行数据详情
  const [rowDetails, setRowDetails] = useState<Record<string, any>>();

  const close = () => {
    setOpen(false);
    setRowDetails(undefined);
  };

  /**
   * 获取当前选中的行的详情数据
   */
  const queryDetailsById = async (id: string) => {
    try {
      const { code, data, msg } = await deviceCalibrateRecordDetailApi(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRowDetails(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (curSelectedRowId && open) {
      queryDetailsById(curSelectedRowId);
    } else {
      setRowDetails(undefined);
    }
  }, [curSelectedRowId, open]);

  return (
    <Drawer width="90%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          {/* <Descriptions.Item label="检定申请编号">
            {rowDetails?.applyNo}
          </Descriptions.Item> */}
          <Descriptions.Item label="检定证书编号">
            {rowDetails?.certNo}
          </Descriptions.Item>
          <Descriptions.Item label="检定时间">
            {rowDetails?.judgeDate}
          </Descriptions.Item>
          <Descriptions.Item label="检定结果">
            {rowDetails?.judgeResult}
          </Descriptions.Item>
          <Descriptions.Item label="检定/校准单位">
            {rowDetails?.unit}
          </Descriptions.Item>
          {/* <Descriptions.Item label="检定负责人">
            {rowDetails?.judgeMan}
          </Descriptions.Item>
          <Descriptions.Item label="检定周期">
            {rowDetails?.period}
          </Descriptions.Item>
          <Descriptions.Item label="下次检定日期">
            {rowDetails?.nextDate}
          </Descriptions.Item>
          <Descriptions.Item label="检定费用">
            {rowDetails?.fee}
          </Descriptions.Item>
          <Descriptions.Item label="备注">
            {rowDetails?.remark}
          </Descriptions.Item> */}
        </Descriptions>
      </div>
    </Drawer>
  );
};

export default Detail;
