/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * 检测质量控制考核结果公示
 */
import React, { useState } from 'react';
import { Button, message, Modal } from 'antd';
import { downloadFile } from '@/api/file';
import { homePublicityListApi } from '@/api/publicity';
import { codeDefinition } from '@/constants';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import FileViewByStream from '@/components/FileViewByStream';

type TResultsPublicAnnouncementProps = {};

// 临时测试数据
const mockData = [
  {
    id: 1,
    year: '2023',
    taskName: '年度质量考核',
    assessmentType: '常规考核',
    endDate: '2023-12-31',
    responsiblePerson: '张三',
    contactNumber: '13800138000',
    viewCount: 256,
    publicationDate: '2024-01-15',
  },
  {
    id: 2,
    year: '2024',
    taskName: '年度质量考核',
    assessmentType: '常规考核',
    endDate: '2024-12-31',
  },
  {
    id: 3,
    year: '2025',
    taskName: '年度质量考核',
    assessmentType: '常规考核',
    endDate: '2025-12-31',
  },
];

const ResultsPublicAnnouncement: React.FC<
  TResultsPublicAnnouncementProps
> = () => {
  const [loading, setLoading] = useState(false);

  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');

  const columns: ProColumns<Record<string, any>>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'year',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      hideInSearch: true,
    },
    {
      title: '考核类型',
      dataIndex: 'assessmentType',
      hideInSearch: true,
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '任务负责人',
      dataIndex: 'people',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      hideInSearch: true,
      width: 140,
    },
    {
      title: '浏览次数',
      dataIndex: 'orderNum',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '公示日期',
      dataIndex: 'publishDate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      hideInSearch: true,
      render: (_, record) => [
        <Button
          key="preview"
          type="link"
          onClick={() => {
            setPreviewFileId(record?.ossId);
            setOpenPreview(true);
          }}
        >
          预览公示文件
        </Button>,
        <Button
          key="download"
          type="link"
          onClick={() => {
            downloadFile(record?.ossId);
          }}
        >
          下载公示附件
        </Button>,
      ],
    },
  ];

  return (
    <>
      <ProTable
        columns={columns}
        request={async (params, sort, filter) => {
          try {
            const param: any = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            const { code, data, msg } = await homePublicityListApi(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.rows ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          } catch (err) {
            throw new Error(`Error: err`);
          } finally {
            // finally todo ...
          }
        }}
        rowKey="taskId"
        search={false}
        options={false}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        scroll={{
          y: 360,
          x: 'max-content',
        }}
      />
      <Modal
        width="60%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview}
        footer={null}
        destroyOnClose
      >
        <FileViewByStream fileId={previewFileId} isPreview />
      </Modal>
    </>
  );
};

export default ResultsPublicAnnouncement;
