/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * 操作指引
 */
import React from 'react';
import { Button, message } from 'antd';
import { downloadFile } from '@/api/file';
import { getOperationGuideList } from '@/api/home';
import { codeDefinition } from '@/constants';
import { useLoadingStore } from '@/store';
import { ProColumns, ProTable } from '@ant-design/pro-components';

type TOperationGuideProps = {};

const OperationGuide: React.FC<TOperationGuideProps> = () => {
  const { setGolbalLoading } = useLoadingStore();

  const columns: ProColumns<Record<string, any>>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '模块名称',
      dataIndex: 'name',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      hideInSearch: true,
      //@ts-ignore
      render: (_, record) => [
        <Button
          key="preview"
          type="link"
          onClick={async () => {
            setGolbalLoading(true);
            await downloadFile(
              record?.id,
              record?.fileName,
              '/system/operation/guide/download'
            );
            setGolbalLoading(false);
          }}
        >
          下载使用说明书
        </Button>,
      ],
    },
  ];

  return (
    <ProTable
      columns={columns}
      request={async (params, sort, filter) => {
        const param: any = {
          ...params,
          pageNum: params.current,
          pageSize: params.pageSize,
        };
        delete param.current;

        const { code, data, msg } = await getOperationGuideList(param);
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
        }
        return {
          data: data ?? [],
          total: data?.total ?? 0,
          success: true,
        };
      }}
      rowKey="id"
      search={false}
      options={false}
      pagination={{
        pageSize: 5,
      }}
      dateFormatter="string"
      size="small"
      scroll={{
        y: 140,
        x: 'max-content',
      }}
    />
  );
};

export default OperationGuide;
