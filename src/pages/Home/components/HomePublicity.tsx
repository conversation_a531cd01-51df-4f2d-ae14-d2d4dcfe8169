/*
 * @Date: 2024-07-22 15:56:50
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-05 18:06:01
 * @FilePath: \xr-qc-jk-web\src\pages\Home\components\HomePublicity.tsx
 * @Description: 首页公示
 */
import { useEffect, useState } from 'react';
import { Button, message, Pagination } from 'antd';
import { homePublicityListApi, homePublicityTopApi } from '@/api/publicity';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { VerticalAlignTopOutlined } from '@ant-design/icons';
import PublicityDetail from './PublicityDetail';

type THomePublicityDataSource = {
  browseCount: number;
  orderNum: number;
  publishDate: string;
  taskId: string;
  taskName: string;
  title: string;
  fileGroupId: string
};

const HomePublicity: React.FC = () => {
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [publicDataSource, setPublicDataSource] = useState<
    THomePublicityDataSource[]
  >([]);
  const [curTaskId, setCurTaskId] = useState('');
  const [curFileGroupId, setCurFileGroupId] = useState('');
  const [publicityDetailOpen, setPublicityDetailOpen] = useState(false);
  const [maxOrderNum, setMaxOrderNum] = useState(0);

  const getHomePublicityList = async () => {
    try {
      const { code, data, msg } = await homePublicityListApi({
        pageNum,
        pageSize,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        if (data.rows.length && pageNum === 1) {
          setMaxOrderNum(data.rows[0].orderNum);
        }
        setTotal(data.total);
        setPublicDataSource(data.rows);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getHomePublicityList();
  }, []);

  // 置顶
  const handlePubTop = async (taskId: string) => {
    try {
      const { code, msg } = await homePublicityTopApi(taskId);
      if (code === 200) {
        message.success(QUERY_SUCCESS_MSG);

        setPageNum(1);
        setPageSize(10);
        getHomePublicityList();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  return (
    <div className=" flex-1 h-full flex flex-col ">
      <div className=" flex-1 flex flex-col overflow-y-auto gap-6">
        {publicDataSource.map((_item, _index) => (
          <div
            key={_index}
            className=" w-full p-4 flex flex-col gap-4 rounded-lg border border-solid border-slate-300 bg-white"
          >
            <div className=" text-lg w-full h-10 flex justify-between items-center">
              <div>【{_item.taskName}】考核结果公示</div>
              {_item.orderNum === maxOrderNum ? (
                <Button
                  type="default"
                  icon={<VerticalAlignTopOutlined />}
                  // onClick={() => handlePubTop(_item.taskId + '')}
                >
                  置顶
                </Button>
              ) : null}
            </div>
            <div className=" indent-8">
              {_item.title}
              <Button
                type="link"
                onClick={() => {
                  setCurTaskId(_item.taskId);
                  setCurFileGroupId(_item.fileGroupId)
                  setPublicityDetailOpen(true);
                }}
              >
                查看详情
              </Button>
            </div>
            <div className=" flex h-10 items-center justify-between">
              <span>浏览次数: {_item.browseCount}</span>
              <span>公示日期: {_item.publishDate}</span>
            </div>
          </div>
        ))}
      </div>
      <div className=" h-12 flex items-center justify-end flex-shrink-0">
        <Pagination
          current={pageNum}
          pageSize={pageSize}
          total={total}
          size="small"
          onChange={(current, size) => {
            setPageNum(current);
            setPageSize(size);
            getHomePublicityList();
          }}
        />
      </div>

      {/* 公示详情 */}
      <PublicityDetail
        open={publicityDetailOpen}
        setOpen={setPublicityDetailOpen}
        taskId={curTaskId}
        fileGroupId={curFileGroupId}
      />
    </div>
  );
};

export default HomePublicity;
