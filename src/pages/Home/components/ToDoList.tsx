/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  待办事项列表
 */
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge, Flex, message } from 'antd';
import { getTodoList } from '@/api/home';
import { codeDefinition } from '@/constants';
import { usePermissionRouterStore } from '@/store';
import { clone } from 'lodash';
import Loading from '@/components/Loading';

type TToDoListProps = {};

const _todoListItemClassNames =
  'w-[190px] h-[30px] bg-[#128BFF] py-6 text-white rounded flex items-center justify-center cursor-pointer hover:bg-[#128BFF] truncate hover:scale-105 transition-all duration-200';

const ToDoList: React.FC<TToDoListProps> = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [todoList, setTodoList] = useState<Record<string, any>[]>([]);

  const [menuList, setMenuList] = useState<any>([]);

  const { menuData, setChildMenuData, childMenuData } =
    usePermissionRouterStore();

  const home = {
    key: '/',
    icon: 'HomeOutlined',
    label: '首页',
  };

  const visualization = {
    key: '/dataVisualizationDashboard/monitoringOverview',
    icon: 'AreaChartOutlined',
    label: '可视化',
  };

  /**
   * 获取待办事项列表
   */
  const queryTodoList = async () => {
    try {
      setLoading(true);
      const { code, data, msg } = await getTodoList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTodoList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 点击时处理路由菜单、侧边栏菜单数据
   */
  const handleNavigateEvent = (_item: any) => {
    if (!menuData?.length) return;
    const arr: any[] = clone(menuData);
    function genMenu(menu: any[]) {
      let mItem: any = [];
      menu.forEach((item) => {
        let m: any = {};
        m.key = item.completePath || item.path;
        m.icon = item.meta?.icon;
        m.label = item.meta?.title;
        if (item.children) {
          m.children = genMenu(item.children);
        }
        mItem.push(m);
      });
      return mItem;
    }
    const menu = genMenu(arr);
    setMenuList([home, ...menu, visualization] as any);

    const _topMenuKey = _item?.path.split('/')[0];
    if (!_topMenuKey) return;

    const _topMenuChildren = menu?.find((_i: Record<string, any>) =>
      _i?.key.includes(_topMenuKey)
    )?.children;
    if (!_topMenuChildren?.length) return;
    setChildMenuData([..._topMenuChildren]);

    navigate(_item?.path);
  };

  useEffect(() => {
    queryTodoList();
  }, []);

  return (
    //@ts-ignore
    <Flex wrap={true} gap={32} className="w-full h-full flex-wrap p-3">
      {loading ? (
        <div className="flex flex-row flex-nowrap justify-center items-center w-full h-full">
          {/* <LoadingOutlined size={48} /> */}
          <Loading textClassName="transform -translate-y-6" />
        </div>
      ) : (
        todoList?.length &&
        todoList.map((_item) => (
          <Badge key={_item?.menuId} count={_item?.num}>
            <div
              className={_todoListItemClassNames}
              title={_item?.name}
              onClick={() => handleNavigateEvent(_item)}
            >
              <span className="block truncate px-2">{_item?.name}</span>
            </div>
          </Badge>
        ))
      )}
    </Flex>
  );
};

export default ToDoList;
