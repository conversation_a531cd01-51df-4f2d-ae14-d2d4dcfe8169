/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { useNavigate } from 'react-router-dom';
import OperationGuide from './components/OperationGuide';
import ResultsPublicAnnouncement from './components/ResultsPublicAnnouncement';
import ToDoList from './components/ToDoList';
import BlockContainer from '@/components/BlockContainer';

type THomeProps = {};

const Home: React.FC<THomeProps> = () => {
  const navigate = useNavigate();

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="w-full rounded h-[calc(100vh-400px)]">
        <BlockContainer
          title="检测质量控制考核结果公示"
          size="default"
          className="h-full"
        >
          <ResultsPublicAnnouncement />
        </BlockContainer>
      </div>
      <div className="w-full flex-1 flex flex-row flex-nowrap gap-4">
        <div className="w-2/3 rounded h-full">
          <BlockContainer title="待办任务" size="default" className="h-full">
            <ToDoList />
          </BlockContainer>
        </div>
        <div className="w-1/3 rounded h-full">
          <BlockContainer title="操作指引" size="default" className="h-full">
            <OperationGuide />
          </BlockContainer>
        </div>
      </div>
    </div>
  );
};

export default Home;
