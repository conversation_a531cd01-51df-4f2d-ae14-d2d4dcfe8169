.loginStyleContainer {
  .default-pro-form {
    margin-top: 20px;
  }
  .videoStyle {
    object-fit: cover;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    filter: blur(7px);
    // -webkit-filter: blur(10px);
  }
  .videoModel {
    width: 100vw;
    height: 100vh;
    background: inherit;
    position: absolute;
    overflow: hidden;
  }
  .ModelRight {
    width: 60vw;
    height: 100vh;
    position: absolute;
    right: 0;
    opacity: 0.9;
    background-image: url('/src/assets/bgr3.png');
    background-size: cover;
  }
  .default-pro-form-login-container {
    border-radius: 10px;
    .default-pro-form-login-top {
      margin-bottom: 25px;
      width: 100%;
      height: 65px;
      .default-pro-form-login-header  {
        height: 100%;
        .default-pro-form-login-logo {
          width: 60px;
          height: 60px;
        }
      }
      
    }
    .default-form-item-explain-error {
      position: absolute;
      bottom: -30px;
      left: 0;
    }
    .default-btn {
      border-radius: 10px;
      height: 45px;
      line-height: 20px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    }
    .default-form-item {
      margin-bottom: 0;
      width: 100%;
      .default-input {
        margin-left: 5px;
      }
    }
  }
  .ant-pro-form-login-main{
    margin-top: 40px;
  }
} 

.unified-auth__page {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
  /* HTML: <div class="loader"></div> */
  .loader {
    width: 40px;
    padding: 8px;
    aspect-ratio: 1;
    border-radius: 50%;
    background: #409eff;
    --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
    -webkit-mask: var(--_m);
    mask: var(--_m);
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
    animation: l3 1s infinite linear;
  }
  @keyframes l3 {
    to {
      transform: rotate(1turn);
    }
  }
}