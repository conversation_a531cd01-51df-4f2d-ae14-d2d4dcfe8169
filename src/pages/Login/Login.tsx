/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/alt-text */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button, Form, Image, Input, message, Modal, Radio, Tabs } from 'antd';
import { onHandleUnifiedAuth, queryPermissionRouter } from '@/api/auth';
import {
  getCaptchaImage,
  getUserInfo,
  signInBindAccount,
  signUpAndSignInBindAccount,
  submitLogin,
} from '@/api/login';
import { getYearList } from '@/api/settings';
import { getInitialRoutes } from '@/app';
import baIcon from '@/assets/ba.png';
import { appTitle } from '@/constants';
import { codeDefinition } from '@/constants';
import { InitialRoutes } from '@/routes';
import { LockOutlined, PictureOutlined, UserOutlined } from '@ant-design/icons';
import {
  LoginForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { cloneDeep, set } from 'lodash';
import { encrypt } from '@/utils/jsencrypt';
import {
  useInfoStore,
  usePermissionRouterStore,
  useTokenStore,
  useYearStore,
} from '../../store';
import './index.less';

type LoginType = 'phone' | 'account';

interface LoginPropsType {
  username: string;
  password: string;
  code: string;
}

const Login: React.FC = (props) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { yearList, setYearList } = useYearStore();

  const [messageApi, contextHolder] = message.useMessage();

  const formRef = useRef<ProFormInstance>();

  //初始化设置token方法
  const { setToken } = useTokenStore();

  //初始化设置用户信息
  const { setUserInfo } = useInfoStore();

  // 登录类型
  const [loginType] = useState<LoginType>('account');

  // 登录 Loading
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);




  // 是否走统一认证通道，默认false，
  // 如果为 true，则走统一认证通道，请求后端接口从认证平台获取授权
  const [isUnifiedAuth, setIsUnifiedAuth] = useState<boolean>(false);
  // 当前需要请求统一认证平台的code值
  const [unifiedAuthCode, setUnifiedAuthCode] = useState<string>();
  // 统一认证登录配置的跳转URL
  const [unifiedAuthRedirectUrl, setUnifiedAuthRedirectUrl] =
    useState<string>();

  // 初始化权限路由
  const { setAllRouter, setRouteStructure, setMenuData } =
    usePermissionRouterStore();

  // 当前后台返回的统一认证平台的用户名
  let ssoUserNameRef = useRef<Record<string, any>>({ ssoUserName: '' });

  // 绑定统一认证平台账号与本地账号的Modal
  const [accountBindModalIsOpen, setAccountBindModalIsOpen] =
    useState<boolean>(false);

  // 当前选择的Tab
  const [curSelectedTab, setCurSelectedTab] = useState<string>('1');

  // 登录绑定的表单Ref
  const [signInForm] = Form.useForm();
  // 注册绑定的表单Ref
  const [registerAndSigninForm] = Form.useForm();

  /**
   * 重写获取用户信息方法
   * @returns
   */
  const queryUserInfos = async () => {
    try {
      const { code, data, msg } = await getUserInfo();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      //储存用户信息
      setUserInfo(data);
      return true;
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  //获取菜单信息
  const getUserMenus = async () => {
    let flag = false;
    try {
      const { code, data, msg } = await queryPermissionRouter();
      if (code === codeDefinition.QUERY_SUCCESS) {
        flag = true;
        setAllRouter(data);
        // 生成react-router依赖的route数据
        const route = cloneDeep(InitialRoutes);
        const menus = getInitialRoutes(data);
        route[0].children = [...route[0].children, ...menus];
        setRouteStructure(route);
        setMenuData(menus);
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      flag = false;
      throw new Error(`Error: ${error}`);
    }
    return flag;
  };

  /**
   * 获取年份List
   */
  const queryYearList = async () => {
    try {
      const { code, data, msg } = await getYearList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      setYearList(data);
    } catch (err) {
      throw new Error(`Error: ${err}`);
    }
  };

  // 登录
  const onSubmit = async (value: LoginPropsType) => {
    setSubmitLoading(true);
    try {
      const { code, msg, data } = await submitLogin(
        needCaptcha
          ? ({
              code: value.code,
              uuid: uuid,
              username: value.username,
              password: encrypt(value.password),
            } as any)
          : ({
              username: value.username,
              password: encrypt(value.password),
            } as any)
      );
      if (code === 200) {
        // 储存token
        if (!data?.token) {
          messageApi.error('系统未返回认证Token, 请检查');
          return;
        }
        setToken(data?.token);

        // 获取用户信息
        const _userInfoIsSuc = await queryUserInfos();
        if (!_userInfoIsSuc) {
          messageApi.error('获取用户信息异常, 请检查');
          return;
        }

        // 获取菜单信息
        const _menuIsSuc = await getUserMenus();
        if (!_menuIsSuc) {
          messageApi.error('获取用户菜单数据异常,请检查');
          return;
        }

        // 获取年份枚举
        await queryYearList();

        if (_userInfoIsSuc && _menuIsSuc) {
          navigate('/', { replace: true });
        }
      } else {
        messageApi.error(msg);
        refreshCaptchaImage();
      }
    } catch (err) {
      throw new Error(`Error: ${err}`);
    } finally {
      setSubmitLoading(false);
    }
  };

  /**
   *  统一认证访问的请求
   */
  const handleLoginByUnifiedAuth = async () => {
    if (!unifiedAuthCode) return;

    try {
      const { code, data, msg } = await onHandleUnifiedAuth(unifiedAuthCode!);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }

      // 根据后端返回的 isSyncAccount 字段判断该用户是否在本系统已与统一平台进行了绑定
      // true 则已绑定， false 则未绑定，需要引导用户进行绑定
      if (data?.isSyncAccount) {
        // 储存token
        if (!data?.token) {
          messageApi.error('系统未返回认证Token, 请检查');
          return;
        }
        setToken(data?.token);
        // 获取用户信息
        const _userInfoIsSuc = await queryUserInfos();
        if (!_userInfoIsSuc) {
          messageApi.error('获取用户信息异常, 请检查');
          return;
        }
        // 获取菜单信息
        const _menuIsSuc = await getUserMenus();
        if (!_menuIsSuc) {
          messageApi.error('获取用户菜单数据异常,请检查');
          return;
        }

        // 获取年份枚举
        await queryYearList();

        if (_userInfoIsSuc && _menuIsSuc) {
          const currentUrl = new URL(window.location.href);
          currentUrl.searchParams.delete('code');
          currentUrl.searchParams.delete('redirect');

          // 检查是否有重定向URL，如果有则跳转到该URL，否则跳转到首页
          if (unifiedAuthRedirectUrl) {
            window.location.href = `/#/${unifiedAuthRedirectUrl}`;
          } else {
            window.location.href = currentUrl?.pathname + '#/';
          }
        }
      } else {
        // 账号绑定
        // 获取返回的统一认证平台的账号
        const _ssoUserName = data?.ssoUserName;
        if (!_ssoUserName) {
          messageApi.error('统一认证平台账号不存在，认证失败!');
          return;
        }
        if (ssoUserNameRef.current) {
          ssoUserNameRef.current.ssoUserName = _ssoUserName;
        }
        setAccountBindModalIsOpen(true);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const [captchaImage, setCaptchaImage] = useState('');
  const [needCaptcha, setNeedCaptcha] = useState(true);
  const [uuid, setUuid] = useState('');

  const refreshCaptchaImage = async () => {
    try {
      const { code, msg, data } = await getCaptchaImage();
      if (code === 200) {
        if (data.captchaEnabled) {
          setNeedCaptcha(true);
          setCaptchaImage('data:image/gif;base64,' + data.img);
          setUuid(data.uuid);
        } else {
          setNeedCaptcha(false);
        }
      } else {
        messageApi.error(msg);
      }
    } catch (err) {
      throw new Error(`Error: ${err}`);
    } finally {
    }
  };

  // 绑定界面的 Loading
  const [bindLoading, setBindLoding] = useState<boolean>(false);

  /**
   *  渲染已有账号登录DOM
   */
  const renderUserSigninElement = () => {
    return (
      <Form
        name="signInForm"
        form={signInForm}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 19 }}
        autoComplete="off"
      >
        <Form.Item
          label="用户名"
          name="username"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password />
        </Form.Item>
      </Form>
    );
  };

  /**
   *  渲染无账号注册并绑定DOM
   */
  const renderUserRegisterAndBindElement = () => {
    return (
      <Form
        name="registerAndSigninForm"
        form={registerAndSigninForm}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 19 }}
        autoComplete="off"
      >
        <Form.Item
          label="用户名"
          name="username"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="用户名" />
        </Form.Item>
        <Form.Item
          label="密码"
          name="password"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password placeholder="密码" />
        </Form.Item>
        <Form.Item
          label="手机号码"
          name="phone"
          rules={[
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号码！',
            },
          ]}
        >
          <Input placeholder="手机号码" />
        </Form.Item>
        <Form.Item
          label="性别"
          name="sex"
          rules={[{ required: true, message: '请选择性别' }]}
        >
          <Radio.Group onChange={() => {}} value="0">
            <Radio value="0">男</Radio>
            <Radio value="1">女</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="姓名"
          name="name"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="用户姓名" />
        </Form.Item>
      </Form>
    );
  };

  /**
   *
   */
  const handleAccountBindOperator = async () => {
    if (!ssoUserNameRef?.current?.ssoUserName) {
      messageApi.error('认证平台账号异常，请检查');
      return;
    }

    // 获取当前的Tab的激活的Key，判断是登录绑定，还是注册绑定
    if (curSelectedTab === '1') {
      try {
        setBindLoding(true);

        // 登录绑定
        const { username, password } = signInForm?.getFieldsValue();

        const { code, data, msg } = await signInBindAccount({
          username,
          password,
          ssoUserName: ssoUserNameRef?.current?.ssoUserName,
        });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          messageApi.error(msg);
          return;
        }

        setAccountBindModalIsOpen(false);

        // 储存token
        if (!data?.token) {
          messageApi.error('系统未返回认证Token, 请检查');
          return;
        }
        setToken(data?.token);
        // 获取用户信息
        const _userInfoIsSuc = await queryUserInfos();
        if (!_userInfoIsSuc) {
          messageApi.error('获取用户信息异常, 请检查');
          return;
        }
        // 获取菜单信息
        const _menuIsSuc = await getUserMenus();
        if (!_menuIsSuc) {
          messageApi.error('获取用户菜单数据异常,请检查');
          return;
        }

        // 获取年份枚举
        await queryYearList();

        if (_userInfoIsSuc && _menuIsSuc) {
          const currentUrl = new URL(window.location.href);
          currentUrl.searchParams.delete('code');

          // 检查是否有重定向URL，如果有则跳转到该URL，否则跳转到首页
          if (unifiedAuthRedirectUrl) {
            window.location.href = `/#/${unifiedAuthRedirectUrl}`;
          } else {
            window.location.href = currentUrl?.pathname + '#/';
          }
        }
      } catch (err) {
        throw new Error(`Error: err`);
      } finally {
        setBindLoding(false);
      }
    } else {
      try {
        setBindLoding(true);

        const { username, password, phone, sex, name } =
          registerAndSigninForm?.getFieldsValue();

        const { code, data, msg } = await signUpAndSignInBindAccount({
          username,
          password,
          phone,
          sex,
          name,
          ssoUserName: ssoUserNameRef?.current?.ssoUserName,
        });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          messageApi.error(msg);
          return;
        }

        setAccountBindModalIsOpen(false);

        // 储存token
        if (!data?.token) {
          messageApi.error('系统未返回认证Token, 请检查');
          return;
        }
        setToken(data?.token);
        // 获取用户信息
        const _userInfoIsSuc = await queryUserInfos();
        if (!_userInfoIsSuc) {
          messageApi.error('获取用户信息异常, 请检查');
          return;
        }
        // 获取菜单信息
        const _menuIsSuc = await getUserMenus();
        if (!_menuIsSuc) {
          messageApi.error('获取用户菜单数据异常,请检查');
          return;
        }

        // 获取年份枚举
        await queryYearList();

        if (_userInfoIsSuc && _menuIsSuc) {
          const currentUrl = new URL(window.location.href);
          currentUrl.searchParams.delete('code');

          // 检查是否有重定向URL，如果有则跳转到该URL，否则跳转到首页
          if (unifiedAuthRedirectUrl) {
            window.location.href = unifiedAuthRedirectUrl;
          } else {
            window.location.href = currentUrl?.pathname + '#/';
          }
        }
      } catch (err) {
        throw new Error(`Error: err`);
      } finally {
        setBindLoding(false);
      }
    }
  };
const getUrlParam = (paramName: string): string | null => {
  // 先尝试从查询字符串中获取参数
  let urlParams = new URLSearchParams(window.location.search);
  let paramValue = urlParams.get(paramName);

  if (paramValue === null) {
    // 如果查询字符串中没有，再尝试从哈希部分获取参数
    const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
    paramValue = hashParams.get(paramName);
  }

  return paramValue;
};
  useEffect(() => {
    // 从统一认证平台过来的访问
    console.log(window.location.href.includes('code') && window.location.search, window.location.href.includes('code'), window.location.search);
    
    if (window.location.href.includes('code')) {
        console.log('当前 URL:', window.location.href);
        const _code = getUrlParam('code');
        const _redirectUrl = getUrlParam('redirect');
        console.log('_code:', _code);
        console.log('_redirectUrl:', _redirectUrl);
    
      if (_code) {
        setIsUnifiedAuth(true);
        setUnifiedAuthCode(_code);

        // 如果存在redirect参数，则设置跳转URL
        if (_redirectUrl) {
          setUnifiedAuthRedirectUrl(_redirectUrl);
        }
      } else {
        setIsUnifiedAuth(false);
        setUnifiedAuthCode(undefined);
        refreshCaptchaImage();
      }
    } else {
      setIsUnifiedAuth(false);
      setUnifiedAuthCode(undefined);
      refreshCaptchaImage();
    }
  }, []);

  useEffect(() => {
    if (unifiedAuthCode) {
      handleLoginByUnifiedAuth();
    }
  }, [unifiedAuthCode]);

  return (
    <>
      {contextHolder}
      {isUnifiedAuth ? (
        <div className="unified-auth__page">
          <div className="loader"></div>
          <div className="text">正在进行统一认证,请稍候...</div>
        </div>
      ) : (
        
        <div className="w-screen h-screen overflow-hidden loginStyleContainer">
          
          <video
            src="https://xinrui-material-resource-ejc.oss-cn-qingdao.aliyuncs.com/video5.mp4"
            autoPlay
            loop
            muted
            className="videoStyle"
          ></video>
          <div className="videoModel"></div>
          <div className="ModelRight"></div>
          <div className="w-[500px] absolute top-1/2 right-[280px] transform overflow-hidden -translate-y-1/2 shadow-2xl !rounded-xl py-[50px] bg-[#f5f5f5] box-border">
            <LoginForm
              formRef={formRef}
              // logo={loginPath}
              title={<div className="text-2xl">{appTitle}</div>}
              onFinish={onSubmit}
              submitter={{
                render: (props: any, doms: any) => {
                  return (
                    <Button
                      htmlType="submit"
                      type="primary"
                      className="w-full"
                      size="large"
                      loading={submitLoading}
                    >
                      登录
                    </Button>
                  );
                },
              }}
            >
              {loginType === 'account' && (
                <div className="">
                  <ProFormText
                    name="username"
                    fieldProps={{
                      size: 'large',
                      prefix: <UserOutlined className={'prefixIcon'} />,
                      placeholder: '请输入用户名',
                      onChange: (e: { target: { value: string } }) => {
                        formRef.current?.setFieldValue(
                          'username',
                          e.target.value.trim()
                        );
                      },
                    }}
                    placeholder={''}
                    rules={[
                      {
                        required: true,
                        message: '请输入账号!',
                      },
                    ]}
                  />
                  <ProFormText.Password
                    name="password"
                    fieldProps={{
                      size: 'large',
                      prefix: <LockOutlined className={'prefixIcon'} />,
                      placeholder: '请输入密码',
                      onChange: (e: { target: { value: string } }) => {
                        formRef.current?.setFieldValue(
                          'password',
                          e.target.value.trim()
                        );
                      },
                    }}
                    placeholder={''}
                    rules={[
                      {
                        required: true,
                        message: '请输入密码！',
                      },
                    ]}
                  />
                  {needCaptcha && (
                    <ProFormText
                      name="code"
                      addonAfter={
                        <Image
                          width={75}
                          height={38}
                          preview={false}
                          src={captchaImage}
                          className="rounded-md cursor-pointer"
                          onClick={() => refreshCaptchaImage()}
                        />
                      }
                      fieldProps={{
                        size: 'large',
                        prefix: <PictureOutlined className={'prefixIcon'} />,
                        placeholder: '请输入验证码',
                        onChange: (e: { target: { value: string } }) => {
                          formRef.current?.setFieldValue(
                            'code',
                            e.target.value.trim()
                          );
                        },
                      }}
                      placeholder={''}
                      rules={[
                        {
                          required: true,
                          message: '请输入验证码！',
                        },
                      ]}
                    />
                  )}
                </div>
              )}
            </LoginForm>
            <div className="font-bold text-red-500 text-center">
              注: 推荐使用Microsoft Edge或Google浏览器访问本系统
            </div>
          </div>
          {import.meta.env.VITE_SHOW_COPYRIGHT === 'true' ? (
            <div className="absolute left-0 right-[400px] mx-auto text-right bottom-5 text-slate-400">
              <div className="inline-block translate-x-[200px]">
                主办单位: 贵州省疾病预防控制中心 黔ICP备17011643号-1 客服电话:
                15802850751 19113169796
              </div>
              <a
                target="_blank"
                href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************"
                rel="noreferrer"
              >
                <p>
                  <img
                    src={baIcon}
                    className=" inline-block translate-y-1.5 mr-1"
                  />
                  贵公网安备 **************号
                </p>
              </a>
              {/* Copyright &copy;{new Date().getFullYear()} {COMPANY} version{' '}
          {APP_VERSION} */}
            </div>
          ) : null}
        </div>
      )}

      <Modal
        title="账号绑定"
        open={accountBindModalIsOpen}
        centered
        onOk={handleAccountBindOperator}
        onCancel={() => setAccountBindModalIsOpen(false)}
        width={400}
        confirmLoading={bindLoading}
        maskClosable={false}
      >
        <Tabs
          defaultActiveKey="1"
          centered
          items={[
            {
              key: '1',
              label: '已有账号绑定',
              children: renderUserSigninElement(),
            },
            {
              key: '2',
              label: '账号注册并绑定',
              children: renderUserRegisterAndBindElement(),
            },
          ]}
          onChange={(activeKey: string) => setCurSelectedTab(activeKey)}
        />
      </Modal>
    </>
  );
};
export default Login;
