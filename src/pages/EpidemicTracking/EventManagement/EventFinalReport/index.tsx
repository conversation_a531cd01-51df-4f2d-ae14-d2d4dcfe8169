/*
 * @Date: 2024-07-12 14:38:45
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 09:41:00
 * @FilePath: \xr-qc-jk-web\src\pages\EpidemicTracking\EventManagement\EventFinalReport\index.tsx
 * @Description: 事件终报
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { eventListApi, TEventListParams } from '@/api/event';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

const EventFinalReport: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('2');
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  // 登记终报
  const [editOpen, setEditOpen] = useState(false);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const getColumns = useCallback((): ProColumns[] => {
    return [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 48,
        title: '序号',
      },
      {
        title: '事件编号',
        dataIndex: 'eventCode',
      },
      {
        title: '发生时间',
        dataIndex: 'eventTime',
        hideInSearch: true
      },
      {
        title: '发生时间',
        dataIndex: 'eventTime',
        valueType: 'dateRange',
        hideInTable: true
      },
      {
        title: '发生地区',
        dataIndex: 'area',
        valueType: 'cascader',
        request: async () => {
          const { code, data, msg } = await getAllCityAreaList({});
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return [];
          }
          // 转换
          const _finalDataList = convertToCascading(data);
          return _finalDataList;
        },
      },
      {
        title: '详细地址',
        dataIndex: 'eventAddr',
        hideInSearch: true,
      },
      {
        title: '事件描述',
        dataIndex: 'eventContent',
        hideInSearch: true,
      },
      {
        title: '上报单位',
        dataIndex: 'deptName',
      },
      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        width: 160,
        render: (text, record, _, action) => [
          <>
            {activeKey === '2' ? (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setCurEventId(record.evenId);
                  setEditOpen(true);
                }}
                key="edit"
              >
                登记终报
              </Button>
            ) : null}
          </>,

          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurEventId(record.evenId);
              setDetailOpen(true);
            }}
            key="detail"
          >
            详情
          </Button>,
        ],
      },
    ];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '2',
                label: <span>待终报</span>,
              },
              {
                key: '3',
                label: <span>已终报</span>,
              },
            ],
            onChange: (key) => {
              // console.log(key);

              setActiveKey(key as string);
            },
          },
        }}
        request={async (params, sort, filter) => {
          const _params: TEventListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            status: activeKey,
            eventCode: params.eventCode,
            deptName: params.deptName,
          };
          if (params.eventTime && params.eventTime.length) {
            _params.startDate = params.eventTime[0];
            _params.endDate = params.eventTime[1];
          }
          if (params.area && params.area.length) {
            _params.cityCode = params.area[0];
            _params.areaCode = params.area[1];
          }
          const { code, data, msg } = await eventListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
      {/* 登记终报 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
        }}
        id={curEventId}
      />
    </PageContainer>
  );
};

export default EventFinalReport;
