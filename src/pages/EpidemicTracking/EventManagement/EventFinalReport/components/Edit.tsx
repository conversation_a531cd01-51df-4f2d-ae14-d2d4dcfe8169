import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, Form, message } from 'antd';
import { eventDetailApi, eventFinalReportCommitApi } from '@/api/event';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import {
  ProForm,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import RichTextEditor from '@/components/RichTextEditor';
import EventFile from '@/pages/EpidemicTracking/components/EventFile';
import EventFollowUpInfo from '@/pages/EpidemicTracking/components/EventFollowUpInfo';
import EventReportingInfo from '@/pages/EpidemicTracking/components/EventReportingInfo';
import EventVerificationInfo from '@/pages/EpidemicTracking/components/EventVerificationInfo';
import { TFollowUpList } from '@/pages/EpidemicTracking/type';
import { getFileTypeByName } from '@/utils/upload';
import EProFormUploadButton from '@/components/EProFormUploadButton';


type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({ open, setOpen, id }) => {
  const { token } = useTokenStore();

  const [btnLoading, setBtnLoading] = useState(false);

  const formRef = useRef<ProFormInstance>(null);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const [followUpList, setFollowUpList] = useState<TFollowUpList[]>([]);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
// 定义 RichTextEditor 实例类型
interface RichTextEditorInstance {
  handleGetData: () => string;
}

  // 更新 formRef2 的类型
  const formRef2 = useRef<RichTextEditorInstance>(null); 



  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data.event);
        setFollowUpList(data.appends);
        const _formVal = { ...data.appends[0] };
        if (_formVal.appendFileArr) {
          try {
          const _files = JSON.parse(_formVal.appendFileArr);
          // 转换为更完整的文件列表格式
          const fileList = _files.map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));
          // console.log(fileList, 'fileList');
          formRef.current?.setFieldValue('files', fileList);
        } catch (parseError) {
          console.error('解析 eventFile 失败:', parseError);
          message.error('解析附件数据失败，请检查数据格式');
        }
        }



      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  const handleSubmit = async () => {
    
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      if (values.files && values.files.length) {
        const _newFiles = values.files.filter(
          (_item: any) => _item.response.code === 200
        );
        if (_newFiles.length) {
          const  fileArr = _newFiles.map((_item: any) => ({
            url: _item.response.data.url,
            fileName: _item.response.data.fileName,
            size: _item.size,
            ossId: _item.response.data.ossId,
          }));
          values.summaryFileArr = JSON.stringify(fileArr);

        }
      }
      if (!formRef2.current?.handleGetData()) {
        message.error('终报信息必填！');
        return
      }
      values.summaryContent = formRef2.current?.handleGetData();
      // console.log(values);
      // return;
      const { code, msg } = await eventFinalReportCommitApi({
        ...values,
        eventId: id,
        summaryTime: dayjs(values.summaryTime).format('YYYY-MM-DD hh:mm:ss'),
        summaryId: 1,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };
  return (
    <Drawer
      width="90%"
      title="登记终报"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <BlockContainer title="事件上报信息">
            <EventReportingInfo dataSource={detailInfo} />
          </BlockContainer>

          <BlockContainer title="事件核实信息">
            <EventVerificationInfo dataSource={detailInfo} />
          </BlockContainer>

          <BlockContainer title="事件初报文书">
            <div className="w-full">
              {detailInfo?.confirmFile ? (
                <EventFile fileGroupId={detailInfo?.confirmFile} />
              ) : null}
            </div>
          </BlockContainer>
          <BlockContainer title="事件续报信息">
            <>
              {followUpList.length
                ? followUpList.map((_item, _index) => (
                    <EventFollowUpInfo dataSource={_item} index={_index} />
                  ))
                : null}
            </>
          </BlockContainer>
          <BlockContainer title="事件终报信息">
            <ProForm
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormDateTimePicker
                name="summaryTime"
                label="结案时间"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '结案时间必填！' }]}
              />
              <ProFormText
                name="summary"
                label="终报人"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '终报人必填！' }]}
              />
              <div className=" w-1/3"></div>
              <Form.Item
                className=" w-full"
                name="summaryContent"
                label="终报信息"
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
                rules={[{ required: true, message: '终报信息必填！' }]}
              >
                <RichTextEditor   onRef={formRef2}/>
              </Form.Item>
              {/* <ProFormUploadButton
                colProps={{ span: 24 }}
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
                name="files"
                label="终报文书"
                rules={[{ required: true, message: '终报文书必填！' }]}
                fieldProps={{
                  name: 'file',
                  onChange(info: any) {
                    const { file } = info;
                    if (file.status === 'done' || file.status === 'removed') {
                      if (
                        file.status === 'done' &&
                        file.response &&
                        file.response.code !== 200
                      ) {
                        message.error(file.response.msg);
                      } else {
                        message.success(file.response.msg);
                      }
                    }
                  },
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                  listType: 'picture-card',
                  // className: 'upload-list-inline',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      if (type === 'Image') {
                        const d = await getFileData(file.response.data.ossId);
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        downloadFile(
                          file.response.data.ossId,
                          file.response.data.fileName
                        );
                      }
                    }
                  },
                }}
                action={uploadFiles}
              /> */}


            <EProFormUploadButton
                name="files"
                label="终报文书"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={10}
                // readonly={readonly}
              />

            </ProForm>
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
            保存并提交
          </Button>
        </div>
        {/* 文件预览 */}
        <FileView
          open={isShowFileView}
          file={isShowFileData}
          closeDetail={() => {
            setIsShowFileView(false);
          }}
        />
      </div>
    </Drawer>
  );
};

export default Edit;
