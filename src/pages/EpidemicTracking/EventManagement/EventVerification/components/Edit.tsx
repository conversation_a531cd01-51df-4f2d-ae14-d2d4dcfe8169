import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, Form, message } from 'antd';
import { eventDetailApi, eventVerificationCommitApi } from '@/api/event';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import {
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import RichTextEditor from '@/components/RichTextEditor';
import EventReportingInfo from '@/pages/EpidemicTracking/components/EventReportingInfo';
import { getFileTypeByName } from '@/utils/upload';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import { Any } from '@react-spring/web';
import './index.less';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({ open, setOpen, id }) => {
  const { token } = useTokenStore();

  const [btnLoading, setBtnLoading] = useState(false);
// 定义 RichTextEditor 实例类型


  const formRef = useRef<ProFormInstance>(null);

interface RichTextEditorInstance {
  handleGetData: () => string;
}


  // 更新 formRef2 的类型
  const formRef2 = useRef<RichTextEditorInstance>(null); 



  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data.event);

        const _formVal = { ...data.event };

        if (_formVal.confirmFileArr) {
          try {
          const _files = JSON.parse(_formVal.confirmFileArr);
          // 转换为更完整的文件列表格式
          const fileList = _files.map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));
          formRef.current?.setFieldValue('files', fileList);
        } catch (parseError) {
          console.error('解析 eventFile 失败:', parseError);
          message.error('解析附件数据失败，请检查数据格式');
        }

        }
        formRef.current?.setFieldsValue(_formVal);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  const handleSubmit = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();
      if (values.files && values.files.length) {
        const _newFiles = values.files.filter(
          (_item: any) => _item.response.code === 200
        );
        if (_newFiles.length) {
          const  fileArr= values.files = _newFiles.map((_item: any) => ({
            url: _item.response.data.url,
            fileName: _item.response.data.fileName,
            size: _item.size,
            ossId: _item.response.data.ossId
          }));
          values.confirmFileArr = JSON.stringify(fileArr);
        }
      }
      console.log(formRef2.current?.handleGetData(), formRef2.current);
      
      values.confirmContent = formRef2.current?.handleGetData();
      // console.log(values);
      // return
      const { code, msg } = await eventVerificationCommitApi({
        ...values,
        eventId: id,
        confirmId: 1,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };
  return (
    <Drawer
      width="90%"
      title="核实"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full ">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          <BlockContainer title="事件上报信息">
            <EventReportingInfo dataSource={detailInfo} />
          </BlockContainer>

          <BlockContainer title="事件核实信息">
            <ProForm
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormRadio.Group
                name="eventTypeCode"
                label="事件类型"
                colProps={{ span: 8 }}
                options={[
                  { label: '真事件', value: 1 },
                  { label: '假事件', value: 0 },
                ]}
                rules={[{ required: true, message: '事件类型必填！' }]}
              />
              <ProFormText
                name="confirm"
                label="核实人"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '核实人必填！' }]}
              />
              <div className=" w-1/3"></div>
              <Form.Item
                className=" w-full hs-msg"
                name="confirmContent"
                label="核实信息"
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
               
              >
                <RichTextEditor    onRef={formRef2}/>
              </Form.Item>
              {/* <ProFormUploadButton
                colProps={{ span: 24 }}
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
                name="files"
                label="初报文书"
                rules={[{ required: true, message: '初报文书必填！' }]}
                fieldProps={{
                  name: 'file',
                  onChange(info: any) {
                    const { file } = info;
                    if (file.status === 'done' || file.status === 'removed') {
                      if (
                        file.status === 'done' &&
                        file.response &&
                        file.response.code !== 200
                      ) {
                        message.error(file.response.msg);
                      } else {
                        message.success(file.response.msg);
                      }
                    }
                  },
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                  listType: 'picture-card',
                  // className: 'upload-list-inline',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      if (type === 'Image') {
                        const d = await getFileData(file.response.data.ossId);
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        downloadFile(
                          file.response.data.ossId,
                          file.response.data.fileName
                        );
                      }
                    }
                  },
                }}
                action={uploadFiles}
              /> */}

            <EProFormUploadButton
                name="files"
                label="初报文书"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={10}
                // readonly={readonly}
              />
            </ProForm>

            <FileView
              open={isShowFileView}
              file={isShowFileData}
              closeDetail={() => {
                setIsShowFileView(false);
              }}
            />
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
            保存并提交
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default Edit;
