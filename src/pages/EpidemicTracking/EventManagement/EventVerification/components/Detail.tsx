import { useEffect, useState,useRef } from 'react';
import { Drawer, message } from 'antd';
import { eventDetailApi } from '@/api/event';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';
import EventFile from '@/pages/EpidemicTracking/components/EventFile';
import EventReportingInfo from '@/pages/EpidemicTracking/components/EventReportingInfo';
import EventVerificationInfo from '@/pages/EpidemicTracking/components/EventVerificationInfo';
import EProFormUploadButton from '@/components/EProFormUploadButton';

import {
  ProFormInstance,
  ProForm,
} from '@ant-design/pro-components';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const close = () => {
    setOpen(false);
    setDetailInfo(undefined);
  };

  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  const formRef3 = useRef<ProFormInstance>(null); 

  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data.event);
        const _formVal = { ...data.appends[0] };
         if (_formVal.appendFileArr) {
            try {
            const _files = JSON.parse(_formVal.appendFileArr);
            const fileList = _files.map((_item: any) => ({
                  uid: _item.ossId,
                  name: _item.fileName,
                  status: 'done',
                  type: 'application/msword',
                  url: _item.url,
                  response: {
                    code: 200,
                    data: {
                      fileName: _item.fileName,
                      ossId: _item.ossId,
                      url: _item.url,
                    },
                  },

            }));

            formRef3.current?.setFieldValue('files', fileList);
          } catch (parseError) {
            console.error('解析 eventFile 失败:', parseError);
            message.error('解析附件数据失败，请检查数据格式');
          }
        }


      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  return (
    <Drawer
      width="90%"
      title="详情"
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full p-4 overflow-y-auto bg-white gap-4">
        <BlockContainer title="事件上报信息">
          <EventReportingInfo dataSource={detailInfo} />
        </BlockContainer>

        <BlockContainer title="事件核实信息">
          <EventVerificationInfo dataSource={detailInfo} />
        </BlockContainer>

        <BlockContainer title="事件初报文书">
 
            <ProForm
              className="p-6"
              formRef={formRef3}
              layout="horizontal"
              grid={true} submitter={false}
              rowProps={{
                gutter: [24, 0],
              }}
              // {...formItemLayout}
            >
              <EProFormUploadButton
                  name="files"
                  label="附件信息"
                  colProps={{ span: 24 }}
                  labelCol={{ flex: 0.005 }}
                  readonly={true}
                />
            </ProForm>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
