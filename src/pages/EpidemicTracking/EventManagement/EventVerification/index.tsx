/*
 * @Date: 2024-07-12 14:40:54
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-03 17:42:38
 * @FilePath: /xr-qc-jk-web/src/pages/EpidemicTracking/EventManagement/EventVerification/index.tsx
 * @Description: 事件核实
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { eventListApi, TEventListParams } from '@/api/event';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { TTableData } from '../../type';

const EventVerification: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('1');
  // 统计数量
  const [counts, setCounts] = useState({
    unVerify: 0,
    verify: 0,
  });
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  // 核实
  const [editOpen, setEditOpen] = useState(false);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const getColumns = useCallback((): ProColumns<TTableData>[] => {
    return [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 48,
        title: '序号',
      },
      {
        title: '事件编号',
        dataIndex: 'eventCode',
      },
      {
        title: '发生时间',
        dataIndex: 'eventTime',
        hideInSearch: true,
      },
      {
        title: '发生时间',
        dataIndex: 'eventTime',
        valueType: 'dateRange',
        hideInTable: true,
      },
      {
        title: '发生地区',
        dataIndex: 'area',
        valueType: 'cascader',
        request: async () => {
          const { code, data, msg } = await getAllCityAreaList({});
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return [];
          }
          // 转换
          const _finalDataList = convertToCascading(data);
          return _finalDataList;
        },
      },
      {
        title: '详细地址',
        dataIndex: 'eventAddr',
        hideInSearch: true,
      },
      {
        title: '事件描述',
        dataIndex: 'eventContent',
        hideInSearch: true,
      },
      {
        title: '上报单位',
        dataIndex: 'deptName',
      },
      {
        title: '上报时间',
        dataIndex: 'createTime',
        hideInSearch: true,
      },

      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        width: 160,
        render: (text, record, _, action) => [
          <>
            {activeKey === '1' ? (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setEditOpen(true);
                  setCurEventId(record.evenId);
                }}
                key="edit"
              >
                核实
              </Button>
            ) : null}
          </>,
          <Button
            type="link"
            size="small"
            onClick={() => {
              setDetailOpen(true);
              setCurEventId(record.evenId);
            }}
            key="detail"
          >
            详情
          </Button>,
        ],
      },
    ];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable<TTableData>
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '1',
                label: <span>待核实（{counts.unVerify}）</span>,
              },
              {
                key: '2',
                label: <span>已核实（{counts.verify}）</span>,
              },
            ],
            onChange: (key) => {
              // console.log(key);

              setActiveKey(key as string);
            },
          },
        }}
        request={async (params, sort, filter) => {
          let err = false;
          const _params: TEventListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            status: activeKey,
            eventCode: params.eventCode,
            deptName: params.deptName,
          };
          if (params.eventTime && params.eventTime.length) {
            _params.startDate = params.eventTime[0];
            _params.endDate = params.eventTime[1];
          }
          if (params.area && params.area.length) {
            _params.cityCode = params.area[0];
            _params.areaCode = params.area[1];
          }
          const { code, data, msg } = await eventListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }
          if (init && !err) {
            const res = await eventListApi({
              ..._params,
              status: activeKey === '1' ? '2' : '1',
            });
            setCounts({
              unVerify: activeKey === '1' ? data.total : res.data.total,
              verify: activeKey === '2' ? data.total : res.data.total,
            });
            setInit(false);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 核实 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
        }}
        id={curEventId}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
    </PageContainer>
  );
};

export default EventVerification;
