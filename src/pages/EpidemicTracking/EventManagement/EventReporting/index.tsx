/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { deleteEventApi, eventListApi, TEventListParams } from '@/api/event';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { TTableData } from '../../type';

const EventQuery: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  // 查询条件
  const [searchParams, setSearchParams] = useState<TEventListParams>();
  // 统计数量
  const [counts, setCounts] = useState({
    draft: 0,
    reporting: 0,
  });
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  // 新增/编辑
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit'>('add');

  const [curEventCode, setCurEventCode] = useState<string>();

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const columns: ProColumns<TTableData>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '事件编号',
      dataIndex: 'eventCode',
    },
    {
      title: '发生时间',
      dataIndex: 'eventTime',
      hideInSearch: true,
    },
    {
      title: '发生时间',
      dataIndex: 'eventTime',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '发生地区',
      dataIndex: 'area',
      valueType: 'cascader',
      request: async () => {
        const { code, data, msg } = await getAllCityAreaList({});
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return [];
        }
        // 转换
        const _finalDataList = convertToCascading(data);
        return _finalDataList;
      },
    },
    {
      title: '详细地址',
      dataIndex: 'eventAddr',
      hideInSearch: true,
    },
    {
      title: '事件描述',
      dataIndex: 'eventContent',
      hideInSearch: true,
    },
    {
      title: '上报单位',
      dataIndex: 'deptName',
    },
  ];

  // 选择表头多选框
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleDelete = async (ids: string[]) => {
    try {
      const { code, msg } = await deleteEventApi(ids);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const getColumns = useCallback((): ProColumns<TTableData>[] => {
    if (activeKey === '0') {
      return [
        ...columns,
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 160,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurEventId(record.evenId);
                setCurEventCode(record?.eventCode);
                setEditType('edit');
                setEditOpen(true);
              }}
              key="edit"
            >
              修改
            </Button>,
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurEventId(record.evenId);
                setDetailOpen(true);
              }}
              key="detail"
            >
              详情
            </Button>,
            <Popconfirm
              title="删除"
              description="确认删除该草稿?"
              onConfirm={() => handleDelete([record.evenId])}
              key="del"
            >
              <Button type="link" size="small" danger>
                删除
              </Button>
            </Popconfirm>,
          ],
        },
      ];
    } else {
      return [
        ...columns,
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 80,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurEventId(record.evenId);
                setDetailOpen(true);
              }}
              key="detail"
            >
              详情
            </Button>,
          ],
        },
      ];
    }
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable<TTableData>
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: <span>草稿（{counts.draft}）</span>,
              },
              {
                key: '1',
                label: <span>已上报（{counts.reporting}）</span>,
              },
            ],
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params, sort, filter) => {
          let err = false;
          const _params: TEventListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            status: activeKey,
            eventCode: params.eventCode,
            deptName: params.deptName,
          };
          if (params.eventTime && params.eventTime.length) {
            _params.startDate = params.eventTime[0];
            _params.endDate = params.eventTime[1];
          }
          if (params.area && params.area.length) {
            _params.cityCode = params.area[0];
            _params.areaCode = params.area[1];
          }
          setSearchParams(_params);
          const { code, data, msg } = await eventListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }
          if (init && !err) {
            const res = await eventListApi({
              ..._params,
              status: activeKey === '1' ? '0' : '1',
            });
            setCounts({
              draft: activeKey === '0' ? data.total : res.data.total,
              reporting: activeKey === '1' ? data.total : res.data.total,
            });
            setInit(false);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        rowSelection={
          activeKey === '0'
            ? {
                // 注释该行则默认不显示下拉选项
                // selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [],
                onChange: onSelectChange,
              }
            : false
        }
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新增事件
          </Button>,
          <>
            {activeKey === '0' ? (
              <Button
                type="default"
                danger
                key="del"
                onClick={() => {
                  if (!selectedRowKeys.length) {
                    message.warning('请选择需要删除的事件！');
                  } else {
                    handleDelete(selectedRowKeys);
                  }
                }}
              >
                批量删除
              </Button>
            ) : null}
          </>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          setEditOpen(val);
          tableReload();
        }}
        type={editType}
        id={curEventId}
        curEventCode={curEventCode}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
    </PageContainer>
  );
};

export default EventQuery;
