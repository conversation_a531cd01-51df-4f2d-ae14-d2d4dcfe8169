import { useEffect, useState } from 'react';
import { Drawer, message } from 'antd';
import { eventDetailApi } from '@/api/event';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';
import EventReportingInfo from '@/pages/EpidemicTracking/components/EventReportingInfo';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      console.log({ code, data, msg });
      console.log(code === 200, 'code === 200');

      if (code === 200) {
        console.log(222222);
        if (
          data.event.eventFile &&
          JSON.parse(data.event.eventFile).length > 0
        ) {
          setDetailInfo({
            ...data.event,
            eventFile: JSON.parse(data.event.eventFile),
          });
        } else {
          setDetailInfo({ ...data.event });
        }
      } else {
        console.log(33333333);
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    console.log(id, 'id');
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]); // 将 open 和 id 加入依赖项

  return (
    <Drawer
      width="80%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" w-full h-full flex flex-col p-4">
        {detailInfo && (
          <BlockContainer title="事件上报详情">
            <EventReportingInfo dataSource={detailInfo} />
          </BlockContainer>
        )}
      </div>
    </Drawer>
  );
};

export default Detail;
