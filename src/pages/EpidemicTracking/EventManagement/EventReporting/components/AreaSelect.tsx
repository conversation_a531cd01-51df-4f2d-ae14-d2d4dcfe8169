import { useEffect, useState } from 'react';
import { message, Select } from 'antd';
import { areaListApi } from '@/api/common';
import { TAreaList, TCityList } from '@/pages/EpidemicTracking/type';

type TValue = {
  cityId?: number;
  cityName?: string;
  areaId?: number;
  areaName?: string;
};

type TAreaSelectProps = {
  id?: string;
  value?: TValue;
  onChange?: (value: TValue) => void;
  cityList?: TCityList[];
};

const AreaSelect: React.FC<TAreaSelectProps> = ({
  value,
  onChange,
  cityList = [],
}) => {
  const [areaList, setAreaList] = useState<TAreaList[]>([]);
  const [selectCity, setSelectCity] = useState<number>();
  const getAreaList = async () => {
    try {
      const { code, data, msg } = await areaListApi({ cityId: selectCity! });
      if (code === 200) {
        setAreaList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    setSelectCity(value?.cityId)
  }, [value?.cityId])

  useEffect(() => {
    if (selectCity) {
      getAreaList();
    } else {
      setAreaList([]);
    }
  }, [selectCity]);
  return (
    <div className=" w-full flex justify-between">
      <Select
        style={{ width: '45%' }}
        allowClear
        value={value?.cityId}
        fieldNames={{
          label: 'cityName',
          value: 'cityId',
        }}
        options={cityList}
        onChange={(e, option) => {
          setSelectCity(e);
          onChange?.({
            ...value,
            cityId: (option as TCityList).cityId,
            cityName: (option as TCityList).cityName,
          });
        }}
        placeholder="市"
      />
      <Select
        style={{ width: '45%' }}
        allowClear
        value={value?.areaId}
        fieldNames={{
          label: 'areaName',
          value: 'areaId',
        }}
        options={areaList}
        onChange={(e, option) => {
          onChange?.({
            ...value,
            areaId: (option as TAreaList).areaId,
            areaName: (option as TAreaList).areaName,
          });
        }}
        placeholder="区"
      />
    </div>
  );
};

export default AreaSelect;
