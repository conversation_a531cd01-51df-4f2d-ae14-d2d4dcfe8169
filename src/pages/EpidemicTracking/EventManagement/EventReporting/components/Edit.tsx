/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { fileGroupApi, TFileGroupRes } from '@/api/common';
import { getDepartmentList } from '@/api/department';
import {
  eventDetailApi,
  eventReportingCommitApi,
  eventReportingSaveApi,
} from '@/api/event';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { convertToCascading, handleTree } from '@/utils';
import {
  ProForm,
  ProFormCascader,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { log } from 'console';
import dayjs from 'dayjs';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import FileView from '@/components/FileView';
import { getFileTypeByName } from '@/utils/upload';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: 'add' | 'edit';
  id: string;
  curEventCode?: string;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  id,
  curEventCode,
}) => {
  const { token } = useTokenStore();
  const formRef = useRef<ProFormInstance>(null);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);

  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [btnLoading, setBtnLoading] = useState(false);

  const [editFileList, setEditFileList] = useState<any>();

  const [selectArea, setSelectArea] = useState<{
    cityId: string;
    cityName: string;
    areaId: string;
    areaName: string;
  }>();

  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        console.log(data);
        const _formVal = { ...data.event };
        _formVal.dept = {
          label: _formVal.deptName,
          value: _formVal.deptId,
        };
        if (_formVal.cityId && _formVal.areaId) {
          _formVal.area = [_formVal.cityId + '', _formVal.areaId + ''];
          setSelectArea({
            cityId: _formVal.cityId + '',
            cityName: _formVal.cityName,
            areaId: _formVal.areaId + '',
            areaName: _formVal.areaName,
          });
        }
        if (_formVal.eventFile) {
          try {
            const _files = JSON.parse(_formVal.eventFile);
            // 转换为更完整的文件列表格式

            const fileList = _files.map((_item: any) => ({
              uid: _item.ossId,
              name: _item.fileName,
              status: 'done',
              type: 'application/msword',
              url: _item.url,
              response: {
                code: 200,
                data: {
                  fileName: _item.fileName,
                  ossId: _item.ossId,
                  url: _item.url,
                },
              },
            }));
            setEditFileList(fileList);
            // console.log(fileList, 'fileList');
            formRef.current?.setFieldValue('files', fileList);
          } catch (parseError) {
            console.error('解析 eventFile 失败:', parseError);
            message.error('解析附件数据失败，请检查数据格式');
          }
        }
        formRef.current?.setFieldsValue(_formVal);
        // if (_formVal.id) {
        //   getFileGroup(_formVal.id);
        // }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && type === 'edit' && id && getDetail(id);
  }, [open, id, type]);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const fetchSave = (saveType: number) => {
    if (saveType === 1) {
      return eventReportingSaveApi;
    } else {
      return eventReportingCommitApi;
    }
  };

  // 1保存 2提交
  const handleSave = async (saveType: number) => {
    try {
      setBtnLoading(true);
      let values = await formRef.current?.validateFields();
      values.eventTime = dayjs(values.eventTime).format('YYYY-MM-DD hh:mm:ss');
      if (values.dept) {
        values.deptId = values.dept.value;
        values.deptName = values.dept.label;
        delete values.dept;
      }
      if (selectArea) {
        values = { ...values, ...selectArea };
        delete values.area;
      }
      if (id) {
        values.eventId = id;
        values['eventCode'] = curEventCode;
      }
      if (values.files && values.files.length) {
        const _newFiles = values.files.filter(
          (_item: any) => _item.response.code === 200
        );

        if (_newFiles.length) {
          const fileArr = _newFiles.map((_item: any) => ({
            url: _item.response.data.url,
            fileName: _item.response.data.fileName,
            size: _item.size,
            ossId: _item.response.data.ossId,
          }));
          values.eventFile = JSON.stringify(fileArr);
        }
      }

      if (type === 'add') {
        //删除values中的eventId
        delete values.eventId;
      }

      const { code, msg } = await fetchSave(saveType)({
        ...values,
        committerId: 1,
        // deptId: 1,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="80%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormDateTimePicker
              name="eventTime"
              label="发生时间"
              rules={[{ required: true, message: '发生时间必填！' }]}
              fieldProps={{
                format: 'YYYY-MM-DD HH:mm:ss',
              }}
              colProps={{ span: 8 }}
            />
            <ProFormCascader
              name="area"
              label="发生地区"
              rules={[{ required: true, message: '发生地区必选！' }]}
              request={async () => {
                const { code, data, msg } = await getAllCityAreaList({});
                if (code !== codeDefinition.QUERY_SUCCESS) {
                  message.error(msg);
                  return [];
                }
                // 转换
                const _finalDataList = convertToCascading(data);
                return _finalDataList;
              }}
              fieldProps={{
                onChange: (value: any, selectedOptions: any) => {
                  if (selectedOptions && selectedOptions.length) {
                    setSelectArea({
                      cityId: selectedOptions[0].value,
                      cityName: selectedOptions[0].label,
                      areaId: selectedOptions[1].value,
                      areaName: selectedOptions[1].label,
                    });
                  } else {
                    setSelectArea(undefined);
                  }
                },
              }}
              colProps={{ span: 8 }}
            />
            <ProFormText
              name="eventAddr"
              label="详细地址"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '详细地址必填！' }]}
            />
            <ProFormTreeSelect
              name="dept"
              label="上报单位"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '上报单位必填！' }]}
              request={async () => {
                const { code, data, msg } = await getDepartmentList({});
                if (code !== codeDefinition.QUERY_SUCCESS) {
                  message.error(msg);
                }
                const d = handleTree(data, 'deptId');
                function setDisabled(d: any) {
                  d.forEach((item: any) => {
                    item.selectable = item.labTag === 1;
                    item.disabled = item.labTag !== 1;
                    if (item.children) {
                      setDisabled(item.children);
                    }
                  });
                }
                setDisabled(d);
                return d;
              }}
              fieldProps={{
                labelInValue: true,
                fieldNames: {
                  label: 'deptName',
                  value: 'deptId',
                  children: 'children',
                },
              }}
            />
            <div className=" w-1/3"></div>
            <ProFormText
              name="committer"
              label="上报人"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '上报人必填！' }]}
            />
            <ProFormText
              name="mobile"
              label="联系方式"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '申请人必填！' }]}
            />
            <ProFormText
              name="contactAddr"
              label="联系地址"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '联系地址必填！' }]}
            />
            <ProFormTextArea
              name="eventContent"
              label="事件描述"
              colProps={{ span: 24 }}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
              rules={[{ required: true, message: '事件描述必填！' }]}
            />
            {/* <ProFormUploadButton
              colProps={{ span: 24 }}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
              name="files"
              label="事件图片上传"
              rules={[{ required: true, message: '事件图片必填！' }]}
              fieldProps={{
                name: 'file',
                onChange(info: any) {
                  const { file } = info;
                  console.log(file, 3333333333);
                  
                  if (file.status === 'done' || file.status === 'removed') {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.code !== 200
                    ) {
                      message.error(file.response.msg);
                    } else {
                      message.success(file.response.msg);
                    }
                  }
                },
                accept: 'image/*',
                headers: {
                  Authorization: `Bearer ${token}`,
                },
                listType: 'picture-card',
                // className: 'upload-list-inline',
                async onPreview(file: any) {
                  if (
                    file.status === 'done' &&
                    file.response &&
                    file.response.data &&
                    file.response.data.ossId
                  ) {
                    const fileType = getFileTypeByName(
                      file.response.data.fileName
                    );
                    if (fileType === 'Image') {
                      const d = await getFileData(file.response.data.ossId);
                      setIsShowFileData({
                        name: file.response.data.fileName,
                        url: d,
                        ossId: file.response.data.ossId,
                      });
                      setIsShowFileView(true);
                    } else {
                      downloadFile(
                        file.response.data.ossId,
                        file.response.data.fileName
                      );
                    }
                  }
                },
              }}
              action={uploadFiles}
            /> */}

            <EProFormUploadButton
              name="files"
              label="附件信息"
              colProps={{ span: 24 }}
              labelCol={{ flex: 0.005 }}
              max={10}
              // readonly={readonly}
            />
          </ProForm>
        </div>
        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => handleSave(1)}
            loading={btnLoading}
          >
            保存
          </Button>
          <Button
            type="primary"
            onClick={() => handleSave(2)}
            loading={btnLoading}
          >
            提交
          </Button>
        </div>

        {/* 文件预览 */}
        <FileView
          open={isShowFileView}
          file={isShowFileData}
          closeDetail={() => {
            setIsShowFileView(false);
          }}
        />
      </div>
    </Drawer>
  );
};

export default Edit;
