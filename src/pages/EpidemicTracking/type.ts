export type TTableData = {
  evenId: string;
  eventCode: string;
  eventTime: string;
  area: string;
  eventAddr: string;
  eventContent: string;
  deptName: string;
  createTime: string;
  confirmTime: string;
  summaryTime: string;
};
export type TCityList = {
  cityId: number;
  cityName: string;
};

export type TAreaList = {
  areaId: number;
  areaName: string;
};

export type TFollowUpList = {
  eventId: number;
  appender: string;
  appenderId: number;
  appendContent: string;
  appendTime: string;
  appendFile: number;
  createId: number;
  creator: string;
  createTime: string;
};
