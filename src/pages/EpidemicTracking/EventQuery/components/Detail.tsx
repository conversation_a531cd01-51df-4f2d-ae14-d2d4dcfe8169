import { useEffect, useState } from 'react';
import { Drawer, message } from 'antd';
import { eventDetailApi } from '@/api/event';
import { codeDefinition } from '@/constants';
import EventFile from '../../components/EventFile';
import EventFinalReportInfo from '../../components/EventFinalReportInfo';
import EventFollowUpInfo from '../../components/EventFollowUpInfo';
import EventReportingInfo from '../../components/EventReportingInfo';
import EventVerificationInfo from '../../components/EventVerificationInfo';
import BlockContainer from '@/components/BlockContainer';
import { TFollowUpList } from '../../type';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  const [followUpList, setFollowUpList] = useState<TFollowUpList[]>([]);

  const close = () => {
    setOpen(false);
    setDetailInfo(undefined);
  };

  const getDetail = async (eventId: string) => {
    try {
      const { code, data, msg } = await eventDetailApi({ eventId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data.event);
        setFollowUpList(data.appends);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);
  return (
    <Drawer
      width="90%"
      title="公示详情"
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col p-4 overflow-y-auto bg-white gap-4">
        <BlockContainer title="事件上报信息">
          <EventReportingInfo dataSource={detailInfo} />
        </BlockContainer>

        <BlockContainer title="事件核实信息">
          <EventVerificationInfo dataSource={detailInfo} />
        </BlockContainer>

        <BlockContainer title="事件初报文书">
          <div className="w-full">
            {detailInfo?.confirmFile ? (
              <EventFile fileGroupId={detailInfo?.confirmFile} />
            ) : null}
          </div>
        </BlockContainer>

        <BlockContainer title="事件续报信息">
          <>
            {followUpList.length
              ? followUpList.map((_item, _index) => (
                  <EventFollowUpInfo dataSource={_item} index={_index} />
                ))
              : null}
          </>
        </BlockContainer>

        <BlockContainer title="事件终报信息">
          <EventFinalReportInfo dataSource={detailInfo} />
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
