/*
 * @Date: 2024-07-25 13:56:59
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-07-26 10:43:58
 * @FilePath: \xr-qc-jk-web\src\pages\EpidemicTracking\components\EventFinalReportInfo.tsx
 * @Description: 事件终报信息
 */
import { useRef,useEffect ,useState} from 'react';
import {
  ProForm,
  ProFormInstance,
} from '@ant-design/pro-components';
import { Descriptions, message } from 'antd';
import RichTextEditor from '@/components/RichTextEditor';
import EventFile from './EventFile';
import EProFormUploadButton from '@/components/EProFormUploadButton'

type TEventFinalReportInfoProps = {
  dataSource?: Record<string, any>;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const EventFinalReportInfo: React.FC<TEventFinalReportInfoProps> = ({
  dataSource,
}) => {



interface RichTextEditorInstance {
  handleGetData: () => string;
}

  const formRef = useRef<ProFormInstance>(null);

  // 更新 formRef2 的类型
  const formRef2 = useRef<RichTextEditorInstance>(null); 



  const [formReady, setFormReady] = useState(false);
  // 监听 formRef 是否准备就绪
  useEffect(() => {
    if (formRef.current) {
      setFormReady(true);
    }
  }, []);



  useEffect(() => {
    console.log(dataSource, 'dataSource', dataSource?.summaryFileArr);
    if (dataSource?.summaryFileArr) {
          try {
          
          const _files = JSON.parse(dataSource?.summaryFileArr);
          console.log(_files, 'files');
          
          // 转换为更完整的文件列表格式
          
          const fileList = _files.map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));
            console.log(fileList, formRef.current, 'fileList111111111');
          if (formRef.current) {
            formRef.current.setFieldValue('files', fileList);
          }
          // formRef.current?.setFieldValue('files', fileList);
        } catch (parseError) {
          console.error('解析 eventFile 失败:', parseError);
        }
      }
  }, [dataSource, formRef]);


  return (
    <>
      <Descriptions column={3}>
        <Descriptions.Item label="时间">
          {dataSource?.summaryTime}
        </Descriptions.Item>
        <Descriptions.Item label="终报人">
          {dataSource?.summary}
        </Descriptions.Item>
        <Descriptions.Item label="">
          <div></div>
        </Descriptions.Item>
        <Descriptions.Item label="终报信息" span={3}>
          <div className=" w-full">
            <RichTextEditor value={dataSource?.summaryContent} readonly   onRef={formRef2}/>
          </div>
        </Descriptions.Item>
      </Descriptions>
      <Descriptions column={1} title="事件终报文书">
   

      </Descriptions>


      <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true} submitter={false}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
          >
            <EProFormUploadButton
                name="files"
                label="附件信息"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                readonly={true}
              />
          </ProForm>


    </>
  );
};

export default EventFinalReportInfo;
