/*
 * @Date: 2024-07-24 18:10:15
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-07-26 10:43:33
 * @FilePath: \xr-qc-jk-web\src\pages\EpidemicTracking\components\EventVerificationInfo.tsx
 * @Description: 事件核实信息
 */
import { Descriptions } from 'antd';
import RichTextEditor from '@/components/RichTextEditor';
import EventFile from './EventFile';
import {  useRef} from 'react';

type TEventVerificationInfoProps = {
  dataSource?: Record<string, any>;
};

const EventVerificationInfo: React.FC<TEventVerificationInfoProps> = ({
  dataSource,
}) => {


interface RichTextEditorInstance {
  handleGetData: () => string;
}


  // 更新 formRef2 的类型
  const formRef2 = useRef<RichTextEditorInstance>(null); 


  return (
    <Descriptions column={3}>
      <Descriptions.Item label="事件类型">
        {dataSource?.eventType}
      </Descriptions.Item>
      <Descriptions.Item label="核实人">
        {dataSource?.confirm}
      </Descriptions.Item>
      <Descriptions.Item label="">
        <div></div>
      </Descriptions.Item>
      <Descriptions.Item label="核实信息" span={3}>
        <div className="w-full">
          <RichTextEditor value={dataSource?.confirmContent} readonly  onRef={formRef2}/>
        </div>
      </Descriptions.Item>
    </Descriptions>
  );
};

export default EventVerificationInfo;
