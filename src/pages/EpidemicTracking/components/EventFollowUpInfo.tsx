/*
 * @Date: 2024-07-25 13:28:42
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-09 14:36:26
 * @FilePath: \xr-qc-jk-web\src\pages\EpidemicTracking\components\EventFollowUpInfo.tsx
 * @Description: 事件续报信息
 */

import {
  ProForm,
  ProFormInstance,
} from '@ant-design/pro-components';

import { Descriptions, message } from 'antd';
import RichTextEditor from '@/components/RichTextEditor';
import { TFollowUpList } from '../type';
import EventFile from './EventFile';
import {  useRef,useEffect,  useState } from 'react';
import EProFormUploadButton from '@/components/EProFormUploadButton'

type TEventFollowUpInfoProps = {
  dataSource: any;
  index: number;
};
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const EventFollowUpInfo: React.FC<TEventFollowUpInfoProps> = ({
  dataSource,
  index,
}) => {


interface RichTextEditorInstance {
  handleGetData: () => string;
}


interface RichTextEditorInstance {
  handleGetData: () => string;
}

  const formRef = useRef<ProFormInstance>(null);
  // 更新 formRef2 的类型
  const formRef2 = useRef<RichTextEditorInstance>(null); 


  useEffect(() => {
    console.log(dataSource, 'dataSource', dataSource?.appendFileArr);
    if (dataSource?.appendFileArr) {
          try {
          
          const _files = JSON.parse(dataSource?.appendFileArr);
          console.log(_files, 'files');
          
          // 转换为更完整的文件列表格式
          
          const fileList = _files.map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));
            console.log(fileList, formRef.current, 'fileList111111111');
          if (formRef.current) {
            formRef.current.setFieldValue('files', fileList);
          }
          // formRef.current?.setFieldValue('files', fileList);
        } catch (parseError) {
          console.error('解析 eventFile 失败:', parseError);
        }
      }
  }, [dataSource, formRef]);

  return (




    <div className=" mt-4 shadow">
      <Descriptions column={3} title={'事件续报' + (index + 1)}>
        <Descriptions.Item label="时间">
          {dataSource.createTime}
        </Descriptions.Item>
        <Descriptions.Item label="续报人">
          {dataSource.appender}
        </Descriptions.Item>
        <Descriptions.Item label="">
          <div></div>
        </Descriptions.Item>
        <Descriptions.Item label="续报信息" span={3}>
          <div className=" w-full">
            <RichTextEditor value={dataSource.appendContent} readonly   onRef={formRef2}/>
          </div>
        </Descriptions.Item>
      </Descriptions>

      <Descriptions column={1} title={`事件续报${index + 1}文书`}>
      </Descriptions>

      <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true} submitter={false}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
          >
            <EProFormUploadButton
                name="files"
                label="附件信息"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                readonly={true}
              />
          </ProForm>

    </div>
  );
};

export default EventFollowUpInfo;
