import { Descriptions } from 'antd';
import { useEffect, useRef, useState } from 'react';

import EventFile from './EventFile';
import EProFormUploadButton from '@/components/EProFormUploadButton'
import {
  ProForm,

  ProFormInstance,

} from '@ant-design/pro-components';
type TEventReportingInfoProps = {
  dataSource?: Record<string, any>;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const EventReportingInfo: React.FC<TEventReportingInfoProps> = ({
  dataSource,
}) => {
  const formRef = useRef<ProFormInstance>(null);
  const [formReady, setFormReady] = useState(false);
  // 监听 formRef 是否准备就绪
  useEffect(() => {
    if (formRef.current) {
      setFormReady(true);
    }
  }, []);

  useEffect(() => {
    console.log(dataSource, 'dataSource');
    if (dataSource?.eventFile) {
          try {
          
          const _files = JSON.parse(dataSource?.eventFile);
          // 转换为更完整的文件列表格式
          
          const fileList = _files.map((_item: any) => ({
                uid: _item.ossId,
                name: _item.fileName,
                status: 'done',
                type: 'application/msword',
                url: _item.url,
                response: {
                  code: 200,
                  data: {
                    fileName: _item.fileName,
                    ossId: _item.ossId,
                    url: _item.url,
                  },
                },

          }));
            console.log(fileList, formRef.current, 'fileList111111111');
          if (formRef.current) {
            formRef.current.setFieldValue('files', fileList);
          }
          // formRef.current?.setFieldValue('files', fileList);
        } catch (parseError) {
          console.error('解析 eventFile 失败:', parseError);
        }
      }
  }, [dataSource, formReady]);

  return (
    <div>

    <Descriptions column={3}>
      <Descriptions.Item label="发生时间">
        {dataSource?.eventTime}
      </Descriptions.Item>
      <Descriptions.Item label="发生地区">
        {(dataSource?.cityName || '') +
          (dataSource?.areaName ? '/' + dataSource?.areaName : '')}
      </Descriptions.Item>
      <Descriptions.Item label="详细地址">
        {dataSource?.eventAddr}
      </Descriptions.Item>
      <Descriptions.Item label="上报单位">
        {dataSource?.deptName}
      </Descriptions.Item>
      <Descriptions.Item label="">
        <div></div>
      </Descriptions.Item>
      <Descriptions.Item label="上报人">
        {dataSource?.committer}
      </Descriptions.Item>
      <Descriptions.Item label="联系方式">
        {dataSource?.mobile}
      </Descriptions.Item>
      <Descriptions.Item label="联系地址">
        {dataSource?.contactAddr}
      </Descriptions.Item>
      <Descriptions.Item label="事件描述">
        {dataSource?.eventContent}
      </Descriptions.Item>

   
    </Descriptions>
      <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true} submitter={false}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
          >
            <EProFormUploadButton
                name="files"
                label="附件信息"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                readonly={true}
              />
          </ProForm>

    </div>

    
  );
};

export default EventReportingInfo;
