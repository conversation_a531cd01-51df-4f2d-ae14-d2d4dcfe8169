/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { fileGroupApi, TFileGroupRes } from '@/api/common';
import { downloadFile } from '@/api/file';
import { codeDefinition } from '@/constants';
import { DownloadOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';



type TEventFileProps = {
  fileGroupId?: string;
  request?: boolean;
  fileList?: TFileList[];
};

export type TFileList = {
  name: string;
  url: string;
  ossId: string;
};

const EventFile: React.FC<TEventFileProps> = ({
  fileGroupId,
  request = true,
  fileList,
}) => {
  const formRef = useRef<ProFormInstance>(null);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  const [fileUrl, setFileUrl] = useState('');

  const getFileList = (
    originList: {
      url: string;
      name: string;
      // size: string;
      ossId: string;
    }[]
  ) => {
    return originList.map((_item) => ({
      response: {
        code: 200,
        data: {
          fileName: _item.name,
          url: _item.url,
          ossId: _item.ossId,
        },
      },
      fileName: _item.name,
      url: _item.url,
      // size: _item.size,
      status: 'done',
      name: _item.name,
    }));
  };

  const getFileGroup = async (fileId: string) => {
    try {
      const { code, data, msg } = await fileGroupApi({ businessId: fileId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _filesList = getFileList(
          data.map((_item: TFileGroupRes) => ({
            url: _item.fileAddr,
            name: _item.originalName,
            ossId: _item.ossId,
          }))
        );
        formRef.current?.setFieldValue('files', _filesList);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (request && fileGroupId) {
      getFileGroup(fileGroupId);
    }
  }, [fileGroupId, request]);

  useEffect(() => {
    if (!request && fileList?.length) {
      const _filesList = getFileList(fileList);
      formRef.current?.setFieldValue('files', _filesList);
    }
  }, [fileList, request]);
  return (
    <>
      <ProForm readonly submitter={false} formRef={formRef}>
        <ProFormUploadButton
          name="file"
          colProps={{ span: 24 }}
          labelCol={{ flex: 0.005 }}
          fieldProps={{
            iconRender: (file) => {
              return (
                <img
                  src={getIconByName(file.name)}
                  className="!w-[40px] !h-[40px] m-auto mt-2"
                  alt="logo"
                />
              );
            },
            // disabled: true,
            name: 'file',
            listType: 'picture-card',
            showUploadList: {
              showDownloadIcon: true,
              downloadIcon: <DownloadOutlined />,
              showRemoveIcon: false,
            },

            async onPreview(file: any) {
              if (
                file.status === 'done' &&
                file.response &&
                file.response.data &&
                file.response.data.ossId
              ) {
                if (file.response.data.url) {
                  setFileUrl(file.response.data.url);
                  setIsShowFileView(true);
                } else {
                }
              }
            },
            onDownload(file) {
              if (
                file.status === 'done' &&
                file.response &&
                file.response.data &&
                file.response.data.ossId
              ) {
                downloadFile(
                  file.response.data.ossId,
                  file.response.data.fileName
                );
              }
            },
          }}
          // action={uploadFiles}
          wrapperCol={{
            span: 24,
          }}
        ></ProFormUploadButton>
      </ProForm>
      {/* 文件预览 */}
      {fileUrl?.includes('xlsx') ? (
        <EExcelFileView
          open={isShowFileView}
          close={() => setIsShowFileView(false)}
          blobUrl={fileUrl}
        />
      ) : (
        <EFileView
          open={isShowFileView}
          close={() => setIsShowFileView(false)}
          blobUrl={fileUrl}
        />
      )}
    </>
  );
};

export default EventFile;
