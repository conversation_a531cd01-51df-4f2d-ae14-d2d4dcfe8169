/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 整体概况
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getOverallOverviewData } from '@/api/qualityAssessment';
import { getAbnormalData } from '@/api/visual/baboratoryPortrait';
import abnormalDataImg01 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img01.png';
import abnormalDataImg02 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img02.png';
import abnormalDataImg03 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img03.png';
import EmptyDataIcon from '@/assets/no_data.webp';
import { codeDefinition, orgName } from '@/constants';
import { converPxToVW } from '@/utils';
import classnames from 'classnames';
import { divide } from 'lodash';
import Loading from '@/components/ELoading';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScrollNext from '@/components/SeamlessScrollNext';

type TOverallOverviewProps = {};

const OverallOverview: React.FC<TOverallOverviewProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  const [dataSource, setDataSource] = useState<Record<string, any>>();

  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getOverallOverviewData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDataSource(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="整体概况" />
      <div className="w-full flex-1 flex flex-col flex-nowrap justify-center items-center px-12 gap-8">
        <div className="w-full flex flex-row flex-nowrap justify-between items-center gap-12">
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img01.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">考核类型数量</div>
              <div className="text-right">{dataSource?.taskTypeNum}</div>
            </div>
          </div>
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img02.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">模版数量</div>
              <div className="text-right">{dataSource?.templateNum}</div>
            </div>
          </div>
        </div>
        <div className="w-full flex flex-row flex-nowrap justify-between items-center gap-12">
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img01.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">任务数量</div>
              <div className="text-right">{dataSource?.taskNum}</div>
            </div>
          </div>
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img02.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">考核机构次数</div>
              <div className="text-right">{dataSource?.taskOrgNum}</div>
            </div>
          </div>
        </div>
        <div className="w-full flex flex-row flex-nowrap justify-between items-center gap-12">
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img01.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">优秀证书发放数量</div>
              <div className="text-right">{dataSource?.excellentNum}</div>
            </div>
          </div>
          <div className="h-[50px] flex-1 bg-[url('/src/assets/fullScreen/QualityAssessment/img02.png')] bg-no-repeat bg-full flex items-center">
            <div className="ml-20 flex flex-row justify-between">
              <div className="mr-12">及格证书发放数量</div>
              <div className="text-right">{dataSource?.passNum}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverallOverview;
