/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 机构分析
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import * as echarts from 'echarts';
import { getInstitutionalAnalysisData } from '@/api/qualityAssessment';
import { codeDefinition } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

type TInstitutionalAnalysisProps = {};

const InstitutionalAnalysis: React.FC<TInstitutionalAnalysisProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);
  const [options, setOptions] = useState<Record<string, any>>();
  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getInstitutionalAnalysisData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setOptions({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          textStyle: {
            color: '#FFFFFF',
          },
          top: 20,
        },
        grid: {
          left: '20%',
          right: '8%',
          bottom: '8%',
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          splitLine: {
            lineStyle: {
              color: ['#FFFFFF'],
              opacity: 0.25,
            },
          },
        },
        yAxis: {
          type: 'category',
          data: data?.map((_item: Record<string, any>) => _item.orgName),
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: 'white',
            interval: 0,
            fontSize: 10,
            width: 120,
          },
        },
        series: [
          {
            name: '优秀次数',
            type: 'bar',
            data: data?.map((_item: Record<string, any>) => _item.excellentNum),
            barMaxWidth: 28,
            itemStyle: {
              borderRadius: [0, 4, 4, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#023465' },
                { offset: 1, color: '#19A2FF' },
              ]),
            },
          },
          {
            name: '合格次数',
            type: 'bar',
            barMaxWidth: 28,
            data: data?.map((_item: Record<string, any>) => _item.passNum),
            itemStyle: {
              borderRadius: [0, 4, 4, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#004449' },
                { offset: 1, color: '#0EFA8A' },
              ]),
            },
          },
          {
            name: '不合格次数',
            type: 'bar',
            barMaxWidth: 28,
            data: data?.map((_item: Record<string, any>) => _item.notPassNum),
            itemStyle: {
              borderRadius: [0, 4, 4, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#382544' },
                { offset: 1, color: '#FF514D' },
              ]),
            },
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="机构分析" />
      <div className="w-full flex-1">
        <ChartsWrapper option={options as any} />
      </div>
    </div>
  );
};

export default InstitutionalAnalysis;
