/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 机构分析
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import * as echarts from 'echarts';
import { getTaskAnalysisData } from '@/api/qualityAssessment';
import { getAbnormalData } from '@/api/visual/baboratoryPortrait';
import { codeDefinition, orgName } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

type TTaskAnalysisProps = {};

const TaskAnalysis: React.FC<TTaskAnalysisProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  const [option, setOption] = useState<Record<string, any>>();

  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getTaskAnalysisData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setOption({
        tooltip: {},
        legend: {
          data: ['优秀机构数', '及格机构数', '不及格机构数', '通过率'],
          textStyle: {
            color: '#FFFFFF',
          },
          top: 10,
        },
        grid: {
          top: '19%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: data?.map((_item: Record<string, any>) => _item?.taskName),
            axisPointer: {
              type: 'shadow',
            },
            axisLabel: {
              show: true,
              textStyle: { color: '#FFFFFF' },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '数量',
            nameTextStyle: {
              padding: [0, 0, 0, 43],
            },
            axisLabel: {
              textStyle: { color: '#FFFFFF' },
            },
            splitLine: {
              lineStyle: {
                color: ['#FFFFFF'],
                opacity: 0.25,
              },
            },
          },
          {
            type: 'value',
            name: '通过率',
            nameTextStyle: {
              padding: [0, 75, 0, 0],
            },
            axisLabel: {
              textStyle: { color: '#FFFFFF' },
              formatter: '{value}%',
            },
            splitLine: {
              lineStyle: {
                color: ['#FFFFFF'],
                opacity: 0.25,
              },
            },
          },
        ],
        series: [
          {
            name: '优秀机构数',
            type: 'bar',
            data: data?.map(
              (_item: Record<string, any>) => _item?.excellentNum
            ),
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1F9EFF' },
                { offset: 1, color: '#043A77' },
              ]),
            },
          },
          {
            name: '及格机构数',
            type: 'bar',
            data: data?.map((_item: Record<string, any>) => _item?.passNum),
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#29EF91' },
                { offset: 1, color: '#004D54' },
              ]),
            },
          },
          {
            name: '不及格机构数',
            type: 'bar',
            data: data?.map((_item: Record<string, any>) => _item?.notPassNum),
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#FF4C4D' },
                { offset: 1, color: '#391F46' },
              ]),
            },
          },
          {
            name: '通过率',
            type: 'line',
            data: data?.map((_item: Record<string, any>) => _item?.passPercent),
            yAxisIndex: 1,
            barWidth: '20%',
            smooth: true,
            itemStyle: {
              color: '#BB75FF',
            },
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="任务分析" />
      <div ref={domRef} className="flex-1 h-full">
        <ChartsWrapper
          option={option as any}
          height={domRef?.current?.clientHeight}
        />
      </div>
    </div>
  );
};

export default TaskAnalysis;
