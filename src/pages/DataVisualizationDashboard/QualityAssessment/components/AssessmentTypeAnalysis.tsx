/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 机构分析
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import {
  getAssessmentTypeAnalysisData,
  getAssessmentTypeAnalysisDetailData,
} from '@/api/qualityAssessment';
import { getAbnormalData } from '@/api/visual/baboratoryPortrait';
import middleImg from '@/assets/fullScreen/QualityAssessment/row.png';
import { codeDefinition, orgName } from '@/constants';
import { converPxToVW } from '@/utils';
import { color } from 'echarts';
import { set } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

type TAssessmentTypeAnalysisProps = {};

const AssessmentTypeAnalysis: React.FC<TAssessmentTypeAnalysisProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  const [options, setOptions] = useState<Record<string, any>>();
  const [detailOptions, setDetailOptions] = useState<Record<string, any>>();

  const [curSelectedAssType, setCurSelectedAssType] = useState<string>();

  // 当前选择的明细数据
  // const [curSelectedAssDetail, setCurSelectedAssDetail] =
  //   useState<Record<string, any>>();

  const [typeOptions, setTypeOptions] = useState<Record<string, any>>();

  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getAssessmentTypeAnalysisData({
        orgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCurSelectedAssType(data[0]?.assType);

      const _data: any[] = [];
      data?.forEach((_item: any) => {
        _data.push({
          value: _item?.num,
          ..._item,
          label: {
            color: '#FFFFFF',
          },
        });
      });

      setOptions({
        legend: {
          top: '75%',
          textStyle: {
            color: '#FFFFFF',
          },
          padding: -10,
        },
        grid: {
          top: '10%',
          right: '10%',
          bottom: 0,
          left: '10%',
        },
        series: [
          {
            name: 'Nightingale Chart',
            type: 'pie',
            radius: '40%',
            center: ['50%', '40%'],
            roseType: 'area',
            itemStyle: {},
            data: _data,
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   *  获取考核类型明细
   */
  const queryAssDetails = async () => {
    try {
      const { code, data, msg } = await getAssessmentTypeAnalysisDetailData({
        assType: curSelectedAssType,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTypeOptions({
        tooltip: {
          trigger: 'item',
          padding: 0,
          borderWidth: 0,
          backgroundColor: 'rgba(0,0,0,0)',
          formatter: function (params: Record<string, any>) {
            const htmlContent = `
            <div style='padding: 8px;background: rgba(2,16,45,0.5);border: 1px solid #1a81e0;border-radius:8px;color:#FFFFFF;box-shadow: 0px 0px 8px 0px rgba(50,132,255,0.4);'>
              <div style='font-size:12px;color:#28ACFA';font-weight: bold;>${params?.data?.name}</div>
              <div style='font-size:12px'>检查数: ${params?.data?.value}</div>
              <div style='font-size:12px'>占比: ${params?.percent}%</div>
            </div>
            `;
            return htmlContent;
          },
        },
        legend: {
          show: false,
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
              {
                value: data?.excellentNum,
                name: '优秀次数',
                itemStyle: { color: '#1EF599' },
                label: {
                  show: true,
                  position: 'outer',
                  color: '#FFFFFF',
                  fontSize: 14,
                },
              },
              {
                value: data?.passNum,
                name: '合格次数',
                itemStyle: { color: '#11B0F9' },
                label: {
                  show: true,
                  position: 'outer',
                  color: '#FFFFFF',
                  fontSize: 14,
                },
              },
              {
                value: data?.notPassNum,
                name: '不合格次数',
                itemStyle: { color: '#FFC427' },
                label: {
                  show: true,
                  position: 'outer',
                  color: '#FFFFFF',
                  fontSize: 14,
                },
              },
            ],
          },
          {
            name: '外边框',
            type: 'pie',
            clockWise: false, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            center: ['50%', '50%'],
            radius: ['75%', '75%'],
            label: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 9,
                name: '',
                itemStyle: {
                  normal: {
                    borderWidth: 1,
                    borderColor: '#1B5998',
                  },
                },
              },
            ],
          },
          {
            name: '内边框',
            type: 'pie',
            clockWise: false, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            center: ['50%', '50%'],
            radius: ['35%', '35%'],
            label: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 9,
                name: '',
                itemStyle: {
                  normal: {
                    borderWidth: 1,
                    borderColor: '#1B5998',
                  },
                },
              },
            ],
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryAssDetails();
  }, [curSelectedAssType]);

  useEffect(() => {
    queryDataList();
  }, []);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="考核类型分析" />
      <div className="w-full flex-1 flex flex-row flex-nowrap justify-between items-center ">
        <div className="flex-1 h-full">
          <ChartsWrapper
            option={options as any}
            // height={domRef?.current?.clientHeight}
            handleEvent={(param: any) => {
              setCurSelectedAssType(param?.data?.assType);
            }}
          />
        </div>
        <div className="w-[60px] h-full flex items-center">
          <img
            src={middleImg}
            alt=""
            style={{
              width: `${converPxToVW(45)}vw`,
              height: `${converPxToVW(45)}vw`,
            }}
          />
        </div>
        <div className="flex-1 h-full flex flex-col">
          <div className="w-full flex flex-row justify-center items-center mt-4">
            <div
              className="bg-[url('/src/assets/fullScreen/QualityAssessment/title-bg.png')] bg-no-repeat bg-full font-YousheBiaotiHei text-lg flex justify-center items-center"
              style={{
                width: `${converPxToVW(242)}vw`,
                height: `${converPxToVW(48)}vw`,
              }}
            >
              所选考核类型分析
            </div>
          </div>
          <div ref={domRef} className="flex-1 h-full">
            <ChartsWrapper
              option={typeOptions as any}
              // height={domRef?.current?.clientHeight}
            />
          </div>
          <div className="flex flex-row flex-nowrap items-center">
            <div className="flex-1 flex flex-row flex-nowrap gap-1 items-center">
              <span className="w-3 h-3 bg-[#1EF599]"></span>
              <span>优秀次数</span>
            </div>
            <div className="flex-1 flex flex-row flex-nowrap gap-1 items-center">
              <span className="w-3 h-3 bg-[#11B0F9]"></span>
              <span>合格次数</span>
            </div>
            <div className="flex-1 flex flex-row flex-nowrap gap-1 items-center">
              <span className="w-3 h-3 bg-[#FFC427]"></span>
              <span>不合格次数</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentTypeAnalysis;
