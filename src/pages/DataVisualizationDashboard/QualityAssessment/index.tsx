// 质量考核
import React from 'react';
import { converPxToVH, converPxToVW } from '@/utils';
import AssessmentTypeAnalysis from './components/AssessmentTypeAnalysis';
import InstitutionalAnalysis from './components/InstitutionalAnalysis';
import OverallOverview from './components/OverallOverview';
import TaskAnalysis from './components/TaskAnalysis';

type TQualityAssessmentProps = {};
const QualityAssessment: React.FC<TQualityAssessmentProps> = () => {
  return (
    <div
      className="w-full flex flex-row flex-nowrap gap-6 overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      {/* 左侧 */}
      <div
        className="h-full flex flex-col flex-nowrap gap-1"
        style={{ width: `${converPxToVW(800)}vw` }}
      >
        {/* 整体概况 */}
        <div className="w-full flex-1">
          <OverallOverview />
        </div>
        {/* 机构分析 */}
        <div className="w-full flex-1">
          <InstitutionalAnalysis />
        </div>
      </div>
      {/* 右侧 */}
      <div className="h-full flex-1 flex flex-col flex-nowrap gap-1">
        {/* 任务分析 */}
        <div className="w-full flex-1">
          <TaskAnalysis />
        </div>
        {/* 考核类型分析 */}
        <div className="w-full flex-1">
          <AssessmentTypeAnalysis />
        </div>
      </div>
    </div>
  );
};

export default QualityAssessment;
