// 数据驾驶舱
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { dataCockpitDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import classnames from 'classnames';
import bgImg from '/fullScreen/dataCockpit/img.png';

type TDataCockpitProps = {};
const DataCockpit: React.FC<TDataCockpitProps> = () => {
  const renderChildModule = (
    top: string,
    left: string,
    label: string,
    total: string | number
  ) => {
    return (
      <div
        className={classnames(
          "absolute bg-[url('/fullScreen/dataCockpit/tips-big.png')] bg-cover bg-full text-center translate-x-[-0.5vw]",
          top,
          left
        )}
        style={{
          width: `${converPxToVW(144)}vw`,
          height: `${converPxToVH(47)}vh`,
        }}
      >
        <div
          className="text-[#5EEEFF] font-bold"
          style={{ fontSize: `${converPxToVW(16)}vw` }}
        >
          {total}
        </div>
        <div
          className="relative -top-[6px]"
          style={{ fontSize: `${converPxToVW(14)}vw` }}
        >
          {/* 就医记录数量 */}
          {label}
        </div>
      </div>
    );
  };

  const [dataSource, setDataSource] = useState<Record<string, any>>({});
  const getDataSource = () => {
    dataCockpitDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div
      className="relative w-full h-full"
      style={{
        padding: `0 ${converPxToVW(20)}vw`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      <div className="absolute top-0 left-0 w-full h-full">
        <img alt="" src={bgImg} className="w-full h-full overflow-hidden" />
      </div>
      {/* 病例数据统计部分展示数据 */}
      {renderChildModule(
        'top-[0.5vh]',
        'left-[38vw]',
        '患者基本信息',
        dataSource?.patientInfo || 0
      )}
      {renderChildModule(
        'top-[7vh]',
        'left-[30.3vw]',
        '传染病报告卡',
        dataSource?.infReport || 0
      )}
      {renderChildModule(
        'top-[14.8vh]',
        'left-[22.5vw]',
        '诊疗活动信息',
        dataSource?.activityInfo || 0
      )}
      {renderChildModule(
        'top-[22.8vh]',
        'left-[14.8vw]',
        '检查报告',
        dataSource?.exClinical || 0
      )}
      {renderChildModule(
        'top-[30vh]',
        'left-[7.5vw]',
        '检验报告',
        dataSource?.exLab || 0
      )}
      {renderChildModule(
        'top-[54.3vh]',
        'left-[4.5vw]',
        '资质能力数量',
        dataSource?.qualification || 0
      )}
      {renderChildModule(
        'top-[60.3vh]',
        'left-[11.8vw]',
        '质量控制记录数量',
        dataSource?.quality || ''
      )}
      {renderChildModule(
        'top-[66.8vh]',
        'left-[18.5vw]',
        '检测人员数量',
        dataSource?.personInfo || 0
      )}
      {renderChildModule(
        'top-[73.5vh]',
        'left-[25vw]',
        '人员培训记录数量',
        dataSource?.personTrain || 0
      )}
      {renderChildModule(
        'top-[43.8vh]',
        'left-[25vw]',
        '实验室数量',
        dataSource?.dept || 0
      )}
      {renderChildModule(
        'top-[37.8vh]',
        'left-[31vw]',
        '异常数据总量',
        dataSource?.abnormalData || 0
      )}
      {renderChildModule(
        'top-[48.8vh]',
        'left-[34vw]',
        '监测任务量',
        dataSource?.task || 0
      )}
      {renderChildModule(
        'top-[19.8vh]',
        'left-[50.5vw]',
        '监测哨点数量',
        dataSource?.sentinelNum || 0
      )}
      {renderChildModule(
        'top-[25vh]',
        'left-[59.8vw]',
        '病原预警总量',
        dataSource?.warnInfo || 0
      )}
      {renderChildModule(
        'top-[13.8vh]',
        'left-[56.5vw]',
        '病原监测数据量',
        dataSource?.sampleDetectionBasicInfo || 0
      )}

      {renderChildModule(
        'top-[0.6vh]',
        'left-[66vw]',
        '设备数量',
        dataSource?.device || 0
      )}
      {renderChildModule(
        'top-[6vh]',
        'left-[73vw]',
        '设备检定校准记录',
        dataSource?.deviceCalibration || 0
      )}
      {renderChildModule(
        'top-[12.5vh]',
        'left-[80.2vw]',
        '设备核查记录数量',
        dataSource?.deviceCheck || 0
      )}
      {renderChildModule(
        'top-[19vh]',
        'left-[87.2vw]',
        '设备运行日志数量',
        dataSource?.deviceRunLog || 0
      )}
      {/* 右下 */}
      {renderChildModule(
        'top-[62vh]',
        'left-[55vw]',
        '样本采集记录数量',
        dataSource?.sampleCollection || 0
      )}
      {renderChildModule(
        'top-[53vh]',
        'left-[64.5vw]',
        '样本接收记录数量',
        dataSource?.sampleAccept || 0
      )}
      {renderChildModule(
        'top-[45vh]',
        'left-[73vw]',
        '领样记录数量',
        dataSource?.getSample || 0
      )}
      {renderChildModule(
        'top-[37vh]',
        'left-[80.5vw]',
        '样本前期处理记录数量',
        dataSource?.samplePreProcess || 0
      )}
      {/*  */}
      {renderChildModule(
        'top-[69vh]',
        'left-[61.5vw]',
        '检验过程记录数量',
        dataSource?.detectionProcess || 0
      )}
      {renderChildModule(
        'top-[60vh]',
        'left-[71vw]',
        '检测数据操作日志数量',
        dataSource?.detectionOperationLog || 0
      )}
      {renderChildModule(
        'top-[53vh]',
        'left-[79.8vw]',
        '报告编制记录数量',
        dataSource?.reportPreparation || 0
      )}
      {renderChildModule(
        'top-[46vh]',
        'left-[87.5vw]',
        '样本处置记录数量',
        dataSource?.sampleDispose || 0
      )}
    </div>
  );
};

export default DataCockpit;
