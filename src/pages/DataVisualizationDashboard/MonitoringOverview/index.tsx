// 监测概况
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { mapContainerAnalysisDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import AgeAnalysis from './components/AgeAnalysis';
import ImplementationStatus from './components/ImplementationStatus';
import MapContainer from './components/MapContainer';
import MissionProfile from './components/MissionProfile';
import PopulationAnalysis from './components/PopulationAnalysis';
import TaskProgress from './components/TaskProgress';
import './index.less';
import { IMapDataSource } from './type';

type TMonitoringOverviewProps = {};
const MonitoringOverview: React.FC<TMonitoringOverviewProps> = () => {
  const [mapDataSource, setMapDataSource] = useState<IMapDataSource[]>([]);

  const getMapDataSource = () => {
    mapContainerAnalysisDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setMapDataSource(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getMapDataSource();
  }, []);
  return (
    <div
      className="w-full flex  overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      {/* 左侧 */}
      <div className=" h-full w-[30%] flex-shrink-0 ">
        {/* 任务概况 */}
        <div className=" h-1/2 w-full pb-3 ">
          <MissionProfile />
        </div>
        {/* 任务进度 */}
        <div className=" h-1/2 w-full pt-3 ">
          <TaskProgress />
        </div>
      </div>
      {/* 中间 */}
      <div className=" w-[40%] px-4">
        {/* 地图 */}
        <div className=" h-3/5 w-full">
          <MapContainer id="monitoringOverview" dataSource={mapDataSource} />
        </div>
        {/* 监测哨点任务执行情况 */}
        <div className=" h-2/5 w-full">
          <ImplementationStatus />
        </div>
      </div>
      {/* 右侧 */}
      <div className=" h-full w-[%] flex-shrink-0 ">
        {/* 年龄特征分析 */}
        <div className=" h-1/2 w-full pb-3 ">
          <AgeAnalysis />
        </div>
        {/* 人群特征分析 */}
        <div className=" h-1/2 w-full pt-3 ">
          <PopulationAnalysis />
        </div>
      </div>
    </div>
  );
};

export default MonitoringOverview;
