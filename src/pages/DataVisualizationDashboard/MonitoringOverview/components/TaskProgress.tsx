import { useEffect, useState } from 'react';
import { message } from 'antd';
import { taskProgressDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';

interface IDataSource {
  id: string;
  planName: string;
  sampleCount: number;
  detectionOverCount: number;
  strainsCount: number;
}
const TaskProgress: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: ['#FFB11B', '#12D2DE', '#8B5EFF', '#2E95FF'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: 'white',
      },
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        color: 'white',
        width: 120,
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      }
    },
    series: [
      {
        name: '监测样本数',
        type: 'bar',
        data: [],
        barMaxWidth: 10,
        itemStyle: {
          borderRadius: [0, 10, 10, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,177,27,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#FFB11B', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '检测完成样本数',
        type: 'bar',
        data: [],
        barMaxWidth: 10,
        itemStyle: {
          borderRadius: [0, 10, 10, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,210,222,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '菌（毒）株数量',
        type: 'bar',
        data: [],
        barMaxWidth: 10,
        itemStyle: {
          borderRadius: [0, 10, 10, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(139,94,255,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#8B5EFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  });

  const getDataSource = () => {
    taskProgressDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data.records || [];
        const _newOptions: any = cloneDeep(option);
        _newOptions.yAxis.data = _dataSource.length
          ? _dataSource.map((item) => item.planName)
          : [];
        _newOptions.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.sampleCount)
          : [];
        _newOptions.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.detectionOverCount)
          : [];
        _newOptions.series[2].data = _dataSource.length
          ? _dataSource.map((item) => item.strainsCount)
          : [];

        setOption(_newOptions);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="任务进度" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="taskProgressChart" />
      </div>
    </div>
  );
};

export default TaskProgress;
