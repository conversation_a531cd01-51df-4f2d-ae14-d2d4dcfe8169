import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { message } from 'antd';
import { peopleAnalysisDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';

interface IDataSource {
  sampleCount: number;
  detectionOverCount: number;
  positiveSampleCount: number;
  strainsCount: number;
  job: string;
}

const PopulationAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IDataSource[]>([]);
  const getDataSource = (params: Record<string, any>) => {
    setLoading(true);
    peopleAnalysisDataApi(params)
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data.records);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource({});
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr_2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.job}</span>
            <span>{_item.sampleCount}</span>
            <span>{_item.detectionOverCount}</span>
            <span>{_item.positiveSampleCount}</span>
            <span>{_item.strainsCount}</span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);

  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle title="人群特征分析" />
      <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr_2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
        <span className="text-center">序号</span>
        <span>人群</span>
        <span>采集样本数</span>
        <span>检测完成数</span>
        <span>阳性样本数</span>
        <span>菌（毒）株数量</span>
      </div>
      <div className=" flex-1">
        {loading ? (
          <div className="w-full h-full">
            {/* <Loading>Loading...</Loading> */}
          </div>
        ) : dataSource.length ? (
          <div
            className="w-full bg-[rgba(8,24,48,0.52)]"
            style={{ height: slideParams.parentHeight }}
          >
            <SeamlessScroll
              swiperSlideClassname="!w-full !h-[40px]"
              direction="vertical"
              spaceBetween={0}
              slidesPerView={slideParams.slidesPerView}
              children={createScrollDom()}
            />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default PopulationAnalysis;
