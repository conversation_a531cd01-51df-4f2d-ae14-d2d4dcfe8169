import { useEffect, useState } from 'react';
import { message } from 'antd';
import { implementationStatusDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';

interface IDataSource {
  monitorName: string;
  sampleCount: number;
  detectionOverCount: number;
  strainsCount: number;
}

const ImplementationStatus: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: ['#FFB11B', '#12D2DE', '#8B5EFF', '#FF597C'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: 'white',
          show: true,
          rotate: -330,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: 'white',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    series: [
      {
        name: '采集样本数量',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,118,255,0.3)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#15AFFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '检测完成数量',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,210,222,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '检出菌（毒）株数量',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(214,151,15,0.3)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#FFC74E', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  });

  const getDataSource = () => {
    implementationStatusDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data || [];
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis[0].data = _dataSource.length
          ? _dataSource.map((item) => item.monitorName)
          : [];
        _newOption.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.sampleCount)
          : [];
        _newOption.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.detectionOverCount)
          : [];
        _newOption.series[2].data = _dataSource.length
          ? _dataSource.map((item) => item.strainsCount)
          : [];
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="监测哨点任务执行情况" size="middle" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="taskProgressChart" />
      </div>
    </div>
  );
};

export default ImplementationStatus;
