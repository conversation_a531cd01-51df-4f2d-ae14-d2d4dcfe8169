import { useEffect, useState } from 'react';
import { message } from 'antd';
import { ageAnalysisDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';

interface IDataSource {
  ageLevel: number;
  ageRange: null;
  sampleCount: number;
  positiveSampleCount: number;
  percentage: string;
}
const AgeAnalysis: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: 'white',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: 'white',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
      {
        type: 'value',
        axisLabel: {
          formatter: '{value} %',
        },
      },
    ],
    series: [
      {
        name: '样本采集数',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,118,255,0.3)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#15AFFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '阳性样本数',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(2,187,199,0.3)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '阳性感染率',
        type: 'line',
        data: [],
        yAxisIndex: 1,
        itemStyle: {
          color: '#FFC74E',
        },
      },
    ],
  });

  const getDataSource = () => {
    ageAnalysisDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data || [];
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis[0].data = _dataSource.length
          ? _dataSource.map((item) => item.ageRange)
          : [];
        _newOption.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.sampleCount)
          : [];
        _newOption.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.positiveSampleCount)
          : [];
        _newOption.series[2].data = _dataSource.length
          ? _dataSource.map((item) => item.percentage)
          : [];
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="年龄特征分析" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="ageAnalysisChart" />
      </div>
    </div>
  );
};

export default AgeAnalysis;
