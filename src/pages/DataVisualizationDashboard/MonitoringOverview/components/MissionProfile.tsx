import { useEffect, useState } from 'react';
import { message } from 'antd';
import { taskOverviewDataApi } from '@/api/visual/monitoringOverview';
import icon_1 from '@/assets/fullScreen/MonitoringOverview/rwgk-img1.png';
import icon_2 from '@/assets/fullScreen/MonitoringOverview/rwgk-img2.png';
import icon_3 from '@/assets/fullScreen/MonitoringOverview/rwgk-img3.png';
import icon_4 from '@/assets/fullScreen/MonitoringOverview/rwgk-img4.png';
import icon_5 from '@/assets/fullScreen/MonitoringOverview/rwgk-img5.png';
import icon_6 from '@/assets/fullScreen/MonitoringOverview/rwgk-img6.png';
import icon_7 from '@/assets/fullScreen/MonitoringOverview/rwgk-img7.png';
import icon_8 from '@/assets/fullScreen/MonitoringOverview/rwgk-img8.png';
import { codeDefinition } from '@/constants';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

interface IDataSource {
  total: number;
  overPlan: number;
  inPlan: number;
  sentinelNum: number;
  totalProject: number;
  totalEtiology: number;
  sampleTotal: number;
  increment: number;
}

const MissionProfile: React.FC = () => {
  const [options, setOptions] = useState([
    {
      key: 'sentinelNum',
      title: '监测哨点总数',
      value: 0,
      icon: icon_1,
    },
    {
      key: 'total',
      title: '监测计划总数',
      value: 0,
      icon: icon_2,
    },
    {
      key: 'overPlan',
      title: '已结束计划数',
      value: 0,
      icon: icon_3,
    },
    {
      key: 'inPlan',
      title: '执行中计划数',
      value: 0,
      icon: icon_4,
    },
    {
      key: 'totalProject',
      title: '监测项目数',
      value: 0,
      icon: icon_5,
    },
    {
      key: 'totalEtiology',
      title: '监测病原数',
      value: 0,
      icon: icon_6,
    },
    {
      key: 'sampleTotal',
      title: '监测样本总数',
      value: 0,
      icon: icon_7,
    },
    {
      key: 'increment',
      title: '增量监测样本数',
      value: 0,
      icon: icon_8,
    },
  ]);

  const getDataSource = () => {
    taskOverviewDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource = res.data || {};
        setOptions(
          options.map((item) => ({
            ...item,
            value: _dataSource[item.key as keyof IDataSource] || 0,
          }))
        );
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="任务概况" />
      <div className=" flex-1 flex flex-wrap pt-3">
        {options.map((_item, _index) => (
          <div
            key={_index}
            className=" w-1/2 h-1/4 flex items-center px-6 gap-2"
          >
            <img alt="" src={_item.icon} className=" w-16 h-16" />
            <div className=" flex-1 h-full flex flex-col justify-center gap-1">
              <span className=" text-[#25F2FF] text-2xl">{_item.value}</span>
              <div className=" text-[#F7FCFF] w-full truncate text-base">
                {_item.title}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MissionProfile;
