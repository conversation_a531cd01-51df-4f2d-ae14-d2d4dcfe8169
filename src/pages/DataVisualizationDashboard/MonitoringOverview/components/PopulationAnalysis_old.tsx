import { useState } from 'react';
import man from '@/assets/fullScreen/MonitoringOverview/man.png';
import bg from '@/assets/fullScreen/MonitoringOverview/rqtzfx-img.png';
import woman from '@/assets/fullScreen/MonitoringOverview/woman.png';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

const PopulationAnalysis: React.FC = () => {
  const [options, setOptions] = useState([
    {
      title: '男性',
      key: 'man',
      value: [0, 0, 0, 0],
      contentImgPath: '/src/assets/fullScreen/MonitoringOverview/man.png',
    },
    {
      title: '女性',
      key: 'woman',
      value: [0, 0, 0, 0],
      contentImgPath: '/src/assets/fullScreen/MonitoringOverview/woman.png',
    },
  ]);
  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="人群特征分析" />
      <div className=" flex-1 flex pt-3">
        {options.map((_item) => (
          <div key={_item.key} className=" w-1/2 h-full text-center">
            <div className=" h-1/3 relative inline-block">
              <img alt="" src={bg} className=" h-full " />
              <div className=" absolute top-0 left-0 px-4 flex flex-col w-full h-full text-sm text-left justify-evenly">
                <div className=" text-[#24D3FF] italic font-semibold">{_item.title}</div>
                <div className=" flex-1 flex flex-col justify-evenly text-white">
                  <span>采集样本数：{_item.value[0]}</span>
                  <span>检测完成数：{_item.value[1]}</span>
                  <span>检出阳性数：{_item.value[2]}</span>
                  <span>阳性感染率：{_item.value[3]}</span>
                </div>
              </div>
            </div>
            <div
              className={classNames(
                _item.title === '男性'
                  ? "bg-[url('/src/assets/fullScreen/MonitoringOverview/man.png')]"
                  : "bg-[url('/src/assets/fullScreen/MonitoringOverview/woman.png')]",
                ' h-2/3  bg-center bg-no-repeat'
              )}
              style={{
                backgroundSize: 'auto 100%',
              }}
            ></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopulationAnalysis;
