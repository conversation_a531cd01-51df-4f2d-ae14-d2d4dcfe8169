/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { message } from 'antd';
import { pathogenSpectrumDataApi } from '@/api/visual/businessMetrics';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import { useBusinessMetricsStore } from '../store';

interface IDataSource {
  dimension: string;
  etiologyName: string;
  positiveSamplesCount: string;
  strainsCount: string;
}

const PathogenSpectrum: React.FC = () => {
  const { curMonitor } = useBusinessMetricsStore();
  const [dataSource, setDataSource] = useState<IDataSource[]>([]);

  const [option, setOption] = useState<ECOption>({
    color: ['#DA1B1D', '#FF843A', '#FBEA28', '#3A89FF', '#1DF598'],

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      left: 'right',
      textStyle: {
        color: 'white',
        fontSize: 10,
      },
    },
    calculable: true,
    xAxis: [
      {
        type: 'category',
        data: [],
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
        axisLabel: {
          color: 'white',
          show: true,
          rotate: -330,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
        axisLabel: {
          color: 'white',
        },
      },
    ],
    series: [
      { type: 'bar', data: [], barMaxWidth: 40 },
      {
        type: 'pie',
        center: ['75%', '30%'],
        radius: ['20%', '30%'],
        z: 100,
        data: [],
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
            formatter: (params) => {
              return `${params.name}\n菌（毒）株数量: ${params.value}`;
            },
          },
        },
      },
    ],
  });

  useEffect(() => {
    const _xData = dataSource.length
      ? [
          ...new Set(
            dataSource.map((item) => item.etiologyName).filter((i) => i)
          ),
        ]
      : [];
    const _barData = _xData.map((item) =>
      dataSource
        .filter((data) => data.etiologyName === item)
        .reduce((pre, cur) => Number(cur.strainsCount) + pre, 0)
    );
    const _area = dataSource.length
      ? [...new Set(dataSource.map((item) => item.dimension))]
      : [];
    const _pieData = _area.map((item) => ({
      name: item,
      value: dataSource
        .filter((data) => data.dimension === item)
        .reduce((pre, cur) => Number(cur.strainsCount) + pre, 0),
    }));
    const _newOption: any = cloneDeep(option);
    _newOption.xAxis[0].data = _xData;
    _newOption.yAxis[0].max = _barData.length
      ? _barData.reduce((pre, cur) => (cur > pre ? cur : pre), 0) * 2
      : 0;
    _newOption.series[0].data = _barData;
    _newOption.series[1].data = _pieData;
    setOption(_newOption);
  }, [dataSource]);

  const getDataSource = (projectId: string) => {
    pathogenSpectrumDataApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(res.data?.areaStrains || []);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource(curMonitor);
  }, [curMonitor]);
  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="病原谱" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="pathogenSpectrumChart" />
      </div>
    </div>
  );
};

export default PathogenSpectrum;
