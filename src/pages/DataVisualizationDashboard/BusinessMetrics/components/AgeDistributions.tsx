import { useEffect, useState } from 'react';
import { message } from 'antd';
import { ageDistributionApi } from '@/api/visual/businessMetrics';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import { useBusinessMetricsStore } from '../store';

const AgeDistribution: React.FC = () => {
  const { curMonitor } = useBusinessMetricsStore();
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
      right: 'right',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        color: 'white',
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
      axisLabel: {
        color: 'white',
      },
    },
    series: [
      {
        name: '样本数量',
        type: 'pictorialBar', // 设置为象形柱状图
        data: [],
        symbol: 'triangle', // 使用内置的三角形符号，或者自定义 SVG 路径
        // symbolSize: [],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(43,218,255,0)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#2BDAFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  });

  const getDataSource = (projectId: string) => {
    ageDistributionApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: { name: string; value: number }[] = res.data || [];
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis.data = _dataSource.length
          ? _dataSource.map((item) => item.name)
          : [];
        _newOption.series[0].data = _dataSource;
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource(curMonitor);
  }, [curMonitor]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="样本采集年龄分布" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="seasonalDistributionChart" />
      </div>
    </div>
  );
};

export default AgeDistribution;
