import { useEffect, useState } from 'react';
import { message } from 'antd';
import { populationDistributionApi } from '@/api/visual/businessMetrics';
import icon_man from '@/assets/fullScreen/BusinessMetrics/icon-man.png';
import icon_woman from '@/assets/fullScreen/BusinessMetrics/icon-woman.png';
import bg from '@/assets/fullScreen/BusinessMetrics/img1.png';
import { codeDefinition } from '@/constants';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { useBusinessMetricsStore } from '../store';

interface IDataSource {
  woman: number;
  man: number;
}
const PopulationDistribution: React.FC = () => {
  const { curMonitor } = useBusinessMetricsStore();
  const [options, setOptions] = useState([
    {
      title: '男性',
      value: 0,
      icon: icon_man,
      key: 'man',
    },
    {
      title: '女性',
      value: 0,
      icon: icon_woman,
      key: 'woman',
    },
  ]);

  const getDataSource = (projectId: string) => {
    populationDistributionApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const data: IDataSource = res.data;
        setOptions(
          options.map((item) => ({
            ...item,
            value: data[item.key as keyof IDataSource] || 0,
          }))
        );
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource(curMonitor);
  }, [curMonitor]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="样本采集性别分布" />
      <div className=" flex-1 w-full px-6">
        {options.map((_item, _index) => (
          <div key={_index} className=" h-1/2 w-full flex items-center">
            <div className=" w-full h-auto relative">
              <img alt="" src={bg} className=" w-full" />
              <div className=" w-full h-full absolute top-0 left-0 py-3 px-10 flex justify-between items-center">
                <div className=" flex gap-7 text-white font-medium text-xl items-center">
                  <img alt="" src={_item.icon} className=" h-full w-auto" />
                  <span>{_item.title}</span>
                </div>
                <div className=" text-[#32D6FF] text-3xl align-middle">
                  {_item.value}
                  <span className=" text-base">人</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopulationDistribution;
