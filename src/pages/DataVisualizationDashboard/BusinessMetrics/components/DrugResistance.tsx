import { useEffect, useState } from 'react';
import { message } from 'antd';
import { drugResistanceApi } from '@/api/visual/businessMetrics';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import { useBusinessMetricsStore } from '../store';

interface IDataSource {
  etiologyId: string;
  etiologyName: string;
  resistanceRate: string;
  intermediacyRate: string;
  sensitizationRate: string;
  nonSensitizationRate: string;
}

const DrugResistance: React.FC = () => {
  const { curMonitor } = useBusinessMetricsStore();
  const [dataSource, setDataSource] = useState<IDataSource[]>([]);

  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      valueFormatter: (val) => val + '%',
      // backgroundColor: 'rgba(0,0,0,0.3)',
      // borderColor: 'none',
      // textStyle: {
      //   color: 'white',
      // },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      nameTextStyle: {
        color: '#fff',
      },
      axisLabel: {
        show: true,
        color: 'white',
      },
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        show: true,
        color: 'white',
        fontSize: 10,
      },
    },
    series: [
      {
        name: '耐药率',
        type: 'bar',
        data: [],
        barMaxWidth: 16,
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,210,222,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '中介率',
        type: 'bar',
        data: [],
        barMaxWidth: 16,
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,118,255,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(18,118,255)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '敏感率',
        type: 'bar',
        data: [],
        barMaxWidth: 16,
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,144,45,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(255,144,45)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '非敏感率',
        type: 'bar',
        data: [1],
        barMaxWidth: 16,
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(9,239,161,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(9,239,161)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  });

  const getDataSource = (projectId: string) => {
    drugResistanceApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource(curMonitor);
  }, [curMonitor]);

  useEffect(() => {
    const _newOption: any = cloneDeep(option);
    _newOption.yAxis.data = dataSource.length
      ? dataSource.map((item) => item.etiologyName)
      : [];
    _newOption.series[0].data = dataSource.length
      ? dataSource.map((item) => item.resistanceRate.replace('%', ''))
      : [];
    _newOption.series[1].data = dataSource.length
      ? dataSource.map((item) => item.intermediacyRate.replace('%', ''))
      : [];
    _newOption.series[2].data = dataSource.length
      ? dataSource.map((item) => item.sensitizationRate.replace('%', ''))
      : [];
    _newOption.series[3].data = dataSource.length
      ? dataSource.map((item) => item.nonSensitizationRate.replace('%', ''))
      : [];
    setOption(_newOption);
  }, [dataSource]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="耐药性分析" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="drugResistanceChart" />
      </div>
    </div>
  );
};

export default DrugResistance;
