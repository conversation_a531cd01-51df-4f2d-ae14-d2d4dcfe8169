import { useEffect, useState } from 'react';
import { message } from 'antd';
import { seasonalDistributionApi } from '@/api/visual/businessMetrics';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import { useBusinessMetricsStore } from '../store';

const SeasonalDistribution: React.FC = () => {
  const { curMonitor } = useBusinessMetricsStore();

  const dataSourceOption = ['春', '夏', '秋', '冬'];
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
      right: 'right',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: dataSourceOption,
        axisLabel: {
          color: 'white',
        },
      },
    ],
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
      axisLabel: {
        color: 'white',
      },
    },
    series: [
      {
        name: '采集样本数量',
        type: 'bar',
        data: [],
        barMaxWidth: 18,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,118,255,0.3)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#15AFFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  });

  const getDataSource = (projectId: string) => {
    seasonalDistributionApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _data = Object.entries(res?.data);

        const _dataSource = dataSourceOption?.map((item, _index) => {
          const _indexData = _data.find(([_key, _value]) =>
            _key.includes(item)
          );
          return _indexData ? _indexData[1] : 0;
        });
        const _newOption: any = cloneDeep(option);
        _newOption.series[0].data = _dataSource;
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource(curMonitor);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curMonitor]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="样本采集季节分布" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <ChartsWrapper option={option} key="seasonalDistributionChart" />
      </div>
    </div>
  );
};

export default SeasonalDistribution;
