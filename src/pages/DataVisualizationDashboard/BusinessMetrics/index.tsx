// 业务指标
import React, { useEffect, useState } from 'react';
import { Button, message, Radio } from 'antd';
import { monitorListApi } from '@/api/pathogenDictionary';
import { mapContainerDataApi } from '@/api/visual/businessMetrics';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import { CloseOutlined } from '@ant-design/icons';
import AgeDistribution from './components/AgeDistributions';
import DrugResistance from './components/DrugResistance';
import MapContainer from './components/MapContainer';
import PathogenSpectrum from './components/PathogenSpectrum';
import PopulationDistribution from './components/PopulationDistribution';
import SeasonalDistribution from './components/SeasonalDistribution';
import './index.less';
import { useBusinessMetricsStore } from './store';
import { IMapDataSource } from './type';

interface IMonitorList {
  projectId: string;
  projectName: string;
}
type TBusinessMetricsProps = {};
const BusinessMetrics: React.FC<TBusinessMetricsProps> = () => {
  const { curMonitor, setCurMonitor } = useBusinessMetricsStore();
  const [mapDataSource, setMapDataSource] = useState<IMapDataSource[]>([]);

  const getMapDataSource = (projectId: string) => {
    mapContainerDataApi(projectId).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setMapDataSource(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [monitorList, setMonitorList] = useState<IMonitorList[]>([]);
  const [selectMonitor, setSelectMonitor] = useState('');
  const getMonitorList = async () => {
    try {
      const { code, data, msg } = await monitorListApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setMonitorList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getMonitorList();
  }, []);

  const handleFilterModalClose = () => {
    setSelectMonitor('');
    setFilterModalOpen(false);
  };

  useEffect(() => {
    getMapDataSource(curMonitor);
  }, [curMonitor]);

  return (
    <div
      className="businessMetricsBox w-full flex overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      {/* 左侧 */}
      <div className=" h-full w-[30%] flex-shrink-0 ">
        {/* 样本采集性别分布 */}
        <div className=" h-1/2 w-full pb-3 ">
          <PopulationDistribution />
        </div>
        {/* 耐药性分析 */}
        <div className=" h-1/2 w-full pt-3 ">
          <DrugResistance />
        </div>
      </div>
      {/* 中间 */}
      <div className=" w-[40%] px-4">
        {/* 地图 */}
        <div className=" h-2/3 w-full relative">
          <MapContainer id="businessMetricsMap" dataSource={mapDataSource} />
          <div
            className=" filterFeature"
            onClick={() => {
              setSelectMonitor(curMonitor);
              setFilterModalOpen(true);
            }}
          >
            选择监测项目
          </div>
        </div>
        {/* 病原谱 */}
        <div className=" h-1/3 w-full">
          <PathogenSpectrum />
        </div>
      </div>
      {/* 右侧 */}
      <div className=" h-full w-[30%] flex-shrink-0 ">
        {/* 样本采集季节分布 */}
        <div className=" h-1/2 w-full pb-3 ">
          <SeasonalDistribution />
        </div>
        {/* 样本采集年龄分布 */}
        <div className=" h-1/2 w-full pt-3 ">
          <AgeDistribution />
        </div>
      </div>

      {/* 筛选弹窗 */}

      <div
        className=" absolute h-full w-full top-0 left-0 bg-[rgba(0,0,0,0.5)] flex justify-center transition-all"
        style={{
          height: filterModalOpen ? '100%' : '0',
        }}
      >
        {filterModalOpen ? (
          <div className="filterModal py-3 flex flex-col pb-8">
            <div className="h-[53px] flex-shrink-0 flex items-center justify-between px-4 text-white font-bold text-xl">
              <span>选择监测项目</span>
              <span
                onClick={handleFilterModalClose}
                className=" cursor-pointer"
              >
                <CloseOutlined />
              </span>
            </div>
            <div className=" flex-1 p-6">
              <div className="w-full h-full flex flex-col overflow-y-auto">
                {monitorList.map((item, index) => (
                  <div
                    key={item.projectId}
                    className=" h-10 flex items-center gap-3 text-white px-6 cursor-pointer"
                    onClick={() => setSelectMonitor(item.projectId)}
                  >
                    <Radio
                      className=" text-xs"
                      checked={selectMonitor === item.projectId}
                    ></Radio>
                    <span>{index + 1}</span>
                    <span
                      className=" w-[60%] truncate"
                      title={item.projectName}
                    >
                      {item.projectName}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div className=" flex-shrink-0 h-12 px-4 flex items-center justify-center gap-4">
              <Button onClick={handleFilterModalClose}>取消</Button>
              <Button
                type="primary"
                onClick={() => {
                  setCurMonitor(selectMonitor);
                  handleFilterModalClose();
                }}
              >
                确定
              </Button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default BusinessMetrics;
