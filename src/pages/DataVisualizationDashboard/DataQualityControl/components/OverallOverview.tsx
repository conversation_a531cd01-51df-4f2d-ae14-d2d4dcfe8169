import { useEffect, useState } from 'react';
import img1 from '@/assets/fullScreen/dataQualityControl/img01.png';
import img2 from '@/assets/fullScreen/dataQualityControl/img02.png';
import img3 from '@/assets/fullScreen/dataQualityControl/img03.png';
import img4 from '@/assets/fullScreen/dataQualityControl/img04.png';
import img5 from '@/assets/fullScreen/dataQualityControl/img05.png';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

type TOverallOverviewProps = {
  dataSource: Record<string, any>;
};

const defaultOptions = [
  { label: '检测数据量', value: 0, key: 'checkData', img: img1 },
  { label: '异常数据量', value: 0, key: 'abnormalData', img: img2 },
  { label: '异常数据占比', value: 0, key: 'anomalyDataProportion', img: img3 },
  { label: '已整改数据量', value: 0, key: 'rectifiedData', img: img4 },
  {
    label: '整改完成率',
    value: 0,
    key: 'rectificationCompletionRate',
    img: img5,
  },
];

const OverallOverview: React.FC<TOverallOverviewProps> = ({ dataSource }) => {
  const [options, setOptions] = useState(() => defaultOptions);

  useEffect(() => {
    if (dataSource) {
      setOptions(
        defaultOptions.map((item) => ({
          ...item,
          value: dataSource[item.key],
        }))
      );
    } else {
      setOptions(() => defaultOptions);
    }
  }, [dataSource]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="整体概况" size="middle" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <div className="w-full h-full grid grid-cols-5 gap-4 pb-6">
          {options.map((item) => (
            <div
              key={item.key}
              className="w-full h-full rounded-md flex flex-col p-4 justify-evenly items-center bg-[url(/fullScreen/dataQualityControl/img-bg.png)] bg-full"
            >
              <img src={item.img} alt="" className=" w-1/2" />
              <span className=' text-xs'>{item.label}</span>
              <div className=" text-[#32D4FD] font-bold">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OverallOverview;
