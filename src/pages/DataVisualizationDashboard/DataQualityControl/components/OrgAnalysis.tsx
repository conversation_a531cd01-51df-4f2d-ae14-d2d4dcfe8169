import { orgAnalysisDataApi } from "@/api/visual/dataQualityControl"
import ChartsWrapper from "@/components/ChartsWrapper"
import FullScreenItemTitle from "@/components/FullScreenItemTitle"
import { codeDefinition } from "@/constants"
import { ECOption } from "@/hooks/useEcharts"
import { message } from "antd"
import { cloneDeep } from "lodash"
import { useEffect, useState } from "react"

interface IDataSource {
  orgName: string,
  checkData: number,
  abnormalData: number,
  rectifiedData: number
}

const OrgAnalysis: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      textStyle: {
        color: 'white'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisLabel: {
          rotate: 60,
          color: 'white'
        },
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: 'white'
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      }
    ],
    series: [
      {
        name: '检测数据量',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        }, itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,118,255,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(18,118,255)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        barMaxWidth: 24,
        data: []
      },
      {
        name: '异常数据量',
        type: 'bar',
        stack: 'Ad',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            global: false, // 缺省为 false
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,144,45,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(255,144,45)', // 100% 处的颜色
              },
            ],
          },
        },
        emphasis: {
          focus: 'series'
        },
        barMaxWidth: 24,
        data: []
      },
      {
        name: '已整改数据',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: 24,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 1,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,210,222,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        emphasis: {
          focus: 'series'
        },
        data: []
      },
    ]
  })

  const getDataSource = () => {
    orgAnalysisDataApi().then(res => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data || []
        const _newOption: any = cloneDeep(option)
        _newOption.xAxis[0].data = _dataSource.length ? _dataSource.map(item => item.orgName) : []
        _newOption.series[0].data = _dataSource.length ? _dataSource.map(item => item.checkData) : []
        _newOption.series[1].data = _dataSource.length ? _dataSource.map(item => item.abnormalData) : []
        _newOption.series[2].data = _dataSource.length ? _dataSource.map(item => item.rectifiedData) : []
        setOption(_newOption)
      } else {
        message.error(res.msg)
      }
    })
  }

  useEffect(() => {
    getDataSource()
  }, [])

  return <div className=" w-full h-full flex flex-col">
    <FullScreenItemTitle title="机构分析" />
    <div className=" flex-1 flex flex-wrap pt-3">
      <ChartsWrapper option={option} key="orgAnalysisChart" />
    </div>
  </div>
}

export default OrgAnalysis