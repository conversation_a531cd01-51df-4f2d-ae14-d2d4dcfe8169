import FullScreenItemTitle from "@/components/FullScreenItemTitle"
import { ECOption } from "@/hooks/useEcharts"
import { useEffect, useState } from "react"
import { IExceptionList } from "../type"
import ChartsWrapper from "@/components/ChartsWrapper"
import { cloneDeep } from "lodash"

type TExceptionTypeAnalysisProps = {
  dataSource: IExceptionList[]
}

const ExceptionTypeAnalysis: React.FC<TExceptionTypeAnalysisProps> = ({ dataSource }) => {

  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: [
      { left: '3%', top: '10%', width: '35%', height: '90%' },
      { right: '3%', top: '10%', width: '35%', height: '90%' },
    ],
    xAxis: [
      {
        gridIndex: 0, inverse: true, splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
      {
        gridIndex: 1, splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    yAxis: [
      {
        gridIndex: 0, name: '异常数量', nameTextStyle: {color: 'white'}, data: [], axisLabel: { show: false }
      },
      {
        gridIndex: 1, name: '已整改数量',nameTextStyle: {color: 'white'}, data: [], axisLabel: {
          color: 'white'
        }
      },
    ],
    series: [
      {
        name: 'I',
        type: 'bar',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: [],
        itemStyle: {
          borderRadius: [8, 0, 0, 8],
          color: {
            type: 'linear',
            x: 1,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,144,45, 0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(255,144,45)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        barMaxWidth: 16
      },
      {
        name: 'II',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        data: [],
        itemStyle: {
          borderRadius: [0, 8, 8, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(18,210,222,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#12D2DE', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        barMaxWidth: 16
      },
    ]
  })

  useEffect(() => {
    const _newOption: any = cloneDeep(option)
    _newOption.yAxis[0].data = dataSource.length ? dataSource.map(item => item.abnormalType) : []
    _newOption.yAxis[1].data = dataSource.length ? dataSource.map(item => item.abnormalType) : []
    _newOption.series[0].data = dataSource.length ? dataSource.map(item => item.abnormalNum) : []
    _newOption.series[1].data = dataSource.length ? dataSource.map(item => item.rectificationNum) : []

    setOption(_newOption)
  }, [dataSource])

  return <div className=" w-full h-full flex flex-col">
    <FullScreenItemTitle title="异常类型分析" />
    <div className=" flex-1">
      <ChartsWrapper option={option} key="exceptionTypeAnalysisChart" />
    </div>
  </div>
}

export default ExceptionTypeAnalysis