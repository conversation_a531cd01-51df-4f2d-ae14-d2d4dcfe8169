import { annualAnalysisDataApi } from "@/api/visual/dataQualityControl"
import ChartsWrapper from "@/components/ChartsWrapper"
import FullScreenItemTitle from "@/components/FullScreenItemTitle"
import { codeDefinition } from "@/constants"
import { ECOption } from "@/hooks/useEcharts"
import { message } from "antd"
import { cloneDeep } from "lodash"
import { useEffect, useState } from "react"

interface IDataSource {
    abnormaldata: number
    checkdata: number
    rectificationcompletionrate: string
    year: number
}

const AnnualAnalysis: React.FC = () => {

    const [option, setOption] = useState<ECOption>({
        color: ['rgba(18,118,255)', 'rgba(255,144,45)', '#12D2DE'],
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            textStyle: {
                color: 'white'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: [],
                axisLabel: {
                    rotate: 60,
                    color: 'white'
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    color: 'white'
                },
                splitLine: {
                    lineStyle: {
                        color: ['#FFFFFF'],
                        opacity: 0.25,
                    },
                },
            },
            {
                type: 'value',
                axisLabel: {
                    color: 'white',
                    formatter: '{value} %',
                },
            },
        ],
        series: [
            {
                name: '检测数据',
                type: 'bar',
                data: [],
                barMaxWidth: 18,
            },
            {
                name: '异常数据',
                type: 'bar',
                data: [],
                barMaxWidth: 18,
            },
            {
                name: '异常数据整改完成率',
                type: 'line',
                data: [],
                tooltip: {
                  valueFormatter: value => `${value}%`
                }
            },
        ]
    })

    const getDataSource = () => {
        annualAnalysisDataApi().then(res => {
            if (res.code === codeDefinition.QUERY_SUCCESS) {
                const _dataSource: IDataSource[] = res.data || []
                const _newOption: any = cloneDeep(option)
                _newOption.xAxis[0].data = _dataSource.length ? _dataSource.map(item => item.year + '年') : []
                _newOption.series[0].data = _dataSource.length ? _dataSource.map(item => item.checkdata) : []
                _newOption.series[1].data = _dataSource.length ? _dataSource.map(item => item.abnormaldata) : []
                _newOption.series[2].data = _dataSource.length ? _dataSource.map(item => Number(item.rectificationcompletionrate.replace('%', ''))) : []
                setOption(_newOption)
            } else {
                message.error(res.msg)
            }
        })
    }

    useEffect(() => {
        getDataSource()
    }, [])

    return <div className=" w-full h-full flex flex-col">
        <FullScreenItemTitle title="年度分析" />
        <div className=" flex-1 pt-3">
            <ChartsWrapper option={option} key="annualAnalysisChart" />
        </div>
    </div>
}

export default AnnualAnalysis