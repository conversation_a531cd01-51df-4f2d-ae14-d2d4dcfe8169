// 数据质控
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { exceptionTypeAnalysisDataApi } from '@/api/visual/dataQualityControl';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import AnnualAnalysis from './components/AnnualAnalysis';
import ExceptionTypeAnalysis from './components/ExceptionTypeAnalysis';
import MapContainer from './components/MapContainer';
import MonthlyAnalysis from './components/MonthlyAnalysis';
import OrgAnalysis from './components/OrgAnalysis';
import OverallOverview from './components/OverallOverview';
import './index.less';
import { IExceptionList } from './type';

const DataQualityControl: React.FC = () => {
  const [exceptionList, setExceptionList] = useState<IExceptionList[]>([]);

  const [statistics, setStatistics] = useState<Record<string, any>>([]);

  const getDataSource = () => {
    exceptionTypeAnalysisDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setExceptionList(res.data.typeVisualResultList);
        setStatistics(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div
      className="businessMetricsBox w-full flex overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      {/* 左侧 */}
      <div className=" h-full w-[30%] flex-shrink-0 ">
        {/* 机构分析 */}
        <div className=" h-1/2 w-full pb-3 ">
          <OrgAnalysis />
        </div>
        {/* 异常类型分析 */}
        <div className=" h-1/2 w-full pt-3 ">
          <ExceptionTypeAnalysis dataSource={exceptionList} />
        </div>
      </div>
      {/* 中间 */}
      <div className=" w-[40%] px-4">
        {/* 地图 */}
        <div className=" h-[72%] w-full">
          <MapContainer id="dataQualityControlMap" />
        </div>
        {/* 整体概况 */}
        <div className=" h-[28%] w-full">
          <OverallOverview dataSource={statistics} />
        </div>
      </div>
      {/* 右侧 */}
      <div className=" h-full w-[30%] flex-shrink-0 ">
        {/* 月度分析 */}
        <div className=" h-1/2 w-full pb-3 ">
          <MonthlyAnalysis />
        </div>
        {/* 年度分析 */}
        <div className=" h-1/2 w-full pt-3 ">
          <AnnualAnalysis />
        </div>
      </div>
    </div>
  );
};

export default DataQualityControl;
