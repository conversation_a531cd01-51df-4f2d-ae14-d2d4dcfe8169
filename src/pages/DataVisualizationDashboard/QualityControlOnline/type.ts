export type TRiskCuesPieData = {
    name: string;
    value: number;
    key: string
}

export type TRiskCuesLineData =
    {
        month: number
        suspectedcluescount: number
        corroboratecount: number
    }

export type TRiskCuesList =
    {
        "id": number,
        "indexId": string,
        "indexLabel": string,
        "clueLabel": string,
        "riskClue": string,
        "score": number,
        "clueYear": number,
        "clueMonth": number,
        "createId": string,
        "createName": string,
        "createTime": string,
        "updateId": string,
        "updateName": string,
        "updateTime": string,
        "remark": string,
        "orgId": number,
        "orgName": string,
        "auditStatus": null,
        "indexDetail": null
    }