/* eslint-disable @typescript-eslint/no-unused-vars */
// 质控在线
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { riskCuesDataApi } from '@/api/visual/qualityControlOnline';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import OrgRiskAnalysis from './components/OrgRiskAnalysis';
import OrgRiskEvaluate from './components/OrgRiskEvaluate';
import OverallOverview from './components/OverallOverview';
import RiskCuesPerception from './components/RiskCuesPerception';
import RisktrailList from './components/RisktrailList';
import { TRiskCuesLineData, TRiskCuesList, TRiskCuesPieData } from './type';

type TQualityControlOnlineProps = {};
const QualityControlOnline: React.FC<TQualityControlOnlineProps> = () => {
  // const getOrgDataSource = () => {
  //   orgRiskDataApi().then(res => {
  //     if (res.code === codeDefinition.QUERY_SUCCESS) {

  //     } else {
  //       message.error(res.msg)
  //     }
  //   })
  // }

  const [loading, setLoading] = useState(false);
  const [riskCuesLineData, setRiskLineData] = useState<TRiskCuesLineData[]>([]);
  const [riskCuesPieData, setRiskCuesPieData] = useState<TRiskCuesPieData[]>([
    { name: '确证线索', value: 0, key: 'corroborateCount' },
    { name: '疑似线索', value: 0, key: 'suspectedCluesCount' },
  ]);
  const [riskCuesList, setRiskCuesList] = useState<TRiskCuesList[]>([]);
  const getRiskCuesDataSource = () => {
    setLoading(true);
    riskCuesDataApi()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setRiskLineData(res.data.riskCues || []);
          setRiskCuesPieData(
            riskCuesPieData.map((item) => ({
              ...item,
              value: res.data[item.key],
            }))
          );
          setRiskCuesList(res.data.riskClueList || []);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getRiskCuesDataSource();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      className="businessMetricsBox w-full flex flex-col overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      <div className=" h-2/3 flex gap-6">
        <div className="h-full w-1/3 flex flex-col">
          <div className="h-1/2 w-full">
            {/* 整体概况 */}
            <OverallOverview />
          </div>
          <div className="h-1/2 w-full">
            {/* 机构风险评价 */}
            <OrgRiskEvaluate />
          </div>
        </div>
        <div className="h-full w-2/3">
          <div className="h-1/2 w-full">
            {/* 机构风险分析 */}
            <OrgRiskAnalysis />
          </div>
          <div className="h-1/2 w-full">
            {/* 风险线索感知情况 */}
            <RiskCuesPerception
              lineData={riskCuesLineData}
              pieData={riskCuesPieData}
            />
          </div>
        </div>
      </div>
      <div className=" h-1/3">
        {/* 风险线索列表 */}
        <RisktrailList dataSource={riskCuesList} loading={loading} />
      </div>
    </div>
  );
};

export default QualityControlOnline;
