import { useEffect, useState } from 'react';
import { message } from 'antd';
import { overallOverviewDataApi } from '@/api/visual/qualityControlOnline';
import img1 from '@/assets/fullScreen/qualityControlOnline/img01.png';
import img2 from '@/assets/fullScreen/qualityControlOnline/img02.png';
import img3 from '@/assets/fullScreen/qualityControlOnline/img03.png';
import { codeDefinition } from '@/constants';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

const OverallOverview: React.FC = () => {
  const [options, setOptions] = useState([
    { label: '机构数量', value: 0, key: 'orgNum', unit: '家', img: img1 },
    { label: '检测数量', value: 0, key: 'checkData', unit: '批次', img: img2 },
    {
      label: '风险线索数量',
      value: 0,
      key: 'riskClueTotal',
      unit: '项',
      img: img3,
    },
  ]);

  const getDataSource = () => {
    overallOverviewDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setOptions(
          options.map((item) => ({
            ...item,
            value: res.data[item.key] || 0,
          }))
        );
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);
  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="整体概况" size="middle" />
      <div className=" flex-1 flex flex-wrap pt-3">
        <div className="w-full h-full grid grid-cols-3 gap-8 p-4">
          {options.map((item) => (
            <div
              key={item.key}
              className="w-full h-full rounded-md flex flex-col p-4 justify-evenly items-center bg-[url(/fullScreen/qualityControlOnline/img-bg.png)] bg-full"
            >
              <img src={item.img} alt="" className=" w-[75%]" />
              <span>{item.label}</span>
              <div className=' text-[#32D4FD] align-middle font-bold'>
                <span className=' text-2xl mr-1'>{item.value}</span>
                <span className=' text-xs'>{item.unit}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OverallOverview;
