import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, message } from 'antd';
import { peopleAnalysisDataApi } from '@/api/visual/monitoringOverview';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import { TRiskCuesList } from '../type';

type TRisktrailListProps = {
  dataSource: TRiskCuesList[];
  loading: boolean;
};

const RisktrailList: React.FC<TRisktrailListProps> = ({
  dataSource,
  loading,
}) => {
  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[80px_120px_180px_180px_120px_1fr_80px_80px] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.updateTime}</span>
            <span>{_item.orgName}</span>
            <span>{_item.indexLabel}</span>
            <span>{_item.clueLabel}</span>
            <span title={_item.riskClue} className=" truncate">
              {_item.riskClue}
            </span>
            <span className=" text-center">{_item.score}</span>
            {/* <span className=" text-center">
              <Button type="link">查看</Button>
            </span> */}
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);

  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle title="风险线索列表" size="big" />
      <div className="w-full h-[40px] grid grid-cols-[80px_120px_180px_180px_120px_1fr_80px_80px] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
        <span className="text-center">序号</span>
        <span>更新日期</span>
        <span>机构名称</span>
        <span>风险指标</span>
        <span>线索类型</span>
        <span>风险线索</span>
        <span className=" text-center">指标分值</span>
        {/* <span className=" text-center">操作</span> */}
      </div>
      <div className=" flex-1">
        {loading ? (
          <div className="w-full h-full">
            {/* <Loading>Loading...</Loading> */}
          </div>
        ) : dataSource.length ? (
          <div
            className="w-full bg-[rgba(8,24,48,0.52)]"
            style={{ height: slideParams.parentHeight }}
          >
            <SeamlessScroll
              swiperSlideClassname="!w-full !h-[40px]"
              direction="vertical"
              spaceBetween={0}
              slidesPerView={slideParams.slidesPerView}
              children={createScrollDom()}
            />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default RisktrailList;
