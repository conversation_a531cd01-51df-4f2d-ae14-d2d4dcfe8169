/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from 'react';
import { message } from 'antd';
import { getOrgRiskAssisy } from '@/api/visual/qualityControlOnline';
import { codeDefinition } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';

const colors = ['#DA1B1D', '#FF843A', '#FBEA28', '#3A89FF', '#1DF598'];

const OrgRiskAnalysis: React.FC = () => {
  const [lineOption, setLineOption] = useState<ECOption>({});

  const [pieOption, setPieOption] = useState<ECOption>();

  /**
   * 获取机构风险分析数据 getOrgRiskAssisy
   */
  const queryOrgRiskAssisy = async () => {
    try {
      const { code, data, msg } = await getOrgRiskAssisy({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setLineOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          show: true,
          data: ['正常'],
          textStyle: {
            color: '#fff',
          },
          itemHeight: 12,
          itemWidth: 12,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: data?.map((_i: Record<string, any>) => _i?.name),
            axisPointer: {
              type: 'shadow',
            },
            axisLabel: {
              color: 'white',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              color: 'white',
            },
            splitLine: {
              lineStyle: {
                color: ['#FFFFFF'],
                opacity: 0.25,
              },
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: data?.map((_i: Record<string, any>, _idx: number) => {
              return {
                value: _i?.num,
                itemStyle: { color: colors[_idx] },
              };
            }),
          },
        ],
      });

      setPieOption({
        color: ['#DA1B1D', '#FF843A', '#FBEA28', '#3A89FF', '#1DF598'],
        tooltip: {
          trigger: 'item',
        },
        // legend: {
        //   top: '5%',
        //   left: 'center'
        // },
        series: [
          {
            type: 'pie',
            radius: ['45%', '65%'],
            // center: ['50%', '70%'],
            // adjust the start and end angle
            // startAngle: 180,
            // endAngle: 360,
            data: data?.map((_i: Record<string, any>) => {
              return {
                value: _i?.num,
                name: _i?.name,
                key: _i?.name,
              };
            }),
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryOrgRiskAssisy();
  }, []);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="机构风险分析" size="middle" />

      <div className=" flex-1 flex pt-3 ">
        <div className=" h-full w-2/3">
          <ChartsWrapper option={lineOption} key="OrgRiskAnalysisLineChart" />
        </div>
        <div className=" h-full w-1/3 flex flex-col">
          <div className=" h-8 leading-8 text-white font-bold text-center">
            年度机构风险统计
          </div>
          <div className=" flex-1">
            <ChartsWrapper
              option={pieOption as any}
              key="OrgRiskAnalysisPieChart"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrgRiskAnalysis;
