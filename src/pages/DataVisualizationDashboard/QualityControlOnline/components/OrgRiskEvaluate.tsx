/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { message } from 'antd';
import { getOrgRiskComment } from '@/api/visual/qualityControlOnline';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';

interface IDataSource {
  sampleCount: number;
  detectionOverCount: number;
  positiveSampleCount: number;
  strainsCount: number;
  job: string;
}

const OrgRiskEvaluate: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  const getDataSource = (params: Record<string, any>) => {
    setLoading(true);
    getOrgRiskComment({ pageNum: 1, pageSize: 99999 })
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res?.data?.rows);
        } else {
          message.error(res?.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource({});
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[60px_2fr_2fr_1.2fr_1.2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item?.date}</span>
            <span>{_item.name}</span>
            <span className="text-center">{_item.code}</span>
            <span className="text-center">{_item.score}</span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);

  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle title="机构风险评价" />
      <div className="w-full h-[40px] grid grid-cols-[60px_2fr_2fr_1.2fr_1.2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
        <span className="text-center">序号</span>
        <span>评价时间</span>
        <span>机构名称</span>
        <span>社会信用代码</span>
        <span>风险评价总分</span>
      </div>
      <div className=" flex-1">
        {loading ? (
          <div className="w-full h-full">
            {/* <Loading>Loading...</Loading> */}
          </div>
        ) : dataSource?.length ? (
          <div
            className="w-full bg-[rgba(8,24,48,0.52)]"
            style={{ height: slideParams.parentHeight }}
          >
            <SeamlessScroll
              swiperSlideClassname="!w-full !h-[40px]"
              direction="vertical"
              spaceBetween={0}
              slidesPerView={slideParams.slidesPerView}
              children={createScrollDom()}
            />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default OrgRiskEvaluate;
