/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import { TRiskCuesLineData, TRiskCuesPieData } from '../type';

type TRiskCuesPerceptionProps = {
  lineData: TRiskCuesLineData[];
  pieData: TRiskCuesPieData[];
};

const RiskCuesPerception: React.FC<TRiskCuesPerceptionProps> = ({
  lineData,
  pieData,
}) => {
  const [lineOption, setLineOption] = useState<ECOption>({
    color: ['#12BFF8', '#FFAE46'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: 'white',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: 'white',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    series: [
      {
        name: '确证线索',
        type: 'bar',
        data: [],
      },
      {
        name: '疑似线索',
        type: 'bar',
        data: [],
      },
    ],
  });

  const [pieOption, setPieOption] = useState<ECOption>({
    color: ['#12BFF8', '#FFAE46'],
    tooltip: {
      trigger: 'item',
    },
    // legend: {
    //   top: '5%',
    //   left: 'center'
    // },
    series: [
      {
        type: 'pie',
        radius: ['45%', '65%'],
        // center: ['50%', '70%'],
        // adjust the start and end angle
        // startAngle: 180,
        // endAngle: 360,
        data: [],
      },
    ],
  });

  useEffect(() => {
    const _newOption: any = cloneDeep(lineOption);
    _newOption.xAxis[0].data = lineData.length
      ? lineData.map((item) => item.month + '月')
      : [];
    _newOption.series[0].data = lineData.length
      ? lineData.map((item) => item.corroboratecount)
      : [];
    _newOption.series[1].data = lineData.length
      ? lineData.map((item) => item.suspectedcluescount)
      : [];

    setLineOption(_newOption);
  }, [lineData]);

  useEffect(() => {
    console.log('pieData', pieData);
    const _newOption: any = cloneDeep(pieOption);
    _newOption.series[0].data = pieData;

    setPieOption(_newOption);
  }, [pieData]);

  return (
    <div className=" w-full h-full flex flex-col">
      <FullScreenItemTitle title="风险线索感知情况" size="middle" />

      <div className=" flex-1 flex pt-3 ">
        <div className=" h-full w-2/3">
          <ChartsWrapper option={lineOption} key="OrgRiskAnalysisLineChart" />
        </div>
        <div className=" h-full w-1/3 flex flex-col">
          <div className=" h-8 leading-8 text-white font-bold text-center">
            年度风险线索统计
          </div>
          <div className=" flex-1">
            <ChartsWrapper option={pieOption} key="OrgRiskAnalysisPieChart" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskCuesPerception;
