/* eslint-disable jsx-a11y/alt-text */
// 送检及时率模块
import React, { useEffect, useState,useImperativeHandle, } from 'react';
import { message } from 'antd';
import * as echarts from 'echarts';
import {
  sampleSubmissionTimelinessCountApi,
  sampleSubmissionTimelinessRateDataApi,
} from '@/api/visual/qualityControlMetrics';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';
import titleImg from '/fullScreen/common/title-dec.png';
import img1 from '/fullScreen/qualityControlMetrics/img1.png';
import img2 from '/fullScreen/qualityControlMetrics/img2.png';
import img3 from '/fullScreen/qualityControlMetrics/img3.png';

type TSampleSubmissionTimelinessRateProps = {};

interface IDataSource {
  orgName: string;
  sampleCount: number;
  timelySampleCount: number;
  timelyPercent: string;
  regionValue?: string;
}

type TEditProps = {
  orgName?: string;
  sampleCount?: number;
  timelySampleCount?: number;
  timelyPercent?: string;
  regionValue?: string;
  orgNameValue?: string;
  onRef?: any;

};


const SampleSubmissionTimelinessRate: React.FC<
TEditProps
> = ({
  orgNameValue,
  regionValue,
  onRef,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        getDataSource();
        getCounts();
      },
    };
  });

  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {},
    legend: {
      // data: ['采集样本数', '及时送检样本数', '及时率'],
      textStyle: {
        color: '#FFFFFF',
      },
      // top: 20,
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisPointer: {
          type: 'shadow',
        },
        nameTextStyle: {
          color: 'white',
        },
        axisLabel: {
          show: true,
          color: 'white',
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        // interval: 50,
        axisLabel: {
          color: '#FFFFFF',
        },
        splitLine: {
          show: false,
        },
        nameTextStyle: {
          color: 'white',
        },
      },
      {
        type: 'value',
        // interval: 5,
        // axisLabel: {
        //   textStyle: { color: '#FFFFFF' },
        // },
        axisLabel: {
          formatter: '{value}%',
          color: '#FFFFFF',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
        nameTextStyle: {
          color: 'white',
        },
      },
    ],
    series: [
      {
        name: '采集样本数',
        type: 'bar',
        data: [],
        barWidth: 14,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(15,102,247,0.2)' },
            { offset: 1, color: '#0F66F7' },
          ]),
        },
      },
      {
        name: '及时送检样本数',
        type: 'bar',
        data: [],
        barWidth: 14,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(121,175,255,0.2)' },
            { offset: 1, color: '#79AFFF' },
          ]),
        },
      },
      {
        name: '及时率',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        itemStyle: {
          color: '#0AE1A6',
        },
      },
    ],
  });

  const getDataSource = () => {
    sampleSubmissionTimelinessRateDataApi({
      orgName: orgNameValue,
      region: regionValue,
    }).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data;
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis[0].data = _dataSource.length
          ? _dataSource.map((item) => item.orgName)
          : [];
        _newOption.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.sampleCount)
          : [];
        _newOption.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.timelySampleCount)
          : [];
        _newOption.series[2].data = _dataSource.length
          ? _dataSource.map((item) => parseFloat(item.timelyPercent).toFixed(2))
          : [];
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  const [counts, setCounts] = useState<Record<string, any>>({});

  const getCounts = () => {
    sampleSubmissionTimelinessCountApi({
      orgName: orgNameValue,
      region: regionValue,
    }).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        setCounts(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
    getCounts();
  }, []);

  return (
    <div className="w-full h-full font-PangmengZhengdaoBiaoti">
      <FullScreenItemTitle title="送检及时率分析" />
      <div
        className="flex flex-row flex-nowrap justify-between items-center gap-[2.552083vw] mt-[1.666667vh]"
        style={{
          paddingLeft: `${converPxToVW(24)}vw`,
          paddingRight: `${converPxToVW(24)}vw`,
        }}
      >
        <div
          className="flex flex-row flex-nowrap items-center bg-[url('/fullScreen/qualityControlMetrics/kuang.png')] bg-no-repeat bg-cover pl-[1.197917vw]"
          style={{
            width: `${converPxToVW(230)}vw`,
            height: `${converPxToVH(75)}vh`,
          }}
        >
          <img
            src={img1}
            alt=""
            style={{
              width: `${converPxToVW(51)}vw`,
              height: `${converPxToVW(51)}vw`,
            }}
          />
          <div className="" style={{ marginLeft: `${converPxToVW(21)}vw` }}>
            <div
              className="text-[#2CF4FF]"
              style={{ fontSize: `${converPxToVW(24)}vw` }}
            >
              {counts?.sampleCount || 0}
            </div>
            <div
              className="relative -top-[6px]"
              style={{ fontSize: `${converPxToVW(16)}vw` }}
            >
              采集样本数
            </div>
          </div>
        </div>
        <div
          className="flex flex-row flex-nowrap items-center bg-[url('/fullScreen/qualityControlMetrics/kuang.png')] bg-no-repeat bg-cover pl-[1.197917vw]"
          style={{
            width: `${converPxToVW(230)}vw`,
            height: `${converPxToVH(75)}vh`,
          }}
        >
          <img
            src={img2}
            alt=""
            style={{
              width: `${converPxToVW(51)}vw`,
              height: `${converPxToVW(51)}vw`,
            }}
          />
          <div className="" style={{ marginLeft: `${converPxToVW(21)}vw` }}>
            <div
              className="text-[#2CF4FF]"
              style={{ fontSize: `${converPxToVW(24)}vw` }}
            >
              {counts?.timelySampleCount || 0}
            </div>
            <div
              className="relative -top-[6px]"
              style={{ fontSize: `${converPxToVW(16)}vw` }}
            >
              及时送检总数
            </div>
          </div>
        </div>
        <div
          className="flex flex-row flex-nowrap items-center bg-[url('/fullScreen/qualityControlMetrics/kuang.png')] bg-no-repeat bg-cover pl-[1.197917vw]"
          style={{
            width: `${converPxToVW(230)}vw`,
            height: `${converPxToVH(75)}vh`,
          }}
        >
          <img
            src={img3}
            alt=""
            style={{
              width: `${converPxToVW(51)}vw`,
              height: `${converPxToVW(51)}vw`,
            }}
          />
          <div className="" style={{ marginLeft: `${converPxToVW(21)}vw` }}>
            <div
              className="text-[#2CF4FF]"
              style={{ fontSize: `${converPxToVW(24)}vw` }}
            >
              {counts?.timelyPercent || '0'}
            </div>
            <div
              className="relative -top-[6px]"
              style={{ fontSize: `${converPxToVW(16)}vw` }}
            >
              及时率
            </div>
          </div>
        </div>
      </div>
      <div className="w-full h-full mt-[1.666667vh]">
        <ChartsWrapper option={option as any} height={300} />
      </div>
    </div>
  );
};

export default SampleSubmissionTimelinessRate;
