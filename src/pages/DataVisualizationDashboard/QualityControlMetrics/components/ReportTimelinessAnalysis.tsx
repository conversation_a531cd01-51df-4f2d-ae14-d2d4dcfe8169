/* eslint-disable @typescript-eslint/no-unused-vars */
// 报告及时性分析模块
import React, { useEffect, useState,useImperativeHandle } from 'react';
import { message } from 'antd';
import * as echarts from 'echarts';
import { timelinessRateApi,reportNum } from '@/api/visual/qualityControlMetrics';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { ECOption } from '@/hooks/useEcharts';

import { RollbackOutlined, } from '@ant-design/icons';

type TReportTimelinessAnalysisProps = {};
interface IDataSource {
  orgName: string;
  inspectionFinishCount: string;
  inspectionTimelyCount: string;
}


type TEditProps = {
  orgName?: string;
  sampleCount?: number;
  timelySampleCount?: number;
  timelyPercent?: string;
  regionValue?: string;
  orgNameValue?: string;
  onRef?: any;
};




const ReportTimelinessAnalysis: React.FC<
 TEditProps
 > = ({
   orgNameValue,
   regionValue,
   onRef,
 }) => {
   // 暴露给父组件
   useImperativeHandle(onRef, () => {
     return {
       handleSubmit: () => {
         getDataSource();
       },
     };
   });
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#FFFFFF',
      },
      top: 20,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '0',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: { color: '#FFFFFF' },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: '检测完成数',
        type: 'bar',
        data: [],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(7,195,229,0.2)' },
            { offset: 1, color: '#07C3E5' },
          ]),
        },
      },
      {
        name: '出具报告数',
        type: 'bar',
        data: [],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(255,144,45,0.2)' },
            { offset: 1, color: '#FF902D' },
          ]),
        },
      },
    ],
  });


  const [option2, setOption2] = useState<ECOption>({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      toolbox: {},
      legend: {
        textStyle: {
          color: '#FFFFFF',
        },
        top: 20,
      },
      xAxis: [
        {
          type: 'category',
          data: [],
          axisPointer: {
            type: 'shadow',
          },
          axisLabel: {
            show: true,
            color: '#FFFFFF',
          },
          axisTick: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: { color: '#FFFFFF' },
          splitLine: {
            show: false,
          },
        },
        {
          type: 'value',
          axisLabel: {
            formatter: '{value}%',
            color: '#FFFFFF',
          },
          splitLine: {
            lineStyle: {
              color: ['#FFFFFF'],
              opacity: 0.25,
            },
          },
        },
      ],
      series: [
        {
          name: '样本数量',
          type: 'bar',
          data: [],
          barWidth: 14,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: 'rgba(13,161,255,0.2)' },
              { offset: 1, color: '#0DA1FF' },
            ]),
          },
        },

  
      ],
    });


  const [isChart1SShow, setIsChart1SShow] = useState(true);

  const [isChart2SShow, setIsChart2SShow] = useState(false);

  const [orgName, setOrgName] = useState("");


  const getDataSource = () => {
    timelinessRateApi({
      orgName: orgNameValue,
      region: regionValue,
    }).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data || [];
        const _newOption: any = cloneDeep(option);
        _newOption.yAxis.data = _dataSource.length
          ? _dataSource.map((item) => item.orgName)
          : [];
        _newOption.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.inspectionFinishCount)
          : [];
        _newOption.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.inspectionTimelyCount)
          : [];
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  const getDataSource2 = () => {

    reportNum({
      orgName: orgName,
      region: regionValue,
    }).then((res:any) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: any = res.data || [];
        const _newOption: any = cloneDeep(option2);
        _newOption.xAxis[0].data = [
          "送检超期", "检测超期", "报告超期"
        ]
        _newOption.series[0].data = [
          res.data.sendTimeout , res.data.inspectTimeout ,res.data.reportTimeout
        ]
        setOption2(_newOption);
        setIsChart2SShow(true)
      } else {
        message.error(res.msg);
      }
    });
  };


  useEffect(() => {
    getDataSource();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 定义点击柱状图的回调函数
  const handleBarClick = (params: any) => {
    console.log('点击了柱状图', params);
    // 可以在这里添加更多逻辑，比如跳转到详情页等
    setIsChart1SShow(false)
    setOrgName(params.name)
    getDataSource2()
    
  };

  const handleHiddenChart = (params: any) => {
    // 可以在这里添加更多逻辑，比如跳转到详情页等
    setIsChart2SShow(false)
    setIsChart1SShow(true)
  };
  

  return (
    <div className="w-full h-full font-PangmengZhengdaoBiaoti flex flex-col">
      <FullScreenItemTitle title="报告及时性分析" />
      {isChart1SShow && (
      <div className="flex-1">
        <ChartsWrapper
          option={option as any}
          height={300}
          key="reportTimelinessAnalysisChart"
           // 传递点击事件监听器
          handleEvent={handleBarClick}
        />
      </div>
      )}

      {isChart2SShow && (
      <div className="flex-1 chart-2">
        <div className='back-btn'
          onClick={handleHiddenChart}
        >
          <RollbackOutlined />
          返回</div>
        <ChartsWrapper
          option={option2 as any}
          height={250}
          key="reportTimelinessAnalysisChart2"
        />
      </div>
      )}


    </div>
  );
};

export default ReportTimelinessAnalysis;
