/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/alt-text */
// 病原检测率率模块
import React, { useEffect, useState,useImperativeHandle} from 'react';
import { message } from 'antd';
import * as echarts from 'echarts';
import { pathogenDetectionRateDataApi } from '@/api/visual/qualityControlMetrics';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';
import titleImg from '/fullScreen/common/title-dec.png';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';

type TPathogenDetectionRateAnalysisProps = {};
interface IDataSource {
  orgName: string;
  receiveSampleCount: number;
  inspectionFinishCount: number;
  inspectionPercent: string;
}

type TEditProps = {
  orgName?: string;
  sampleCount?: number;
  timelySampleCount?: number;
  timelyPercent?: string;
  regionValue?: string;
  orgNameValue?: string;
  onRef?:any;
};




const PathogenDetectionRateAnalysis: React.FC<
 TEditProps
 > = ({
   orgNameValue,
   regionValue,
   onRef,
 }) => {
   // 暴露给父组件
   useImperativeHandle(onRef, () => {
     return {
       handleSubmit: () => {
         // handleSave();
         getDataSource()
       },
     };
   });
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      textStyle: {
        color: '#FFFFFF',
      },
      top: 20,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '0',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      splitLine: {
        lineStyle: {
          color: ['#FFFFFF'],
          opacity: 0.25,
        },
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      // axisLabel: { color: '#FFFFFF' },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: 'white',
        // rotate: -30,
        interval: 0,
        fontSize: 10,
        width: 120,
      },
    },
    series: [
      {
        name: '接收样本数',
        type: 'bar',
        data: [38, 50, 42, 56, 65, 52, 41],
        barMaxWidth: 28,
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(31,136,255,0.2)' },
            { offset: 1, color: '#1F88FF' },
          ]),
        },
      },
      {
        name: '检测完成样本数',
        type: 'bar',
        barMaxWidth: 28,
        data: [29, 42, 36, 49, 56, 42, 28],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgba(9,239,161,0.2)' },
            { offset: 1, color: '#09EFA1' },
          ]),
        },
      },
    ],
  });

  const getDataSource = () => {
    pathogenDetectionRateDataApi({
      orgName: orgNameValue,
      region: regionValue,
    }).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: IDataSource[] = res.data || [];
        const _newOption: any = cloneDeep(option);
        _newOption.yAxis.data = _dataSource.length
          ? _dataSource.map((item) => item.orgName)
          : [];
        _newOption.series[0].data = _dataSource.length
          ? _dataSource.map((item) => item.receiveSampleCount)
          : [];
        _newOption.series[1].data = _dataSource.length
          ? _dataSource.map((item) => item.inspectionFinishCount)
          : [];
        setOption(_newOption);
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div className="w-full h-full font-PangmengZhengdaoBiaoti">
      <FullScreenItemTitle title='病原检测率分析' />
      <div className="w-full h-full">
        <ChartsWrapper option={option} height={300} />
      </div>
    </div>
  );
};

export default PathogenDetectionRateAnalysis;
