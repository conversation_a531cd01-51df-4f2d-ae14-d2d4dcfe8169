
.form-wraper {
    .ant-form-item {
        margin-bottom: 0;
    }

    .ant-form-item-label  label{
        color: #fff;
    }
    .ant-input,.ant-select-selector {
        background: rgba(255, 255, 255, 0) !important;
        color: #fff;
        border: #033498 1px solid !important;
    }
    // 设置 Select 组件 placeholder 样式
    .ant-select-selection-placeholder {
        color: #eaebed; // 设置 placeholder 文字颜色
        font-size: 14px; // 设置 placeholder 文字大小
    }
    .ant-select-selection-item {
        color: #fff !important;
    }
}

.businessMetricsBox1 {
    .d-flex {
        display: flex;
        flex-wrap: wrap;
        overflow-x: hidden;
    }
    .tips {
    position: absolute !important;
    right: 100px;
    bottom: 100px;
    color: #fff;
}
    .filterFeature {
        height: 44px;
        width: 165px;
        background: url(/src/assets/fullScreen/BusinessMetrics/filter-bg.png);
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        padding-left: 48px;
        position: absolute;
        top: 0;
        left: 0;
        cursor: pointer;
    }
    .filterModal {
        width: 1033px;
        height: 659px;
        background: #0B2545;
        border: 1px solid #00DBFB;
        background-size: 100% 100%;
        margin-top: 100px;
        .w-50 {
            width: 50%;
            position: relative;
            padding-top: 20px;
        }

        .title {
            position: absolute;
            left: 0;
            top: 0px;
            font-size: 14px;
            color: #fff;
        }

        .r-bg {
            background: url(/src/assets/fullScreen/BusinessMetrics/u2.png) no-repeat 0px  -10px;
            background-size: 100% 100%;
        }

   
    }

    .map-tooltips-wrapper {
      background-image: url('@/assets/fullScreen/MonitoringOverview/map-tips.png');
    }
}



.filterFeature1 {
    color: #fff;
    cursor: pointer;
}


.chart-container-1 {
        width: 380px;
        margin-top: 10px;
        padding-top: 20px;

    .top-title {
        position: relative;
        font-size: 12px;
        margin-bottom: 15px;
        .num {
            position: absolute;
            right: 120px;
            top: -10px;
            font-size: 12px;
        }

        .num0 {
            width: 30px;
            display: inline-block;
            position: absolute;
            top: -10px;
        }

        .num01 {
            display: inline-block;
            position: absolute;
            top: -10px;
            left: 30px;
        }


        .num1 {
            position: absolute;
            right: 50px;
            top: -10px;
            font-size: 12px;
        }
        .num2 {
            position: absolute;
            right: -20px;
            top: -10px;
            font-size: 12px;
        }
    }
    .bar {
        display: flex;
        align-items: center;
        margin-bottom: 3px;
        position: relative;
    }
    .bar-label {
        width: 30px;
        height: 5px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        font-weight: bold;
    }


    .icon {
        width: 20px;
        height: 20px;
        background-color: #3D8FFF;
        border-radius: 5px 0 0 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin-right: 20px;
    }
    .icon::before {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 10px solid #3D8FFF;
        right: -10px;
    }

        .bar:first-child .icon {
        background-color: #FF3232;
    }

        .bar:first-child .icon::before {
        border-left: 10px solid #FF3232;
    }


        .bar:nth-child(2)   .icon {
        background-color: #FF9800;
    }

        .bar:nth-child(2)   .icon::before {
        border-left: 10px solid #FF9800;
    }


        .bar:nth-child(3)   .icon {
        background-color: #FFD200;
    }

        .bar:nth-child(3)   .icon::before {
        border-left: 10px solid #FFD200;
    }

    .icon span {
        color: white;
        font-size: 24px;
        font-weight: bold;
    }

    .icon .num-1 {
        font-size: 16px;
    }

    .bar-value {
        height: 5px;
        background-color: #1e3a5f;
        width: 100%;
        position: relative;
        border-radius: 5px;
        // overflow: hidden;
    }
    .bar-fill {
        height: 100%;
        background-color: #00aaff;
        width: 20px;
        transition: width 0.5s ease;
        border-radius: 5px;
    }
    .bar .num {
        position: absolute;
        right: 130px;
        top: -21px;
        font-size: 14px;
    }

    .bar .num1 {
        position: absolute;
        right: 60px;
        top: -21px;
        font-size: 14px;
    }



    .bar .num2 {
        position: absolute;
        right: -10px;
        top: -21px;
        font-size: 14px;
    }

    .bar-increase {
        position: absolute;
        right: -80px;
        top: -2px;
        color: #00aaff;
        font-size: 14px;
    }
    .nameValue {
        width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

}
    

.map-warper {
    width: 500px;
    height: 380px;
}


.btn-wraper {
    margin-top: -30px;
}
.chart-2 {
    position: relative;
    padding-top: 20px;
}

.chart-2 .back-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    z-index: 5px;
}