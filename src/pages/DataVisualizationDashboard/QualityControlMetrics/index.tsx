// 质控指标
import React from 'react';
import {
  useState,useRef,useEffect,
} from 'react';

import { geoJson } from '../GuizhouMap/china';

import { converPxToVH, converPxToVW } from '@/utils';
import PathogenDetectionRateAnalysis from './components/PathogenDetectionRateAnalysis';
import PathogenDetectionTimelinessRateAnalysis from './components/PathogenDetectionTimelinessRateAnalysis';
import ReportTimelinessAnalysis from './components/ReportTimelinessAnalysis';
import SampleSubmissionTimelinessRate from './components/SampleSubmissionTimelinessRate';
import './index.less';
import mapDefaultTexture from '@/assets/fullScreen/MonitoringOverview/mapDefaultTexture.png';
import mapEmphasisTexture from '@/assets/fullScreen/MonitoringOverview/mapEmphasisTexture.png';
import {
  qcIndexMap,
} from '@/api/visual/qualityControlMetrics';
import {
  Form,
  Select,
  Button,Input,Row,Col,
} from 'antd';
import {
EnvironmentOutlined,
} from '@ant-design/icons';

import useEcharts, { ECOption } from '@/hooks/useEcharts';
import { codeDefinition } from '@/constants';


// 登录绑定的表单Ref

// 新增引入 Option 组件
const { Option } = Select;



type TQualityControlMetricsProps = {};
const QualityControlMetrics: React.FC<TQualityControlMetricsProps> = () => {

  const [signInForm] = Form.useForm();
  // const [form] = Form.useForm();
  const [regionValue, setRegionValue] = useState<any>("");
  const [orgNameValue, setOrgNameValue] = useState<any>("");
  const [filterModalOpen, setFilterModalOpen] = useState(false);


  const [mapText, setMapText] = useState<any>("");


  

  const onGenderChange = (value: string) => {
    setRegionValue(value)
  };

  const formRef1 =useRef<any>(null);
  const formRef2 =useRef<any>(null);
  const formRef3 =useRef<any>(null);
  const formRef4 =useRef<any>(null);
  const [dataChartsSource, setDataChartsSource] = useState<any>([]);


  const onOrgNameChange = (event:any) => {
    setOrgNameValue(event.target.value)
  };

  const onFinish = (values: any) => {
  };

  const onReset = () => {
    // 重置表单字段
      signInForm.resetFields();
      // 重置 regionValue 状态
      setRegionValue("");
      // 重置 orgNameValue 状态
      setOrgNameValue("");
      // 调用子组件的 handleSubmit 方法刷新数据
      setTimeout(()=>{
      formRef1?.current?.handleSubmit();
      formRef2?.current?.handleSubmit();
      formRef3?.current?.handleSubmit();
      formRef4?.current?.handleSubmit();
      })

  };


  const onSearch = () => {
    formRef1?.current?.handleSubmit();
    formRef2?.current?.handleSubmit();
    formRef3?.current?.handleSubmit();
    formRef4?.current?.handleSubmit();
  };
  const [dataSource, setMapDataSource] = useState<any>([]);


  useEffect(() => {
    getMapDataSource();
  }, []);
  const [option, setOption] = useState<ECOption>({
    grid: {
      bottom: '8%',
    },
    tooltip: {
      trigger: 'item',
      showDelay: 0,
      transitionDuration: 0.2,
      padding: 0,
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderWidth: 0,
      formatter: (params: any) => {
        console.log(params, 11111);
        if(params.data && params.data?.timelyPercent) {
        return `<div 
            style="
                width: 174px; 
                min-height: 138px;
                position: relative;
                background: url('/fullScreen/map-tips.png');
                background-size: 100% 100%;
                padding: 8px 12px;
            "
        >
            <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
              params.name
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">样本数：${
              params.data?.sampleCount
             }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">及时送检数：${  
              params.data?.timelySampleCount
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">及时送检率：${ 
              params.data?.timelyPercent
            }</div>
        </div>`;
          } else {
                    return `<div 
            style="
                width: 174px; 
                min-height: 138px;
                position: relative;
                background: url('/fullScreen/map-tips.png');
                background-size: 100% 100%;
                padding: 8px 12px;
            "
        >
            <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
              params.name
            }</div>
        </div>`;
        
          }
      },
    },
    geo: [
      {
        map: 'Guizhou',
        roam: false,
        aspectScale: 0.85,
        zoom: 1.1,
        zlevel: -1,
        label: {
          show: false,
        },
        itemStyle: {
          shadowColor: '#01C2F6',
          shadowOffsetY: 12,
          shadowOffsetX: -4,
        },
      },
 
    ],
    series: [
      // 配置地图相关的数据参数
      {
        type: 'map',
        roam: false, // 不开启缩放和平移
        map: 'Guizhou',
        aspectScale: 0.85,
        zoom: 1.1, // 当前视角的缩放比例
        zlevel: 1,
        itemStyle: {
          areaColor: {
            image: mapDefaultTexture,
            repeat: 'repeat',
          },
          borderColor: '#B3F6FF',
          borderWidth: 2,
        },
        label: {
          show: true,
          color: 'white',
          fontWeight: 'bold',
          fontSize: 16,
        },
        emphasis: {
          itemStyle: {
            areaColor: {
              image: mapEmphasisTexture,
              repeat: 'no-repeat',
            },
            borderColor: '#B3F6FF',
            borderWidth: 3,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            shadowColor: 'white',
            shadowBlur: 4,
          },
          label: {
            show: true,
            color: 'white',
            fontWeight: 'bold',
            fontSize: 16,
          },
        },
        select: {
          disabled: true,
        },
        data: dataSource,
      },
    ],
  });


  const [echarts] = useEcharts();

  const cRef = useRef<HTMLDivElement>(null);

  const cInstance = useRef<any>();
  // 手动刷新函数
  const refreshChart = () => {
    if (cInstance.current) {
      // 销毁现有的 ECharts 实例
      cInstance.current.dispose();
      cInstance.current = null;
    }
    // 重新初始化图表
    initChart();
  };
  const initChart = () => {
    try {
      if (cRef.current) {
        // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
        cInstance.current = echarts.getInstanceByDom(
          cRef.current
        ) as unknown as any | undefined;

        if (!cInstance.current) {
          cInstance.current = echarts.init(cRef.current, undefined, {
            //   renderer: 'svg',
          }) as unknown as any;
        }

        // 设置配置项
        if (option)
          cInstance.current?.setOption({
            grid: {
              bottom: '8%',
            },
                tooltip: {
      trigger: 'item',
      showDelay: 0,
      transitionDuration: 0.2,
      padding: 0,
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderWidth: 0,
      formatter: (params: any) => {
             console.log(params, 11111);
        if(params.data && params.data?.timelyPercent) {
        return `<div 
            style="
                width: 174px; 
                min-height: 138px;
                position: relative;
                background: url('/fullScreen/map-tips.png');
                background-size: 100% 100%;
                padding: 8px 12px;
            "
        >
            <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
              params.name
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">样本数：${
              params.data?.sampleCount
             }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">及时送检数：${  
              params.data?.timelySampleCount
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">及时送检率：${ 
              params.data?.timelyPercent
            }</div>
        </div>`;
          } else {
                    return `<div 
            style="
                width: 174px; 
                min-height: 138px;
                position: relative;
                background: url('/fullScreen/map-tips.png');
                background-size: 100% 100%;
                padding: 8px 12px;
            "
        >
            <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
              params.name
            }</div>
        </div>`;
        
          }
      },
    },


            visualMap: {
                show: true,
                type: "piecewise",
                min: 0,
                max: 100,
                showLabel: false, //展示字
                itemGap: 2,
                itemWidth: 25,
                itemHeight: 8,
                inRange: {
                    color: [
                        "#061551",
                        "#0C239F",
                        "#005AE1",
                        "#62A8FF",
                        "#AEDBFF",
                    ],
                },
                bottom: "1%",
                right: "20%",
            },

            geo: [
              {
                map: 'Guizhou',
                roam: false,
                aspectScale: 0.85,
                zoom: 1.1,
                zlevel: -1,
                label: {
                  show: false,
                },
                itemStyle: {
                  shadowColor: '#01C2F6',
                  shadowOffsetY: 12,
                  shadowOffsetX: -4,
                },
              },
              {
                map: 'Guizhou',
                roam: false,
                aspectScale: 0.85,
                zoom: 1.1,
                zlevel: -2,
                label: {
                  show: false,
                },
                itemStyle: {
                  shadowColor: '#014A9F',
                  shadowOffsetY: 24,
                  shadowOffsetX: -6,
                },
              },
            ],

            series: [
              // 配置地图相关的数据参数
              {
                type: 'map',
                roam: false, // 不开启缩放和平移
                map: 'Guizhou',
                aspectScale: 0.85,
                zoom: 1.1, // 当前视角的缩放比例
                zlevel: 1,
                itemStyle: {
                  areaColor: '#066C96',
                  borderColor: '#B3F6FF',
                  borderWidth: 2,
                },
                label: {
                  show: true,
                  color: 'white',
                  fontSize: 14,

                formatter: (params: any) => {
                  if(params.data && params.data?.timelyPercent) {
                  if (params?.data?.timelyPercent.replace("%","") *1 > 90 ) {
                       return ` ${params.data.name}`;
                  }  else  if (params?.data?.timelyPercent.replace("%","") *1 < 90 &&
                  params?.data?.timelyPercent.replace("%","") *1 > 60
                 ) {
                       return ` ${params.data.name}`;
                  } else  if (params?.data?.timelyPercent.replace("%","") *1  < 60) {
                       return ` ${params.data.name}`;
                  } 
                }
                },

                },
                emphasis: {
                  itemStyle: {
                    areaColor: '#066C96',
                    borderColor: '#B3F6FF',
                    borderWidth: 3,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    shadowColor: 'white',
                    shadowBlur: 1,
                  },
                  label: {
                    show: true,
                    color: 'white',
                    fontSize: 14,
                  },
                },
                select: {
                  disabled: true,
                },
                data: dataSource,
              },
            ],

          });
      }
    } catch (error) {
      console.log(error, 'initChart');

      cInstance.current?.dispose();
    }
  };

  const getMapDataSource = () => {
    qcIndexMap({
      orgName: orgNameValue,
      region: regionValue,
    }).then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        
        const resData = res.data.map((e:any)=> {
          let obj:any = {};
          obj= {
            ...e,
            name:e.region,
            value:e.timelyPercent.replace("%","") *1,
          }
          return obj;
        })

        setMapDataSource(resData);
        setDataChartsSource(res.data);
        initChart()
      } else {
        // message.error(res.msg);
      }
    });
  };





  useEffect(() => {
    if (dataSource?.length) {
      
      echarts.registerMap('Guizhou', { geoJSON: geoJson } as any);
      initChart();
      // setIsMapShow(false)
      setTimeout(()=> {
        setMapText("加载中")
        refreshChart()
      }, 1500)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, cRef]);



  const handleFilterModalClose = () => {
    setFilterModalOpen(false);
  };



  return (
    <div
      className="w-full flex flex-col gap-[1.851851vh] overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      <Form form={signInForm} name="control-hooks"  className="form-wraper" onFinish={onFinish}>
        <Row gutter={[16, 16]}>
        <Col span={4}>
        <Form.Item name="region" label="区域" >
          <Select
            placeholder="选择区域"
            onChange={onGenderChange}
            allowClear
            size="small" 
            value={regionValue}>
            <Option value="贵阳市">贵阳市</Option>
            <Option value="遵义市">遵义市</Option>
            <Option value="六盘水市">六盘水市</Option>
            <Option value="安顺市">安顺市</Option>
            <Option value="铜仁市">铜仁市</Option>
            <Option value="毕节市">毕节市</Option>
            <Option value="黔南苗族布依族自治州">黔南苗族布依族自治州</Option>
            <Option value="黔东南苗族侗族自治州">黔东南苗族侗族自治州</Option>
            <Option value="黔西南布依族苗族自治州">黔西南布依族苗族自治州</Option>
          </Select>
        </Form.Item>
        </Col>
        <Col span={4}>
        <Form.Item name="机构" label="机构" >
            <Input size="small" value={orgNameValue}  onChange={onOrgNameChange}/>
        </Form.Item>
        </Col>
        <Col span={4}>
        <Form.Item >
          <Button type="primary" htmlType="submit"   onClick={onSearch} size="small" className='mr-2'>
            搜索
          </Button>
          <Button htmlType="button" onClick={onReset} size="small">
            重置
          </Button>
        </Form.Item>
        </Col>
        <div
            className=" filterFeature1"
            onClick={() => {
              setFilterModalOpen(true);

              if (dataSource?.length) {
                echarts.registerMap('Guizhou', { geoJSON: geoJson } as any);
                initChart();
                // setIsMapShow(false)
                setTimeout(()=> {
                  setMapText("加载中")
                  refreshChart()
                }, 300)
              }

            }}
          >
            <EnvironmentOutlined />
            质控指标地图
          </div>
        </Row>
      </Form>

        

      <div className="w-full h-full flex-1 flex flex-row flex-nowrap justify-between items-center gap-[4.84375vw]">
        <div className="w-full h-full flex-1">
          <SampleSubmissionTimelinessRate 
          onRef={formRef1}
          regionValue={regionValue} 
          orgNameValue={orgNameValue} 
          />
        </div>
        <div className="w-full h-full flex-1">
          <PathogenDetectionTimelinessRateAnalysis regionValue={regionValue} 
          onRef={formRef2}

          orgNameValue={orgNameValue} />
        </div>
      </div>
      <div className="w-full h-full flex-1 flex flex-row flex-nowrap justify-between items-center gap-[4.84375vw]">
        <div className="w-full h-full flex-1">
          <PathogenDetectionRateAnalysis 
          onRef={formRef3}
          regionValue={regionValue} 
          orgNameValue={orgNameValue}  />
        </div>
        <div className="w-full h-full flex-1">
          <ReportTimelinessAnalysis 
          onRef={formRef4}
          regionValue={regionValue} 
          orgNameValue={orgNameValue} />
        </div>
      </div>

      <div
        className="businessMetricsBox1 absolute h-full w-full top-0 left-0 bg-[rgba(0,0,0,0.5)] flex justify-center transition-all"
        style={{
          height: filterModalOpen ? '100%' : '0',
        }}
      >
        {filterModalOpen ? (
          <div className="filterModal py-3 flex flex-col pb-8">
            <div className="h-[53px] flex-shrink-0 flex items-center justify-between px-4 text-white font-bold text-xl">
              <span
                onClick={handleFilterModalClose}
                className=" cursor-pointer"
              >
              </span>
            </div>
            <div className=" flex-1 p-6">
              <div className="w-full h-full d-flex flex-col overflow-y-auto">

                <div className='w-50  h-full r-bg'>
                    <div className='title'>
                      质控指标地图</div>
                      <div className='tips'>按及时送检率由上到下分布</div>
                    <div   ref={cRef}  className="w-full h-full map-warper ">{mapText}</div>
                </div>
                <div className='w-50  h-full'>
                  <div className='title'>
                      地区样本及送检率排行</div>

                  <div className="chart-container-1">

                    <div  className='top-title'>
                      <span className='num0'>#</span>
                      <span className='num01'>地区</span>
                      <span className='num'>样本数</span>
                      <span className='num1'>及时送检数</span>
                      <span className='num2'>及时送检率</span>
                    </div>

                    {dataChartsSource.map((item:any, index:number) => (
                      <div key={index} className="bar">
                          <div className="icon"><span className='num-1'>{index + 1}</span>
                          </div>
                          <div className='nameValue'>{item.region}</div>
             
                          <div className="bar-value">
                            <div className='bar-fill' style={{ width: `${item.timelyPercent }` }}></div>
                            <span className='num'>{item.sampleCount}</span>
                            <span className='num1'>{item.timelySampleCount}</span>
                            <span className='num2'>{item.timelyPercent}</span>
                          </div>
               
                      </div>
                    ))}
                  </div>
                </div>



              </div>
            </div>
            <div className="btn-wraper flex-shrink-0 h-12 px-4 flex items-center justify-center gap-4">
              <Button
                type="primary"
                onClick={() => {
                  handleFilterModalClose();
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        ) : null}
      </div>

    </div>
  );
};

export default QualityControlMetrics;
