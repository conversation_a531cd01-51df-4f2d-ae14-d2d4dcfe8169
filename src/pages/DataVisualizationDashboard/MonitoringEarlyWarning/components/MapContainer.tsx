/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getMapAreaData } from '@/api/visual/monitorEarlyWarning';
import mapDefaultTexture from '@/assets/fullScreen/MonitoringOverview/mapDefaultTexture.png';
import mapEmphasisTexture from '@/assets/fullScreen/MonitoringOverview/mapEmphasisTexture.png';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import { EChartsType } from 'echarts';
import useEcharts, { ECOption } from '@/hooks/useEcharts';
import { geoJson } from '../../GuizhouMap/china';

type TMapContainerProps = {
  id: string;
  handleSearch: (val?: number) => void;
};

const MapContainer: React.FC<TMapContainerProps> = ({ id, handleSearch }) => {
  const [selectName, setSelectName] = useState('');
  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);

  const [option, setOption] = useState<ECOption>({
    grid: {
      bottom: '8%',
    },
    tooltip: {
      trigger: 'item',
      showDelay: 0,
      transitionDuration: 0.2,
      padding: 0,
      backgroundColor: 'rgba(0, 0, 0, 0)',
      borderWidth: 0,
      formatter: (params: any) => {
        return `<div 
            style="
                width: 174px; 
                min-height: 138px;
                position: relative;
                background: url('/fullScreen/map-tips.png');
                background-size: 100% 100%;
                padding: 8px 12px;
            "
        >
            <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
              params.name
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">采集样本数：${
              dataSource?.find((_i: any) => _i?.cityName === params?.name)
                ?.sampleCount || 0
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">检测完成数：${
              dataSource?.find((_i: any) => _i?.cityName === params?.name)
                ?.detectionOverCount || 0
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">菌（毒）株数量：${
              dataSource?.find((_i: any) => _i?.cityName === params?.name)
                ?.strainsCount || 0
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">复核符合数：${
              dataSource?.find((_i: any) => _i?.cityName === params?.name)
                ?.recheckSuccessCount || 0
            }</div>
            <div style=" fontSize: 12; fontWeight: 500; color: white">复核不符合数：${
              dataSource?.find((_i: any) => _i?.cityName === params?.name)
                ?.recheckFailCount || 0
            }</div>
        </div>`;
      },
    },
    geo: [
      {
        map: 'Guizhou',
        roam: false,
        aspectScale: 0.85,
        zoom: 1.1,
        zlevel: -1,
        label: {
          show: false,
        },
        itemStyle: {
          shadowColor: '#01C2F6',
          shadowOffsetY: 12,
          shadowOffsetX: -4,
        },
      },
      {
        map: 'Guizhou',
        roam: false,
        aspectScale: 0.85,
        zoom: 1.1,
        zlevel: -2,
        label: {
          show: false,
        },
        itemStyle: {
          shadowColor: '#014A9F',
          shadowOffsetY: 24,
          shadowOffsetX: -6,
        },
      },
    ],
    series: [
      // 配置地图相关的数据参数
      {
        type: 'map',
        roam: false, // 不开启缩放和平移
        map: 'Guizhou',
        aspectScale: 0.85,
        zoom: 1.1, // 当前视角的缩放比例
        zlevel: 1,
        itemStyle: {
          areaColor: {
            image: mapDefaultTexture,
            repeat: 'repeat',
          },
          borderColor: '#B3F6FF',
          borderWidth: 2,
        },
        label: {
          show: true,
          color: 'white',
          fontWeight: 'bold',
          fontSize: 16,
        },
        emphasis: {
          itemStyle: {
            areaColor: {
              image: mapEmphasisTexture,
              repeat: 'no-repeat',
            },
            borderColor: '#B3F6FF',
            borderWidth: 3,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            shadowColor: 'white',
            shadowBlur: 4,
          },
          label: {
            show: true,
            color: 'white',
            fontWeight: 'bold',
            fontSize: 16,
          },
        },
        select: {
          disabled: true,
        },
        data: [],
      },
    ],
  });
  const [echarts] = useEcharts();

  const cRef = useRef<HTMLDivElement>(null);

  const cInstance = useRef<EChartsType>();

  const initChart = () => {
    try {
      if (cRef.current) {
        // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
        cInstance.current = echarts.getInstanceByDom(
          cRef.current
        ) as unknown as EChartsType | undefined;

        if (!cInstance.current) {
          cInstance.current = echarts.init(cRef.current, undefined, {
            //   renderer: 'svg',
          }) as unknown as EChartsType;
        }

        // 设置配置项
        if (option) {
          cInstance.current?.setOption({
            grid: {
              bottom: '8%',
            },
            tooltip: {
              trigger: 'item',
              showDelay: 0,
              transitionDuration: 0.2,
              padding: 0,
              backgroundColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0,
              formatter: (params: any) => {
                return `<div 
                  class="map-tooltips-wrapper"
                  style="
                      width: 174px; 
                      min-height: 138px;
                      position: relative;
                      background-size: 100% 100%;
                      padding: 8px 12px;
                  "
              >
                  <div style=" fontSize: 16; fontWeight: 500; color: #FFB368">${
                    params.name
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">病原阳性预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.pathogensPositive || 0
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">急性病原预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.acutePathogens || 0
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">高致病性病原预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.highPathogens || 0
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">新发病原预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.newPathogens || 0
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">突发病原预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.emergencyPathogens || 0
                  }</div>
                  <div style=" fontSize: 12; fontWeight: 500; color: white">输入性病原预警数：${
                    dataSource?.find((_i: any) => _i?.cityName === params?.name)
                      ?.inputPathogens || 0
                  }</div>
              </div>`;
              },
            },
            geo: [
              {
                map: 'Guizhou',
                roam: false,
                aspectScale: 0.85,
                zoom: 1.1,
                zlevel: -1,
                label: {
                  show: false,
                },
                itemStyle: {
                  shadowColor: '#01C2F6',
                  shadowOffsetY: 12,
                  shadowOffsetX: -4,
                },
              },
              {
                map: 'Guizhou',
                roam: false,
                aspectScale: 0.85,
                zoom: 1.1,
                zlevel: -2,
                label: {
                  show: false,
                },
                itemStyle: {
                  shadowColor: '#014A9F',
                  shadowOffsetY: 24,
                  shadowOffsetX: -6,
                },
              },
            ],
            series: [
              // 配置地图相关的数据参数
              {
                type: 'map',
                roam: false, // 不开启缩放和平移
                map: 'Guizhou',
                aspectScale: 0.85,
                zoom: 1.1, // 当前视角的缩放比例
                zlevel: 1,
                itemStyle: {
                  areaColor: {
                    image: mapDefaultTexture,
                    repeat: 'repeat',
                  },
                  borderColor: '#B3F6FF',
                  borderWidth: 2,
                },
                label: {
                  show: true,
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: 16,
                },
                emphasis: {
                  itemStyle: {
                    areaColor: {
                      image: mapEmphasisTexture,
                      repeat: 'no-repeat',
                    },
                    borderColor: '#B3F6FF',
                    borderWidth: 3,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    shadowColor: 'white',
                    shadowBlur: 4,
                  },
                  label: {
                    show: true,
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: 16,
                  },
                },
                select: {
                  disabled: true,
                },
                data: [],
              },
            ],
          });
        }
      }
    } catch (error) {
      console.log(error, 'initChart');

      cInstance.current?.dispose();
    }
  };

  /**
   * 获取地图区域数据
   */
  const queryMapAreaData = async () => {
    try {
      const { code, data, msg } = await getMapAreaData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDataSource(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryMapAreaData();
  }, []);

  useEffect(() => {
    if (dataSource?.length) {
      echarts.registerMap('Guizhou', { geoJSON: geoJson } as any);
      initChart();
    }
  }, [dataSource]);

  // 重新适配大小并开启过渡动画
  const resize = () => {
    cInstance.current?.resize({
      animation: { duration: 300 },
    });
  };
  const mapSize = useSize(cRef);

  useEffect(() => {
    resize();
  }, [mapSize]);

  // 监听事件
  // useEffect(() => {
  //   if (cInstance && cInstance.current) {
  //     cInstance.current.on('click', 'series', function (params: any) {
  //       let code: number | undefined = undefined;
  //       if (params.name !== selectName) {
  //         code = geoJson.features.find(
  //           (item) => item.properties.name === params.name
  //         )?.properties.adcode!;
  //         cInstance.current?.dispatchAction({
  //           type: 'select',
  //           name: params.name,
  //         });
  //       } else {
  //         cInstance.current?.dispatchAction({
  //           type: 'unselect',
  //           name: params.name,
  //         });
  //       }

  //       setSelectName(!code ? '' : params.name);
  //       handleSearch(code);
  //     });
  //   }
  // }, [cInstance, selectName, handleSearch]);

  return <div ref={cRef} id={id} className="w-full h-full "></div>;
};

export default MapContainer;
