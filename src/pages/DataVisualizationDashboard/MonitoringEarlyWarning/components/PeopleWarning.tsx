import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, message } from 'antd';
import { peopleWarningApi,peopleFeatureCharts } from '@/api/visual/monitorEarlyWarning';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import * as echarts from 'echarts';
import ChartsWrapper from '@/components/ChartsWrapper';

interface IDataSource {
  id: string;
  warnDate: string;
  etiologyId: number;
  etiologyName: null;
  warnPeople: string;
  warnPeopleLab: string;
  warnInfo: string;
}

const PeopleWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IDataSource[]>([]);

  const getDataSource = () => {
    setLoading(true);
    peopleWarningApi()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.warnDate}</span>
            <span>{_item.warnPeopleLab}</span>
            <span>{_item.etiologyName}</span>
            <span className=" w-full truncate" title={_item.warnInfo}>
              {_item.warnInfo}
            </span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);


  const [active1, setActive1] = useState<any>(2);

  // 点击处理函数，根据传入的值更新 active1
  const handleLinkClick = (value: number) => {
    setActive1(value);
  };



  const [options, setOptions] = useState<Record<string, any>>();
  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await peopleFeatureCharts();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

       setOptions({
           tooltip: {
             trigger: 'axis',
             axisPointer: {
               type: 'shadow',
             },
           },
             legend: {
               top: 10,
               left: 'center',
               itemWidth: 10,
               itemHeight: 10,
               padding: [5, 10],
               textStyle: {
                 fontSize: 12,
                 color: '#96A4F4',
                 padding: [3, 0, 0, 0],
               },
               data:['贵阳', '黔南', '黔东南','黔西南', '毕节', '铜仁', '安顺', '遵义', '六盘水'],
             },


      // 添加垂直方向的 dataZoom 组件
      dataZoom: [
        {
          type: 'slider',
          yAxisIndex: 0,
          start: 0,
          end: 50,
          // handleSize: '100%',

            handleIcon: "path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z",
            handleSize: "6",
            height: '100%',
            width: 3,
            backgroundColor: "rgba(0,210,255,.2)",
            textStyle: {
                color: "rgba(0,0,0,0)",
            },
            borderColor: "rgba(0,0,0,0)",
            fillerColor: "rgba(0,210,255,.3)",
        },
        {
          type: 'inside',
          yAxisIndex: 0,
          zoomOnMouseWheel: false,
          moveOnMouseMove: true,
          moveOnMouseWheel: true,
        },
      ],




           grid: {
             left: '20%',
             right: '8%',
             bottom: '0%',
           },
           xAxis: {
             type: 'value',
             boundaryGap: [0, 0.01],
             splitLine: {
               lineStyle: {
                 color: ['#FFFFFF'],
                 opacity: 0.25,
               },
             },
           },
           yAxis: {
             type: 'category',
             data: data?.map((_item: Record<string, any>) => _item.peopleRange),
             splitLine: {
               show: false,
             },
             axisTick: {
               show: false,
             },
             axisLabel: {
               color: 'white',
               interval: 0,
               fontSize: 10,
               width: 120,
             },
           },
           series: [


             {
               name: '贵阳',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.guiyangshi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                      label: {
                        show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
                        position: 'insideRight',
                        color: '#fff',
                        fontSize: 10,
                        formatter: (e:any) => {
                                      console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#1AF3C2',
               },
  
             },
             {
               name: '黔南',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.qiannanzhou),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#02A6EF',
               },
             },
             {
               name: '黔东南',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.qiandongnan),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#E0E98E',
               },
             },





             {
               name: '黔东南',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.qiandongnan),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#95F204',
               },
             },


            {
               name: '毕节',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.bijieshi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10,
             formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#F9BD63',
               },
             },


                         {
               name: '黔西南',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.qianxinan),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#19A2FF',
               },
             },



              {
               name: '黔东南',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.qiandongnan),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                
                              label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10,
             formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#8400FF',
               },
             },


            {
               name: '铜仁',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.tongrenshi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                                            label: {
                                      show: (params: any) => {
                    console.log('params:', params); // 打印 params 信息用于调试
                    const value = Number(params.data); // 确保获取的数值为数字类型
                    return value !== 0; 
                  },
                        position: 'insideRight',
                        color: '#fff',
                        fontSize: 10,
              formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
                      },
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#CC77C2',
               },
             },


            {
               name: '安顺',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.anshunshi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                label: {
              show: (params: any) => {
                    console.log('params:', params); // 打印 params 信息用于调试
                    const value = Number(params.data); // 确保获取的数值为数字类型
                    return value !== 0; 
                  },
                  position: 'insideRight',
                  color: '#fff',
                  fontSize: 10,
             formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
                },
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#30BD63',
               },
             },







            {
               name: '遵义',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.zunyishi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
                                               label: {
                                      show: (params: any) => {
                    console.log('params:', params); // 打印 params 信息用于调试
                    const value = Number(params.data); // 确保获取的数值为数字类型
                    return value !== 0; 
                  },
                        position: 'insideRight',
                        color: '#fff',
                        fontSize: 10,
                       formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
                      },
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#C075CD',
               },
             },

            {
               name: '六盘水',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.liupanshuishi),
               barMaxWidth: 28,
                       // 在每个柱子上显示数值
            
                              label: {
                                      show: (params: any) => {
                    console.log('params:', params); // 打印 params 信息用于调试
                    const value = Number(params.data); // 确保获取的数值为数字类型
                    return value !== 0; 
                  },
                        position: 'insideRight',
                        color: '#fff',
                        fontSize: 10,
                   formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
                      },

               
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#D42C85',
               },
             },

             



           ],
         });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);


  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle
        title="人群态势预警"
        extra={
          <div className=" bg-[rgba(19,133,239,0.2)] rounded text-white px-2 h-auto  d-flex">
                <div
                className={classNames('link-1', { 'link-active': active1 === 1 })}
                onClick={() => handleLinkClick(1)}
                style={{ cursor: 'pointer' }}
              >
                表格
              </div>
              {/* 为表格按钮添加点击事件，点击时将 active1 设置为 2 */}
              <div
                className={classNames('link-2', { 'link-active': active1 === 2 })}
                onClick={() => handleLinkClick(2)}
                style={{ cursor: 'pointer' }}
              >
                图表
                </div>
                预警次数
            <span className=" text-lg leading-5">{dataSource.length}</span>次
          </div>
        }
      />
      {active1==1 && (
        <div>
          <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
            <span className="text-center">序号</span>
            <span>预警日期</span>
            <span>人群</span>
            <span>病原体</span>
            <span>预警内容</span>
          </div>
          <div className=" flex-1">
            {loading ? (
              <div className="w-full h-full">
                {/* <Loading>Loading...</Loading> */}
              </div>
            ) : dataSource.length ? (
              <div
                className="w-full bg-[rgba(8,24,48,0.52)]"
                style={{ height: slideParams.parentHeight }}
              >
                <SeamlessScroll
                  swiperSlideClassname="!w-full !h-[40px]"
                  direction="vertical"
                  spaceBetween={0}
                  slidesPerView={slideParams.slidesPerView}
                  children={createScrollDom()}
                />
              </div>
            ) : null}
          </div>
        </div>
      )}

    {active1==2 && (
      <div className="w-full flex-1">
        <ChartsWrapper option={options as any} />
      </div>
      )}


    </div>
  );
};

export default PeopleWarning;
