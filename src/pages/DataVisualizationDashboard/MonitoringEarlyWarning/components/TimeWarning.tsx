import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, message } from 'antd';
import { timeFeature,timeFeatureCharts } from '@/api/visual/monitorEarlyWarning';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import ChartsWrapper from '@/components/ChartsWrapper';







interface IDataSource {
  id: string;
  warnDate: string;
  etiologyId: number;
  etiologyName: null;
  warnAge: number;
  warnAgeLab: string;
  warnInfo: string;
}

const PathogenWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IDataSource[]>([]);

  const getDataSource = () => {
    setLoading(true);
    timeFeature()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item:any, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_1fr_2fr_2fr_3fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span className=" w-full truncate" title={_item.warnDate}>{_item.warnDate}</span>
            <span>{_item.season}</span>
            <span>{_item.infect}</span>
            <span>{_item.etiology}</span>
            <span className=" w-full truncate" title={_item.info}>{_item.info}</span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);


  const [options, setOptions] = useState<Record<string, any>>();
  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await timeFeatureCharts();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

       setOptions({
            color: ['rgba(18,118,255)', 'rgba(255,144,45)', '#12D2DE'],
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            textStyle: {
                color: 'white'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: data?.map((_item: Record<string, any>) => _item.timeRange),
                axisLabel: {
                    rotate: 60,
                    color: 'white'
                },
            }
        ],
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    color: 'white'
                },
                splitLine: {
                    lineStyle: {
                        color: ['#FFFFFF'],
                        opacity: 0.25,
                    },
                },
            },
            {
                type: 'value',
                axisLabel: {
                    color: 'white',
                    formatter: '{value} %',
                },
            },
        ],
        series: [
            {
                name: '检测样本数（份）',
                type: 'bar',
                data: data?.map((_item: Record<string, any>) => _item.sampleCount),
                barMaxWidth: 18,
            },
            {
                name: '阳性样本数（份）',
                type: 'bar',
                data:data?.map((_item: Record<string, any>) => _item.positiveCount),
                barMaxWidth: 18,
            },
            {
                name: '阳性率',
                type: 'line',
                 yAxisIndex: 1, 
                data:data?.map((_item: Record<string, any>) => _item.positiveRate),
                tooltip: {
                  valueFormatter: (value:any) => `${value}%`
                }
            },
        ]
         });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);


  const [active1, setActive1] = useState<any>(2);

  // 点击处理函数，根据传入的值更新 active1
  const handleLinkClick = (value: number) => {
    setActive1(value);
  };




  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle
        title="时间特征预警"
        extra={
          <div className=" bg-[rgba(19,133,239,0.2)] rounded text-white px-2 h-auto   d-flex">

                <div
                className={classNames('link-1', { 'link-active': active1 === 1 })}
                onClick={() => handleLinkClick(1)}
                style={{ cursor: 'pointer' }}
              >
                表格
              </div>
              {/* 为表格按钮添加点击事件，点击时将 active1 设置为 2 */}
              <div
                className={classNames('link-2', { 'link-active': active1 === 2 })}
                onClick={() => handleLinkClick(2)}
                style={{ cursor: 'pointer' }}
              >
                图表
                </div>


            预警次数
            <span className=" text-lg leading-5">{dataSource.length}</span>次
          </div>
        }
      />
      {active1==1 && (

        <div>

        <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_1fr_2fr_2fr_3fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
          <span className="text-center">序号</span>
          <span>预警时间</span>
          <span>季节</span>
          <span>传染病类别</span>
          <span>病原名称</span>
          <span>预警内容</span>
        </div>
        <div className=" flex-1">
          {loading ? (
            <div className="w-full h-full">
              {/* <Loading>Loading...</Loading> */}
            </div>
          ) : dataSource.length ? (
            <div
              className="w-full bg-[rgba(8,24,48,0.52)]"
              style={{ height: slideParams.parentHeight }}
            >
              <SeamlessScroll
                swiperSlideClassname="!w-full !h-[40px]"
                direction="vertical"
                spaceBetween={0}
                slidesPerView={slideParams.slidesPerView}
                children={createScrollDom()}
              />
            </div>
          ) : null}
        </div>

        </div>
        )}


    {active1==2 && (
      <div className="w-full flex-1">
        <ChartsWrapper option={options as any} />
      </div>
      )}


    </div>
  );
};

export default PathogenWarning;
