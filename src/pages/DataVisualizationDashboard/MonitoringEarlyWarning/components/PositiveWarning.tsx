import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import { IPositiveWarningDataSource } from '../type';

type TPositiveWarningProps = {
  loading: boolean;
  dataSource: IPositiveWarningDataSource[];
};

const PositiveWarning: React.FC<TPositiveWarningProps> = ({
  loading,
  dataSource,
}) => {
  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr_2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.warnDate}</span>
            <span>{_item.sampleCode}</span>
            <span>{_item.sampleName}</span>
            <span>{_item.etiologyName}</span>
            <span>{_item.detectionDate}</span>
            <span>{_item.strainCode}</span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);

  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle title="病原阳性预警" size="big" />
      <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr_2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
        <span className="text-center">序号</span>
        <span>预警时间</span>
        <span>样本编号</span>
        <span>样本名称</span>
        <span>病原体</span>
        <span>监测日期</span>
        {/* <span>菌（毒）株编号</span> */}
      </div>
      <div className=" flex-1">
        {loading ? (
          <div className="w-full h-full">
            {/* <Loading>Loading...</Loading> */}
          </div>
        ) : dataSource.length ? (
          <div
            className="w-full bg-[rgba(8,24,48,0.52)]"
            style={{ height: slideParams.parentHeight }}
          >
            <SeamlessScroll
              swiperSlideClassname="!w-full !h-[40px]"
              direction="vertical"
              spaceBetween={0}
              slidesPerView={slideParams.slidesPerView}
              children={createScrollDom()}
            />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default PositiveWarning;
