import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, message } from 'antd';
import { ageWarningApi,ageFeatureCharts } from '@/api/visual/monitorEarlyWarning';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import ChartsWrapper from '@/components/ChartsWrapper';

interface IDataSource {
  id: string;
  warnDate: string;
  etiologyId: number;
  etiologyName: null;
  warnAge: number;
  warnAgeLab: string;
  warnInfo: string;
}

const PathogenWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IDataSource[]>([]);

  const getDataSource = () => {
    setLoading(true);
    ageWarningApi()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource();
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.warnDate}</span>
            <span>{_item.warnAgeLab}</span>
            <span>{_item.etiologyName}</span>
            <span className=" w-full truncate" title={_item.warnInfo}>
              {_item.warnInfo}
            </span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);


  const [options, setOptions] = useState<Record<string, any>>();
  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await ageFeatureCharts();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

       setOptions({
           tooltip: {
             trigger: 'axis',
             axisPointer: {
               type: 'shadow',
             },
           },
             legend: {
               top: 10,
               left: 'center',
               itemWidth: 10,
               itemHeight: 10,
               padding: [5, 10],
               textStyle: {
                 fontSize: 12,
                 color: '#96A4F4',
                 padding: [3, 0, 0, 0],
               },
               data:['男', '女',],
             },






           grid: {
             left: '25%',
             right: '8%',
             bottom: '0%',
           },
           xAxis: {
             type: 'value',
             boundaryGap: [0, 0.01],
             splitLine: {
               lineStyle: {
                 color: ['#FFFFFF'],
                 opacity: 0.25,
               },
             },
           },
           yAxis: {
             type: 'category',
             data: data?.map((_item: Record<string, any>) => _item.ageRange),
             splitLine: {
               show: false,
             },
             axisTick: {
               show: false,
             },
             axisLabel: {
               color: 'white',
               interval: 0,
               fontSize: 10,
               width: 120,
             },
           },
           series: [


             {
               name: '女',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.womanWarnCount),
               barMaxWidth: 28,
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#C075CD',
               },
                   label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

             },
             {
               name: '男',
               stack: '人群分布',
               type: 'bar',
               data: data?.map((_item: Record<string, any>) => _item.manWarnCount),
               barMaxWidth: 28,
               itemStyle: {
                 borderRadius: [0, 4, 4, 0],
                 color: '#02A6EF',
               },

                   label: {
            show: (params: any) => params.data !== 0, // 数值不为 0 时显示 label
            position: 'insideRight',
            color: '#fff',
            fontSize: 10, formatter: (e:any) => {
                          console.log(e.data, e.data!=0);
                          
                            if (e.data!=0) {
                                return e.data;
                            } else {
                            return "";
                            }

                        },
          },

             },
          

           ],
         });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, []);


  const [active1, setActive1] = useState<any>(2);

  // 点击处理函数，根据传入的值更新 active1
  const handleLinkClick = (value: number) => {
    setActive1(value);
  };




  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle
        title="年龄态势预警"
        extra={
          <div className=" bg-[rgba(19,133,239,0.2)] rounded text-white px-2 h-auto   d-flex">

                <div
                className={classNames('link-1', { 'link-active': active1 === 1 })}
                onClick={() => handleLinkClick(1)}
                style={{ cursor: 'pointer' }}
              >
                表格
              </div>
              {/* 为表格按钮添加点击事件，点击时将 active1 设置为 2 */}
              <div
                className={classNames('link-2', { 'link-active': active1 === 2 })}
                onClick={() => handleLinkClick(2)}
                style={{ cursor: 'pointer' }}
              >
                图表
                </div>


            预警次数
            <span className=" text-lg leading-5">{dataSource.length}</span>次
          </div>
        }
      />
      {active1==1 && (

        <div>

        <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
          <span className="text-center">序号</span>
          <span>预警日期</span>
          <span>年龄</span>
          <span>病原体</span>
          <span>预警内容</span>
        </div>
        <div className=" flex-1">
          {loading ? (
            <div className="w-full h-full">
              {/* <Loading>Loading...</Loading> */}
            </div>
          ) : dataSource.length ? (
            <div
              className="w-full bg-[rgba(8,24,48,0.52)]"
              style={{ height: slideParams.parentHeight }}
            >
              <SeamlessScroll
                swiperSlideClassname="!w-full !h-[40px]"
                direction="vertical"
                spaceBetween={0}
                slidesPerView={slideParams.slidesPerView}
                children={createScrollDom()}
              />
            </div>
          ) : null}
        </div>

        </div>
        )}


    {active1==2 && (
      <div className="w-full flex-1">
        <ChartsWrapper option={options as any} />
      </div>
      )}


    </div>
  );
};

export default PathogenWarning;
