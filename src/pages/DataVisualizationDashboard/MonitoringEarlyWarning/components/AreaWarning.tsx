import {
  FunctionComponent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, message,Tooltip } from 'antd';
import { areaWarningApi,areaFeatureCharts } from '@/api/visual/monitorEarlyWarning';
import { codeDefinition } from '@/constants';
import { useSize } from 'ahooks';
import classNames from 'classnames';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScroll from '@/components/SeamlessScroll';
import '../index.less';

interface IDataSource {
  id: string;
  warnDate: string;
  etiologyId: number;
  etiologyName: null;
  warnArea: string;
  warnInfo: string;
}

const AreaWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IDataSource[]>([]);


  const [dataChartsSource, setDataChartsSource] = useState<IDataSource[]>([]);



  const [active1, setActive1] = useState<any>(1);


  const getDataSource = () => {
    setLoading(true);
    areaWarningApi()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getChartsDataSource = () => {
    setLoading(true);
    areaFeatureCharts()
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataChartsSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };




  useEffect(() => {
    getDataSource();
    getChartsDataSource();
  }, []);

  const createScrollDom = useCallback(() => {
    let _dom: FunctionComponent[] = [];
    if (dataSource.length) {
      _dom = dataSource.map((_item, _index) => {
        return () => (
          <div
            key={_index}
            className={classNames(
              'w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center text-white ',
              _index % 2 === 0 ? '' : 'bg-[rgba(0,114,187,0.3)]'
            )}
          >
            <span className="text-center">{_index + 1}</span>
            <span>{_item.warnDate}</span>
            <span>{_item.warnArea}</span>
            <span>{_item.etiologyName}</span>
            <span className=" w-full truncate" title={_item.warnInfo}>
              {_item.warnInfo}
            </span>
          </div>
        );
      });
    }
    return _dom;
  }, [dataSource]);

  const boxRef = useRef(null);

  const scrollSwipeSize = useSize(boxRef);
  const [slideParams, setSlideParams] = useState({
    parentHeight: 0,
    slidesPerView: 0,
  });

  useEffect(() => {
    const _params = { parentHeight: 0, slidesPerView: 0 };
    if (scrollSwipeSize) {
      _params.parentHeight = scrollSwipeSize.height - 44 - 24 - 40;
      _params.slidesPerView = Math.floor(_params.parentHeight / 40);
    }
    setSlideParams(_params);
  }, [scrollSwipeSize]);

  // 点击处理函数，根据传入的值更新 active1
  const handleLinkClick = (value: number) => {
    setActive1(value);
  };



  return (
    <div className=" w-full h-full flex flex-col" ref={boxRef}>
      <FullScreenItemTitle
        title="区域态势预警"
        extra={
          <div className=" bg-[rgba(19,133,239,0.2)] rounded text-white px-2 h-auto  d-flex">
              <div
              className={classNames('link-1', { 'link-active': active1 === 1 })}
              onClick={() => handleLinkClick(1)}
              style={{ cursor: 'pointer' }}
            >
              表格
            </div>
            {/* 为表格按钮添加点击事件，点击时将 active1 设置为 2 */}
            <div
              className={classNames('link-2', { 'link-active': active1 === 2 })}
              onClick={() => handleLinkClick(2)}
              style={{ cursor: 'pointer' }}
            >
              图表
            </div>
            预警次数
            <span className=" text-lg leading-5">{dataSource.length}</span>次
          </div>
        }
      />

        {active1==1 && (
          <div>
            <div className="w-full h-[40px] grid grid-cols-[1fr_2fr_2fr_2fr_2fr] items-center bg-[rgba(0,114,187,0.3)] text-[#ADD2FF] mt-6">
              <span className="text-center">序号</span>
              <span>预警日期</span>
              <span>区域</span>
              <span>病原体</span>
              <span>预警内容</span>
            </div>
            <div className=" flex-1">
              {loading ? (
                <div className="w-full h-full">
                  {/* <Loading>Loading...</Loading> */}
                </div>
              ) : dataSource.length ? (
                <div
                  className="w-full bg-[rgba(8,24,48,0.52)]"
                  style={{ height: slideParams.parentHeight }}
                >
                  <SeamlessScroll
                    swiperSlideClassname="!w-full !h-[40px]"
                    direction="vertical"
                    spaceBetween={0}
                    slidesPerView={slideParams.slidesPerView}
                    children={createScrollDom()}
                  />
                </div>
              ) : null}
            </div>
          </div>
      )}


    {active1 === 2 && (
        <div className="chart-container">
          {dataChartsSource.map((item:any, index:number) => (
            <div key={index} className="bar">
                <div className="icon"><span className='num-1'>{index + 1}</span>
                </div>
                <div className='nameValue'>{item.areaRange}</div>
               {/* 假设 item 有 value 字段表示数值，计算宽度 */}
                <div className="bar-value">
                  <div className='bar-fill' style={{ width: `${item.cityRate }%` }}></div>
                  <span className='num'>{item.cityCount}</span>
                </div>
                <Tooltip className='bar-increase' placement="leftTop" title="省内占比">
                  <div className="bar-increase bar-increase-up">{item.cityRate}%</div>
                </Tooltip>
            </div>
          ))}
        </div>
      )}



    </div>
  );
};

export default AreaWarning;
