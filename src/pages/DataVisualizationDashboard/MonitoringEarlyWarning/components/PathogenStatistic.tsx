import { useEffect, useState } from 'react';
import { message } from 'antd';
import { warningDataApi } from '@/api/visual/monitorEarlyWarning';
import icon_1 from '@/assets/fullScreen/MonitoringEarlyWarning/icon1.png';
import icon_2 from '@/assets/fullScreen/MonitoringEarlyWarning/icon2.png';
import icon_3 from '@/assets/fullScreen/MonitoringEarlyWarning/icon3.png';
import icon_4 from '@/assets/fullScreen/MonitoringEarlyWarning/icon4.png';
import icon_5 from '@/assets/fullScreen/MonitoringEarlyWarning/icon5.png';
import icon_6 from '@/assets/fullScreen/MonitoringEarlyWarning/icon6.png';
import bg from '@/assets/fullScreen/MonitoringEarlyWarning/img-bg.png';
import { codeDefinition } from '@/constants';

interface IDataSource {
  acutePathogens: number;
  highPathogens: number;
  newPathogens: number;
  emergencyPathogens: number;
  inputPathogens: number;
  pathogensPositive: number;
}

const PathogenStatistic: React.FC = () => {
  const [options, setOptions] = useState([
    {
      key: 'pathogensPositive',
      title: '病原阳性预警数',
      value: 0,
      icon: icon_1,
    },
    {
      key: 'acutePathogens',
      title: '急性病原预警数',
      value: 0,
      icon: icon_2,
    },
    {
      key: 'highPathogens',
      title: '高致病性病原预警数',
      value: 0,
      icon: icon_3,
    },
    {
      key: 'newPathogens',
      title: '新发病原预警数',
      value: 0,
      icon: icon_4,
    },
    {
      key: 'emergencyPathogens',
      title: '突发病原预警数',
      value: 0,
      icon: icon_5,
    },
    {
      key: 'inputPathogens',
      title: '输入性病原预警数',
      value: 0,
      icon: icon_6,
    },
  ]);

  const getDataSource = () => {
    warningDataApi().then((res) => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const data: IDataSource = res.data;
        setOptions(
          options.map((item) => ({
            ...item,
            value: data[item.key as keyof IDataSource] || 0,
          }))
        );
      } else {
        message.error(res.msg);
      }
    });
  };

  useEffect(() => {
    getDataSource();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className=" w-full h-full flex flex-col justify-evenly">
      {options.map((_item, _index) => (
        <div key={_index} className=" relative flex">
          <img alt="" src={bg} className=" w-full max-h-[70px]" />
          <div className=" absolute top-0 left-0 w-full h-full flex items-center justify-between px-4 text-white">
            <img alt="" src={_item.icon} className=" h-[51px]" />
            <span className=" text-lg">{_item.title}</span>
            <span className=" w-[20%] text-[28px] font-semibold text-center">
              {_item.value}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PathogenStatistic;
