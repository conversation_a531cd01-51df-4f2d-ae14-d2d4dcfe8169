// 监测预警
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { positiveWarningApi } from '@/api/visual/monitorEarlyWarning';
import { codeDefinition } from '@/constants';
import { converPxToVH, converPxToVW } from '@/utils';
import { useDebounceFn } from 'ahooks';
import AgeWarning from './components/AgeWarning';
import AreaWarning from './components/AreaWarning';
import MapContainer from './components/MapContainer';
import PathogenStatistic from './components/PathogenStatistic';
import PeopleWarning from './components/PeopleWarning';
import PositiveWarning from './components/PositiveWarning';

import TimeWarning from './components/TimeWarning';


import './index.less';
import { IPositiveWarningDataSource } from './type';

const MonitoringEarlyWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<IPositiveWarningDataSource[]>(
    []
  );
  const getDataSource = (code?: number) => {
    setLoading(true);
    positiveWarningApi(code)
      .then((res) => {
        if (res.code === codeDefinition.QUERY_SUCCESS) {
          setDataSource(res.data);
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDataSource();
  }, []);
  const { run } = useDebounceFn(
    (code) => {
      getDataSource(code);
    },
    {
      wait: 500,
    }
  );

  return (
    <div
      className="businessMetricsBox w-full flex overflow-hidden"
      style={{
        paddingLeft: `${converPxToVW(50)}vw`,
        paddingRight: `${converPxToVW(50)}vw`,
        paddingTop: `${converPxToVH(34)}vh`,
        paddingBottom: `${converPxToVH(34)}vh`,
        height: `${100 - ~~converPxToVH(108)}vh`,
      }}
    >
      <div className=" w-2/3 h-full pr-6">
        <div className=" w-full h-2/3 flex">
          {/* 统计展示 */}
          <div className=" w-5/12 h-full">
            <PathogenStatistic />
          </div>
          {/* 地图 */}
          <div className=" w-7/12 h-full">
            <MapContainer
              id="monitoringEarlyWarningMap"
              handleSearch={(code) => {
                run(code);
              }}
            />
          </div>
        </div>
        {/* 病原阳性预警 */}

        <div className=" w-full h-1/3 pt-6 d-flex">
          <div className=" w-1/2  h-full">
            <PositiveWarning loading={loading} dataSource={dataSource} />
          </div>
          <div className="ml-2 w-1/2  h-full">
            <TimeWarning />
          </div>
        </div>

      </div>
      <div className=" w-1/3 h-full pl-6">
        {/* 区域态势预警 */}
        <div className=" w-full h-1/3 pb-4">
          <AreaWarning />
        </div>
        {/* 人群态势预警 */}
        <div className=" w-full h-1/3 py-2">
          <PeopleWarning />
        </div>
        {/* 年龄态势预警 */}
        <div className=" w-full h-1/3 pt-4">
          <AgeWarning />
        </div>
      </div>
    </div>
  );
};

export default MonitoringEarlyWarning;
