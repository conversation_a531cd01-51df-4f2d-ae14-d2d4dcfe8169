.map-tooltips-wrapper {
  background-image: url('@/assets/fullScreen/MonitoringOverview/map-tips.png');
}

.d-flex {
  display: flex;

  .link-1 ,.link-2 {
    margin-right: 10px;
    color: #13B1FA;
  }

  .link-active {
    text-decoration: underline;
    font-weight: bold;
  }

}
   .chart-container {
            width: 420px;
            margin-top: 10px;
        }
        .bar {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            position: relative;
        }
        .bar-label {
            width: 30px;
            height: 15px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-weight: bold;
        }


        .icon {
            width: 20px;
            height: 20px;
            background-color: #3D8FFF;
            border-radius: 5px 0 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-right: 20px;
        }
        .icon::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid #3D8FFF;
            right: -10px;
        }

         .bar:nth-child(2)  .icon {
            background-color: #FF3232;
        }

          .bar:nth-child(2)  .icon::before {
            border-left: 10px solid #FF3232;
        }


         .bar:nth-child(3)   .icon {
            background-color: #FF9800;
        }

         .bar:nth-child(3)   .icon::before {
            border-left: 10px solid #FF9800;
        }


         .bar:nth-child(4)   .icon {
            background-color: #FFD200;
        }

         .bar:nth-child(4)   .icon::before {
            border-left: 10px solid #FFD200;
        }

        .icon span {
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .icon .num-1 {
            font-size: 16px;
        }

        .bar-value {
            height: 12px;
            background-color: #1e3a5f;
            width: 100%;
            position: relative;
            border-radius: 5px;
            // overflow: hidden;
        }
        .bar-fill {
            height: 100%;
            background-color: #00aaff;
            width: 20px;
            transition: width 0.5s ease;
            border-radius: 5px;
        }
        .bar .num {
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 14px;
        }
        .bar-increase {
            position: absolute;
            right: -80px;
            top: -2px;
            color: #00aaff;
            font-size: 14px;
        }
        .nameValue {
            width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }