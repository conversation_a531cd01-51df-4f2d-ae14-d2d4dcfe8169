/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 样本检测情况
import React, { useContext, useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getLaboratoryRatingData } from '@/api/visual/baboratoryPortrait';
import EmptyDataIcon from '@/assets/no_data.webp';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import { converPxToVW } from '@/utils';
import classnames from 'classnames';
import Loading from '@/components/ELoading';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScrollNext from '@/components/SeamlessScrollNext';
import { LaboratoryPortraitContext } from '..';

type TLaboratoryRatingProps = {};

const LaboratoryRating: React.FC<TLaboratoryRatingProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  // 滚动的数据集合
  const [scrollListCollection, setScrollListCollection] = useState<
    React.FunctionComponent[]
  >([]);

  /**
   * @TODO 获取实验室评级数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getLaboratoryRatingData({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      const _scrollListCollection: React.FunctionComponent[] = [];
      data?.forEach((_item: Record<string, any>, _index: number) => {
        _scrollListCollection.push(() => generateScrollItemDOM(_item, _index));
      });
      setScrollListCollection(_scrollListCollection);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 创建单条滚动数据DOM
   */
  const generateScrollItemDOM = (
    _item: Record<string, any>,
    _index: number
  ) => {
    return (
      <div
        className={classnames(
          'w-full flex flex-row flex-nowrap items-center py-2',
          (_index + 1) % 2 === 0
            ? 'bg-[rgba(8,24,48,0.4)]'
            : 'bg-[rgba(8,24,48,0.5216)]'
        )}
      >
        <div className="flex justify-center items-center w-[80px]">
          <span
            className={classnames(
              ' w-[18px] h-[18px] border border-solid  flex justify-center items-center',
              _index === 0
                ? 'border-[#FFD86DFF] text-[#FFD86DFF]'
                : _index === 1
                ? 'border-[#52CC92FF] text-[#52CC92FF]'
                : _index === 2
                ? 'border-[#81D7FCFF] text-[#81D7FCFF]'
                : 'border-white text-white'
            )}
          >
            {_index + 1}
          </span>
        </div>
        <div className="flex-1 truncate text-center">{_item?.gradeTime}</div>
        <div className="flex-1 truncate text-center">{_item?.grade}</div>
      </div>
    );
  };

  useEffect(() => {
    queryDataList();
  }, [selectedOrgName]);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="实验室评级" />
      <div className="flex-1 flex flex-col flex-nowrap">
        {/* 表格头 */}
        <div className="w-full h-[42px] flex flex-row flex-nowrap items-center text-[13px] text-[#FFFFFFB2]">
          <div className="w-[80px] text-center">序号</div>
          <div className="flex-1 text-center">评定时间</div>
          <div className="flex-1 text-center">等级</div>
        </div>
        {/* 滚动部分 */}
        <div ref={domRef} className="w-full flex-1 flex flex-col flex-nowrap">
          {loading ? (
            <Loading>loading...</Loading>
          ) : (
            <>
              {scrollListCollection?.length ? (
                <SeamlessScrollNext
                  spaceBetween="0"
                  slidesPerView="5"
                  direction="vertical"
                  children={scrollListCollection}
                  height={domRef?.current?.clientHeight!}
                />
              ) : (
                <div className="w-full h-full flex flex-row flex-nowrap justify-center items-center">
                  <img src={EmptyDataIcon} alt="" />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default LaboratoryRating;
