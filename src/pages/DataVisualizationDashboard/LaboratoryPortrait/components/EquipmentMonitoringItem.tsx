/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import TempImg from '@/assets/fullScreen/LaboratoryPortrait/temp-icon.png';
import { converPxToVW } from '@/utils';
import ChartsWrapper from '@/components/ChartsWrapper';

type TEquipmentMonitoringItemProps = {
  data: Record<string, any>;
};
const EquipmentMonitoringItem: React.FC<TEquipmentMonitoringItemProps> = ({
  data,
}) => {
  const option = {
    tooltip: {},
    legend: {
      data: [],
      textStyle: {
        color: '#FFFFFF',
      },
      top: 10,
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['01/20', '01/21', '01/22', '01/23'],
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          show: true,
          textStyle: { color: '#FFFFFF' },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          textStyle: { color: '#FFFFFF' },
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    series: [
      {
        name: '',
        type: 'line',
        data: [13, 14, 49, 10],
        lineStyle: {
          normal: {
            color: '#3AE8FF',
          },
        },
        areaStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#3AE8FF',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 255, 255, 0)',
                },
              ],
              global: false,
            },
          },
        },
      },
    ],
  };

  return (
    <div className="w-full h-full flex flex-row flex-nowrap gap-2 items-center">
      <div className="w-full flex-1 flex flex-row flex-nowrap gap-1">
        <div
          className="border border-solid border-[#194E93] rounded-md p-1"
          style={{
            width: `${converPxToVW(90)}vw`,
            height: `${converPxToVW(90)}vw`,
          }}
        >
          <img src={TempImg} alt="" className="w-full h-full rounded" />
        </div>
        <div
          className="flex flex-col"
          style={{ fontSize: `${converPxToVW(12)}vw` }}
        >
          <div className="">设备名称: {data?.equName}</div>
          <div className="">设备编号: {data?.financeCode}</div>
          <div className="">最近开机: {data?.maxBeginDate?.split(' ')[0]}</div>
          <div className="">上次关机: {data?.secondMaxDate?.split(' ')[0]}</div>
        </div>
      </div>
      <div className="flex-1">
        <ChartsWrapper
          option={option as any}
          // height={domRef?.current?.clientHeight}
        />
      </div>
    </div>
  );
};

export default EquipmentMonitoringItem;
