/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
import React, { useContext, useEffect, useState } from 'react';
import { ConfigProvider, message, Pagination } from 'antd';
import { getDeviceMonitoringData } from '@/api/visual/baboratoryPortrait';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { LaboratoryPortraitContext } from '..';
import EquipmentMonitoringItem from './EquipmentMonitoringItem';

type TEquipmentMonitoringProps = {};

const EquipmentMonitoring: React.FC<TEquipmentMonitoringProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [dataSource, setDataSource] = useState<Record<string, any>>();
  const [total, setTotal] = useState<number>();
  // 当前页码
  const [current, setCurrent] = useState<number>(1);

  /**
   * 获取数据
   * @returns
   */
  const queryDeviceMonitoringData = async () => {
    try {
      const { code, data, msg } = await getDeviceMonitoringData({
        orgName: selectedOrgName || defaultOrgName,
        pageSize: 5,
        pageNum: current,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      //@ts-ignore
      setDataSource(data?.rows);
      setTotal(data?.total);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryDeviceMonitoringData();
  }, [current, selectedOrgName]);

  return (
    <div className="w-full h-full flex flex-col gap-1">
      <FullScreenItemTitle title="设备运行监控" />
      <div className="w-full flex-1 flex flex-col gap-1 justify-start">
        {dataSource?.map((_item: any, _index: number) => (
          <div className="w-full flex-1">
            <EquipmentMonitoringItem key={_index} data={_item} />
          </div>
        ))}
        <div className="flex justify-end">
          <ConfigProvider
            theme={{
              components: {
                Pagination: {
                  /* 这里是你的组件 token */
                  colorText: '#FFFFFF',
                  colorTextDisabled: 'rgba(255, 255, 255, 0.8)',
                },
              },
            }}
          >
            <Pagination
              current={current}
              total={total}
              size="small"
              pageSize={5}
              onChange={(value) => {
                setCurrent(value);
              }}
            />
          </ConfigProvider>
        </div>
      </div>
    </div>
  );
};

export default EquipmentMonitoring;
