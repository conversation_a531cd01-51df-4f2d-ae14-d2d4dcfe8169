/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 样本检测情况
import React, { useContext, useEffect, useState } from 'react';
import { message } from 'antd';
import {
  getReportCompilationCount,
  getSampleRecordCount,
  getSampleTestingCount,
} from '@/api/visual/baboratoryPortrait';
import icon1 from '@/assets/fullScreen/LaboratoryPortrait/icon1.png';
import icon2 from '@/assets/fullScreen/LaboratoryPortrait/icon2.png';
import icon3 from '@/assets/fullScreen/LaboratoryPortrait/icon3.png';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import { converPxToVW } from '@/utils';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { LaboratoryPortraitContext } from '..';

type TSampleTestingStatusProps = {};

const SampleTestingStatus: React.FC<TSampleTestingStatusProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [sampleRecordCount, setSampleRecordCount] = useState<number>(0);
  const [sampleTestingCount, setSampleTestingCount] = useState<number>(0);
  const [reportCompilationCount, setReportCompilationCount] =
    useState<number>(0);

  /**
   *  获取样本接收数
   */
  const querySampleRecordCount = async () => {
    try {
      const { code, data, msg } = await getSampleRecordCount({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleRecordCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取样本接收数
   */
  const querySampleTestingCount = async () => {
    try {
      const { code, data, msg } = await getSampleTestingCount({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTestingCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取样本接收数
   */
  const queryReportCompilationCount = async () => {
    try {
      const { code, data, msg } = await getReportCompilationCount({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setReportCompilationCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  const renderItem = (title: string, icon: any, num: number) => {
    return (
      <div className="flex flex-row flex-nowrap items-center gap-2">
        <img
          src={icon}
          alt=""
          style={{
            width: `${converPxToVW(60)}vw`,
            height: `${converPxToVW(60)}vw`,
          }}
        />
        <div className="flex flex-col">
          <div className="" style={{ fontSize: `${converPxToVW(20)}vw` }}>
            {num}
          </div>
          <div className="">{title}</div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    querySampleRecordCount();
    querySampleTestingCount();
    queryReportCompilationCount();
  }, [selectedOrgName]);

  return (
    <div className="w-full h-full flex flex-col font-PangmengZhengdaoBiaoti">
      <FullScreenItemTitle title="样本检测情况" />
      <div className="w-full flex-1 flex flex-row flex-nowrap justify-between items-center px-4">
        {renderItem('样本接收数', icon1, sampleRecordCount)}
        {renderItem('样本检测数', icon2, sampleTestingCount)}
        {renderItem('报告编制数', icon3, reportCompilationCount)}
      </div>
    </div>
  );
};

export default SampleTestingStatus;
