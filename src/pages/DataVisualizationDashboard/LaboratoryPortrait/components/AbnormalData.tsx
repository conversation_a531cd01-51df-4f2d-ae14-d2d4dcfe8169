/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 样本检测情况
import React, { useContext, useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getAbnormalData } from '@/api/visual/baboratoryPortrait';
import abnormalDataImg01 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img01.png';
import abnormalDataImg02 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img02.png';
import abnormalDataImg03 from '@/assets/fullScreen/LaboratoryPortrait/abnormal-data-img03.png';
import EmptyDataIcon from '@/assets/no_data.webp';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import classnames from 'classnames';
import Loading from '@/components/ELoading';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import SeamlessScrollNext from '@/components/SeamlessScrollNext';
import { LaboratoryPortraitContext } from '..';

type TAbnormalDataProps = {};

const AbnormalData: React.FC<TAbnormalDataProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  const [abnormalDataList, setAbnormalDataList] =
    useState<Record<string, any>>();

  // 滚动的数据集合
  const [scrollListCollection, setScrollListCollection] = useState<
    React.FunctionComponent[]
  >([]);

  /**
   * @TODO 获取计划排行数据
   */
  const queryDataList = async () => {
    setLoading(true);

    try {
      const { code, data, msg } = await getAbnormalData({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setAbnormalDataList({
        abnormal: data?.abnormal,
        rectified: data?.rectified,
        rectifiedRate: data?.rectifiedRate,
      });

      const _scrollListCollection: React.FunctionComponent[] = [];
      data?.anomalousData?.forEach(
        (_item: Record<string, any>, _index: number) => {
          _scrollListCollection.push(() =>
            generateScrollItemDOM(_item, _index)
          );
        }
      );
      setScrollListCollection(_scrollListCollection);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 创建单条滚动数据DOM
   */
  const generateScrollItemDOM = (
    _item: Record<string, any>,
    _index: number
  ) => {
    return (
      <div
        className={classnames(
          'w-full flex flex-row flex-nowrap items-center py-2',
          (_index + 1) % 2 === 0
            ? 'bg-[rgba(8,24,48,0.4)]'
            : 'bg-[rgba(8,24,48,0.5216)]'
        )}
      >
        <div className="flex justify-center items-center w-[60px]">
          <span
            className={classnames(
              ' w-[18px] h-[18px] border border-solid  flex justify-center items-center',
              _index === 0
                ? 'border-[#FFD86DFF] text-[#FFD86DFF]'
                : _index === 1
                ? 'border-[#52CC92FF] text-[#52CC92FF]'
                : _index === 2
                ? 'border-[#81D7FCFF] text-[#81D7FCFF]'
                : 'border-white text-white'
            )}
          >
            {_index + 1}
          </span>
        </div>
        <div className="flex-1 truncate text-center">
          {_item?.sampleTaskNumber}
        </div>
        <div className="flex-1 truncate text-center">{_item?.abnormalType}</div>
        <div className="flex-1 truncate text-center">{_item?.recordState}</div>
      </div>
    );
  };

  /**
   * 选择异常数据
   */
  const renderAbnormalDataList = (
    total: number,
    updateTotal: number,
    rate: string
  ) => {
    return (
      <>
        <div className="flex-1 flex flex-row flex-nowrap justify-center items-center gap-2 bg-[url('/src/assets/fullScreen/LaboratoryPortrait/abnormal-data-bg.png')] bg-no-repeat bg-full mx-2">
          <img src={abnormalDataImg01} alt="" />
          <div className="flex flex-col">
            <div className="text-lg font-PangmengZhengdaoBiaoti">{total}</div>
            <div className="text-xs">异常总数</div>
          </div>
        </div>
        <div className="flex-1 flex flex-row flex-nowrap justify-center items-center gap-2 bg-[url('/src/assets/fullScreen/LaboratoryPortrait/abnormal-data-bg.png')] bg-no-repeat bg-full mx-2">
          <img src={abnormalDataImg02} alt="" />
          <div className="flex flex-col">
            <div className="text-lg  font-PangmengZhengdaoBiaoti">
              {updateTotal}
            </div>
            <div className="text-xs">整改完成数</div>
          </div>
        </div>
        <div className="flex-1 flex flex-row flex-nowrap justify-center items-center gap-2 bg-[url('/src/assets/fullScreen/LaboratoryPortrait/abnormal-data-bg.png')] bg-no-repeat bg-full mx-2">
          <img src={abnormalDataImg03} alt="" />
          <div className="flex flex-col">
            <div className="text-lg font-PangmengZhengdaoBiaoti">{rate}</div>
            <div className="text-xs">整改完成率</div>
          </div>
        </div>
      </>
    );
  };

  useEffect(() => {
    queryDataList();
  }, [selectedOrgName]);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="异常数据展示" />
      <div className="w-full h-[60px] flex flex-row flex-nowrap my-2">
        {renderAbnormalDataList(
          abnormalDataList?.abnormal,
          abnormalDataList?.rectified,
          abnormalDataList?.rectifiedRate
        )}
      </div>
      <div className="flex-1 flex flex-col flex-nowrap">
        {/* 表格头 */}
        <div className="w-full h-[42px] flex flex-row flex-nowrap items-center text-[13px] text-[#FFFFFFB2]">
          <div className="w-[60px] text-center">序号</div>
          <div className="flex-1 text-center">样本编号</div>
          <div className="flex-1 text-center">异常原因</div>
          <div className="flex-1 text-center">状态</div>
        </div>
        {/* 滚动部分 */}
        <div ref={domRef} className="w-full flex-1 flex flex-col flex-nowrap">
          {loading ? (
            <Loading>loading...</Loading>
          ) : (
            <>
              {scrollListCollection?.length ? (
                <SeamlessScrollNext
                  spaceBetween="0"
                  slidesPerView="4"
                  direction="vertical"
                  children={scrollListCollection}
                  height={domRef?.current?.clientHeight!}
                />
              ) : (
                <div className="w-full h-full flex flex-row flex-nowrap justify-center items-center">
                  <img src={EmptyDataIcon} alt="" />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AbnormalData;
