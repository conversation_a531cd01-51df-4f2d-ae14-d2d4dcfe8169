/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 样本检测情况
import React, { useContext, useEffect, useRef, useState } from 'react';
import { message, Space } from 'antd';
import { getEnvironmentMonitoringData } from '@/api/visual/baboratoryPortrait';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';
import Loading from '@/components/ELoading';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { LaboratoryPortraitContext } from '..';

type TEnvironmentalMonitoringProps = {};

const EnvironmentalMonitoring: React.FC<TEnvironmentalMonitoringProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [loading, setLoading] = useState<boolean>(false);
  const domRef = useRef<HTMLDivElement>(null);

  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);
  const [option, setOption] = useState<Record<string, any>>();

  // 温湿度数据

  /**
   *  获取数据
   */
  const queryDataList = async () => {
    setLoading(true);
    try {
      const { code, data, msg } = await getEnvironmentMonitoringData({
        orgName: selectedOrgName || defaultOrgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      setTimeout(() => {
        setOption({
          tooltip: {},
          legend: {
            data: [],
            textStyle: {
              color: '#FFFFFF',
            },
            top: 10,
          },
          grid: {
            top: '10%',
            left: '3%',
            right: '4%',
            bottom: '23%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: data.map((item: Record<string, any>) => item.hourNum + 'h'),
              axisPointer: {
                type: 'shadow',
              },
              axisLabel: {
                show: true,
                textStyle: { color: '#FFFFFF' },
              },
              axisTick: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: { color: '#FFFFFF' },
              },
              splitLine: {
                lineStyle: {
                  color: ['#FFFFFF'],
                  opacity: 0.25,
                },
              },
            },
          ],
          series: [
            {
              name: '温度',
              type: 'line',
              data: data.map((item: Record<string, any>) => item.temperature),
              lineStyle: {
                normal: {
                  color: '#FFA533',
                },
              },
              // areaStyle: {
              //   normal: {
              //     color: {
              //       type: 'linear',
              //       x: 0,
              //       y: 0,
              //       x2: 0,
              //       y2: 1,
              //       colorStops: [
              //         {
              //           offset: 0,
              //           color: '#FFA533',
              //         },
              //         {
              //           offset: 1,
              //           color: 'rgba(255, 255, 255, 0)',
              //         },
              //       ],
              //       global: false,
              //     },
              //   },
              // },
            },
            {
              name: '湿度',
              type: 'line',
              data: data.map((item: Record<string, any>) => item.humidity),
              lineStyle: {
                normal: {
                  color: '#94C1FF',
                },
              },
            },
          ],
        });
      }, 200);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDataList();
  }, [selectedOrgName]);

  return (
    <div className="w-full h-full flex flex-col">
      <FullScreenItemTitle title="环境监测" />
      {loading ? (
        <Loading>Loading...</Loading>
      ) : (
        <div className="w-full h-full flex flex-col">
          <div className="w-full flex flex-row flex-nowrap mt-2 gap-4">
            <div className="w-[164px] h-[40px] bg-[url('/src/assets/fullScreen/LaboratoryPortrait/temperature.png')] bg-no-repeat bg-full text-center flex justify-center items-center">
              <div className="pl-6">
                <span>温度: </span>
                <span className="font-YousheBiaotiHei">25&deg;C</span>
              </div>
            </div>
            <div className="w-[164px] h-[40px] bg-[url('/src/assets/fullScreen/LaboratoryPortrait/humidity.png')] bg-no-repeat bg-full text-center flex justify-center items-center">
              <div className="pl-6">
                <span>湿度: </span>
                <span className="font-YousheBiaotiHei">25%</span>
              </div>
            </div>
          </div>
          <div ref={domRef} className="w-full flex-1">
            <ChartsWrapper
              option={option as any}
              height={domRef?.current?.clientHeight}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EnvironmentalMonitoring;
