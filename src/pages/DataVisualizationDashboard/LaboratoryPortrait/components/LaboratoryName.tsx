/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/alt-text */
// 样本检测情况
import React, { useContext, useEffect, useState } from 'react';
import { Input, message, Select } from 'antd';
import {
  getOrganizationBasicInformation,
  handleSearchOrg,
} from '@/api/visual/baboratoryPortrait';
import laboratoryImg from '@/assets/fullScreen/LaboratoryPortrait/laboratory.png';
import { codeDefinition, orgName as defaultOrgName } from '@/constants';
import { SearchOutlined } from '@ant-design/icons';
import FullScreenItemTitle from '@/components/FullScreenItemTitle';
import { LaboratoryPortraitContext } from '..';

type TLaboratoryNameProps = {};

let timeout: ReturnType<typeof setTimeout> | null;

const LaboratoryName: React.FC<TLaboratoryNameProps> = () => {
  const { selectedOrgName, setSelectedOrgName } = useContext<
    Record<string, any>
  >(LaboratoryPortraitContext);

  const [orgBasicInfo, setOrgBasicInfo] = useState<Record<string, any>>();
  const [startSearch, setStartSearch] = useState<boolean>(false);

  // 搜索的机构列表
  const [orgResultList, setOrgResultList] = useState<Record<string, any>[]>();

  // 当前选择的机构
  const [curSelectedOrgName, setCurSelectedOrgName] = useState<string>();

  /**
   * 获取用户信息
   * @returns
   */
  const queryOrganizationBasicInformation = async (orgName?: string) => {
    if (!orgName) {
      orgName = defaultOrgName;
    }

    try {
      const { code, data, msg } = await getOrganizationBasicInformation({
        orgName,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setOrgBasicInfo(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 搜索
   */
  const handleSearch = async (keyword: string) => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    try {
      const { code, data, msg } = await handleSearchOrg({
        orgName: keyword,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setOrgResultList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 选择机构
   */
  const handleChange = (e: any) => {
    handleSearch(e.target?.value);
  };

  useEffect(() => {
    if (curSelectedOrgName) {
      setSelectedOrgName(curSelectedOrgName);
      queryOrganizationBasicInformation(curSelectedOrgName);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curSelectedOrgName]);

  useEffect(() => {
    queryOrganizationBasicInformation();
  }, []);

  return (
    <div className="relative w-full h-full">
      <FullScreenItemTitle title={orgBasicInfo?.orgName} />
      {/* 搜索 */}
      <div className="absolute top-[5px] right-4 cursor-pointer">
        {startSearch ? (
          <Select
            showSearch
            // value={value}
            placeholder="请输入"
            defaultActiveFirstOption={false}
            suffixIcon={null}
            filterOption={false}
            onSearch={handleSearch}
            onChange={handleChange}
            notFoundContent={null}
            options={orgResultList}
            size="small"
            style={{ width: '180px', position: 'relative', top: '-2px' }}
            fieldNames={{ label: 'orgName', value: 'orgName' }}
            onSelect={(value) => {
              setCurSelectedOrgName(value);
              setStartSearch(false);
            }}
          />
        ) : (
          <SearchOutlined
            style={{ fontSize: '20px' }}
            onClick={() => setStartSearch(true)}
          />
        )}
      </div>
      <div className="w-full h-full flex flex-row flex-nowrap gap-4">
        <div className="flex-1">
          <img src={laboratoryImg} alt="" />
        </div>
        <div className="relative flex-1 flex flex-col gap-3 border border-solid border-[#2E67BC] my-16 mx-6 p-6 text-[rgba(255,255,255,0.75)]">
          <div className="">机构名称: {orgBasicInfo?.orgName}</div>
          <div className="">检验项目数: {orgBasicInfo?.aptitude}</div>
          <div className="">质量控制记录数: {orgBasicInfo?.qualityControl}</div>
          <div className="">设备数量: {orgBasicInfo?.equipmentInfo}</div>
          <div className="">
            设备检定校准次数: {orgBasicInfo?.equipmentVerification}
          </div>
          <div className="">设备核查次数: {orgBasicInfo?.equipmentCheck}</div>
          <div className="">检测人员数量: {orgBasicInfo?.Inspectors}</div>
          <div className="absolute left-0 -top-[14px] w-[134px] h-[28px] text-center bg-[url('/src/assets/fullScreen/LaboratoryPortrait/img-smallTitle.png')] bg-no-repeat bg-full leading-7">
            基本信息
          </div>
        </div>
      </div>
    </div>
  );
};

export default LaboratoryName;
