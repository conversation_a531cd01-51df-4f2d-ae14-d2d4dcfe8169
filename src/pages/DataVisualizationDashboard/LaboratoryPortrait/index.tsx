/* eslint-disable @typescript-eslint/no-unused-vars */
// 实验室画像
import React, { createContext, useState } from 'react';
import { converPxToVH, converPxToVW } from '@/utils';
import AbnormalData from './components/AbnormalData';
import EnvironmentalMonitoring from './components/EnvironmentalMonitoring';
import EquipmentMonitoring from './components/EquipmentMonitoring';
import LaboratoryName from './components/LaboratoryName';
import LaboratoryRating from './components/LaboratoryRating';
import SampleTestingStatus from './components/SampleTestingStatus';

type TLaboratoryPortraitProps = {};

export const LaboratoryPortraitContext = createContext({});

const LaboratoryPortrait: React.FC<TLaboratoryPortraitProps> = () => {
  const [selectedOrgName, setSelectedOrgName] = useState<string>();

  const initValue = { selectedOrgName, setSelectedOrgName };

  return (
    <LaboratoryPortraitContext.Provider value={initValue}>
      <div
        className="w-full flex flex-row flex-nowrap gap-6 overflow-hidden"
        style={{
          paddingLeft: `${converPxToVW(50)}vw`,
          paddingRight: `${converPxToVW(50)}vw`,
          paddingTop: `${converPxToVH(34)}vh`,
          paddingBottom: `${converPxToVH(34)}vh`,
          height: `${100 - ~~converPxToVH(108)}vh`,
        }}
      >
        {/* 左侧 */}
        <div
          className="h-full flex flex-col flex-nowrap gap-1"
          style={{ width: `${converPxToVW(500)}vw` }}
        >
          {/* 样本检测情况 */}
          <div className="w-full" style={{ height: `${converPxToVH(100)}vw` }}>
            <SampleTestingStatus />
          </div>
          {/* 实验室评级 */}
          <div className="w-full flex-1">
            <LaboratoryRating />
          </div>
          {/* 异常数据展示 */}
          <div className="w-full flex-1">
            <AbnormalData />
          </div>
        </div>
        {/* 中间 */}
        <div className="h-full flex-1 flex flex-col flex-nowrap">
          {/* 实验室名称 */}
          <div className="flex-1">
            <LaboratoryName />
          </div>
          {/* 环境监测 */}
          <div className="w-full" style={{ height: '40vh' }}>
            <EnvironmentalMonitoring />
          </div>
        </div>
        {/* 右侧 */}
        <div
          className="h-full flex flex-col flex-nowrap"
          style={{ width: `${converPxToVW(450)}vw` }}
        >
          {/* 设备运行监控 */}
          <div className="w-full h-full">
            <EquipmentMonitoring />
          </div>
        </div>
      </div>
    </LaboratoryPortraitContext.Provider>
  );
};

export default LaboratoryPortrait;
