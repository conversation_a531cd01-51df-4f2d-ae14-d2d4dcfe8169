/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Divider, message } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { useTokenStore } from '@/store';
import { useQualityStore } from '@/store/quality';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import { getIconByName } from '@/utils/upload';
import { getFileTypeByName } from '@/utils/upload';
import { yearList } from '../data';
import { FormInitVal, formItemLayout } from '../data';

const layoutProps = {
  colProps: { ...formItemLayout },
  // labelCol: { span: 5 },
};
type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  readonly: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
};

const BaseForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onSubmit,
  onRef,
  readonly,
  readonlyAll = true,
  hideRank = false,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        handleSave();
      },
    };
  });

  const { token } = useTokenStore();
  // 获取table中需要的枚举
  const {
    assessmentTypesOnForm,
    getAssessmentTypes,
    assessmentTaskTypesOnForm,
    getAssessmentTaskTypes,
  } = useQualityStore();

  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);

  // 表单实例
  const formRef = useRef<FormInstance>(null);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    try {
      if (id && detailInfo) {
        const p = clone(detailInfo);
        try {
          p.attachmentIds = p.attachmentIds
            ? JSON.parse(p.attachmentIds).map((item: any) => {
                return {
                  uid: item.id,
                  name: item.name,
                  status: 'done',
                  type: 'application/msword',
                  url: item.id,
                  response: {
                    data: {
                      fileName: item.name,
                      ossId: item.id,
                    },
                  },
                };
              })
            : [];
        } catch (error) {
          p.attachmentIds = [];
        }
        formRef.current?.setFieldsValue(p);
        formRef2.current?.setFieldsValue(p);
      } else {
        formRef.current?.setFieldsValue({
          particularYear: new Date().getFullYear(),
        });
        formRef2.current?.setFieldsValue({});
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async () => {
    await formRef.current?.validateFields();
    await formRef2.current?.validateFields();

    let form1 = cloneDeep(formRef.current?.getFieldsValue());
    let form2 = cloneDeep(formRef2.current?.getFieldsValue());

    let attachmentIds = [];
    if (form1.attachmentIds && form1.attachmentIds.length > 0) {
      attachmentIds = form1.attachmentIds
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            id: item.response.data.ossId,
          };
        });
    }
    const params = {
      ...form1,
      ...form2,
      attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
    };
    onSubmit(params);
  };

  // 表单实例2
  const formRef2 = useRef<FormInstance>(null);

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  return (
    <>
      <BlockContainer title="基本信息">
        <ProForm
          readonly={readonly || readonlyAll}
          formRef={formRef}
          {...formItemLayout}
          layout="horizontal"
          grid={true}
          submitter={false}
          initialValues={FormInitVal}
          onValuesChange={(_: any, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormSelect
            readonly={readonlyAll || !!id}
            options={yearList}
            rules={[{ required: true, message: '请选择年份' }]}
            name="particularYear"
            label="年份"
            {...layoutProps}
          />
          <ProFormText
            readonly={readonlyAll || !!id}
            name="name"
            label="任务名称"
            placeholder="请输入任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
            {...layoutProps}
          />
          <ProFormSelect
            readonly={readonlyAll || !!id}
            options={assessmentTypesOnForm}
            rules={[{ required: true, message: '请选择考核类型' }]}
            name="assessmentType"
            label="考核类型"
            {...layoutProps}
          />
          <ProFormSelect
            readonly={readonlyAll || !!id}
            options={assessmentTaskTypesOnForm}
            rules={[{ required: true, message: '请选择任务类型' }]}
            name="taskType"
            label="任务类型"
            {...layoutProps}
          />
          <ProFormDatePicker
            readonly={readonlyAll}
            rules={[{ required: true, message: '请选择' }]}
            initialValue={dayjs()}
            name="endDate"
            label="结束日期"
            width={'lg'}
            {...layoutProps}
          />
          <ProFormTextArea
            readonly={readonlyAll}
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.005 }}
            name="remark"
            label="备注信息"
          />

          <ProFormUploadButton
            name="attachmentIds"
            label="附件信息"
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.005 }}
            max={10}
            readonly={readonlyAll}
            fieldProps={{
              iconRender: (file) => {
                return (
                  <img
                    src={getIconByName(file.name)}
                    className="!w-[40px] !h-[40px] m-auto mt-2"
                    alt="logo"
                  />
                );
              },
              disabled: readonlyAll,
              name: 'file',
              listType: 'picture-card',
              // className: 'upload-list-inline',
              onChange: (info) => {
                handleUploadFiles(info);
              },
              headers: {
                Authorization: `Bearer ${token}`,
              },
              async onPreview(file: any) {
                if (
                  file.status === 'done' &&
                  file.response &&
                  file.response.data &&
                  file.response.data.ossId
                ) {
                  const type = getFileTypeByName(file.response.data.fileName);
                  if (type === 'Image') {
                    const d = await getFileData(file.response.data.ossId);
                    setIsShowFileData({
                      name: file.response.data.fileName,
                      url: d,
                      ossId: file.response.data.ossId,
                    });
                    setIsShowFileView(true);
                  } else {
                    downloadFile(
                      file.response.data.ossId,
                      file.response.data.fileName
                    );
                  }
                }
              },
            }}
            action={uploadFiles}
            wrapperCol={{
              span: 24,
            }}
            extra={
              readonlyAll || readonly
                ? ''
                : '注意：1、支持上传Excel、Word、PDF格式文件；2、每份文件不得超过10M；3、最多支持上传10份文件；'
            }
          >
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </div>
          </ProFormUploadButton>
        </ProForm>
      </BlockContainer>
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
      {!hideRank && (
        <div className="mt-4">
          <BlockContainer title="评分规则">
            <ProForm
              form={false}
              readonly={readonly || readonlyAll}
              formRef={formRef2}
              layout="horizontal"
              grid={true}
              submitter={false}
              initialValues={{
                remarks: null,
                auto: 'Y',
                sampleScore: null,
                docScore: null,
                refType: '0',
                qualifiedMinScore: 60,
                scoreCount: 100,
              }}
              onValuesChange={(changeValue: any, values: any) => {
                for (const key in values) {
                  if (typeof values[key] === 'string') {
                    values[key] = values[key].trim();
                  }
                }
                // 计算总得分
                if ('sampleScore' in changeValue) {
                  values.docScore = 100 - (values.sampleScore || 0);
                }
                if ('docScore' in changeValue) {
                  values.sampleScore = 100 - (values.docScore || 0);
                }

                formRef2.current?.setFieldsValue(values);
              }}
            >
              <ProFormTextArea
                name="remarks"
                label="评分规则说明"
                placeholder="请输入评分规则"
                rules={[{ required: true, message: '请输入评分规则' }]}
              />
              <ProFormDigit
                {...layoutProps}
                name="sampleScore"
                label="样品得分"
                max={100}
                min={0}
                rules={[{ required: true, message: '请输入样品得分' }]}
              />
              <ProFormDigit
                {...layoutProps}
                name="docScore"
                max={100}
                min={0}
                label="资料得分"
                rules={[{ required: true, message: '请输入资料得分' }]}
              />
              <ProFormText
                {...layoutProps}
                disabled
                label="总分"
                name="scoreCount"
              />
              <ProFormDigit
                {...layoutProps}
                rules={[{ required: true, message: '请输入及格分数' }]}
                name="qualifiedMinScore"
                label="及格分数"
              />
              <ProFormDependency name={['qualifiedMinScore']}>
                {({ qualifiedMinScore }) => {
                  return (
                    <ProFormDigit
                      {...layoutProps}
                      rules={[{ required: true, message: '请输入优秀分数' }]}
                      name="fineMinScore"
                      min={qualifiedMinScore}
                      label="优秀分数"
                    />
                  );
                }}
              </ProFormDependency>
              <ProFormRadio.Group
                name="auto"
                {...layoutProps}
                label="自动评分"
                rules={[{ required: true, message: '请选择是否启用自动评分' }]}
                options={[
                  {
                    label: '开启',
                    value: 'Y',
                  },
                  {
                    label: '关闭',
                    value: 'N',
                  },
                ]}
              />
              <ProFormSelect
                {...layoutProps}
                name="refType"
                options={[
                  { label: '文本', value: '0' },
                  { label: '数值', value: '1' },
                  { label: '区间', value: '2' },
                ]}
                label="合格样品参考值类型"
                rules={[
                  { required: true, message: '请选择合格样品参考值类型' },
                ]}
              />
            </ProForm>
          </BlockContainer>
        </div>
      )}

      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
    </>
  );
};

export default BaseForm;
