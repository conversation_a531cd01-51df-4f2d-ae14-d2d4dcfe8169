/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/*
 * @Date: 2024-07-18 15:56:25
 * @LastEditors: <PERSON><PERSON>hen
 * @LastEditTime: 2024-12-03 15:30:06
 * @FilePath: /xr-qc-jk-web/src/pages/Pathogen/StatisticalAnalysis/StrainReview/index.tsx
 * @Description: 菌株复核统计
 */
import { useEffect, useState } from 'react';
import { message } from 'antd';
import {
  pathogenListApi,
  strainReviewStatisticApi,
} from '@/api/statisticalAnalysis';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';

const StrainReview: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const { yearList } = useYearStore();

  const [exportParams, setExportParams] = useState<{
    strainId?: number;
  }>({});

  const [pathogenList, setPathogenList] = useState<
    {
      bacterId: number;
      bacterName: string;
      sampleCount: number;
      detectionCount: number;
      checkCount: number;
      unequalCount: number;
      equalCount: number;
    }[]
  >([]);

  const [statisticOptions, setStatisticOptions] = useState([
    { label: '菌（毒）株总数', value: 0, key: 'strainsCount' },
    { label: '已复核总数', value: 0, key: 'reviewedCount' },
    { label: '复核符合数', value: 0, key: 'isMatchedCount' },
    { label: '复核符合率', value: 0, key: 'isMatchedRate' },
    { label: '复核不符合数', value: 0, key: 'notMatchedCount' },
    { label: '复核不符合率', value: 0, key: 'notMatchedRate' },
  ]);

  const getStatistic = async () => {
    try {
      const { code, data, msg } = await strainReviewStatisticApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _statisticOptions = statisticOptions.map((_item) => ({
          ..._item,
          value: data[_item.key],
        }));
        setStatisticOptions(_statisticOptions);
        setPathogenList(data.strains);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getStatistic();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
      hideInSearch: true,
    },
    {
      title: '病原名称',
      dataIndex: 'strainName',
      hideInSearch: true,
    },
    {
      title: '样本采集数',
      dataIndex: 'sampleCollectionCount',
      hideInSearch: true,
    },
    {
      title: '检测完成数',
      dataIndex: 'completeCount',
      hideInSearch: true,
    },
    {
      title: '菌（毒）株数量',
      dataIndex: 'strainsCount',
      hideInSearch: true,
    },
    {
      title: '已复核总数',
      dataIndex: 'reviewedCount',
      hideInSearch: true,
    },
    {
      title: '复核符合数',
      dataIndex: 'isMatchedCount',
      hideInSearch: true,
    },
    {
      title: '复核符合率',
      dataIndex: 'isMatchedRate',
      hideInSearch: true,
    },
    {
      title: '复核不符合数',
      dataIndex: 'notMatchedCount',
      hideInSearch: true,
    },
    {
      title: '复核不符合率',
      dataIndex: 'notMatchedRate',
      hideInSearch: true,
    },

    // 查询
    {
      title: '计划年份',
      dataIndex: 'planYear',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      initialValue: new Date().getFullYear(),
    },
    {
      title: '采样日期',
      dataIndex: 'searchDateRange',
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()],
    },
  ];

  return (
    <>
      <PageContainer>
        <BlockContainer title="菌株复核总览" className=" mb-4">
          <div className=" grid grid-cols-6 gap-6 p-4">
            {statisticOptions.map((_item) => (
              <div
                className=" h-20 flex flex-col items-center justify-evenly border border-solid border-slate-400"
                key={_item.key}
              >
                <div>{_item.label}</div>
                <div>{_item.value}</div>
              </div>
            ))}
          </div>
        </BlockContainer>
        <BlockContainer title="复核统计">
          <ProTable
            columns={columns}
            cardBordered
            bordered
            request={async (params, sort, filter) => {
              const _params: any = {
                ...params,
                pageNum: params.current,
              };
              delete _params.current;

              if (_params.searchDateRange) {
                _params['startDate'] = _params?.searchDateRange[0];
                _params['endDate'] = _params?.searchDateRange[1];
              }

              delete _params.searchDateRange;

              delete _params.date;
              setExportParams(_params);
              const { code, data, msg } = await pathogenListApi(_params);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }
              return {
                data: data.rows ?? [],
                total: data.total ?? 0,
                success: true,
              };
            }}
            editable={{
              type: 'multiple',
            }}
            columnsState={{
              persistenceKey: 'pro-table-singe-demos',
              persistenceType: 'localStorage',
              defaultValue: {
                option: { fixed: 'right', disable: true },
              },
            }}
            rowKey="id"
            search={{
              defaultCollapsed: false,
              labelWidth: 80,
            }}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: pageSize,
              onShowSizeChange: (current, size) => {
                setPageSize(size);
              },
            }}
            dateFormatter="string"
            toolBarRender={() => [
              <DownloadButton
                url="/data/strainAnalyze/strains/strainsExport"
                params={exportParams}
                method="get"
              >
                导出统计结果
              </DownloadButton>,
            ]}
          />
        </BlockContainer>
      </PageContainer>
    </>
  );
};

export default StrainReview;
