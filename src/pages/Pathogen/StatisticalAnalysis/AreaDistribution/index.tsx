/*
 * @Date: 2024-07-18 16:10:19
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 10:24:13
 * @FilePath: \xr-qc-jk-web\src\pages\Pathogen\StatisticalAnalysis\AreaDistribution\index.tsx
 * @Description: 监测地区分布
 */
import { useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { cityListApi } from '@/api/common';
import { areaDistributionListApi } from '@/api/statisticalAnalysis';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { TAreaTable } from '../../ActiveStatisticalAnalysis/type';
import PositiveSample from '../PositiveSample';
import RePositiveSample from '../RePositiveSample';

const AreaDistribution: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [open, setOpen] = useState(false);
  const [openRe, setOpenRe] = useState(false);
  const [curSelectRow, setCurSelectRow] = useState<TAreaTable>();

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'sampleYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '区域',
      key: 'cityId',
      dataIndex: 'cityName',
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          label: 'cityName',
          value: 'cityId',
        },
      },
      request: async () => {
        const { code, data, msg } = await cityListApi();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '检测完成数量',
      dataIndex: 'detectionCount',
      hideInSearch: true,
    },
    {
      title: '初检阳性数量',
      dataIndex: 'positiveCount',
      hideInSearch: true,
      render: (_, record) => (
        <div
          className=" w-full truncate text-[#1677ff] cursor-pointer"
          onClick={() => {
            setCurSelectRow(record);
            setOpen(true);
          }}
          title={record.positiveCount}
        >
          {record.positiveCount}
        </div>
      ),
    },
    {
      title: '复核阳性数量',
      dataIndex: 'rePositive',
      hideInSearch: true,
      render: (_, record) => (
        <div
          className=" w-full truncate text-[#1677ff] cursor-pointer"
          onClick={() => {
            setCurSelectRow(record);
            setOpenRe(true);
          }}
          title={record.rePositive}
        >
          {record.rePositive}
        </div>
      ),
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          setSearchParams(_params);
          const { code, data, msg } = await areaDistributionListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/spreadAnalyze/areaInfo/export"
            params={searchParams}
            method="get"
          >
            导出统计结果
          </DownloadButton>,
        ]}
      />
      {/* 初检阳性样本 */}
      <Drawer
        width="80%"
        title="初检阳性样本"
        onClose={() => {
          setOpen(false);
          setCurSelectRow(undefined);
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <PositiveSample statisticArea={curSelectRow} />
      </Drawer>
      {/* 复核阳性样本 */}
      <Drawer
        width="80%"
        title="复核阳性样本"
        onClose={() => {
          setOpenRe(false);
          setCurSelectRow(undefined);
        }}
        open={openRe}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <RePositiveSample statisticArea={curSelectRow} />
      </Drawer>
    </PageContainer>
  );
};

export default AreaDistribution;
