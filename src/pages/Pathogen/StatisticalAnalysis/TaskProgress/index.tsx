/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { message } from 'antd';
import {
  taskStatisticApi,
  taskStatisticInfoApi,
} from '@/api/statisticalAnalysis';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';

const TaskProgress: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  const { yearList } = useYearStore();

  const [exportParams, setExportParams] = useState<{
    planId?: number;
  }>({});

  const [statisticOptions, setStatisticOptions] = useState([
    { label: '计划总数', value: 0, key: 'allCount' },
    { label: '执行中计划数', value: 0, key: 'processCount' },
    { label: '已完成计划数', value: 0, key: 'finishCount' },
  ]);

  const [taskList, setTaskList] = useState<
    {
      planId: number;
      planName: string;
    }[]
  >([]);

  const getStatistic = async () => {
    try {
      const { code, data, msg } = await taskStatisticApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _statisticOptions = statisticOptions.map((_item) => ({
          ..._item,
          value: data[_item.key],
        }));
        setStatisticOptions(_statisticOptions);
        setTaskList(data.plans);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getStatistic();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      initialValue: new Date().getFullYear(),
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
      hideInSearch: true,
    },
    {
      title: '监测哨点',
      dataIndex: 'sentinelName',
      hideInSearch: true,
    },
    {
      title: '监测样本要求数',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    {
      title: '样本采集数',
      dataIndex: 'saampleCollectionCount',
      hideInSearch: true,
    },
    {
      title: '样本采集率',
      dataIndex: 'sampleCollectionRate',
      hideInSearch: true,
    },
    {
      title: '检测完成数',
      dataIndex: 'completeCount',
      hideInSearch: true,
    },
    {
      title: '检测完成率',
      dataIndex: 'completionRate',
      hideInSearch: true,
    },
    {
      title: '菌（毒）株数量',
      dataIndex: 'strainsCount',
      hideInSearch: true,
    },
    {
      title: '已复核总数',
      dataIndex: 'reviewedCount',
      hideInSearch: true,
    },
    {
      title: '复核符合数',
      dataIndex: 'isMatchedCount',
      hideInSearch: true,
    },
    {
      title: '复核符合率',
      dataIndex: 'isMatchedRate',
      hideInSearch: true,
    },
    {
      title: '复核不符合数',
      dataIndex: 'notMatchedCount',
      hideInSearch: true,
    },
    {
      title: '复核不符合率',
      dataIndex: 'notMatchedRate',
      hideInSearch: true,
    },
  ];

  return (
    <>
      <PageContainer>
        <BlockContainer title="监测计划总览" className=" mb-4">
          <div className=" grid grid-cols-3 gap-6 p-4">
            {statisticOptions.map((_item) => (
              <div
                className=" h-20 flex flex-col items-center justify-evenly border border-solid border-slate-400"
                key={_item.key}
              >
                <div>{_item.label}</div>
                <div>{_item.value}</div>
              </div>
            ))}
          </div>
        </BlockContainer>
        <BlockContainer title="计划进度">
          <ProTable
            columns={columns}
            cardBordered
            bordered
            request={async (params, sort, filter) => {
              const _params: any = {
                ...params,
                pageNum: params.current ?? 1,
              };

              setExportParams(_params);
              const { code, data, msg } = await taskStatisticInfoApi(_params);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }

              return {
                data: data.rows ?? [],
                total: data.total ?? 0,
                success: true,
              };
            }}
            editable={{
              type: 'multiple',
            }}
            columnsState={{
              persistenceKey: 'pro-table-singe-demos',
              persistenceType: 'localStorage',
              defaultValue: {
                option: { fixed: 'right', disable: true },
              },
            }}
            rowKey="id"
            search={{
              defaultCollapsed: false,
              labelWidth: 80,
            }}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: pageSize,
              onShowSizeChange: (current, size) => {
                setPageSize(size);
              },
            }}
            dateFormatter="string"
            toolBarRender={() => [
              <DownloadButton
                url="/data/taskAnalyze/taskProgress/taskProgressExport"
                params={exportParams}
                method="get"
              >
                导出统计结果
              </DownloadButton>,
            ]}
          />
        </BlockContainer>
      </PageContainer>
    </>
  );
};

export default TaskProgress;
