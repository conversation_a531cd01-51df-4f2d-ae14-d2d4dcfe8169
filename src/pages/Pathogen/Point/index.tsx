/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测哨点主入口
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Tag } from 'antd';
import {
  getAllCityAreaList,
  getPathogenPointList,
  updatePathogenPoint,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
// 配置任务明细（机构考核）
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TPathogenPointProps = {};

// 市县级联数据
export const cascaderOptions = [
  {
    label: '某市',
    value: '111',
    children: [
      {
        label: '某县111',
        value: 'js',
      },
      {
        label: '某县222',
        value: 'ts',
      },
    ],
  },
];

const PathogenPoint: React.FC<TPathogenPointProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 市县区数据
  const [cityAreaList, setCityAreaList] = useState<any[]>([]);

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '哨点名称',
      dataIndex: 'monitorName',
    },
    {
      title: '行政区域',
      dataIndex: 'adminDivision',
      valueType: 'cascader',
      fieldProps: {
        options: cityAreaList,
      },
      render: (_, record) => (
        <span>
          {record?.monitorCity}/{record?.monitorCounty}
        </span>
      ),
    },
    {
      title: '联系人',
      dataIndex: 'relPeople',
    },
    {
      title: '联系电话',
      dataIndex: 'relPhone',
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
    },
    {
      title: '更新日期',
      dataIndex: 'updateTime',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        0: { text: '禁用' },
        1: { text: '启用' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Popconfirm
          title={
            record?.status === 0
              ? '请确认是否启用本监测点，启用后可为本监测点部署监测任务'
              : '请确认是否禁用本监测点，禁用后不可为本监测点部署监测任务'
          }
          onConfirm={() =>
            handleUpdatePathogenPointStatus(record?.id, record?.status)
          }
          okText="确定"
          cancelText="取消"
        >
          <Button size="small" type="link">
            {record?.status === 0 ? '启用' : '禁用'}
          </Button>
        </Popconfirm>,
        <Button
          type="link"
          size="small"
          key="updateRow"
          onClick={() => {
            setDetailId(record.id);
            setOpenEdit(true);
          }}
        >
          修改
        </Button>,
        <Popconfirm
          title="请确认是否删除本条数据，删除后数据无法找回"
          onConfirm={() => handleDeletePathogenPoint(record.id)}
          okText="确定"
          cancelText="取消"
          key="delRow"
        >
          <Button danger key="editable" size="small" type="text">
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 删除哨点
   */
  const handleDeletePathogenPoint = async (id: number | string) => {
    try {
      const { code, msg } = await updatePathogenPoint({
        id,
        isDeleted: 1,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 启用或禁用哨点
   */
  const handleUpdatePathogenPointStatus = async (
    id: number | string,
    curStatus: number
  ) => {
    const { code, data, msg } = await updatePathogenPoint({
      id,
      status: curStatus === 0 ? 1 : 0,
    });
    if (code !== codeDefinition.QUERY_SUCCESS) {
      message.error(msg);
      return;
    }
    message.success(codeDefinition.POST_DATA_SUCCESS);
    tableReload();
  };

  useEffect(() => {
    // 判断本地是否有市县区数据缓存存储，如果有则直接读取，否则从远程获取并转换
    queryCityAreaList();
  }, []);

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            name: null,
          };
          console.log('params:', params);

          // 如果查询了行政区域
          if (params?.adminDivision?.length) {
            const _monitorCityId = params?.adminDivision[0];
            const _monitorCountyId = params?.adminDivision[1];
            const _originAreaList = JSON.parse(
              sessionStorage.getItem('cityAreaOriginList')!
            );

            // 在区域数据集合中查询它们的 name
            const _monitorCity = _originAreaList?.find(
              (_item: any) => _item?.cityId === ~~_monitorCityId
            )?.cityName;

            const _monitorCounty = _originAreaList?.find(
              (_item: any) => _item?.areaId === ~~_monitorCountyId
            )?.areaName;

            param['monitorCity'] = _monitorCity;
            param['monitorCounty'] = _monitorCounty;
          }

          delete param.current;
          delete param.adminDivision;
          const { code, data, msg } = await getPathogenPointList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="监测哨点"
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setDetailId('');
              setOpenEdit(true);
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
      />
      {/* 新增 */}
      <Drawer
        width="60%"
        title={!detailId ? '新增哨点' : '编辑哨点'}
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit close={closeEdit} detailId={detailId} tableReload={tableReload} />
      </Drawer>
    </PageContainer>
  );
};
export default PathogenPoint;
