/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { createContext, useCallback, useEffect, useRef, useState } from 'react';
import { But<PERSON>, Col, message, Row, Space } from 'antd';
import { downloadFile, getFileData } from '@/api/file';
import { getQATask, taskDetailSample } from '@/api/quality';
import { codeDefinition } from '@/constants';
import {
  ProForm,
  ProFormDigit,
  ProFormSlider,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import DownloadButton from '@/components/DownloadButton';
import EFileView from '@/components/EFileView';
import FileView from '@/components/FileView';
import BaseForm from '@/pages/Examine/components/BaseForm';
import { getFileTypeByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId?: string;
};

const layoutProps = {
  colProps: { span: 8 },
};

interface SampleData {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  qaTaskSampleItemVoList: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
}

// 状态下拉
const statusOption = {
  0: { text: '待接收' },
  1: { text: '待检验' },
  2: { text: '待评判' },
  3: { text: '已评判' },
};

// 创建Context
export const TaskContext = createContext<any>({});

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  const [baseInfo, setBaseInfo] = useState<any>();
  // 考核结论-合格分
  const [qualifiedScore, setQualifiedScore] = useState<number>(60);
  // 考核结论-优秀分
  const [excellentScore, setExcellentScore] = useState<number>(80);
  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any[]>([]);
  const formRef = useRef<ProFormInstance>(null);
  const formRef2 = useRef<ProFormInstance>(null);

  // 考核任务类型  机构考核    盲样考核   随机考核
  const [taskType, setTaskType] = useState<string>('');

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '考核项目',
      dataIndex: 'itemName',
      key: 'itemName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record?.conditionThree ? record?.conditionThree : '无判定标准'}
        </span>
      ),
    },
  ];
  const columns2: ProColumns<Record<string, any>>[] = [
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
      key: 'assessedLabName',
      hideInSearch: true,
      width: 300,
    },
    // 机构/盲样
    ...(taskType !== '随机考核'
      ? [
          {
            title: '样本盒编号',
            dataIndex: 'sampleBox',
            key: 'sampleBox',
          },
        ]
      : []),
    // 随机
    ...(taskType === 'random'
      ? [
          {
            title: '样本类型',
            dataIndex: 'sampleName',
            key: 'sampleName',
          },
        ]
      : []),
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      key: 'sampleCode',
    },
    {
      title: '参考结果',
      dataIndex: 'sampleValue',
      key: 'sampleValue',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: statusOption,
      render: (item, record) => <span>{getStatusText(record.status)}</span>,
    },
    {
      title: '操作',
      dataIndex: 'opt',
      key: 'opt',
      width: '200px',
      render: (item, record) => {
        return record.receiptFile ? (
          <Space>
            <Button
              type="primary"
              size="small"
              loading={downloading === record.id}
              onClick={() => handlePreview(record)}
            >
              查看回执单
            </Button>
            <Button
              type="primary"
              size="small"
              loading={downloading === record.id}
              onClick={() => {
                onDownload(record);
              }}
            >
              下载回执单
            </Button>
          </Space>
        ) : (
          ''
        );
      },
    },
  ];

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = useCallback(async () => {
    try {
      if (detailId) {
        const { code, data, msg } = await getQATask(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          setBaseInfo(data);
          setSampleListData(data.qaTaskSampleVos);
          setTaskType(data.taskType);
          // 考核项目
          let qaTaskItemVos = '';
          const assessmentProject = data.qaTaskItemVos.filter(
            (item: any) => item.check === 1
          );
          assessmentProject.forEach((item: any) => {
            qaTaskItemVos = qaTaskItemVos + `${item.name} ${item.ratio}%； `;
          });
          const obj = {
            sampleScore: data.sampleScore,
            docScore: data.docScore,
            scoreCount: 100,
            slider: [data.qualifiedMinScore, data.fineMinScore],
            qaTaskItemVos: qaTaskItemVos,
          };
          formRef2.current?.setFieldsValue(obj);
          setQualifiedScore(data.qualifiedMinScore);
          setExcellentScore(data.fineMinScore);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [detailId]);
  /**
   * @TODO 状态值映射
   */
  const getStatusText = (status: number | any) => {
    switch (status) {
      case 0:
        return '待接收';
      case 1:
        return '待检验';
      case 2:
        return '待评判';
      case 3:
        return '已评判';
      default:
        return '未知状态';
    }
  };

  // 预览
  const [previewFile, setPreviewFile] = useState<any>('');
  const [openPreview, setOpenPreview] = useState<any>(false);
  const handlePreview = async (record: any) => {
    if (!record.receiptFile.includes('?')) {
      return;
    }
    setDownloading(record.id);
    const f = record.receiptFile.split('?');
    if (getFileTypeByName(f[1]) === 'Image') {
      const d = await getFileData(f[0]);
      setPreviewFile({
        url: d,
        name: f[1],
      });
      setOpenPreview('img');
    } else {
      const url = await getFileData(f[0]);
      setPreviewFile(url);
      setOpenPreview('pdf');
    }

    setDownloading('');
  };

  // 下载回执单
  const [downloading, setDownloading] = useState<any>('');
  const onDownload = async (record: any) => {
    if (!record.receiptFile.includes('?')) {
      return;
    }
    setDownloading(record.id);
    const f = record.receiptFile.split('?');
    downloadFile(f[0], record.assessedLabName + '.' + f[1].split('.')[1]);
    setTimeout(() => {
      setDownloading('');
    }, 3000);
  };

  useEffect(() => {
    getDetailData();
  }, []);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        {/* 基本信息 */}
        <BaseForm
          readonly={true}
          onRef={formRef}
          id={detailId}
          detailInfo={baseInfo}
          onSubmit={() => {}}
        />
        <BlockContainer title="任务配置333333333" className="mt-3">
          <ProForm
            formRef={formRef2}
            layout="horizontal"
            grid={true}
            submitter={false}
            validateTrigger="onBlur"
          >
            <ProFormDigit
              {...layoutProps}
              readonly={true}
              name="sampleScore"
              label="样品得分"
              max={100}
              min={0}
            />
            <ProFormDigit
              readonly={true}
              {...layoutProps}
              name="docScore"
              max={100}
              min={0}
              label="资料得分"
            />
            <ProFormText
              readonly={true}
              {...layoutProps}
              disabled
              label="总分"
              name="scoreCount"
            />
            <div className="relative flex h-[80px] w-full py-5">
              <ProFormSlider
                disabled
                name="slider"
                label="考核结论"
                marks={{
                  0: '0分',
                  100: '100分',
                }}
                range
                fieldProps={{
                  range: { draggableTrack: true },
                  max: 100,
                  min: 0,
                  className: 'w-[75%] flex-1',
                  tooltip: {
                    formatter: (value) => <span>{value}分</span>,
                    placement: 'bottom',
                    open: true,
                    color: '#1677FF',
                  },
                  styles: {
                    track: {
                      background: '#90C9FE',
                    },
                  },
                }}
              />
              <div className="text-[#FF3D00] w-[180px] absolute top-[25px] right-10">
                <div>优&nbsp;&nbsp;&nbsp;秀：{excellentScore} ≤ 得分</div>
                <div>
                  合&nbsp;&nbsp;&nbsp;格：{qualifiedScore} ≤ 得分 ＜
                  {excellentScore}
                </div>
                <div>不合格：&nbsp;0 ≤ 得分 ＜ {qualifiedScore}</div>
              </div>
            </div>
            <ProFormText
              readonly={true}
              disabled
              colProps={{ span: 24 }}
              label="考核项目"
              name="qaTaskItemVos"
            />
          </ProForm>
          <Row gutter={[25, 25]} className="mt-3">
            {sampleListData?.map((item: SampleData, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                >
                  <div className="h-[300px] overflow-auto overflow-x-hidden">
                    <ProForm<{ table: any[] }>
                      readonly={true}
                      layout="horizontal"
                      grid={true}
                      submitter={false}
                    >
                      <ProFormText
                        readonly={true}
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        label="参考结果"
                        fieldProps={{
                          value: item.value,
                        }}
                      />
                      <ProTable
                        key={`itemName${idx}`}
                        name="qaTaskSampleItemVoList"
                        className="w-full px-1"
                        toolBarRender={false}
                        options={false}
                        search={false}
                        bordered
                        columns={columns}
                        dataSource={item?.qaTaskSampleItemVoList}
                        pagination={false}
                      />
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
          </Row>
        </BlockContainer>
        <BlockContainer
          title="明细配置"
          bg="#F5F5F5"
          bodyStyle={{ padding: '10px' }}
          className="mt-3 border-[#D9D9D9]"
        >
          <ProTable
            key="id"
            className="w-full"
            cardBordered
            search={{
              defaultCollapsed: false,
              labelWidth: 90,
            }}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            editable={{
              type: 'multiple',
            }}
            columnsState={{
              persistenceKey: 'pro-table-singe-demos',
              persistenceType: 'localStorage',
              defaultValue: {
                option: { fixed: 'right', disable: true },
              },
            }}
            bordered
            columns={columns2}
            toolBarRender={() => [
              <Space className="">
                <DownloadButton
                  url="/qa/receipt-forms/downFile"
                  params={{
                    taskId: detailId,
                  }}
                  method="get"
                  type="default"
                >
                  下载全部回执单
                </DownloadButton>
                <DownloadButton
                  url="/system/taskDetailsOrg/exportOrgDataDetail"
                  params={{
                    id: detailId,
                    type: baseInfo?.taskType === 'random' ? 2 : 1,
                  }}
                  type="default"
                >
                  导出样品样本清单
                </DownloadButton>
              </Space>,
            ]}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: 10,
            }}
            request={async (params, sort, filter) => {
              const param = {
                ...params,
                id: detailId,
                pageNum: params.current,
                pageSize: params.pageSize,
              };
              delete param.current;
              const { code, rows, total, msg } = await taskDetailSample(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }

              return {
                data: rows ?? [],
                total: total ?? 0,
                success: true,
              };
            }}
            dateFormatter="string"
          />
        </BlockContainer>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button onClick={close} type="default">
          关闭
        </Button>
      </div>
      <EFileView
        open={openPreview === 'pdf'}
        close={() => setOpenPreview(false)}
        blobUrl={previewFile}
        previewType="pdf"
      />
      <FileView
        open={openPreview === 'img'}
        file={previewFile}
        closeDetail={() => {
          setOpenPreview(false);
        }}
      />
    </div>
  );
};

export default Detail;
