/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { CheckboxOptionType } from 'antd/lib';
import { getParentDept } from '@/api/department';
import {
  getAllCityAreaList,
  getPathogenDetailsById,
  getPathogenList,
  submitNewPathogenPoint,
  updatePathogenPoint,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import {
  ProForm,
  ProFormCascader,
  ProFormSelect,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { FormInitVal, formItemLayout, yearList } from '../data';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
};

const layoutProps = {
  colProps: { ...formItemLayout },
};

// 创建Context
export const TaskContext = createContext<any>({});

type DepartmentNode = {
  id: string | number;
  parentId: string | number;
  label: string;
  weight: number;
  children?: DepartmentNode[];
};

const TaskEdit: React.FC<TEditProps> = ({ close, detailId, tableReload }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  const [refType, setRefType] = useState('1');
  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();
  // 病原信息列表数据
  const [pathogenInfoList, setPathogenInfoList] =
    useState<CheckboxOptionType[]>();
  // 详情数据
  const [detailsData, setDetailsData] = useState<Record<string, any>>();

  // 部门列表数据
  const [deptListData, setDeptListData] = useState<any>();

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async (detailId: number | string) => {
    try {
      const { code, data, msg } = await getPathogenDetailsById({
        id: detailId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDetailsData(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 查询病原信息列表数据
   */
  const queryPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 拼装数据
      const _result: any[] = [];
      data?.rows.forEach((_item: any) => {
        _result.push({
          label: _item?.etiologyName,
          value: _item?.id,
        });
      });
      setPathogenInfoList(_result);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 提交表单数据
   */
  const handleSubmitEvent = async (values: Record<string, any>) => {
    setLoading(true);
    // 将行政区域选择的结果写入提交数据中
    if (values?.cityArea?.length) {
      values['monitorCityId'] = ~~values?.cityArea[0];
      values['monitorCountyId'] = ~~values?.cityArea[1];
    }
    // 从原始数据中获取选择市县区域名称
    const _cityAreaOriginList = JSON.parse(
      sessionStorage.getItem('cityAreaOriginList')!
    );
    if (!_cityAreaOriginList) {
      message.error('市县区域信息数据错误');
      return;
    }
    _cityAreaOriginList.forEach((_item: any) => {
      if (values['monitorCityId'] === _item?.cityId) {
        values['monitorCity'] = _item?.cityName;
      }
      if (values['monitorCountyId'] === _item?.areaId) {
        values['monitorCounty'] = _item?.areaName;
      }
    });
    // 将选择的信息数组平铺成字符串
    values['etiologyIds'] = values?.etiologyIds?.join(',');
    // 删除多余的信息
    delete values?.cityArea;

    // 根据 monitorId 从部门列表数据中获取 monitorName
    values['monitorName'] =
      findMonitorName(deptListData, values?.monitorId) ?? null;

    try {
      const { code, msg } = await submitNewPathogenPoint({ ...values });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      close();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 更新哨点
   */
  const handleUpdatePathogenPoint = async () => {
    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();

      // 将更新的参数中检测范围的 ArrayList 类型转换为字符串
      if (_formResult?.etiologyIds?.length) {
        _formResult.etiologyIds = _formResult?.etiologyIds.join(',');
      } else {
        _formResult.etiologyIds = '';
      }

      const { code, msg } = await updatePathogenPoint({
        id: detailId,
        ..._formResult,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      close();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 根据监测点ID在部门树中查找对应的监测点名称
   * @param tree 部门树结构数据
   * @param monitorId 监测点ID
   * @returns 返回监测点名称,如果未找到返回null
   */
  const findMonitorName = (
    tree: DepartmentNode[],
    monitorId: string | number
  ): string | null => {
    // 遍历树节点
    for (const node of tree) {
      // 如果找到匹配的ID,返回对应的label
      if (node.id === monitorId) {
        return node.label;
      }
      // 如果有子节点,递归查找
      if (node.children) {
        const result = findMonitorName(node.children, monitorId);
        if (result) {
          return result;
        }
      }
    }
    // 未找到返回null
    return null;
  };

  useEffect(() => {
    // 获取病原信息列表
    queryPathogenListData();
    queryCityAreaList();
  }, []);

  useEffect(() => {
    if (detailId) {
      getDetailData(detailId);
    }
  }, [detailId]);

  useEffect(() => {
    if (detailsData) {
      formRef.current.setFieldsValue({
        ...detailsData,
        cityArea: `${detailsData?.monitorCity}/${detailsData?.monitorCounty}`,
        etiologyIds: detailsData?.etiologyIds
          ?.split(',')
          .map((item: any) => parseInt(item)),
      });
    }
  }, [detailsData]);

  return (
    <div className="flex flex-col h-full w-full">
      <TaskContext.Provider value={{ refType, setRefType }}>
        <div className="flex-1 overflow-auto p-4">
          <BlockContainer>
            <ProForm
              formRef={formRef}
              rowProps={{
                gutter: [48, 0],
              }}
              layout="horizontal"
              grid={true}
              submitter={{
                render: (props, doms) => {
                  return [
                    <div className="h-[60px] w-full flex justify-center items-center bg-[#FFF] rounded mt-[-10px] relative z-1">
                      <Button
                        type="primary"
                        key="submit"
                        loading={loading}
                        onClick={() =>
                          detailId
                            ? handleUpdatePathogenPoint()
                            : props.form?.submit?.()
                        }
                      >
                        {!detailId ? '新增监测哨点' : '保存修改信息'}
                      </Button>
                    </div>,
                  ];
                },
              }}
              onFinish={handleSubmitEvent}
              initialValues={FormInitVal}
              onValuesChange={(_: any, values: any) => {
                // 当选择了市县区域信息时
                if (values?.cityArea?.length) {
                  values['monitorCityId'] = values?.cityArea[0];
                  values['monitorCountyId'] = values?.cityArea[1];
                }
              }}
            >
              <ProFormTreeSelect
                readonly={detailId ? true : false}
                name="monitorId"
                label="哨点名称"
                placeholder="请选择"
                rules={[{ required: true, message: '请选择' }]}
                request={async () => {
                  let result: any[] = [];
                  const { code, data, msg } = await getParentDept();
                  if (code === codeDefinition.QUERY_SUCCESS) {
                    result = [...data];
                  } else {
                    message.error(msg);
                  }
                  setDeptListData(result);
                  return result;
                }}
                fieldProps={{
                  fieldNames: { value: 'id' },
                  showSearch: true,
                  treeNodeFilterProp: 'label',
                }}
                {...layoutProps}
                labelCol={{ span: 6 }}
              />
              <ProFormCascader
                readonly={detailId ? true : false}
                name="cityArea"
                fieldProps={{ options: cityAreaList }}
                rules={[{ required: true, message: '请选择行政区域' }]}
                label="行政区域"
                {...layoutProps}
                labelCol={{ span: 6 }}
              />
              <ProFormText
                name="relPeople"
                label="联系人"
                placeholder="请输入哨点联系人"
                rules={[{ required: true, message: '请输入哨点联系人' }]}
                {...layoutProps}
                labelCol={{ span: 6 }}
              />
              <ProFormText
                name="relPhone"
                label="联系电话"
                placeholder="请输入哨点联系电话"
                rules={[{ required: true, message: '请输入哨点联系电话' }]}
                {...layoutProps}
                labelCol={{ span: 6 }}
              />
            </ProForm>
          </BlockContainer>
        </div>
      </TaskContext.Provider>
    </div>
  );
};

export default TaskEdit;
