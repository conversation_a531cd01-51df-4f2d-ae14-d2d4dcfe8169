/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-throw-literal */

/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Button, Drawer, message, Popconfirm, Tooltip } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { getTempList } from '@/api/temp';
import { codeDefinition } from '@/constants';
import { useTokenStore } from '@/store';
import { useQualityStore } from '@/store/quality';
import {
  ActionType,
  FormInstance,
  ProForm,
  ProFormCascader,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import { yearList } from '@/pages/Pathogen/Plan/data';
import { FormInitVal, formItemLayout } from '@/pages/Pathogen/Plan/data';
import { getIconByName } from '@/utils/upload';
import { getFileTypeByName } from '@/utils/upload';
import { TaskContext } from './Edit';

const layoutProps = {
  colProps: { ...formItemLayout },
  // labelCol: { span: 5 },
};
type TEditProps = {
  id?: any;
  detailInfo?: any;
  onRef?: any;
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isShowChar?: boolean; // 是否显示使用模板和任务类型字段
};

type Options = {
  label: string;
  value: string;
};

const BaseForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onRef,
  readonly = false,
  readonlyAll = false,
  isShowChar = true,
}) => {
  const actionRef = useRef<ActionType>();

  const { setRefType } = useContext(TaskContext);
  const [templateOnform, setTemplateOnform] = useState<any>([]);
  // 表单实例
  const formRef = useRef<FormInstance>(null);

  // 是否是草稿
  const [isDraft, setIsDraft] = useState<boolean>(false);

  // 考核类型ID
  const [assessmentTypeId, setAssessmentTypeId] = useState<string>('');
  const { token } = useTokenStore();
  // 获取table中需要的枚举
  const {
    assessmentTypesOnForm, //所有
    assessmentTypesOnForm2, // 带权限
    getAssessmentTypes,
    getAssessmentTaskTypes,
    getAssessmentTypesOnForm2,
  } = useQualityStore();

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSave,
      getTaskType: async () => {
        const task = await formRef.current?.getFieldValue('taskType');
        return task;
      },
      handleParams: async () => {
        const formDataInfo = formRef.current?.getFieldsValue();
        let attachmentIds = [];
        if (
          formDataInfo.attachmentIds &&
          formDataInfo.attachmentIds.length > 0
        ) {
          attachmentIds = formDataInfo.attachmentIds
            .filter(
              (item: any) =>
                item.response && item.response.data && item.response.data.ossId
            )
            .map((item: any) => {
              return {
                name: item.response.data.fileName,
                id: item.response.data.ossId,
              };
            });
        }
        return {
          ...formDataInfo,
          attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
        };
      },
      handleValidateFields: async () => {
        try {
          await formRef.current?.validateFields();
        } catch (error) {
          throw '请完善基本信息';
        }
      },
    };
  });

  // 新增的计划数据
  const [newPlanList, setNewPlanList] = useState<Record<string, any>[]>([
    {
      id: 1,
      name: '测试计划名称',
      pathogenName: '测试监测病原名称',
      remark: '',
    },
  ]);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    try {
      if (id && detailInfo) {
        const p = clone(detailInfo);
        // try {
        p.attachmentIds = p.attachmentIds
          ? JSON.parse(p.attachmentIds).map((item: any) => {
              return {
                uid: item.id,
                name: item.name,
                status: 'done',
                type: 'application/msword',
                url: item.id,
                response: {
                  data: {
                    fileName: item.name,
                    ossId: item.id,
                  },
                },
              };
            })
          : [];
        setIsDraft(detailInfo.status === '0');
        formRef.current?.setFieldsValue(p);
        setAssessmentTypeId(detailInfo.assessmentType);
      } else {
        formRef.current?.setFieldsValue({
          particularYear: new Date().getFullYear(),
        });
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 获取使用模板
   */
  const getTemplateOnform = async (templateTypeId: number | string) => {
    formRef.current?.setFieldValue('templateType', null);
    if (!templateTypeId) {
      setTemplateOnform([]);
      return;
    }
    try {
      const params = {
        assessmentType: templateTypeId,
        current: 1,
        size: 100,
        status: 1,
      };
      const { code, rows } = await getTempList(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        const newData = rows?.map((_item: any) => ({
          label: _item.name,
          value: _item.id,
          code: _item.code,
        }));
        setTemplateOnform(newData);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 新增/编辑
   */
  const handleSave = async () => {
    await formRef.current?.validateFields();
    let formInfo = cloneDeep(formRef.current?.getFieldsValue());
    // 模板名称-模板code
    const templateObj =
      templateOnform.filter(
        (_item: Options) => formInfo.templateId === _item.value
      )![0] || {};
    let attachmentIds = [];
    if (formInfo.attachmentIds && formInfo.attachmentIds.length > 0) {
      attachmentIds = formInfo.attachmentIds
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            id: item.response.data.ossId,
          };
        });
    }
    const params = {
      ...formInfo,
      templateName: templateObj.label,
      templateCode: templateObj.code,
      attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
    };
    return params;
  };
  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };

  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  useEffect(() => {
    getAssessmentTaskTypes();
    if (!readonly && !isDraft) {
      getAssessmentTypesOnForm2();
    } else {
      getAssessmentTypes();
    }
  }, []);

  useEffect(() => {
    assessmentTypeId && getTemplateOnform(assessmentTypeId);
  }, [assessmentTypeId]);

  return (
    <>
      <BlockContainer title="计划基本信息" className="mb-4">
        <ProForm
          readonly={readonly || readonlyAll}
          formRef={formRef}
          {...formItemLayout}
          layout="horizontal"
          grid={true}
          submitter={false}
          initialValues={FormInitVal}
          //@ts-ignore
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormText
            readonly={readonly || isDraft}
            name="name"
            label="计划名称"
            placeholder="请输入计划名称"
            rules={[{ required: true, message: '请输入计划名称' }]}
            {...layoutProps}
          />
          <ProFormSelect
            readonly={readonly || isDraft}
            options={yearList}
            rules={[{ required: true, message: '请选择年份' }]}
            name="particularYear"
            label="计划年份"
            {...layoutProps}
          />
          <ProFormText
            readonly
            name="people"
            label="创建人"
            placeholder="请输入创建人"
            {...layoutProps}
          />
          <ProFormDatePicker
            readonly={readonly}
            rules={[{ required: true, message: '请选择' }]}
            initialValue={dayjs()}
            name="endDate"
            label="计划截止日期"
            width={'lg'}
            {...layoutProps}
          />
          <ProFormTextArea
            readonly={readonly}
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.005 }}
            name="remark"
            label="备注信息"
          />
        </ProForm>
      </BlockContainer>
      <BlockContainer title="任务配置" className="mb-4">
        <div className=" pl-6">
          <ProForm
            readonly={readonly || readonlyAll}
            formRef={formRef}
            {...formItemLayout}
            layout="horizontal"
            grid={true}
            submitter={false}
            initialValues={FormInitVal}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <ProFormSelect
              options={yearList}
              rules={[{ required: true, message: '请选择监测哨点' }]}
              name="particularYear"
              label="监测哨点"
              colProps={{
                span: 6,
              }}
            />
            <ProFormSelect
              options={yearList}
              rules={[{ required: true, message: '请选择监测病原' }]}
              name="particularYear"
              label="监测病原"
              colProps={{
                span: 6,
              }}
              fieldProps={{
                mode: 'multiple',
              }}
            />
            <Button type="primary">新增任务</Button>
          </ProForm>
        </div>
        <ProTable
          columns={[
            {
              dataIndex: 'index',
              valueType: 'indexBorder',
              width: 48,
            },
            {
              title: '监测哨点',
              dataIndex: 'name',
            },
            {
              title: '监测病原',
              dataIndex: 'pathogenName',
            },
            {
              title: '备注',
              dataIndex: 'remark',
            },
            {
              title: '操作',
              valueType: 'option',
              key: 'option',
              width: 160,
              render: (text, record, _, action) => [
                <Popconfirm
                  title="确认删除？"
                  onConfirm={() => {}}
                  okText="确定"
                  cancelText="取消"
                  key="delpro"
                >
                  <Button danger key="editable" size="small" type="text">
                    删除
                  </Button>
                </Popconfirm>,
              ],
            },
          ]}
          actionRef={actionRef}
          rowKey="id"
          search={false}
          dataSource={newPlanList}
          options={false}
        />
      </BlockContainer>
    </>
  );
};

export default BaseForm;
