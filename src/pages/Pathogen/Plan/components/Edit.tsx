/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm, Space } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import {
  getMonitorProjectList,
  getPathogenList,
  getPathogenListByProjectId,
  getPathogenPlanDetail,
  getPathogenPointList,
  submitNewPathogenPlan,
  updatePathogenPlan,
} from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { commonYesNoSelectEnums } from '@/enums';
import { useInfoStore, useTokenStore, useYearStore } from '@/store';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  EditableProTable,
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import { formItemLayout } from '../data';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
  querathogenPlanCount: () => void;
};

const layoutProps = {
  colProps: { ...formItemLayout },
};

// 创建Context
export const TaskContext = createContext<any>({});

const TaskEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  querathogenPlanCount,
}) => {
  const { token } = useTokenStore();
  const { userInfo } = useInfoStore();
  const { yearList } = useYearStore();

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  // 哨点列表
  const [pathogenPointList, setPathogenPointList] =
    useState<Record<string, any>[]>();
  // 病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();
  // 需要提交的数据中的计划任务列表数据
  const [monitorTaskList, setMonitorTaskList] = useState<Record<string, any>[]>(
    []
  );
  // 计划详情数据
  const [planTaskDetails, setPlanTaskDetails] = useState<Record<string, any>>();

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
    [].map((item: any) => item.id)
  );
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  // 监测项目数据列表
  const [monitorProjectList, setMonitorProjectList] = useState<
    Record<string, any>[]
  >([]);

  // 当前选择的监测项目ID
  const [curSelectedMonitorProjectId, setCurSelectedMonitorProjectId] =
    useState<string>();

  // 当前选择的监测项目下的监测病原列表数据
  const [curMonitorPathogenList, setCurMonitorPathogenList] =
    useState<Record<string, any>[]>();

  /**
   * 获取哨点列表
   */
  const getPathogenPointListData = async () => {
    try {
      const { code, data, msg } = await getPathogenPointList({
        pageNum: 1,
        pageSize: 999,
        status: 1,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenPointList(data?.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取病原列表
   */
  const getPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data?.rows?.length ? data?.rows : data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 添加新的任务到计划任务列表数据
   */
  const addNewItemToTaskList = async () => {
    // 获取当前表单对象
    const _formResult = formRef?.current?.getFieldsValue();
    // 判断是否已选择值
    if (!_formResult?.selectedPoint || !_formResult?.selectedPathogenIds) {
      return;
    }

    setMonitorTaskList([
      {
        _id: new Date().getTime(),
        sentinelId: _formResult?.selectedPoint,
        sentinelName: pathogenPointList?.find(
          (_i: any) => _i?.id === _formResult?.selectedPoint
        )?.monitorName,
        projectId: _formResult?.selectedProject,
        projectName: monitorProjectList?.find(
          (_i: any) => _i?.projectId === _formResult?.selectedProject
        )?.projectName,
        sampleNum: _formResult?.sampleNum,
        etiologyIds: _formResult?.selectedPathogenIds.join(','),
        etiologyNames: _formResult?.selectedPathogenIds
          ?.map(
            (_i: string) =>
              curMonitorPathogenList?.find((_j) => _j?.value === _i)?.label
          )
          .join(','),
        multiEtiology: _formResult?.multiEtiology,
      },
      ...monitorTaskList,
    ]);
    // 清空之前的表单填写
    formRef.current?.setFieldsValue({
      selectedPoint: null,
      selectedProject: null,
      sampleNum: null,
      selectedPathogenIds: null,
      multiEtiology: null,
    });
  };

  /**
   * 从计划任务列表中删除项目
   */
  const deleteTaskFromTaskList = async (id: number) => {
    if (!monitorTaskList?.length) return;
    setMonitorTaskList(monitorTaskList?.filter((_i: any) => _i?.id !== id));
  };

  /**
   * 保存并提交或保存为草稿
   * 通过参数区别两者
   * planStatus   状态(0-草稿，1-执行中，2-已结束)
   * isStart    是否启动(0-禁用，1-启动)
   */
  const handleSavePlan = async (isDraft: number) => {
    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();
      delete _formResult?.selectedPathogenIds;
      delete _formResult?.selectedPoint;

      const _monitorTaskList: any[] = [];
      monitorTaskList?.forEach((_item) => {
        delete _item?._id;
        _monitorTaskList.push(_item);
      });

      // 处理上传附件
      if (_formResult?.file?.length) {
        if (_formResult?.file[0]?.response?.code === 200) {
          _formResult['planAttachmentList'] = [
            _formResult?.file[0]?.response?.data,
          ];
        }
        delete _formResult?.file;
      }

      const _params = {
        planStatus: isDraft,
        createUser: userInfo?.user?.userName,
        monitorTaskList: _monitorTaskList,
        ..._formResult,
      };

      const { code, msg } = await submitNewPathogenPlan(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      querathogenPlanCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 编辑保存
   * @param isDraft
   * @returns
   */
  const handleUpdatePlan = async (isDraft: number) => {
    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();
      delete _formResult?.selectedPathogenIds;
      delete _formResult?.selectedPoint;

      const _monitorTaskList: any[] = [];
      monitorTaskList?.forEach((_item) => {
        delete _item?.key;
        _monitorTaskList.push(_item);
      });

      // 处理上传附件
      if (_formResult?.file?.length) {
        if (_formResult?.file[0]?.response?.code === 200) {
          _formResult['planAttachmentList'] = [
            _formResult?.file[0]?.response?.data,
          ];
        }
        if (_formResult?.file?.uid) {
          _formResult['planAttachmentList'] = _formResult?.file?.uid;
        }
        delete _formResult?.file;
      }

      const { code, msg } = await updatePathogenPlan({
        id: detailId,
        planStatus: isDraft,
        createUser: userInfo?.user?.userName,
        monitorTaskList: monitorTaskList,
        ..._formResult,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      querathogenPlanCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 通过选中的多个病原id获取其名称并拼装字符串返回
   */
  const _getPathogenNamesString = (ids: any[]) => {
    if (!ids?.length || !pathogenList?.length) return;
    const _result: string[] = [];
    ids?.forEach((_id: any) => {
      const _pathogen = pathogenList?.find((_i: any) => _i?.id === _id);
      if (_pathogen) {
        _result.push(_pathogen?.etiologyName);
      }
    });
    return _result?.join(',');
  };

  /**
   * 获取详情
   */
  const queryPlanTaskDetails = async () => {
    try {
      const { code, data, msg } = await getPathogenPlanDetail({ id: detailId });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPlanTaskDetails(data);
      // 重新配置任务列表
      const _tasks: Record<string, any>[] = [];
      if (data?.tasks?.length) {
        data?.tasks?.forEach((_item: any) => {
          _tasks.push({
            ..._item,
            _id: Math.random() * 1000000000 + '',
          });
        });
        setMonitorTaskList(_tasks);
      }

      // 通过其他接口获取附件列表
      const _id = data?.id;
      if (_id && _id !== 'null') {
        const { code, data, msg } = await fileGroupApi({ businessId: _id });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.url,
          });
        });
        formRef?.current?.setFieldValue('file', _result);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };

  /**
   * 文件预览
   */
  const handleUploadPreview = async (file: any) => {
    if (
      file.status === 'done' &&
      file.response &&
      file.response.data &&
      file.response.data.ossId
    ) {
      const type = getFileTypeByName(file.response.data.fileName);
      if (type === 'Image') {
        const d = await getFileData(file.response.data.ossId);
        setIsShowFileData({
          name: file.response.data.fileName,
          url: d,
          ossId: file.response.data.ossId,
        });
        setIsShowFileView(true);
      } else {
        downloadFile(file.response.data.ossId, file.response.data.fileName);
      }
    }
  };

  /**
   *  获取监测项目列表数据
   */
  const queryMonitorProjectList = async () => {
    try {
      const { code, data, msg } = await getMonitorProjectList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setMonitorProjectList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 根据选择的监测项目获取该项目下的病原列表
   */
  const queryPathogenListByProjectId = async (projectId: string) => {
    try {
      const { code, data, msg } = await getPathogenListByProjectId(projectId);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _data: Record<string, any> = [];
      // 需要默认全部选中
      const _selectedKeys: string[] = [];
      data?.forEach((_item: Record<string, any>) => {
        _data.push({
          label: _item?.etiologyName,
          value: _item?.etiologyId,
        });
        _selectedKeys.push(_item?.etiologyId);
      });
      setCurMonitorPathogenList(_data as any);
      formRef.current.setFieldValue('selectedPathogenIds', _selectedKeys);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    // 获取哨点列表
    getPathogenPointListData();
    // 获取病原列表
    getPathogenListData();
    // 获取监测项目列表数据
    queryMonitorProjectList();
  }, []);

  useEffect(() => {
    if (detailId) {
      queryPlanTaskDetails(); // 获取详情
      // queryPathogenTaskDetailsByPlanId(); // 格努计划 id 获取任务列表数据
    }
  }, [detailId]);

  useEffect(() => {
    if (planTaskDetails) {
      formRef.current.setFieldsValue({ ...planTaskDetails });
    }
  }, [planTaskDetails]);

  useEffect(() => {
    const _keys: any[] = [];
    monitorTaskList?.forEach((_item: any) => {
      _keys.push(_item?._id);
    });
    setEditableRowKeys(_keys);
  }, [monitorTaskList]);

  // 新建&编辑时当前选择的监测项目ID发生变化
  useEffect(() => {
    if (curSelectedMonitorProjectId) {
      queryPathogenListByProjectId(curSelectedMonitorProjectId);
    } else {
      setCurMonitorPathogenList([]);
    }
  }, [curSelectedMonitorProjectId]);

  return (
    <>
      <div className="flex flex-col h-full w-full">
        <div className="flex-1 p-4 overflow-x-hidden">
          <ProForm
            onFinish={() => handleUpdatePlan(1)}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={false} //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="计划基本信息">
              <ProFormText
                readonly={detailId ? true : false}
                name="planName"
                label="计划名称"
                placeholder="请输入计划名称"
                rules={[{ required: true, message: '请输入计划名称' }]}
                {...layoutProps}
              />
              <ProFormSelect
                readonly={detailId ? true : false}
                name="planYear"
                options={yearList}
                rules={[{ required: true, message: '请选择年份' }]}
                label="计划年份"
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="people"
                label="创建人"
                placeholder="请输入创建人"
                fieldProps={{
                  value: userInfo?.user?.userName,
                }}
                {...layoutProps}
              />
              <ProFormDatePicker
                name="planStopDate"
                rules={[{ required: true, message: '请选择' }]}
                initialValue={dayjs()}
                label="计划截止日期"
                width={'lg'}
                {...layoutProps}
              />
              <ProFormTextArea
                name="remark"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                label="备注信息"
              />
              <ProFormUploadButton
                name="file"
                label="计划附件"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  name: 'file',
                  listType: 'picture-card',
                  onChange: (info) => {
                    handleUploadFiles(info);
                  },
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      const d = await getFileData(file.response.data.ossId);
                      if (type === 'Image') {
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        if (file.response.data.url) {
                          setFileUrl(file.response.data.url);
                          setOpenPreview(true);
                        } else {
                          downloadFile(
                            file.response.data.ossId,
                            file.response.data.fileName
                          );
                        }
                      }
                    } else if (file?.name && file?.uid && file?.url) {
                      setFileUrl(file?.url);
                      setOpenPreview(true);
                    }
                  },
                }}
                action={uploadFiles}
                wrapperCol={{
                  span: 24,
                }}
              >
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              </ProFormUploadButton>
            </EProFormGroup>
            <EProFormGroup title="任务配置">
              <div className="w-full  flex flex-col flex-nowrap">
                <div className="w-full flex-1 flex flex-row flex-nowrap items-center gap-8">
                  <div className="flex-1">
                    <ProFormSelect
                      name="selectedPoint"
                      options={pathogenPointList}
                      label="监测哨点"
                      fieldProps={{
                        fieldNames: { label: 'monitorName', value: 'id' },
                        showSearch: true,
                      }}
                    />
                  </div>
                  <div className="flex-1">
                    <ProFormSelect
                      name="selectedProject"
                      options={monitorProjectList}
                      label="监测项目"
                      fieldProps={{
                        fieldNames: {
                          label: 'projectName',
                          value: 'projectId',
                        },
                        showSearch: true,
                      }}
                      onChange={(value: any) =>
                        setCurSelectedMonitorProjectId(value)
                      }
                    />
                  </div>
                  <div className="flex-1">
                    <ProFormDigit label="监测样本数要求" name="sampleNum" />
                  </div>
                </div>
                <div className="w-full">
                  <ProFormCheckbox.Group
                    name="selectedPathogenIds"
                    label="监测病原"
                    options={curMonitorPathogenList as any}
                  />
                </div>
                <div className="w-full h-[56px] flex flex-row flex-nowrap justify-between items-center">
                  <div className="flex-1">
                    <ProFormRadio.Group
                      name="multiEtiology"
                      label="多病原监测"
                      options={commonYesNoSelectEnums}
                    />
                  </div>
                  <div className="flex-1 flex justify-end transform -translate-y-4">
                    <Button type="primary" onClick={addNewItemToTaskList}>
                      新增任务
                    </Button>
                  </div>
                </div>
              </div>
              <div className="w-full">
                <EditableProTable
                  columns={[
                    {
                      dataIndex: 'index',
                      valueType: 'indexBorder',
                      width: 48,
                    },
                    {
                      title: '监测哨点',
                      dataIndex: 'sentinelName',
                      editable: false,
                    },
                    {
                      title: '监测项目',
                      dataIndex: 'projectName',
                      editable: false,
                    },
                    {
                      title: '监测病原',
                      dataIndex: 'etiologyNames',
                      editable: false,
                    },
                    {
                      title: '监测样本数要求',
                      dataIndex: 'sampleNum',
                      editable: false,
                    },
                    {
                      title: '多病原监测',
                      dataIndex: 'multiEtiology',
                      editable: false,
                      render: (_, record: any) => (
                        <span>{record?.multiEtiology === 1 ? '是' : '否'}</span>
                      ),
                    },
                    {
                      title: '备注',
                      dataIndex: 'taskRemark',
                    },
                    {
                      title: '操作',
                      valueType: 'option',
                      key: 'option',
                      render: (text, record, _, action) => [
                        <Popconfirm
                          title="确认删除？"
                          onConfirm={() => {
                            deleteTaskFromTaskList(record?.id);
                          }}
                          okText="确定"
                          cancelText="取消"
                          key="delpro"
                        >
                          <Button
                            danger
                            key="editable"
                            size="small"
                            type="text"
                          >
                            删除
                          </Button>
                        </Popconfirm>,
                      ],
                    },
                  ]}
                  actionRef={actionRef}
                  rowKey="_id"
                  search={false}
                  value={monitorTaskList}
                  options={false}
                  recordCreatorProps={false} // 关闭默认的新建一行
                  editable={{
                    type: 'multiple',
                    editableKeys,
                    actionRender: (row, config, defaultDoms) => {
                      return [defaultDoms.delete];
                    },
                    onValuesChange: (record, recordList) => {
                      setMonitorTaskList([...recordList]);
                    },
                    onChange: setEditableRowKeys,
                  }}
                />
              </div>
            </EProFormGroup>
          </ProForm>
        </div>
        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Space>
            <Button
              type="default"
              loading={loading}
              onClick={() =>
                detailId ? handleUpdatePlan(0) : handleSavePlan(0)
              }
            >
              保存为草稿
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => handleUpdatePlan(1)}
            >
              启动监测计划
            </Button>
          </Space>
        </div>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </>
  );
};

export default TaskEdit;
