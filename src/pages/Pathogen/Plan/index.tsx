/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测计划主入口
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm } from 'antd';
import {
  deletePathogenPlan,
  getPathogenPlanCount,
  getPathogenPlanList,
  getPathogenPlanListWithPedingFinished,
  startPathogenPlan,
} from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TPathogenPointProps = {};

const PathogenPlan: React.FC<TPathogenPointProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 不同状态计划数量
  const [planStatusCount, setPlanStatusCount] = useState<Record<string, any>>();
  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('0');

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      disable: true,
      title: '计划年份',
      dataIndex: 'planYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '监测样本总数要求',
      dataIndex: 'taskSum',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
    },
    {
      title: '更新日期',
      dataIndex: 'updateTime',
      valueType: 'dateRange',
      render: (_, record) => <span>{record?.updateTime}</span>,
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="edit"
          onClick={() => {
            setDetailId(record.id);
            setOpenEdit(true);
          }}
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          key="detail"
          onClick={() => {
            setDetailId(record?.id);
            setOpenDetail(true);
          }}
        >
          详情
        </Button>,
        <Popconfirm
          title="确认是否删除本条数据，删除后数据无法找回?"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
          key="delpro"
        >
          <Button danger key="editable" size="small" type="text">
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  const columnsWithPendingFinished: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      disable: true,
      title: '计划年份',
      dataIndex: 'planYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '监测样本总数要求',
      dataIndex: 'taskSum',
      hideInSearch: true,
    },
    {
      title: '已采集样本数量',
      dataIndex: 'sampleGetNum',
      hideInSearch: true,
    },
    {
      title: '检测完成数量',
      dataIndex: 'detectionAchieveNum',
      hideInSearch: true,
    },
    {
      title: '检出阳性数量',
      dataIndex: 'checkSunNum',
      hideInSearch: true,
    },
    {
      title: '阳性检出率',
      dataIndex: 'sunCheckRate',
      hideInSearch: true,
      render: (_, record) => (
        <span>{record?.sunCheckRate ? `${record?.sunCheckRate}%` : -''}</span>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="detail"
          onClick={() => {
            setDetailId(record?.id);
            setOpenDetail(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];

  /**
   * @TODO 删除
   */
  const handleDelete = async (id: string) => {
    try {
      const { code, msg } = await deletePathogenPlan({ id });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * 启动计划
   */
  const handleStartPlan = async (id: string) => {
    try {
      const { code, msg } = await startPathogenPlan({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      // 更新数量
      querathogenPlanCount();
      tableReload();
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * 获取不同状态计划的数量
   */
  const querathogenPlanCount = async (params?: Record<string, any>) => {
    try {
      const { code, data, msg } = await getPathogenPlanCount(params || {});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPlanStatusCount(data);
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    // 获取不同状态计划的数量
    querathogenPlanCount();
  }, []);

  return (
    <PageContainer>
      {curStatus === '0' ? (
        <>
          <ProTable<QualityTaskItem>
            columns={columns}
            actionRef={actionRef}
            cardBordered
            bordered
            toolbar={{
              menu: {
                type: 'tab',
                activeKey: curStatus,
                items: [
                  {
                    key: '0',
                    label: <span>草稿({planStatusCount?.draftNum || 0})</span>,
                  },
                  {
                    key: '1',
                    label: <span>执行中({planStatusCount?.runNum || 0})</span>,
                  },
                  {
                    key: '2',
                    label: <span>已结束({planStatusCount?.endNum || 0})</span>,
                  },
                ],
                onChange: (key) => {
                  setCurStatus(key as any);
                },
              },
            }}
            request={async (params, sort, filter) => {
              const param: any = {
                ...params,
                planStatus: curStatus,
                pageNum: params.current,
                pageSize: params.pageSize,
              };
              delete param.current;

              if (curStatus !== null) {
                param['planStatus'] = curStatus;
              }

              // 如果有日期区间查询
              if (params?.updateTime?.length) {
                param['updateStartTime'] = params?.updateTime[0];
                param['updateEndTime'] = params?.updateTime[1];
              }

              delete param.updateTime;

              await querathogenPlanCount({ ...params });

              const { code, data, msg } = await getPathogenPlanList(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }

              return {
                data: data?.rows ?? [],
                total: data?.total ?? 0,
                success: true,
              };
            }}
            editable={{
              type: 'multiple',
            }}
            columnsState={{
              persistenceKey: 'pro-table-singe-demos',
              persistenceType: 'localStorage',
              defaultValue: {
                option: { fixed: 'right', disable: true },
              },
            }}
            rowKey="id"
            search={{
              defaultCollapsed: false,
              labelWidth: 70,
            }}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: pageSize,
              onShowSizeChange: (current, size) => {
                setPageSize(size);
              },
            }}
            dateFormatter="string"
            headerTitle="监测计划"
            toolBarRender={() => [
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  setDetailId('');
                  setOpenEdit(true);
                }}
                type="primary"
              >
                新增计划
              </Button>,
            ]}
          />
        </>
      ) : (
        <>
          <ProTable<QualityTaskItem>
            columns={columnsWithPendingFinished}
            actionRef={actionRef}
            cardBordered
            bordered
            toolbar={{
              menu: {
                type: 'tab',
                activeKey: curStatus,
                items: [
                  {
                    key: '0',
                    label: <span>草稿({planStatusCount?.draftNum || 0})</span>,
                  },
                  {
                    key: '1',
                    label: <span>执行中({planStatusCount?.runNum || 0})</span>,
                  },
                  {
                    key: '2',
                    label: <span>已结束({planStatusCount?.endNum || 0})</span>,
                  },
                ],
                onChange: (key) => {
                  setCurStatus(key as any);
                },
              },
            }}
            request={async (params, sort, filter) => {
              const param: any = {
                ...params,
                planStatus: curStatus,
                pageNum: params.current,
                pageSize: params.pageSize,
              };
              delete param.current;

              if (curStatus !== null) {
                param['planStatus'] = curStatus;
              }

              // 如果有日期区间查询
              if (params?.updateTime?.length) {
                param['updateStartTime'] = params?.updateTime[0];
                param['updateEndTime'] = params?.updateTime[1];
              }

              delete param.updateTime;

              await querathogenPlanCount({ ...params });

              const { code, data, msg } =
                await getPathogenPlanListWithPedingFinished(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }
              return {
                data: data?.rows ?? [],
                total: data?.total ?? 0,
                success: true,
              };
            }}
            editable={{
              type: 'multiple',
            }}
            columnsState={{
              persistenceKey: 'pro-table-singe-demos',
              persistenceType: 'localStorage',
              defaultValue: {
                option: { fixed: 'right', disable: true },
              },
            }}
            rowKey="id"
            search={{
              defaultCollapsed: false,
              labelWidth: 70,
            }}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: pageSize,
              onShowSizeChange: (current, size) => {
                setPageSize(size);
              },
            }}
            dateFormatter="string"
            headerTitle="监测计划"
            toolBarRender={() => [
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  setDetailId('');
                  setOpenEdit(true);
                }}
                type="primary"
              >
                新增计划
              </Button>,
            ]}
          />
        </>
      )}
      {/* 新增 */}
      <Drawer
        width="60%"
        title={!detailId ? '新增计划' : '编辑计划'}
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={tableReload}
          querathogenPlanCount={querathogenPlanCount}
        />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="60%"
        title="详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </PageContainer>
  );
};
export default PathogenPlan;
