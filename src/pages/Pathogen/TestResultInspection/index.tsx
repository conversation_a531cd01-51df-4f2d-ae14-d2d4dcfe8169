/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测结果巡查主入口
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Tag } from 'antd';
import { getTestResultReCheck } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import { useDictStore } from '@/store/dict';
import { ExportOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TTestResultInspectionProps = {};

const TestResultInspection: React.FC<TTestResultInspectionProps> = () => {
  const actionRef = useRef<ActionType>();
  const { yearList } = useYearStore();
  const { sampleNameEnums, getSampleNameEnums } = useDictStore();
  const [pageSize, setPageSize] = useState<number>(10);

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
    },
    {
      title: '监测哨点',
      dataIndex: 'sentinelName',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleNo',
    },
    {
      title: '样本名称',
      dataIndex: 'sampleName',
      valueType: 'select',
      fieldProps: {
        options: sampleNameEnums,
        showSearch: true,
      },
      render: (_, record) => <span>{record?.sampleNameCh}</span>,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleGetDate',
      hideInSearch: true,
    },
    {
      title: '样本归类',
      dataIndex: 'sampleTypeName',
      hideInSearch: true,
    },
    {
      title: '哨点医院',
      dataIndex: 'sentinelHospital',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="view"
          onClick={() => {
            setDetailId(record.id);
            setOpenDetail(true);
          }}
        >
          详情
        </Button>,
      ],
    },
  ];

  useEffect(() => {
    !sampleNameEnums?.length && getSampleNameEnums();
  }, []);

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { code, data, msg } = await getTestResultReCheck(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="检测结果巡查"
        toolBarRender={() => []}
      />

      {/* 详情 */}
      <Drawer
        width="60%"
        title="检测结果详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </PageContainer>
  );
};
export default TestResultInspection;
