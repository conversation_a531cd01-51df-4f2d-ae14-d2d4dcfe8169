/* eslint-disable react/jsx-no-target-blank */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { downloadFile, getFileData } from '@/api/file';
import {
  getPathogenTaskDetail,
  getPathogenTaskListByPlanId,
  getSampleDetailById,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import {
  ProDescriptions,
  ProForm,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId: string;
};

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formFileRef = useRef<any>(null);
  // 样本详情
  const [sampleDetailsData, setSampleDetailsData] =
    useState<Record<string, any>>();
  // 样本检测信息表格数据
  const [sampleDetectionInfoList, setSampleDetectionInfoList] = useState<
    Record<string, any>[]
  >([]);
  const [uploadedImagesList, setUploadedImagesList] = useState<string[]>([]);
  const [sampleTestReport, setSampleTestReport] =
    useState<Record<string, any>>();

  const [taskDetaiils, setTaskDetails] = useState<Record<string, any>>();

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getSampleDetailById({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetailsData(data);
      setSampleDetectionInfoList(data?.sampleDetectionInfoList);

      // 通过任务ID获取任务详情
      const _taskDetails = await getPathogenTaskDetail({
        id: data?.taskId,
      });
      setTaskDetails(_taskDetails?.data);

      // 通过其他接口获取附件列表
      const _id = data?.taskBasicInfo?.planId;
      if (_id && _id !== 'null') {
        fileGroupApi({ businessId: _id }).then(({ code, data, msg }) => {
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return;
          }
          const _result: any[] = [];
          data?.forEach((_item: any) => {
            _result.push({
              uid: _item?.ossId,
              name: _item?.originalName,
              url: _item?.url,
            });
          });
          formFileRef?.current?.setFieldValue('file', _result);
        });
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getDetailData(detailId);
    querySampleNameEnums();
    querySampleTypeEnums();
    querySampleSourceEnums();
    querySampleSeparateStatusEnums();
    queryAgeRangeListEnums();
    queryAgeUnitListEnums();
    queryJobListEnums();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4 h-full w-full p-4">
      <div className="flex flex-col h-full w-full gap-4 p-2">
        <BlockContainer title="任务基本信息">
          <ProDescriptions column={4}>
            <ProDescriptions.Item label="计划名称">
              {taskDetaiils?.planName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="计划年份">
              {taskDetaiils?.planYear}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="监测项目">
              {taskDetaiils?.sentinelName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="多病原监测">
              {taskDetaiils?.multiEtiology === 1
                ? '是'
                : taskDetaiils?.multiEtiology === 2
                ? '否'
                : '-'}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="计划备注信息">
              {taskDetaiils?.planRemark}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="任务备注信息">
              {taskDetaiils?.taskRemark}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="计划附件">
              <ProForm
                formRef={formFileRef}
                formKey="base-form-use-demo"
                layout="horizontal"
                grid={true}
                rowProps={{
                  gutter: [64, 0],
                  justify: 'space-between',
                }}
                submitter={false}
              >
                <ProFormUploadButton
                  readonly
                  name="file"
                  labelCol={{ flex: 0.005 }}
                  max={1}
                  fieldProps={{
                    iconRender: (file) => {
                      return (
                        <img
                          src={getIconByName(file.name)}
                          className="!w-[40px] !h-[40px] m-auto mt-2"
                          alt="logo"
                        />
                      );
                    },
                    name: 'file',
                    listType: 'picture-card',
                    async onPreview(file: any) {
                      if (
                        file.status === 'done' &&
                        file.response &&
                        file.response.data &&
                        file.response.data.ossId
                      ) {
                        const type = getFileTypeByName(
                          file.response.data.fileName
                        );
                        const d = await getFileData(file.response.data.ossId);
                        if (type === 'Image') {
                          setIsShowFileData({
                            name: file.response.data.fileName,
                            url: d,
                            ossId: file.response.data.ossId,
                          });
                          setIsShowFileView(true);
                        } else {
                          if (file.response.data.url) {
                            setFileUrl(file.response.data.url);
                            setOpenPreview(true);
                          } else {
                            downloadFile(
                              file.response.data.ossId,
                              file.response.data.fileName
                            );
                          }
                        }
                      } else if (file?.name && file?.uid && file?.url) {
                        setFileUrl(file?.url);
                        setOpenPreview(true);
                      }
                    },
                    disabled: true,
                  }}
                  action={''}
                  wrapperCol={{
                    span: 24,
                  }}
                />
              </ProForm>
            </ProDescriptions.Item>
          </ProDescriptions>
        </BlockContainer>
        <BlockContainer title="监测样本采样信息">
          <ProDescriptions column={4}>
            <ProDescriptions.Item label="样本编号">
              {sampleDetailsData?.sampleNo}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="样本名称">
              {
                sampleNameList?.find(
                  (_i) => _i?.dictValue === sampleDetailsData?.sampleName
                )?.dictLabel
              }
            </ProDescriptions.Item>
            <ProDescriptions.Item label="采样日期">
              {sampleDetailsData?.sampleGetDate}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="样本归类">
              {
                sampleTypeList?.find(
                  (_i) => _i?.dictValue === sampleDetailsData?.sampleType
                )?.dictLabel
              }
            </ProDescriptions.Item>
            <ProDescriptions.Item label="样本来源">
              {
                sampleSourceList?.find(
                  (_i) => _i?.dictValue === sampleDetailsData?.sampleSource
                )?.dictLabel
              }
            </ProDescriptions.Item>
            <ProDescriptions.Item label="来源详情">
              {sampleDetailsData?.sourceDetail}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="采样地区">
              {sampleDetailsData?.cityName}/{sampleDetailsData?.areaName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="哨点医院">
              {sampleDetailsData?.sentinelHospital}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="分离状态">
              {
                separateStatusList?.find(
                  (_i) => _i?.dictValue === sampleDetailsData?.separateState
                )?.dictLabel
              }
            </ProDescriptions.Item>
            <ProDescriptions.Item label="送检日期">
              {sampleDetailsData?.sendDate}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="收样日期">
              {sampleDetailsData?.receiveDate}
            </ProDescriptions.Item>
            {sampleDetailsData?.sampleType === '1' ? (
              <>
                <ProDescriptions.Item label="姓名">
                  {sampleDetailsData?.name}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="家长姓名">
                  {sampleDetailsData?.parentName}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="身份证号">
                  {sampleDetailsData?.idNo}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="联系电话">
                  {sampleDetailsData?.phone}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="出生日期">
                  {sampleDetailsData?.birthday}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="年龄">
                  {sampleDetailsData?.age}
                  {
                    ageUnitList?.find(
                      (_i) => _i?.dictValue === sampleDetailsData?.ageUnit
                    )?.dictLabel
                  }
                </ProDescriptions.Item>
                <ProDescriptions.Item label="年龄段">
                  {
                    ageRangeList?.find(
                      (_i) => _i?.dictValue === sampleDetailsData?.ageLevel
                    )?.dictLabel
                  }
                </ProDescriptions.Item>
                <ProDescriptions.Item label="性别">
                  {sampleDetailsData?.sex === '1' ? '男' : '女'}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="名族">
                  {sampleDetailsData?.nation}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="职业">
                  {
                    jobList?.find(
                      (_i) => _i?.dictValue === sampleDetailsData?.job
                    )?.dictLabel
                  }
                </ProDescriptions.Item>
                <ProDescriptions.Item label="病历号">
                  {sampleDetailsData?.patientNo}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="发病日期">
                  {sampleDetailsData?.attackDate}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="常住地">
                  {sampleDetailsData?.parentName}
                </ProDescriptions.Item>
                <ProDescriptions.Item label="详细地址">
                  {sampleDetailsData?.liveAddress}
                </ProDescriptions.Item>
              </>
            ) : null}
          </ProDescriptions>
        </BlockContainer>
        <BlockContainer title="样本检测信息">
          <ProTable
            columns={[
              {
                dataIndex: 'index',
                valueType: 'indexBorder',
                width: 48,
              },
              {
                title: '监测病原',
                dataIndex: 'etiologyName',
                ellipsis: true,
              },
              {
                title: '检测结果',
                dataIndex: 'resultName',
                ellipsis: true,
              },
              {
                title: '菌(毒)株编号',
                dataIndex: 'bacterialStrainNo',
                ellipsis: true,
              },
            ]}
            dataSource={sampleDetailsData?.detectionResult}
            rowKey="id"
            search={false}
            pagination={false}
            options={false}
          />
          <ProDescriptions column={4} className="px-6">
            <ProDescriptions.Item label="检测日期">
              {sampleDetailsData?.detectionResult[0]?.detectionDate}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="检测人员">
              {sampleDetailsData?.detectionResult[0]?.detectionReport}
            </ProDescriptions.Item>
          </ProDescriptions>
        </BlockContainer>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default Detail;
