/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { downloadFile, getFileData } from '@/api/file';
import { ossObjectApi } from '@/api/oss';
import { getStrainVerificationTaskDetail } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import {
  ProDescriptions,
  ProForm,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId?: string;
};

const layoutProps = {
  colProps: { span: 8 },
};

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formRef3 = useRef<any>(null);
  const formRef4 = useRef<any>(null);
  const formFileRef = useRef<any>(null);
  // 复核任务详情信息
  const [strainTaskDetails, setStrainTaskDetails] =
    useState<Record<string, any>>();
  // 样本检测信息表格数据
  const [sampleTestDataInfo, setSampleTestDataInfo] =
    useState<Record<string, any>[]>();

  // 任务详情
  const [taskDetailsData, setTaskDetailsData] = useState<Record<string, any>>();

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  /**
   * 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getStrainVerificationTaskDetail({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetailsData(data);

      // 处理复核的附件资源
      if (data?.reCheckSampleDetectionReport) {
        const result = await ossObjectApi(data?.reCheckSampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('file', _res);
      }

      // 通过其他接口获取附件列表
      const _id = data?.taskBasicInfo?.planId;
      if (_id && _id !== 'null') {
        fileGroupApi({ businessId: _id }).then(({ code, data, msg }) => {
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return;
          }
          const _result: any[] = [];
          data?.forEach((_item: any) => {
            _result.push({
              uid: _item?.ossId,
              name: _item?.originalName,
              url: _item?.url,
            });
          });
          formFileRef?.current?.setFieldValue('file', _result);
        });
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData(detailId);
    }
  }, [detailId]);

  useEffect(() => {
    querySampleNameEnums();
    querySampleTypeEnums();
    querySampleSourceEnums();
    querySampleSeparateStatusEnums();
    queryAgeRangeListEnums();
    queryAgeUnitListEnums();
    queryJobListEnums();
  }, []);

  return (
    <div className="flex flex-col h-full w-full gap-4 p-2">
      <BlockContainer title="任务基本信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="计划名称">
            {taskDetailsData?.taskBasicInfo?.planName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="计划年份">
            {taskDetailsData?.taskBasicInfo?.planYear}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="监测项目">
            {taskDetailsData?.taskBasicInfo?.projectName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="多病原监测">
            {taskDetailsData?.taskBasicInfo?.multiEtiology === 1 ? '是' : '否'}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="计划备注信息">
            {taskDetailsData?.taskBasicInfo?.planRemark}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="任务备注信息">
            {taskDetailsData?.taskBasicInfo?.taskRemark}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="计划附件">
            <ProForm
              formRef={formFileRef}
              formKey="base-form-use-demo"
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [64, 0],
                justify: 'space-between',
              }}
              submitter={false}
            >
              <ProFormUploadButton
                readonly
                name="file"
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  name: 'file',
                  listType: 'picture-card',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      const d = await getFileData(file.response.data.ossId);
                      if (type === 'Image') {
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        if (file.response.data.url) {
                          setFileUrl(file.response.data.url);
                          setOpenPreview(true);
                        } else {
                          downloadFile(
                            file.response.data.ossId,
                            file.response.data.fileName
                          );
                        }
                      }
                    } else if (file?.name && file?.uid && file?.url) {
                      setFileUrl(file?.url);
                      setOpenPreview(true);
                    }
                  },
                  disabled: true,
                }}
                action={''}
                wrapperCol={{
                  span: 24,
                }}
              />
            </ProForm>
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="监测样本采样信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="样本编号">
            {taskDetailsData?.sampleNo}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本名称">
            {
              sampleNameList?.find(
                (_i) => _i?.dictValue === taskDetailsData?.sampleName
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="采样日期">
            {taskDetailsData?.sampleGetDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本归类">
            {
              sampleTypeList?.find(
                (_i) => _i?.dictValue === taskDetailsData?.sampleType
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本来源">
            {
              sampleSourceList?.find(
                (_i) => _i?.dictValue === taskDetailsData?.sampleSource
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="来源详情">
            {taskDetailsData?.sourceDetail}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="采样地区">
            {taskDetailsData?.cityName}/{taskDetailsData?.areaName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="哨点医院">
            {taskDetailsData?.sentinelHospital}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="分离状态">
            {
              separateStatusList?.find(
                (_i) => _i?.dictValue === taskDetailsData?.separateState
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="送检日期">
            {taskDetailsData?.sendDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="收样日期">
            {taskDetailsData?.receiveDate}
          </ProDescriptions.Item>
          {taskDetailsData?.sampleType === '1' ? (
            <>
              <ProDescriptions.Item label="姓名">
                {taskDetailsData?.name}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="家长姓名">
                {taskDetailsData?.parentName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="身份证号">
                {taskDetailsData?.idNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="联系电话">
                {taskDetailsData?.phone}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="出生日期">
                {taskDetailsData?.birthday}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="年龄">
                {taskDetailsData?.age}
                {
                  ageUnitList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.ageUnit
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="年龄段">
                {
                  ageRangeList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.ageLevel
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="性别">
                {taskDetailsData?.sex === '1' ? '男' : '女'}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="名族">
                {taskDetailsData?.nation}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="职业">
                {
                  jobList?.find((_i) => _i?.dictValue === taskDetailsData?.job)
                    ?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="病历号">
                {taskDetailsData?.patientNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="发病日期">
                {taskDetailsData?.attackDate}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="常住地">
                {taskDetailsData?.parentName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="详细地址">
                {taskDetailsData?.liveAddress}
              </ProDescriptions.Item>
            </>
          ) : null}
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="菌（毒）株信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="菌（毒）株编号">
            {taskDetailsData?.bacterialStrainNo}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="病原体">
            {taskDetailsData?.etiologyName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="哨点单位">
            {taskDetailsData?.sentinelName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="检测日期">
            {taskDetailsData?.detectionDate}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="复核信息">
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="复核结果">
            {taskDetailsData?.reCheckResult === 1 ? '符合' : '不符合'}
          </ProDescriptions.Item>
        </ProDescriptions>
        {taskDetailsData?.reCheckResult === 0 ? (
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="不符合原因">
              {taskDetailsData?.reCheckRemark}
            </ProDescriptions.Item>
          </ProDescriptions>
        ) : null}
        <ProDescriptions column={3}>
          <ProDescriptions.Item label="复核单位">
            {taskDetailsData?.deptName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="复核日期">
            {taskDetailsData?.reCheckDate?.split(' ')[0]}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="复核人员">
            {taskDetailsData?.reDetectionPeople}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default Detail;
