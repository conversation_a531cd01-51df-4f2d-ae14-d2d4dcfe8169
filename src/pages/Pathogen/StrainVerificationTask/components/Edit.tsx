/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { downloadFile, getFileData } from '@/api/file';
import { ossObjectApi } from '@/api/oss';
import {
  getStrainVerificationTaskDetail,
  updateStrainVerificationTask,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { useInfoStore, useTokenStore } from '@/store';
import {
  ProDescriptions,
  ProForm,
  ProFormDatePicker,
  ProFormRadio,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import { formItemLayout } from '../data';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
  curUserInfo: Record<string, any>;
  queryStrainVerificationTaskCount: any;
};

const layoutProps = {
  colProps: { ...formItemLayout },
};

const TaskEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  curUserInfo,
  queryStrainVerificationTaskCount,
}) => {
  const { token } = useTokenStore();
  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const formRef3 = useRef<any>(null);
  const formRef4 = useRef<any>(null);
  const formFileRef = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { userInfo } = useInfoStore();
  // 任务详情
  const [taskDetailsData, setTaskDetailsData] = useState<Record<string, any>>();
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);
  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  // 复核图片数据集
  const [reCheckImgDataSource, setReCheckImgDataSource] = useState<
    Record<string, any>[]
  >([]);

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  // 是否显示不符合原因填写
  const [isResultMatch, setIsResultMatch] = useState<boolean>(true);

  /**
   * 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getStrainVerificationTaskDetail({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetailsData(data);
      setSampleDetectionDataSource(data?.sampleDetectionInfoList);

      // 如果是不符合的
      if (data?.reCheckResult === 0) {
        setIsResultMatch(false);
        formRef?.current?.setFieldValue('reCheckRemark', data?.reCheckRemark);
      }

      // 处理上传的图片资源
      // if (data?.sampleDetectionImgUrlList?.length) {
      //   try {
      //     const _id = data?.sampleDetectionImgUrlList[0];
      //     if (_id && _id !== 'null') {
      //       const result = await fileGroupApi({ businessId: _id });
      //       const _res: any[] = [];
      //       result?.data?.forEach((_i: any) => {
      //         _res.push({
      //           uid: _i?.ossId,
      //           name: _i?.originalName,
      //           url: _i?.url,
      //         });
      //       });
      //       formRef2.current?.setFieldValue('images', _res);
      //     } else {
      //       message.error('资源ID不存在');
      //     }
      //   } catch (err) {
      //     throw new Error(`Error: err`);
      //   } finally {
      //   }
      // }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef3.current?.setFieldValue('file', _res);
      }

      // 处理复核图片
      if (data?.reCheckSampleDetectionImgUrlList?.length) {
        const _id = data?.reCheckSampleDetectionImgUrlList[0];
        const result = await fileGroupApi({ businessId: _id });
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            ossId: _i?.ossId,
            fileName: _i?.originalName,
            size: _i?.fileSize,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('images', _res);
        setReCheckImgDataSource(_res);
      }

      // 处理复核的附件资源
      if (data?.reCheckSampleDetectionReport) {
        const result = await ossObjectApi(data?.reCheckSampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('file', _res);
      }

      // 设置表单信息
      // 设置复核结果值
      if (data?.reCheckResult === 1 || data?.reCheckResult === 0) {
        formRef.current?.setFieldValue('reCheckResult', data?.reCheckResult);
      }

      if (data?.reCheckDate) {
        formRef?.current?.setFieldValue(
          'reCheckDate',
          data?.reCheckDate.split(' ')[0]
        );
      }

      if (data?.reDetectionPeople) {
        formRef?.current?.setFieldValue(
          'reDetectionPeople',
          data?.reDetectionPeople
        );
      }

      // 设置复核单位值
      if (!data?.deptName) {
        formRef?.current?.setFieldValue('deptName', curUserInfo?.deptName);
      } else {
        formRef?.current?.setFieldValue('deptName', data?.deptName);
      }

      // 通过其他接口获取附件列表
      const _id = data?.taskBasicInfo?.planId;
      if (_id && _id !== 'null') {
        fileGroupApi({ businessId: _id }).then(({ code, data, msg }) => {
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return;
          }
          const _result: any[] = [];
          data?.forEach((_item: any) => {
            _result.push({
              uid: _item?.ossId,
              name: _item?.originalName,
              url: _item?.url,
            });
          });
          formFileRef?.current?.setFieldValue('file', _result);
        });
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * @TODO 保存填报信息
   */
  const handleSaveReCheck = async (isDraft: number) => {
    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();

      const _params = {
        id: detailId,
        status: isDraft,
        ..._formResult,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      close();
      tableReload();
      queryStrainVerificationTaskCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 提交填报信息
   */
  const handleSubmitReCheck = async (isDraft: number) => {
    await formRef?.current?.validateFields();

    setLoading(true);
    try {
      const _formResult = formRef?.current?.getFieldsValue();

      // 处理上传图片附件
      if (reCheckImgDataSource?.length) {
        _formResult['reCheckSampleDetectionImgUrl'] = [...reCheckImgDataSource];
      }

      // 处理上传报告附件
      if (_formResult?.reports?.length) {
        _formResult['reCheckSampleDetectionReport'] =
          _formResult['reports'][0]?.response?.data?.ossId;
        delete _formResult?.reports;
      }

      delete _formResult['images'];

      // 检查复核结果，如果为不符合，则需要检查不符合原因是否已填写
      if (_formResult?.reCheckResult === 0 && !_formResult?.reCheckRemark) {
        message.error('不符合原因是必填的');
        return;
      }

      if (typeof _formResult?.reCheckDate !== 'string') {
        _formResult.reCheckDate = dayjs(_formResult.reCheckDate).format(
          'YYYY-MM-DD'
        );
      }

      const _params = {
        id: detailId,
        status: isDraft,
        ..._formResult,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      close();
      tableReload();
      queryStrainVerificationTaskCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        file?.response?.msg && message.error(file?.response?.msg);
      } else {
        file?.response?.msg && message.success(file?.response?.msg);
        if (file?.status !== 'removed') {
          // 保存新上传的图片信息
          setReCheckImgDataSource([
            ...reCheckImgDataSource,
            {
              ossId: file?.response?.data?.ossId,
              fileName: file?.response?.data?.fileName,
              size: file?.size,
              url: file?.response?.data?.url,
            },
          ]);
        } else {
          // 移除检测图片
          const _removedImgId = file?.ossId;
          const _reCheckImgDataSource = [...reCheckImgDataSource];
          const _removedItemIndex = _reCheckImgDataSource?.findIndex(
            (_item) => _item?.ossId === _removedImgId
          );
          // 移除
          _reCheckImgDataSource.splice(_removedItemIndex, 1);
          setReCheckImgDataSource(_reCheckImgDataSource);
        }
      }
    }
  };

  // 文件预览
  const handleUploadPreview = async (file: any) => {
    if (
      file.status === 'done' &&
      file.response &&
      file.response.data &&
      file.response.data.ossId
    ) {
      const type = getFileTypeByName(file.response.data.fileName);
      if (type === 'Image') {
        const d = await getFileData(file.response.data.ossId);
        setIsShowFileData({
          name: file.response.data.fileName,
          url: d,
          ossId: file.response.data.ossId,
        });
        setIsShowFileView(true);
      } else {
        downloadFile(file.response.data.ossId, file.response.data.fileName);
      }
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData(detailId);
    }
  }, [detailId]);

  useEffect(() => {
    if (sampleDetectionDataSource?.length) {
      const _keys: any = [];
      sampleDetectionDataSource?.forEach((_item: any) => _keys.push(_item?.id));
      setEditableRowKeys(_keys);
    }
  }, [sampleDetectionDataSource]);

  useEffect(() => {
    querySampleNameEnums();
    querySampleTypeEnums();
    querySampleSourceEnums();
    querySampleSeparateStatusEnums();
    queryAgeRangeListEnums();
    queryAgeUnitListEnums();
    queryJobListEnums();
  }, []);

  return (
    <>
      <div className="flex flex-col h-full w-full">
        <div className="relative flex flex-col h-full w-full gap-4 p-2 overflow-x-hidden">
          <BlockContainer title="任务基本信息">
            <ProDescriptions column={4}>
              <ProDescriptions.Item label="计划名称">
                {taskDetailsData?.taskBasicInfo?.planName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="计划年份">
                {taskDetailsData?.taskBasicInfo?.planYear}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="监测项目">
                {taskDetailsData?.taskBasicInfo?.projectName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="多病原监测">
                {taskDetailsData?.taskBasicInfo?.multiEtiology === 1
                  ? '是'
                  : '否'}
              </ProDescriptions.Item>
            </ProDescriptions>
            <ProDescriptions column={1}>
              <ProDescriptions.Item label="计划备注信息">
                {taskDetailsData?.taskBasicInfo?.planRemark}
              </ProDescriptions.Item>
            </ProDescriptions>
            <ProDescriptions column={1}>
              <ProDescriptions.Item label="任务备注信息">
                {taskDetailsData?.taskBasicInfo?.taskRemark}
              </ProDescriptions.Item>
            </ProDescriptions>
            <ProDescriptions column={1}>
              <ProDescriptions.Item label="计划附件">
                <ProForm
                  formRef={formFileRef}
                  formKey="base-form-use-demo"
                  layout="horizontal"
                  grid={true}
                  rowProps={{
                    gutter: [64, 0],
                    justify: 'space-between',
                  }}
                  submitter={false}
                >
                  <ProFormUploadButton
                    readonly
                    name="file"
                    labelCol={{ flex: 0.005 }}
                    max={1}
                    fieldProps={{
                      iconRender: (file) => {
                        return (
                          <img
                            src={getIconByName(file.name)}
                            className="!w-[40px] !h-[40px] m-auto mt-2"
                            alt="logo"
                          />
                        );
                      },
                      name: 'file',
                      listType: 'picture-card',
                      async onPreview(file: any) {
                        if (
                          file.status === 'done' &&
                          file.response &&
                          file.response.data &&
                          file.response.data.ossId
                        ) {
                          const type = getFileTypeByName(
                            file.response.data.fileName
                          );
                          const d = await getFileData(file.response.data.ossId);
                          if (type === 'Image') {
                            setIsShowFileData({
                              name: file.response.data.fileName,
                              url: d,
                              ossId: file.response.data.ossId,
                            });
                            setIsShowFileView(true);
                          } else {
                            if (file.response.data.url) {
                              setFileUrl(file.response.data.url);
                              setOpenPreview(true);
                            } else {
                              downloadFile(
                                file.response.data.ossId,
                                file.response.data.fileName
                              );
                            }
                          }
                        } else if (file?.name && file?.uid && file?.url) {
                          setFileUrl(file?.url);
                          setOpenPreview(true);
                        }
                      },
                      disabled: true,
                    }}
                    action={''}
                    wrapperCol={{
                      span: 24,
                    }}
                  />
                </ProForm>
              </ProDescriptions.Item>
            </ProDescriptions>
          </BlockContainer>
          <BlockContainer title="监测样本采样信息">
            <ProDescriptions column={4}>
              <ProDescriptions.Item label="样本编号">
                {taskDetailsData?.sampleNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="样本名称">
                {
                  sampleNameList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.sampleName
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="采样日期">
                {taskDetailsData?.sampleGetDate}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="样本归类">
                {
                  sampleTypeList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.sampleType
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="样本来源">
                {
                  sampleSourceList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.sampleSource
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="来源详情">
                {taskDetailsData?.sourceDetail}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="采样地区">
                {taskDetailsData?.cityName}/{taskDetailsData?.areaName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="哨点医院">
                {taskDetailsData?.sentinelHospital}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="分离状态">
                {
                  separateStatusList?.find(
                    (_i) => _i?.dictValue === taskDetailsData?.separateState
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="送检日期">
                {taskDetailsData?.sendDate}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="收样日期">
                {taskDetailsData?.receiveDate}
              </ProDescriptions.Item>
              {taskDetailsData?.sampleType === '1' ? (
                <>
                  <ProDescriptions.Item label="姓名">
                    {taskDetailsData?.name}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="家长姓名">
                    {taskDetailsData?.parentName}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="身份证号">
                    {taskDetailsData?.idNo}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="联系电话">
                    {taskDetailsData?.phone}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="出生日期">
                    {taskDetailsData?.birthday}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="年龄">
                    {taskDetailsData?.age}
                    {
                      ageUnitList?.find(
                        (_i) => _i?.dictValue === taskDetailsData?.ageUnit
                      )?.dictLabel
                    }
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="年龄段">
                    {
                      ageRangeList?.find(
                        (_i) => _i?.dictValue === taskDetailsData?.ageLevel
                      )?.dictLabel
                    }
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="性别">
                    {taskDetailsData?.sex === '1' ? '男' : '女'}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="名族">
                    {taskDetailsData?.nation}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="职业">
                    {
                      jobList?.find(
                        (_i) => _i?.dictValue === taskDetailsData?.job
                      )?.dictLabel
                    }
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="病历号">
                    {taskDetailsData?.patientNo}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="发病日期">
                    {taskDetailsData?.attackDate}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="常住地">
                    {taskDetailsData?.parentName}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="详细地址">
                    {taskDetailsData?.liveAddress}
                  </ProDescriptions.Item>
                </>
              ) : null}
            </ProDescriptions>
          </BlockContainer>
          <BlockContainer title="菌（毒）株信息">
            <ProDescriptions column={4}>
              <ProDescriptions.Item label="菌（毒）株编号">
                {taskDetailsData?.bacterialStrainNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="病原体">
                {taskDetailsData?.etiologyName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="哨点单位">
                {taskDetailsData?.sentinelName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="检测日期">
                {taskDetailsData?.detectionDate}
              </ProDescriptions.Item>
            </ProDescriptions>
          </BlockContainer>
          <div className="w-full h-full flex flex-col">
            <ProForm
              onFinish={() => handleSubmitReCheck(2)}
              formRef={formRef}
              formKey="base-form-use-demo"
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [32, 0],
              }}
              submitter={false}
            >
              <div className=" flex-1 p-4 overflow-y-auto bg-white">
                <EProFormGroup title="复核信息">
                  <ProFormRadio.Group
                    name="reCheckResult"
                    label="复核结果"
                    options={[
                      { label: '符合', value: 1 },
                      { label: '不符合', value: 0 },
                    ]}
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={
                      formRef?.current?.getFieldValue('reCheckResult') === 0
                        ? { span: 8 }
                        : { span: 24 }
                    }
                    fieldProps={{
                      onChange: (e) =>
                        setIsResultMatch(e?.target?.value === 1 ? true : false),
                    }}
                  />
                  {!isResultMatch ? (
                    <ProFormText
                      name="reCheckRemark"
                      label=""
                      placeholder="请输入不符合原因"
                      colProps={{ span: 10 }}
                    />
                  ) : null}
                  <ProFormText
                    name="deptName"
                    label="复核单位"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={{ xl: 8, md: 12 }}
                    disabled
                    initialValue={curUserInfo?.deptName}
                  />
                  <ProFormDatePicker
                    name="reCheckDate"
                    label="复核日期"
                    initialValue={dayjs().format('YYYY-MM-DD')}
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={{ xl: 8, md: 12 }}
                    fieldProps={{
                      style: {
                        width: '100%',
                      },
                    }}
                  />
                  <ProFormText
                    name="reDetectionPeople"
                    label="复核人员"
                    placeholder="请输入名称"
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={{ xl: 8, md: 12 }}
                    initialValue={curUserInfo?.reDetectionPeople}
                  />
                </EProFormGroup>
              </div>
            </ProForm>
          </div>
        </div>
        <div className=" h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Space>
            <Button type="default" onClick={close}>
              取消
            </Button>
            <Button
              type="default"
              loading={loading}
              onClick={() => handleSaveReCheck(1)}
            >
              保存
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => handleSubmitReCheck(2)}
            >
              提交
            </Button>
          </Space>
        </div>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </>
  );
};

export default TaskEdit;
