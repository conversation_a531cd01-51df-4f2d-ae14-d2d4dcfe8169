/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测哨点主入口
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getPathogenTaskList } from '@/api/pathogen';
import { getMonitorItemList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TPlanTableProps = {
  setCurSelectedPlanInfo: (record: any) => void;
  curSelectedPlanInfo: Record<string, any>;
};

const PlanTable: React.FC<TPlanTableProps> = ({
  setCurSelectedPlanInfo,
  curSelectedPlanInfo,
}) => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);

  const { yearList } = useYearStore();

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  const columns: any = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      width: 80,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
      ellipsis: true,
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
      valueType: 'select',
      request: async (params: any) => {
        const { code, data, msg } = await getMonitorItemList({
          pageNum: 1,
          pageSize: 99999,
        });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        return data?.rows?.map((item: Record<string, any>) => ({
          label: item.name,
          value: item.name,
        }));
      },
      fieldProps: { showSearch: true },
    },
    {
      title: '监测病原',
      dataIndex: 'etiologyName',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '监测样本数要求',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    {
      title: '多病原监测',
      dataIndex: 'multiEtiology',
      hideInSearch: true,
      render: (_: any, record: any) => (
        <span>
          {record?.multiEtiology === 1
            ? '是'
            : record?.multiEtiology === 2
            ? '否'
            : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      key: 'option',
      width: 90,
      //@ts-ignore
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          key="view"
          onClick={() => setCurSelectedPlanInfo(record)}
        >
          选择任务
        </Button>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        size="small"
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            isAccept: 1, // 只查询已接收的任务数据
          };
          delete param.current;
          const { code, data, msg } = await getPathogenTaskList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          span: 12,
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={false}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        scroll={{ x: 'max-content' }}
        rowClassName={(record: any, index: number, indent: number) =>
          `${record?.id === curSelectedPlanInfo?.id ? 'bg-blue-100' : ''}`
        }
      />
    </PageContainer>
  );
};
export default PlanTable;
