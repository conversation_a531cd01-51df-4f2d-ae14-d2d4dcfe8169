/* eslint-disable array-callback-return */

/**
 *  检测中状态的填报页面
 */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, Divider, Input, message, Select, Space } from 'antd';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { downloadFile, getFileData } from '@/api/file';
import { ossObjectApi } from '@/api/oss';
import {
  getAllCityAreaList,
  getPathogenInfoByTask,
  getSampleById,
  submitDecteSampleData,
} from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { sampleDetectionResultEnums } from '@/enums';
import { useInfoStore, useTokenStore } from '@/store';
import { convertToCascading } from '@/utils';
import {
  EditableProTable,
  ProForm,
  ProFormCascader,
  ProFormDatePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import './index.less';

type TEditProps = {
  close: () => void;
  detailId?: string;
  curSelectedPlanInfo: Record<string, any>;
  tableReload: any;
  querySampleStatusCount: any;
};

const layoutProps = {
  colProps: { span: 6 },
  labelCol: { span: 7 },
};

// 创建Context
export const TaskContext = createContext<any>({});

const resultEnums = [
  { label: '阴性', value: '0' },
  { label: '阳性', value: '1' },
  { label: '未检测', value: '2' },
];

const FillIn: React.FC<TEditProps> = ({
  close,
  detailId,
  curSelectedPlanInfo,
  tableReload,
  querySampleStatusCount,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  const { userInfo } = useInfoStore();
  const { token } = useTokenStore();

  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);
  // 样本检测详情信息
  const [sampleDetectionDetails, setSampleDetectionDetails] =
    useState<Record<string, any>>();
  // 年龄段枚举值
  const [sampleAgeList, setSampleAgeList] = useState<Record<string, any>[]>();
  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 该计划任务的病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();

  // 图片数据集
  const [uploadImgDataSource, setUploadImgDataSource] = useState<
    Record<string, any>[]
  >([]);

  // 当前样本归类的选择
  const [isChoicePerson, setIsChoicePerson] = useState<boolean>(false);

  // 年龄单位的枚举值列表
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);

  // 当前选择的年龄单位
  const [curSelectedAgeUnit, setCurSelectedAgeUnit] = useState<string>('1');

  // 是否选择的阳性结果
  const [isChoicePositive, setIsChoicePositive] = useState<boolean>(false);

  // 是否为多病原监测,默认否
  const [isMultiEtiology, setIsMultiEtiology] = useState<boolean>(false);

  // 多病原数据集合
  const [multiEtiologyList, setMultiEtiologyList] = useState<any[]>([]);

  const [editableKey, setEditableKey] = useState<string>('');

  // 是否刷新
  const isRefresh = useRef(false);

  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  /**
   * 提交新建样本
   * @param isDraft
   * @returns
   */
  const handleSavePlan = async (isDraft: number) => {
    setLoading(true);
    try {
      // 表单检验
      await formRef.current.validateFields();

      const _formResult = formRef?.current?.getFieldsValue();

      const _detectionResult: Record<string, any>[] = [];

      if (_formResult?.multiEtiology === '否') {
        // 如果不是多病原
        _detectionResult.push({
          etiologyId: _formResult?.etiologyId,
          etiologyName: pathogenList?.find(
            (_i) => _i?.etiologyId === _formResult?.etiologyId
          )?.etiologyName,
          result: _formResult?.result,
          resultName: resultEnums?.find(
            (_item) => _item?.value === _formResult?.result
          )?.label,
          detectionDate: _formResult?.detectionDate,
          detectionReport: _formResult?.detectionReport,
          bacterialStrainNo: _formResult?.bacterialStrainNo,
        });
      } else {
        multiEtiologyList.map((_item) => {
          _detectionResult.push({
            ..._item,
            detectionDate: _formResult?.detectionDate,
            detectionReport: _formResult?.detectionReport,
          });
        });
      }

      const _params = {
        id: detailId,
        taskId: curSelectedPlanInfo?.id,
        isRCheck: isDraft,
        detectionResult: _detectionResult,
      };

      const { code, msg } = await submitDecteSampleData(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      querySampleStatusCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取详情
   */
  const querySampleDetails = async () => {
    try {
      const { code, data, msg } = await getSampleById({ id: detailId });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetectionDetails(data);

      // 处理上传的图片资源
      if (data?.sampleDetectionImgUrlList?.length) {
        // 这里后端返回一条id,查询所有的上传图片资源对象
        // ^_^ ....
        // 上面的通过 Promise.all 分开查询的逻辑不要删除
        // 下面的私有方法 _queryUploadedImagesList, 也不要删除
        const _id = data?.sampleDetectionImgUrlList[0];
        const result = await fileGroupApi({ businessId: _id });
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            ossId: _i?.ossId,
            fileName: _i?.originalName,
            url: _i?.url,
            size: _i?.fileSize,
          });
        });
        formRef.current?.setFieldValue('images', _res);
        // 上面的form设值为了页面上显示检测图片
        // 另外需要将该数据设置到另一个数据集合中以方便提交
        setUploadImgDataSource(_res);
      }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('reports', _res);
      }

      if (data?.detectionResult?.length) {
        const _multiEtiologyList: Record<string, any>[] = [];
        if (curSelectedPlanInfo?.multiEtiology === 1) {
          // 多病原监测
          data?.detectionResult?.forEach((_item: any) => {
            _multiEtiologyList.push({
              ..._item,
              result: _item?.result + '',
            });
          });
          setMultiEtiologyList(_multiEtiologyList);
        } else {
          // 不是多病原
          formRef.current.setFieldsValue({
            etiologyId: data?.detectionResult[0]?.etiologyId,
            etiologyName: data?.detectionResult[0]?.etiologyName,
            result: data?.detectionResult[0]?.result,
            resultName: data?.detectionResult[0]?.resultName,
          });
          if (data?.detectionResult[0]?.etiologyId) {
            formRef.current.setFieldValue('id', data?.detectionResult[0]?.id);
          }
        }

        formRef.current.setFieldValue(
          'detectionDate',
          data?.detectionResult[0]?.detectionDate
        );

        formRef.current.setFieldValue(
          'detectionReport',
          data?.detectionResult[0]?.detectionReport
        );
      }
    } catch (err) {
      console.error('Error fetching sample details:', err);
      throw new Error(`Error: ${err}`);
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取年龄单位的Enums
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('	age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  根据任务获取病原信息
   */
  const queryPathogenInfoByTask = async (taskId: string) => {
    try {
      const { code, data, msg } = await getPathogenInfoByTask({
        taskId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data);

      const _multiEtiologyList: Record<string, any>[] = [];
      data?.forEach((_item: any) => {
        _multiEtiologyList.push({
          etiologyName: _item?.etiologyName,
          etiologyId: _item?.etiologyId, // 病原ID
          result: '', // 0 - 阴性 ， 1 - 阳性
          bacterialStrainNo: '', // 编号
        });
      });
      setMultiEtiologyList(_multiEtiologyList);
      setEditableRowKeys(_multiEtiologyList.map((_item) => _item?.etiologyId));
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  多病院表格数据录入时的数据处理
   */
  const handleResultChange = (index: number, handler: string, value: any) => {
    const _multiEtiologyList: any = [...multiEtiologyList];
    if (handler === 'result') {
      // 如果操作的是检测结果
      _multiEtiologyList[index].result = value;
    }

    if (handler === 'result_comment') {
      _multiEtiologyList[index].bacterialStrainNo = value;
    }

    setMultiEtiologyList(_multiEtiologyList);
  };

  useEffect(() => {
    if (detailId) {
      querySampleDetails(); // 获取详情
    }
  }, [detailId]);

  useEffect(() => {
    if (sampleDetectionDetails) {
      console.log('sampleDetectionDetails:', sampleDetectionDetails);
      formRef.current.setFieldsValue({
        ...sampleDetectionDetails,
        sampleArea: [
          sampleDetectionDetails?.cityName,
          sampleDetectionDetails?.areaName,
        ],
        result: sampleDetectionDetails?.detectionResult[0]?.result + '',
        bacterialStrainNo:
          sampleDetectionDetails?.detectionResult[0]?.bacterialStrainNo,
      });

      setSampleDetectionDataSource(
        sampleDetectionDetails?.sampleDetectionInfoList
      );

      // 手动将采样地区的数据写入formRef
      formRef?.current?.setFieldValue('cityId', sampleDetectionDetails?.cityId);
      formRef?.current?.setFieldValue('areaId', sampleDetectionDetails?.areaId);

      // 样本归类是否是选择了人
      if (sampleDetectionDetails?.sampleType === '1') {
        setIsChoicePerson(true);

        formRef.current.setFieldsValue({
          liveCityArea: [
            sampleDetectionDetails?.liveCityName,
            sampleDetectionDetails?.liveCountryName,
          ],
        });
      }
    }
  }, [sampleDetectionDetails]);

  useEffect(() => {
    if (curSelectedPlanInfo) {
      // 设置任务信息
      formRef?.current?.setFieldsValue({
        planName: curSelectedPlanInfo?.planName,
        planYear: curSelectedPlanInfo?.planYear,
        projectName: curSelectedPlanInfo?.projectName,
        multiEtiology: curSelectedPlanInfo?.multiEtiology === 1 ? '是' : '否',
        planRemark: curSelectedPlanInfo?.planRemark,
        taskRemark: curSelectedPlanInfo?.taskRemark,
        planAttachment: curSelectedPlanInfo?.planAttachment,
      });
      // 获取任务对应的病原
      queryPathogenInfoByTask(curSelectedPlanInfo?.id);
      setIsMultiEtiology(curSelectedPlanInfo?.multiEtiology === 1);

      // 通过其他接口获取附件列表
      const _id = curSelectedPlanInfo?.planId;
      if (_id && _id !== 'null') {
        fileGroupApi({ businessId: _id }).then(({ code, data, msg }) => {
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return;
          }
          const _result: any[] = [];
          data?.forEach((_item: any) => {
            _result.push({
              uid: _item?.ossId,
              name: _item?.originalName,
              url: _item?.url,
            });
          });
          formRef?.current?.setFieldValue('file', _result);
        });
      }
    }
  }, [curSelectedPlanInfo]);

  useEffect(() => {
    isRefresh.current = true;
    queryCityAreaList();
    queryAgeUnitListEnums();
  }, []);

  useEffect(() => {
    isRefresh.current && setEditableKey(new Date().getTime() + '');
    setTimeout(() => {
      isRefresh.current = false;
    }, 1000);
  }, [multiEtiologyList]);

  return (
    <div className=" h-full w-full">
      <div className="w-full flex flex-col flex-nowrap">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            onFinish={() => handleSavePlan(2)}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={{
              render: (_, dom) => (
                <div className="flex justify-center items-center mb-4">
                  <Space>
                    <Button type="default" onClick={close}>
                      取消
                    </Button>
                    <Button
                      type="default"
                      loading={loading}
                      onClick={() => handleSavePlan(1)}
                    >
                      保存
                    </Button>
                    <Button type="primary" loading={loading} htmlType="submit">
                      提交
                    </Button>
                  </Space>
                </div>
              ),
            }}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="任务基本信息">
              <ProFormText
                readonly
                name="planName"
                label="计划名称"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="planYear"
                label="计划年份"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="projectName"
                label="监测项目"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="multiEtiology"
                label="多病原监测"
                colProps={{ span: 6 }}
              />
              <ProFormText
                readonly
                name="planRemark"
                label="计划备注信息"
                colProps={{ span: 24 }}
              />
              <ProFormText
                readonly
                name="taskRemark"
                label="任务备注信息"
                colProps={{ span: 24 }}
              />
              <ProFormUploadButton
                readonly
                name="file"
                label="计划附件"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  name: 'file',
                  listType: 'picture-card',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      const d = await getFileData(file.response.data.ossId);
                      if (type === 'Image') {
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        if (file.response.data.url) {
                          setFileUrl(file.response.data.url);
                          setOpenPreview(true);
                        } else {
                          downloadFile(
                            file.response.data.ossId,
                            file.response.data.fileName
                          );
                        }
                      }
                    } else if (file?.name && file?.uid && file?.url) {
                      setFileUrl(file?.url);
                      setOpenPreview(true);
                    }
                  },
                  disabled: true,
                }}
                action={''}
                wrapperCol={{
                  span: 24,
                }}
              />
            </EProFormGroup>
            <EProFormGroup title="监测样本采样信息">
              <ProFormText
                readonly
                name="sampleNo"
                label="样本编号"
                placeholder="请输入"
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="sampleName"
                label="样本名称"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_name');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                  showSearch: true,
                }}
                {...layoutProps}
              />

              <ProFormDatePicker
                readonly
                name="sampleGetDate"
                label="采样日期"
                placeholder="请输入"
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="sampleType"
                label="样本归类"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_type');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                {...layoutProps}
                onChange={() => {
                  formRef.current.getFieldValue('sampleType') === '1'
                    ? setIsChoicePerson(true)
                    : setIsChoicePerson(false);
                }}
              />
              <ProFormSelect
                readonly
                name="sampleSource"
                label="样本来源"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_source');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="sourceDetail"
                label="来源详情"
                placeholder="请输入"
                {...layoutProps}
              />
              <ProFormCascader
                readonly
                name="sampleArea"
                fieldProps={{ options: cityAreaList }}
                label="采样地区"
                // {...layoutProps}
                colProps={{ span: 12 }}
              />
              <ProFormText
                readonly
                name="sentinelHospital"
                label="哨点医院"
                placeholder="请输入"
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="separateState"
                label="分离状态"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict(
                      'separate_status'
                    );
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                {...layoutProps}
              />
              <ProFormDatePicker
                readonly
                name="sendDate"
                label="送检日期"
                placeholder="请输入"
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormDatePicker
                readonly
                name="receiveDate"
                label="收样日期"
                placeholder="请输入"
                style={{ width: '100%' }}
                {...layoutProps}
              />

              {isChoicePerson ? (
                <>
                  <ProFormText
                    readonly
                    name="name"
                    label="姓名"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="parentName"
                    label="家长姓名"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="idNo"
                    label="身份证号"
                    placeholder="请输入"
                    rules={[
                      {
                        pattern:
                          /^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/,
                        message: '请输入正确的身份证号码格式！',
                      },
                    ]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="phone"
                    label="联系电话"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    readonly
                    name="birthday"
                    label="出生日期"
                    placeholder="请输入"
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormDigit
                    readonly
                    label="年龄"
                    name="age"
                    fieldProps={{
                      precision: 0,
                      addonAfter: (
                        <Select
                          defaultValue="1"
                          options={ageUnitList}
                          fieldNames={{
                            label: 'dictLabel',
                            value: 'dictValue',
                          }}
                          onChange={(e: any) =>
                            setCurSelectedAgeUnit(e?.target?.value)
                          }
                        />
                      ),
                    }}
                    {...layoutProps}
                  />
                  <ProFormSelect
                    readonly
                    name="ageLevel"
                    label="年龄段"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('age_level');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    {...layoutProps}
                  />
                  <ProFormRadio.Group
                    readonly
                    name="sex"
                    label="性别"
                    options={[
                      {
                        label: '男',
                        value: '1',
                      },
                      {
                        label: '女',
                        value: '2',
                      },
                    ]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="nation"
                    label="民族"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormSelect
                    readonly
                    name="job"
                    label="职业"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('job_group');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="patientNo"
                    label="病历号"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    readonly
                    name="attackDate"
                    label="发病日期"
                    placeholder="请输入"
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormCascader
                    readonly
                    name="liveCityArea"
                    fieldProps={{ options: cityAreaList }}
                    label="常住地"
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="liveAddress"
                    label="详细地址"
                    placeholder="请输入"
                    colProps={{ span: 12 }}
                    labelCol={{ span: 4 }}
                  />
                </>
              ) : null}
            </EProFormGroup>
            <EProFormGroup title="样本检测信息">
              {!isMultiEtiology ? (
                <>
                  <ProFormSelect
                    name="etiologyId"
                    label="监测病原"
                    placeholder="请输入"
                    options={pathogenList}
                    fieldProps={{
                      fieldNames: {
                        label: 'etiologyName',
                        value: 'etiologyId',
                      },
                    }}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormSelect
                    name="result"
                    label="检测结果"
                    placeholder="请输入"
                    options={sampleDetectionResultEnums}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    name="detectionDate"
                    label="检测日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="detectionReport"
                    label="检测人员"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormText
                    name="bacterialStrainNo"
                    label="菌(毒)株编号"
                    placeholder="请输入"
                    colProps={{ span: 6 }}
                    labelCol={{ span: 9 }}
                  />
                </>
              ) : (
                <>
                  <div className="w-full mb-6">
                    <EditableProTable
                      key={editableKey}
                      columns={[
                        {
                          dataIndex: 'index',
                          valueType: 'indexBorder',
                          width: 48,
                        },
                        {
                          title: '病原',
                          key: 'etiologyName',
                          dataIndex: 'etiologyName',
                          editable: false,
                        },
                        {
                          title: '检测结果',
                          key: 'result',
                          dataIndex: 'result',
                          valueType: 'select',
                          fieldProps: {
                            options: sampleDetectionResultEnums,
                          },
                        },
                        {
                          title: '菌（毒）株编号',
                          key: 'bacterialStrainNo',
                          dataIndex: 'bacterialStrainNo',
                          // editable: (value, row, index) => {
                          //   if (
                          //     (row && row.result === '0') ||
                          //     (row && row.result === '2')
                          //   ) {
                          //     row.bacterialStrainNo = undefined;
                          //   }
                          //   return row.result === '1';
                          // },
                        },
                      ]}
                      rowKey="etiologyId"
                      value={multiEtiologyList}
                      onChange={() => {}}
                      recordCreatorProps={false}
                      editable={{
                        type: 'multiple',
                        editableKeys,
                        onValuesChange: (record, recordList) => {
                          setMultiEtiologyList(recordList);
                        },
                      }}
                    />
                  </div>
                  <div className="w-full flex flex-row flex-nowrap gap-4">
                    <ProFormDatePicker
                      name="detectionDate"
                      label="检测日期"
                      placeholder="请输入"
                      rules={[{ required: true, message: '这是必填项' }]}
                      style={{ width: '100%' }}
                      {...layoutProps}
                    />
                    <ProFormText
                      name="detectionReport"
                      label="检测人员"
                      placeholder="请输入"
                      {...layoutProps}
                    />
                  </div>
                </>
              )}
            </EProFormGroup>
          </ProForm>
        </div>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default FillIn;
