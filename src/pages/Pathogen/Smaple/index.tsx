/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 样本检测信息主入口
import React, { useState } from 'react';
import PlanTable from './components/PlanTable';
import SampleTable from './components/SampleTable';

type TSampleDetectionInfoProps = {};
const SampleDetectionInfo: React.FC<TSampleDetectionInfoProps> = () => {
  // 当前选择的计划 id
  const [curSelectedPlanInfo, setCurSelectedPlanInfo] =
    useState<Record<string, any>>();

  return (
    <div className="flex flex-row flex-nowrap justify-between w-full h-full gap-3">
      <div className="w-[30vw] bg-white">
        <PlanTable
          setCurSelectedPlanInfo={setCurSelectedPlanInfo}
          curSelectedPlanInfo={curSelectedPlanInfo!}
        />
      </div>
      <div className="flex-1 bg-white">
        <SampleTable curSelectedPlanInfo={curSelectedPlanInfo!} />
      </div>
    </div>
  );
};

export default SampleDetectionInfo;
