/**
 * @description 详情表单布局
 */
export const formItemLayout = {
  span: 8,
};

/**
 * @description 详情表单默认值
 */
export const FormInitVal = {
  // 上级菜单
  parentId: null,
  // 菜单类型
  menuType: 'M',
  // 菜单图标
  icon: '',
  // 菜单名称
  menuName: '',
  // 排序
  orderNum: '',
  // 菜单状态
  status: 0,
};

/**
 * @description 年份下拉框枚举
 */
export const yearList = [
  {
    value: '2024',
    label: '2024',
  },
  {
    value: '2023',
    label: '2023',
  },
]
const getYearList = () => {
  const currentYear = new Date().getFullYear();
  // 创建一个空数组用于存放年份选项
  const yearsArray: { value: number; label: number; }[] = [];
  // 往前推算四年
  for (let i = 0; i <= 4; i++) {
    const year = currentYear - i;
    yearsArray.push({
      value: year,
      label: year,
    });
  }
  return yearsArray
}
export const yearListOnTable = getYearList()