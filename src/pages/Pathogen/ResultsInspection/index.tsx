/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 菌株复核任务主入口
import { Key, useEffect, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
} from 'antd';
import {
  batchSaveStrainVerificationTask,
  getStrainVerificationTaskCount,
  getStrainVerificationTaskList,
  getUserInfoAtReCheck,
  updateStrainVerificationTask,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Detail from './components/Detail';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TStrainVerificationTaskProps = {};

const ResultsInspection: React.FC<TStrainVerificationTaskProps> = () => {
  const { yearList } = useYearStore();

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(10);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 待复核、已复核数量统计
  const [reCheckStatusCount, setReCheckStatusCount] =
    useState<Record<string, any>>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  // 是否已复核 isReCheck = 1 待复核、 isReCheck = 2 已复核
  const [isRCheck, setIsRCheck] = useState<number | null>();

  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('1');

  // 用户信息数据
  const [curUserInfo, setCurUserInfo] = useState<Record<string, any>>();

  // 当前输入的不符合原因
  const [curNotMatchReason, setCurNotMatchReason] = useState<string>();

  // 当前选中的项的key集合
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  // 当前批量选中的项目
  const [curSelectedRows, setCurSelectedRows] = useState<Record<string, any>[]>(
    []
  );

  const [checkInfoForm] = Form.useForm();

  // 批量提交弹层是否显示
  const [isShowBatchSubmitModal, setIsShowBatchSubmitModal] =
    useState<boolean>(false);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleNo',
      hideInSearch: true,
    },
    {
      title: '样本名称',
      dataIndex: 'sampleNameCh',
      hideInSearch: true,
    },
    {
      title: '检测日期',
      dataIndex: 'detectionDate',
      hideInSearch: true,
    },
    {
      title: '菌（毒）株编号',
      dataIndex: 'bacterialStrainNo',
    },
    {
      title: '病原体',
      dataIndex: 'etiologyName',
    },
    {
      title: '复核结论',
      dataIndex: 'reCheckResult',
      valueType: 'select',
      fieldProps: {
        options: [
          {
            label: '符合',
            value: 1,
          },
          {
            label: '不符合',
            value: 0,
          },
        ],
      },
      render: (_, record) => (
        <>
          {record?.reCheckResult === 1
            ? '符合'
            : record?.reCheckResult === 0
            ? '不符合'
            : '-'}
          {record?.reCheckResult === '0' ? `(${record?.reCheckRemark})` : null}
        </>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => {
        return [
          <Button
            type="link"
            size="small"
            key="detail"
            onClick={() => {
              setDetailId(record.id);
              setOpenDetail(true);
            }}
          >
            详情
          </Button>,
        ];
      },
    },
  ];
  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  const queryStrainVerificationTaskCount = async () => {
    try {
      const { code, data, msg } = await getStrainVerificationTaskCount({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setReCheckStatusCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取用户信息
   */
  const queryUserInfo = async () => {
    try {
      const { code, data, msg } = await getUserInfoAtReCheck();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCurUserInfo(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  处理符合操作
   */
  const handleCompliantOperation = async (id: string) => {
    try {
      const _params = {
        id,
        status: 1,
        reCheckResult: 1,
        deptName: curUserInfo?.deptName,
        reCheckDate: dayjs().format('YYYY-MM-DD'),
        reDetectionPeople: curUserInfo?.reDetectionPeople,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  处理不符合操作
   */
  const handleNotInComplianceOperation = async (id: string) => {
    if (!curNotMatchReason) {
      message.error('不符合原因是必填的');
      return;
    }

    try {
      const _params = {
        id,
        status: 1,
        reCheckResult: 0,
        deptName: curUserInfo?.deptName,
        reCheckDate: dayjs().format('YYYY-MM-DD'),
        reDetectionPeople: curUserInfo?.reDetectionPeople,
        reCheckRemark: curNotMatchReason,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setCurNotMatchReason(undefined);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量处理符合操作
   */
  const batchHandleCompliantOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }
    try {
      let _params: Record<string, any>[] = [];

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 1,
          reCheckResult: 1,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs().format('YYYY-MM-DD'),
          reDetectionPeople: curUserInfo?.reDetectionPeople,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setSelectedRowKeys([]);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量处理不符合操作
   */
  const batchHandleNotCompliantOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }

    if (!curNotMatchReason) {
      message.error('不符合原因是必填的');
      return;
    }

    try {
      let _params: Record<string, any>[] = [];

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 1,
          reCheckResult: 0,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs().format('YYYY-MM-DD'),
          reDetectionPeople: curUserInfo?.reDetectionPeople,
          reCheckRemark: curNotMatchReason,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setSelectedRowKeys([]);
      setCurSelectedRows([]);
      setCurNotMatchReason(undefined);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量提交操作
   */
  const handleBatchSubmitOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }

    try {
      setLoading(true);
      let _params: Record<string, any>[] = [];

      const _formData = checkInfoForm?.getFieldsValue();

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 2,
          reCheckResult: _item?.reCheckResult,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs(_formData?.reCheckDate).format('YYYY-MM-DD'),
          reDetectionPeople: _formData?.reDetectionPeople,
          reCheckRemark: _item?.curNotMatchReason,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setIsShowBatchSubmitModal(false);
      setSelectedRowKeys([]);
      setCurSelectedRows([]);
      setCurNotMatchReason(undefined);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    tableReload();
  }, [isRCheck]);

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    queryStrainVerificationTaskCount();
    queryUserInfo();
  }, []);

  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        request={async (params: any, sort, filter) => {
          const param = {
            ...params,
            status: 2,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;

          const { code, data, msg } = await getStrainVerificationTaskList(
            param
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => []}
      />

      {/* 详情 */}
      <Drawer
        width="60%"
        title="复核详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </PageContainer>
  );
};
export default ResultsInspection;
