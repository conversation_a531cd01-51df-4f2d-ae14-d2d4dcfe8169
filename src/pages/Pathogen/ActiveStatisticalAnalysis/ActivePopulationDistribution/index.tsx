/*
 * @Date: 2024-07-19 11:18:12
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 10:25:12
 * @FilePath: \xr-qc-jk-web\src\pages\Pathogen\ActiveStatisticalAnalysis\ActivePopulationDistribution\index.tsx
 * @Description: 人群分布统计
 */
import { useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { populationDistributionListApi } from '@/api/activeSurveillance';
import { getDict } from '@/api/dict';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import PositiveSample from '../PositiveSample';
import { TPopulationTable } from '../type';

const ActivePopulationDistribution: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [open, setOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [curSelectRow, setCurSelectRow] = useState<TPopulationTable>();

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'sampleYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '人群',
      dataIndex: 'genderId',
      hideInSearch: true,
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('sys_user_sex');
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '检测完成数量',
      dataIndex: 'detectionCount',
      hideInSearch: true,
    },
    {
      title: '初检阳性数量',
      dataIndex: 'positiveCount',
      hideInSearch: true,
      render: (_, record) => (
        <div
          className=" w-full truncate text-[#1677ff] cursor-pointer"
          onClick={() => {
            setCurSelectRow(record);
            setOpen(true);
          }}
          title={record.positiveCount}
        >
          {record.positiveCount}
        </div>
      ),
    },
    // {
    //   title: '复核阳性数量',
    //   dataIndex: 'rePositive',
    //   hideInSearch: true,
    // },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            sampleYear: params.sampleYear,
          };
          setSearchParams(_params);
          const { code, data, msg } = await populationDistributionListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/spreadAnalyzeActive/genderInfo/export"
            params={searchParams}
            method="get"
          >
            导出统计结果
          </DownloadButton>,
        ]}
      />
      {/* 初检阳性样本 */}
      <Drawer
        width="80%"
        title="初检阳性样本"
        onClose={() => {
          setOpen(false);
          setCurSelectRow(undefined);
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <PositiveSample statisticGender={curSelectRow} />
      </Drawer>
    </PageContainer>
  );
};

export default ActivePopulationDistribution;
