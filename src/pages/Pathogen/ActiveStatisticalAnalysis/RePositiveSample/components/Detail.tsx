import { Descriptions, Drawer, Image } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
};
const Detail: React.FC<TDetailProps> = ({ open, setOpen }) => {
  return (
    <Drawer
      width="80%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">

        <BlockContainer title="样本基本信息">
          <Descriptions column={4}>
            <Descriptions.Item label="样本编号">样本编号</Descriptions.Item>
            <Descriptions.Item label="样本名称">样本名称</Descriptions.Item>
            <Descriptions.Item label="样本数量">样本数量</Descriptions.Item>
            <Descriptions.Item label="样本包装">样本包装</Descriptions.Item>
            <Descriptions.Item label="样本状态">样本状态</Descriptions.Item>
            <Descriptions.Item label="样本储存条件">
              样本储存条件
            </Descriptions.Item>
            <Descriptions.Item label="样本采样日期">
              样本采样日期
            </Descriptions.Item>
            <Descriptions.Item label="采样区域">采样区域</Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="样本处理信息">
          <Descriptions column={4}>
            <Descriptions.Item label="处理方式">处理方式</Descriptions.Item>
            <Descriptions.Item label="处理日期">处理日期</Descriptions.Item>
            <Descriptions.Item
              label="处理描述"
              contentStyle={{ width: '100%' }}
            >
              处理描述
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="样本检测信息">
          <ProTable
            search={false}
            options={false}
            columns={[
              {
                dataIndex: 'index',
                valueType: 'indexBorder',
                width: 48,
              },
              {
                title: '检测项目',
                dataIndex: 'particularYear',
                hideInSearch: true,
              },
              {
                title: '检验依据',
                dataIndex: 'name',
                hideInSearch: true,
              },
              {
                title: '判定标准',
                dataIndex: 'assessmentType',
                hideInSearch: true,
              },
              {
                title: '检出限',
                dataIndex: 'assessmentType',
                hideInSearch: true,
              },
              {
                title: '检出限单位',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
              {
                title: '检出结果',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
              {
                title: '检出结果单位',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
            ]}
            dataSource={[{}]}
            cardBordered
            bordered
            rowKey="id"
            pagination={false}
            dateFormatter="string"
          />
        </BlockContainer>

        <BlockContainer title="样本处置信息">
          <Descriptions column={4}>
            <Descriptions.Item label="处置日期">处置日期</Descriptions.Item>
            <Descriptions.Item label="处置人">处置人</Descriptions.Item>
            <Descriptions.Item label="处置方式">处置方式</Descriptions.Item>
            <Descriptions.Item
              label="处理描述"
              contentStyle={{ width: '100%' }}
            >
              处理描述
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="样本检测附件">
          <Descriptions column={4}>
            <Descriptions.Item label="样本检测结果">
              样本检测结果
            </Descriptions.Item>
            <Descriptions.Item label="检测日期">检测日期</Descriptions.Item>
            <Descriptions.Item label="检测人员">检测人员</Descriptions.Item>
            <Descriptions.Item
              label="样本检测图片"
              contentStyle={{ width: '100%', display: 'flex', gap: '16px' }}
            >
              {[1, 2].map((_item) => (
                <Image
                  src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
                  width={100}
                  height={100}
                  preview={false}
                />
              ))}
            </Descriptions.Item>
            <Descriptions.Item
              label="样本检测报告"
              contentStyle={{ width: '100%', display: 'flex', gap: '16px' }}
            >
              {[1, 2].map((_item) => (
                <Image
                  src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
                  width={100}
                  height={100}
                  preview={false}
                />
              ))}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="复核结果信息">
          <Descriptions column={4}>
            <Descriptions.Item label="复核日期">复核日期</Descriptions.Item>
            <Descriptions.Item label="检测人">检测人</Descriptions.Item>
            <Descriptions.Item label="复核结果">复核结果</Descriptions.Item>
            <Descriptions.Item
              label="复核备注"
              contentStyle={{ width: '100%' }}
            >
              复核备注
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="复核检测信息">
          <ProTable
            search={false}
            options={false}
            columns={[
              {
                dataIndex: 'index',
                valueType: 'indexBorder',
                width: 48,
              },
              {
                title: '检测项目',
                dataIndex: 'particularYear',
                hideInSearch: true,
              },
              {
                title: '检验依据',
                dataIndex: 'name',
                hideInSearch: true,
              },
              {
                title: '判定标准',
                dataIndex: 'assessmentType',
                hideInSearch: true,
              },
              {
                title: '检出限',
                dataIndex: 'assessmentType',
                hideInSearch: true,
              },
              {
                title: '检出限单位',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
              {
                title: '检出结果',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
              {
                title: '检出结果单位',
                dataIndex: 'endDate',
                hideInSearch: true,
              },
            ]}
            dataSource={[{}]}
            cardBordered
            bordered
            rowKey="id"
            pagination={false}
            dateFormatter="string"
          />
        </BlockContainer>

        <BlockContainer title="耐药性检测结果信息">
          <Descriptions column={4}>
            <Descriptions.Item label="检测日期">检测日期</Descriptions.Item>
            <Descriptions.Item label="检测人">检测人</Descriptions.Item>
            <Descriptions.Item label="耐药时长">耐药时长</Descriptions.Item>
            <Descriptions.Item
              label="耐药性检测备注"
              contentStyle={{ width: '100%' }}
            >
              耐药性检测备注
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>

        <BlockContainer title="复核附件">
          <Descriptions column={1}>
            <Descriptions.Item
              label="样本检测图片"
              contentStyle={{ width: '100%', display: 'flex', gap: '16px' }}
            >
              {[1, 2].map((_item) => (
                <Image
                  src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
                  width={100}
                  height={100}
                  preview={false}
                />
              ))}
            </Descriptions.Item>
            <Descriptions.Item
              label="样本检测报告"
              contentStyle={{ width: '100%', display: 'flex', gap: '16px' }}
            >
              {[1, 2].map((_item) => (
                <Image
                  src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
                  width={100}
                  height={100}
                  preview={false}
                />
              ))}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
