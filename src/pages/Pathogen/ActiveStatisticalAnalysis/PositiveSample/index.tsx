/*
 * @Date: 2024-07-19 11:18:12
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 10:41:57
 * @FilePath: \xr-qc-jk-web\src\pages\Pathogen\ActiveStatisticalAnalysis\PositiveSample\index.tsx
 * @Description: 初检阳性样本
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { initPositiveListApi } from '@/api/activeSurveillance';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from '../../ActiveSurveillance/SampleDetectionInfo/components/Detail';
import PageContainer from '@/components/PageContainer';
import { TAgeTable, TAreaTable, TPopulationTable } from '../type';

type TPositiveSampleProps = {
  statisticArea?: TAreaTable;
  statisticAge?: TAgeTable;
  statisticGender?: TPopulationTable;
};

const PositiveSample: React.FC<TPositiveSampleProps> = ({
  statisticArea,
  statisticAge,
  statisticGender,
}) => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [curSelectId, setCurSelectId] = useState('');

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '样本名称',
      dataIndex: 'sampleName',
    },
    {
      title: '初检日期',
      dataIndex: 'detectionDate',
      hideInSearch: true,
    },
    // {
    //   title: '复核日期',
    //   dataIndex: 'checkDate',
    //   hideInSearch: true,
    // },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectId(record.sampleId);
            setDetailOpen(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const { code, data, msg } = await initPositiveListApi(
            {
              pageSize: params.pageSize!,
              pageNum: params.current!,
            },
            {
              statisticArea,
              statisticAge,
              statisticGender,
              sampleQuery: {
                sampleCode: params.sampleCode,
                sampleName: params.sampleName,
              },
            }
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curSelectId} />
    </PageContainer>
  );
};

export default PositiveSample;
