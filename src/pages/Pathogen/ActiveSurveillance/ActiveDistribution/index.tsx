/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Date: 2024-11-21 16:20:03
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-04 09:39:14
 * @FilePath: /xr-qc-jk-web/src/pages/Pathogen/ActiveSurveillance/ActiveDistribution/index.tsx
 * @Description: 监测分布
 */
import { useRef, useState } from 'react';
import { Card, Checkbox, Drawer, message } from 'antd';
import { distributionListApi } from '@/api/activeSurveillance';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearList } from '@/pages/data';
import PositiveSample from '../PositiveSample';
import RePositiveSample from '../RePositiveSample';

// 统计维度
const typOptions = [
  { label: '区域', value: 'areaName', numValue: 0 },
  { label: '人群', value: 'job', numValue: 1 },
  { label: '年龄', value: 'age', numValue: 2 },
];
// 统计指标
const indexOptions = [
  { label: '样本采集数', value: 'sampleCount' },
  { label: '检测完成数', value: 'detectFinishCount' },
  { label: '菌（毒）株数量', value: 'strainsCount' },
  // { label: '已复核数量', value: 'reviewed' },
  // { label: '复核符合数', value: 'reviewCompliant' },
  // { label: '复核符合率', value: 'reviewCompliantRate' },
  // { label: '复核不符合数', value: 'noReviewCompliant' },
  // { label: '复核不符合率', value: 'noReviewCompliantRate' },
];

const Distribution: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [open, setOpen] = useState(false);
  const [openRe, setOpenRe] = useState(false);
  const [curSelectRow, setCurSelectRow] = useState<any>();

  //
  const [type, setType] = useState(['areaName']);
  const [index, setIndex] = useState([
    'sampleCount',
    'detectFinishCount',
    'strainsCount',
  ]);

  const actionRef = useRef<ActionType>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '区域',
      dataIndex: 'areaName',
      hideInSearch: true,
    },
    {
      title: '人群',
      dataIndex: 'job',
      hideInSearch: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      hideInSearch: true,
    },

    {
      title: '样本采集数',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '检测完成数',
      dataIndex: 'detectFinishCount',
      hideInSearch: true,
    },
    {
      title: '菌（毒）株数量',
      dataIndex: 'strainsCount',
      hideInSearch: true,
    },
  ];

  const defaultColumns: ProColumns[] = [
    {
      title: '计划年份',
      dataIndex: 'planYear',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
      initialValue: new Date().getFullYear(),
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'date',
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()],
    },
  ];
  const [tableColumns, setTableColumns] = useState<ProColumns[]>([]);

  return (
    <PageContainer>
      <Card className="mb-5">
        <div className="flex gap-5 leading-8">
          <div className=" w-32 text-right">统计维度:</div>
          <div className="flex-1">
            <Checkbox.Group
              options={typOptions}
              value={type}
              onChange={(val) => setType(val as string[])}
            />
          </div>
        </div>
        <div className="flex gap-5 leading-8">
          <div className=" w-32 text-right">统计指标:</div>
          <div className="flex-1">
            <Checkbox.Group
              options={indexOptions}
              value={index}
              onChange={(val) => setIndex(val as string[])}
            />
          </div>
        </div>
      </Card>
      <ProTable
        columns={tableColumns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current,
          };
          delete _params.current;
          if (_params.date && _params.date.length) {
            _params.sampleStartDate = _params.date[0];
            _params.sampleEndDate = _params.date[1];
          }
          _params.dimension = typOptions
            .filter((item) => type.includes(item.value))
            .map((item) => item.numValue)
            .toString();
          const _columns = columns.filter((item) =>
            [...type, ...index].includes(item.dataIndex as string)
          );
          setTableColumns([
            ..._columns,
            // 查询
            ...defaultColumns,
          ]);

          setSearchParams(_params);
          const { code, data, msg } = await distributionListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
          searchText: '统计',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/spreadAnalyzeActive/activeMonitoring/export"
            params={searchParams}
            method="get"
          >
            导出统计结果
          </DownloadButton>,
        ]}
      />
      {/* 初检阳性样本 */}
      <Drawer
        width="80%"
        title="初检阳性样本"
        onClose={() => {
          setOpen(false);
          setCurSelectRow(undefined);
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <PositiveSample statisticAge={curSelectRow} />
      </Drawer>
      {/* 复核阳性样本 */}
      <Drawer
        width="80%"
        title="复核阳性样本"
        onClose={() => {
          setOpenRe(false);
          setCurSelectRow(undefined);
        }}
        open={openRe}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <RePositiveSample statisticAge={curSelectRow} />
      </Drawer>
    </PageContainer>
  );
};

export default Distribution;
