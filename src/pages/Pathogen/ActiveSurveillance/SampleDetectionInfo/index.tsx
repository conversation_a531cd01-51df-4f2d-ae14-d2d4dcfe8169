/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 主动检测 - 样本检测信息
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Tag } from 'antd';
import {
  deleteSampleDetectionInfo,
  getActiveSampleStatusCount,
  getSampleDetectionInfo,
} from '@/api/activeSurveillance';
import { getDict } from '@/api/dict';
import { deleteSampleData } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import FillIn from './components/FillIn';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TSampleDetectionInfoProps = {
  curSelectedPlanInfo?: Record<string, any>;
};

const SampleDetectionInfo: React.FC<TSampleDetectionInfoProps> = ({
  curSelectedPlanInfo,
}) => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 当前选择的任务 id 下的样本列表数据
  const [sampleListData, setSampleListData] = useState<Record<string, any>[]>();
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  // 列表刷新
  const tableReload = () => actionRef.current?.reload();
  // 是否是草稿
  const [isDraft, setIsDraft] = useState<number>();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  // 样本状态数量
  const [sampleStatusCount, setSampleStatusCount] =
    useState<Record<string, any>>();

  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('0');

  // 是否打开填报页面
  const [isOpenFillIn, setIsOpenFillIn] = useState<boolean>(false);

  // 样本归类枚举数据
  const [sampleTypeEnums, setSampleTypeEnums] = useState<Record<string, any>[]>(
    []
  );

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleNo',
    },
    {
      title: '样本名称',
      dataIndex: 'sampleName',
      valueType: 'select',
      request: async (params: any) => {
        const { code, data, msg } = await getDict('sample_name');
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        return data;
      },
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => <div>{record?.sampleNameCh}</div>,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleGetDate',
      valueType: 'dateRange',
      render: (_, record) => <span>{record?.sampleGetDate}</span>,
    },
    {
      title: '样本归类',
      dataIndex: 'sampleType',
      valueType: 'select',
      fieldProps: {
        options: sampleTypeEnums,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => <span>{record?.sampleTypeName}</span>,
    },
    {
      title: '采集地区',
      dataIndex: 'sampleArea',
      hideInSearch: true,
      render: (_, record) => (
        <span>
          {record?.cityName}-{record?.areaName}
        </span>
      ),
    },
    {
      title: '哨点医院',
      dataIndex: 'sentinelHospital',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 60,
      render: (text, record, _, action) => {
        return curStatus === '0' ? (
          [
            <Button
              type="link"
              size="small"
              key="edit"
              onClick={() => {
                setDetailId(record?.id);
                setOpenEdit(true);
              }}
            >
              编辑
            </Button>,
            <Popconfirm
              title="删除此项？"
              onConfirm={() => handleDeleteSample(record?.id)}
              okText="确定"
              cancelText="取消"
              key="delpro"
            >
              <Button danger key="editable" size="small" type="text">
                删除
              </Button>
            </Popconfirm>,
          ]
        ) : curStatus === '1' ? (
          [
            <Button
              type="link"
              size="small"
              key="view"
              onClick={() => {
                setDetailId(record?.id);
                setIsOpenFillIn(true);
              }}
            >
              填报
            </Button>,
          ]
        ) : (
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDetailId(record?.id);
              setOpenDetail(true);
            }}
          >
            详情
          </Button>
        );
      },
    },
  ];

  /**
   * 获取列表数据
   */
  const querySampleListData = async () => {
    if (!curSelectedPlanInfo) return;

    try {
      const _param: any = {};

      const { code, data, msg } = await getSampleDetectionInfo(_param);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleListData(data?.rows || data?.records);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 删除样本信息
   */
  const handleDeleteSample = async (id: number | string) => {
    try {
      const { code, msg } = await deleteSampleDetectionInfo({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
      querySampleStatusCount();
    } catch (err) {
      throw new Error(`Error: err`);
    }
  };

  /**
   * 获取草稿、已提交状态的数量
   * @returns
   */
  const querySampleStatusCount = async () => {
    try {
      const { code, data, msg } = await getActiveSampleStatusCount({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleStatusCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取样本归类枚举数据
   */
  const querySampleTypeEnumsList = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeEnums(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    querySampleStatusCount();
    tableReload();
  };

  useEffect(() => {
    tableReload();
  }, [isDraft]);

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    querySampleStatusCount();
    querySampleTypeEnumsList();
  }, []);

  return (
    <PageContainer>
      <ProTable<any>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        size="small"
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: curStatus,
            items: [
              {
                key: '0',
                label: <span>草稿({sampleStatusCount?.draftNum || 0})</span>,
              },
              {
                key: '1',
                label: (
                  <span>待检测({sampleStatusCount?.inspectNum || 0})</span>
                ),
              },
              {
                key: '2',
                label: (
                  <span>已提交({sampleStatusCount?.acceptedNum || 0})</span>
                ),
              },
            ],
            onChange: (key) => {
              setCurStatus(key as any);
            },
          },
        }}
        request={async (params, sort, filter) => {
          const param: any = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            isRCheck: curStatus,
          };

          delete param.current;

          if (params?.sampleGetDate) {
            param['sampleGetDateStart'] = params?.sampleGetDate[0];
            param['sampleGetDateEnd'] = params?.sampleGetDate[1];
          }

          const { code, data, msg } = await getSampleDetectionInfo(param);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={false}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setDetailId('');
              setOpenEdit(true);
            }}
            type="primary"
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增 & 编辑 */}
      <Drawer
        width="70%"
        title={!detailId ? '新增样本' : '编辑样本'}
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={querySampleListData}
        />
      </Drawer>
      {/* 填报 */}
      <Drawer
        width="70%"
        title="填报"
        onClose={() => setIsOpenFillIn(false)}
        open={isOpenFillIn}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <FillIn
          close={() => setIsOpenFillIn(false)}
          detailId={detailId}
          curSelectedPlanInfo={curSelectedPlanInfo}
          tableReload={tableReload}
          querySampleStatusCount={querySampleStatusCount}
        />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="70%"
        title="样本检测详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail
          close={() => setOpenDetail(false)}
          detailId={detailId}
          curSelectedPlanInfo={curSelectedPlanInfo}
        />
      </Drawer>
    </PageContainer>
  );
};
export default SampleDetectionInfo;
