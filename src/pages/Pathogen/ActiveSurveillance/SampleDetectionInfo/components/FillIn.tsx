/* eslint-disable array-callback-return */

/**
 *  检测中状态的填报页面
 */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, Divider, Input, message, Select, Space } from 'antd';
import {
  getSampleDetectionInfoDetail,
  postSampleDetectionInfo,
  putSampleDetectionInfo,
} from '@/api/activeSurveillance';
import { getDict } from '@/api/dict';
import {
  getAllCityAreaList,
  getPathogenInfoByTask,
  getPathogenInfoByTaskId,
  getSampleAgeList,
  getSampleDetailById,
  postNewSampleData,
  submitDecteSampleData,
  updateSampleData,
} from '@/api/pathogen';
import { etiologyListApi } from '@/api/pathogenDictionary';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { sampleDetectionResultEnums } from '@/enums';
import { useInfoStore, useTokenStore } from '@/store';
import { convertToCascading } from '@/utils';
import {
  EditableFormInstance,
  EditableProTable,
  ProForm,
  ProFormCascader,
  ProFormDatePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import EProFormGroup from '@/components/EProFromGroup';
import './index.less';

type TEditProps = {
  close: () => void;
  detailId?: string;
  curSelectedPlanInfo?: Record<string, any>;
  tableReload: any;
  querySampleStatusCount: any;
};

const layoutProps = {
  colProps: { span: 6 },
  labelCol: { span: 7 },
};

// 创建Context
export const TaskContext = createContext<any>({});

const resultEnums = [
  { label: '阴性', value: '0' },
  { label: '阳性', value: '1' },
];

const FillIn: React.FC<TEditProps> = ({
  close,
  detailId,
  curSelectedPlanInfo,
  tableReload,
  querySampleStatusCount,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  const { userInfo } = useInfoStore();
  const { token } = useTokenStore();

  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);
  // 样本检测详情信息
  const [sampleDetectionDetails, setSampleDetectionDetails] =
    useState<Record<string, any>>();
  // 年龄段枚举值
  const [sampleAgeList, setSampleAgeList] = useState<Record<string, any>[]>();
  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 该计划任务的病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();

  // 图片数据集
  const [uploadImgDataSource, setUploadImgDataSource] = useState<
    Record<string, any>[]
  >([]);

  // 当前样本归类的选择
  const [isChoicePerson, setIsChoicePerson] = useState<boolean>(false);

  // 年龄单位的枚举值列表
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);

  // 当前选择的年龄单位
  const [curSelectedAgeUnit, setCurSelectedAgeUnit] = useState<string>('1');

  // 是否选择的阳性结果
  const [isChoicePositive, setIsChoicePositive] = useState<boolean>(false);

  // 是否为多病原监测,默认否
  const [isMultiEtiology, setIsMultiEtiology] = useState<boolean>(false);

  // 多病原数据集合
  const [multiEtiologyList, setMultiEtiologyList] = useState<any[]>([]);

  const [editableKey, setEditableKey] = useState<string>('');

  // 是否刷新
  const isRefresh = useRef(false);

  // 病原库列表
  const [pathogenLibrayList, setPathogenLibrayList] = useState<any[]>([]);

  const editorFormRef = useRef<EditableFormInstance<any>>();

  /**
   * 提交新建样本
   * @param isDraft
   * @returns
   */
  const handleSavePlan = async (isDraft: number) => {
    setLoading(true);
    try {
      // 表单检验
      await formRef.current.validateFields();

      const _formResult = formRef?.current?.getFieldsValue();

      const _detectionResult: Record<string, any>[] = [];

      const _sampleInfoList = editorFormRef.current?.getRowsData?.();

      // 检查病原列表的编号是否填写
      _sampleInfoList?.forEach((_item) => {
        if (_item?.result === '1' && !_item?.bacterialStrainNo) {
          message.error('病原列表有阳性结果未填写编号');
          return;
        }
      });

      _sampleInfoList?.map((_item) => {
        _detectionResult.push({
          etiologyId: _item?.etiologyId,
          result: _item?.result,
          bacterialStrainNo: _item?.bacterialStrainNo,
          detectionDate: _formResult?.detectionDate,
          detectionReport: _formResult?.detectionReport,
        });
      });

      const _params = {
        id: detailId,
        isRCheck: isDraft,
        detectionResult: _detectionResult,
      };

      const { code, msg } = await putSampleDetectionInfo(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      querySampleStatusCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取详情
   */
  const querySampleDetails = async () => {
    try {
      const { code, data, msg } = await getSampleDetectionInfoDetail({
        id: detailId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetectionDetails(data);

      if (data?.resultList?.length) {
        // 处理返回的数据，需要result字段的值的类型为 String
        const _updatedResultList = data?.resultList?.map(
          (item: Record<string, any>) => ({
            ...item,
            result: String(item.result), // 将 result 转换为字符串
          })
        );
        formRef.current?.setFieldValue('table', _updatedResultList);

        formRef.current.setFieldValue(
          'detectionDate',
          data?.resultList[0]?.detectionDate
        );

        formRef.current.setFieldValue(
          'detectionReport',
          data?.resultList[0]?.detectionReport
        );
      }
    } catch (err) {
      console.error('Error fetching sample details:', err);
      throw new Error(`Error: ${err}`);
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取年龄单位的Enums
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('	age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  根据任务获取病原信息
   */
  const queryPathogenInfoByTask = async (taskId: string) => {
    try {
      const { code, data, msg } = await getPathogenInfoByTask({
        taskId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data);

      const _multiEtiologyList: Record<string, any>[] = [];
      data?.forEach((_item: any) => {
        _multiEtiologyList.push({
          etiologyName: _item?.etiologyName,
          etiologyId: _item?.etiologyId, // 病原ID
          result: '', // 0 - 阴性 ， 1 - 阳性
          bacterialStrainNo: '', // 编号
        });
      });
      setMultiEtiologyList(_multiEtiologyList);
      setEditableRowKeys(_multiEtiologyList.map((_item) => _item?.etiologyId));
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取病原库列表
   */
  const queryPathogenLibrayList = async () => {
    try {
      const { code, data, msg } = await etiologyListApi({
        pageSize: 999999,
        pageNum: 1,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenLibrayList(data?.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      querySampleDetails(); // 获取详情
    }
  }, [detailId]);

  useEffect(() => {
    if (sampleDetectionDetails) {
      formRef.current.setFieldsValue({
        ...sampleDetectionDetails,
        sampleArea: [
          sampleDetectionDetails?.cityName,
          sampleDetectionDetails?.areaName,
        ],
      });

      setSampleDetectionDataSource(
        sampleDetectionDetails?.sampleDetectionInfoList
      );

      // 手动将采样地区的数据写入formRef
      formRef?.current?.setFieldValue('cityId', sampleDetectionDetails?.cityId);
      formRef?.current?.setFieldValue('areaId', sampleDetectionDetails?.areaId);

      // 样本归类是否是选择了人
      if (sampleDetectionDetails?.sampleType === '1') {
        setIsChoicePerson(true);

        formRef.current.setFieldsValue({
          liveCityArea: [
            sampleDetectionDetails?.liveCityName,
            sampleDetectionDetails?.liveCountryName,
          ],
        });
      }
    }
  }, [sampleDetectionDetails]);

  useEffect(() => {
    if (curSelectedPlanInfo) {
      // 设置任务信息
      formRef?.current?.setFieldsValue({
        planName: curSelectedPlanInfo?.planName,
        planYear: curSelectedPlanInfo?.planYear,
        projectName: curSelectedPlanInfo?.projectName,
        multiEtiology: curSelectedPlanInfo?.multiEtiology === 1 ? '是' : '否',
        planRemark: curSelectedPlanInfo?.planRemark,
        taskRemark: curSelectedPlanInfo?.taskRemark,
      });
      // 获取任务对应的病原
      queryPathogenInfoByTask(curSelectedPlanInfo?.id);
      setIsMultiEtiology(curSelectedPlanInfo?.multiEtiology === 1);
    }
  }, [curSelectedPlanInfo]);

  useEffect(() => {
    isRefresh.current = true;
    queryCityAreaList();
    queryAgeUnitListEnums();
    queryPathogenLibrayList();
  }, []);

  return (
    <div className=" h-full w-full">
      <div className="w-full flex flex-col flex-nowrap">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            onFinish={() => handleSavePlan(2)}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={{
              render: (_, dom) => (
                <div className="flex justify-center items-center mb-4">
                  <Space>
                    <Button type="default" onClick={close}>
                      取消
                    </Button>
                    <Button
                      type="default"
                      loading={loading}
                      onClick={() => handleSavePlan(1)}
                    >
                      保存
                    </Button>
                    <Button type="primary" loading={loading} htmlType="submit">
                      提交
                    </Button>
                  </Space>
                </div>
              ),
            }}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="监测样本采样信息">
              <ProFormText
                readonly
                name="sampleNo"
                label="样本编号"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="sampleName"
                label="样本名称"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_name');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                  showSearch: true,
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />

              <ProFormDatePicker
                readonly
                name="sampleGetDate"
                label="采样日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="sampleType"
                label="样本归类"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_type');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
                onChange={() => {
                  formRef.current.getFieldValue('sampleType') === '1'
                    ? setIsChoicePerson(true)
                    : setIsChoicePerson(false);
                }}
              />
              <ProFormSelect
                readonly
                name="sampleSource"
                label="样本来源"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_source');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="sourceDetail"
                label="来源详情"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormCascader
                readonly
                name="sampleArea"
                fieldProps={{ options: cityAreaList }}
                rules={[{ required: true, message: '请选择行政区域' }]}
                label="采样地区"
                // {...layoutProps}
                colProps={{ span: 12 }}
              />
              <ProFormText
                readonly
                name="sentinelHospital"
                label="哨点医院"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormSelect
                readonly
                name="separateState"
                label="分离状态"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict(
                      'separate_status'
                    );
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormDatePicker
                readonly
                name="sendDate"
                label="送检日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormDatePicker
                readonly
                name="receiveDate"
                label="收样日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />

              {isChoicePerson ? (
                <>
                  <ProFormText
                    readonly
                    name="name"
                    label="姓名"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="parentName"
                    label="家长姓名"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="idNo"
                    label="身份证号"
                    placeholder="请输入"
                    rules={[
                      {
                        pattern:
                          /^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/,
                        message: '请输入正确的身份证号码格式！',
                      },
                    ]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="phone"
                    label="联系电话"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    readonly
                    name="birthday"
                    label="出生日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormDigit
                    readonly
                    label="年龄"
                    name="age"
                    rules={[{ required: true, message: '这是必填项' }]}
                    fieldProps={{
                      precision: 0,
                      addonAfter: (
                        <Select
                          defaultValue="1"
                          options={ageUnitList}
                          fieldNames={{
                            label: 'dictLabel',
                            value: 'dictValue',
                          }}
                          onChange={(e: any) =>
                            setCurSelectedAgeUnit(e?.target?.value)
                          }
                        />
                      ),
                    }}
                    {...layoutProps}
                  />
                  <ProFormSelect
                    readonly
                    name="ageLevel"
                    label="年龄段"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('age_level');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormRadio.Group
                    readonly
                    name="sex"
                    label="性别"
                    options={[
                      {
                        label: '男',
                        value: '1',
                      },
                      {
                        label: '女',
                        value: '2',
                      },
                    ]}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="nation"
                    label="民族"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormSelect
                    readonly
                    name="job"
                    label="职业"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('job_group');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="patientNo"
                    label="病历号"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    readonly
                    name="attackDate"
                    label="发病日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormCascader
                    readonly
                    name="liveCityArea"
                    fieldProps={{ options: cityAreaList }}
                    rules={[{ required: true, message: '请选择行政区域' }]}
                    label="常住地"
                    {...layoutProps}
                  />
                  <ProFormText
                    readonly
                    name="liveAddress"
                    label="详细地址"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={{ span: 12 }}
                    labelCol={{ span: 4 }}
                  />
                </>
              ) : null}
            </EProFormGroup>
            <EProFormGroup title="样本检测信息">
              <>
                <div className="w-full mb-6">
                  <EditableProTable
                    rowKey="id"
                    editableFormRef={editorFormRef}
                    name="table"
                    controlled
                    recordCreatorProps={{
                      position: 'bottom',
                      record: () => ({
                        id: (Math.random() * 1000000).toFixed(0),
                      }),
                    }}
                    toolBarRender={() => []}
                    columns={
                      !formRef?.current?.getFieldsValue()?.table
                        ? [
                            {
                              dataIndex: 'index',
                              valueType: 'indexBorder',
                              width: 48,
                            },
                            {
                              title: '病原',
                              key: 'etiologyId',
                              dataIndex: 'etiologyId',
                              valueType: 'select',
                              fieldProps: {
                                options: pathogenLibrayList,
                                showSearch: true,
                                fieldNames: {
                                  label: 'etiologyName',
                                  value: 'id',
                                },
                              },
                            },
                            {
                              title: '检测结果',
                              key: 'result',
                              dataIndex: 'result',
                              valueType: 'select',
                              fieldProps: {
                                options: sampleDetectionResultEnums,
                              },
                            },
                            {
                              title: '菌（毒）株编号',
                              key: 'bacterialStrainNo',
                              dataIndex: 'bacterialStrainNo',
                              editable: (value, row, index) =>
                                row?.result === '1',
                            },
                            {
                              title: '操作',
                              valueType: 'option',
                              width: 180,
                              render: (text, record, _, action) => [
                                <a
                                  key="editable"
                                  onClick={() => {
                                    action?.startEditable?.(record.id, record);
                                  }}
                                >
                                  编辑
                                </a>,
                                <a
                                  key="delete"
                                  onClick={() => {
                                    const tableDataSource =
                                      formRef.current?.getFieldValue(
                                        'table'
                                      ) as any;
                                    formRef.current?.setFieldsValue({
                                      table: tableDataSource.filter(
                                        (item: any) => item.id !== record.id
                                      ),
                                    });
                                  }}
                                >
                                  删除
                                </a>,
                              ],
                            },
                          ]
                        : [
                            {
                              dataIndex: 'index',
                              valueType: 'indexBorder',
                              width: 48,
                            },
                            {
                              title: '病原',
                              key: 'etiologyId',
                              dataIndex: 'etiologyId',
                              valueType: 'select',
                              fieldProps: {
                                options: pathogenLibrayList,
                                showSearch: true,
                                fieldNames: {
                                  label: 'etiologyName',
                                  value: 'id',
                                },
                              },
                            },
                            {
                              title: '检测结果',
                              key: 'result',
                              dataIndex: 'result',
                              valueType: 'select',
                              fieldProps: {
                                options: sampleDetectionResultEnums,
                              },
                            },
                            {
                              title: '菌（毒）株编号',
                              key: 'bacterialStrainNo',
                              dataIndex: 'bacterialStrainNo',
                              editable: (value, row, index) => {
                                if (
                                  (row && row.result === '0') ||
                                  (row && row.result === '2')
                                ) {
                                  row.bacterialStrainNo = undefined;
                                }
                                return row?.result === '1';
                              },
                            },
                            {
                              title: '操作',
                              valueType: 'option',
                              width: 180,
                              render: (text, record, _, action) => [
                                <a
                                  key="editable"
                                  onClick={() => {
                                    action?.startEditable?.(record.id, record);
                                  }}
                                >
                                  编辑
                                </a>,
                                <a
                                  key="delete"
                                  onClick={() => {
                                    const tableDataSource =
                                      formRef.current?.getFieldValue(
                                        'table'
                                      ) as any;
                                    formRef.current?.setFieldsValue({
                                      table: tableDataSource.filter(
                                        (item: any) => item.id !== record.id
                                      ),
                                    });
                                  }}
                                >
                                  删除
                                </a>,
                              ],
                            },
                          ]
                    }
                    editable={{
                      type: 'multiple',
                      editableKeys,
                      onChange: setEditableRowKeys,
                      actionRender: (row, config, defaultDom) => {
                        return [
                          defaultDom.save,
                          defaultDom.delete,
                          defaultDom.cancel,
                        ];
                      },
                      onSave: async (key: any, record: any) => {},
                    }}
                  />
                </div>
                <div className="w-full flex flex-row flex-nowrap gap-4">
                  <ProFormDatePicker
                    name="detectionDate"
                    label="检测日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="detectionReport"
                    label="检测人员"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                </div>
              </>
            </EProFormGroup>
          </ProForm>
        </div>
      </div>
    </div>
  );
};

export default FillIn;
