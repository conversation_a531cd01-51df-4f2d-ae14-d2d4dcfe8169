/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { Button, message, Select, Space } from 'antd';
import {
  getSampleDetectionInfoDetail,
  postSampleDetectionInfo,
} from '@/api/activeSurveillance';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { ossObjectApi } from '@/api/oss';
import {
  getAllCityAreaList,
  getSampleAgeList,
  getSampleDetailById,
} from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore, useTokenStore } from '@/store';
import { convertToCascading } from '@/utils';
import {
  EditableProTable,
  ProDescriptions,
  ProForm,
  ProFormCascader,
  ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import './index.less';

type TEditProps = {
  close: () => void;
  detailId?: string;
  curSelectedPlanInfo?: Record<string, any>;
  tableReload: any;
};

const layoutProps = {
  colProps: { span: 6 },
  labelCol: { span: 7 },
};

// 创建Context
export const TaskContext = createContext<any>({});

const TaskEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  curSelectedPlanInfo,
  tableReload,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);

  const { userInfo } = useInfoStore();
  const { token } = useTokenStore();

  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);
  // 样本检测详情信息
  const [sampleDetectionDetails, setSampleDetectionDetails] =
    useState<Record<string, any>>();
  // 年龄段枚举值
  const [sampleAgeList, setSampleAgeList] = useState<Record<string, any>[]>();
  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 该计划任务的病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();

  // 图片数据集
  const [uploadImgDataSource, setUploadImgDataSource] = useState<
    Record<string, any>[]
  >([]);

  // 当前样本归类的选择
  const [isChoicePerson, setIsChoicePerson] = useState<boolean>(false);

  // 年龄单位的枚举值列表
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);

  // 当前选择的年龄单位
  const [curSelectedAgeUnit, setCurSelectedAgeUnit] = useState<string>('1');

  /**
   * 提交新建样本
   * @param isDraft
   * @returns
   */
  const handleSavePlan = async (isDraft: number) => {
    setLoading(true);
    try {
      // 表单检验
      await formRef.current.validateFields();

      const _formResult = formRef?.current?.getFieldsValue();

      // 将行政区域选择的结果写入提交数据中
      if (_formResult?.sampleArea?.length) {
        _formResult['cityId'] = ~~_formResult?.sampleArea[0];
        _formResult['areaId'] = ~~_formResult?.sampleArea[1];

        // 从原始数据中获取选择市县区域名称
        const _cityAreaOriginList = JSON.parse(
          sessionStorage.getItem('cityAreaOriginList')!
        );
        if (!_cityAreaOriginList) {
          message.error('市县区域信息数据错误');
          return;
        }
        _cityAreaOriginList.forEach((_item: any) => {
          if (_item?.cityId === _formResult['cityId']) {
            _formResult['cityName'] = _item?.cityName;
          }
          if (_item?.areaId === _formResult['areaId']) {
            _formResult['areaName'] = _item?.areaName;
          }
        });

        delete _formResult?.sampleArea;
      }

      // 根据选择的病原填充发送字段 etiologyName
      if (_formResult?.etiologyId) {
        _formResult['etiologyName'] = pathogenList?.find(
          (_i) => _i?.id === _formResult?.etiologyId
        )?.etiologyName;
      }

      // 处理常驻市州和区县
      if (_formResult?.liveCityArea) {
        _formResult['liveCity'] = ~~_formResult?.liveCityArea[0];
        _formResult['liveCountry'] = ~~_formResult?.liveCityArea[1];

        // 从原始数据中获取选择市县区域名称
        const _cityAreaOriginList = JSON.parse(
          sessionStorage.getItem('cityAreaOriginList')!
        );
        if (!_cityAreaOriginList) {
          message.error('市县区域信息数据错误');
          return;
        }
        _cityAreaOriginList.forEach((_item: any) => {
          if (_item?.cityId === _formResult['liveCity']) {
            _formResult['liveCityName'] = _item?.cityName;
          }
          if (_item?.areaId === _formResult['liveCountry']) {
            _formResult['liveCountryName'] = _item?.areaName;
          }
        });
        delete _formResult?.liveCityArea;
      }

      // 处理年龄单位
      if (_formResult?.sampleType === '1') {
        // 只有当样本归类为 人，值为 '1' 时，才会有年龄单位
        _formResult['ageUnit'] = curSelectedAgeUnit;
      }

      const _params = {
        sampleDetectionInfoList: sampleDetectionDataSource,
        taskId: curSelectedPlanInfo?.id,
        isRCheck: isDraft,
        creatUser: userInfo?.user?.userName,
        ..._formResult,
      };

      if (detailId) {
        _params['id'] = detailId;
      }

      const { code, msg } = await postSampleDetectionInfo(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取详情
   */
  const querySampleDetails = async () => {
    try {
      const { code, data, msg } = await getSampleDetectionInfoDetail({
        id: detailId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetectionDetails(data);

      // 处理上传的图片资源
      if (data?.sampleDetectionImgUrlList?.length) {
        // 这里后端返回一条id,查询所有的上传图片资源对象
        // ^_^ ....
        // 上面的通过 Promise.all 分开查询的逻辑不要删除
        // 下面的私有方法 _queryUploadedImagesList, 也不要删除
        const _id = data?.sampleDetectionImgUrlList[0];
        const result = await fileGroupApi({ businessId: _id });
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            ossId: _i?.ossId,
            fileName: _i?.originalName,
            url: _i?.url,
            size: _i?.fileSize,
          });
        });
        formRef.current?.setFieldValue('images', _res);
        // 上面的form设值为了页面上显示检测图片
        // 另外需要将该数据设置到另一个数据集合中以方便提交
        setUploadImgDataSource(_res);
      }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        const _res: any[] = [];
        result?.data?.forEach((_i: any) => {
          _res.push({
            uid: _i?.ossId,
            name: _i?.originalName,
            url: _i?.url,
          });
        });
        formRef.current?.setFieldValue('reports', _res);
      }
    } catch (err) {
      console.error('Error fetching sample details:', err);
      throw new Error(`Error: ${err}`);
    }
  };

  /**
   * 获取年龄段
   */
  const querySampleAgeList = async () => {
    try {
      const { code, data, msg } = await getSampleAgeList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleAgeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取年龄单位的Enums
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('	age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      querySampleDetails(); // 获取详情
    }
  }, [detailId]);

  useEffect(() => {
    if (sampleDetectionDetails) {
      formRef.current.setFieldsValue({
        ...sampleDetectionDetails,
        sampleArea: [
          sampleDetectionDetails?.cityName,
          sampleDetectionDetails?.areaName,
        ],
      });

      setSampleDetectionDataSource(
        sampleDetectionDetails?.sampleDetectionInfoList
      );

      // 手动将采样地区的数据写入formRef
      formRef?.current?.setFieldValue('cityId', sampleDetectionDetails?.cityId);
      formRef?.current?.setFieldValue('areaId', sampleDetectionDetails?.areaId);

      // 样本归类是否是选择了人
      if (sampleDetectionDetails?.sampleType === '1') {
        setIsChoicePerson(true);

        formRef.current.setFieldsValue({
          liveCityArea: [
            sampleDetectionDetails?.liveCityName,
            sampleDetectionDetails?.liveCountryName,
          ],
        });
      }
    }
  }, [sampleDetectionDetails]);

  useEffect(() => {
    if (curSelectedPlanInfo) {
      // 设置任务信息
      formRef?.current?.setFieldsValue({
        planName: curSelectedPlanInfo?.planName,
        planYear: curSelectedPlanInfo?.planYear,
        projectName: curSelectedPlanInfo?.projectName,
        multiEtiology: curSelectedPlanInfo?.multiEtiology === 1 ? '是' : '否',
        planRemark: curSelectedPlanInfo?.planRemark,
        taskRemark: curSelectedPlanInfo?.taskRemark,
      });
      // 获取任务对应的病原
      // queryPathogenInfoByTaskId();
    }
  }, [curSelectedPlanInfo]);

  useEffect(() => {
    queryCityAreaList();
    queryAgeUnitListEnums();
  }, []);

  return (
    <div className=" h-full w-full">
      <div className="w-full flex flex-col flex-nowrap">
        <div className="flex-1 p-4 overflow-x-hidden overflow-y-auto">
          <ProForm
            onFinish={() => handleSavePlan(1)}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={{
              render: (_, dom) => (
                <div className="flex justify-center items-center mb-4">
                  <Space>
                    <Button type="default" onClick={close}>
                      取消
                    </Button>
                    <Button
                      type="default"
                      loading={loading}
                      onClick={() => handleSavePlan(0)}
                    >
                      保存
                    </Button>
                    <Button type="primary" loading={loading} htmlType="submit">
                      提交
                    </Button>
                  </Space>
                </div>
              ),
            }}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="监测样本采样信息">
              <ProFormText
                name="sampleNo"
                label="样本编号"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormSelect
                name="sampleName"
                label="样本名称"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_name');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                  showSearch: true,
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />

              <ProFormDatePicker
                name="sampleGetDate"
                label="采样日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormSelect
                name="sampleType"
                label="样本归类"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_type');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
                onChange={() => {
                  formRef.current.getFieldValue('sampleType') === '1'
                    ? setIsChoicePerson(true)
                    : setIsChoicePerson(false);
                }}
              />
              <ProFormSelect
                name="sampleSource"
                label="样本来源"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict('sample_source');
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormText
                name="sourceDetail"
                label="来源详情"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormCascader
                name="sampleArea"
                fieldProps={{ options: cityAreaList }}
                rules={[{ required: true, message: '请选择行政区域' }]}
                label="采样地区"
                // {...layoutProps}
                colProps={{ span: 12 }}
              />
              <ProFormText
                name="sentinelHospital"
                label="哨点医院"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormSelect
                name="separateState"
                label="分离状态"
                placeholder="请输入"
                request={async () => {
                  try {
                    const { code, data, msg } = await getDict(
                      'separate_status'
                    );
                    if (code !== codeDefinition.QUERY_SUCCESS) {
                      message.error(msg);
                      return;
                    }
                    return data;
                  } catch (err) {
                    throw new Error(`Error: err`);
                  } finally {
                  }
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'dictLabel',
                    value: 'dictValue',
                  },
                }}
                rules={[{ required: true, message: '这是必填项' }]}
                {...layoutProps}
              />
              <ProFormDatePicker
                name="sendDate"
                label="送检日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />
              <ProFormDatePicker
                name="receiveDate"
                label="收样日期"
                placeholder="请输入"
                rules={[{ required: true, message: '这是必填项' }]}
                style={{ width: '100%' }}
                {...layoutProps}
              />

              {isChoicePerson ? (
                <>
                  <ProFormText
                    name="name"
                    label="姓名"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="parentName"
                    label="家长姓名"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormText
                    name="idNo"
                    label="身份证号"
                    placeholder="请输入"
                    rules={[
                      {
                        pattern:
                          /^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/,
                        message: '请输入正确的身份证号码格式！',
                      },
                    ]}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="phone"
                    label="联系电话"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    name="birthday"
                    label="出生日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormDigit
                    label="年龄"
                    name="age"
                    rules={[{ required: true, message: '这是必填项' }]}
                    fieldProps={{
                      precision: 0,
                      addonAfter: (
                        <Select
                          defaultValue="1"
                          options={ageUnitList}
                          fieldNames={{
                            label: 'dictLabel',
                            value: 'dictValue',
                          }}
                          onChange={(e: any) =>
                            setCurSelectedAgeUnit(e?.target?.value)
                          }
                        />
                      ),
                    }}
                    {...layoutProps}
                  />
                  <ProFormSelect
                    name="ageLevel"
                    label="年龄段"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('age_level');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormRadio.Group
                    name="sex"
                    label="性别"
                    options={[
                      {
                        label: '男',
                        value: '1',
                      },
                      {
                        label: '女',
                        value: '2',
                      },
                    ]}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="nation"
                    label="民族"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormSelect
                    name="job"
                    label="职业"
                    placeholder="请输入"
                    request={async () => {
                      try {
                        const { code, data, msg } = await getDict('job_group');
                        if (code !== codeDefinition.QUERY_SUCCESS) {
                          message.error(msg);
                          return;
                        }
                        return data;
                      } catch (err) {
                        throw new Error(`Error: err`);
                      } finally {
                      }
                    }}
                    fieldProps={{
                      fieldNames: {
                        label: 'dictLabel',
                        value: 'dictValue',
                      },
                    }}
                    rules={[{ required: true, message: '这是必填项' }]}
                    {...layoutProps}
                  />
                  <ProFormText
                    name="patientNo"
                    label="病历号"
                    placeholder="请输入"
                    {...layoutProps}
                  />
                  <ProFormDatePicker
                    name="attackDate"
                    label="发病日期"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    style={{ width: '100%' }}
                    {...layoutProps}
                  />
                  <ProFormCascader
                    name="liveCityArea"
                    fieldProps={{ options: cityAreaList }}
                    rules={[{ required: true, message: '请选择行政区域' }]}
                    label="常住地"
                    {...layoutProps}
                  />
                  <ProFormText
                    name="liveAddress"
                    label="详细地址"
                    placeholder="请输入"
                    rules={[{ required: true, message: '这是必填项' }]}
                    colProps={{ span: 12 }}
                    labelCol={{ span: 4 }}
                  />
                </>
              ) : null}
            </EProFormGroup>
          </ProForm>
        </div>
      </div>
    </div>
  );
};

export default TaskEdit;
