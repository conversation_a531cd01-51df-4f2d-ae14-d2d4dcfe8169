/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 菌株复核任务主入口
import { Key, useEffect, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
} from 'antd';
import { getDict } from '@/api/dict';
import {
  batchSaveStrainVerificationTask,
  getDrugResistanceTestingCount,
  getDrugResistanceTestingList,
  getUserInfoAtReCheck,
  updateStrainVerificationTask,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TDrugResistanceTestingProps = {};

const DrugResistanceTesting: React.FC<TDrugResistanceTestingProps> = () => {
  const { yearList } = useYearStore();

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(10);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  // 待复核、已复核数量统计
  const [reCheckStatusCount, setReCheckStatusCount] =
    useState<Record<string, any>>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  // 是否已复核 isReCheck = 1 待复核、 isReCheck = 2 已复核
  const [isRCheck, setIsRCheck] = useState<number | null>();

  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('1');

  // 用户信息数据
  const [curUserInfo, setCurUserInfo] = useState<Record<string, any>>();

  // 当前输入的不符合原因
  const [curNotMatchReason, setCurNotMatchReason] = useState<string>();

  // 当前选中的项的key集合
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  // 当前批量选中的项目
  const [curSelectedRows, setCurSelectedRows] = useState<Record<string, any>[]>(
    []
  );

  const [checkInfoForm] = Form.useForm();

  // 批量提交弹层是否显示
  const [isShowBatchSubmitModal, setIsShowBatchSubmitModal] =
    useState<boolean>(false);

  // 耐药实验结果枚举数据
  const [drugResistExperResult, setDrugResistExperResult] = useState<
    Record<string, any>[]
  >([]);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '菌（毒）株编号',
      dataIndex: 'bacterialStrainNo',
    },
    {
      title: '病原体',
      dataIndex: 'etiologyName',
    },
    {
      title: '复核单位',
      dataIndex: 'deptName',
      hideInSearch: true,
    },
    {
      title: '复核日期',
      dataIndex: 'reCheckDate',
      hideInSearch: true,
    },
    {
      title: '复核人员',
      dataIndex: 'reDetectionPeople',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => {
        if (curStatus === '1') {
          return [
            <Button
              type="link"
              size="small"
              key="fillIn"
              onClick={() => {
                setDetailId(record.id);
                setOpenEdit(true);
              }}
            >
              填报耐药性检测结果
            </Button>,
          ];
        } else {
          return [
            <Button
              type="link"
              size="small"
              key="detail"
              onClick={() => {
                setDetailId(record.id);
                setOpenDetail(true);
              }}
            >
              详情
            </Button>,
          ];
        }
      },
    },
  ];
  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * 统计待检测、已检测记录数量
   * @returns
   */
  const queryDrugResistanceTestingCount = async () => {
    try {
      const { code, data, msg } = await getDrugResistanceTestingCount({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setReCheckStatusCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取用户信息
   */
  const queryUserInfo = async () => {
    try {
      const { code, data, msg } = await getUserInfoAtReCheck();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCurUserInfo(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  处理符合操作
   */
  const handleCompliantOperation = async (id: string) => {
    try {
      const _params = {
        id,
        status: 1,
        reCheckResult: 1,
        deptName: curUserInfo?.deptName,
        reCheckDate: dayjs().format('YYYY-MM-DD'),
        reDetectionPeople: curUserInfo?.reDetectionPeople,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  处理不符合操作
   */
  const handleNotInComplianceOperation = async (id: string) => {
    if (!curNotMatchReason) {
      message.error('不符合原因是必填的');
      return;
    }

    try {
      const _params = {
        id,
        status: 1,
        reCheckResult: 0,
        deptName: curUserInfo?.deptName,
        reCheckDate: dayjs().format('YYYY-MM-DD'),
        reDetectionPeople: curUserInfo?.reDetectionPeople,
        reCheckRemark: curNotMatchReason,
      };

      const { code, msg } = await updateStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setCurNotMatchReason(undefined);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量处理符合操作
   */
  const batchHandleCompliantOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }
    try {
      let _params: Record<string, any>[] = [];

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 1,
          reCheckResult: 1,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs().format('YYYY-MM-DD'),
          reDetectionPeople: curUserInfo?.reDetectionPeople,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setSelectedRowKeys([]);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量处理不符合操作
   */
  const batchHandleNotCompliantOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }

    if (!curNotMatchReason) {
      message.error('不符合原因是必填的');
      return;
    }

    try {
      let _params: Record<string, any>[] = [];

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 1,
          reCheckResult: 0,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs().format('YYYY-MM-DD'),
          reDetectionPeople: curUserInfo?.reDetectionPeople,
          reCheckRemark: curNotMatchReason,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setSelectedRowKeys([]);
      setCurSelectedRows([]);
      setCurNotMatchReason(undefined);
      queryDrugResistanceTestingCount();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  批量提交操作
   */
  const handleBatchSubmitOperation = async () => {
    if (!curSelectedRows?.length) {
      message.error('没有选中任何项');
      return;
    }

    try {
      setLoading(true);
      let _params: Record<string, any>[] = [];

      const _formData = checkInfoForm?.getFieldsValue();

      curSelectedRows?.forEach((_item) => {
        _params.push({
          id: _item?.id,
          status: 2,
          reCheckResult: _item?.reCheckResult,
          deptName: curUserInfo?.deptName,
          reCheckDate: dayjs(_formData?.reCheckDate).format('YYYY-MM-DD'),
          reDetectionPeople: _formData?.reDetectionPeople,
          reCheckRemark: _item?.reCheckRemark,
        });
      });

      const { code, msg } = await batchSaveStrainVerificationTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      setIsShowBatchSubmitModal(false);
      setSelectedRowKeys([]);
      setCurSelectedRows([]);
      setCurNotMatchReason(undefined);
      queryDrugResistanceTestingCount();
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取耐药实验结果数据集合
   */
  const queryDrugResistExperResultList = async () => {
    try {
      const { code, data, msg } = await getDict('resistance_result');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDrugResistExperResult(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    tableReload();
  }, [isRCheck]);

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    queryDrugResistanceTestingCount();
    queryUserInfo();
    queryDrugResistExperResultList();
  }, []);

  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: curStatus,
            items: [
              {
                key: '1',
                label: (
                  <span>待检测({reCheckStatusCount?.inspectNum || 0})</span>
                ),
              },
              {
                key: '2',
                label: (
                  <span>已检测({reCheckStatusCount?.acceptedNum || 0})</span>
                ),
              },
            ],
            onChange: (key) => {
              setCurStatus(key as any);
            },
          },
        }}
        request={async (params: any, sort, filter) => {
          const param = {
            ...params,
            status: curStatus,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;

          const { code, data, msg } = await getDrugResistanceTestingList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => []}
      />
      {/* 编辑 */}
      <Drawer
        width="60%"
        title="耐药性填报"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={tableReload}
          curUserInfo={curUserInfo!}
          queryDrugResistanceTestingCount={queryDrugResistanceTestingCount}
          drugResistExperResult={drugResistExperResult}
        />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="60%"
        title="耐药性详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail
          close={() => setOpenDetail(false)}
          detailId={detailId}
          drugResistExperResult={drugResistExperResult}
        />
      </Drawer>
      {/* 批量提交操作信息填写 */}
      <Modal
        title="批量提交操作"
        open={isShowBatchSubmitModal}
        centered
        onOk={handleBatchSubmitOperation}
        onCancel={() => setIsShowBatchSubmitModal(false)}
        width={380}
        okButtonProps={{ loading }}
      >
        <Form
          name="reCheckInfo"
          form={checkInfoForm}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            key="1"
            label="复核单位"
            name="deptName"
            rules={[{ required: true, message: '复核单位是必填的' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            key="2"
            label="复核日期"
            name="reCheckDate"
            rules={[{ required: true, message: '复核日期是必填的' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            key="reDetectionPeople"
            label="复核人员"
            name="reDetectionPeople"
            rules={[{ required: true, message: '复核人员是必填的' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};
export default DrugResistanceTesting;
