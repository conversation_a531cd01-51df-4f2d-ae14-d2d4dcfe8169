/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message, Table } from 'antd';
import { getDict } from '@/api/dict';
import { getDrugResistanceTestingDetail } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ProDescriptions } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import './index.less';

type TEditProps = {
  close: () => void;
  detailId?: string;
  drugResistExperResult: any;
};

const Detail: React.FC<TEditProps> = ({
  close,
  detailId,
  drugResistExperResult,
}) => {
  // 任务详情
  const [taskDetailsData, setTaskDetailsData] = useState<Record<string, any>>();
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  // 动态的表格头 columns
  const [columns, setColumns] = useState<Record<string, any>[]>([]);

  const [dataSrouce, setDataSrouce] = useState<Record<string, any>[]>([]);

  // 需要提交的数据
  let submitDataRef = useRef<Record<string, any>>({ data: [] });

  /**
   * 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getDrugResistanceTestingDetail({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetailsData(data);

      // 处理获取的数据，使用返回的列表数据构造动态行和列
      let _details: Record<string, any>[] = data?.details;

      // 生成 columns
      const _columns: Record<string, any>[] = [
        {
          title: '序号',
          dataIndex: 'rowName',
          width: 120,
          editable: false,
          fixed: 'left',
        },
      ];

      _details?.forEach((_item: Record<string, any>, _idx: number) => {
        _columns.push({
          title: _idx + 1 + '',
          dataIndex: `column${_idx + 1}`,
          //@ts-ignore
          render: (text, record) =>
            record?.id === '003'
              ? _item?.minBactConcent
              : record?.id === '004'
              ? drugResistExperResult?.find(
                  (_i: any) => _i?.dictValue === _item?.result
                )?.dictLabel
              : text,
        });
      });
      setColumns(_columns);

      // 生成 dataSource
      const _dataSource: Record<string, any>[] = [
        {
          id: '001',
          rowName: '药物中文名称',
        },
        {
          id: '002',
          rowName: '药物英文简称',
        },
        {
          id: '003',
          rowName: '最小抑菌浓度',
        },
        {
          id: '004',
          rowName: '耐药实验结果',
        },
      ];

      _details?.forEach((_item: Record<string, any>, _idx: number) => {
        _dataSource[0][`column${_idx + 1}`] = `${_item?.drugName}`;
        _dataSource[1][`column${_idx + 1}`] = `${_item?.drugEnName}`;
      });

      setDataSrouce(_dataSource);
      submitDataRef.current.data = _details;
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData(detailId);
    }
  }, [detailId]);

  useEffect(() => {
    querySampleNameEnums();
    querySampleTypeEnums();
    querySampleSourceEnums();
    querySampleSeparateStatusEnums();
    queryAgeRangeListEnums();
    queryAgeUnitListEnums();
    queryJobListEnums();
  }, []);

  return (
    <div className="drug-resistance-testing__edit flex flex-col h-full w-full gap-4 p-2 overflow-x-hidden">
      <BlockContainer title="菌（毒）株信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="菌（毒）株编号">
            {taskDetailsData?.bacterialStrainNo}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="病原体">
            {taskDetailsData?.etiologyName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="监测哨点">
            {taskDetailsData?.sentinelName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="检测日期">
            {taskDetailsData?.detectionDate}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="复核结论">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="复核结论">
            {taskDetailsData?.reCheckResult === 1
              ? '符合'
              : taskDetailsData?.reCheckResult === 0
              ? '不符合'
              : '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="复核单位">
            {taskDetailsData?.deptName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="复核日期">
            {taskDetailsData?.reCheckDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="复核人员">
            {taskDetailsData?.reDetectionPeople}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="耐药性检测信息">
        <Table
          rowKey="id"
          columns={columns}
          dataSource={dataSrouce}
          pagination={false}
          scroll={columns?.length > 9 ? { x: 1600 } : {}}
        />
      </BlockContainer>
    </div>
  );
};

export default Detail;
