/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Input, message, Radio, Space, Table } from 'antd';
import { getDict } from '@/api/dict';
import {
  getDrugResistanceTestingDetail,
  saveDrugResistanceTesting,
} from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ProDescriptions } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import './index.less';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
  curUserInfo: Record<string, any>;
  queryDrugResistanceTestingCount: any;
  drugResistExperResult: any;
};

const TaskEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  curUserInfo,
  queryDrugResistanceTestingCount,
  drugResistExperResult,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  // 任务详情
  const [taskDetailsData, setTaskDetailsData] = useState<Record<string, any>>();
  // 样本检测信息 DataSource
  const [sampleDetectionDataSource, setSampleDetectionDataSource] =
    useState<any>([]);
  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([
    '003',
    '004',
  ]);

  // 复核图片数据集
  const [reCheckImgDataSource, setReCheckImgDataSource] = useState<
    Record<string, any>[]
  >([]);

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  // 动态的表格头 columns
  const [columns, setColumns] = useState<Record<string, any>[]>([]);

  const [dataSrouce, setDataSrouce] = useState<Record<string, any>[]>([]);

  // 需要提交的数据
  let submitDataRef = useRef<Record<string, any>>({ data: [] });

  /**
   * 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getDrugResistanceTestingDetail({ id });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetailsData(data);

      // 处理获取的数据，使用返回的列表数据构造动态行和列
      let _details: Record<string, any>[] = data?.details;

      // 生成 columns
      const _columns: Record<string, any>[] = [
        {
          title: '序号',
          dataIndex: 'rowName',
          width: 120,
          editable: false,
          fixed: 'left',
        },
      ];

      _details?.forEach((_item: Record<string, any>, _idx: number) => {
        _columns.push({
          title: _idx + 1 + '',
          dataIndex: `column${_idx + 1}`,
          //@ts-ignore
          render: (text, record) =>
            record?.id === '003' ? (
              <Input
                placeholder="请输入"
                onChange={(e: any) =>
                  handleFormValueChange('input', _idx, e?.target.value)
                }
                defaultValue={_item?.minBactConcent}
              />
            ) : record?.id === '004' ? (
              <Radio.Group
                onChange={(e: any) =>
                  handleFormValueChange('radio', _idx, e?.target.value)
                }
                defaultValue={_item?.result}
              >
                <Space direction="vertical">
                  {drugResistExperResult?.map((_i: any) => (
                    <Radio value={_i?.dictValue}>{_i?.dictLabel}</Radio>
                  ))}
                </Space>
              </Radio.Group>
            ) : (
              text
            ),
        });
      });
      setColumns(_columns);

      // 生成 dataSource
      const _dataSource: Record<string, any>[] = [
        {
          id: '001',
          rowName: '药物中文名称',
        },
        {
          id: '002',
          rowName: '药物英文简称',
        },
        {
          id: '003',
          rowName: '最小抑菌浓度',
        },
        {
          id: '004',
          rowName: '耐药实验结果',
        },
      ];

      _details?.forEach((_item: Record<string, any>, _idx: number) => {
        _dataSource[0][`column${_idx + 1}`] = `${_item?.drugName}`;
        _dataSource[1][`column${_idx + 1}`] = `${_item?.drugEnName}`;
      });

      setDataSrouce(_dataSource);
      submitDataRef.current.data = _details;
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * @TODO 保存&提交数据
   */
  const handleSubmitOperation = async (isDraft: number) => {
    // 检查是否有未填写值的项
    const details = submitDataRef.current.data;
    const _isEmpty = details.some(
      (_item: Record<string, any>) => !_item?.minBactConcent || !_item?.result
    );

    if (_isEmpty) {
      message.error('有未填写的耐药性检测信息项');
      return;
    }

    setLoading(true);
    try {
      const _params = {
        id: detailId,
        status: isDraft,
        details,
      };
      const { code, msg } = await saveDrugResistanceTesting(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(codeDefinition.POST_DATA_SUCCESS);
      close();
      tableReload();
      queryDrugResistanceTestingCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   *  耐药性检测信息表单处理函数
   * @param type 操作类型 Input输入 & 结果Radio选择
   */
  const handleFormValueChange = (type: string, idx: number, value: string) => {
    if (type === 'input') {
      if (submitDataRef.current && submitDataRef.current.data) {
        submitDataRef.current.data[idx]['minBactConcent'] = value;
      }
    }

    if (type === 'radio') {
      if (submitDataRef.current && submitDataRef.current.data) {
        submitDataRef.current.data[idx]['result'] = value;
      }
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData(detailId);
    }
  }, [detailId]);

  useEffect(() => {
    if (sampleDetectionDataSource?.length) {
      const _keys: any = [];
      sampleDetectionDataSource?.forEach((_item: any) => _keys.push(_item?.id));
      setEditableRowKeys(_keys);
    }
  }, [sampleDetectionDataSource]);

  useEffect(() => {
    querySampleNameEnums();
    querySampleTypeEnums();
    querySampleSourceEnums();
    querySampleSeparateStatusEnums();
    queryAgeRangeListEnums();
    queryAgeUnitListEnums();
    queryJobListEnums();
  }, []);

  return (
    <div className="drug-resistance-testing__edit flex flex-col h-full w-full overflow-x-hidden">
      <div className="drug-resistance-testing__edit flex-1 flex flex-col gap-4 p-4 overflow-y-auto overflow-x-hidden bg-white">
        <BlockContainer title="菌（毒）株信息">
          <ProDescriptions column={4}>
            <ProDescriptions.Item label="菌（毒）株编号">
              {taskDetailsData?.bacterialStrainNo}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="病原体">
              {taskDetailsData?.etiologyName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="监测哨点">
              {taskDetailsData?.sentinelName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="检测日期">
              {taskDetailsData?.detectionDate}
            </ProDescriptions.Item>
          </ProDescriptions>
        </BlockContainer>
        <BlockContainer title="复核结论">
          <ProDescriptions column={4}>
            <ProDescriptions.Item label="复核结论">
              {taskDetailsData?.reCheckResult === 1
                ? '符合'
                : taskDetailsData?.reCheckResult === 0
                ? '不符合'
                : '-'}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="复核单位">
              {taskDetailsData?.deptName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="复核日期">
              {taskDetailsData?.reCheckDate}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="复核人员">
              {taskDetailsData?.reDetectionPeople}
            </ProDescriptions.Item>
          </ProDescriptions>
        </BlockContainer>
        <BlockContainer title="耐药性检测信息">
          <Table
            rowKey="id"
            columns={columns}
            dataSource={dataSrouce}
            pagination={false}
            scroll={columns?.length > 9 ? { x: 1600 } : {}}
          />
        </BlockContainer>
      </div>

      <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button type="default" onClick={close}>
          取消
        </Button>
        <Button type="default" onClick={() => handleSubmitOperation(1)}>
          保存
        </Button>
        <Button type="primary" onClick={() => handleSubmitOperation(2)}>
          提交
        </Button>
      </div>
    </div>
  );
};

export default TaskEdit;
