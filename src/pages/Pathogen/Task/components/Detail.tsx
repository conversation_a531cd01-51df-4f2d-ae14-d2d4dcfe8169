/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData } from '@/api/file';
import { getPathogenTaskDetail } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import {
  ProDescriptions,
  ProForm,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TEditProps = {
  close: () => void;
  detailId?: string;
};

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  // 详情数据
  const [taskDetailsData, setTaskDetails] = useState<Record<string, any>>();
  // 任务详情数据
  const [taskDetailsList, setTaskDetailsList] = useState<Record<string, any>[]>(
    []
  );

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  // word等文件预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      const { code, data, msg } = await getPathogenTaskDetail({ id: detailId });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetails(data);
      setTaskDetailsList([
        {
          id: Math.random(),
          sentinelName: data?.sentinelName,
          projectName: data?.projectName,
          etiologyName: data?.etiologyName,
          sampleNum: data?.sampleNum,
          multiEtiology:
            data?.multiEtiology === 1
              ? '是'
              : data?.multiEtiology === 2
              ? '否'
              : '-', // 是否多病原
          taskRemark: data?.taskRemark,
        },
      ]);
      // 通过其他接口获取附件列表
      const _id = data?.planId;
      if (_id && _id !== 'null') {
        const { code, data, msg } = await fileGroupApi({ businessId: _id });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.url,
          });
        });
        formRef?.current?.setFieldValue('file', _result);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData();
    }
  }, [detailId]);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4 flex flex-col flex-nowrap gap-4">
        {/* 基本信息 */}
        <BlockContainer title="计划基本信息">
          <ProDescriptions column={4}>
            <ProDescriptions.Item label="计划名称">
              {taskDetailsData?.planName}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="计划年份">
              {taskDetailsData?.planYear}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="创建人">
              {taskDetailsData?.planCreateUser}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="计划截止日期">
              {taskDetailsData?.planStopDate}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="备注信息">
              {taskDetailsData?.planRemark}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            {taskDetailsData?.planId ? (
              <ProForm
                onFinish={() => {}}
                formRef={formRef}
                disabled
                formKey="base-form-use-demo"
                layout="horizontal"
                grid={true}
                rowProps={{
                  gutter: [64, 0],
                  justify: 'space-between',
                }}
                submitter={false}
              >
                <ProFormUploadButton
                  name="file"
                  label="计划附件"
                  max={1}
                  fieldProps={{
                    iconRender: (file) => {
                      return (
                        <img
                          src={getIconByName(file.name)}
                          className="!w-[40px] !h-[40px] m-auto mt-2"
                          alt="logo"
                        />
                      );
                    },
                    name: 'file',
                    listType: 'picture-card',
                    onChange: (info) => {},
                    async onPreview(file: any) {
                      if (
                        file.status === 'done' &&
                        file.response &&
                        file.response.data &&
                        file.response.data.ossId
                      ) {
                        const type = getFileTypeByName(
                          file.response.data.fileName
                        );
                        const d = await getFileData(file.response.data.ossId);
                        if (type === 'Image') {
                          setIsShowFileData({
                            name: file.response.data.fileName,
                            url: d,
                            ossId: file.response.data.ossId,
                          });
                          setIsShowFileView(true);
                        } else {
                          if (file.response.data.url) {
                            setFileUrl(file.response.data.url);
                            setOpenPreview(true);
                          } else {
                            downloadFile(
                              file.response.data.ossId,
                              file.response.data.fileName
                            );
                          }
                        }
                      } else if (file?.name && file?.uid && file?.url) {
                        setFileUrl(file?.url);
                        setOpenPreview(true);
                      }
                    },
                  }}
                />
              </ProForm>
            ) : (
              <ProDescriptions.Item label="计划附件">-</ProDescriptions.Item>
            )}
          </ProDescriptions>
        </BlockContainer>
        {/* 任务信息 */}
        <BlockContainer title="任务信息">
          <ProTable
            columns={[
              {
                title: '监测哨点',
                dataIndex: 'sentinelName',
                hideInSearch: true,
              },
              {
                title: '监测项目',
                dataIndex: 'projectName',
                hideInSearch: true,
              },
              {
                title: '监测病原',
                dataIndex: 'etiologyName',
                hideInSearch: true,
              },
              {
                title: '监测样本数要求',
                dataIndex: 'sampleNum',
                hideInSearch: true,
              },
              {
                title: '多病原监测',
                dataIndex: 'multiEtiology',
                hideInSearch: true,
              },
              {
                title: '备注',
                dataIndex: 'taskRemark',
                hideInSearch: true,
              },
            ]}
            rowKey="id"
            search={false}
            options={false}
            pagination={false}
            dataSource={taskDetailsList}
          />
        </BlockContainer>
        {/* 接收信息 */}
        <BlockContainer title="接收信息">
          <ProDescriptions column={2}>
            <ProDescriptions.Item label="任务接收人">
              {taskDetailsData?.acceptPeople}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="任务接收日期">
              {taskDetailsData?.acceptDate}
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions column={1}>
            <ProDescriptions.Item label="接收备注">
              {taskDetailsData?.acceptRemark}
            </ProDescriptions.Item>
          </ProDescriptions>
        </BlockContainer>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </div>
  );
};

export default Detail;
