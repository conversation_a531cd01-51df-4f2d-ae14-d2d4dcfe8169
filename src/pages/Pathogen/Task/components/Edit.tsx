/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { getPathogenTaskDetail, receivePathogenTask } from '@/api/pathogen';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import {
  ProDescriptions,
  ProForm,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import EProFormGroup from '@/components/EProFromGroup';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import { formItemLayout } from '../data';

type TEditProps = {
  close: () => void;
  detailId?: string;
  tableReload: () => void;
  queryTaskListCount: any;
};

const layoutProps = {
  colProps: { ...formItemLayout },
};

const TaskEdit: React.FC<TEditProps> = ({
  close,
  detailId,
  tableReload,
  queryTaskListCount,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  const formAttachmentRef = useRef<any>(null);

  const { userInfo } = useInfoStore();

  // 详情数据
  const [taskDetailsData, setTaskDetails] = useState<Record<string, any>>();
  // 任务配置列表数据
  const [taskConfigListData, setTaskConfigListData] =
    useState<Record<string, any>[]>();

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      const { code, data, msg } = await getPathogenTaskDetail({
        id: detailId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskDetails(data);
      setTaskConfigListData([
        {
          id: 1,
          sentinelName: data?.sentinelName,
          projectName: data?.projectName, // 监测项目
          etiologyName: data?.etiologyName, // 监测病原
          sampleNum: data?.sampleNum, // 样本要求数
          multiEtiology:
            data?.multiEtiology === 1
              ? '是'
              : data?.multiEtiology === 2
              ? '否'
              : '-', // 是否多病原
          taskRemark: data?.taskRemark,
        },
      ]);

      // 通过其他接口获取附件列表
      const _id = data?.planId;
      if (_id && _id !== 'null') {
        const { code, data, msg } = await fileGroupApi({ businessId: _id });
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.url,
          });
        });
        formAttachmentRef?.current?.setFieldValue('file', _result);
      }

      // 设置接收信息填报表单中任务接收人的默认值
      formRef?.current?.setFieldValue(
        'taskAcceptPeople',
        userInfo?.user?.userName
      );
      // 设置接收信息填报表单中任务接收日期的默认值
      formRef?.current?.setFieldValue('taskAcceptDate', dayjs());
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 接收任务操作
   * @param id
   * @returns
   */
  const handleReceivePathogenTask = async (values: any) => {
    setLoading(true);

    try {
      const _params: Record<string, any> = {
        id: detailId,
        ...values,
      };

      const { code, msg } = await receivePathogenTask(_params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      close();
      tableReload();
      queryTaskListCount();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData();
    }
  }, [detailId]);

  return (
    <>
      <div className="flex flex-col h-full w-full">
        <div className="flex-1 overflow-x-hidden overflow-y-auto p-4 flex flex-col flex-nowrap gap-4">
          {/* 基本信息 */}
          <BlockContainer title="计划基本信息">
            <ProDescriptions column={4}>
              <ProDescriptions.Item label="计划名称">
                {taskDetailsData?.planName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="计划年份">
                {taskDetailsData?.planYear}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="创建人">
                {taskDetailsData?.planCreateUser}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="计划截止日期">
                {taskDetailsData?.planStopDate}
              </ProDescriptions.Item>
            </ProDescriptions>
            <ProDescriptions column={1}>
              <ProDescriptions.Item label="备注信息">
                {taskDetailsData?.planRemark}
              </ProDescriptions.Item>
            </ProDescriptions>
            <ProDescriptions column={1}>
              {taskDetailsData?.planId ? (
                <ProForm
                  onFinish={() => {}}
                  formRef={formAttachmentRef}
                  disabled
                  formKey="base-form-use-demo"
                  layout="horizontal"
                  grid={true}
                  rowProps={{
                    gutter: [64, 0],
                    justify: 'space-between',
                  }}
                  submitter={false}
                >
                  <ProFormUploadButton
                    name="file"
                    label="计划附件"
                    colProps={{ span: 24 }}
                    labelCol={{ flex: 0.005 }}
                    max={1}
                    fieldProps={{
                      iconRender: (file) => {
                        return (
                          <img
                            src={getIconByName(file.name)}
                            className="!w-[40px] !h-[40px] m-auto mt-2"
                            alt="logo"
                          />
                        );
                      },
                      name: 'file',
                      listType: 'picture-card',
                      onChange: (info) => {},
                      async onPreview(file: any) {
                        if (
                          file.status === 'done' &&
                          file.response &&
                          file.response.data &&
                          file.response.data.ossId
                        ) {
                          const type = getFileTypeByName(
                            file.response.data.fileName
                          );
                          const d = await getFileData(file.response.data.ossId);
                          if (type === 'Image') {
                            setIsShowFileData({
                              name: file.response.data.fileName,
                              url: d,
                              ossId: file.response.data.ossId,
                            });
                            setIsShowFileView(true);
                          } else {
                            if (file.response.data.url) {
                              setFileUrl(file.response.data.url);
                              setOpenPreview(true);
                            } else {
                              downloadFile(
                                file.response.data.ossId,
                                file.response.data.fileName
                              );
                            }
                          }
                        } else if (file?.name && file?.uid && file?.url) {
                          setFileUrl(file?.url);
                          setOpenPreview(true);
                        }
                      },
                    }}
                    action={''}
                    wrapperCol={{
                      span: 24,
                    }}
                  />
                </ProForm>
              ) : (
                <ProDescriptions.Item label="计划附件">-</ProDescriptions.Item>
              )}
            </ProDescriptions>
          </BlockContainer>
          {/* 任务配置 */}
          <BlockContainer title="任务配置">
            <ProTable
              columns={[
                {
                  title: '监测哨点',
                  dataIndex: 'sentinelName',
                  hideInSearch: true,
                },
                {
                  title: '监测项目',
                  dataIndex: 'projectName',
                  hideInSearch: true,
                },
                {
                  title: '监测病原',
                  dataIndex: 'etiologyName',
                  hideInSearch: true,
                },
                {
                  title: '监测样本数要求',
                  dataIndex: 'sampleNum',
                  hideInSearch: true,
                },
                {
                  title: '多病原监测',
                  dataIndex: 'multiEtiology',
                  hideInSearch: true,
                },
                {
                  title: '备注',
                  dataIndex: 'taskRemark',
                  hideInSearch: true,
                },
              ]}
              rowKey="id"
              search={false}
              options={false}
              pagination={false}
              dataSource={taskConfigListData}
            />
          </BlockContainer>
          <ProForm
            onFinish={(values: any) => handleReceivePathogenTask(values)}
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [64, 0],
              justify: 'space-between',
            }}
            submitter={false}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <EProFormGroup title="接收信息">
              <ProFormText
                name="taskAcceptPeople"
                label="任务接收人"
                placeholder="请输入任务接收人"
                initialValue={userInfo?.user?.userName}
                rules={[{ required: true, message: '任务接收人是必填项' }]}
                {...layoutProps}
              />
              <ProFormDatePicker
                name="taskAcceptDate"
                initialValue={dayjs()}
                label="任务接收日期"
                rules={[{ required: true, message: '任务日期是必选项' }]}
                width={'lg'}
                {...layoutProps}
              />
              <ProFormTextArea
                name="acceptRemark"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                label="接收备注"
              />
            </EProFormGroup>
          </ProForm>
        </div>
        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            type="primary"
            loading={loading}
            onClick={() =>
              handleReceivePathogenTask(formRef?.current?.getFieldsValue())
            }
          >
            接收任务
          </Button>
        </div>
      </div>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )}
        </>
      )}
    </>
  );
};

export default TaskEdit;
