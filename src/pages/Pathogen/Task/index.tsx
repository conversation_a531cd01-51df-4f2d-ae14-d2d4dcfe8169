/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
// 监测业务 - 检测任务主入口
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getPathogenTaskCount, getPathogenTaskList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { useYearStore } from '@/store';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TPathogenPointProps = {};

const PathogenTask: React.FC<TPathogenPointProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  const [loading, setLoading] = useState<boolean>(false);
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const { yearList } = useYearStore();

  /**
   * @TODO 刷新
   */
  const tableReload = () => actionRef.current?.reload();

  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  // 是否接受
  const [isAccept, setIsAccept] = useState<number>();
  // 状态数量
  const [taskStatusCount, setTaskStatusCount] = useState<Record<string, any>>();

  // 当前列表显示的数据的状态
  const [curStatus, setCurStatus] = useState<string>('0');

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '监测哨点',
      dataIndex: 'sentinelName',
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
    },
    {
      title: '监测病原',
      dataIndex: 'etiologyName',
      hideInSearch: true,
    },
    {
      title: '监测样本数要求',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    {
      title: '多病原监测',
      dataIndex: 'multiEtiology',
      hideInSearch: true,
      render: (text, record, _, action) => {
        return text === 1 ? '是' : '否';
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => {
        return curStatus === '0'
          ? [
              <Button
                type="link"
                size="small"
                key="edit"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenEdit(true);
                }}
              >
                任务接收
              </Button>,
              <Button
                type="link"
                size="small"
                key="detail"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenDetail(true);
                }}
              >
                详情
              </Button>,
            ]
          : [
              <Button
                type="link"
                size="small"
                key="detail"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenDetail(true);
                }}
              >
                详情
              </Button>,
            ];
      },
    },
  ];

  const columns2: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '计划年份',
      dataIndex: 'planYear',
      valueType: 'select',
      fieldProps: {
        options: yearList,
      },
    },
    {
      title: '计划名称',
      dataIndex: 'planName',
    },
    {
      title: '监测哨点',
      dataIndex: 'sentinelName',
      hideInSearch: true,
    },
    {
      title: '监测项目',
      dataIndex: 'projectName',
      hideInSearch: true,
    },
    {
      title: '监测病原',
      dataIndex: 'etiologyName',
      hideInSearch: true,
    },
    {
      title: '监测样本数要求',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    {
      title: '多病原监测',
      dataIndex: 'multiEtiology',
      hideInSearch: true,
      render: (text, record, _, action) => {
        return text === 1 ? '是' : '否';
      },
    },
    {
      title: '接收人',
      dataIndex: 'acceptPeople',
      hideInSearch: true,
    },
    {
      title: '接收日期',
      dataIndex: 'acceptDate',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => {
        return curStatus === '0'
          ? [
              <Button
                type="link"
                size="small"
                key="edit"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenEdit(true);
                }}
              >
                任务接收
              </Button>,
              <Button
                type="link"
                size="small"
                key="detail"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenDetail(true);
                }}
              >
                详情
              </Button>,
            ]
          : [
              <Button
                type="link"
                size="small"
                key="detail"
                onClick={() => {
                  setDetailId(record?.id);
                  setOpenDetail(true);
                }}
              >
                详情
              </Button>,
            ];
      },
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  const queryTaskListCount = async (params?: Record<string, any>) => {
    try {
      const { code, data, msg } = await getPathogenTaskCount(params || {});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTaskStatusCount(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    tableReload();
  }, [isAccept]);

  useEffect(() => {
    if (curStatus !== null) {
      tableReload();
    }
  }, [curStatus]);

  useEffect(() => {
    queryTaskListCount();
  }, []);

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        loading={loading}
        columns={curStatus === '0' ? columns : columns2}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: curStatus,
            items: [
              {
                key: '0',
                label: <span>待接收({taskStatusCount?.rejectedNum || 0})</span>,
              },
              {
                key: '1',
                label: <span>已接收({taskStatusCount?.acceptedNum || 0})</span>,
              },
            ],
            onChange: (key) => {
              setCurStatus(key as any);
            },
          },
        }}
        request={async (params, sort, filter) => {
          const param: any = {
            ...params,
            isAccept: curStatus,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;

          await queryTaskListCount({ ...params });

          const { code, data, msg } = await getPathogenTaskList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="监测任务"
      />
      <Drawer
        width="60%"
        title="任务接收"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailId={detailId}
          tableReload={tableReload}
          queryTaskListCount={queryTaskListCount}
        />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="60%"
        title="详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </PageContainer>
  );
};
export default PathogenTask;
