/*
 * @Date: 2024-07-31 18:05:03
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-09 14:25:34
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskManagement\RiskClue\index.tsx
 * @Description: 风险线索
 */
import { useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getDict } from '@/api/dict';
import {
  orgListApi,
  riskClueListApi,
  riskPerceptionIndexAllApi,
  TRiskClueListParams,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import RiskClueDetail from '../components/RiskClueDetail';
import PageContainer from '@/components/PageContainer';

const RiskClue: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curSelectId, setCurSelectId] = useState('');

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验机构',
      key: 'orgId',
      dataIndex: 'orgName',
      valueType: 'select',
      width: 100,
      request: async () => {
        const { code, data, msg } = await orgListApi();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'orgName',
          value: 'orgId',
        },
      },
    },
    {
      title: '风险指标',
      key: 'indexId',
      dataIndex: 'indexLabel',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await riskPerceptionIndexAllApi();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'indexLabName',
          value: 'indexId',
        },
      },
      width: 120,
    },
    {
      title: '线索类型',
      key: 'clueType',
      dataIndex: 'clueLabel',
      width: 100,
      valueType: 'select',
      formItemProps: {
        label: '风险类型',
      },
      request: async () => {
        const { code, data, msg } = await getDict('clue_type');
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '风险线索',
      dataIndex: 'riskClue',
      hideInSearch: true,
    },
    {
      title: '指标分值',
      dataIndex: 'score',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <Button
          type="link"
          onClick={() => {
            setCurSelectId(record.id);
            setDetailOpen(true);
          }}
        >
          查看
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: TRiskClueListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            orgId: params.orgId,
            indexId: params.indexId,
            clueType: params.clueType,
          };
          if (params.updateTime && params.updateTime.length) {
            _params.updateBeginTime = params.updateTime[0];
            _params.updateEndTime = params.updateTime[1];
          }
          const { code, data, msg } = await riskClueListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />

      {/* 详情 */}
      <Drawer
        width="70%"
        title="风险线索详情"
        onClose={() => setDetailOpen(false)}
        open={detailOpen}
        destroyOnClose
      >
        <RiskClueDetail open={detailOpen} id={curSelectId} />
      </Drawer>
    </PageContainer>
  );
};

export default RiskClue;
