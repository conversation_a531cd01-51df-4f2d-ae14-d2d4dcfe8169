/*
 * @Date: 2024-08-09 15:48:09
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-09 15:54:39
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskManagement\RiskSelfExamination\components\StayAuditDetail.tsx
 * @Description: 风险自查 - 待审核详情
 */

import { useEffect, useState } from 'react';
import { Drawer, message } from 'antd';
import { riskSelfExaminationDetailApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import RiskClueDetail from '../../components/RiskClueDetail';
import RiskSelfExaminationDetail from '../../components/RiskSelfExaminationDetail';

type TStayAuditDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const StayAuditDetail: React.FC<TStayAuditDetailProps> = ({
  open,
  id,
  setOpen,
}) => {
  const close = () => {
    setOpen(false);
    setDetailInfo(undefined);
  };

  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  const getDetail = async (clueId: string) => {
    try {
      const { code, data, msg } = await riskSelfExaminationDetailApi(clueId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]);
  return (
    <Drawer width="70%" title="详情" onClose={close} open={open} destroyOnClose>
      <RiskClueDetail open={open} id={id} />
      <RiskSelfExaminationDetail dataSource={detailInfo} />
    </Drawer>
  );
};

export default StayAuditDetail