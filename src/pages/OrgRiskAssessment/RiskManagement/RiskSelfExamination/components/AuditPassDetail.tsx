/*
 * @Date: 2024-08-09 15:58:01
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-09 17:42:12
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskManagement\RiskSelfExamination\components\AuditPassDetail.tsx
 * @Description: 风险自查 - 审核通过详情
 */
import { useEffect, useState } from 'react';
import { Drawer, message } from 'antd';
import { riskSelfExaminationDetailApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import ResultsAuditDetail from '../../components/ResultsAuditDetail';
import RiskClueDetail from '../../components/RiskClueDetail';
import RiskSelfExaminationDetail from '../../components/RiskSelfExaminationDetail';

type TAuditPassDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const AuditPassDetail: React.FC<TAuditPassDetailProps> = ({
  open,
  id,
  setOpen,
}) => {
  const close = () => {
    setOpen(false);
    setDetailInfo(undefined);
  };

  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  const getDetail = async (clueId: string) => {
    try {
      const { code, data, msg } = await riskSelfExaminationDetailApi(clueId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]);
  return (
    <Drawer width="70%" title="详情" onClose={close} open={open} destroyOnClose>
      <div className=' flex flex-col gap-4'>
        
      {/* 风险线索详情 */}
      <RiskClueDetail open={open} id={id} />
      {/* 自查结果详情 */}
      <RiskSelfExaminationDetail dataSource={detailInfo} />
      {/* 审核信息详情 */}
      <ResultsAuditDetail dataSource={detailInfo} />
      </div>
    </Drawer>
  );
};

export default AuditPassDetail;
