import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import { fileGroupApi, TFileGroupRes } from '@/api/common';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import {
  addRiskSelfExaminationApi,
  riskSelfExaminationDetailApi,
  updateRiskSelfExaminationApi,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import {
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import ResultsAuditDetail from '../../components/ResultsAuditDetail';
import RiskClueDetail from '../../components/RiskClueDetail';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import { getFileTypeByName } from '@/utils/upload';

type TAuditNotPassEditProps = {
  id: string;
  open: boolean;
  setOpen: (val: boolean) => void;
};
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const AuditNotPassEdit: React.FC<TAuditNotPassEditProps> = ({
  open,
  setOpen,
  id,
}) => {
  const close = () => {
    setOpen(false);
    setDetailInfo(undefined);
  };
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const { token } = useTokenStore();
  const [btnLoading, setBtnLoading] = useState(false);
  const formRef = useRef<ProFormInstance>(null);
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  const getDetail = async (clueId: string) => {
    try {
      const { code, data, msg } = await riskSelfExaminationDetailApi(clueId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
        formRef.current?.setFieldsValue(data);
        if (data?.id) {
          getFileGroup(data?.id);
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const getFileGroup = async (fileId: string) => {
    try {
      const { code, data, msg } = await fileGroupApi({ businessId: fileId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _files = data.map((_item: TFileGroupRes) => ({
          response: {
            code: 200,
            data: {
              fileName: _item.originalName,
              url: _item.fileAddr,
              ossId: _item.ossId,
            },
          },
          fileName: _item.originalName,
          url: _item.fileAddr,
          size: _item.fileSize,
          status: 'done',
        }));
        formRef.current?.setFieldValue('files', _files);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]);

  const handleSubmit = async () => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();

      values.checkDate = dayjs(values.checkDate).format('YYYY-MM-DD');
      if (values.files && values.files.length) {
        const _newFiles = values.files.filter(
          (_item: any) => _item.response.code === 200
        );
        if (_newFiles.length) {
          values.attachments = _newFiles.map((_item: any) => ({
            url: _item.response.data.url,
            fileName: _item.response.data.fileName,
            size: _item.size,
            ossId: _item.response.data.ossId,
          }));
        }
        delete values.files;
      }
      const { code, msg } = await updateRiskSelfExaminationApi({
        ...values,
        id: detailInfo?.id,
        clueId: id
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="70%"
      title="修改自查结果"
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          {/* 风险线索详情 */}
          <RiskClueDetail open={open} id={id} />
          {/* 审核信息详情 */}
          <ResultsAuditDetail dataSource={detailInfo} />
          <BlockContainer title="自查结果信息">
            <ProForm
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormText
                name="checkUser"
                label="自查记录人"
                colProps={{ span: 12 }}
                // rules={[{ required: true, message: '自查记录人必填！' }]}
              />
              <ProFormDatePicker
                name="checkDate"
                label="自查日期"
                colProps={{ span: 12 }}
                // rules={[{ required: true, message: '自查日期必填！' }]}
              />
              <ProFormTextArea
                name="checkResult"
                label="自查结果描述"
                colProps={{ span: 24 }}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
                // rules={[{ required: true, message: '自查结果描述必填！' }]}
              />
              <ProFormUploadButton
                colProps={{ span: 24 }}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
                name="files"
                label="附件材料"
                fieldProps={{
                  name: 'file',
                  onChange(info: any) {
                    const { file } = info;
                    if (file.status === 'done' || file.status === 'removed') {
                      if (
                        file.status === 'done' &&
                        file.response &&
                        file.response.code !== 200
                      ) {
                        message.error(file.response.msg);
                      } else {
                        message.success(file.response.msg);
                      }
                    }
                  },
                  accept: 'image/*',
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                  listType: 'picture-card',
                  // className: 'upload-list-inline',
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const fileType = getFileTypeByName(
                        file.response.data.fileName
                      );
                      if (fileType === 'Image') {
                        const d = await getFileData(file.response.data.ossId);
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        downloadFile(
                          file.response.data.ossId,
                          file.response.data.fileName
                        );
                      }
                    }
                  },
                }}
                action={uploadFiles}
              />
            </ProForm>
          </BlockContainer>
        </div>

        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={btnLoading}>
            保存
          </Button>
        </div>
        {/* 文件预览 */}
        <FileView
          open={isShowFileView}
          file={isShowFileData}
          closeDetail={() => {
            setIsShowFileView(false);
          }}
        />
      </div>
    </Drawer>
  );
};

export default AuditNotPassEdit;
