/*
 * @Date: 2024-07-31 18:57:28
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-09 17:44:53
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskManagement\RiskSelfExamination\index.tsx
 * @Description: 风险自查
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  riskPerceptionIndexAllApi,
  riskSelfExaminationListApi,
  riskSelfExaminationTotalApi,
  TRiskSelfExaminationListParams,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import ResultsAuditDetail from '../components/ResultsAuditDetail';
import RiskClueDetail from '../components/RiskClueDetail';
import RiskSelfExaminationDetail from '../components/RiskSelfExaminationDetail';
import AuditNotPassEdit from './components/AuditNotPassEdit';
import AuditPassDetail from './components/AuditPassDetail';
import Edit from './components/Edit';
import StayAuditDetail from './components/StayAuditDetail';
import PageContainer from '@/components/PageContainer';

const RiskSelfExamination: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  const [totalStatistic, setTotalStatistic] = useState<{
    auditPassed: number;
    notPass: number;
    selfCheck: number;
    waitAudit: number;
  }>({ auditPassed: 0, notPass: 0, selfCheck: 0, waitAudit: 0 });

  const [curSelectClueId, setCurSelectClueId] = useState('');
  // 风险线索详情
  const [clueDetailOpen, setClueDetailOpen] = useState(false);
  // 登记自查结果
  const [editOpen, setEditOpen] = useState(false);
  // 待审核详情
  const [stayAuditDetailOpen, setStayAuditDetailOpen] = useState(false);
  // 审核通过详情
  const [auditPassDetailOpen, setAuditPassDetailOpen] = useState(false);
  // 审核不通过 - 修改自查结果
  const [auditNotPassEditOpen, setAuditNotPassEditOpen] = useState(false);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '更新日期',
      dataIndex: 'updateDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '更新日期',
      key: 'updateDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '检验机构',
      dataIndex: 'orgName',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '风险指标',
      key: 'indexId',
      dataIndex: 'indexName',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await riskPerceptionIndexAllApi();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'indexLabName',
          value: 'indexId',
        },
      },
      width: 120,
    },
    {
      title: '风险线索',
      dataIndex: 'riskClue',
      hideInSearch: true,
      width: 300,
    },
  ];

  const handleToDetail = (clueId: string) => {
    setCurSelectClueId(clueId);
    if (activeKey === '0') {
      // 登记自查结果
      setEditOpen(true);
    } else if (activeKey === '1') {
      // 待审核
      setStayAuditDetailOpen(true);
    } else if (activeKey === '2') {
      // 审核通过
      setAuditPassDetailOpen(true);
    } else {
      // 审核不通过 => 修改自查结果
      setAuditNotPassEditOpen(true);
    }
  };

  const getColumns = useCallback((): ProColumns[] => {
    let _columns: ProColumns[] = [];
    if (activeKey === '0') {
      _columns = [
        {
          title: '指标分值',
          dataIndex: 'score',
          hideInSearch: true,
          width: 100,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 160,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="riskClue"
              onClick={() => {
                setCurSelectClueId(record.clueId);
                setClueDetailOpen(true);
              }}
            >
              查看风险线索
            </Button>,
            <Button
              type="link"
              size="small"
              key="result"
              onClick={() => {
                handleToDetail(record.clueId);
              }}
            >
              登记自查结果
            </Button>,
          ],
        },
      ];
    } else if (activeKey === '1') {
      _columns = [
        {
          title: '自查记录人',
          dataIndex: 'checkUser',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '自查日期',
          dataIndex: 'checkDate',
          hideInSearch: true,
          width: 120,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 100,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="detail"
              onClick={() => {
                handleToDetail(record.clueId);
              }}
            >
              查看详情
            </Button>,
          ],
        },
      ];
    } else if (activeKey === '2') {
      _columns = [
        {
          title: '自查日期',
          dataIndex: 'checkDate',
          hideInSearch: true,
        },
        {
          title: '审核人',
          dataIndex: 'auditUser',
          hideInSearch: true,
          width: 100,
        },
        {
          title: '审核日期',
          dataIndex: 'auditDate',
          hideInSearch: true,
          width: 120,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 100,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="detail"
              onClick={() => {
                handleToDetail(record.clueId);
              }}
            >
              查看详情
            </Button>,
          ],
        },
      ];
    } else {
      _columns = [
        {
          title: '自查日期',
          dataIndex: 'checkDate',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '审核人',
          dataIndex: 'auditUser',
          hideInSearch: true,
          width: 100,
        },
        {
          title: '审核日期',
          dataIndex: 'auditDate',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '审核建议',
          dataIndex: 'auditSuggest',
          hideInSearch: true,
          width: 120,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 100,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="edit"
              onClick={() => {
                handleToDetail(record.clueId);
              }}
            >
              修改自查结果
            </Button>,
          ],
        },
      ];
    }

    return [...columns, ..._columns];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: <span>待自查（{totalStatistic.selfCheck}）</span>,
              },
              {
                key: '1',
                label: <span>待审核（{totalStatistic.waitAudit}）</span>,
              },
              {
                key: '2',
                label: <span>审核通过（{totalStatistic.auditPassed}）</span>,
              },
              {
                key: '3',
                label: <span>审核不通过（{totalStatistic.notPass}）</span>,
              },
            ],
            onChange: (key) => {
              // console.log(key);

              setActiveKey(key as string);
            },
          },
        }}
        request={async (params, sort, filter) => {
          const _params: TRiskSelfExaminationListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            status: activeKey,
            indexId: params.indexId,
          };
          if (params.updateDate && params.updateDate.length) {
            _params.updateBeginTime = params.updateDate[0];
            _params.updateEndTime = params.updateDate[1];
          }
          // setSearchParams(_params);
          const { code, data, msg } = await riskSelfExaminationListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 获取各个状态下的total
          const res = await riskSelfExaminationTotalApi(_params);
          setTotalStatistic(res.data);

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
      />
      {/* 风险线索详情 */}
      <Drawer
        width="70%"
        title="风险线索详情"
        onClose={() => setClueDetailOpen(false)}
        open={clueDetailOpen}
        destroyOnClose
      >
        <RiskClueDetail open={clueDetailOpen} id={curSelectClueId} />
      </Drawer>

      {/* 待自查 - 登记自查 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          tableReload();
          setEditOpen(val);
        }}
        id={curSelectClueId}
      />
      {/* 待审核详情/ */}
      <StayAuditDetail
        open={stayAuditDetailOpen}
        setOpen={setStayAuditDetailOpen}
        id={curSelectClueId}
      />
      {/* 审核通过详情 */}
      <AuditPassDetail
        open={auditPassDetailOpen}
        setOpen={setAuditPassDetailOpen}
        id={curSelectClueId}
      />
      {/* 审核不通过修改 */}
      <AuditNotPassEdit
        id={curSelectClueId}
        open={auditNotPassEditOpen}
        setOpen={(val) => {
          tableReload();
          setAuditNotPassEditOpen(val);
        }}
      />
    </PageContainer>
  );
};

export default RiskSelfExamination;
