import { useState } from 'react';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const RiskAnalysis: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#a90000',
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [120, 150, 80, 70, 110, 130, 130],
        type: 'bar',
        barMaxWidth: 48,
      },
    ],
  });

  return (
    <div className=" w-full ">
      <div className=' px-6'>
        浙江公正检验中心有限公司2024年4月检验数据中发现有2个检验项目不在本机构的《检测能力项目表》中，包括：喹啉黄、乳酸菌数，共涉及检验数据59项次，覆盖糕点、发酵乳、赤砂糖、粉丝粉条、饼干、红糖、月饼、冰糖8个食品细类。检验记录如下：
      </div>
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="RiskAnalysisChart" />
      </div>
    </div>
  );
};

export default RiskAnalysis;
