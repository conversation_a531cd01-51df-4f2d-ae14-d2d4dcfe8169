import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { riskClueDetailQuestionListApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { VerticalAlignBottomOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';

type TProblemDataProps = {
  id: string;
};

const ProblemData: React.FC<TProblemDataProps> = ({ id }) => {
  const [pageSize, setPageSize] = useState(10);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '抽样编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '抽样时间',
      dataIndex: 'sampleDate',
    },
    {
      title: '被抽样单位名称',
      dataIndex: 'assessedLabName',
    },
    {
      title: '样品名称',
      dataIndex: 'sampleName',
    },
    // {
    //   title: '食品大类',
    //   dataIndex: 'endDate',
    // },
    // {
    //   title: '食品细类',
    //   dataIndex: 'endDate',
    // },
    {
      title: '结论',
      dataIndex: 'conclusion',
    },
    {
      title: '检验项目',
      dataIndex: 'inspectionItem',
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          if (!id) {
            return {
              data: [],
              total: 0,
              success: true,
            };
          }
          const _params = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            id,
          };
          const { code, data, msg } = await riskClueDetailQuestionListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        // toolBarRender={() => [
        //   <Button type="primary" icon={<VerticalAlignBottomOutlined />}>
        //     下载
        //   </Button>,
        // ]}
      />
    </PageContainer>
  );
};

export default ProblemData;
