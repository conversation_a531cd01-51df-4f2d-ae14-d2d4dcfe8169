import { useEffect, useState } from 'react';
import { message } from 'antd';
import { riskClueDetailApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import classNames from 'classnames';
import BlockContainer from '@/components/BlockContainer';
import ProblemData from './ProblemData';
import RiskAnalysis from './RiskAnalysis';

type TRiskClueDetailProps = {
  open: boolean;
  id: string;
};
const RiskClueDetail: React.FC<TRiskClueDetailProps> = ({ open, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  useEffect(() => {
    if (!open) {
      setDetailInfo(undefined);
    }
  }, [open]);

  const getDetail = async (clueId: string) => {
    try {
      const { code, data, msg } = await riskClueDetailApi(clueId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]);

  return (
    <div className=' flex flex-col gap-4'>
      <header>
        <div className=" text-xs text-[#999]">
          {detailInfo?.indexName ? detailInfo?.indexName + '： ' : ''}
          {detailInfo?.indexDescribe}
        </div>
        <div className=" mt-5 flex gap-4">
          <div className=" w-[120px] h-[120px] rounded-xl bg-[#fed70a] text-white flex items-center justify-center text-[40px] font-semibold">
            {detailInfo?.riskExtent}
          </div>
          <div className=" flex flex-col justify-evenly">
            <div className=" text-[32px]">{detailInfo?.indexName}</div>
            <div className=" text-[26px]">
              风险线索：
              <span
                className={classNames(
                  ' font-semibold',
                  detailInfo?.clueName === '确证线索' && ' text-[#da0000]',
                  detailInfo?.clueName === '疑似线索' && ' text-[#fad119]'
                )}
              >
                {detailInfo?.clueName}
              </span>
            </div>
            <div>更新时间：{detailInfo?.updateTime}</div>
          </div>
        </div>
      </header>
      <BlockContainer title="线索描述">
        <div>{detailInfo?.clueDescribe}</div>
      </BlockContainer>
      <BlockContainer title="风险分析">
        <RiskAnalysis />
      </BlockContainer>
      <BlockContainer title="问题数据">
        <div>
          <ProblemData id={id} />
        </div>
      </BlockContainer>
    </div>
  );
};

export default RiskClueDetail;
