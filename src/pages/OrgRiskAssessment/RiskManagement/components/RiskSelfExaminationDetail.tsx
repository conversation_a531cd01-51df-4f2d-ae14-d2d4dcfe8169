import { Descriptions } from 'antd';
import BlockContainer from '@/components/BlockContainer';
import EventFile from '@/pages/EpidemicTracking/components/EventFile';

type TRiskSelfExaminationDetailProps = {
  dataSource?: Record<string, any>;
};

const RiskSelfExaminationDetail: React.FC<TRiskSelfExaminationDetailProps> = ({
  dataSource,
}) => {
  return (
    <BlockContainer title="自查结果信息">
      <Descriptions column={2}>
        <Descriptions.Item label="自查记录人">
          {dataSource?.checkUser}
        </Descriptions.Item>
        <Descriptions.Item label="自查日期">
          {dataSource?.checkDate}
        </Descriptions.Item>
        <Descriptions.Item label="自查结果描述" span={2}>
          {dataSource?.checkResult}
        </Descriptions.Item>
        <Descriptions.Item label="附件材料" span={2}>
          <div className="w-full">
            {dataSource?.attachmentIds ? (
              <EventFile fileGroupId={dataSource?.attachmentIds} />
            ) : null}
          </div>
        </Descriptions.Item>
      </Descriptions>
    </BlockContainer>
  );
};

export default RiskSelfExaminationDetail;
