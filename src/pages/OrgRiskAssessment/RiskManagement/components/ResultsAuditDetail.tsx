import { useEffect, useState } from 'react';
import { Descriptions, message } from 'antd';
import { riskSelfExaminationDetailApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';

type TResultsAuditDetailProps = {
  dataSource?: Record<string, any>;
};

const ResultsAuditDetail: React.FC<TResultsAuditDetailProps> = ({
  dataSource,
}) => {
  return (
    <BlockContainer title="审核信息">
      <Descriptions column={2}>
        <Descriptions.Item label="审核人">
          {dataSource?.auditUser}
        </Descriptions.Item>
        <Descriptions.Item label="审核日期">
          {dataSource?.auditDate}
        </Descriptions.Item>
        <Descriptions.Item label="审核结论">
          {dataSource?.auditResult}
        </Descriptions.Item>
        <Descriptions.Item label="审核建议">
          {dataSource?.auditSuggest}
        </Descriptions.Item>
      </Descriptions>
    </BlockContainer>
  );
};

export default ResultsAuditDetail;
