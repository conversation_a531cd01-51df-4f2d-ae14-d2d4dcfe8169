import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  resultsAuditSubmitApi,
  riskSelfExaminationDetailApi,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import {
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import RiskClueDetail from '../../components/RiskClueDetail';
import RiskSelfExaminationDetail from '../../components/RiskSelfExaminationDetail';
import BlockContainer from '@/components/BlockContainer';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
  readonly?: boolean;
};
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const Edit: React.FC<TEditProps> = ({
  open,
  id,
  readonly = false,
  setOpen,
}) => {
  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const [btnLoading, setBtnLoading] = useState(false);
  const formRef = useRef<ProFormInstance>(null);
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();
  useEffect(() => {
    if (!open) {
      formRef.current?.resetFields();
    }
  }, [open]);
  const getDetail = async (clueId: string) => {
    try {
      const { code, data, msg } = await riskSelfExaminationDetailApi(clueId);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
        formRef.current?.setFieldsValue(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  const handleSubmit = async (auditResult: 2 | 3) => {
    try {
      setBtnLoading(true);
      const values = await formRef.current?.validateFields();

      values.auditDate = dayjs(values.auditDate).format('YYYY-MM-DD');
      const { code, msg } = await resultsAuditSubmitApi({
        ...values,
        clueId: id,
        id: detailInfo?.id,
        auditResult,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <Drawer
      width="70%"
      title={readonly ? '详情' : '审核'}
      onClose={close}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className="  w-full h-full flex flex-col">
        <div className=" flex-1 p-4 overflow-y-auto bg-white flex flex-col gap-4">
          {/* 风险线索详情 */}
          <RiskClueDetail open={open} id={id} />
          {/* 自查结果详情 */}
          <RiskSelfExaminationDetail dataSource={detailInfo} />
          <BlockContainer title="审核信息">
            <ProForm
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
              readonly={readonly}
            >
              <ProFormText
                name="auditUser"
                label="审核人"
                colProps={{ span: 12 }}
              />
              <ProFormDatePicker
                name="auditDate"
                label="审核日期"
                colProps={{ span: 12 }}
              />
              <ProFormTextArea
                name="auditSuggest"
                label="审核建议"
                colProps={{ span: 24 }}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              />
            </ProForm>
          </BlockContainer>
        </div>

        {!readonly ? (
          <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
            <Button
              type="primary"
              loading={btnLoading}
              onClick={() => handleSubmit(2)}
            >
              通过
            </Button>
            <Button
              type="primary"
              danger
              loading={btnLoading}
              onClick={() => handleSubmit(3)}
            >
              不通过
            </Button>
            <Button type="default" onClick={close}>
              取消
            </Button>
          </div>
        ) : null}
      </div>
    </Drawer>
  );
};

export default Edit;
