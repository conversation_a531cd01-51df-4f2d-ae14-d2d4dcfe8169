/*
 * @Date: 2024-07-31 19:15:45
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-09-23 17:46:26
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskManagement\ResultsAudit\index.tsx
 * @Description: 自查结果审核
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  resultsAuditListApi,
  resultsAuditTotalApi,
  riskPerceptionIndexAllApi,
  TResultsAuditListParams,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';

const RiskSelfExamination: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('1');
  const [totalStatistic, setTotalStatistic] = useState<{
    audited: number;
    waitAudit: number;
  }>({ audited: 0, waitAudit: 0 });
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curSelectId, setCurSelectId] = useState('');
  const close = () => {
    if (activeKey === '1') {
      tableReload();
    }
    setDetailOpen(false);
    setCurSelectId('');
  };

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '更新日期',
      key: 'updateDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '更新日期',
      dataIndex: 'updateDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '检验机构',
      dataIndex: 'orgName',
      width: 100,
    },
    {
      title: '风险指标',
      key: 'indexId',
      dataIndex: 'indexName',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await riskPerceptionIndexAllApi();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'indexLabName',
          value: 'indexId',
        },
      },
      width: 120,
    },
    {
      title: '风险线索',
      dataIndex: 'riskClue',
      hideInSearch: true,
      width: 300
    },
  ];

  const getColumns = useCallback((): ProColumns[] => {
    let _columns: ProColumns[] = [];
    if (activeKey === '1') {
      _columns = [
        {
          title: '自查记录人',
          dataIndex: 'checkUser',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '自查日期',
          dataIndex: 'checkDate',
          hideInSearch: true,
          width: 120,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 160,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurSelectId(record.clueId);
                setDetailOpen(true);
              }}
            >
              审核
            </Button>,
          ],
        },
      ];
    } else {
      _columns = [
        {
          title: '自查日期',
          dataIndex: 'checkDate',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '审核人',
          dataIndex: 'auditUser',
          hideInSearch: true,
          width: 100,
        },
        {
          title: '审核日期',
          dataIndex: 'auditDate',
          hideInSearch: true,
          width: 120,
        },
        {
          title: '审核结论',
          dataIndex: 'auditStatus',
          hideInSearch: true,
          renderText: (_, record) =>
            record.auditStatus + '' === '2' ? '通过' : '不通过',
          width: 120,
        },
        {
          title: '审核建议',
          dataIndex: 'auditSuggest',
          hideInSearch: true,
          width: 120,
        },

        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 100,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              key="detail"
              onClick={() => {
                setCurSelectId(record.clueId);
                setDetailOpen(true);
              }}
            >
              查看详情
            </Button>,
          ],
        },
      ];
    }

    return [...columns, ..._columns];
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '1',
                label: <span>待审核（{totalStatistic.waitAudit}）</span>,
              },
              {
                key: '2',
                label: <span>已审核（{totalStatistic.audited}）</span>,
              },
            ],
            onChange: (key) => {
              // console.log(key);

              setActiveKey(key as string);
            },
          },
        }}
        request={async (params, sort, filter) => {
          console.log(params);
          
          const _params: TResultsAuditListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            auditStatus: activeKey,
            orgName: params.orgName,
            indexId: params.indexId
          };
          if (params.updateDate && params.updateDate.length) {
            _params.updateBeginTime = params.updateDate[0];
            _params.updateEndTime = params.updateDate[1];
          }
          const { code, data, msg } = await resultsAuditListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 获取各个状态下的total
          const res = await resultsAuditTotalApi({});
          setTotalStatistic(res.data);
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="evenId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        scroll={{
          x: 'max-content'
        }}
      />

      {/* 审核/详情 */}
      <Edit
        open={detailOpen}
        setOpen={(val) => {
          if (activeKey === '1') {
            tableReload();
          }
          setDetailOpen(val);
        }}
        id={curSelectId}
        readonly={activeKey === '2'}
      />
    </PageContainer>
  );
};

export default RiskSelfExamination;
