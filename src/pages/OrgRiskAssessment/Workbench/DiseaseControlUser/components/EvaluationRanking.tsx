import { useEffect, useState } from 'react';
import { message } from 'antd';
import { evaluationRankingApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import BlockContainer from '@/components/BlockContainer';

const EvaluationRanking: React.FC = () => {
  const [title, setTitle] = useState('月检验机构评价排行');

  const [dataSource, setDataSource] = useState<
    {
      orgName: string;
      score: number;
    }[]
  >([]);

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await evaluationRankingApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setTitle(data.month + '月检验机构评价排行');
        setDataSource(data.data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title={title}>
      <div className=" h-[300px] w-full grid grid-rows-10 text-[#303133] px-4 text-xs">
        {dataSource.length
          ? dataSource.map((_item, _index) => (
              <div key={_index} className=" w-full flex items-center">
                <div className=" w-4/5 truncate">
                  {_index + 1} {_item.orgName}
                </div>
                <div className=" w-1/5 text-right">{_item.score}分</div>
              </div>
            ))
          : null}
      </div>
    </BlockContainer>
  );
};

export default EvaluationRanking;
