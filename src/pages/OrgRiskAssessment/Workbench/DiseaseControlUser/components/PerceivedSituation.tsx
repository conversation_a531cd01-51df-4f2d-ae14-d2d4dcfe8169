import { useEffect, useState } from 'react';
import { message } from 'antd';
import { perceivedSituationApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  quantity: number;
  riskIndex: string;
};

const PerceivedSituation: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#a90000',
    tooltip: {
      trigger: 'item',
      valueFormatter: (value) => value + '项',
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        overflow: 'break',
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barMaxWidth: 48,
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await perceivedSituationApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOptions: any = cloneDeep(option);
        _newOptions.xAxis.data = data.length
          ? data.map((_item: TDataSource) => _item.riskIndex)
          : [];
        _newOptions.series[0].data = data.length
          ? data.map((_item: TDataSource) => _item.quantity)
          : [];
        setOption(_newOptions);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="风险指标感知情况">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="PerceivedSituationChart" />
      </div>
    </BlockContainer>
  );
};

export default PerceivedSituation;
