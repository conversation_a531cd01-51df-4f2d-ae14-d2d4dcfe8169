import { useEffect, useState } from 'react';
import { message, Tooltip } from 'antd';
import { statisticOverviewApi } from '@/api/OrgRiskAssessment/api';
import workbench_icon_1 from '@/assets/OrgRiskAssessment/workbench_icon_1.png';
import workbench_icon_2 from '@/assets/OrgRiskAssessment/workbench_icon_2.png';
import workbench_icon_3 from '@/assets/OrgRiskAssessment/workbench_icon_3.png';
import { codeDefinition } from '@/constants';
import { InfoCircleOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';

const StatisticOverview: React.FC = () => {
  const [options, setOptions] = useState([
    {
      title: '检验机构数量',
      value: 0,
      key: 'orgTotal',
      titleTip: '',
      valueUnit: '家',
      right: <img alt="" src={workbench_icon_1} className=" w-full h-full" />,
    },
    {
      title: '风险线索总量',
      value: 0,
      key: 'cluesTotalYear',
      titleTip:
        '统计对本年度检验数据分析，感知的风险线索总量。风险线索为本年度数据分析结果。',
      valueUnit: '项',
      right: <img alt="" src={workbench_icon_2} className=" w-full h-full" />,
    },
    {
      title: '存在风险检验机构数量',
      value: 0,
      key: 'orgCurrentMonth',
      titleTip:
        '统计当前月发现风险的检验机构家数。风险线索更新时间为当月1日至今。',
      valueUnit: '家',
      right: <img alt="" src={workbench_icon_3} className=" w-full h-full" />,
      time: '',
    },
    {
      title: '新增风险线索情况',
      value: 0,
      key: 'newRiskClueMonth',
      titleTip:
        '统计当前月发现风险的风险线索情况。风险线索更新时间为当月1日至今。',
      valueUnit: '项',
      rightOptions: [
        {
          label: '确证线索',
          value: 0,
          key: 'corroborativeClues',
          color: 'text-[#da0000]',
        },
        {
          label: '疑似线索',
          value: 0,
          key: 'suspectedClues',
          color: 'text-[#fad119]',
        },
      ],
      time: '',
    },
  ]);

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await statisticOverviewApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        console.log(data);
        const _options = cloneDeep(options);
        _options.forEach((_item) => {
          _item.value = data[_item.key];
          if (_item.rightOptions) {
            _item.rightOptions = _item.rightOptions?.map((_i) => ({
              ..._i,
              value: data[_i.key],
            }));
          }
          if(Object.hasOwn(_item, 'time')) {
            _item.time = data.time
          }
        });
        setOptions(_options);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <div className=" w-full grid grid-cols-4 gap-5">
      {options.map((_item) => (
        <div key={_item.key} className=" h-32 bg-white flex p-4">
          <div className=" w-1/2 h-full flex flex-col justify-between">
            <div className=" text-[#4c4c4c] text-sm">
              {_item.title}
              {_item.titleTip ? (
                <Tooltip
                  title={_item.titleTip}
                  color="white"
                  overlayInnerStyle={{ color: '#333' }}
                >
                  <InfoCircleOutlined className=" ml-2" />
                </Tooltip>
              ) : null}
            </div>
            {_item.time ? (
              <div className=" text-[#8d8d8d] text-xs">{_item.time + '--至今'}</div>
            ) : null}
            <div className="  text-3xl font-bold">
              {_item.value}
              <span className=" text-xs">{_item.valueUnit}</span>
            </div>
          </div>
          <div className=" w-1/2 h-full flex ">
            {_item.right
              ? _item.right
              : _item.rightOptions.map((_i) => (
                  <div
                    key={_i.key}
                    className=" w-1/2 flex flex-col items-center justify-center"
                  >
                    <div
                      className={classNames(
                        _i.color,
                        'text-xl font-bold align-bottom'
                      )}
                    >
                      {_i.value}
                      <span className=" text-[10px]">个</span>
                    </div>
                    <div className=" text-[#8d8d8d] text-xs">{_i.label}</div>
                  </div>
                ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatisticOverview;
