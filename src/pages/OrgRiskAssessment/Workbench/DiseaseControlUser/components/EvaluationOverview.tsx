import { useEffect, useState } from 'react';
import { message } from 'antd';
import { evaluationOverviewApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const EvaluationOverview: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: ['#C23531', '#2F4554', '#61A0A8', '#D48265', '#91C7AE'],
    title: {
      text: '',
      left: 'left',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '60%'],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await evaluationOverviewApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOption: any = cloneDeep(option);
        _newOption.title.text = data.month + '月风险评价情况';
        _newOption.series[0].data = data.data ?? [];
        setOption(_newOption);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="检验机构风险评价情况">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="EvaluationOverviewChart" />
      </div>
    </BlockContainer>
  );
};

export default EvaluationOverview;
