import { useEffect, useState } from 'react';
import { message } from 'antd';
import { monthRiskCluesApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  month: string;
  quantity: number;
};

const MonthRiskClues: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#10CFA2',
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
      valueFormatter: (value) => value + '项',
    },
    series: [
      {
        data: [],
        type: 'line',
        areaStyle: {},
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await monthRiskCluesApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOptions: any = cloneDeep(option);
        _newOptions.xAxis.data = data.length
          ? data.map((_item: TDataSource) => _item.month + '月')
          : [];
        _newOptions.series[0].data = data.length
          ? data.map((_item: TDataSource) => _item.quantity)
          : [];
        setOption(_newOptions);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="各月风险线索情况">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="MonthRiskCluesChart" />
      </div>
    </BlockContainer>
  );
};

export default MonthRiskClues;
