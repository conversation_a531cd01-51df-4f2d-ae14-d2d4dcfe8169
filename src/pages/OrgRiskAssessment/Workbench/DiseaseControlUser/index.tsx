/*
 * @Date: 2024-07-30 16:47:01
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 14:06:43
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\Workbench\DiseaseControlUser\index.tsx
 * @Description: 疾控用户工作台
 */
import EvaluationOverview from './components/EvaluationOverview';
import EvaluationRanking from './components/EvaluationRanking';
import MonthRiskClues from './components/MonthRiskClues';
import PerceivedSituation from './components/PerceivedSituation';
import RiskClueBox from './components/RiskClueBox';
import StatisticOverview from './components/StatisticOverview';
import PageContainer from '@/components/PageContainer';

const DiseaseControlUser: React.FC = () => {
  return (
    <PageContainer>
      <header>监管工作台</header>
      <StatisticOverview />
      <section className=" flex flex-col py-8 gap-5">
        <div className=" flex">
          <div className=" w-1/3 pr-[10px] flex flex-col gap-5">
            {/* 检验机构风险评价情况 */}
            <EvaluationOverview />
            {/* 6月检验机构评价排行 */}
            <EvaluationRanking />
          </div>
          <div className=" w-2/3 pl-[10px] flex flex-col gap-5">
            {/* 风险线索盒 */}
            <RiskClueBox />
            {/* 各月风险线索情况 */}
            <MonthRiskClues />
          </div>
        </div>
        {/* 风险指标感知情况 */}
        <PerceivedSituation />
      </section>
    </PageContainer>
  );
};

export default DiseaseControlUser;
