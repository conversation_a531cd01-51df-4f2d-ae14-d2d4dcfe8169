/*
 * @Date: 2024-07-31 10:20:20
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 16:20:20
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\Workbench\OrgUser\index.tsx
 * @Description: 机构用户工作台
 */
import { useEffect, useState } from 'react';
import { message } from 'antd';
import { orgStatisticOverview } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import classNames from 'classnames';
import InspectionLotQuantity from './components/InspectionLotQuantity';
import MonthRiskClues from './components/MonthRiskClues';
import RiskAssessmentTrend from './components/RiskAssessmentTrend';
import RiskClueBox from './components/RiskClueBox';
import RiskClues from './components/RiskClues';
import PageContainer from '@/components/PageContainer';

const OrgUser: React.FC = () => {
  const [orgName, setOrgName] = useState('');

  const [options, setOptions] = useState([
    {
      title: '风险线索',
      value: 0,
      unit: '项',
      valueStyle: ' text-[#FF0000]',
      key: 'orgCluesTotal',
    },
    {
      title: '检验数据',
      value: 0,
      unit: '批次',
      valueStyle: ' ',
      key: 'inspectTotal',
    },
  ]);

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgStatisticOverview();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setOrgName(data.orgName);
        const _options = options.map((_item) => ({
          ..._item,
          value: data[_item.key],
        }));
        setOptions(_options);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <PageContainer>
      <header>工作台</header>
      <section className=" h-32 flex justify-between px-6 bg-white rounded">
        <div className=" text-3xl w-1/2 truncate h-full flex items-center">
          {orgName}
        </div>
        <div className=" flex h-full py-5">
          {options.map((_item, _index) => (
            <div
              key={_index}
              className={classNames(
                'flex flex-col justify-center gap-2 px-5',
                _index === 0 &&
                  ' border-0 border-r border-solid border-[#e6ebf5]'
              )}
            >
              <div className=" text-[#8d8d8d] text-base">{_item.title}</div>
              <div
                className={classNames(
                  ' text-2xl font-semibold flex gap-2',
                  _item.valueStyle
                )}
              >
                <span>{_item.value}</span>
                <span>{_item.unit}</span>
              </div>
            </div>
          ))}
        </div>
      </section>
      <section className=" py-8 flex">
        <div className=" w-1/2 pr-[10px] flex flex-col gap-5">
          {/* 风险线索盒 */}
          <RiskClueBox />
          {/* 风险评价趋势 */}
          <RiskAssessmentTrend />
          {/* 检验批次数量 */}
          <InspectionLotQuantity />
        </div>
        <div className=" w-1/2 pl-[10px] flex flex-col gap-5">
          {/* 风险线索情况 */}
          <RiskClues />
          {/* 各月风险线索情况 */}
          <MonthRiskClues />
        </div>
      </section>
    </PageContainer>
  );
};

export default OrgUser;
