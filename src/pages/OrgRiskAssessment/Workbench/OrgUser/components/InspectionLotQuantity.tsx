import { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { orgInspectionLotQuantityApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  month: number;
  num: number;
};

const InspectionLotQuantity: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#a90000',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: '#DFDFDF',
        },
      },
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
      name: '批次数',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barMaxWidth: 48,
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgInspectionLotQuantityApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis.data = data.length
          ? data.map((_item: TDataSource) => _item.month)
          : [];
        _newOption.series[0].data = data.length
          ? data.map((_item: TDataSource) => _item.num)
          : [];
        setOption(_newOption);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="检验批次数量">
      <div className=" w-full h-[300px] ">
        <ChartsWrapper option={option} key="PerceivedSituationChart" />
      </div>
    </BlockContainer>
  );
};

export default InspectionLotQuantity;
