import { useEffect, useState } from 'react';
import { message } from 'antd';
import { orgRiskAssessmentTrendApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  month: number;
  score: number;
};

const RiskAssessmentTrend: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#10CFA2',
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
      name: '风险指数总分',
    },
    tooltip: {
      trigger: 'axis',
    },
    series: [
      {
        data: [],
        type: 'line',
        areaStyle: {},
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgRiskAssessmentTrendApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis.data = data.length
          ? data.map((_item: TDataSource) => _item.month + '月')
          : [];
        _newOption.series[0].data = data.length
          ? data.map((_item: TDataSource) => _item.score)
          : [];
        setOption(_newOption);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="风险评价趋势">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="MonthRiskCluesChart" />
      </div>
    </BlockContainer>
  );
};

export default RiskAssessmentTrend;
