import { useEffect, useState } from 'react';
import { message } from 'antd';
import { orgRiskCluesApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  clueType: string;
  num: number;
};

const RiskClues: React.FC = () => {
  const [dataSource, setDataSource] = useState<TDataSource[]>([]);

  const [option, setOption] = useState<ECOption>({
    color: ['#C23531', '#2F4554', '#61A0A8', '#D48265', '#91C7AE'],
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}项 ({d}%)',
    },
    legend: {},
    series: [
      {
        type: 'pie',
        // radius: '50%',
        radius: ['40%', '60%'],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        padAngle: 2
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgRiskCluesApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  useEffect(() => {
    const _newOption: any = cloneDeep(option);
    _newOption.series[0].data = dataSource.length
      ? dataSource.map((_item) => ({
          name: _item.clueType,
          value: _item.num,
        }))
      : [];
    setOption(_newOption);
  }, [dataSource]);

  return (
    <BlockContainer title="风险线索情况">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="EvaluationOverviewChart" />
      </div>
    </BlockContainer>
  );
};

export default RiskClues;
