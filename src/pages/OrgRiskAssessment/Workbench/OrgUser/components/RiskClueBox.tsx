import { useEffect, useState } from 'react';
import { message } from 'antd';
import { orgRiskClueBoxApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { DoubleRightOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import BlockContainer from '@/components/BlockContainer';
import { useNavigate } from 'react-router';

type TDataSource = {
  clueLabel: string;
  id: number;
  riskClue: string;
  updateTime: string;
};

const RiskClueBox: React.FC = () => {
  const [dataSource, setDataSource] = useState<TDataSource[]>([]);
  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgRiskClueBoxApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);
  
  const navigate = useNavigate();

  const handleToMore = () => {
    navigate('/orgRiskAssessment/riskManagement/riskClue');
  };

  return (
    <BlockContainer
      title="风险线索盒"
      extra={
        <div
          className=" flex items-center text-[#1677ff] cursor-pointer gap-2"
          onClick={handleToMore}
        >
          <span>更多</span>
          <DoubleRightOutlined />
        </div>
      }
    >
      <div className=" w-full h-[300px] flex flex-col">
        {dataSource.map((_item, _index) => (
          <div
            key={_index}
            className=" flex w-full items-center px-2 gap-3 h-[12.5%] cursor-pointer"
          >
            <div className=" w-[75%] truncate" title={_item.riskClue}>
              {_item.riskClue}
            </div>
            <div
              className={classNames(
                ' flex-shrink-0',
                _item.clueLabel === '确证线索'
                  ? ' text-[#ff0000]'
                  : 'text-[#ffa500]'
              )}
            >
              {_item.clueLabel}
            </div>
            <div className=" flex-shrink-0">{_item.updateTime}</div>
          </div>
        ))}
      </div>
    </BlockContainer>
  );
};

export default RiskClueBox;
