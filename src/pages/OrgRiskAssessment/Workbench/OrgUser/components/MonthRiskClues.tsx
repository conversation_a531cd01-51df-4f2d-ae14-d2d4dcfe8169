import { useEffect, useState } from 'react';
import { message } from 'antd';
import { orgMonthRiskCluesApi } from '@/api/OrgRiskAssessment/api';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDataSource = {
  month: number;
  quantity: number;
};

const MonthRiskClues: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: '#a90000',
    tooltip: {
      trigger: 'item',
      valueFormatter: (value) => value + '项',
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
      name: '项数',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barMaxWidth: 48,
      },
    ],
  });

  const getDataSource = async () => {
    try {
      const { code, data, msg } = await orgMonthRiskCluesApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis.data = data.length
          ? data.map((_item: TDataSource) => _item.month + '月')
          : [];
        _newOption.series[0].data = data.length
          ? data.map((_item: TDataSource) => _item.quantity)
          : [];
        setOption(_newOption);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getDataSource();
  }, []);

  return (
    <BlockContainer title="各月风险线索情况">
      <div className=" w-full h-[300px]">
        <ChartsWrapper option={option} key="PerceivedSituationChart" />
      </div>
    </BlockContainer>
  );
};

export default MonthRiskClues;
