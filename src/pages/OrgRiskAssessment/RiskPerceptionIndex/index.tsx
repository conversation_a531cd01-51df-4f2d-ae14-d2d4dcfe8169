/*
 * @Date: 2024-07-30 16:42:04
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 14:13:50
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskPerceptionIndex\index.tsx
 * @Description: 风险感知指标
 */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getDict } from '@/api/dict';
import {
  riskPerceptionIndexListApi,
  updateRiskPerceptionIndexStatusApi,
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';

const RiskPerceptionIndex: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };
  const handleUpdateStatus = async (id: string, status: number) => {
    try {
      const { code, msg } = await updateRiskPerceptionIndexStatusApi({
        id,
        status,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '风险指标',
      dataIndex: 'indexName',
    },
    {
      title: '线索类型',
      dataIndex: 'clueType',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('clue_type');
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '指数说明',
      dataIndex: 'indexDescribe',
      hideInSearch: true,
    },
    {
      title: '指标分值',
      dataIndex: 'indexScore',
      hideInSearch: true,
    },
    {
      title: '指标状态',
      dataIndex: 'status',
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <Button
          type="link"
          onClick={() =>
            handleUpdateStatus(record.id, record.status === 0 ? 1 : 0)
          }
        >
          {record.status === 0 ? '禁用' : '启用'}
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          const { code, data, msg } = await riskPerceptionIndexListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
    </PageContainer>
  );
};

export default RiskPerceptionIndex;
