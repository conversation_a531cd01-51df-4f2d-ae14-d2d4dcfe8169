export const complianceIndex = [
  {
    label: '检测能力合规',
    value:
      '机构承接任务的检验项目和检验方法应该在其【检测能力项目表范围】之内。',
  },
  {
    label: '检验项目合规',
    value:
      '机构进行监督抽检时检验项目应与抽检细则中对应要求的检验项目保持一致。',
  },
  {
    label: '检验依据合规',
    value:
      '机构进行监督抽检时采用的检验依据应与抽检细则中对应检验项目的检验依据保持一致或更加严格。',
  },
  {
    label: '判定依据合规',
    value:
      '机构进行监督抽检时采用的判定依据应与抽检细则中对应检验项目的判定依据保持一致或更加严格。',
  },
];
// 真实性指标
export const AuthenticityIndex = [
  {
    label: '不合格样品相近生产批次分析',
    value:
      '当一样品被检验不合格，与此样品相近或相同生产批次样品检验结果应相同或相近。',
  },
  {
    label: '同产品检验结果分析',
    value: '同一款产品（条形码相同）检验项目的检验结果应在其合理范围内。',
  },
  {
    label: '设备载样超标分析',
    value:
      '使用同一检验设备和同一检验方法检验的项次量，应在设备最大承检范围内。',
  },
  {
    label: '同规模机构检验项次异常',
    value:
      '在固定时间内，检验机构对同一检验项目的检验项次与同规模检验机构对同检验项目的检验批次对比应在合理范围内。',
  },
];
// 准确性指标
export const AccuracyIndex = [
  {
    label: '未检出占比',
    value:
      '机构某食品次亚类下某一检验项目的检验结果判定为【合格或不判定】时，该机构在此检验项目下的【未检出占比】应该在其合理范围内。',
  },
  {
    label: '项目合格率',
    value:
      '当抽检批次达到一定规模时，属于同一食品次亚类下同检验项目的【检验项目合格率】应在其合理范围内。',
  },
  {
    label: '检出值波动',
    value:
      '固定时间内，同一食品次亚类下同一检验项目的检出值应在某一合理范围内波动。',
  },
  {
    label: '检出值重复',
    value:
      '固定时间内，不同样品检验的同一检验项目，检验结果的检出值重复度应在合理的区间内。',
  },
];
