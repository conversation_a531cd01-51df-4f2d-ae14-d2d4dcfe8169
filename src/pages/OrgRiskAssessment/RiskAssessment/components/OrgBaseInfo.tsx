import { useState } from 'react';
import org_bg from '@/assets/OrgRiskAssessment/org_bg.png';
import org_icon_1 from '@/assets/OrgRiskAssessment/org_icon_1.png';
import org_icon_2 from '@/assets/OrgRiskAssessment/org_icon_2.png';
import org_icon_3 from '@/assets/OrgRiskAssessment/org_icon_3.png';
import org_icon_4 from '@/assets/OrgRiskAssessment/org_icon_4.png';
import org_icon_5 from '@/assets/OrgRiskAssessment/org_icon_5.png';
import org_icon_6 from '@/assets/OrgRiskAssessment/org_icon_6.png';
import org_icon_7 from '@/assets/OrgRiskAssessment/org_icon_7.png';
import org_icon_8 from '@/assets/OrgRiskAssessment/org_icon_8.png';
import org_icon_9 from '@/assets/OrgRiskAssessment/org_icon_9.png';
import org_icon_10 from '@/assets/OrgRiskAssessment/org_icon_10.png';
import BlockContainer from '@/components/BlockContainer';

const OrgBaseInfo: React.FC = () => {
  const [options, setOptions] = useState([
    { label: '从业人员', value: '0', unit: '人', icon: org_icon_1, key: '1' },
    { label: '实验室面积', value: '0', unit: 'm', icon: org_icon_2, key: '2' },
    { label: '承担任务次数', value: '0', unit: '', icon: org_icon_3, key: '3' },
    { label: '承检年限', value: '0', unit: '', icon: org_icon_4, key: '4' },
    {
      label: '中高级技术人员占比',
      value: '0',
      unit: '%',
      icon: org_icon_5,
      key: '5',
    },
    {
      label: '检测设备数量',
      value: '0',
      unit: '台',
      icon: org_icon_6,
      key: '6',
    },
    { label: '检测能力项目', value: '0', unit: '', icon: org_icon_7, key: '7' },
    { label: '特殊检测装备', value: '0', unit: '', icon: org_icon_8, key: '8' },
    { label: '检测领域', value: '0', unit: '', icon: org_icon_9, key: '9' },
    {
      label: '检验产品类别',
      value: '0',
      unit: '类',
      icon: org_icon_10,
      key: '10',
    },
  ]);
  return (
    <BlockContainer title="机构基本信息">
      <div className=" w-full relative p-4 ">
        {options.map((_item) => (
          <div key={_item.key} className=" flex items-center py-1 flex-wrap">
            <img alt="" src={_item.icon} className=" w-4 h-4 mr-2" />
            <span>{_item.label}:</span>
            <span>{_item.value}</span>
            <span>{_item.unit}</span>
          </div>
        ))}
        <img
          alt=""
          src={org_bg}
          className=" absolute bottom-0 right-0 w-24 h-24"
        />
      </div>
    </BlockContainer>
  );
};

export default OrgBaseInfo