import { useState } from 'react';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const RiskAssessmentRecord: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    visualMap: {
      type: 'continuous',
      min: 0,
      max: 100,
      text: ['100', '0'],
      orient: 'horizontal',
      left: 'center',
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
    },
    yAxis: {
      type: 'category',
      data: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World'],
    },
    series: [
      {
        name: '2011',
        type: 'bar',
        data: [18203, 23489, 29034, 104970, 131744, 630230],
        barMaxWidth: 32,
      },
    ],
  });

  return (
    <BlockContainer title="风险评价记录">
      <div className=" w-full h-[450px]">
        <ChartsWrapper option={option} key="RiskAssessmentRecordChart" />
      </div>
    </BlockContainer>
  );
};

export default RiskAssessmentRecord;
