import { useState } from 'react';
import { Tabs, TabsProps } from 'antd';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';
import { AccuracyIndex, AuthenticityIndex, complianceIndex } from '../data';

const RiskIndex: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
    },
    radar: {
      // shape: 'circle',
      indicator: [
        { name: '判定依据合规' },
        { name: '检验依据合规' },
        { name: '检验项目合规' },
        { name: '检测能力合规' },
        { name: '未检出占比' },
        { name: '检验项目合格率' },
        { name: '检出值波动' },
        { name: '检出值重复' },
        { name: '不合格样品相近生产批次分析' },
        { name: '同产品检验结果分析' },
        { name: '设备载样超标分析' },
        { name: '同规模机构检验项次异常' },
      ],
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: [0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5],
          },
        ],
      },
    ],
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '合规性指标',
      children: (
        <div className=" px-4 text-[10px] text-[#75787b]">
          {complianceIndex.map((_item, _index) => (
            <div key={_index}>
              <span className=" font-semibold">{_item.label}</span>:
              {_item.value}
            </div>
          ))}
        </div>
      ),
    },
    {
      key: '2',
      label: '真实性指标',
      children: (
        <div className=" px-4 text-[10px] text-[#75787b]">
          {AuthenticityIndex.map((_item, _index) => (
            <div key={_index}>
              <span className=" font-semibold">{_item.label}</span>:
              {_item.value}
            </div>
          ))}
        </div>
      ),
    },
    {
      key: '3',
      label: '准确性指标',
      children: (
        <div className=" px-4 text-[10px] text-[#75787b]">
          {AccuracyIndex.map((_item, _index) => (
            <div key={_index}>
              <span className=" font-semibold">{_item.label}</span>:
              {_item.value}
            </div>
          ))}
        </div>
      ),
    },
  ];

  return (
    <BlockContainer title="风险指数">
      <div className=" py-4">
        <div className=" w-[60%] h-[300px] mx-auto">
          <ChartsWrapper option={option} key="RiskIndexChart" />
        </div>
        <div>
          <div className=" h-8 leading-8 font-semibold">指数说明</div>
          <Tabs defaultActiveKey="1" items={items} />
        </div>
      </div>
    </BlockContainer>
  );
};

export default RiskIndex;
