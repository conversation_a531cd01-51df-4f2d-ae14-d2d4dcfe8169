import { useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const Overview: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        startAngle: 200,
        endAngle: -20,
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [0.25, '#5B8FF9'],
              [0.5, '#FED70A'],
              [0.75, '#F0871C'],
              [1, '#F70016'],
            ],
          },
        },
        splitLine: {
          distance: -10,
          lineStyle: {
            width: 1,
            color: 'white',
          },
        },
        axisTick: {
          distance: -10,
          length: 6,
          lineStyle: {
            width: 1,
            color: 'white',
          },
        },
        axisLabel: {
          distance: -20,
          color: '#8d8d8d',
        },
        pointer: {
          length: '70%',
          width: 3,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value} 分',
          color: 'inherit',
          fontSize: 14,
          offsetCenter: [0, '30%'],
        },
        title: {
          offsetCenter: [0, '50%'],
          fontSize: 16,
        },
        data: [
          {
            value: 2,
            name: '风险评价总分',
          },
        ],
      },
    ],
  });

  const EvaluationScoreChartInfo = (
    <div className=" p-4 flex flex-col gap-1">
      <div>异常指数总分说明：</div>
      <div>
        <span className=" text-[#1d9328]">正常：</span>
        <span>
          分值为0，表示检验机构在风险分析中未发现问题线索，风险指标处于正常状态。
        </span>
      </div>
      <div>
        <span className=" text-[#5b8ff9]">低风险：</span>
        <span>
          分值在1~25之间，表示检验机构在风险分析中发现较少量的风险线索，且风险带来的影响较低，整体指标的风险状态低。
        </span>
      </div>
      <div>
        <span className=" text-[#fed70a]">一般风险：</span>
        <span>
          分值在26~50之间，表示检验机构在风险分析中发现少量的风险线索，且风险带来的影响低，整体指标的风险状态一般。
        </span>
      </div>
      <div>
        <span className=" text-[#f0871c]">较大风险：</span>
        <span>
          分值在51~75之间，表示检验机构在风险分析中发现较多的风险线索，且风险带来的影响较大，整体指标的风险状态较高。
        </span>
      </div>
      <div>
        <span className=" text-[#f70016]">重大风险：</span>
        <span>
          分值在76~100之间，表示检验机构在风险分析中发现大量的风险线索，且风险带来的影响重大，整体指标的风险状态较高。
        </span>
      </div>
    </div>
  );

  const [orgInfoOptions, setOrgInfoOptions] = useState([
    { label: '统一社会信用代码', value: '12330000470051830K' },
    { label: '联系人', value: '李康' },
    { label: '联系电话', value: '13867428077' },
    { label: '地址', value: '平乐街325号' },
  ]);

  const [taskSource, setTaskSource] = useState([
    '食品',
    '保健食品',
    '食用农产品',
  ]);

  return (
    <div className=" w-full bg-white flex  h-52">
      <div className=" h-full flex-1 flex flex-col p-4">
        <div className=" flex items-center gap-2">
          <span className=" text-2xl font-semibold">贵阳市疾病预防控制中心</span>
          {taskSource.map((_item, _index) => (
            <div key={_index} className=" rounded px-2 text-white bg-[#2080fe]">
              {_item}
            </div>
          ))}
        </div>
        <div className=" flex-1 flex items-center">
          <div className=" flex flex-wrap">
            {orgInfoOptions.map((_item, _index) => (
              <div key={_index} className=" w-1/2 py-1">
                {_item.label}: {_item.value}
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className=" w-64 h-full flex-shrink-0 relative">
        <ChartsWrapper key="EvaluationScoreChart" option={option} />
        <div className=" absolute top-[12%] right-[20%]">
          <Tooltip
            title={EvaluationScoreChartInfo}
            color="white"
            overlayInnerStyle={{ color: '#333' }}
            placement="left"
          >
            <InfoCircleOutlined />
          </Tooltip>
        </div>
      </div>
      <div className=" w-44 h-full flex flex-col justify-center gap-1">
        <span>风险线索</span>
        <span className=" font-semibold text-xl">1 项</span>
        <div className=" w-full flex gap-2">
          <span className=" text-[#f70016]">确证 1 项</span>
          <span className=" text-[#f0871c]">疑似 1 项</span>
        </div>
        <span className=" text-xs">风险评价时间： 2024-05-05</span>
      </div>
    </div>
  );
};

export default Overview;
