import { ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';

const SupervisoryInfo: React.FC = () => {
  const columns: ProColumns[] = [
    {
      title: '督查类别',
      dataIndex: 'eventCode',
    },
    {
      title: '考核时间',
      dataIndex: 'eventTime',
    },
    {
      title: '考核单位',
      dataIndex: 'area',
    },
    {
      title: '得分',
      dataIndex: 'eventAddr',
    },
    {
      title: '考核结果',
      dataIndex: 'eventContent',
    },
  ];
  return (
    <BlockContainer title="督查信息">
      <ProTable
        className=" w-full"
        columns={columns}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          let data: any = {};
          //   const _params: TEventListParams = {
          //     pageNum: params.current!,
          //     pageSize: params.pageSize!,
          //     status: '2',
          //     eventCode: params.eventCode,
          //     deptName: params.deptName,
          //     areaCode: selectArea,
          //     cityCode: selectCity,
          //   };
          //   if (params.time && params.time.length) {
          //     _params.startDate = params.time[0];
          //     _params.endDate = params.time[1];
          //   }
          //   setSearchParams(_params);
          //   const { code, data, msg } = await eventListApi(_params);
          //   if (code !== codeDefinition.QUERY_SUCCESS) {
          //     message.error(msg);
          //   }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        rowKey="id"
        search={false}
        options={false}
        pagination={false}
        dateFormatter="string"
        scroll={{
          x: 'max-content',
        }}
      />
    </BlockContainer>
  );
};

export default SupervisoryInfo;
