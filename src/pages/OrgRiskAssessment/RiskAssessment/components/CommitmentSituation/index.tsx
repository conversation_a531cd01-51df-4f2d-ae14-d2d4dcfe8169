import BlockContainer from '@/components/BlockContainer';
import MonthlyTrend from './MonthlyTrend';
import Statistic from './Statistic';
import TaskLevel from './TaskLevel';

const CommitmentSituation: React.FC = () => {
  return (
    <BlockContainer title="承担任务情况">
      <section>
        <Statistic />
      </section>
      <section className=" flex mt-10">
        <div className=" w-2/5 pr-[10px] h-[400px]">
          {/* 任务级别 */}
          <TaskLevel />
        </div>
        <div className=" w-3/5 pl-[10px] h-[400px]">
          {/* 月度趋势 */}
          <MonthlyTrend />
        </div>
      </section>
    </BlockContainer>
  );
};

export default CommitmentSituation;
