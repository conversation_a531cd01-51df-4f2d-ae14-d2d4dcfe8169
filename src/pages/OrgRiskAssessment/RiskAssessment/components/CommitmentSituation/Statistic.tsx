import { useState } from 'react';
import icon_1 from '@/assets/OrgRiskAssessment/icon_1.png';
import icon_2 from '@/assets/OrgRiskAssessment/icon_2.png';
import icon_3 from '@/assets/OrgRiskAssessment/icon_3.png';
import icon_4 from '@/assets/OrgRiskAssessment/icon_4.png';
import icon_5 from '@/assets/OrgRiskAssessment/icon_5.png';
import classNames from 'classnames';

const Statistic: React.FC = () => {
  const [options, setOptions] = useState([
    {
      label: '机构承检',
      value: '0',
      icon: icon_1,
      unit: '批次',
      key: '1',
      style: ' bg-[#e6f1ff]',
    },
    {
      label: '已完成',
      value: '0',
      icon: icon_2,
      unit: '批次',
      key: '2',
      style: ' bg-[#d9f8f3]',
    },
    {
      label: '检测项目',
      value: '0',
      icon: icon_3,
      unit: '项次',
      key: '3',
      style: ' bg-[#e6f1ff]',
    },
    {
      label: '出具检测报告',
      value: '0',
      icon: icon_4,
      unit: '批次',
      key: '4',
      style: ' bg-[#e5f4ff]',
    },
    {
      label: '覆盖品种',
      value: '0',
      icon: icon_5,
      unit: '类',
      key: '5',
      style: ' bg-[#ebebff]',
    },
  ]);

  return (
    <div className=" flex">
      {options.map((_item) => (
        <div key={_item.key} className=" w-1/5 px-2">
          <div
            className={classNames(
              'w-full p-4 rounded-xl flex items-center gap-2',
              _item.style
            )}
          >
            <img alt="" src={_item.icon} className=" w-8 h-8" />
            <div className=" flex-1 flex flex-col justify-evenly text-base">
              <div>{_item.label}</div>
              <div className=" font-semibold">
                {_item.value} {_item.unit}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Statistic;
