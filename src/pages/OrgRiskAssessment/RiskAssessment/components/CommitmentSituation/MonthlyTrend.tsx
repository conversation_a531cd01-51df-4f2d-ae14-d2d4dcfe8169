import { useState } from 'react';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const MonthlyTrend: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: ['#C23531', '#2F4554'],
    title: {
      text: '月度趋势',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330],
        type: 'line',
        areaStyle: {
          //   color: '#6E7D88',
        },
        name: '2022年',
      },
      {
        data: [500, 1100, 666, 0, 0, 0],
        type: 'line',
        areaStyle: {
          //   color: '#D5726F',
        },
        name: '2023年',
      },
    ],
  });

  return (
    <div className=" w-full h-full">
      <ChartsWrapper option={option} key="MonthlyTrendChart" />
    </div>
  );
};

export default MonthlyTrend;
