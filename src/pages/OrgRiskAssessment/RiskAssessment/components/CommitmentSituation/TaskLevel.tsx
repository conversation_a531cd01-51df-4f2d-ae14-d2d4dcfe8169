import { useState } from 'react';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

const TaskLevel: React.FC = () => {
  const [option, setOption] = useState<ECOption>({
    color: ['#C23531', '#2F4554', '#61A0A8', '#D48265'],
    title: {
      text: '任务级别',
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '65%'],
        data: [
          { value: 1, name: '国家抽检' },
          { value: 1, name: '省级抽检' },
          { value: 1, name: '市级抽检' },
          { value: 1, name: '县级抽检' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });

  return (
    <div className=" w-full h-full">
      <ChartsWrapper option={option} key="TaskLevelChart" />
    </div>
  );
};

export default TaskLevel;
