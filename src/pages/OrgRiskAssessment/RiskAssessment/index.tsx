/*
 * @Date: 2024-07-31 13:04:06
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-07-31 17:54:44
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskAssessment\index.tsx
 * @Description: 机构风险评价
 */
import { Button, Select } from 'antd';
import CommitmentSituation from './components/CommitmentSituation';
import OrgBaseInfo from './components/OrgBaseInfo';
import Overview from './components/Overview';
import RiskAssessmentRecord from './components/RiskAssessmentRecord';
import RiskIndex from './components/RiskIndex';
import SupervisoryInfo from './components/SupervisoryInfo';
import PageContainer from '@/components/PageContainer';

const RiskAssessment: React.FC = () => {
  return (
    <PageContainer>
      <header className=" flex h-16 items-center gap-4">
        <span>检验机构：</span>
        <Select className=" w-60"></Select>
        <Button type="primary">查看</Button>
      </header>
      <section>
        <Overview />
      </section>
      <section className=" flex mt-5">
        <div className=" w-2/3 pr-[10px] flex flex-col gap-5">
          {/* 风险指数 */}
          <RiskIndex />
          {/* 承担任务情况 */}
          <CommitmentSituation />
        </div>
        <div className=" w-1/3 pl-[10px] flex flex-col gap-5">
          {/* 机构基本信息 */}
          <OrgBaseInfo />
          {/* 风险评价记录 */}
          <RiskAssessmentRecord />
          {/* 督查信息 */}
          <SupervisoryInfo />
        </div>
      </section>
    </PageContainer>
  );
};

export default RiskAssessment;
