/*
 * @Date: 2024-07-30 16:42:04
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-07 14:13:50
 * @FilePath: \xr-qc-jk-web\src\pages\OrgRiskAssessment\RiskPerceptionIndex\index.tsx
 * @Description: 风险感知指标
 */
import { useRef, useState,useEffect } from 'react';
import { Button, message,Form,Input,Row,Col,Spin } from 'antd';
import {
  riskModelList,editIndex,riskModelEdit
} from '@/api/OrgRiskAssessment/api';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import './index.less';
import {
  EditableProTable,
} from '@ant-design/pro-components';

const RiskPerceptionIndex: React.FC = () => {


 const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
 const [dataSource, setDataSource] = useState<any>([]);
 const [tableList, setTableList] = useState<any>([]);
 const [totalRate, setTotalRate] = useState<any>(0);


 const [totalWeight, setTotalWeight] = useState([]); // 新增状态存储 weight 总和

  const [loading, setLoading] = useState<boolean>(false); // 新增 loading 状态

const [form] = Form.useForm();

const getRiskModelList = async () => {
  try {
    setLoading(true)
    const { code, msg, data} = await riskModelList();
    if (code === codeDefinition.QUERY_SUCCESS) {
      setTableList(data)
      const fromData = {
        compliance: data[0].rate,
        authenticity: data[1].rate,
        accuracy: data[2].rate,
      }
      form.setFieldsValue(fromData);
      const totalNum = data[0].rate *1 + data[1].rate *1 + data[2].rate *1 ;
      setTotalRate(totalNum)
      setLoading(false)
    } else {
      setLoading(false)
      message.error(msg);
    }
  } catch (error) {}
};


const handleEdit = async (row:any) => {
  try {
    const { code, msg, data} = await editIndex(row);
    if (code === codeDefinition.QUERY_SUCCESS) {
      message.success('保存成功')
      getRiskModelList()
    } else {
      message.error(msg);
      getRiskModelList()
    }
  } catch (error) {}
};



const handleSave = async () => {
  try {
    
    
    const formData = {...form.getFieldsValue()}
    if (formData.compliance.length> 0 &&  formData.authenticity.length> 0 &&  formData.accuracy.length> 0)
    {

        const params:any = [
          {
            id: tableList[0].id,
            name: tableList[0].name,
            rate: formData.compliance,
          },
          {
            id: tableList[1].id,
            name: tableList[1].name,
            rate: formData.authenticity,
          },
          {
            id: tableList[2].id,
            name: tableList[2].name,
            rate: formData.accuracy,
          }

        ]
        const { code, msg, data} = await riskModelEdit(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
      message.success('保存成功')
      } else {
        message.error(msg);
      }
    }else{
        message.error("请输入完整信息");
    }

  } catch (error) {}
};






  // 监听 dataSource 变化，计算 weight 总和
  useEffect(() => {
    if (tableList.length>0) {


    let sum:any = []
    tableList.forEach((element:any,index:number) => {
      console.log(element, 11111);
      
      sum[index] = element.indices.reduce((acc: number, item: any) => {
      const weight = parseFloat(item.weight) || 0;
      return acc + weight;
      }, 0);
    });

    // const sum = 
    setTotalWeight(sum);
    console.log(totalWeight, 777777);
    }

  }, [tableList]);




  useEffect( () => {
    getRiskModelList()
  }, []);



  const columns = [

    {
      title: '评价指标',
      dataIndex: 'indexName',

    },    {
      title: '依赖数据',
      dataIndex: 'dependencyOn',

    },    {
      title: '评价指标权重配置',
      dataIndex: 'weight',

    },    {
      title: '计算后真实权重',
      dataIndex: 'realWeight',

    },    {
      title: '评价指标说明',
      dataIndex: 'describe',

    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      fieldProps: (form:any, { rowKey, rowIndex }) => {
       
      return {
        rules: [
          {
            required: true,
            message: '请输入阈值',
          },
          {
            validator: (_:any, value:any) => {
              if (value === undefined || value === null) {
                return Promise.reject(new Error('请输入阈值'));
              }
              const numValue = parseFloat(value);
              if (isNaN(numValue) || numValue < 1) {
                return Promise.reject(new Error('阈值不能小于 1'));
              }
              return Promise.resolve();
            },
          },
        ],
      };
      },
    },
    {
      title: '指标原始分',
      dataIndex: 'score',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (text:any, record:any, _:any, action:any) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        // <a
        //   key="delete"
        //   onClick={() => {
        //     setDataSource(dataSource.filter((item) => item.id !== record.id));
        //   }}
        // >
        //   删除
        // </a>,
      ],
    },
  ];
  return (
    <div className='risk-model'>
      <div className='search-box box'>
        <div className='title'>风险维度权重配置</div>
        <Form
          name="basic"
           form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ remember: true }}
          autoComplete="off"
        >
        <Row gutter={[24, 24]}>
        <Col span={6}>
          <Form.Item
            label="合规性"
            name="compliance" 
            rules={[{ required: true, message: '请输入合规性' }]}>
            <Input 
            suffix={'%'}
            />
          </Form.Item>
          </Col>
        <Col span={6}>
          <Form.Item
            label="真实性"
            name="authenticity"
            rules={[{ required: true, message: '请输入真实性' }]}
          >
            <Input    suffix={'%'}/>
          </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="准确性"
              name="accuracy"
              rules={[{ required: true, message: '请输入准确性' }]}
            >
              <Input   suffix={'%'}/>
            </Form.Item>
          </Col>

          </Row>
        </Form>
        <div className='text'>温馨提示:风险维度权重之和必须为100%，当前以上权重论计为{totalRate}%。</div>
      </div>


      <div className='box'>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
          </div>
        ) : (

      
                tableList  && tableList.length && tableList.map((_item: any,index:number) => (

                    <div>
                    <EditableProTable
                      rowKey="id"
                      headerTitle={_item.name+_item.rate+'%'}
                      maxLength={5}
                      scroll={{
                        x: 960,
                      }}
                      recordCreatorProps={false}
                      loading={false}
                      columns={columns}
                      request={async () => ({
                        data: _item.indices,
                        total: 3,
                        success: true,
                      })}
                      onChange={setDataSource}
                      editable={{
                        type: 'multiple',
                        editableKeys,
                        onSave: async (rowKey, data, row) => {
                          const threshold = parseFloat(data.threshold);
                            if (isNaN(threshold) || threshold < 1 ) {
                              message.error('阈值不能小于 1');
                              setLoading(true)
                              setTimeout(()=>{
                              setLoading(false)
                              const tableData = [...tableList]
                              tableData[index].indices.threshold = 1
                              setTableList(tableData)
                              },300)
                            } else  {
                              handleEdit(data);
                            }
                        },
                        onChange: setEditableRowKeys,
                      }}
                    />
                    <div className='tips'>
                      温馨提示:评价指标权重配置之和必须为100%，当前以上权重合计为{totalWeight[index]}%; 计算后真实权重=风险维度权重*评价指标权重。
                    </div>
                    </div>
                    ))
              
         )}
      </div>
      <div  className='btn-wraper'>
           <Button type="primary"  onClick={handleSave}>
                    保存
            </Button>
      </div>
     
    </div>
  );
};

export default RiskPerceptionIndex;
