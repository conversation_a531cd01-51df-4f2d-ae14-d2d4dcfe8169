/*
 * @Author: LGX
 * @Date: 2024-03-29 14:05:43
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-29 14:18:39
 * @FilePath: \xr-qc-jk-web\src\pages\data.ts
 * @Description: 页面公共静态数据
 * 
 */
/**
 * @description 详情表单布局
 */
export const formItemLayout = {
  span: 8,
};

/**
 * @description 年份下拉框枚举
 */
export const yearList = [
  {
    value: '2024',
    label: '2024',
  },
  {
    value: '2023',
    label: '2023',
  },
]
const getYearList = () => {
  const minYear = 2023;
  const currentYear = new Date().getFullYear();
  // 计算起始年份（取两者较大者）
  const startYear = Math.max(minYear, currentYear);
  // 创建一个空数组用于存放年份选项
  const yearsArray: { value: number; label: number; }[] = [];
  // 往后推算，直到达到最小年份
  for (let year = startYear; year >= minYear; year--) {
    yearsArray.push({
      value: year,
      label: year,
    });
  }
  return yearsArray
}
export const yearListOnTable = getYearList()