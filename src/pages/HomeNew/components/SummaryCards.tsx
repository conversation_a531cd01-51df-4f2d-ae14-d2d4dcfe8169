// =======================================================================
// 文件: src/pages/Home/components/SummaryCards.tsx (顶部汇总信息卡片)
// =======================================================================
import React, { useEffect, useState } from 'react';
import { Spin, message } from 'antd';
import { getSummaryDataApi } from '@/api/home';
import { CodeSandboxOutlined, ApartmentOutlined, ClusterOutlined, ContainerOutlined, ExperimentOutlined } from '@ant-design/icons';

// --- 1. 定义 Card 的 props 和 state 类型 ---
interface StatCardProps {
  title: string;
  mainValue: string | number;
  subTitle?: string;
  subValue?: string | number;
  bgColor: string;
  iconPlaceholder: React.ReactNode;
  loading: boolean; // 新增 loading 状态
}
interface CardState {
  mainValue: string | number;
  subValue?: string | number;
}

// --- 2. 独立的 StatCard 子组件 ---
const StatCard: React.FC<StatCardProps> = ({
  title,
  mainValue,
  subTitle,
  subValue,
  bgColor,
  iconPlaceholder,
  loading,
}) => (
  <div
    className={`flex-1 p-4 rounded-lg text-white ${bgColor} flex justify-between items-center min-h-[120px]`}
  >
    <Spin spinning={loading} wrapperClassName="w-full">
      <div className="flex justify-between items-center w-full">
        {/* 左侧内容区 */}
        <div className="flex flex-col justify-between h-[90px]">
          {/* 顶部内容：标题和主数值 */}
          <div>
            <p className="text-sm opacity-90">{title}</p>
            <p className="text-4xl font-bold my-1">{mainValue}</p>
          </div>
          {/* 底部内容：副标题 */}
          <div className="flex items-center gap-2">
            {subTitle ? (
              <>
                <p className="text-xs opacity-80">{subTitle}</p>
                <p className="text-base font-semibold">{subValue}</p>
              </>
            ) : (
              <p className="text-xs opacity-80"> </p>
            )}
          </div>
        </div>

        {/* 右侧的图标占位符 */}
        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center self-center">
          <div className="w-10 h-10 bg-white/20 rounded-md flex items-center justify-center text-lg font-bold">
            {iconPlaceholder}
          </div>
        </div>
      </div>
    </Spin>
  </div>
);

// --- 3. 主组件，负责状态管理和数据获取 ---
const SummaryCards: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  // 为每个卡片创建独立的状态
  const [loading, setLoading] = useState(true);
  const [cdcData, setCdcData] = useState<CardState>({
    mainValue: 0,
    subValue: 0,
  });
  const [thirdPartyData, setThirdPartyData] = useState<CardState>({
    mainValue: 0,
    subValue: 0,
  });
  const [pathogenTotalData, setPathogenTotalData] = useState<CardState>({
    mainValue: 0,
    subValue: 0,
  });
  const [sampleTotalData, setSampleTotalData] = useState<CardState>({
    mainValue: 0,
    subValue: 0,
  });
  const [caseTotalData, setCaseTotalData] = useState<CardState>({
    mainValue: 0,
  }); // 这个卡片没有 subValue

  // 定义获取和设置数据的函数
  const fetchData = async () => {
    setLoading(true);
    try {
      const { code, data, msg } = await getSummaryDataApi();
      if (code === 200 && data) {
        // 将获取到的数据分别设置到对应的 state 中
        setCdcData({
          mainValue: data.cdcLabNum,
          subValue: data.sampleNum,
        });
        setThirdPartyData({
          mainValue: data.thirdPartyLabNum,
          subValue: data.thirdPartySampleNum,
        });
        setPathogenTotalData({
          mainValue: data.pathogenNum,
          subValue: data.pathogenType,
        });
        setSampleTotalData({
          mainValue: data.pathogenSampleNum,
          subValue: `${Number(data.pathogenPositiveRate || 0)}%`,
        });
        setCaseTotalData({ 
          mainValue: data.totalInfectiousDiseaseCases, 
          subValue: data.icdNum 
        });
      } else {
        messageApi.error(msg)
      }
    } catch (error) {
      console.error('获取汇总数据失败:', error);
      // 在这里可以处理错误，例如显示一个错误提示
    } finally {
      setLoading(false);
    }
  };

  // 在组件首次渲染时调用 fetchData
  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      {contextHolder}
      {/* 使用 flex 布局和 gap-4 来排列卡片 */}
      <div className="flex flex-row gap-4 w-full min-h-[150px]">
        <StatCard
          title="接入疾控机构实验室总数"
          mainValue={cdcData.mainValue}
          subTitle="采集病原样本(份)"
          subValue={cdcData.subValue}
          bgColor="bg-blue-500"
          iconPlaceholder={<ClusterOutlined />}
          loading={loading}
        />
        <StatCard
          title="接入第三方实验室总数"
          mainValue={thirdPartyData.mainValue}
          subTitle="采集病原样本(份)"
          subValue={thirdPartyData.subValue}
          bgColor="bg-green-500"
          iconPlaceholder={<CodeSandboxOutlined />}
          loading={loading}
        />
        <StatCard
          title="病原样本总数"
          mainValue={sampleTotalData.mainValue}
          subTitle="检出阳性率(%)"
          subValue={sampleTotalData.subValue}
          bgColor="bg-blue-600"
          iconPlaceholder={<ExperimentOutlined />}
          loading={loading}
        />
        <StatCard
          title="检出病原总数"
          mainValue={pathogenTotalData.mainValue}
          subTitle="检出病原体"
          subValue={pathogenTotalData.subValue}
          bgColor="bg-green-600"
          iconPlaceholder={<ApartmentOutlined />}
          loading={loading}
        />
        <StatCard
          title="传染病病例总数(例)"
          mainValue={caseTotalData.mainValue}
          subTitle="涉及传染病种数"
          subValue={caseTotalData.subValue}
          bgColor="bg-orange-500"
          iconPlaceholder={<ContainerOutlined />}
          loading={loading}
        />
      </div>
    </>
  );
};

export default SummaryCards;
