// =======================================================================
// 文件: src/pages/Dashboard/components/AssessmentResults.tsx (考核结果公示 - 前端分页滚动)
// =======================================================================
import React, { useState, useEffect } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Spin, message, Modal } from 'antd';
import BlockContainer from './BlockContainer';
import FileViewByStream from '@/components/FileViewByStream';
import { homePublicityListApi } from '@/api/publicity';
import { codeDefinition } from '@/constants';

// 定义数据项的类型
interface ResultItem {
  assessmentType: string;
  browseCount: number;
  endDate: string;
  orderNum: number;
  ossId: string;
  people: string;
  phone: string;
  publishDate: string;
  taskId: string;
  taskName: string;
  year: string;
}

// --- 组件主体 ---
const AssessmentResults: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState(true);
  const [allItems, setAllItems] = useState<ResultItem[]>([]);
  const [currentPage, setCurrentPage] = useState(0); // 页码从 0 开始
  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');
  // 文件预览弹窗
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);

  const ITEMS_PER_PAGE = 3; // 设置每页显示的数量

  // 模拟数据获取函数
  const fetchData = async () => {
    setLoading(true);
    try {
      const params: any = {
        pageNum: 1,
        pageSize: 999,
      };
      const { code, data, msg } = await homePublicityListApi(params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      setAllItems(data?.rows || []);
      setTotal(data?.total || 0)
    } catch (error) {
      console.error("获取考核结果公示失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时执行数据获取
  useEffect(() => {
    fetchData();
  }, []);

  // 计算总页数
  const totalPages = Math.ceil(allItems.length / ITEMS_PER_PAGE);

  // 切换到下一页
  const handleNext = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages - 1));
  };

  // 切换到上一页
  const handlePrev = () => {
    setCurrentPage(prev => Math.max(prev - 1, 0));
  };

  // 预览附件
  const handleView = (item: any) => {
    if (item?.ossId) {
      setOpenPreview(true)
      setPreviewFileId(item?.ossId)
    } else {
      messageApi.error('当前没有可预览的附件')
    }
  };

  // 判断按钮是否应该被禁用
  const isPrevDisabled = currentPage === 0;
  const isNextDisabled = currentPage >= totalPages - 1;

  return (
    <>
      {contextHolder}
      <BlockContainer title="考核结果公示" extra={<span className='ml-2'>总数：{total} 条</span>}>
        <Spin spinning={loading}>
          <div className="flex items-center justify-between h-full">
            {/* 左箭头按钮 */}
            <LeftOutlined
              className={`cursor-pointer text-2xl ${isPrevDisabled ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-blue-500'
                }`}
              onClick={!isPrevDisabled ? handlePrev : undefined}
            />

            {/* 中间滚动内容区 */}
            <div className="flex-1 overflow-hidden px-4">
              <div
                className="flex transition-transform duration-300 ease-in-out"
                style={{
                  // 计算滚动的距离：-100% * 当前页码
                  transform: `translateX(-${currentPage * 100}%)`,
                }}
              >
                {/* 渲染所有项目，但只有当前页的在视口中可见 */}
                {allItems.map(item => (
                  // - flex-shrink-0: 防止项目在 flex 容器中被压缩。
                  // - w-1/3: 每个项目占据容器宽度的 1/3 (因为每页3条)。
                  <div key={item.ossId} className="flex-shrink-0 w-1/3 text-center px-2" onClick={() => handleView(item)}>
                    <a
                      href="#"
                      className="text-sm text-[#009AFF] hover:text-[#009AFF] hover:underline truncate block"
                      title={item.taskName} // 添加 title 属性，鼠标悬浮时可看完整标题
                    >
                      {item.taskName}
                    </a>
                  </div>
                ))}
              </div>
            </div>

            {/* 右箭头按钮 */}
            <RightOutlined
              className={`cursor-pointer text-2xl ${isNextDisabled ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-blue-500'
                }`}
              onClick={!isNextDisabled ? handleNext : undefined}
            />
          </div>
        </Spin>
        <Modal
          width="90%"
          title="文件预览"
          onCancel={() => setOpenPreview(false)}
          open={openPreview}
          footer={null}
          destroyOnClose
        >
          <FileViewByStream fileId={previewFileId} isPreview />
        </Modal>
      </BlockContainer>
    </>

  );
};

export default AssessmentResults;