// =======================================================================
// 文件: src/pages/Home/components/BlockContainer.tsx (一个可复用的区块容器)
// =======================================================================
import React from 'react';
import { Card } from 'antd';
import type { CardProps } from 'antd';

// 组件的 props 类型保持不变，并继承所有 antd Card 的 props，以获得更高灵活性
type BlockContainerProps = {
  title: string | React.ReactNode;
  className?: string;
  children: React.ReactNode;
  extra?: React.ReactNode;
} & CardProps;

/**
 * @description 一个基于 Ant Design Card 的通用区块容器。
 * 它解决了标题固定、内容区独立滚动的问题。
 * @param {BlockContainerProps} props - 组件的属性
 */
const BlockContainer: React.FC<BlockContainerProps> = ({
  title,
  className = '',
  children,
  extra,
  ...rest // 接收任何其他传递给 antd Card 的 props
}) => {
  return (
    <Card
      title={title}
      extra={extra}
      // Card 本身需要是 flex 布局，才能让 body 部分自适应高度
      // h-full 使其填满父容器的高度
      className={`h-full flex flex-col shadow-sm ${className}`}
      // 保证标题栏有最小高度，且样式统一
      // 这是解决问题的关键：
      // flex: 1 -> 让 body 区域填充除了 head 之外的所有剩余空间
      // overflow: 'auto' -> 当 body 内容超出其高度时，自动显示滚动条
      // padding: '16px' -> 统一内容区的内边距，使其与之前的版本视觉上保持一致
      styles={{
        header: { minHeight: '48px', borderBottom: '1px solid #f0f0f0' },
        body: { display: 'flex', flexDirection: 'column', flex: 1, padding: '16px' }
      }}
      {...rest}
    >
      {children}
    </Card>
  );
};

export default BlockContainer;