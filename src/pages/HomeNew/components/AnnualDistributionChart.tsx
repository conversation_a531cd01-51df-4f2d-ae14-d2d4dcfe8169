// =======================================================================
// 文件: src/pages/Dashboard/components/AnnualDistributionChart.tsx (年度分布图表)
// =======================================================================
import React, { useState, useEffect } from 'react';
import { Spin } from 'antd';
import { cloneDeep } from 'lodash'; // 引入 cloneDeep 来安全地更新 option
import ChartsWrapper from '@/components/ChartsWrapper'; // 引入 ECharts 封装组件
import BlockContainer from './BlockContainer'; // 引入通用容器
import { ECOption } from '@/hooks/useEcharts'; // 引入 ECharts 配置类型
import { getAnnualDistributionApi } from '@/api/home';

// 模拟 API 返回的数据结构
interface ChartData {
  categories: string[]; // X 轴类目, e.g., ['1月', '2月', ...]
  collectedSamples: number[];
  testedSamples: number[];
  positiveSamples: number[];
  positiveRate: number[];
}

// 初始化的 ECharts 配置，数据部分为空
const initialOption: ECOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    formatter: (params: any) => { // antd v5 建议为 params 添加类型
      let tooltipHtml = `${params[0].name}<br/>`;
      params.forEach((param: any) => {
        const color = param.color;
        const seriesName = param.seriesName;
        const value = param.value;
        const marker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
        if (seriesName === '阳性率') {
          tooltipHtml += `${marker} ${seriesName}: ${value}%<br/>`;
        } else {
          tooltipHtml += `${marker} ${seriesName}: ${value}<br/>`;
        }
      });
      return tooltipHtml;
    },
    backgroundColor: 'rgba(255,255,255,0.5)'
  },
  legend: {
    data: ['采集样本数', '检测样本数', '阳性样本数', '阳性率'],
    bottom: 10,
    itemWidth: 14,
    itemHeight: 14,
  },
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '12%', // 为图例留出更多空间
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: [], // 初始为空，由 API 填充
    },
  ],
  yAxis: [
    { type: 'value', name: '数量' },
    { type: 'value', name: '百分比', axisLabel: { formatter: '{value}%' } },
  ],
  series: [
    { name: '采集样本数', type: 'bar', barWidth: 10, data: [], itemStyle: { color: '#5470C6' } },
    { name: '检测样本数', type: 'bar', barWidth: 10, data: [], itemStyle: { color: '#91CC75' } },
    { name: '阳性样本数', type: 'bar', barWidth: 10, data: [], itemStyle: { color: '#EE6666' } },
    { name: '阳性率', type: 'line', yAxisIndex: 1, data: [], itemStyle: { color: '#FAC858' }, smooth: true },
  ],
};


const AnnualDistributionChart: React.FC = () => {
  // 使用 state 管理 option 和 loading 状态
  const [loading, setLoading] = useState(true);
  const [option, setOption] = useState<ECOption>(initialOption);

  // 定义获取和设置图表数据的函数
  const fetchChartData = async () => {
    try {
      setLoading(true);
      const { code, data, msg } = await getAnnualDistributionApi();
      if (code !== 200 && !data) throw new Error(msg);

      // 使用 cloneDeep 创建一个新的 option 对象，避免直接修改 state
      const newOption = cloneDeep(initialOption);
      // 处理数据
      const categories = data.map((item: any) => (item.month + '月'));
      const collectedSamples = data.map((item: any) => item.sample);
      const testedSamples = data.map((item: any) => item.detect);
      const positiveSamples = data.map((item: any) => item.positive);
      const positiveRate = data.map((item: any) => item.positiveRate);
      // 用获取到的数据填充 newOption
      if (newOption.xAxis && Array.isArray(newOption.xAxis)) {
        (newOption.xAxis[0] as any).data = categories
      }
      if (newOption.series && Array.isArray(newOption.series)) {
        newOption.series[0].data = collectedSamples;
        newOption.series[1].data = testedSamples;
        newOption.series[2].data = positiveSamples;
        newOption.series[3].data = positiveRate;
      }
      console.log(newOption)
      // 更新 option state，触发图表重渲染
      setOption(newOption);

    } catch (error) {
      console.error("获取年度分布图表数据失败:", error);
      // 这里可以处理错误，例如显示一个错误状态或提示
    } finally {
      // 无论成功或失败，最后都将 loading 状态设为 false
      setLoading(false);
    }
  };

  // 在组件首次加载时调用 fetchChartData
  useEffect(() => {
    fetchChartData();
  }, []);

  return (
    <BlockContainer title="病原样本本年度分布">
      {
        loading ? <Spin spinning={loading} className="flex-1 flex justify-center items-center" /> :
          <div className="flex-1 flex flex-wrap">
            <ChartsWrapper option={option} key={JSON.stringify(option)} />
          </div>
      }
    </BlockContainer>
  );
};

export default AnnualDistributionChart;