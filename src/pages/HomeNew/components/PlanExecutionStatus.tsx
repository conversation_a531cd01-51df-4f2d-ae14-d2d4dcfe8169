// =======================================================================
// 文件: src/pages/Home/components/PlanExecutionStatus.tsx (计划执行情况)
// =======================================================================
import React, { useEffect, useState } from 'react';
import { Progress, Select, Spin, Tooltip, message, Empty } from 'antd';
import { getPlanExecutionApi } from '@/api/home';
import BlockContainer from './BlockContainer';
import { codeDefinition } from '@/constants';

// 定义API返回的数据项类型
interface PlanDataItem {
  cityName: string; // 机构名
  yearPlan: string; // 计划总数
  sample: string; // 采集样本数
  detection: string; // 检测样本数
  finished: string; // 完成数
  finishedRate: string; // 完成率
}

const PlanExecutionStatus: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [planData, setPlanData] = useState<PlanDataItem[]>([]);
  const [year, setYear] = useState('2025');
  const [messageApi] = message.useMessage();

  const fetchData = async (currentYear: string) => {
    try {
      setLoading(true);
      const { code, data, msg } = await getPlanExecutionApi(currentYear);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }

      // 假设后端返回的数据需要处理
      const processedData = data.map((item: any) => ({
        ...item,
        // 您可以根据实际返回的字段名进行映射
        sample: item.sample || '/',
        detection: item.detection || '/',
        finishedRate: (Number(item.finishedRate || 0) * 100).toFixed(2),
      }));

      setPlanData(processedData);
    } catch (err) {
      console.error("Error fetching plan execution data:", err);
      messageApi.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(year);
  }, [year]);

  const extraNode = (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-700">年度 :</span>
      <Select value={year} onChange={setYear} className="w-[150px]">
        <Select.Option value="2025">2025年</Select.Option>
        <Select.Option value="2024">2024年</Select.Option>
      </Select>
    </div>
  );

  return (
    <BlockContainer title="监测计划执行情况" extra={extraNode}>
      {loading ? (
        <div className="h-full flex justify-center items-center">
          <Spin spinning={loading} />
        </div>
      ) : (
        // 2. (修改) 采用 Flex 布局，分离固定表头和滚动内容区
        <div className='flex-1 flex justify-center items-center'>
          {
            !planData.length ? <Empty /> :
              <div className="flex flex-col h-full w-full">
                {/* ---- 固定表头 ---- */}
                <div className="flex justify-between items-center px-4 py-2 border-b border-gray-200 text-[13px] font-semibold text-gray-600">
                  <div className="flex-1">机构名称</div>
                  <div className="w-[40px] text-center">计划</div>
                  <div className="w-[60px] text-center">采集样本</div>
                  <div className="w-[60px] text-center">检测样本</div>
                  <div className="w-[40px] text-right">完成率</div>
                </div>

                {/* ---- 可滚动内容区 ---- */}
                <div className="flex-1 flex flex-col gap-5 pt-2 overflow-y-auto">
                  {planData.map((item, index) => (
                    // 3. (修改) 更新每行数据的布局以对齐表头
                    <div key={item.cityName + index} className="px-2">
                      <Tooltip
                        title={
                          <div>
                            <div className='text-blod'>{item.cityName}</div>
                            <div>
                              {`计划数: ${item.yearPlan}； 采集样本数: ${item.sample}； 检测样本数: ${item.detection}； 完成率: ${item.finishedRate}%`}
                            </div>
                          </div>
                        }
                        color="#333"
                      >
                        <div className="border rounded-md px-2 py-1 hover:bg-gray-50">
                          <div className="flex justify-between items-center text-sm mb-1 text-gray-500 min-w-0">
                            {/* 序号和机构名称 */}
                            <div className="flex items-center gap-2 flex-1 truncate">
                              <span className="font-medium text-gray-400">
                                {String(index + 1).padStart(2, '0')}
                              </span>
                              <span className="text-gray-800 truncate">{item.cityName}</span>
                            </div>
                            {/* 右侧数据 */}
                            <div className="flex items-center flex-shrink-0">
                              <div className="w-[40px] text-center text-gray-800">{item.yearPlan}</div>
                              <div className="w-[60px] text-center text-gray-800">{item.sample}</div>
                              <div className="w-[60px] text-center text-gray-800">{item.detection}</div>
                              <div className="w-[40px] text-right font-medium text-[#429CF0]">
                                {item.finishedRate}%
                              </div>
                            </div>
                          </div>
                          {/* 进度条 */}
                          <Progress
                            strokeColor="#429CF0"
                            percent={Number(item.finishedRate)}
                            showInfo={false}
                            trailColor="#E5E7EB"
                            size="small"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  ))}
                </div>
              </div>
          }
        </div>

      )}
    </BlockContainer>
  );
};

export default PlanExecutionStatus;