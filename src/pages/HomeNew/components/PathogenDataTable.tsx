// =======================================================================
// 文件: src/pages/Home/components/PathogenDataTable.tsx (病原检测数据表格)
// =======================================================================
import React, { useState, useEffect, useCallback } from 'react';
import dayjs from 'dayjs';
// 引入 Ant Design 的相关组件
import {
  message,
  DatePicker,
  Select,
  Space,
  Table,
  Tooltip,
  Form,
  TablePaginationConfig, // 引入分页配置类型
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import BlockContainer from './BlockContainer';
// import BlockContainer from '@/components/BlockContainer';
import { getPathogenDataApi } from '@/api/home';
import { codeDefinition } from '@/constants';
import { getDict } from '@/api/dict';

const { RangePicker } = DatePicker;

// 定义查询参数的接口，增强代码可读性和类型安全
interface QueryParams {
  pageNum: number;
  pageSize: number;
  level?: string;
  sampleStartDate?: string;
  sampleEndDate?: string;
  orderByColumn?: string;
  isAsc?: string;
}

const PathogenDataTable: React.FC = () => {
  const [form] = Form.useForm();
  const [messageApi] = message.useMessage();
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  // 实验室类型
  const [laboratoryType, setLaboratoryType] = useState<any[]>([]);
  // 分页总数
  const [total, setTotal] = useState(0);

  // 1. 将所有查询条件整合到一个 state 中，方便管理和作为 useEffect 的依赖
  const [queryParams, setQueryParams] = useState<QueryParams>({
    pageNum: 1,
    pageSize: 10, // 初始每页条数
    level: '2', // 表单初始值
    // 使用 dayjs 设置默认日期
    sampleStartDate: "",
    sampleEndDate: "",
    // sampleStartDate: dayjs().subtract(3, 'month').format('YYYY-MM-DD'),
    // sampleEndDate: dayjs().format('YYYY-MM-DD'),
  });

  const rangePresets: {
    label: string;
    value: any[];
  }[] = [
      { label: '最近一周', value: [dayjs().subtract(7, 'days'), dayjs()] },
      { label: '最近一月', value: [dayjs().subtract(1, 'month'), dayjs()] },
      { label: '最近一季度', value: [dayjs().subtract(3, 'months'), dayjs()] },
      { label: '最近半年', value: [dayjs().subtract(6, 'months'), dayjs()] },
      { label: '最近一年', value: [dayjs().subtract(1, 'year'), dayjs()] },
    ];

  // 定义表格的列配置
  const columns = [
    {
      title: '序号',
      key: 'id',
      width: 60,
      align: 'center' as const,
      // 使用分页参数计算序号，使其在跨页时也能连续
      render: (text: any, record: any, index: number) =>
        `${(queryParams.pageNum - 1) * queryParams.pageSize + index + 1}`,
    },
    {
      title: '实验室',
      dataIndex: 'labName',
      key: 'labName',
      width: 300,
      align: 'center' as const,
      render: (text: string) => <a href="#">{text}</a>,
    },
    {
      title: '采集样本数 (份)',
      dataIndex: 'sample',
      key: 'sample',
      align: 'center' as const,
    },
    {
      title: '检测样本数 (份)',
      dataIndex: 'detect',
      key: 'detect',
      align: 'center' as const,
    },
    {
      title: '阳性样本数 (份)',
      dataIndex: 'positive',
      key: 'positive',
      align: 'center' as const,
    },
    {
      title: '阳性率 (%)',
      dataIndex: 'positiveRate',
      key: 'positiveRate',
      align: 'center' as const,
      render: (val: any) => `${val}%`,
    },
  ];

  // 2. 将数据获取逻辑封装成一个函数，使用 useCallback 避免不必要的重渲染
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      // API 请求使用 state 中的 queryParams
      const { code, data, msg } = await getPathogenDataApi(queryParams);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      setTableData(data.rows || []);
      setTotal(data.total || 0);
    } catch (err) {
      // 错误处理可以更具体
      messageApi.error('数据加载失败');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [queryParams, messageApi]); // 依赖 queryParams，当它变化时 fetchData 会更新

  // 获取实验室类型列表 (仅在组件挂载时执行一次)
  const getLaboratoryList = async () => {
    try {
      const { code, data } = await getDict('lab_type');
      if (code === codeDefinition.QUERY_SUCCESS) {
        setLaboratoryType(data);
      }
    } catch (error) {
      console.error('获取实验室类型失败', error);
    }
  };

  useEffect(() => {
    getLaboratoryList();
  }, []);

  // 3. 使用一个 useEffect 来监听 queryParams 的变化，并触发数据请求
  useEffect(() => {
    fetchData();
  }, [fetchData]); // 依赖 fetchData 函数

  // 4. 处理表单筛选条件变化
  const handleFormChange = (changedValues: any, allValues: any) => {
    const { sampleDate, ...rest } = allValues;

    // 格式化日期范围
    const sampleStartDate = sampleDate?.[0]?.format('YYYY-MM-DD');
    const sampleEndDate = sampleDate?.[1]?.format('YYYY-MM-DD');

    // 当筛选条件变化时，重置页码到第一页
    setQueryParams((prev) => ({
      ...prev,
      ...rest,
      sampleStartDate,
      sampleEndDate,
      pageNum: 1, // 重置页码
    }));
  };

  // 5. 处理分页器变化
  const handleTableChange = (pagination: TablePaginationConfig) => {
    setQueryParams((prev) => ({
      ...prev,
      pageNum: pagination.current || 1,
      pageSize: pagination.pageSize || 10,
    }));
  };

  const titleNode = (
    <div className="flex items-center gap-2">
      <h3 className="text-base font-semibold text-gray-800 m-0">
        病原检测数据
      </h3>
      <Tooltip title="展示各地实验室上报的病原类样本数据，阳性率=阳性样本数/检测样本数*100%。">
        <InfoCircleOutlined className="text-gray-400" />
      </Tooltip>
    </div>
  );

  // 6. 将表单的初始值与 queryParams 同步
  const extraNode = (
    <div className="flex items-center gap-4">
      <Form
        form={form}
        layout="inline"
        initialValues={{
          level: queryParams.level,
          // sampleDate: [
          //   dayjs(queryParams.sampleStartDate),
          //   dayjs(queryParams.sampleEndDate),
          // ],
        }}
        onValuesChange={handleFormChange}
      >
        <Form.Item name="level" label="实验室类型">
          <Select
            style={{ width: 160 }}
            options={laboratoryType}
            fieldNames={{
              label: 'dictLabel',
              value: 'dictValue',
            }}
          />
        </Form.Item>
        <Form.Item name="sampleDate" label="采样日期">
          <RangePicker presets={rangePresets} />
        </Form.Item>
      </Form>
    </div>
  );

  return (
    <BlockContainer
      title={titleNode}
      extra={extraNode}
      className="min-h-[30vh]"
    >
      <Table
        loading={loading}
        columns={columns}
        dataSource={tableData}
        rowKey="id" // 建议为表格行提供一个唯一的 key
        size="middle"
        scroll={{ y: 310 }}
        // 7. 使用 Table 自带的分页器，并进行配置
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: total,
          showSizeChanger: true, // 显示 pageSize 切换器
          showQuickJumper: true, // 显示快速跳转
          showTotal: (total) => `共 ${total} 条`, // 显示总数
          pageSizeOptions: ['10', '15', '30', '50'], // 自定义每页条数选项
        }}
        // 绑定分页变化事件
        onChange={handleTableChange}
      />
    </BlockContainer>
  );
};

export default PathogenDataTable;