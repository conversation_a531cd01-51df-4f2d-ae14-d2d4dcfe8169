// =======================================================================
// 文件: src/pages/Home/components/TodoList.tsx (待办任务组件)
// =======================================================================
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Dropdown, message, Spin } from 'antd';
import type { MenuProps } from 'antd';
import { downloadFile } from '@/api/file';
import { getOperationGuideList, getTodoList } from '@/api/home';
import { codeDefinition } from '@/constants';
import { usePermissionRouterStore } from '@/store';
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { clone } from 'lodash';
import BlockContainer from './BlockContainer';

// 定义单个待办事项的数据类型
interface TodoItemData {
  label: string;
  count: number;
  path: string; // 每个待办项对应的跳转路径
}

// 定义整个组件所需的数据结构
interface TodoData {
  quality: TodoItemData[]; // 检测质量的待办项
  pathogen: TodoItemData[]; // 病原监测的待办项
  lab: TodoItemData[]; // 实验室评级的待办项
}

const TodoList: React.FC = () => {
  // 使用 React Router 的 navigate 函数来进行页面跳转
  const navigate = useNavigate();

  const [messageApi] = message.useMessage();

  const { menuData, setChildMenuData } = usePermissionRouterStore();

  // 使用 state 来追踪当前选中的 Tab。默认为第一个 
  const [activeTab, setActiveTab] = useState<string>();

  // 待办列表加载
  const [loading, setLoading] = useState<boolean>(false);

  // “操作指引”下拉菜单的内容
  const [menuItems, setMenuItems] = useState<MenuProps['items']>([]);

  // 下拉菜单的原始数据
  const [menuItemsList, setMenuItemsList] = useState<any[]>([]);

  // 待办数据
  const [toDoList, setToDoList] = useState<any[]>([]);

  // 待办数据-顶部 Tab 的配置数据
  const [tabsConfig, setTabsConfig] = useState<any[]>();

  /**
   * 处理待办事项点击事件的函数
   */
  const handleItemClick = (_item: any) => {
    if (!menuData?.length) return;
    const arr: any[] = clone(menuData);
    function genMenu(menu: any[]) {
      let mItem: any = [];
      menu.forEach((item) => {
        let m: any = {};
        m.key = item.completePath || item.path;
        m.icon = item.meta?.icon;
        m.label = item.meta?.title;
        if (item.children) {
          m.children = genMenu(item.children);
        }
        mItem.push(m);
      });
      return mItem;
    }
    const menu = genMenu(arr);

    const _topMenuKey = _item?.path.split('/')[0];
    if (!_topMenuKey) return;

    const _topMenuChildren = menu?.find((_i: Record<string, any>) =>
      _i?.key.includes(_topMenuKey)
    )?.children;
    if (!_topMenuChildren?.length) return;
    setChildMenuData([..._topMenuChildren]);

    navigate(_item?.path);
  };

  // 操作指引点击跳转
  const handleMenuClick: MenuProps['onClick'] = async ({
    key,
  }: {
    key: string;
  }) => {
    try {
      if (!key) {
        return;
      }
      const selectItem = menuItemsList.filter((item: any) => item.id === key);
      if (selectItem.length) {
        await downloadFile(
          selectItem[0]?.id,
          selectItem[0]?.fileName,
          '/system/operation/guide/download'
        );
      }
    } catch (e) { }
  };

  // 操作指引-下拉菜单
  const extraNode = (
    <div className="flex items-center gap-4">
      <Dropdown
        menu={{ items: menuItems, onClick: handleMenuClick }}
        trigger={['click']}
      >
        <Button>
          操作指引 <DownOutlined />
        </Button>
      </Dropdown>
    </div>
  );

  // 获取指引列表
  const getMenuItems = async () => {
    try {
      const params: any = {
        pageNum: 1,
        pageSize: 100,
      };
      const { code, data, msg } = await getOperationGuideList(params);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
      }
      const tempArr: any[] = data.map((item: any) => {
        return {
          key: item.id,
          label: item.name + '使用说明书',
        };
      });
      setMenuItemsList(data);
      setMenuItems([...tempArr]);
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * 获取待办事项列表
   */
  const queryTodoList = async () => {
    try {
      setLoading(true);
      const { code, data, msg } = await getTodoList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      setTabsConfig(data || []);
      if (data && data.length) {
        const titleIndex = data[0]?.modelKey
        const contentList = data.filter((item: any) => item.modelKey === titleIndex)[0]?.commissions || []
        setActiveTab(titleIndex)
        setToDoList([...contentList])
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryTodoList();
    getMenuItems();
  }, []);

  return (
    <BlockContainer title="待办任务" extra={extraNode}>
      <Spin spinning={loading} className="h-full flex justify-center items-center">
        <div className="flex flex-col h-full">
          {/* 顶部 Tab 区域 */}
          <div className="flex items-center justify-around pb-4 border-b border-gray-200">
            {tabsConfig?.map((tab: any) => (
              <div
                key={tab.modelKey}
                className="flex items-center gap-1.5 cursor-pointer"
                onClick={() => {
                  setActiveTab(tab.modelKey as keyof TodoData)
                  const todoLidtNow = tabsConfig.filter((item: any) => item.modelKey === tab.modelKey)[0]?.commissions || []
                  setToDoList([...todoLidtNow])
                }}
              >
                {/* Tab 标题，根据是否选中来决定是否加粗 */}
                <span
                  className={`text-sm ${activeTab === tab.modelKey
                    ? 'font-bold text-gray-800'
                    : 'text-gray-600'
                    }`}
                >
                  {tab.modelName}
                </span>
                {/* 红色徽标 */}
                <span className="bg-red-500 text-white text-xs w-7 h-5 flex items-center justify-center rounded-full font-semibold">
                  {tab.num}
                </span>
              </div>
            ))}
          </div>

          {/* 底部内容区域 */}
          <div className="flex-1 flex items-center justify-around text-center px-4">
            {toDoList?.map((item: any) => (
              // 单个待办事项，绑定点击事件
              <div
                key={item.id}
                className="cursor-pointer min-w-[60px]"
                onClick={() => handleItemClick(item)}
              >
                {/* 待办数量，使用图片中的蓝色字体 */}
                <p className="text-2xl font-bold text-[#016FA0]">{item.num}</p>
                {/* 待办事项名称 */}
                <p className="text-sm text-gray-500 mt-1">{item.name}</p>
              </div>
            ))}
          </div>
        </div>
      </Spin>
    </BlockContainer>
  );
};

export default TodoList;
