// =======================================================================
// 文件: src/pages/Dashboard/index.tsx (仪表盘主页面 - 组装所有组件)
// =======================================================================
import React from 'react';
import AnnualDistributionChart from './components/AnnualDistributionChart';
import AssessmentResults from './components/AssessmentResults';
import PathogenDataTable from './components/PathogenDataTable';
import PlanExecutionStatus from './components/PlanExecutionStatus';
// 引入所有子组件
import SummaryCards from './components/SummaryCards';
import TodoList from './components/TodoList';
import "./index.less"

const DashboardPage: React.FC = () => {
  return (
    // 页面最外层容器，设置灰色背景和内边距
    <div className="px-4 bg-gray-100 min-h-[calc(100vh_-_80px)] overflow-y-auto">
      <div className="flex flex-col gap-4">
        {/* 第一行: 顶部汇总卡片 */}
        <SummaryCards />
        {/* 第二行: 主内容区，使用 flex 布局分为左右两栏 */}
        <div className="flex flex-row gap-4">
          {/* 左侧栏，宽度占 8/12 */}
          <div className="flex flex-col gap-4 w-8/12">
            {/* 左栏第一个块：数据表格 */}
            <div className="h-[500px]">
              <PathogenDataTable />
            </div>
            {/* 左栏第二个块：年度图表 */}
            <div className="h-[400px]">
              <AnnualDistributionChart />
            </div>
            {/* 左栏第三个块：考核结果 */}
            <div className="h-[100px]">
              <AssessmentResults />
            </div>
          </div>

          {/* 右侧栏，宽度占 4/12 */}
          <div className="flex flex-col gap-4 w-4/12">
            {/* 右栏第一个块：待办任务 */}
            <div className="h-[200px]">
              <TodoList />
            </div>
            {/* 右栏第二个块：计划执行情况，flex-1 使其填充剩余高度 */}
            <div className="flex-1 max-h-[815px]">
              <PlanExecutionStatus />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
