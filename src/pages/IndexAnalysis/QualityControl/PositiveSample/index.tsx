/*
 * @Date: 2024-08-16 08:51:21
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-08-16 09:08:52
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\QualityControl\PositiveSample\index.tsx
 * @Description: 阳性样本
 */
import { useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { positiveSampleListApi } from '@/api/QualityControl/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';
import Detail from '@/pages/Pathogen/Smaple/components/Detail';

type TPositiveSampleProps = {
  analyzeType: number; //指标分析类型,1送检及时率,2病原检测率,3病原检测及时率,4报告及时性
  orgId: string;
};

const PositiveSample: React.FC<TPositiveSampleProps> = ({
  analyzeType,
  orgId,
}) => {
  const [pageSize, setPageSize] = useState(10);
  const [openDetail, setOpenDetail] = useState(false);
  const [detailId, setDetailId] = useState<string>('');

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '样本名称',
      dataIndex: 'sampleName',
    },
    {
      title: '初检日期',
      dataIndex: 'firstInspectDate',
      hideInSearch: true,
    },
    {
      title: '复核日期',
      dataIndex: 'checkDate',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => (
        <Button
          type="link"
          size="small"
          onClick={() => {
            setDetailId(record.detailId);
            setOpenDetail(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
            analyzeType,
            orgId,
          };
          delete _params.current;
          const { code, data, msg } = await positiveSampleListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <Drawer
        width="80%"
        title="样本检测详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail
          close={() => setOpenDetail(false)}
          detailId={detailId}
          curSelectedPlanInfo={{}}
        />
      </Drawer>
    </PageContainer>
  );
};

export default PositiveSample;
