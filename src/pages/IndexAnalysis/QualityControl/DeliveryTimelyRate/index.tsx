/*
 * @Date: 2024-08-07 17:50:12
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-19 16:19:43
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\QualityControl\DeliveryTimelyRate\index.tsx
 * @Description: 送检及时率分析
 */
import { useEffect, useRef, useState } from 'react';
import { Drawer, message } from 'antd';
import { deliveryTimelyRageListApi,submissionStackChart } from '@/api/QualityControl/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import PositiveSample from '../PositiveSample';
import './index.less';
import { ECOption } from "@/hooks/useEcharts"
import { cloneDeep } from "lodash"
import ChartsWrapper from "@/components/ChartsWrapper"

const DeliveryTimelyRate: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  const [open, setOpen] = useState(false);
  const [curOrgId, setCurOrgId] = useState('');

  const actionRef = useRef<ActionType>();

  const columns:any = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '检测机构（上报单位）名称',
      dataIndex: 'orgName',
      formItemProps: {
        labelCol: { span: 12 },
      },
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '及时送检样本数量',
      dataIndex: 'timelySampleCount',
      hideInSearch: true,
    },
    {
      title: '送检及时率',
      dataIndex: 'timelyPercent',
      hideInSearch: true,
    },
    {
      title: '采样日期',
      dataIndex: 'sampleDate',
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()],
    },
  ];



  const [option, setOption] = useState<ECOption>({
    color: ['rgba(18,118,255)', 'rgba(255,144,45)', '#12D2DE'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      textStyle: {
        color: '#999'
      },
      itemHeight: 12,
      itemWidth: 12,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: [],
        axisLabel: {
          rotate: 60,
          color: '#999',
        // 使用 formatter 函数处理文字显示
          formatter: (value: string) => {
            if (value.length > 4) {
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }

        },
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#999'
        },
        name: '采样量，单位：份',
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
      {
        type: 'value',
        name: '及时率，单位：%',
        axisLabel: {
          color: '#999',
          formatter: '{value} %',
        },
        splitLine: {
          lineStyle: {
            color: ['#FFFFFF'],
            opacity: 0.25,
          },
        },
      },
    ],
    series: [
      {
        name: '及时送检数量',
        type: 'bar',
        barMaxWidth: 18,
        stack: 'one',
        data: [],
      },
      {
        name: '未及时送检数量',
        stack: 'one',
        type: 'bar',
        barMaxWidth: 18,
        data: [],
      },
      {
        name: '送检及时率',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        tooltip: {
          valueFormatter: value => `${value}%`
        }
      },
    ]
  })

  const getDataSource = (searchValue:any) => {
    submissionStackChart(searchValue).then(res => {
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        const _dataSource: any = res.data || []
        const _newOption: any = cloneDeep(option)
        _newOption.xAxis[0].data = _dataSource.length ? _dataSource.map((item:any) => item.orgName) : []
        _newOption.series[0].data = _dataSource.length ? _dataSource.map((item:any) => item.timelySampleCount) : []
        _newOption.series[1].data = _dataSource.length ? _dataSource.map((item:any) => item.unTimelySampleCount) : []
        _newOption.series[2].data = _dataSource.length ? _dataSource.map((item:any) => Number(item.timelyPercent.replace('%', ''))) : []
        setOption(_newOption)
      } else {
        message.error(res.msg)
      }
    })
  }

  useEffect(() => {
    // getDataSource()
  }, [])
  const [searchParamsList, setSearchParamsList] = useState<any>({});
  // 定义搜索回调函数
  const handleSearch = (values: Record<string, any>) => {
    console.log('搜索参数:', values);
    // 可以在这里对搜索参数进行处理
    setSearchParamsList(values);
  };
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleStartDate = dayjs(_params.sampleDate[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            _params.sampleEndDate = dayjs(_params.sampleDate[1]).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            delete _params.sampleDate;
          }
          setSearchParams(_params);
          getDataSource(_params)
          const { code, data, msg } = await deliveryTimelyRageListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
   
        scroll={{ y: 400 }}
        rowKey="orgIds"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
          searchText: '统计'
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/qc/analyze/submissionExport"
            params={searchParams}
            method="get"
          >
            导出统计结果
          </DownloadButton>,
        ]}
        headerTitle='注：采集的样本需在7天内由检测机构接收。'

  


      />

      <div className='chart-wraper'>
      <ChartsWrapper   option={option} key="monthlyAnalysisChart" width={'85%'}/>
      </div>

      <Drawer
        width="80%"
        title="阳性样本"
        onClose={() => {
          setOpen(false);
          setCurOrgId('');
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <PositiveSample analyzeType={1} orgId={curOrgId}  />
      </Drawer>
    </PageContainer>
  );
};

export default DeliveryTimelyRate;
