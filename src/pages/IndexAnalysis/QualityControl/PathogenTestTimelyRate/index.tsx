/*
 * @Date: 2024-08-07 18:07:25
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-19 16:20:34
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\QualityControl\PathogenTestTimelyRate\index.tsx
 * @Description: 病原检测及时率分析
 */
import { useRef, useState } from 'react';
import { Drawer, message } from 'antd';
import { pathogenTestTimelyRageListApi } from '@/api/QualityControl/api';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import PositiveSample from '../PositiveSample';
import dayjs from 'dayjs';

const PathogenTestTimelyRate: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  const [open, setOpen] = useState(false);
  const [curOrgId, setCurOrgId] = useState('');

  const actionRef = useRef<ActionType>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '检测机构（上报单位）名称',
      dataIndex: 'orgName',
      formItemProps: {
        labelCol: { span: 12 },
      },
    },
    {
      title: '接收样本数量',
      dataIndex: 'receiveSampleCount',
      hideInSearch: true,
    },
    {
      title: '及时检测样本数量',
      dataIndex: 'inspectionTimelyCount',
      hideInSearch: true,
      // render: (_, record) => (
      //   <div
      //     className=" w-full truncate text-[#1677ff] cursor-pointer"
      //     onClick={() => {
      //       setCurOrgId(record.orgId);
      //       setOpen(true);
      //     }}
      //     title={record.inspectionTimelyCount}
      //   >
      //     {record.inspectionTimelyCount}
      //   </div>
      // ),
    },
    {
      title: '检测及时率',
      dataIndex: 'inspectionTimelyPercent',
      hideInSearch: true,
    },
    {
      title: '接收日期',
      dataIndex: 'sampleDate',
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params:any = {
            ...params,
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };
          delete _params.current;
          if (_params.sampleDate && _params.sampleDate.length) {
            _params.sampleStartDate = dayjs(_params.sampleDate[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            _params.sampleEndDate = dayjs(_params.sampleDate[1]).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            delete _params.sampleDate;
          }
          setSearchParams(_params);
          const { code, data, msg } = await pathogenTestTimelyRageListApi(
            _params
          );
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data?.rows ?? [],
            total: data?.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="orgId"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
          searchText: '统计'
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/qc/analyze/etiologyTimelyExport"
            params={searchParams}
            method="get"
          >
            导出统计结果
          </DownloadButton>,
        ]}
        headerTitle='注：接收的样本需在2天内由检测机构提交检测结果。'
      />

      <Drawer
        width="80%"
        title="阳性样本"
        onClose={() => {
          setOpen(false);
          setCurOrgId('');
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <PositiveSample analyzeType={3} orgId={curOrgId} />
      </Drawer>
    </PageContainer>
  );
};

export default PathogenTestTimelyRate;
