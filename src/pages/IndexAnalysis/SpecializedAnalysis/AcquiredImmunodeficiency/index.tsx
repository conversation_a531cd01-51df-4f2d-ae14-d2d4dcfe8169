/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Table,
} from 'antd';
import { downloadFile } from '@/api/file';
import {
  createReport,
  getAcquiredImmunodeficiencyList,
  getAreaDistributionList,
  getPopulationDistributionList,
  getTimeDistributionList,
  getTransmissionRouteList,
  getYear,
  listReport,
} from '@/api/specializedAnalysis';
import { codeDefinition } from '@/constants';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import MonitoringDistribution from './components/MonitoringDistribution';
import BlockContainer from '@/components/BlockContainer';
import FileViewByStream from '@/components/FileViewByStream';
import './index.less';

const { Option } = Select;

type TAcquiredImmunodeficiencyProps = {};

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
};

const timeDistributionColumns = [
  {
    title: '年份',
    dataIndex: 'yearStr',
    key: 'yearStr',
  },
  {
    title: '月份',
    dataIndex: 'monthStr',
    key: 'monthStr',
  },
  {
    title: '例数',
    dataIndex: 'num',
    key: 'num',
  },
  {
    title: '死亡',
    dataIndex: 'deathNum',
    key: 'deathNum',
  },
];

const regionalDistributionColumns = [
  {
    title: '市（州）',
    dataIndex: 'area',
    key: 'area',
  },
  {
    title: '例数',
    dataIndex: 'num',
    key: 'num',
  },
  {
    title: '死亡',
    dataIndex: 'deathNum',
    key: 'deathNum',
  },
];

const crowdDistributionColumns = [
  {
    title: '年份',
    dataIndex: 'yearStr',
    key: 'yearStr',
  },
  {
    title: '月份',
    dataIndex: 'monthStr',
    key: 'monthStr',
  },
  {
    title: '男性',
    children: [
      {
        title: '例数',
        dataIndex: 'manNum',
        key: 'manNum',
      },
      {
        title: '百分比',
        dataIndex: 'manPercent',
        key: 'manPercent',
        render: (text: string) => <>{text}%</>,
      },
    ],
  },
  {
    title: '女性',
    children: [
      {
        title: '例数',
        dataIndex: 'womanNum',
        key: 'womanNum',
      },
      {
        title: '百分比',
        dataIndex: 'womanPercent',
        key: 'womanPercent',
        render: (text: string) => <>{text}%</>,
      },
    ],
  },
  {
    title: '未知',
    children: [
      {
        title: '例数',
        dataIndex: 'unknownNum',
        key: 'unknownNum',
      },
      {
        title: '百分比',
        dataIndex: 'unknownPercent',
        key: 'unknownPercent',
        render: (text: string) => <>{text}%</>,
      },
    ],
  },
];

const transmissionRouteColumns = [
  {
    title: '传播途径',
    dataIndex: 'way',
    key: 'way',
  },
  {
    title: '例数',
    dataIndex: 'num',
    key: 'num',
  },
  {
    title: '百分比',
    dataIndex: 'percent',
    key: 'percent',
    render: (text: string, record: any) => <div>{`${record.percent}%`}</div>,
  },
];

const AcquiredImmunodeficiency: React.FC<
  TAcquiredImmunodeficiencyProps
> = () => {
  const [form] = Form.useForm();

  const [loading, setLoading] = useState<boolean>(false);

  const { RangePicker } = DatePicker;

  const [rangePickerKey, setRangePickerKey] = useState<string>(
    new Date().getTime() + ''
  );

  const actionRef = useRef<ActionType>();

  const [openPreview, setOpenPreview] = useState<boolean>(false);

  const [openDialog, setOpenDialog] = useState<boolean>(false);

  const [previewFileId, setPreviewFileId] = useState<string>('');

  const [radioValue, setRadioValue] = useState<string>('year');

  const [optionsList, setOptionsList] = useState<any>([]);

  const [yearList, setYearList] = useState<any>([]);

  // 获取的数据详情
  const [detailsDataList, setDetailsDataList] = useState<Record<string, any>>();

  // 当前选择的分析起始日期
  let curSelectedStartDateRef = useRef<string>(
    dayjs().startOf('year').format('YYYY-MM-DD')
  );

  // 当前选择的分析结束日期
  let curSelectedEndDateRef = useRef<string>(dayjs().format('YYYY-MM-DD'));

  // 时间分布数据
  const [timeDistributionDataSource, setTimeDistributionDataSource] =
    useState<Record<string, any>[]>();
  // 时间分布表格PageNum
  const [timeDistributionPageNum, setTimeDistributionPageNum] =
    useState<number>(1);

  // 地区分布数据
  const [regionalDistributionDataSource, setRegionalDistributionDataSource] =
    useState<Record<string, any>[]>();
  // 地区分布表格PageNum
  const [regionalDistributionPageNum, setRegionalDistributionPageNum] =
    useState<number>(1);

  // 人群缝补数据
  const [crowdDistributionDataSource, setCrowdDistributionDataSource] =
    useState<Record<string, any>[]>();
  // 人群分布表格PageNum
  const [crowdDistributionPageNum, setCrowdDistributionPageNum] =
    useState<number>(1);

  // 传播途径数据
  const [transmissionRouteDataSource, setTransmissionRouteDataSource] =
    useState<Record<string, any>[]>();
  // 传播途径表格PageNum
  const [transmissionRoutePageNum, setTransmissionRoutePageNum] =
    useState<number>(1);

  // 表格的每页条数，固定为5条
  const [pageSize, setPageSize] = useState<number>(5);

  const columns = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '报告期',
      dataIndex: 'year',
    },
    {
      title: '报告名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      hideInSearch: true,
      dataIndex: 'status', // 假设数据中状态字段为 status
      valueType: 'status',
      render: (_: any, record: any) => {
        console.log(record, 111111);
        if (record.status === '1') {
          return '已生成';
        }
        if (record.status === '0') {
          return '生成中';
        }
      },
      // valueEnum: assessmentTypesOnTable,
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text: any, record: any, _: any, action: any) => {
        let btns: any = [];
        btns = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setPreviewFileId(record.id);
              setOpenPreview(true);
            }}
            // disabled={record.needPeopleJudge === '否'}
          >
            预览报告
          </Button>,
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              handleDownload(record);
            }}
            // disabled={record.needPeopleJudge === '否'}
          >
            下载报告
          </Button>,
        ];
        return btns;
      },
    },
  ];

  /**
   * 获取详情数据
   */
  const getDetailData = async () => {
    setLoading(true);
    try {
      const _params: Record<string, any> = {
        startDate: curSelectedStartDateRef?.current,
        endDate: curSelectedEndDateRef.current,
      };

      const { code, data, msg } = await getAcquiredImmunodeficiencyList(
        _params
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDetailsDataList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   *  获取时间分布数据
   */
  const queryTimeDistributionList = async () => {
    try {
      const { code, data, msg } = await getTimeDistributionList({
        startDate: curSelectedStartDateRef.current,
        endDate: curSelectedEndDateRef.current,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTimeDistributionDataSource(data.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取时间分布数据
   */
  const getYearList = async () => {
    try {
      const { code, data, msg } = await getYear();
      console.log(data, 'year');
      setYearList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取地区分布数据
   */
  const queryRegionalDistributionList = async () => {
    try {
      const { code, data, msg } = await getAreaDistributionList({
        startDate: curSelectedStartDateRef.current,
        endDate: curSelectedEndDateRef.current,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setRegionalDistributionDataSource(data.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  获取人群分布数据
   */
  const queryCrowdDistributionList = async () => {
    try {
      const { code, data, msg } = await getPopulationDistributionList({
        startDate: curSelectedStartDateRef.current,
        endDate: curSelectedEndDateRef.current,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCrowdDistributionDataSource(data.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取传播途径数据
   */
  const queryTransmissionRouteList = async () => {
    try {
      const { code, data, msg } = await getTransmissionRouteList({
        startDate: curSelectedStartDateRef.current,
        endDate: curSelectedEndDateRef.current,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setTransmissionRouteDataSource(data.rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryTimeDistributionList();
    queryRegionalDistributionList();
    queryCrowdDistributionList();
    queryTransmissionRouteList();
  }, [curSelectedStartDateRef?.current, curSelectedEndDateRef?.current]);

  useEffect(() => {
    getDetailData();
    getYearList();
  }, []);
  // 生成 1 月到 12 月的数组对象
  const generateMonthOptions = () => {
    return Array.from({ length: 12 }, (_, index) => {
      const month = index + 1;
      return {
        label: `${month}月`,
        name: `${month}月`,
        value: `${month}`,
      };
    });
  };

  const submitForm = async () => {
    try {
      // 调用 validateFields 方法校验表单
      await form.validateFields();
      // 校验通过，执行后续逻辑
      console.log('表单校验通过，可提交数据');
      const formValue = form.getFieldsValue();
      if (radioValue === 'year') {
        formValue.years = formValue.reportPeriod;
      } else if (radioValue === 'month') {
        formValue.months = formValue.reportPeriod;
      }
      formValue.type = 2;
      const { code, data, msg } = await createReport(formValue);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      } else {
        form.resetFields();
        setRadioValue('year');
        message.success('创建成功');
        // 提交成功后刷新列表
        actionRef.current?.reload();
      }
      setOpenDialog(false); // 关闭弹窗
    } catch (errorInfo) {
      // 校验失败，输出错误信息
      console.log('表单校验失败', errorInfo);
      message.error('请完成所有必填项');
    }
  };

  useEffect(() => {
    if (radioValue === 'year') {
      setOptionsList(yearList);
    } else if (radioValue === 'month') {
      const monthList = generateMonthOptions();
      setOptionsList(monthList);
    }
  }, [radioValue, yearList]);

  const handleDownload = (record: any) => {
    // 获取文件ID
    const fileId = record.id;
    if (!fileId) {
      message.error('文件ID不存在，无法下载');
      return;
    }

    // 获取文件名
    const fileName = record.name || '附件';

    // 调用API下载文件
    downloadFile(fileId, fileName)
      .then(() => {
        message.success(`${fileName} 下载成功`);
      })
      .catch((error) => {
        console.error('下载文件失败:', error);
        message.error('下载文件失败，请稍后重试');
      });
  };

  return (
    <div className="drug-resistance-testing__edit flex flex-col h-full w-full gap-4 p-2 overflow-x-hidden">
      <BlockContainer title="数据筛选">
        <div className="w-full flex flex-row flex-nowrap justify-between items-center py-2">
          <div className="flex flex-row items-center">
            <div className="">统计日期：</div>
            <div className="">
              <RangePicker
                key={rangePickerKey}
                defaultValue={[
                  dayjs(curSelectedStartDateRef.current),
                  dayjs(curSelectedEndDateRef.current),
                ]}
                onChange={(value: any) => {
                  curSelectedStartDateRef.current = dayjs(value[0]).format(
                    'YYYY-MM-DD'
                  );
                  curSelectedEndDateRef.current = dayjs(value[1]).format(
                    'YYYY-MM-DD'
                  );
                }}
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button type="primary" onClick={getDetailData}>
              统计
            </Button>
            <Button
              type="default"
              onClick={() => {
                setRangePickerKey(new Date().getTime() + '');
                curSelectedStartDateRef.current = dayjs()
                  .startOf('year')
                  .format('YYYY-MM-DD');
                curSelectedEndDateRef.current = dayjs().format('YYYY-MM-DD');
                getDetailData();
              }}
            >
              重置
            </Button>
          </div>
        </div>
      </BlockContainer>
      <BlockContainer title="概述信息">
        <div className="">{detailsDataList?.surveyInfo}</div>
        <div className="">{detailsDataList?.diseaseReportInfo}</div>
      </BlockContainer>
      <BlockContainer title="监测分布">
        <MonitoringDistribution
          startDate={curSelectedStartDateRef.current}
          endDate={curSelectedEndDateRef.current}
        />
      </BlockContainer>
      <div className="w-full flex flex-row flex-nowrap gap-4">
        <div className="flex-1">
          <BlockContainer title="时间分布信息">
            <Table
              rowKey="id"
              bordered
              columns={timeDistributionColumns}
              dataSource={timeDistributionDataSource}
              style={{ minHeight: '400px' }}
              pagination={{
                size: 'small',
                pageSize,
                current: timeDistributionPageNum,
              }}
            />
          </BlockContainer>
        </div>
        <div className="flex-1">
          <BlockContainer title="地区分布信息">
            <Table
              bordered
              columns={regionalDistributionColumns}
              dataSource={regionalDistributionDataSource}
              style={{ minHeight: '400px' }}
              pagination={{
                size: 'small',
                pageSize,
                current: regionalDistributionPageNum,
              }}
            />
          </BlockContainer>
        </div>
      </div>
      <div className="w-full flex flex-row flex-nowrap gap-4">
        <div className="flex-1">
          <BlockContainer title="人群分布信息">
            <Table
              bordered
              columns={crowdDistributionColumns}
              dataSource={crowdDistributionDataSource}
              style={{ minHeight: '400px' }}
              pagination={{
                size: 'small',
                pageSize,
                current: crowdDistributionPageNum,
              }}
            />
          </BlockContainer>
        </div>
        <div className="flex-1">
          <BlockContainer title="传播途径分析">
            <Table
              bordered
              columns={transmissionRouteColumns}
              dataSource={transmissionRouteDataSource}
              style={{ minHeight: '400px' }}
              pagination={{
                size: 'small',
                pageSize,
                current: transmissionRoutePageNum,
              }}
            />
          </BlockContainer>
        </div>
      </div>
      <BlockContainer>
        <ProTable
          toolBarRender={() => [
            <Button
              key="buttonAll"
              // loading={batchJudgeLoading}
              type="primary"
              onClick={() => {
                setRadioValue('year');
                setOpenDialog(true);
              }}
            >
              创建分析报告
            </Button>,
          ]}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
              type: 2,
            };
            delete param.current;

            const { code, data, total, msg } = await listReport(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data.rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 120,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle="检验管理"
        />
      </BlockContainer>

      {/* 预览pdf\word */}
      {openPreview && (
        <>
          <Modal
            width="60%"
            title="文件预览"
            onCancel={() => setOpenPreview(false)}
            open={openPreview}
            footer={null}
            destroyOnClose
          >
            <FileViewByStream fileId={previewFileId} isPreview />
          </Modal>
        </>
      )}

      {/* 预览pdf\word */}
      {openDialog && (
        <>
          <Modal
            width="35%"
            title="创建分析报告"
            onCancel={() => setOpenDialog(false)}
            open={openDialog}
            footer={null}
            destroyOnClose
          >
            <Form
              form={form}
              {...layout}
              name="control-hooks"
              style={{ maxWidth: 600 }}
            >
              <Radio.Group
                className="mt-2"
                value={radioValue}
                onChange={(e) => {
                  setRadioValue(e.target.value);
                  form.setFieldsValue({ reportPeriod: [] });
                }}
              >
                <Radio value="year">年份选择（可多选）</Radio>
                <Radio value="month">月份选择（可多选）</Radio>
              </Radio.Group>
              <Form.Item
                name="reportPeriod"
                label="报告期"
                rules={[{ required: true }]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择"
                  allowClear
                  options={optionsList}
                />
              </Form.Item>
              <Form.Item
                name="name"
                label="报告名称"
                rules={[{ required: true }]}
              >
                <Input />
              </Form.Item>
            </Form>
            <div className="d-flex btn-wraper">
              <Button type="primary" className="mr-2" onClick={submitForm}>
                确认
              </Button>
              <Button
                onClick={() => {
                  setOpenDialog(false);
                }}
              >
                取消
              </Button>
            </div>
          </Modal>
        </>
      )}
    </div>
  );
};

export default AcquiredImmunodeficiency;
