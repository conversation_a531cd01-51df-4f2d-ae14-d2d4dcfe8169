/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect } from 'react';
import { message } from 'antd';
import { getAcquiredImmunodeficiencyDistribution } from '@/api/specializedAnalysis';
import { codeDefinition } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';

type TMonitoringDistributionProps = {
  startDate: string;
  endDate: string;
};
const MonitoringDistribution: React.FC<TMonitoringDistributionProps> = ({
  startDate,
  endDate,
}) => {
  const [option, setOption] = React.useState<any>({});

  /**
   * 获取监测分布数据
   */
  const queryDataList = async () => {
    try {
      const { code, data, msg } = await getAcquiredImmunodeficiencyDistribution(
        {
          startDate,
          endDate,
        }
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setOption({
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: [],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          data: data?.rows?.map((item: Record<string, any>) => item.name),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '平均耐药时长',
            type: 'bar',
            barWidth: '40%',
            data: data?.rows?.map((item: Record<string, any>) => item.num),
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryDataList();
  }, [startDate, endDate]);

  return (
    <>
      <ChartsWrapper option={option as any} width="100%" height={300} />
    </>
  );
};

export default MonitoringDistribution;
