/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect } from 'react';
import { message } from 'antd';
import { getSchoolCaseDistributionList } from '@/api/specializedAnalysis';
import { codeDefinition } from '@/constants';
import { start } from 'repl';
import ChartsWrapper from '@/components/ChartsWrapper';

type TMonitoringDistributionProps = {
  startDate: string;
  endDate: string;
  unit: string;
};
const MonitoringDistribution: React.FC<TMonitoringDistributionProps> = ({
  startDate,
  endDate,
  unit,
}) => {
  const [option, setOption] = React.useState<any>({});

  /**
   * 获取监测分布数据
   */
  const queryDataList = async () => {
    try {
      const { code, data, msg } = await getSchoolCaseDistributionList({
        startDate,
        endDate,
        unit,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setOption({
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: [],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          data: data?.map((item: Record<string, any>) => item.name),
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '男性病例',
            type: 'bar',
            barWidth: '40%',
            stack: 'BL',
            data: data?.map((item: Record<string, any>) => item.manNum),
          },
          {
            name: '女性病例',
            type: 'bar',
            barWidth: '40%',
            stack: 'BL',
            data: data?.map((item: Record<string, any>) => item.womanNum),
          },
        ],
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (startDate && endDate && unit) {
      queryDataList();
    }
  }, [startDate, endDate, unit]);

  return (
    <>
      <ChartsWrapper option={option as any} width="100%" height={300} />
    </>
  );
};

export default MonitoringDistribution;
