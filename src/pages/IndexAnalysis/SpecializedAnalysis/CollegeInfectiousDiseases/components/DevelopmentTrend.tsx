/* eslint-disable array-callback-return */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect } from 'react';
import { message } from 'antd';
import { getSchoolDevelopmentTrendList } from '@/api/specializedAnalysis';
import { codeDefinition } from '@/constants';
import ChartsWrapper from '@/components/ChartsWrapper';

type TDevelopmentTrendProps = {
  startDate: string;
  endDate: string;
  unit: string;
};
const DevelopmentTrend: React.FC<TDevelopmentTrendProps> = ({
  startDate,
  endDate,
  unit,
}) => {
  const option1 = {
    legend: {},
    tooltip: {
      trigger: 'axis',
      showContent: false,
    },
    dataset: {
      source: [
        ['product', '2012', '2013', '2014', '2015', '2016', '2017'],
        ['Milk Tea', 56.5, 82.1, 88.7, 70.1, 53.4, 85.1],
        ['<PERSON><PERSON> Latte', 51.1, 51.4, 55.1, 53.3, 73.8, 68.7],
        ['Cheese Cocoa', 40.1, 62.2, 69.5, 36.4, 45.2, 32.5],
        ['Wal<PERSON> Brownie', 25.2, 37.1, 41.2, 18, 33.9, 49.1],
      ],
    },
    xAxis: { type: 'category' },
    yAxis: { gridIndex: 0 },
    grid: { top: '55%' },
    series: [
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
      },
    ],
  };

  const [option, setOption] = React.useState<any>({});

  /**
   * 获取监测分布数据
   */
  const queryDataList = async () => {
    try {
      const { code, data, msg } = await getSchoolDevelopmentTrendList({
        startDate,
        endDate,
        unit,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }

      const months = data.map((item: any) => item.monthCh);

      // 提取所有疾病名称
      const diseaseNames = [
        ...new Set(
          data.flatMap((item: any) =>
            item.unitTrends.map((trend: any) => trend.diseaseName)
          )
        ),
      ];

      // 构造最终结果
      const result = diseaseNames.map((name) => ({
        name,
        data: data.map((item: any) => {
          const trend = item.unitTrends.find(
            (trend: any) => trend.diseaseName === name
          );
          return trend ? trend.num : 0;
        }),
        type: 'line',
        smooth: true,
      }));

      setOption({
        legend: {},
        tooltip: {
          trigger: 'axis',
          showContent: false,
        },
        xAxis: {
          type: 'category',
          data: months,
        },
        yAxis: { gridIndex: 0 },
        grid: {
          top: '15%',
          right: '5%',
          bottom: '10%',
          left: '5%',
        },
        series: result,
      });
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (startDate && endDate && unit) {
      queryDataList();
    }
  }, [startDate, endDate, unit]);

  return (
    <>
      <ChartsWrapper option={option as any} width="100%" height={300} />
    </>
  );
};

export default DevelopmentTrend;
