/*
 * @Date: 2024-11-04 10:29:55
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-02 13:38:56
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\PathogenWarning\AgeWarning\index.tsx
 * @Description: 年龄态势预警
 */
import { useRef, useState } from 'react';
import { message } from 'antd';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import {
  ageWarningListApi,
  TAreaWarningListParams,
} from '@/api/pathogenWarning';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { getDict } from '@/api/dict';

const AgeWarning: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [curSelectId, setCurSelectId] = useState('');
  const actionRef = useRef<ActionType>();
  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      hideInSearch: true,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '年龄',
      dataIndex: 'warnAge',
      valueType: 'select',
      request: async () => {
        const { code, data, msg } = await getDict('age_level');
        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return [];
        }
        return data;
      },
      fieldProps: {
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '病原体',
      dataIndex: 'etiologyId',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
      request: async () => {
        const { code, data, msg } = await getPathogenNameList();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
    },
    {
      title: '预警内容',
      dataIndex: 'warnInfo',
      hideInSearch: true,
    },
    // {
    //   title: '操作',
    //   valueType: 'option',
    //   key: 'option',
    //   width: 80,
    //   render: (text, record, _, action) => [
    //     <Button
    //       type="link"
    //       size="small"
    //       onClick={() => {
    //         setCurSelectId(record.id);
    //         setDetailOpen(true);

    //       }}
    //     >
    //       查看详情
    //     </Button>,
    //   ],
    // },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current!,
            current: undefined
          };
          if (params.warnDate && params.warnDate.length) {
            _params.startDate = params.warnDate[0];
            _params.endDate = params.warnDate[1];
          }
          setSearchParams(_params);
          const { code, data, msg } = await ageWarningListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [{}],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/etiology/positive/age/export"
            params={searchParams}
          >
            导出统计结果
          </DownloadButton>,
        ]}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curSelectId} />
    </PageContainer>
  );
};

export default AgeWarning;
