import { createContext, useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { fileGroupApi } from '@/api/common';
import { getDict } from '@/api/dict';
import { ossObjectApi } from '@/api/oss';
import { codeDefinition } from '@/constants';
import { ProDescriptions } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { positiveWarningDetailApi } from '@/api/pathogenWarning';

type TEditProps = {
  detailId: string;
};

// 创建Context
export const TaskContext = createContext<any>({});

const Detail: React.FC<TEditProps> = ({ detailId }) => {
  // 样本检测详情信息
  const [sampleDetectionDetails, setSampleDetectionDetails] =
    useState<Record<string, any>>();
  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);

  // 样本名称枚举
  const [sampleNameList, setSampleNameList] = useState<Record<string, any>[]>(
    []
  );
  // 样本归类枚举
  const [sampleTypeList, setSampleTypeList] = useState<Record<string, any>[]>(
    []
  );
  // 样本来源枚举
  const [sampleSourceList, setSampleSourceList] = useState<
    Record<string, any>[]
  >([]);
  // 分离状态枚举
  const [separateStatusList, setSeparateStatusList] = useState<
    Record<string, any>[]
  >([]);
  // 年龄段枚举
  const [ageRangeList, setAgeRangeList] = useState<Record<string, any>[]>([]);
  // 年龄单位枚举
  const [ageUnitList, setAgeUnitList] = useState<Record<string, any>[]>([]);
  // 职业列表枚举
  const [jobList, setJobList] = useState<Record<string, any>[]>([]);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      const { code, data, msg } = await positiveWarningDetailApi(detailId);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetectionDetails(data);

      // 处理复核图片
      if (data?.sampleDetectionImgUrlList?.length) {
        const _res = await fileGroupApi({
          businessId: data?.sampleDetectionImgUrlList[0],
        });
        if (_res?.code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        _res?.data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.fileAddr,
          });
        });
        formRef?.current?.setFieldValue('images', _result);
      }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        formRef2?.current?.setFieldValue('file', [
          {
            url: result?.data[0]?.fileAddr,
            name: result?.data[0]?.originalName,
          },
        ]);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本名称枚举
   */
  const querySampleNameEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_name');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleNameList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本归类枚举
   */
  const querySampleTypeEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_type');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleTypeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本来源枚举
   */
  const querySampleSourceEnums = async () => {
    try {
      const { code, data, msg } = await getDict('sample_source');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleSourceList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取样本分离状态枚举
   */
  const querySampleSeparateStatusEnums = async () => {
    try {
      const { code, data, msg } = await getDict('separate_status');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeparateStatusList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄段区间枚举
   */
  const queryAgeRangeListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_level');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取年龄单位枚举
   */
  const queryAgeUnitListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('age_unit');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeUnitList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取职业枚举
   */
  const queryJobListEnums = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setJobList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    if (detailId) {
      getDetailData();
      querySampleNameEnums();
      querySampleTypeEnums();
      querySampleSourceEnums();
      querySampleSeparateStatusEnums();
      queryAgeRangeListEnums();
      queryAgeUnitListEnums();
      queryJobListEnums();
    }
  }, [detailId]);

  return (
    <div className="flex flex-col h-full w-full gap-4 p-2">
      <BlockContainer title="监测样本采样信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="样本编号">
            {sampleDetectionDetails?.sampleNo}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本名称">
            {
              sampleNameList?.find(
                (_i) => _i?.dictValue === sampleDetectionDetails?.sampleName
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="采样日期">
            {sampleDetectionDetails?.sampleGetDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本归类">
            {
              sampleTypeList?.find(
                (_i) => _i?.dictValue === sampleDetectionDetails?.sampleType
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本来源">
            {
              sampleSourceList?.find(
                (_i) => _i?.dictValue === sampleDetectionDetails?.sampleSource
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="来源详情">
            {sampleDetectionDetails?.sourceDetail}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="采样地区">
            {sampleDetectionDetails?.cityName}/
            {sampleDetectionDetails?.areaName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="哨点医院">
            {sampleDetectionDetails?.sentinelHospital}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="分离状态">
            {
              separateStatusList?.find(
                (_i) => _i?.dictValue === sampleDetectionDetails?.separateState
              )?.dictLabel
            }
          </ProDescriptions.Item>
          <ProDescriptions.Item label="送检日期">
            {sampleDetectionDetails?.sendDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="收样日期">
            {sampleDetectionDetails?.receiveDate}
          </ProDescriptions.Item>
          {sampleDetectionDetails?.sampleType === '1' ? (
            <>
              <ProDescriptions.Item label="姓名">
                {sampleDetectionDetails?.name}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="家长姓名">
                {sampleDetectionDetails?.parentName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="身份证号">
                {sampleDetectionDetails?.idNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="联系电话">
                {sampleDetectionDetails?.phone}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="出生日期">
                {sampleDetectionDetails?.birthday}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="年龄">
                {sampleDetectionDetails?.age}
                {
                  ageUnitList?.find(
                    (_i) => _i?.dictValue === sampleDetectionDetails?.ageUnit
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="年龄段">
                {
                  ageRangeList?.find(
                    (_i) => _i?.dictValue === sampleDetectionDetails?.ageLevel
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="性别">
                {sampleDetectionDetails?.sex === '1' ? '男' : '女'}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="名族">
                {sampleDetectionDetails?.nation}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="职业">
                {
                  jobList?.find(
                    (_i) => _i?.dictValue === sampleDetectionDetails?.job
                  )?.dictLabel
                }
              </ProDescriptions.Item>
              <ProDescriptions.Item label="病历号">
                {sampleDetectionDetails?.patientNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="发病日期">
                {sampleDetectionDetails?.attackDate}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="常住地">
                {sampleDetectionDetails?.parentName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="详细地址">
                {sampleDetectionDetails?.liveAddress}
              </ProDescriptions.Item>
            </>
          ) : null}
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="样本检测信息">
        {sampleDetectionDetails?.etiologyList?.map(
          (_item: any, _index: number) => (
            <ProDescriptions key={_index} column={4}>
              <ProDescriptions.Item label="监测病原">
                {_item?.etiologyName}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="检测结果">
                {_item?.resultName}
              </ProDescriptions.Item>
              {_item?.resultName === '阳性' ? (
                <ProDescriptions.Item label="菌(毒)株编号">
                  {_item?.bacterialStrainNo}
                </ProDescriptions.Item>
              ) : null}
              <ProDescriptions.Item label="检测日期">
                {_item?.detectionDate}
              </ProDescriptions.Item>
              <ProDescriptions.Item label="检测人员">
                {_item?.detectionPeople}
              </ProDescriptions.Item>
            </ProDescriptions>
          )
        )}
      </BlockContainer>
    </div>
  );
};

export default Detail;
