/*
 * @Date: 2024-11-22 16:01:40
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 11:24:27
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\PathogenWarning\PositiveWarning\index.tsx
 * @Description: 病原阳性预警
 */
import { useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import {
  positiveWarningListApi,
} from '@/api/pathogenWarning';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';

const PositiveWarning: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [curSelectId, setCurSelectId] = useState('');
  const actionRef = useRef<ActionType>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      hideInSearch: true,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      hideInSearch: true,
    },
    {
      title: '样本名称',
      dataIndex: 'sampleCode',
      hideInSearch: true,
    },
    {
      title: '病原体',
      dataIndex: 'etiologyName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: {
          label: 'name',
          value: 'name',
        },
      },
      request: async () => {
        const { code, data, msg } = await getPathogenNameList();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
    },
    {
      title: '检测日期',
      dataIndex: 'detectionDate',
      hideInSearch: true,
    },
    {
      title: '菌(毒)株编号',
      dataIndex: 'strainCode',
      hideInSearch: false,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurSelectId(record.id);
            setDetailOpen(true);
          }}
        >
          查看详情
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params :any = {
            ...params,
            pageNum: params.current!,
          };
          delete _params.current
          if (_params.warnDate && _params.warnDate.length) {
            _params.startDate = _params.warnDate[0];
            _params.endDate = _params.warnDate[1];
          }
          delete _params.warnDate
          setSearchParams(_params);
          const { code, data, msg } = await positiveWarningListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton url="/data/etiology/positive/export" params={searchParams}>
            导出查询列表
          </DownloadButton>,
        ]}
      />
      {/* 详情 */}
      <Drawer
        width="70%"
        title="详情"
        onClose={() => setDetailOpen(false)}
        open={detailOpen}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail detailId={curSelectId} />
      </Drawer>
    </PageContainer>
  );
};

export default PositiveWarning;
