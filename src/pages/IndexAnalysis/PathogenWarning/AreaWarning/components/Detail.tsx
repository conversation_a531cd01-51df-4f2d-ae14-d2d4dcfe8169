import { useEffect, useState } from 'react';
import { Descriptions, Drawer } from 'antd';
import { message } from 'antd';
import { areaWarningDetailApi } from '@/api/pathogenWarning';
import { codeDefinition } from '@/constants';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};
type TDataSource = {
  week: string;
  num: number;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [dataSource, setDataSource] = useState<Record<string, any>>();
  const [option, setOption] = useState<ECOption>({
    color: '#1677ff',
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
    },
    series: [
      {
        data: [],
        type: 'line',
        areaStyle: { opacity: 0.5 },
      },
    ],
  });
  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await areaWarningDetailApi({
        etiologyId: id,
        type: '1',
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const _newOption: any = cloneDeep(option);
        _newOption.xAxis.data = data.weekList.length
          ? data.weekList.map((_item: TDataSource) => _item.week)
          : [];
        _newOption.series[0].data = data.weekList.length
          ? data.weekList.map((_item: TDataSource) => _item.num)
          : [];
        setOption(_newOption);
        setDataSource(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (open && id) {
      getDetail(id);
    }
  }, [open, id]);

  return (
    <Drawer
      width="70%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <BlockContainer title="区域态势">
          <div className=" w-full h-[400px]">
            <ChartsWrapper option={option} key="MonthRiskCluesChart" />
          </div>
        </BlockContainer>
        <BlockContainer title="预警信息">
          <Descriptions column={24}>
            <Descriptions.Item label="预警信息" span={24}>
              {dataSource?.warnInfo}
            </Descriptions.Item>
            <Descriptions.Item label="预警信息说明" span={24}>
              {dataSource?.warnDescribe}
            </Descriptions.Item>
          </Descriptions>
        </BlockContainer>
      </div>
    </Drawer>
  );
};

export default Detail;
