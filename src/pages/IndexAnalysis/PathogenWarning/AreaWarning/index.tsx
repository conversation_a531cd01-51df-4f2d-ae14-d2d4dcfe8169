/*
 * @Date: 2024-11-04 10:29:55
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 18:07:10
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\PathogenWarning\AreaWarning\index.tsx
 * @Description: 区域态势预警
 */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getAllCityAreaList } from '@/api/pathogen';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import {
  areaWarningListApi,
  TAreaWarningListParams,
} from '@/api/pathogenWarning';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';

const AreaWarning: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [detailOpen, setDetailOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [curSelectId, setCurSelectId] = useState('');
  const actionRef = useRef<ActionType>();

  // 市县区域数据
  const [cityAreaList, setCityAreaList] = useState<Record<string, any>[]>();
  /**
   * 获取贵州市所有市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryCityAreaList();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      hideInSearch: true,
    },
    {
      title: '预警日期',
      dataIndex: 'warnDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '区域',
      dataIndex: 'warnArea',
      valueType: 'cascader',
      fieldProps: {
        options: cityAreaList,
      },
    },
    {
      title: '病原体',
      dataIndex: 'etiologyId',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
      request: async () => {
        const { code, data, msg } = await getPathogenNameList();
        if (code === codeDefinition.QUERY_SUCCESS) {
          return data;
        } else {
          message.error(msg);
          return [];
        }
      },
    },
    {
      title: '预警内容',
      dataIndex: 'warnInfo',
      hideInSearch: true,
    },

    // {
    //   title: '操作',
    //   valueType: 'option',
    //   key: 'option',
    //   width: 80,
    //   render: (text, record, _, action) => [
    //     <Button
    //       type="link"
    //       size="small"
    //       onClick={() => {
    //         setCurSelectId(record.id);
    //         setDetailOpen(true);

    //       }}
    //     >
    //       查看详情
    //     </Button>,
    //   ],
    // },
  ];
  return (
    <PageContainer>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const _params: any = {
            ...params,
            pageNum: params.current!,
            current: undefined,
          };
          if (params.warnDate && params.warnDate.length) {
            _params.startDate = params.warnDate[0];
            _params.endDate = params.warnDate[1];
          }
          if(_params.warnArea && _params.warnArea.length) {
            _params.warnArea = _params.warnArea.toString()
          }
          
          setSearchParams(_params);
          const { code, data, msg } = await areaWarningListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: data.rows ?? [{}],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          // 为了查看详情页面，用完删除
          // <Button type="primary"  onClick={() => {
          //   setDetailOpen(true);

          // }}>查看详情</Button>,
          <DownloadButton
            url="/data/etiology/positive/area/export"
            params={searchParams}
          >
            导出统计结果
          </DownloadButton>,
        ]}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curSelectId} />
    </PageContainer>
  );
};

export default AreaWarning;
