/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { eventDetailApi } from '@/api/event';
import {
  getMonitorItemDetail,
  getMonitorItemSelectedList,
} from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ProTable } from '@ant-design/pro-components';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await getMonitorItemDetail(id);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDetailInfo(data);
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  return (
    <Drawer
      width="60%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" flex flex-col w-full h-full p-4">
        <Descriptions>
          <Descriptions.Item label="监测项目">
            {detailInfo?.name}
          </Descriptions.Item>
        </Descriptions>
        <ProTable
          size="small"
          columns={[
            {
              dataIndex: 'index',
              valueType: 'indexBorder',
              width: 48,
              title: '序号',
            },
            {
              title: '病原体名称',
              dataIndex: 'etiologyName',
            },
            {
              title: '病原体分类',
              dataIndex: 'etiologyClassName',
            },
          ]}
          request={async (params, sort, filter) => {
            const _params: any = {
              pageNum: params.current!,
              pageSize: params.pageSize!,
              projectId: id,
            };
            const { code, data, msg } = await getMonitorItemSelectedList(
              _params
            );
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
              return {
                data: [],
                success: false,
                total: 0,
              };
            }

            return {
              data: data.rows ?? [],
              total: data.total ?? 0,
              success: true,
            };
          }}
          search={false}
          rowKey="id"
          options={false}
          pagination={false}
          headerTitle="已选择病原列表"
        />
      </div>
    </Drawer>
  );
};

export default Detail;
