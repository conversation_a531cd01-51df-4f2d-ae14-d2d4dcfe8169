/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useState } from 'react';
import { Button, Drawer, Input, message, Popconfirm } from 'antd';
import {
  editMonitorItem,
  postAddNewMonitorItem,
} from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons';
import PathogenListTable from './PathogenListTable';
import SelectedPathogenListTable from './SelectedPathogenListTable';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: 'add' | 'edit';
  id: string;
  curEventName: string;
  setCurEventName: any;
  handleDelete: any;
};

export const EditContext = createContext({});

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  id,
  curEventName,
  setCurEventName,
  handleDelete,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  // 监测项目名称
  const [monitorProjectName, setMonitorProjectName] = useState<string>();
  // 当前需要提交的病原数据列表
  const [selectedPathogenItemsList, setSelectedPathogenItemsList] = useState<
    Record<string, any>[]
  >([]);

  // 通过表格选择器选择的一个或多个选项的数据集合
  const [selectedOptions, setSelectedOptions] = useState<Record<string, any>[]>(
    []
  );

  // 需要从已选列表中被移除的选项列表集合
  const [removeOptions, setRemoveOptions] = useState<Record<string, any>[]>([]);

  /**
   *  选入操作处理函数
   */
  const handleSelectedEvent = (item: Record<string, any>) => {
    // 检查当前已选择列表中是否已存在该项
    if (selectedPathogenItemsList.some((i) => i.id === item.id)) {
      message.warning('已选择过该条数据');
      return;
    }

    if (selectedPathogenItemsList.some((i) => i.etiologyId === item.id)) {
      message.warning('已选择过该条数据');
      return;
    }

    setSelectedPathogenItemsList([item, ...selectedPathogenItemsList]);
  };

  /**
   *  保存数据
   */
  const handleSave = async () => {
    // 校验监测项目名称是否填写
    if (!monitorProjectName) {
      message.error('请填写监测项目名称');
      return;
    }
    // 选择的病原列表不能为空
    if (!selectedPathogenItemsList?.length) {
      message.error('没有选择任何病原');
      return;
    }

    try {
      setLoading(true);
      const { code, data, msg } =
        type === 'add'
          ? await postAddNewMonitorItem({
              name: monitorProjectName,
              details: selectedPathogenItemsList,
            })
          : await editMonitorItem({
              id,
              name: monitorProjectName,
              details: selectedPathogenItemsList,
            });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(type === 'add' ? '新增成功' : '修改成功');
      setCurEventName(undefined);
      setMonitorProjectName(undefined);
      setSelectedPathogenItemsList([]);
      setOpen(false);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   *  将多个已选选项移入已选择列表
   */
  const handleSelectItemsWithMultiple = () => {
    if (!selectedOptions.length) {
      message.warning('没有选择任何病原');
      return;
    }
    // 去重
    const _filteredItems: Record<string, any>[] = [];
    selectedOptions?.forEach((_item) => {
      const item = selectedPathogenItemsList.find(
        (item) => item?.id === _item?.id
      );
      if (!item) {
        _filteredItems.push(_item);
      }
    });

    setSelectedPathogenItemsList([
      ...selectedPathogenItemsList,
      ..._filteredItems,
    ]);
  };

  /**
   *  移除多个选项
   */
  const handleRemoveItemsWithMultiple = () => {
    setSelectedPathogenItemsList([...removeOptions]);
  };

  useEffect(() => {
    if (curEventName) setMonitorProjectName(curEventName);
  }, [curEventName]);

  const initValue = {
    selectedOptions,
    setSelectedOptions,
    removeOptions,
    setRemoveOptions,
    id,
    curEventName,
  };

  return (
    <EditContext.Provider value={initValue}>
      <Drawer
        width="80%"
        title={type === 'add' ? '新增监测项目信息' : '编辑监测项目信息'}
        onClose={() => {
          setCurEventName(undefined);
          setMonitorProjectName(undefined);
          setSelectedPathogenItemsList([]);
          setOpen(false);
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <div className=" w-full h-full flex flex-col">
          <div className=" flex-1 p-4 overflow-y-auto bg-white">
            {/* 监测项目搜索 */}
            <div className="w-full flex flex-row flex-nowrap items-center gap-2 py-4 px-8">
              <div className="flex flex-row flex-nowrap items-center">
                <span className="text-red-500">*</span>
                <span>监测项目:</span>
              </div>
              <div className="w-[200px]">
                <Input
                  placeholder="请输入监测项目名称"
                  value={curEventName}
                  onChange={(e: any) => {
                    setCurEventName(e.target.value);
                    setMonitorProjectName(e.target.value);
                  }}
                  disabled={type !== 'add'}
                />
              </div>
            </div>
            <div className="w-full h-full flex flex-row flex-nowrap">
              {/* 待选择病原列表 */}
              <div className="flex-1 border border-solid border-gray-200 ml-6 p-2 rounded-lg">
                <PathogenListTable
                  type={type}
                  handleSelectedEvent={handleSelectedEvent}
                  selectedPathogenItemsList={selectedPathogenItemsList}
                />
              </div>
              {/* 中间操作符 */}
              <div className="w-[48px] flex flex-col justify-center items-center gap-4">
                <div
                  className="cursor-pointer hover:scale-105 hover:text-red-500"
                  onClick={() => handleRemoveItemsWithMultiple()}
                >
                  <LeftCircleOutlined style={{ fontSize: '30px' }} />
                </div>
                <div
                  className="cursor-pointer hover:scale-105 hover:text-blue-500"
                  onClick={() => handleSelectItemsWithMultiple()}
                >
                  <RightCircleOutlined style={{ fontSize: '30px' }} />
                </div>
              </div>
              {/* 已选择病原列表 */}
              <div className="flex-1 border border-solid border-gray-200 mr-6 p-2 rounded-lg">
                <SelectedPathogenListTable
                  type={type}
                  selectedPathogenItemsList={selectedPathogenItemsList}
                  setSelectedPathogenItemsList={setSelectedPathogenItemsList}
                />
              </div>
            </div>
          </div>

          <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
            <div className="">
              <Button onClick={() => setOpen(false)}>取消</Button>
            </div>
            {type === 'edit' ? (
              <div className="">
                <Popconfirm
                  title="删除"
                  description="确认删除该监测项目?"
                  onConfirm={() => {
                    handleDelete(id).then((res: any) => setOpen(false));
                  }}
                >
                  <Button danger>删除</Button>
                </Popconfirm>
              </div>
            ) : null}

            <div className="">
              <Button type="primary" onClick={handleSave} loading={loading}>
                保存
              </Button>
            </div>
          </div>
        </div>
      </Drawer>
    </EditContext.Provider>
  );
};

export default Edit;
