/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  病原列表表格
 */
import React, { Key, useContext, useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { getMonitorItemSelectedList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { EditContext } from './Edit';

type TPathogenListTableProps = {
  type: string; // add - 新增
  selectedPathogenItemsList: Record<string, any>[];
  setSelectedPathogenItemsList: any;
};

const PathogenListTable: React.FC<TPathogenListTableProps> = ({
  type,
  selectedPathogenItemsList,
  setSelectedPathogenItemsList,
}) => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState(10);

  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);

  const { removeOptions, setRemoveOptions, id } =
    useContext<Record<string, any>>(EditContext);

  const [currentPage, setCurrentPage] = useState<number>(1);

  // 查询条件
  const [searchParams, setSearchParams] = useState({
    pageNum: currentPage,
    pageSize: 10,
  });

  // 在编辑时，单个移除病原时需要存储的被移除的选项的ID
  let removeItemIdRef = useRef<string | null>(null);

  // originDataSource,用于存放原始列表数据
  // 当本地搜索事件发生时，用于还原之前的数据
  const [originDataSource, setOriginDataSource] = useState<
    Record<string, any>[]
  >([]);

  const {
    infectiousDiseasesList,
    getInfectiousDiseasesList,
    pathogenClassificationList,
    getPathogenClassificationList,
  } = useDictStore();

  const columns = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '病原体名称',
      dataIndex: 'etiologyName',
    },
    {
      title: '病原体分类',
      dataIndex: 'etiologyClass',
      valueType: 'select',
      fieldProps: {
        options: pathogenClassificationList,
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 48,
      render: (_: any, record: Record<string, any>) => [
        <Popconfirm
          title="删除"
          description="确认移除该项?"
          onConfirm={() => {
            if (type === 'edit') {
              removeItemIdRef.current = record.id;
            }
            handleRemoveItem(record);
          }}
          key="del"
        >
          <Button type="link" size="small" danger>
            移除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  const tableReload = () => actionRef.current?.reload();

  /**
   *  移除已选项
   */
  const handleRemoveItem = (record: Record<string, any>) => {
    const newList = selectedPathogenItemsList?.filter(
      (item: Record<string, any>) => item.id !== record.id
    );
    setSelectedPathogenItemsList([...newList]);
    setDataSource([...newList]);
    setOriginDataSource([...newList]);
  };

  /**
   * 通过选择器选择选项的处理函数
   * @param selectedRowKeys
   * @param selectedRows
   */
  const onChange = (
    selectedRowKeys: Key[],
    selectedRows: Record<string, any>[]
  ) => {
    // 移除已选项
    const newList = selectedPathogenItemsList?.filter(
      (item: Record<string, any>) => !selectedRowKeys.includes(item.id)
    );
    setRemoveOptions([...newList]);
  };

  /**
   *  获取已选择病原列表
   */
  const queryTableDataList = async () => {
    if (!id || type === 'add') return;
    try {
      const { code, data, msg } = await getMonitorItemSelectedList({
        pageNum: currentPage,
        pageSize: 999999,
        projectId: id,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDataSource(data?.rows);
      setOriginDataSource(data?.rows);
      setSelectedPathogenItemsList([...data?.rows]);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    setDataSource([...selectedPathogenItemsList]);
    setOriginDataSource([...selectedPathogenItemsList]);
  }, [selectedPathogenItemsList]);

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
    !pathogenClassificationList?.length && getPathogenClassificationList();
    queryTableDataList();
  }, []);

  return (
    <div className="w-full h-full pathogen-list__table">
      <div className="w-full bg-white mb-2 rounded py-2 px-4">
        已选择病原列表
      </div>
      <ProTable
        size="small"
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        form={{ span: 12 }}
        params={searchParams}
        dataSource={dataSource}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={false}
        pagination={false}
        rowSelection={{
          onChange,
        }}
        dateFormatter="string"
        toolBarRender={() => []}
        onSubmit={(params: any) => {
          /**
           *   etiologyClass: "1"  传染病类别
           *   etiologyName: "11"  病原体名称
           *   infectClass: 1      病原体分类
           */
          // 触发搜索，本地手动处理
          // 1. 如果params对象为空，则返回
          if (!Object.keys(params).length) return;

          const _dataSource: Record<string, any>[] = [];
          if (
            params?.infectClass &&
            !params?.etiologyName &&
            !params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.infectClass ===
                infectiousDiseasesList?.find(
                  (_item) => _item?.value === params?.infectClass
                )?.label
              ) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            !params?.infectClass &&
            params?.etiologyName &&
            !params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (_item?.etiologyName?.includes(params?.etiologyName)) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            !params?.infectClass &&
            !params?.etiologyName &&
            params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.etiologyClass ===
                pathogenClassificationList?.find(
                  (_pathogen) => _pathogen?.value === params?.etiologyClass
                )?.label
              ) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            params?.infectClass &&
            params?.etiologyName &&
            !params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.infectClass === params?.infectClass &&
                _item?.etiologyName?.includes(params?.etiologyName)
              ) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            !params?.infectClass &&
            params?.etiologyName &&
            params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.etiologyName?.includes(params?.etiologyName) &&
                _item?.etiologyClass === params?.etiologyClass
              ) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            params?.infectClass &&
            !params?.etiologyName &&
            params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.infectClass === params?.infectClass &&
                _item?.etiologyClass === params?.etiologyClass
              ) {
                _dataSource.push(_item);
              }
            });
          }

          if (
            params?.infectClass &&
            params?.etiologyName &&
            params?.etiologyClass
          ) {
            dataSource?.forEach((_item) => {
              if (
                _item?.infectClass === params?.infectClass &&
                _item?.etiologyName?.includes(params?.etiologyName) &&
                _item?.etiologyClass === params?.etiologyClass
              ) {
                _dataSource.push(_item);
              }
            });
          }

          setDataSource(_dataSource);
        }}
        onReset={() => {
          // 当搜索条件重置时，触发数据还原
          setDataSource(originDataSource);
        }}
      />
    </div>
  );
};

export default PathogenListTable;
