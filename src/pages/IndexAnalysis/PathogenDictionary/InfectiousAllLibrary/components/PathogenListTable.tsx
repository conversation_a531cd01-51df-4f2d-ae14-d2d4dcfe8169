/* eslint-disable no-empty-pattern */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  病原列表表格
 */
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { etiologyListApi } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { EditContext } from './Edit';

type TPathogenListTableProps = {
  type: string; // add - 新增
  handleSelectedEvent: (item: Record<string, any>) => void;
  selectedPathogenItemsList?: Record<string, any>[];
};

const PathogenListTable: React.FC<TPathogenListTableProps> = ({
  type,
  handleSelectedEvent,
  selectedPathogenItemsList,
}) => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState(10);

  const { selectedOptions, setSelectedOptions } =
    useContext<Record<string, any>>(EditContext);

  // 查询条件
  const [searchParams, setSearchParams] = useState();

  const {
    infectiousDiseasesList,
    getInfectiousDiseasesList,
    pathogenClassificationList,
    getPathogenClassificationList,
  } = useDictStore();

  const columns = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '病原体名称',
      dataIndex: 'etiologyName',
    },
    {
      title: '病原体分类',
      dataIndex: 'etiologyClass',
      valueType: 'select',
      fieldProps: {
        options: pathogenClassificationList,
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 48,
      render: (_: any, record: Record<string, any>) => [
        <Button
          type="link"
          size="small"
          onClick={() => handleSelectedEvent(record)}
        >
          选入
        </Button>,
      ],
    },
  ];

  const tableReload = () => actionRef.current?.reload();

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
    !pathogenClassificationList?.length && getPathogenClassificationList();
  }, []);

  return (
    <div className="w-full h-full pathogen-list__table">
      <div className="w-full bg-white mb-2 rounded py-2 px-4">
        待选择病原列表
      </div>
      <ProTable
        size="small"
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        form={{ span: 12 }}
        request={async (params, sort, filter) => {
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            infectClass: params?.infectClass,
            infectName: params?.infectName,
            etiologyName: params.etiologyName,
            etiologyClass: params.etiologyClass,
            spreadApproach: params.spreadApproach,
          };

          setSearchParams(_params);

          const { code, data, msg } = await etiologyListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            return {
              data: [],
              total: 0,
              success: true,
            };
          }

          return {
            data: data?.rows || [],
            total: data?.total || 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={false}
        pagination={{
          size: 'small',
          showSizeChanger: false,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedOptions([...selectedRows]);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => []}
      />
    </div>
  );
};

export default PathogenListTable;
