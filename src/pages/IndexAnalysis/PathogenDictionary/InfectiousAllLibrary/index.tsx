/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import {
  deleteMonitorItem,
  getMonitorItemList,
  monitorListApi,
  TEtiologyListParams,
} from '@/api/pathogenDictionary';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { TTableData } from '../type';
import './index.less';

const InfectiousAllLibrary: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('1');

  // 查询条件
  const [searchParams, setSearchParams] = useState<TEtiologyListParams>();

  // 统计数量
  const [counts, setCounts] = useState({
    draft: 0,
    reporting: 0,
  });
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  const [curEventName, setCurEventName] = useState('');

  // 新增/编辑
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit'>('add');

  // 监测项目库搜索列表数据
  const [projectsListEnums, setProjectsListEnums] = useState<any[]>([]);

  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '监测项目',
      dataIndex: 'name',
      valueType: 'select',
      fieldProps: {
        options: projectsListEnums,
        showSearch: true,
        fieldNames: { label: 'projectName', value: 'projectId' },
      },
    },
    {
      title: '病原体名称',
      dataIndex: 'etiologyName',
      hideInSearch: true,
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurEventId(record?.id);
            setCurEventName(record?.name);
            setEditType('edit');
            setEditOpen(true);
          }}
          key="edit"
        >
          编辑
        </Button>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurEventId(record.id);
            setDetailOpen(true);
          }}
          key="detail"
        >
          详情
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该项?"
          onConfirm={() => handleDelete(record.id)}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  const handleDelete = async (id: string) => {
    try {
      const { code, msg } = await deleteMonitorItem(id);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  /**
   *  获取监测项目搜索查询列表
   */
  const queryProjectListEnums = async () => {
    try {
      const { code, data, msg } = await monitorListApi();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setProjectsListEnums(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryProjectListEnums();
  }, []);

  return (
    <PageContainer>
      <ProTable<TTableData>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        request={async (params, sort, filter) => {
          let err = false;
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
          };

          if (params?.name) {
            _params['projectId'] = params?.name;
          }

          setSearchParams(_params);
          const { code, data, msg } = await getMonitorItemList(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          setEditOpen(val);
          tableReload();
        }}
        type={editType}
        id={curEventId}
        curEventName={curEventName}
        setCurEventName={setCurEventName}
        handleDelete={handleDelete}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
    </PageContainer>
  );
};

export default InfectiousAllLibrary;
