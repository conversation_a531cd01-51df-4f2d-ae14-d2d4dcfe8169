/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  etiologyAddApi,
  etiologyDetailApi,
  etiologyUpdateApi,
  getInfectListByClassId,
  getPathogenNameList,
} from '@/api/pathogenDictionary';
import { queryDictValueList } from '@/api/settings';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons';
import {
  EditableFormInstance,
  EditableProTable,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import MedicineListTable from './MedicineListTable';
import SelectedMedicineListTable from './SelectedMedicineListTable';
import { log } from 'node:console';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: 'add' | 'edit';
  id: string;
  infectiousDiseasesList?: any[];
  pathogenClassificationList?: any[];
  activeKey: number | string;
};
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};

export const EditContext = createContext({});

const Edit: React.FC<TEditProps> = ({
  open,
  setOpen,
  type,
  id,
  infectiousDiseasesList,
  pathogenClassificationList,
  // 贵州库 或 全库
  activeKey,
}) => {
  const { token } = useTokenStore();
  const formRef = useRef<ProFormInstance>(null);

  const [btnLoading, setBtnLoading] = useState(false);
  const [etiologyClass, setEtiologyClass] = useState<
    {
      dictValue?: string;
      dictLabel?: string;
    }[]
  >([]);

  // 病原体名称列表数据
  const [pathogenNameList, setPathogenNameList] = useState<
    Record<string, any>[]
  >([]);

  // 当前选择的传染病分类
  const [curSelectedInfectClass, setCurSelectedInfectClass] = useState<
    number | string
  >();

  // 当前选择的传染病分类下的所有传染病数据列表
  const [infectList, setInfectList] = useState<Record<string, any>[]>([]);

  // 当前需要提交的病原数据列表
  const [selectedMedicineItemsList, setSelectedMedicineItemsList] = useState<
    Record<string, any>[]
  >([]);

  // 通过表格选择器选择的一个或多个选项的数据集合
  const [selectedOptions, setSelectedOptions] = useState<Record<string, any>[]>(
    []
  );

  // 需要从已选列表中被移除的选项列表集合
  const [removeOptions, setRemoveOptions] = useState<Record<string, any>[]>([]);

  // 样本检测信息 正在编辑的行
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const editorFormRef = useRef<EditableFormInstance<any>>();

  const getClassList = async () => {
    try {
      const { code, rows, msg } = await queryDictValueList({
        dictType: 'etiology_type',
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const classList = rows.map((item: any) => ({
          dictValue: item.dictValue,
          dictLabel: item.dictLabel,
        }));
        setEtiologyClass(classList);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await etiologyDetailApi({ id });
      if (code === codeDefinition.QUERY_SUCCESS) {
        data.etiologyClass += '';

        data['pathogenCodeName'] = pathogenNameList?.find(
          (_item) => ~~_item?.value === data?.pathogenId
        )?.label;

        formRef.current?.setFieldsValue(data);

        // 设置已选择药品列表
        if (data?.drugs?.length) {
          setSelectedMedicineItemsList([...data?.drugs]);
        }

        // 这里需要根据返回的传染病类别ID请求当前类别下的所有传染病名称列表
        queryInfectListByClassId(data?.infectType);

        // 将所属传染病列表数据返回到可编辑表格中
        if (data?.infects?.length) {
          formRef.current?.setFieldsValue({
            table: data?.infects,
          });
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const fetchSave = () => {
    if (type === 'add') {
      return etiologyAddApi;
    } else {
      return etiologyUpdateApi;
    }
  };

  // 1保存 2提交
  const handleSave = async (isDraft: number) => {
    // 业务需求，selectedMedicineItemsList 中每项的 drugEnName 必须存在且不能为空
    let _flat: boolean = false;
    selectedMedicineItemsList?.forEach((_item) => {
      if (_flat) return;

      if (!_item.drugEnName || _item.drugEnName === '请维护简写') {
        _flat = true;
      }
    });

    if (_flat) {
      message.error('选择的药品中有未完成维护英文简写的项');
      return;
    }

    // 所属传染病列表数据处理
    // 所属传染病至少需要一条数据
    const _originTableData = formRef.current?.getFieldValue('table');
    if (!_originTableData?.length) {
      message.error('所属传染病至少需要一条数据');
      return;
    }
    // 处理数据
    const _infectIds = _originTableData?.map(
      (_item: Record<string, any>) => _item?.infectId + ''
    );

    try {
      setBtnLoading(true);
      let values = await formRef.current?.validateFields();
      if (type === 'edit') {
        values.id = id;
      }

      const _params: Record<string, any> = {
        ...values,
        isDraft,
        etiologyBank: activeKey,
        drugs: selectedMedicineItemsList,
        infectIds: _infectIds,
      };

      delete _params['table'];

      const { code, msg } = await fetchSave()(_params as any);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  /**
   *  将多个已选选项移入已选择列表
   */
  const handleSelectItemsWithMultiple = () => {
    if (!selectedOptions.length) {
      message.warning('没有选择任何病原');
      return;
    }
    // 去重
    const _filteredItems: Record<string, any>[] = [];
    selectedOptions?.forEach((_item) => {
      const item = selectedMedicineItemsList.find(
        (item) => item?.id === _item?.id
      );
      if (!item) {
        _filteredItems.push(_item);
      }
    });

    setSelectedMedicineItemsList([
      ...selectedMedicineItemsList,
      ..._filteredItems,
    ]);
  };

  /**
   *  移除多个选项
   */
  const handleRemoveItemsWithMultiple = () => {
    setSelectedMedicineItemsList([...removeOptions]);
  };

  /**
   *  获取病原体名称列表数据
   */
  const queryPathogenNameList = async () => {
    try {
      const { code, data, msg } = await getPathogenNameList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _data: Record<string, any> = [];
      data?.forEach((_item: any) => {
        _data.push({
          label: _item.code + ' ' + _item.name,
          value: ~~_item.id,
        });
      });
      setPathogenNameList(_data as any);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  根据选择的传染病分类ID获取该分类下的所有传染病列表数据
   */
  const queryInfectListByClassId = async (infectClass?: string | number) => {
    try {
      const { code, data, msg } = await getInfectListByClassId({ infectClass });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setInfectList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  选入操作处理函数
   */
  const handleSelectedEvent = (item: Record<string, any>) => {
    setSelectedMedicineItemsList([item, ...selectedMedicineItemsList]);
  };

  useEffect(() => {
    open && type === 'edit' && id && getDetail(id);
  }, [open, id, type]);

  useEffect(() => {
    if (curSelectedInfectClass) {
      queryInfectListByClassId(curSelectedInfectClass);
    } else {
      setInfectList([]);
      formRef.current?.setFieldValue('infectName', '');
    }
  }, [curSelectedInfectClass]);

  useEffect(() => {
    getClassList();
    queryPathogenNameList();
    queryInfectListByClassId();
  }, []);

  const initValue = {
    type,
    selectedMedicineItemsList,
    setSelectedMedicineItemsList,
    handleSelectedEvent,
    removeOptions,
    setRemoveOptions,
    selectedOptions,
    setSelectedOptions,
  };

  return (
    <EditContext.Provider value={initValue}>
      <Drawer
        width="70%"
        title={type === 'add' ? '新增' : '编辑'}
        onClose={() => {
          // Drarwer 关闭时，清空已选择的列表
          setSelectedMedicineItemsList([]);
          setOpen(false);
        }}
        open={open}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <div className=" w-full h-full flex flex-col">
          <div className=" flex-1 p-4 overflow-y-auto bg-white">
            <ProForm
              className="p-6"
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormSelect
                name="etiologyClass"
                label="病原体分类"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '病原体分类必选！' }]}
                options={pathogenClassificationList}
              />
              <ProFormSelect
                name="pathogenId"
                label="病原体名称"
                colProps={{ span: 8 }}
                rules={[{ required: true, message: '病原体名称必选择！' }]}
                options={pathogenNameList}
                fieldProps={{
                  showSearch: true,
                }}
              />
              
              <ProFormText
                name="introInfo"
                label="简介"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="pathogenicity"
                label="致病性"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="immunity"
                label="免疫性"
                colProps={{ span: 8 }}
              />
              <ProFormText
                name="principle"
                label="防治原则"
                colProps={{ span: 8 }}
              />




              <div className="w-full">
                <div className="w-full pl-6 pb-4 font-bold">所属传染病</div>
                <div className="w-full px-6">
                  <EditableProTable
                    rowKey="id"
                    editableFormRef={editorFormRef}
                    name="table"
                    controlled
                    recordCreatorProps={{
                      position: 'bottom',
                      record: () => ({
                        id: (Math.random() * 1000000).toFixed(0),
                      }),
                    }}
                    toolBarRender={() => []}
                    columns={[
                      {
                        dataIndex: 'index',
                        valueType: 'indexBorder',
                        width: 48,
                      },
                      {
                        title: '传染病名称',
                        key: 'infectId',
                        dataIndex: 'infectId',
                        valueType: 'select',
                        fieldProps: {
                          options: infectList,
                          fieldNames: { label: 'infectName', value: 'id' },
                        },
                      },
                      {
                        title: '操作',
                        valueType: 'option',
                        width: 180,
                        render: (text, record, _, action) => [
                          <a
                            key="editable"
                            onClick={() => {
                              action?.startEditable?.(record.id, record);
                            }}
                          >
                            编辑
                          </a>,
                          <a
                            key="delete"
                            onClick={() => {
                              const tableDataSource =
                                formRef.current?.getFieldValue('table') as any;
                              formRef.current?.setFieldsValue({
                                table: tableDataSource.filter(
                                  (item: any) => item.id !== record.id
                                ),
                              });
                            }}
                          >
                            删除
                          </a>,
                        ],
                      },
                    ]}
                    editable={{
                      type: 'multiple',
                      editableKeys,
                      onChange: setEditableRowKeys,
                      actionRender: (row, config, defaultDom) => {
                        return [
                          defaultDom.save,
                          defaultDom.delete,
                          defaultDom.cancel,
                        ];
                      },
                    }}
                  />
                </div>
              </div>
            </ProForm>
            <div className="">
              <div className="pl-6 pb-4 font-bold">
                药品清单维护(耐药性检测使用)
              </div>
              <div className="w-full h-full flex flex-row flex-nowrap">
                {/* 待选择药品清单列表 */}
                <div className="flex-1 border border-solid border-gray-200 ml-6 p-2 rounded-lg">
                  <MedicineListTable />
                </div>
                {/* 中间操作符 */}
                <div className="w-[48px] flex flex-col justify-center items-center gap-4">
                  <div
                    className="cursor-pointer hover:scale-105 hover:text-red-500"
                    onClick={() => handleRemoveItemsWithMultiple()}
                  >
                    <LeftCircleOutlined style={{ fontSize: '30px' }} />
                  </div>
                  <div
                    className="cursor-pointer hover:scale-105 hover:text-blue-500"
                    onClick={() => handleSelectItemsWithMultiple()}
                  >
                    <RightCircleOutlined style={{ fontSize: '30px' }} />
                  </div>
                </div>
                {/* 已选择病原列表 */}
                <div className="flex-1 border border-solid border-gray-200 mr-6 p-2 rounded-lg">
                  <SelectedMedicineListTable />
                </div>
              </div>
            </div>
          </div>
          <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
            <Button type="default" onClick={close}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => handleSave(0)}
              loading={btnLoading}
            >
              保存
            </Button>
          </div>
        </div>
      </Drawer>
    </EditContext.Provider>
  );
};

export default Edit;
