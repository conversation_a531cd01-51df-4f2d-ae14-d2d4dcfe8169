/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  病原列表表格
 */
import React, { Key, useContext, useEffect, useRef, useState } from 'react';
import { Button, Input, message, Modal, Popconfirm, Popover } from 'antd';
import { getMonitorItemSelectedList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { EditContext } from './Edit';

type TSelectedMedicineListTableProps = {};

const SelectedMedicineListTable: React.FC<
  TSelectedMedicineListTableProps
> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState(10);

  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);
  // originDataSource,用于存放原始列表数据
  // 当本地搜索事件发生时，用于还原之前的数据
  const [originDataSource, setOriginDataSource] = useState<
    Record<string, any>[]
  >([]);

  const {
    id,
    type,
    selectedMedicineItemsList,
    setSelectedMedicineItemsList,
    removeOptions,
    setRemoveOptions,
  } = useContext<Record<string, any>>(EditContext);

  const [currentPage, setCurrentPage] = useState<number>(1);

  // 查询条件
  const [searchParams, setSearchParams] = useState({
    pageNum: currentPage,
    pageSize: 10,
  });

  // 在编辑时，单个移除病原时需要存储的被移除的选项的ID
  let removeItemIdRef = useRef<string | null>(null);

  // 英文简写维护toolTipis是否打开
  const [isOpenDrugEnNameTips, setIsOpenDrugEnNameTips] =
    useState<boolean>(false);

  // 当前维护的英文简写
  const [curInputDrugEnName, setCurInputDrugEnName] = useState<string>();
  // 当前需要维护英文简写的数据行索引
  const [drugEnNameRowIndex, setDrugEnNameRowIndex] = useState<number>();

  const {
    infectiousDiseasesList,
    getInfectiousDiseasesList,
    pathogenClassificationList,
    getPathogenClassificationList,
    drugTypeList,
    getDrugTypeList,
  } = useDictStore();

  const columns = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '药品编码',
      dataIndex: 'drugCode',
      hideInSearch: true,
    },
    {
      title: '药品名称',
      dataIndex: 'drugName',
    },
    {
      title: '药品类别',
      dataIndex: 'drugType',
      valueType: 'select',
      fieldProps: {
        options: drugTypeList,
      },
    },
    {
      title: '英文简写',
      dataIndex: 'drugEnName',
      hideInSearch: true,
      render: (_: any, record: Record<string, any>, index: number) => {
        return (
          <Button
            type="link"
            onClick={() => {
              setDrugEnNameRowIndex(index);
              setIsOpenDrugEnNameTips(!isOpenDrugEnNameTips);
            }}
          >
            {record?.drugEnName ? record?.drugEnName : '请维护简写'}
          </Button>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 48,
      render: (_: any, record: Record<string, any>) => [
        <Popconfirm
          title="删除"
          description="确认移除该项?"
          onConfirm={() => {
            if (type === 'edit') {
              removeItemIdRef.current = record.id;
            }
            handleRemoveItem(record);
          }}
          key="del"
        >
          <Button type="link" size="small" danger>
            移除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  const tableReload = () => actionRef.current?.reload();

  /**
   *  移除已选项
   */
  const handleRemoveItem = (record: Record<string, any>) => {
    const newList = selectedMedicineItemsList?.filter(
      (item: Record<string, any>) => item?.drugId !== record?.drugId
    );
    setSelectedMedicineItemsList([...newList]);
    setDataSource([...newList]);
    setOriginDataSource([...newList]);
  };

  /**
   * 通过选择器选择选项的处理函数
   * @param selectedRowKeys
   * @param selectedRows
   */
  const onChange = (
    selectedRowKeys: Key[],
    selectedRows: Record<string, any>[]
  ) => {
    // 移除已选项
    const newList = selectedMedicineItemsList?.filter(
      (item: Record<string, any>) => !selectedRowKeys.includes(item.drugId)
    );
    setRemoveOptions([...newList]);
  };

  /**
   *  获取已选择病原列表
   */
  const queryTableDataList = async () => {
    if (!id) return;
    try {
      const { code, data, msg } = await getMonitorItemSelectedList({
        pageNum: currentPage,
        pageSize: 999999,
        projectId: id,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setDataSource(data?.rows);
      setOriginDataSource(data?.rows);
      setSelectedMedicineItemsList([...data?.rows]);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   *  更新已选药品的英文简称
   */
  const handleEditGrugEnName = () => {
    if (
      !curInputDrugEnName ||
      drugEnNameRowIndex === undefined ||
      drugEnNameRowIndex === null
    ) {
      setIsOpenDrugEnNameTips(false);
      return;
    }

    const _dataSource = [...dataSource];
    _dataSource[drugEnNameRowIndex].drugEnName = curInputDrugEnName;
    setDataSource(_dataSource);
    setOriginDataSource(_dataSource);
    setIsOpenDrugEnNameTips(false);
  };

  useEffect(() => {
    setDataSource([...selectedMedicineItemsList]);
    setOriginDataSource([...selectedMedicineItemsList]);
  }, [selectedMedicineItemsList]);

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
    !pathogenClassificationList?.length && getPathogenClassificationList();
    !drugTypeList?.length && getDrugTypeList();
    queryTableDataList();
  }, []);

  return (
    <div className="w-full h-full pathogen-list__table">
      <div className="w-full bg-white mb-2 rounded py-2 px-4">
        已选择药品列表
      </div>
      <ProTable
        size="small"
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        form={{ span: 12 }}
        params={searchParams}
        dataSource={dataSource}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="drugId"
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        options={false}
        pagination={false}
        rowSelection={{
          onChange,
        }}
        dateFormatter="string"
        toolBarRender={() => []}
        onSubmit={(params: any) => {
          // 触发搜索，本地手动处理
          // 1. 如果params对象为空，则返回
          if (!Object.keys(params).length) return;

          const _dataSource: Record<string, any>[] = [];
          if (params?.drugName && !params?.drugType) {
            dataSource?.forEach((_item) => {
              if (_item?.drugName?.includes(params?.drugName)) {
                _dataSource.push(_item);
              }
            });
          }

          if (!params?.drugName && params?.drugType) {
            dataSource?.forEach((_item) => {
              if (_item?.drugType === params?.drugType) {
                _dataSource.push(_item);
              }
            });
          }

          if (params?.drugName && params?.drugType) {
            dataSource?.forEach((_item) => {
              if (
                _item?.drugName?.includes(params?.drugName) &&
                _item?.drugType === params?.drugType
              ) {
                _dataSource.push(_item);
              }
            });
          }

          setDataSource(_dataSource);
        }}
        onReset={() => {
          // 当搜索条件重置时，触发数据还原
          setDataSource(originDataSource);
        }}
      />
      <Modal
        centered
        title="维护药品英文简写"
        open={isOpenDrugEnNameTips}
        onOk={handleEditGrugEnName}
        onCancel={() => setIsOpenDrugEnNameTips(false)}
        width={300}
        destroyOnClose
      >
        <Input
          placeholder="请填写简写"
          onChange={(e: any) => setCurInputDrugEnName(e.target.value)}
        />
      </Modal>
    </div>
  );
};

export default SelectedMedicineListTable;
