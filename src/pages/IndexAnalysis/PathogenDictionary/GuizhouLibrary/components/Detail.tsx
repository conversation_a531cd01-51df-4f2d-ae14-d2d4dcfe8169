/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message, Table } from 'antd';
import { etiologyDetailApi } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ProTable } from '@ant-design/pro-components';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await etiologyDetailApi({ id });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  return (
    <Drawer
      width="80%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions>
          <Descriptions.Item label="病原体分类">
            {detailInfo?.etiologyClassName}
          </Descriptions.Item>
          <Descriptions.Item label="病原体名称">
            {detailInfo?.etiologyName}
          </Descriptions.Item>
          <Descriptions.Item label="传染病类别">
            {detailInfo?.infectTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="传染病名称">
            {detailInfo?.infectName}
          </Descriptions.Item>



          <Descriptions.Item label="简介">
            {detailInfo?.introInfo}
          </Descriptions.Item>
          <Descriptions.Item label="致病性">
            {detailInfo?.pathogenicity}
          </Descriptions.Item>
          <Descriptions.Item label="免疫性">
            {detailInfo?.immunity}
          </Descriptions.Item>
          <Descriptions.Item label="防治原则">
            {detailInfo?.principle}
          </Descriptions.Item>

        </Descriptions>
        {/* 药品清单列表 */}
        <div className="flex flex-col w-full h-full gap-4 py-4">
          <div className="text-[16px] font-bold">药品清单</div>
          <ProTable
            rowKey="id"
            columns={[
              {
                dataIndex: 'index',
                valueType: 'indexBorder',
                width: 48,
                title: '序号',
              },
              {
                title: '药品编码',
                dataIndex: 'drugCode',
              },
              {
                title: '药品名称',
                dataIndex: 'drugName',
              },
              {
                title: '药品类别',
                dataIndex: 'drugTypeName',
              },
              {
                title: '英文简写',
                dataIndex: 'drugEnName',
              },
            ]}
            search={false}
            options={false}
            dataSource={detailInfo?.drugs}
            pagination={false}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default Detail;
