/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { getDict } from '@/api/dict';
import {
  etiologyDeleteApi,
  etiologyListApi,
  etiologySubmitApi,
  TEtiologyListParams,
} from '@/api/pathogenDictionary';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { TTableData } from '../type';

const GuizhouLibrary: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('1');

  const {
    infectiousDiseasesList,
    getInfectiousDiseasesList,
    pathogenClassificationList,
    getPathogenClassificationList,
  } = useDictStore();

  // 查询条件
  const [searchParams, setSearchParams] = useState<TEtiologyListParams>();

  // 统计数量
  const [counts, setCounts] = useState({
    draft: 0,
    reporting: 0,
  });
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  // 新增/编辑
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit'>('add');

  const [columns, setColumns] = useState([]);

  const actionRef = useRef<ActionType>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const handleDelete = async (ids: string[]) => {
    try {
      const { code, msg } = await etiologyDeleteApi(ids);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const handleSubmit = async (ids: string) => {
    try {
      const { code, msg } = await etiologySubmitApi({ id: ids });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    const columns1: any = [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 48,
        title: '序号',
      },
      {
        title: '病原体分类',
        dataIndex: 'etiologyClass',
        valueType: 'select',
        fieldProps: {
          options: pathogenClassificationList,
        },
      },
      {
        title: '病原体名称',
        dataIndex: 'etiologyName',
      },
      {
        title: '传染病名称',
        dataIndex: 'infectName',
        hideInSearch: true,
      },
      {
        title: '简介',
        dataIndex: 'introInfo',
      },
      {
        title: '致病性',
        dataIndex: 'pathogenicity',
        hideInSearch: true,
      },

      {
        title: '免疫性',
        dataIndex: 'immunity',
      },
      {
        title: '防治原则',
        dataIndex: 'principle',
        hideInSearch: true,
      },

      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        width: 160,
        render: (text: any, record: any, _: any, action: any) => [
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurEventId(record.id);
              setEditType('edit');
              setEditOpen(true);
            }}
            key="edit"
          >
            编辑
          </Button>,
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurEventId(record.id);
              setDetailOpen(true);
            }}
            key="detail"
          >
            详情
          </Button>,
          <Popconfirm
            title="删除"
            description="确认删除该项?"
            onConfirm={() => handleDelete([record.id])}
            key="del"
          >
            <Button type="link" size="small" danger>
              删除
            </Button>
          </Popconfirm>,
        ],
      },
    ];
    const columns2: any = [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 48,
        title: '序号',
      },
      {
        title: '病原体分类',
        dataIndex: 'etiologyClass',
        valueType: 'select',
        fieldProps: {
          options: pathogenClassificationList,
        },
      },
      {
        title: '病原体名称',
        dataIndex: 'etiologyName',
      },
      {
        title: '传染病名称',
        dataIndex: 'infectName',
        hideInSearch: true,
      },
      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        width: 160,
        render: (text: any, record: any, _: any, action: any) => [
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurEventId(record.id);
              setEditType('edit');
              setEditOpen(true);
            }}
            key="edit"
          >
            编辑
          </Button>,
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurEventId(record.id);
              setDetailOpen(true);
            }}
            key="detail"
          >
            详情
          </Button>,
          <Popconfirm
            title="删除"
            description="确认删除该项?"
            onConfirm={() => handleDelete([record.id])}
            key="del"
          >
            <Button type="link" size="small" danger>
              删除
            </Button>
          </Popconfirm>,
        ],
      },
    ];
    if (activeKey === '1') {
      setColumns(columns1);
    } else {
      setColumns(columns2);
    }

    tableReload();
  }, [activeKey]);

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
    !pathogenClassificationList?.length && getPathogenClassificationList();
  }, []);

  return (
    <PageContainer>
      <ProTable<TTableData>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '1',
                label: <span>贵州病原库</span>,
              },
              {
                key: '0',
                label: <span>病原全库</span>,
              },
            ],
            onChange: (key) => setActiveKey(key as string),
          },
        }}
        request={async (params, sort, filter) => {
          let err = false;
          const _params: any = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            infectClass: params?.infectClass,
            infectName: params?.infectName,
            etiologyName: params.etiologyName,
            etiologyClass: params.etiologyClass,
            spreadApproach: params.spreadApproach,
            etiologyBank: ~~activeKey,
            isDraft: activeKey,
          };
          setSearchParams(_params);
          const { code, data, msg } = await etiologyListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }
          if (init && !err) {
            const res = await etiologyListApi({
              ..._params,
              isDraft: activeKey === '1' ? '0' : '1',
            });
            setCounts({
              draft: activeKey === '1' ? data.total : res.data.total,
              reporting: activeKey === '0' ? data.total : res.data.total,
            });
            setInit(false);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          setEditOpen(val);
          tableReload();
        }}
        type={editType}
        id={curEventId}
        infectiousDiseasesList={infectiousDiseasesList}
        pathogenClassificationList={pathogenClassificationList}
        activeKey={activeKey}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
    </PageContainer>
  );
};

export default GuizhouLibrary;
