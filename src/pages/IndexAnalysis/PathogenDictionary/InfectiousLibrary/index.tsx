/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import {
  infectDeleteApi,
  infectListApi,
  infectSubmitApi,
  TInfectListParams,
} from '@/api/pathogenDictionary';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useDictStore } from '@/store/dict';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { TTableData } from '../type';
import './index.less';

const InfectiousLibrary: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('1');

  const { infectiousDiseasesList, getInfectiousDiseasesList } = useDictStore();

  // 查询条件
  const [searchParams, setSearchParams] = useState<TInfectListParams>();

  // 统计数量
  const [counts, setCounts] = useState({
    draft: 0,
    reporting: 0,
  });
  const [init, setInit] = useState(true);
  // 详情
  const [detailOpen, setDetailOpen] = useState(false);
  const [curEventId, setCurEventId] = useState('');
  // 新增/编辑
  const [editOpen, setEditOpen] = useState(false);
  const [editType, setEditType] = useState<'add' | 'edit'>('add');

  const actionRef = useRef<ActionType>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  const columns: ProColumns<TTableData>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '传染病类别',
      dataIndex: 'infectClass',
      valueType: 'select',
      fieldProps: {
        options: infectiousDiseasesList,
      },
    },
    {
      title: '传染病编号',
      dataIndex: 'infectCode',
    },
    {
      title: '传染病名称',
      dataIndex: 'infectName',
    },
    {
      title: '病原体',
      dataIndex: 'etiologyName',
      hideInSearch: true,
    },




    {
      title: '潜伏周期',
      dataIndex: 'incubation',
      hideInSearch: true,
    },
    {
      title: '报告时限',
      dataIndex: 'reportingTime',
      hideInSearch: true,
    },
    {
      title: '传播源',
      dataIndex: 'source',
      hideInSearch: true,
    },
    {
      title: '流行特征',
      dataIndex: 'epidemiology',
      hideInSearch: true,
    },



    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurEventId(record.id);
            setEditType('edit');
            setEditOpen(true);
          }}
          key="edit"
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="确认删除该数据?"
          onConfirm={() => handleDelete([record.id])}
          key="del"
        >
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Popconfirm>,
        <Button
          type="link"
          size="small"
          onClick={() => {
            setCurEventId(record.id);
            setEditType('edit');
            setDetailOpen(true);
          }}
          key="detail"
        >
          详情
        </Button>,
      ],
    },
  ];

  // 选择表头多选框
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleDelete = async (ids: string[]) => {
    try {
      const { code, msg } = await infectDeleteApi(ids);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const handleSubmit = async (ids: string) => {
    try {
      const { code, msg } = await infectSubmitApi({ id: ids });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
  }, []);

  return (
    <PageContainer>
      <ProTable<TTableData>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        toolbar={{}}
        request={async (params, sort, filter) => {
          let err = false;
          const _params: TInfectListParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            infectClass: params.infectClass,
            infectCode: params?.infectCode,
            infectName: params.infectName,
            nature: params.nature,
            // isDraft: activeKey,
          };
          setSearchParams(_params);
          const { code, data, msg } = await infectListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }
          if (init && !err) {
            const res = await infectListApi({
              ..._params,
              isDraft: activeKey === '1' ? '0' : '1',
            });
            setCounts({
              draft: activeKey === '1' ? data.total : res.data.total,
              reporting: activeKey === '0' ? data.total : res.data.total,
            });
            setInit(false);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              setEditType('add');
              setEditOpen(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      {/* 新增/编辑 */}
      <Edit
        open={editOpen}
        setOpen={(val) => {
          setEditOpen(val);
          tableReload();
        }}
        type={editType}
        id={curEventId}
      />
      {/* 详情 */}
      <Detail open={detailOpen} setOpen={setDetailOpen} id={curEventId} />
    </PageContainer>
  );
};

export default InfectiousLibrary;
