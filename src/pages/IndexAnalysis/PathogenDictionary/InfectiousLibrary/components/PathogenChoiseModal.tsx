/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 *  病原体选择
 */
import React, { useState } from 'react';
import { Transfer } from 'antd';

interface RecordType {
  key: string;
  title: string;
  description: string;
}

const mockData = Array.from({ length: 20 }).map<RecordType>((_, i) => ({
  key: i.toString(),
  title: `content${i + 1}`,
  description: `description of content${i + 1}`,
}));

const initialTargetKeys = mockData
  .filter((item) => Number(item.key) > 10)
  .map((item) => item.key);

type TPathogenChoiseModalProps = {
  setIsOpenPathogenChoiseModal: React.Dispatch<React.SetStateAction<boolean>>;
};

const PathogenChoiseModal: React.FC<TPathogenChoiseModalProps> = ({
  setIsOpenPathogenChoiseModal,
}) => {
  const [targetKeys, setTargetKeys] = useState<any>(initialTargetKeys);
  const [selectedKeys, setSelectedKeys] = useState<any>([]);

  const onChange = (nextTargetKeys: any, direction: any, moveKeys: any) => {
    console.log('targetKeys:', nextTargetKeys);
    console.log('direction:', direction);
    console.log('moveKeys:', moveKeys);
    setTargetKeys(nextTargetKeys);
  };

  const onSelectChange = (sourceSelectedKeys: any, targetSelectedKeys: any) => {
    console.log('sourceSelectedKeys:', sourceSelectedKeys);
    console.log('targetSelectedKeys:', targetSelectedKeys);
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  const onScroll = (direction: any, e: any) => {
    console.log('direction:', direction);
    console.log('target:', e.target);
  };

  return (
    <Transfer
      dataSource={mockData}
      titles={['待选择', '已选择']}
      targetKeys={targetKeys}
      selectedKeys={selectedKeys}
      onChange={onChange}
      onSelectChange={onSelectChange}
      onScroll={onScroll}
      render={(item) => item.title}
    />
  );
};

export default PathogenChoiseModal;
