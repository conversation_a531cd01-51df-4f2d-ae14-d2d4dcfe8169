/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, Input, message, Modal, Popover, Transfer } from 'antd';
import { getDict } from '@/api/dict';
import {
  infectAddApi,
  infectDetailApi,
  infectUpdateApi,
} from '@/api/pathogenDictionary';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { useDictStore } from '@/store/dict';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,ProFormDatePicker,
} from '@ant-design/pro-components';
import PathogenChoiseModal from './PathogenChoiseModal';

type TEditProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  type: 'add' | 'edit';
  id: string;
};
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const Edit: React.FC<TEditProps> = ({ open, setOpen, type, id }) => {
  const { token } = useTokenStore();
  const formRef = useRef<ProFormInstance>(null);

  const [btnLoading, setBtnLoading] = useState(false);

  const { infectiousDiseasesList, getInfectiousDiseasesList } = useDictStore();

  // 是否打开病原选择Modal
  const [isOpenPathogenChoiseModal, setIsOpenPathogenChoiseModal] =
    useState<boolean>(false);

  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await infectDetailApi({ id });
      if (code === codeDefinition.QUERY_SUCCESS) {
        formRef.current?.setFieldsValue(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && type === 'edit' && id && getDetail(id);
  }, [open, id, type]);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  const fetchSave = () => {
    if (type === 'add') {
      return infectAddApi;
    } else {
      return infectUpdateApi;
    }
  };

  // 1保存 2提交
  const handleSave = async (isDraft: number) => {
    try {
      setBtnLoading(true);
      let values = await formRef.current?.validateFields();
      if (type === 'edit') {
        values.id = id;
      }
      const { code, msg } = await fetchSave()({
        ...values,
        isDraft,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
  }, []);

  return (
    <Drawer
      width="60%"
      title={type === 'add' ? '新增' : '编辑'}
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <div className=" w-full h-full flex flex-col">
        <div id="testForm" className=" flex-1 p-4 overflow-y-auto bg-white">
          <ProForm
            className="p-6"
            formRef={formRef}
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [24, 0],
            }}
            {...formItemLayout}
            submitter={false}
          >
            <ProFormSelect
              name="infectClass"
              label="传染病类别"
              placeholder="请输入"
              request={async () => {
                try {
                  const { code, data, msg } = await getDict(
                    'infectious_disease_type'
                  );
                  if (code !== codeDefinition.QUERY_SUCCESS) {
                    message.error(msg);
                    return;
                  }
                  const _data = data.map((item: any) => {
                    return {
                      ...item,
                      dictValue: ~~item.dictValue,
                    };
                  });
                  console.log(_data);
                  return _data;
                } catch (err) {
                  throw new Error(`Error: err`);
                } finally {
                }
              }}
              fieldProps={{
                fieldNames: {
                  label: 'dictLabel',
                  value: 'dictValue',
                },
              }}
              rules={[{ required: true, message: '这是必填项' }]}
              colProps={{ span: 8 }}
            />
            <ProFormText
              name="infectCode"
              label="传染病编号"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '传染病编号必填！' }]}
              fieldProps={{}}
            />
            <ProFormText
              name="infectName"
              label="传染病名称"
              colProps={{ span: 8 }}
              rules={[{ required: true, message: '传染病名称必填！' }]}
              fieldProps={{}}
            />
            <ProFormSelect
              name="infectiousNature"
              label="传染病性质"
              placeholder="请输入"
              request={async () => {
                try {
                  const { code, data, msg } = await getDict(
                    'infectious_nature'
                  );
                  if (code !== codeDefinition.QUERY_SUCCESS) {
                    message.error(msg);
                    return;
                  }
                  const _data = data.map((item: any) => {
                    return {
                      ...item,
                      dictValue: ~~item.dictValue,
                    };
                  });
                  return _data;
                } catch (err) {
                  throw new Error(`Error: err`);
                } finally {
                }
              }}
              fieldProps={{
                fieldNames: {
                  label: 'dictLabel',
                  value: 'dictValue',
                },
              }}
              rules={[{ required: true, message: '这是必填项' }]}
              colProps={{ span: 8 }}
            />

            <ProFormText
              name="incubation"
              label="潜伏周期"
              colProps={{ span: 8 }}
              fieldProps={{}}
            />

            <ProFormDatePicker
              name="reportingTime"
              label="报告时限"
              colProps={{ span: 8 }}
              fieldProps={{}}
            />

            <ProFormText
              name="source"
              label="传播源"
              colProps={{ span: 8 }}
              fieldProps={{}}
            />

              <ProFormText
              name="epidemiology"
              label="流行特征"
              colProps={{ span: 8 }}
              fieldProps={{}}
            />



            <ProFormTextArea
              name="commonSymptom"
              label="常见症状"
              width={550}
              colProps={{ span: 21 }}
              labelCol={{ span: 2, offset: 1 }}
            />
            <ProFormTextArea
              name="routeTransmission"
              label="传播途径"
              width={550}
              colProps={{ span: 21 }}
              labelCol={{ span: 2, offset: 1 }}
            />
            <ProFormTextArea
              name="inspectWay"
              label="检测方式"
              width={550}
              colProps={{ span: 21 }}
              labelCol={{ span: 2, offset: 1 }}
            />
          </ProForm>
        </div>
        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => handleSave(0)}
            loading={btnLoading}
          >
            保存
          </Button>
          {/* <Button
            type="primary"
            onClick={() => handleSave(0)}
            loading={btnLoading}
          >
            提交
          </Button> */}
        </div>
      </div>
      <Modal
        className="infectious-library__modal"
        title="请选择病原体"
        open={isOpenPathogenChoiseModal}
        centered
        onCancel={() => setIsOpenPathogenChoiseModal(false)}
      >
        <PathogenChoiseModal
          setIsOpenPathogenChoiseModal={setIsOpenPathogenChoiseModal}
        />
      </Modal>
    </Drawer>
  );
};

export default Edit;
