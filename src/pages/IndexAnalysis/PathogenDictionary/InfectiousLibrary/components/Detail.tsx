/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from 'react';
import { Descriptions, Drawer, message } from 'antd';
import { infectDetailApi } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { useDictStore } from '@/store/dict';

type TDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  id: string;
};

const Detail: React.FC<TDetailProps> = ({ open, setOpen, id }) => {
  const [detailInfo, setDetailInfo] = useState<Record<string, any>>();

  const { infectiousDiseasesList, getInfectiousDiseasesList } = useDictStore();

  const getDetail = async (id: string) => {
    try {
      const { code, data, msg } = await infectDetailApi({ id });
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDetailInfo(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    open && id && getDetail(id);
  }, [open, id]);

  useEffect(() => {
    !infectiousDiseasesList?.length && getInfectiousDiseasesList();
  }, []);

  return (
    <Drawer
      width="60%"
      title="详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
    >
      <div className=" flex flex-col w-full h-full gap-4 p-4">
        <Descriptions
          items={[
            {
              label: '传染病类别',
              children: infectiousDiseasesList?.find(
                (item: Record<string, any>) =>
                  ~~item.value === detailInfo?.infectClass
              )?.label,
            },
            {
              label: '传染病编号',
              children: detailInfo?.infectCode,
            },
            {
              label: '传染病名称',
              children: detailInfo?.infectName,
            },
            {
              label: '传染病性质',
              span: 3,
              children: detailInfo?.infectiousNatureName,
            },





            {
              label: '潜伏周期',
              span: 3,
              children: detailInfo?.incubation,
            },          
            {
              label: '报告时限',
              span: 3,
              children: detailInfo?.reportingTime,
            },         
            {
              label: '传播源',
              span: 3,
              children: detailInfo?.source,
            },          
            {
              label: '流行特征',
              span: 3,
              children: detailInfo?.epidemiology,
            },



            {
              label: '常见症状',
              span: 3,
              children: detailInfo?.commonSymptom,
            },
            {
              label: '传播途径',
              span: 3,
              children: detailInfo?.routeTransmission,
            },
            {
              label: '检测方式',
              span: 3,
              children: detailInfo?.inspectWay,
            },
          ]}
        />
      </div>
    </Drawer>
  );
};

export default Detail;
