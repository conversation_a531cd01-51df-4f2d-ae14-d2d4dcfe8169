/*
 * @Date: 2024-11-22 09:06:41
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-02 11:28:25
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\BusinessIndicatorAnalysis\PathogenComplexInfection\index.tsx
 * @Description: 复合感染分析
 */
import { useEffect, useRef, useState } from 'react';
import { Button, Card, DatePicker, Form, message, Select } from 'antd';
import {
  pathogenComplexInfectionCountApi,
  pathogenComplexInfectionListApi,
} from '@/api/businessIndicatorAnalysis';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import DownloadButton from '@/components/DownloadButton';

const { RangePicker } = DatePicker;

// 统计维度
const typOptions = [
  { label: '区域', value: 'areaName', numValue: 0 },
  { label: '人群', value: 'gender', numValue: 1 },
  { label: '年龄', value: 'age', numValue: 2 },
];
const defaultSampleDate = [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()];

const PathogenComplexInfection: React.FC = () => {
  const [searchParams, setSearchParams] = useState<Record<string, any>>();
  const [loading, setLoading] = useState(false);
  const [pathogenList, setPathogenList] = useState<
    {
      id: string;
      code: string;
      name: string;
    }[]
  >([]);

  // 统计维度
  const [type, setType] = useState<string>();
  const [sampleDate, setSampleDate] = useState<any>(defaultSampleDate);
  const [curPathogen, setCurPathogen] = useState<string>();

  const actionRef = useRef<ActionType>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '区域',
      dataIndex: 'areaName',
      hideInSearch: true,
    },
    {
      title: '人群',
      dataIndex: 'job',
      hideInSearch: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      hideInSearch: true,
    },
  ];

  const defaultColumns: ProColumns[] = [
    {
      title: '病原名称',
      dataIndex: 'complexEtiologyId',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        options: pathogenList,
        fieldNames: { label: 'name', value: 'id' },
      }
    },
    {
      title: '复合感染数量',
      dataIndex: 'complexInfectionCount',
      hideInSearch: true,
    },
    {
      title: '复合感染率',
      dataIndex: 'complexInfectionRate',
      hideInSearch: true,
    },
  ];

  const [tableColumns, setTableColumns] = useState<ProColumns[]>([]);

  const getPathogenList = async () => {
    try {
      const { code, data, msg } = await getPathogenNameList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setPathogenList(data);
        if (data && data.length) {
          setCurPathogen(data[0].id);
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getPathogenList();
  }, []);

  const [tableData, setTableData] = useState<Record<string, any>[]>([])
  const getTableData = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await pathogenComplexInfectionListApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setTableData(data)
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getCount = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await pathogenComplexInfectionCountApi(
        params
      );
      if (code === codeDefinition.QUERY_SUCCESS) {
        console.log(data);
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    console.log(curPathogen, sampleDate, type);
    if (!sampleDate || !sampleDate.length) {
      message.warning('请选择采样日期');
      return;
    }
    if (!curPathogen) {
      message.warning('请选择病原体');
      return;
    }
    if (!type) {
      message.warning('请选择统计维度');
      return;
    }
    const _params = {
      etiologyId: curPathogen,
      sampleStartDate: dayjs(sampleDate[0]).format('YYYY-MM-DD'),
      sampleEndDate: dayjs(sampleDate[1]).format('YYYY-MM-DD'),
      dimension: typOptions.find((item) => item.value === type)?.numValue,
    };
    setSearchParams(_params);
    getTableData(_params);
    getCount(_params);
    const _columns = columns.filter(
      (item) => type === (item.dataIndex as string)
    );
    setTableColumns([..._columns, ...defaultColumns]);
  };

  const handleReset = () => {
    setSampleDate(() => defaultSampleDate)
    if(pathogenList.length) {
      setCurPathogen(pathogenList[0].id);
    }
    setType(undefined)
    setTableColumns(() => defaultColumns)
    setTableData([])
    setSearchParams({})
  }

  return (
    <div className="flex flex-col gap-5">
      <Card title="数据筛选">
        <Form submitter={false} autoComplete="off" layout="inline">
          <Form.Item label="采样日期">
            <RangePicker
              value={sampleDate}
              onChange={(val) => setSampleDate(val)}
            />
          </Form.Item>
          <Form.Item label="病原体">
            <Select
              className="!w-56"
              options={pathogenList}
              showSearch
              fieldNames={{
                label: 'name',
                value: 'id',
              }}
              placeholder="请选择"
              optionFilterProp="name"
              value={curPathogen}
              onChange={(val) => setCurPathogen(val)}
            ></Select>
          </Form.Item>
        </Form>
      </Card>
      <Card title="统计维度">
        <div className="flex gap-5 leading-8">
          <div className="flex-1">
            <Form submitter={false} autoComplete="off" layout="inline">
              <Form.Item label="统计维度">
                <Select
                  className="!w-56"
                  options={typOptions}
                  showSearch
                  placeholder="请选择"
                  optionFilterProp="name"
                  value={type}
                  onChange={(val) => setType(val)}
                ></Select>
              </Form.Item>
            </Form>
          </div>
          <div className="flex items-center gap-6">
            <Button type="primary" onClick={handleSearch} loading={loading}>
              统计
            </Button>
            <Button loading={loading} onClick={handleReset}>重置</Button>
          </div>
        </div>
      </Card>
      <Card title="统计结果">
        <ProTable
          loading={loading}
          columns={tableColumns}
          actionRef={actionRef}
          cardBordered
          bordered
          headerTitle=""
          dataSource={tableData}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={false}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={false}
          dateFormatter="string"
        />
      </Card>
    </div>
  );
};

export default PathogenComplexInfection;
