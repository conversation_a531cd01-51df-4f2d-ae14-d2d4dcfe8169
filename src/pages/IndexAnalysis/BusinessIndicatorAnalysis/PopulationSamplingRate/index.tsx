/* eslint-disable @typescript-eslint/no-unused-vars */
// 人群采样率分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  getPopulationSamplingRateEchartsData,
  getPopulationSamplingRatePage,
} from '@/api/businessIndicatorAnalysis';
import { queryDictValueList } from '@/api/settings';
import { codeDefinition } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import DownloadButton from '@/components/DownloadButton';
import SampleList from './sampleList';

type TRegionalSamplingRateProps = {};

const PopulationSamplingRate: React.FC<TRegionalSamplingRateProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 市县区数据
  const [populationList, setPopulationList] = useState<any[]>([]);
  // 当前点击选择的区域id
  const [listId, setListId] = useState<string>('');
  // 样本明细表格Drawer
  const [openSampleList, setOpenSampleList] = useState<boolean>(false);
  // echarts数据 - 横坐标数据
  const [echartsXData, setEchartsXData] = useState<any[]>([]);
  // echarts数据 - 男性样本数据集合
  const [echartsSeriesManList, setEchartsSeriesManList] = useState<any[]>([]);
  // echarts数据 - 女性样本数据集合
  const [echartsSeriesWomanList, setEchartsSeriesWomanList] = useState<any[]>(
    []
  );
  // echarts数据 - 男性采样率占比集合
  const [echartsSeriesManRateList, setEchartsSeriesManRateList] = useState<
    any[]
  >([]);
  // echarts数据 - 男性采样率占比集合
  const [echartsSeriesWomanRateList, setEchartsSeriesWomanRateList] = useState<
    any[]
  >([]);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'sampleYear',
    },
    {
      title: '人群',
      dataIndex: 'peopleLevel',
      valueType: 'select',
      fieldProps: {
        options: populationList,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => <span>{record?.peopleName}</span>,
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenSampleList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
    {
      title: '采集样本占比',
      dataIndex: 'sampleRate',
      hideInSearch: true,
      render: (_, record) => <span>{record?.sampleRate}%</span>,
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: [
        '男性采集样本数量',
        '女性采集样本数量',
        '男性采样率占比',
        '女性采样率占比',
      ],
    },
    xAxis: [
      {
        type: 'category',
        data: echartsXData,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '采集样本数量',
        axisLabel: {},
      },
      {
        type: 'value',
        name: '采样率占比',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '男性采集样本数量',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesManList,
      },
      {
        name: '女性采集样本数量',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesWomanList,
      },
      {
        name: '男性采样率占比',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesManRateList,
      },
      {
        name: '女性采样率占比',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesWomanRateList,
      },
    ],
  };

  // 关闭抽屉
  const close = () => setOpenSampleList(false);

  /**
   * 获取人群分类
   */
  const queryPopulationList = async () => {
    try {
      const { code, rows, msg } = await queryDictValueList({
        dictType: 'sys_user_sex',
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPopulationList(rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取Echarts数据
   * @returns
   */
  const queryPopulationSamplingRateEchartsData = async () => {
    try {
      const { code, data, msg } = await getPopulationSamplingRateEchartsData(
        {}
      );
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _xData: any = [];
      const _echartsSeriesManList: any = [];
      const _echartsSeriesWomanList: any = [];
      const _echartsSeriesManRateList: any = [];
      const _echartsSeriesWomanRateList: any = [];
      data?.forEach((_item: any) => {
        _xData.push(_item?.sampleYear);
        _echartsSeriesManList.push(_item?.manNum);
        _echartsSeriesWomanList.push(_item?.womanNum);
        _echartsSeriesManRateList.push(_item?.manRate);
        _echartsSeriesWomanRateList.push(_item?.womanRate);
      });
      setEchartsXData(_xData);
      setEchartsSeriesManList(_echartsSeriesManList);
      setEchartsSeriesWomanList(_echartsSeriesWomanList);

      setEchartsSeriesManRateList(_echartsSeriesManRateList);
      setEchartsSeriesWomanRateList(_echartsSeriesWomanRateList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryPopulationList();
    queryPopulationSamplingRateEchartsData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            const { code, data, msg } = await getPopulationSamplingRatePage(
              param
            );
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.records ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <DownloadButton
              method="get"
              url="/sample/rate/people/sampleAreaDataExport"
              // params={queryParamsCache}
            >
              导出统计结果
            </DownloadButton>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openSampleList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default PopulationSamplingRate;
