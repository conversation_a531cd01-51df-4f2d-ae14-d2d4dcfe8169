// 样本列表页
import React, { useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import { getMonthlyDetectionRateChildPage } from '@/api/businessIndicatorAnalysis';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import Details from './details';

type TSampleListProps = {
  close: () => void;
  listId: string;
};

const SampleList: React.FC<TSampleListProps> = ({ close, listId }) => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);

  // 当前点击选择的区域id
  const [detailId, setDetailId] = useState<string>('');
  // 样本详情
  const [openDetails, setOpenDetails] = useState<boolean>(false);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleNo',
    },
    {
      title: '样本名称',
      dataIndex: 'sampleName',
    },
    {
      title: '初验日期',
      dataIndex: 'startCheckDate',
      hideInSearch: true,
    },
    // 该字段暂不展示
    // {
    //   title: '复核日期',
    //   dataIndex: 'sampleRate',
    //   hideInSearch: true,
    // },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="updateRow"
          onClick={() => {
            setDetailId(record.id);
            setOpenDetails(true);
          }}
        >
          查看详情
        </Button>,
      ],
    },
  ];

  // 关闭抽屉
  const closeDetail = () => setOpenDetails(false);

  return (
    <div className="">
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            const { code, data, msg } = await getMonthlyDetectionRateChildPage(
              param
            );
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.records ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={false}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => []}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本详情"
        onClose={closeDetail}
        open={openDetails}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Details closeDetail={closeDetail} detailId={detailId} />
      </Drawer>
    </div>
  );
};

export default SampleList;
