/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
// 样本详情
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { getPopulationDetectionDetailsById } from '@/api/businessIndicatorAnalysis';
import { fileGroupApi } from '@/api/common';
import { ossObjectApi } from '@/api/oss';
import { codeDefinition } from '@/constants';
import {
  ProDescriptions,
  ProForm,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { getIconByName } from '@/utils/upload';

type TDetailsProps = {
  closeDetail: () => void;
  detailId: string;
};

const Details: React.FC<TDetailsProps> = ({ closeDetail, detailId }) => {
  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);

  // 样本详情
  const [sampleDetailsData, setSampleDetailsData] =
    useState<Record<string, any>>();
  // 样本检测信息表格数据
  const [sampleDetectionInfoList, setSampleDetectionInfoList] = useState<
    Record<string, any>[]
  >([]);

  const columns: any = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '检测项目',
      dataIndex: 'inspectionItem',
    },
    {
      title: '检测依据',
      dataIndex: 'inspectionStandard',
    },
    {
      title: '判定标准',
      dataIndex: 'determiningStandard',
    },
    {
      title: '检出限',
      dataIndex: 'checkLimit',
    },
    {
      title: '检出限单位',
      dataIndex: 'checkLimitUnit',
    },
    {
      title: '检出结果',
      dataIndex: 'testResult',
    },
    {
      title: '检出结果单位',
      dataIndex: 'testResultUnit',
    },
  ];

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async (id: string | number) => {
    try {
      const { code, data, msg } = await getPopulationDetectionDetailsById({
        id,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSampleDetailsData(data);
      setSampleDetectionInfoList(data?.sampleDetectionInfoList);

      // 处理复核图片
      if (data?.sampleDetectionImgUrlList?.length) {
        const _res = await fileGroupApi({
          businessId: data?.sampleDetectionImgUrlList[0],
        });
        if (_res?.code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }
        const _result: any[] = [];
        _res?.data?.forEach((_item: any) => {
          _result.push({
            uid: _item?.ossId,
            name: _item?.originalName,
            url: _item?.fileAddr,
          });
        });
        formRef?.current?.setFieldValue('images', _result);
      }

      // 处理上传的附件资源
      if (data?.sampleDetectionReport) {
        const result = await ossObjectApi(data?.sampleDetectionReport);
        formRef2?.current?.setFieldValue('file', [
          {
            url: result?.data[0]?.fileAddr,
            name: result?.data[0]?.originalName,
          },
        ]);
      }
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getDetailData(detailId);
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4 h-full w-full p-4">
      <BlockContainer title="样本基本信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="样本编号">
            {sampleDetailsData?.sampleNo}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本名称">
            {sampleDetailsData?.sampleName}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本数量">
            {sampleDetailsData?.sampleNum}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本包装">
            {sampleDetailsData?.samplePackage}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本状态">
            {sampleDetailsData?.sampleStatus}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本存储条件">
            {sampleDetailsData?.sampleStorageCondition}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="样本采样日期">
            {sampleDetailsData?.sampleGetDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="采样区域">
            {sampleDetailsData?.sampleArea}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="样本处理信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="处理方式">
            {sampleDetailsData?.handleWay}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="处理日期">
            {sampleDetailsData?.handleDate}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="处理描述">
            {sampleDetailsData?.handleDesc}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="样本检测信息">
        <ProTable
          columns={columns}
          cardBordered
          rowKey="id"
          options={false}
          search={false}
          dataSource={sampleDetectionInfoList}
          pagination={false}
        />
      </BlockContainer>
      <BlockContainer title="样本处置信息">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="处置日期">
            {sampleDetailsData?.disposeDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="处置人">
            {sampleDetailsData?.disposePeople}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="处置方式">
            {sampleDetailsData?.disposeWay}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          <ProDescriptions.Item label="处理描述">
            {sampleDetailsData?.disposeSampleDesc}
          </ProDescriptions.Item>
        </ProDescriptions>
      </BlockContainer>
      <BlockContainer title="样本处置附件">
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="样本检测结果">
            {sampleDetailsData?.sampleDetectionResult === 0 ? '阴性' : '阳性'}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="检测日期">
            {sampleDetailsData?.detectionDate}
          </ProDescriptions.Item>
          <ProDescriptions.Item label="检测人员">
            {sampleDetailsData?.detectionPeople}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProDescriptions column={1}>
          {sampleDetailsData?.sampleDetectionImgUrlList?.length ? (
            <ProForm
              onFinish={() => {}}
              formRef={formRef}
              disabled
              formKey="base-form-use-demo"
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [64, 0],
                justify: 'space-between',
              }}
              submitter={false}
            >
              <ProFormUploadButton
                name="images"
                label="样品检测图片"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  listType: 'picture-card',
                  onChange: (info) => {},
                  onPreview: () => {},
                }}
                action={''}
                wrapperCol={{
                  span: 24,
                }}
              />
            </ProForm>
          ) : (
            <ProDescriptions.Item label="样品检测图片">-</ProDescriptions.Item>
          )}
        </ProDescriptions>
        <ProDescriptions column={1}>
          {sampleDetailsData?.sampleDetectionReport ? (
            <ProForm
              onFinish={() => {}}
              formRef={formRef2}
              disabled
              formKey="base-form-use-demo"
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [64, 0],
                justify: 'space-between',
              }}
              submitter={false}
            >
              <ProFormUploadButton
                name="file"
                label="样本检测报告"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  listType: 'picture-card',
                  onChange: (info) => {},
                  onPreview: () => {},
                }}
                action={''}
                wrapperCol={{
                  span: 24,
                }}
              />
            </ProForm>
          ) : (
            <ProDescriptions.Item label="样品检测附件">-</ProDescriptions.Item>
          )}
        </ProDescriptions>
      </BlockContainer>
    </div>
  );
};

export default Details;
