/*
 * @Date: 2024-11-22 13:53:00
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-28 16:40:35
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\BusinessIndicatorAnalysis\PathogenSpectrumAnalysis\index.tsx
 * @Description: 病原谱分析
 */
import { useEffect, useState } from 'react';
import { Button, Card, DatePicker, Form, message, Select } from 'antd';
import { pathogenSpectrumAnalysisDataApi } from '@/api/businessIndicatorAnalysis';
import { monitorListApi } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';
import { TDataSource } from '../PathogenPositiveTrend/type';

const { RangePicker } = DatePicker;
const defaultSampleDate = [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()];

const PathogenSpectrumAnalysis: React.FC = () => {
  const [sampleDate, setSampleDate] = useState<any>(defaultSampleDate);
  const [curItem, setCurItem] = useState<number>();

  const [chartKey, setChartKey] = useState(() => new Date().getTime())
  const [option, setOption] = useState<ECOption>({
    tooltip: {},
    legend: {
      left: 'right',
    },
    calculable: true,
    grid: {
      top: 80,
      bottom: 100,
      tooltip: {
        trigger: 'axis',
      },
    },
    xAxis: [
      {
        type: 'category',
        axisLabel: { interval: 0 },
        data: [],
        splitLine: { show: false },
      },
    ],
    yAxis: [
      {
        type: 'value',
        max: 0,
      },
    ],
    series: [
      { type: 'bar', data: [], barMaxWidth: 40 },
      {
        type: 'pie',
        center: ['75%', '30%'],
        radius: ['20%', '30%'],
        z: 100,
        data: [],
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
            formatter: (params) => {
              return `${params.name}\n菌（毒）株数量: ${params.value}`
            }
          },
        },
      },
    ],
  });

  // 监测项目库
  const [monitorList, setMonitorList] = useState<Record<string, any>[]>([]);
  const getMonitorList = async () => {
    try {
      const { code, data, msg } = await monitorListApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setMonitorList(data);
        if (data && data.length) {
          setCurItem(data[0].projectName);
          handleSearch(data[0].projectName)
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };
  useEffect(() => {
    getMonitorList();
  }, []);

  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState<TDataSource[]>([]);

  const getDataSource = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await pathogenSpectrumAnalysisDataApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(data?.areaStrains || []);
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (projectName: string) => {
    const _projectName = projectName || curItem;
    if (!sampleDate || !sampleDate.length) {
      message.warning('请选择采样时间');
      return;
    }
    if (!_projectName) {
      message.warning('请选择监测项目');
      return;
    }
    const _params = {
      projectName: _projectName,
      startDate: dayjs(sampleDate[0]).format('YYYY-MM-DD'),
      endDate: dayjs(sampleDate[1]).format('YYYY-MM-DD'),
    };
    getDataSource(_params);
  };

  const handleReset = () => {
    setCurItem(monitorList[0].projectName);
    setSampleDate(() => defaultSampleDate)
    const _params = {
      projectName: monitorList[0].projectName,
      startDate: dayjs(defaultSampleDate[0]).format('YYYY-MM-DD'),
      endDate: dayjs(defaultSampleDate[1]).format('YYYY-MM-DD'),
    };
    getDataSource(_params);
    setChartKey(() => new Date().getTime())
  }

  useEffect(() => {
    const _xData = dataSource.length
      ? [
          ...new Set(
            dataSource.map((item) => item.etiologyName).filter((i) => i)
          ),
        ]
      : [];
    const _barData = _xData.map((item) =>
      dataSource
        .filter((data) => data.etiologyName === item)
        .reduce((pre, cur) => Number(cur.strainsCount) + pre, 0)
    );
    const _area = dataSource.length
      ? [...new Set(dataSource.map((item) => item.dimension))]
      : [];
    const _pieData = _area.map((item) => ({
      name: item,
      value: dataSource
        .filter((data) => data.dimension === item)
        .reduce((pre, cur) => Number(cur.strainsCount) + pre, 0),
    }));
    const _newOption: any = cloneDeep(option);
    _newOption.xAxis[0].data = _xData;
    _newOption.yAxis[0].max = _barData.length
      ? _barData.reduce((pre, cur) => (cur > pre ? cur : pre), 0)* 2
      : 0;
    _newOption.series[0].data = _barData;
    _newOption.series[1].data = _pieData;
    setOption(_newOption);
  }, [dataSource]);

  return (
    <div className="flex flex-col flex-nowrap gap-5">
      <Card>
        <div className="flex gap-5 leading-8">
          <div className="flex-1">
            <Form submitter={false} autoComplete="off" layout="inline">
              <Form.Item label="监测项目">
                <Select
                  className="!w-56"
                  options={monitorList}
                  showSearch
                  fieldNames={{
                    label: 'projectName',
                    value: 'projectName',
                  }}
                  placeholder="请选择"
                  optionFilterProp="projectName"
                  value={curItem}
                  onChange={(val) => setCurItem(val)}
                  allowClear
                ></Select>
              </Form.Item>
              <Form.Item label="采样日期">
                <RangePicker
                  value={sampleDate}
                  onChange={(val) => setSampleDate(val)}
                />
              </Form.Item>
            </Form>
          </div>
          <div className="flex items-center gap-3">
            <Button
              type="primary"
              loading={loading}
              onClick={() => handleSearch('')}
            >
              统计
            </Button>
            <Button loading={loading} onClick={handleReset}>重置</Button>
            {/* <DownloadButton url="/data/spreadAnalyze/monitoring/export" method="get">
              导出统计结果
            </DownloadButton> */}
          </div>
        </div>
      </Card>
      <Card>
        <ChartsWrapper
          option={option}
          height={640}
          handleEvent={(val) => {
            if (val.seriesType === 'pie') {
              const _xData = dataSource.length
                ? [
                    ...new Set(
                      dataSource
                        .map((item) => item.etiologyName)
                        .filter((i) => i)
                    ),
                  ]
                : [];
              const _barData = _xData.map((item) =>
                dataSource
                  .filter(
                    (data) =>
                      data.etiologyName === item && data.dimension === val.name
                  )
                  .reduce((pre, cur) => Number(cur.strainsCount) + pre, 0)
              );
              const _newOption: any = cloneDeep(option);
              _newOption.xAxis[0].data = _xData;
              _newOption.yAxis[0].max = _barData.length
                ? _barData.reduce((pre, cur) => (cur > pre ? cur : pre), 0)
                : 0;
              _newOption.series[0].data = _barData;
              setOption(_newOption);
            }
          }}
          key={chartKey}
        />
      </Card>
    </div>
  );
};

export default PathogenSpectrumAnalysis;
