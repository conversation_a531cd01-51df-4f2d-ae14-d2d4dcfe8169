/*
 * @Date: 2024-11-22 13:16:17
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-26 18:02:43
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\BusinessIndicatorAnalysis\PathogenPositiveTrend\index.tsx
 * @Description: 病原阳性趋势分析
 */
import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  Card,
  DatePicker,
  Form,
  message,
  Select,
  Tabs,
  TabsProps,
} from 'antd';
import { pathOgenPositiveTrendListApi } from '@/api/businessIndicatorAnalysis';
import { monitorListApi } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Chart from './components/Chart';
import { TDataSource } from './type';

const { RangePicker } = DatePicker;
const defaultSampleDate = [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()];
// 统计维度
const typeOptions = [
  { label: '区域', value: 'areaName', numValue: 0 },
  { label: '人群', value: 'gender', numValue: 1 },
  { label: '年龄', value: 'age', numValue: 2 },
  { label: '季度', value: 'quarter', numValue: 3 },
  { label: '月度', value: 'month', numValue: 4 },
];

const PathogenPositiveTrend: React.FC = () => {
  const [sampleDate, setSampleDate] = useState<any>(defaultSampleDate);
  const [curItem, setCurItem] = useState<number>();
  const [curType, setCurType] = useState<string>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '区域',
      dataIndex: 'dimension',
      hideInSearch: true,
    },
    {
      title: '人群',
      dataIndex: 'dimension',
      hideInSearch: true,
    },
    {
      title: '年龄',
      dataIndex: 'dimension',
      hideInSearch: true,
    },
    {
      title: '季度',
      dataIndex: 'dimension',
      hideInSearch: true,
    },
    {
      title: '月度',
      dataIndex: 'dimension',
      hideInSearch: true,
    },
  ];
  const defaultColumns: ProColumns[] = [
    {
      title: '病原体',
      dataIndex: 'etiologyName',
      hideInSearch: true,
    },
    {
      title: '阳性样本数',
      dataIndex: 'positiveSamplesCount',
      hideInSearch: true,
    },
    {
      title: '菌（毒）株数量',
      dataIndex: 'strainsCount',
      hideInSearch: true,
    },
  ];

  const [tableColumns, setTableColumns] = useState<ProColumns[]>([]);

  // 监测项目库
  const [monitorList, setMonitorList] = useState<Record<string, any>[]>([]);
  const getMonitorList = async () => {
    try {
      const { code, data, msg } = await monitorListApi();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setMonitorList(data);
        if (data && data.length) {
          setCurItem(data[0].projectName);
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };
  useEffect(() => {
    getMonitorList();
  }, []);
  const [dataSource, setDataSource] = useState<TDataSource[]>([]);

  const [activeKey, setActiveKey] = useState('positiveSamplesCount');
  const items: TabsProps['items'] = useCallback(
    () => [
      {
        key: 'positiveSamplesCount',
        label: '阳性样本趋势分析',
        children: (
          <Chart
            dataSource={dataSource}
            activeKey={activeKey}
            key="positiveSamplesCount"
          />
        ),
      },
      {
        key: 'strainsCount',
        label: '菌（毒）株数量趋势分析',
        children: (
          <Chart
            dataSource={dataSource}
            activeKey={activeKey}
            key="strainsCount"
          />
        ),
      },
    ],
    [dataSource, activeKey]
  );

  const [loading, setLoading] = useState(false);

  const getDataSource = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await pathOgenPositiveTrendListApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(data);
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (!sampleDate || !sampleDate.length) {
      message.warning('请选择采样时间');
      return;
    }
    if (!curItem) {
      message.warning('请选择监测项目');
      return;
    }
    if (!curType) {
      message.warning('请选择统计维度');
      return;
    }
    const _params = {
      dimension: curType,
      projectName: curItem,
      startDate: dayjs(sampleDate[0]).format('YYYY-MM-DD'),
      endDate: dayjs(sampleDate[1]).format('YYYY-MM-DD'),
    };
    getDataSource(_params);

    const _columns = columns.filter((item) => curType === item.title);
    setTableColumns([..._columns, ...defaultColumns]);
  };

  const handleReset = () => {
    setSampleDate(() => defaultSampleDate);
    setCurItem(monitorList.length ? monitorList[0].projectName : '');
    setCurType(undefined);
    setDataSource([]);
    setTableColumns([...defaultColumns]);
  };

  return (
    <div className="flex flex-col flex-nowrap gap-5">
      <Card title="数据筛选">
        <Form submitter={false} autoComplete="off" layout="inline">
          <Form.Item label="监测项目">
            <Select
              className="!w-56"
              options={monitorList}
              showSearch
              fieldNames={{
                label: 'projectName',
                value: 'projectName',
              }}
              placeholder="请选择"
              optionFilterProp="projectName"
              value={curItem}
              onChange={(val) => setCurItem(val)}
              allowClear
            ></Select>
          </Form.Item>
          <Form.Item label="采样日期">
            <RangePicker
              value={sampleDate}
              onChange={(val) => setSampleDate(val)}
            />
          </Form.Item>
        </Form>
      </Card>
      <Card title="统计维度">
        <div className="flex gap-5 leading-8">
          <div className="flex-1">
            <Form submitter={false} autoComplete="off" layout="inline">
              <Form.Item label="统计维度">
                <Select
                  className="!w-56"
                  options={typeOptions.map((item) => ({
                    label: item.label,
                    value: item.label,
                  }))}
                  placeholder="请选择"
                  value={curType}
                  onChange={(val) => setCurType(val)}
                  allowClear
                ></Select>
              </Form.Item>
            </Form>
          </div>
          <div className="flex items-center gap-3">
            <Button type="primary" onClick={handleSearch} loading={loading}>
              统计
            </Button>
            <Button loading={loading} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>
      </Card>
      <Card title="统计结果">
        <Tabs
          activeKey={activeKey}
          items={items()}
          onChange={(val: string) => setActiveKey(val)}
        />

        <ProTable
          loading={loading}
          className=" mt-5"
          columns={tableColumns}
          bordered
          headerTitle=""
          dataSource={dataSource}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={false}
          options={false}
          pagination={false}
          dateFormatter="string"
        />
      </Card>
    </div>
  );
};

export default PathogenPositiveTrend;
