import { useEffect, useState } from 'react';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';
import { TDataSource } from '../type';

type TChartProps = {
  dataSource: TDataSource[];
  activeKey: string;
};

const Chart: React.FC<TChartProps> = ({ dataSource, activeKey }) => {
  const [key, setKey] = useState(() => new Date().getTime())
  const defaultOption: ECOption = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '3%',
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [],
  };
  const [option, setOption] = useState<ECOption>(() => defaultOption);

  useEffect(() => {
    if (!dataSource.length) {
      setKey(() => new Date().getTime())
      setOption(() => defaultOption);
    } else {
      const _newOption: any = cloneDeep(option);
      const _xData = dataSource.length
        ? [...new Set(dataSource.map((item) => item.dimension))]
        : [];
      const _seriesBase = dataSource.length
        ? [...new Set(dataSource.map((item) => item.etiologyName))]
        : [];
      _newOption.xAxis.data = _xData;
      _newOption.series = _seriesBase.length
        ? _seriesBase.map((item) => ({
            name: item,
            type: 'line',
            data: _xData.map((x) => {
              const data = dataSource.find(
                (data) => data.dimension === x && data.etiologyName === item
              );
              return data ? data[activeKey as keyof TDataSource] : '0';
            }),
          }))
        : [];
      setOption(_newOption);
    }
  }, [dataSource, activeKey]);

  return <ChartsWrapper option={option} height={300} key={key} />;
};

export default Chart;
