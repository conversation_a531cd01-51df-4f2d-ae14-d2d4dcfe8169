/* eslint-disable @typescript-eslint/no-unused-vars */
// 病原阳性趋势分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getPathogenList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import SampleList from './sampleList';

type TPathogenPositiveTrendProps = {};

const PathogenPositiveTrend: React.FC<TPathogenPositiveTrendProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  const [listId, setListId] = useState<string>('');
  const [openDetailsList, setOpenDetailsList] = useState<boolean>(false);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '病原名称',
      dataIndex: 'sampleYear',
    },
    {
      title: '周别',
      dataIndex: 'sampleYear',
      hideInSearch: true,
    },
    {
      title: '检出阳性数量',
      dataIndex: 'sampleYear',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenDetailsList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['平均耐药时长', '最高耐药时长', '最低耐药时长'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '药物1',
        '药物2',
        '药物3',
        '药物4',
        '药物5',
        '药物6',
        '药物7',
        '药物8',
        '药物9',
        '药物10',
        '药物11',
        '药物12',
      ],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '平均耐药时长',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210, 198, 350, 22, 78, 10],
      },
      {
        name: '最高耐药时长',
        type: 'line',
        data: [220, 182, 191, 234, 290, 330, 310, 225, 210, 200, 189, 110],
      },
      {
        name: '最低耐药时长',
        type: 'line',
        data: [150, 232, 201, 154, 190, 330, 410],
      },
    ],
  };

  // 关闭抽屉
  const close = () => setOpenDetailsList(false);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            // const { code, data, msg } = await getPathogenPointList(param);
            // if (code !== codeDefinition.QUERY_SUCCESS) {
            //   message.error(msg);
            // }
            // return {
            //   data: data?.rows ?? [],
            //   total: data?.total ?? 0,
            //   success: true,
            // };

            return {
              data: [
                {
                  id: 1,
                  sampleYear: 2024,
                  cityName: '某市',
                  areaName: '某县',
                  sampleNum: 20,
                  sampleRate: '20%',
                },
              ],
              total: 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<ExportOutlined />}
              onClick={() => {}}
              type="primary"
            >
              导出统计结果
            </Button>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openDetailsList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default PathogenPositiveTrend;
