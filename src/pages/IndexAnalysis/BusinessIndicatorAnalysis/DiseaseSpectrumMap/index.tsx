/* eslint-disable @typescript-eslint/no-unused-vars */
// 病谱图构成分析
import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import { getAllCityAreaList, getPathogenList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import SampleList from './sampleList';

type TDiseaseSpectrumMapProps = {};

const DiseaseSpectrumMap: React.FC<TDiseaseSpectrumMapProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();
  // 当前点击选择的区域id
  const [listId, setListId] = useState<string>('');
  // 样本明细表格Drawer
  const [openSampleList, setOpenSampleList] = useState<boolean>(false);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '病原名称',
      dataIndex: 'sampleYear',
      valueType: 'select',
      fieldProps: {
        options: pathogenList,
        fieldNames: { label: 'etiologyName', value: 'id' },
        showSearch: true,
      },
    },
    {
      title: '病原分值',
      dataIndex: 'sampleRate',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenSampleList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
  ];

  /**
   * 获取病原列表
   */
  const getPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data?.rows?.length ? data?.rows : data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  // 关闭抽屉
  const close = () => setOpenSampleList(false);

  useEffect(() => {
    getPathogenListData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            // const { code, data, msg } = await getPathogenPointList(param);
            // if (code !== codeDefinition.QUERY_SUCCESS) {
            //   message.error(msg);
            // }
            // return {
            //   data: data?.rows ?? [],
            //   total: data?.total ?? 0,
            //   success: true,
            // };

            return {
              data: [
                {
                  id: 1,
                  sampleYear: 2024,
                  cityName: '某市',
                  areaName: '某县',
                  sampleNum: 20,
                  sampleRate: '20%',
                },
              ],
              total: 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<ExportOutlined />}
              onClick={() => {}}
              type="primary"
            >
              导出统计结果
            </Button>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openSampleList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default DiseaseSpectrumMap;
