/* eslint-disable @typescript-eslint/no-unused-vars */
// 病原耐药性加测记录
import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getPathogenList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import Detail from './details';

type TPathogenDrugResistanceTestRecordsProps = {};

const PathogenDrugResistanceTestRecords: React.FC<
  TPathogenDrugResistanceTestRecordsProps
> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();
  const [detailId, setDetailId] = useState<string>('');
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'planName',
    },
    {
      title: '样本名称',
      dataIndex: 'planName',
    },
    {
      title: '病原名称',
      dataIndex: 'planName',
    },
    {
      title: '药物名称',
      dataIndex: 'planName',
    },
    {
      title: '耐药时长(day)',
      dataIndex: 'planName',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => [
        <Button
          type="link"
          size="small"
          key="detail"
          onClick={() => {
            setDetailId(record?.id);
            setOpenDetail(true);
          }}
        >
          查看详情
        </Button>,
      ],
    },
  ];

  /**
   * 获取病原列表
   */
  const getPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data?.rows?.length ? data?.rows : data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getPathogenListData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            // const { code, data, msg } = await getPathogenPointList(param);
            // if (code !== codeDefinition.QUERY_SUCCESS) {
            //   message.error(msg);
            // }
            // return {
            //   data: data?.rows ?? [],
            //   total: data?.total ?? 0,
            //   success: true,
            // };

            return {
              data: [
                {
                  id: 1,
                  sampleYear: 2024,
                  cityName: '某市',
                  areaName: '某县',
                  sampleNum: 20,
                  sampleRate: '20%',
                },
              ],
              total: 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => []}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail closeDetail={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </div>
  );
};

export default PathogenDrugResistanceTestRecords;
