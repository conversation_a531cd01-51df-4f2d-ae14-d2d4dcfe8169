/* eslint-disable @typescript-eslint/no-unused-vars */
// 区域检出率分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  getRegionalDetectionRateEchartsData,
  getRegionalDetectionRatePage,
} from '@/api/businessIndicatorAnalysis';
import { getAllCityAreaList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { convertToCascading } from '@/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import DownloadButton from '@/components/DownloadButton';
import SampleList from './sampleList';

type TRegionalSamplingRateProps = {};

const RegionalDetectionRate: React.FC<TRegionalSamplingRateProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 市县区数据
  const [cityAreaList, setCityAreaList] = useState<any[]>([]);
  // 当前点击选择的区域id
  const [listId, setListId] = useState<string>('');
  // 样本明细表格Drawer
  const [openSampleList, setOpenSampleList] = useState<boolean>(false);
  // echarts数据 - 横坐标数据
  const [echartsXData, setEchartsXData] = useState<any[]>([]);
  // echarts数据 - 检验完成数
  const [echartsSeriesSampleList, setEchartsSeriesSampleList] = useState<any[]>(
    []
  );
  // echarts数据 - 检出阳性数
  const [echartsSeriesPositiveList, setEchartsSeriesPositiveList] = useState<
    any[]
  >([]);
  // echarts数据 - 阳性检出率
  const [echartsSeriesPositiveRateList, setEchartsSeriesPositiveRateList] =
    useState<any[]>([]);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'sampleYear',
    },
    {
      title: '区域',
      dataIndex: 'allName',
      valueType: 'cascader',
      fieldProps: {
        options: cityAreaList,
      },
      render: (_, record) => (
        <span>
          {record?.cityName}/{record?.areaName}
        </span>
      ),
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    {
      title: '检测完成数量',
      dataIndex: 'achievementNum',
      hideInSearch: true,
    },
    {
      title: '检出阳性数量',
      dataIndex: 'sunNum',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenSampleList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
    {
      title: '阳性检出率',
      dataIndex: 'sunRate',
      hideInSearch: true,
      render: (_, record) => <span>{record?.sunRate}%</span>,
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: ['检测完成数', '检出阳性数', '阳性检出率'],
    },
    xAxis: [
      {
        type: 'category',
        data: echartsXData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          interval: 0,
          rotate: 38,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '检测完成数',
        axisLabel: {},
      },
      {
        type: 'value',
        name: '阳性检出率',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '检测完成数',
        type: 'bar',
        tooltip: {},
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        data: echartsSeriesSampleList,
      },
      {
        name: '检出阳性数',
        type: 'bar',
        tooltip: {},
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        data: echartsSeriesPositiveList,
      },
      {
        name: '阳性检出率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesPositiveRateList,
      },
    ],
  };

  // 关闭抽屉
  const close = () => setOpenSampleList(false);

  /**
   * 获取贵州市所哟市县区域数据
   */
  const queryCityAreaList = async () => {
    try {
      const { code, data, msg } = await getAllCityAreaList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      // 将原始信息存入本地
      sessionStorage.setItem('cityAreaOriginList', JSON.stringify(data));
      // 转换
      const _finalDataList = convertToCascading(data);
      // 设置数据
      setCityAreaList(_finalDataList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取Echarts数据
   * @returns
   */
  const queryRegionalDetectionRateEchartsData = async () => {
    try {
      const { code, data, msg } = await getRegionalDetectionRateEchartsData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _xData: any = [];
      const _echartsSeriesSampleList: any = [];
      const _echartsSeriesPositiveList: any = [];
      const _echartsSeriesPositiveRateList: any = [];
      data?.forEach((_item: any) => {
        _xData.push(_item?.allName);
        _echartsSeriesSampleList.push(_item?.sampleNum);
        _echartsSeriesPositiveList.push(_item?.sunNum);
        _echartsSeriesPositiveRateList.push(_item?.sunRate);
      });
      setEchartsXData(_xData);
      setEchartsSeriesSampleList(_echartsSeriesSampleList);
      setEchartsSeriesPositiveList(_echartsSeriesPositiveList);
      setEchartsSeriesPositiveRateList(_echartsSeriesPositiveRateList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    queryCityAreaList();
    queryRegionalDetectionRateEchartsData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param: any = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            if (param?.allName?.length) {
              param['cityId'] = param?.allName[0];
              param['areaId'] = param?.allName[1];
              delete param?.allName;
            }

            const { code, data, msg } = await getRegionalDetectionRatePage(
              param
            );
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.records ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <DownloadButton
              method="get"
              url="/detection/rate/area/areaDataExport"
              // params={queryParamsCache}
            >
              导出统计结果
            </DownloadButton>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openSampleList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default RegionalDetectionRate;
