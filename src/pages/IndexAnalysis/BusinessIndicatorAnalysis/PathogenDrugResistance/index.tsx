/* eslint-disable @typescript-eslint/no-unused-vars */
// 病原耐药性分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getPathogenList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';

type TPathogenDrugResistanceProps = {};

const PathogenDrugResistance: React.FC<TPathogenDrugResistanceProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 病原列表
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '病原名称',
      dataIndex: 'sampleYear',
      valueType: 'select',
      fieldProps: {
        options: pathogenList,
        fieldNames: { label: 'etiologyName', value: 'id' },
        showSearch: true,
      },
    },
    {
      title: '药物名称',
      dataIndex: 'sampleYear',
      hideInSearch: true,
    },
    {
      title: '平均耐药时长(day)',
      dataIndex: 'sampleYear',
      hideInSearch: true,
    },
    {
      title: '最高耐药时间(day)',
      dataIndex: 'sampleYear',
      hideInSearch: true,
    },
    {
      title: '最低耐药时间(day)',
      dataIndex: 'sampleYear',
      hideInSearch: true,
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['平均耐药时长', '最高耐药时长', '最低耐药时长'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '药物1',
        '药物2',
        '药物3',
        '药物4',
        '药物5',
        '药物6',
        '药物7',
        '药物8',
        '药物9',
        '药物10',
        '药物11',
        '药物12',
      ],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '平均耐药时长',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210, 198, 350, 22, 78, 10],
      },
      {
        name: '最高耐药时长',
        type: 'line',
        data: [220, 182, 191, 234, 290, 330, 310, 225, 210, 200, 189, 110],
      },
      {
        name: '最低耐药时长',
        type: 'line',
        data: [150, 232, 201, 154, 190, 330, 410],
      },
    ],
  };

  /**
   * 获取病原列表
   */
  const getPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data?.rows?.length ? data?.rows : data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    getPathogenListData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            // const { code, data, msg } = await getPathogenPointList(param);
            // if (code !== codeDefinition.QUERY_SUCCESS) {
            //   message.error(msg);
            // }
            // return {
            //   data: data?.rows ?? [],
            //   total: data?.total ?? 0,
            //   success: true,
            // };

            return {
              data: [
                {
                  id: 1,
                  sampleYear: 2024,
                  cityName: '某市',
                  areaName: '某县',
                  sampleNum: 20,
                  sampleRate: '20%',
                },
              ],
              total: 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<ExportOutlined />}
              onClick={() => {}}
              type="primary"
            >
              导出统计结果
            </Button>,
          ]}
        />
      </BlockContainer>
    </div>
  );
};

export default PathogenDrugResistance;
