/*
 * @Date: 2024-11-22 10:22:18
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-11-27 15:24:47
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\BusinessIndicatorAnalysis\DrugResistanceAnalysis\index.tsx
 * @Description: 耐药性分析
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Card, DatePicker, Form, message, Select } from 'antd';
import {
  drugResistanceListApi,
  drugResistanceProfileListApi,
} from '@/api/businessIndicatorAnalysis';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import Chart from './components/Chart';
import DownloadButton from '@/components/DownloadButton';
import { profileColorOption, TDataSource, TProfileList } from './type';

const { RangePicker } = DatePicker;

const defaultSampleDate = [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()];

const DrugResistanceAnalysis: React.FC = () => {
  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  const [sampleDate, setSampleDate] = useState<any>(defaultSampleDate);
  const [curPathogen, setCurPathogen] = useState<string>();

  const actionRef = useRef<ActionType>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '药物名称',
      dataIndex: 'drugName',
      hideInSearch: true,
    },
    {
      title: '药物英文缩写',
      dataIndex: 'drugNameAbbr',
      hideInSearch: true,
    },
    {
      title: '耐药率',
      dataIndex: 'resistanceRate',
      hideInSearch: true,
    },
    {
      title: '中介率',
      dataIndex: 'intermediacyRate',
      hideInSearch: true,
    },
    {
      title: '敏感率',
      dataIndex: 'sensitizationRate',
      hideInSearch: true,
    },
    {
      title: '非敏感率',
      dataIndex: 'nonSensitizationRate',
      hideInSearch: true,
    },
  ];

  const [pathogenList, setPathogenList] = useState<
    {
      id: string;
      code: string;
      name: string;
    }[]
  >([]);
  const getPathogenList = async () => {
    try {
      const { code, data, msg } = await getPathogenNameList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setPathogenList(data);
        if (data && data.length) {
          setCurPathogen(data[0].name);
          handleSearch(data[0].name);
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getPathogenList();
  }, []);

  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<TDataSource[]>([]);
  const getTableData = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await drugResistanceListApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setDataSource(data);
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const [profileList, setProfileList] = useState<Record<string, any>[]>([]);

  const getColor = (val: string) => {
    if (val) {
      return profileColorOption.find((item) => item.label === val)?.color || '';
    }
    return '';
  };

  const getInfoColumns = useCallback((): ProColumns[] => {
    let _columns: ProColumns[] = [];
    if (profileList.length && profileList[0].columns.length) {
      _columns = profileList[0].columns.map(
        (item: TProfileList, index: number): ProColumns => ({
          title: item.drugName,
          children: [
            {
              title: item.drugEnName,
              // dataIndex: `pathogen${index}`,
              render: (_, record) => (
                <div
                  className={classNames(
                    'w-full h-full absolute top-0 left-0 px-2 flex items-center',
                  )}
                  style={{
                    backgroundColor: getColor(record[`pathogen${index}`])
                  }}
                >
                  {record[`pathogen${index}`]}
                </div>
              ),
            },
          ],
        })
      );
    }

    return [
      {
        title: '菌（毒）株编号',
        dataIndex: 'bacterialStrainNo',
        width: 200,
      },
      ..._columns,
    ];
  }, [profileList]);
  const getProfileList = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await drugResistanceProfileListApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        let _profileList: Record<string, any>[] = [];
        if (data) {
          for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
              const element = data[key];
              const _item: Record<string, any> = {
                bacterialStrainNo: key,
                columns: [],
              };
              if (element && element.length) {
                element.forEach((i: TProfileList, index: number) => {
                  _item[`pathogen${index}`] = i.result;
                });
                _item.columns = element;
              }
              _profileList.push(_item);
            }
          }
        }
        setProfileList(_profileList);
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (name?: string) => {
    if(!sampleDate || !sampleDate.length) {
      message.warning('请选择采样日期')
      return 
    }
    const pathogenName = name || curPathogen;
    if (!pathogenName) {
      message.warning('请选择病原体');
      return;
    }
    const _params: Record<string, any> = {
      pathogenName,
      startDate: dayjs(sampleDate[0]).format('YYYY-MM-DD'),
      endDate: dayjs(sampleDate[1]).format('YYYY-MM-DD')
    };
    // if (sampleDate && sampleDate.length) {
    //   _params.startDate = dayjs(sampleDate[0]).format('YYYY-MM-DD');
    //   _params.endDate = dayjs(sampleDate[1]).format('YYYY-MM-DD');
    // }
    setSearchParams(_params);
    getTableData(_params);
    getProfileList(_params);
  };

  const handleReset = () => {
    if (pathogenList.length) {
      setCurPathogen(pathogenList[0].name);
      setSampleDate(defaultSampleDate);
      const _params = {
        pathogenName: pathogenList[0].name,
        startDate: dayjs(defaultSampleDate[0]).format('YYYY-MM-DD'),
        endDate: dayjs(defaultSampleDate[1]).format('YYYY-MM-DD'),
      };
      setSearchParams(_params);
      getTableData(_params);
      getProfileList(_params);
    }
  };

  return (
    <div className=" flex flex-col gap-5">
      <Card title="数据筛选">
        <div className="w-full flex justify-between items-center">
          <Form submitter={false} autoComplete="off" layout="inline">
            <Form.Item label="采样日期">
              <RangePicker
                value={sampleDate}
                onChange={(val) => setSampleDate(val)}
              />
            </Form.Item>
            <Form.Item label="病原体">
              <Select
                className="!w-56"
                options={pathogenList}
                showSearch
                fieldNames={{
                  label: 'name',
                  value: 'name',
                }}
                placeholder="请选择"
                optionFilterProp="name"
                value={curPathogen}
                onChange={(val) => setCurPathogen(val)}
              ></Select>
            </Form.Item>
          </Form>

          <div className="flex items-center gap-6">
            <Button
              type="primary"
              onClick={() => handleSearch()}
              loading={loading}
            >
              统计
            </Button>
            <Button onClick={handleReset} loading={loading}>
              重置
            </Button>
            {/* <DownloadButton
              url="/data/index/etiology/info/antimicrobialResistanceExport"
              params={searchParams}
              method="get"
            >
              导出统计结果
            </DownloadButton> */}
          </div>
        </div>
      </Card>
      <Card title="分析结果">
        <Chart dataSource={dataSource} />
        <ProTable
          className=" mt-5"
          columns={columns}
          actionRef={actionRef}
          bordered
          headerTitle=""
          dataSource={dataSource}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="drugName"
          search={false}
          options={false}
          pagination={false}
          dateFormatter="string"
        />
      </Card>
      <Card title="耐药检测信息（耐药谱）">
        <div>注：S代表敏感、non-S代表非敏感、X代表耐药、I代表中介</div>
        <ProTable
          className=" mt-5"
          columns={getInfoColumns()}
          actionRef={actionRef}
          bordered
          headerTitle=""
          dataSource={profileList}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="bacterialStrainNo"
          search={false}
          options={false}
          pagination={false}
          dateFormatter="string"
        />
      </Card>
    </div>
  );
};

export default DrugResistanceAnalysis;
