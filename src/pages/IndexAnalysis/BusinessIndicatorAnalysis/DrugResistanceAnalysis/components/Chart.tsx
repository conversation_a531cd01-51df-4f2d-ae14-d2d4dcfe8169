import { useEffect, useState } from 'react';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';
import { TDataSource } from '../type';

type TChartOptions = {
  dataSource: TDataSource[];
};

const Chart: React.FC<TChartOptions> = ({ dataSource }) => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'category',
    },
    series: [
      {
        name: '耐药率',
        type: 'line',
        data: [],
      },
      {
        name: '中介率',
        type: 'line',
        data: [],
      },
      {
        name: '敏感率',
        type: 'line',
        data: [],
      },
      {
        name: '非敏感率',
        type: 'line',
        data: [],
      },
    ],
  });

  useEffect(() => {
    const _newOption: any = cloneDeep(option);
    _newOption.xAxis.data = dataSource.length
      ? dataSource.map((item) => item.drugName)
      : [];
    _newOption.series[0].data = dataSource.length
      ? dataSource.map((item) => item.resistanceRate)
      : [];
    _newOption.series[1].data = dataSource.length
      ? dataSource.map((item) => item.intermediacyRate)
      : [];
    _newOption.series[2].data = dataSource.length
      ? dataSource.map((item) => item.sensitizationRate)
      : [];
    _newOption.series[3].data = dataSource.length
      ? dataSource.map((item) => item.nonSensitizationRate)
      : [];
    setOption(_newOption);
  }, [dataSource]);

  return <ChartsWrapper option={option} height={300} />;
};

export default Chart;
