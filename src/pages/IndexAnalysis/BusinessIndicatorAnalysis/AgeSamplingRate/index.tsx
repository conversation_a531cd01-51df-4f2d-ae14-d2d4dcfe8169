/* eslint-disable @typescript-eslint/no-unused-vars */
// 年龄采样率分析
import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message } from 'antd';
import {
  getAgeSamplingRateEchartsData,
  getAgeSamplingRatePage,
} from '@/api/businessIndicatorAnalysis';
import { getSampleAgeList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import DownloadButton from '@/components/DownloadButton';
import SampleList from './sampleList';

type TRegionalSamplingRateProps = {};

const AgeSamplingRate: React.FC<TRegionalSamplingRateProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 市县区数据
  const [ageRangeList, setAgeRangeList] = useState<any[]>([]);
  // 当前点击选择的区域id
  const [listId, setListId] = useState<string>('');
  // 样本明细表格Drawer
  const [openSampleList, setOpenSampleList] = useState<boolean>(false);
  // echarts数据 - 横坐标数据
  const [echartsXData, setEchartsXData] = useState<any[]>([]);
  // echarts数据 - 样本采集数量集合
  const [echartsSeriesSampleList, setEchartsSeriesSampleList] = useState<any[]>(
    []
  );
  // echarts数据 - 采样率集合
  const [echartsSeriesSampleRateList, setEchartsSeriesSampleRateList] =
    useState<any[]>([]);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'sampleYear',
    },
    {
      title: '年龄',
      dataIndex: 'ageLevel',
      valueType: 'cascader',
      fieldProps: {
        options: ageRangeList,
        fieldNames: { label: 'detail', value: 'level' },
      },
      render: (_, record) => <span>{record?.ageName}</span>,
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenSampleList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
    {
      title: '采集样本占比',
      dataIndex: 'sampleRate',
      hideInSearch: true,
      render: (_, record) => <span>{record?.sampleRate}%</span>,
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: ['采集样本数量', '采样率占比'],
    },
    xAxis: [
      {
        type: 'category',
        data: echartsXData,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '采集样本数量',
        axisLabel: {},
      },
      {
        type: 'value',
        name: '采样率占比',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '采集样本数量',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesSampleList,
      },
      {
        name: '采样率占比',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesSampleRateList,
      },
    ],
  };

  // 关闭抽屉
  const close = () => setOpenSampleList(false);

  /**
   * 获取年龄段
   */
  const querySampleAgeList = async () => {
    try {
      const { code, data, msg } = await getSampleAgeList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取Echarts数据
   * @returns
   */
  const queryRegionalSamplingRateEchartsData = async () => {
    try {
      const { code, data, msg } = await getAgeSamplingRateEchartsData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _xData: any = [];
      const _echartsSeriesSampleList: any = [];
      const _echartsSeriesSampleRateList: any = [];
      data?.forEach((_item: any) => {
        _xData.push(_item?.ageName);
        _echartsSeriesSampleList.push(_item?.sampleNum);
        _echartsSeriesSampleRateList.push(_item?.sampleRate);
      });
      setEchartsXData(_xData);
      setEchartsSeriesSampleList(_echartsSeriesSampleList);
      setEchartsSeriesSampleRateList(_echartsSeriesSampleRateList);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    querySampleAgeList();
    queryRegionalSamplingRateEchartsData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            const { code, data, msg } = await getAgeSamplingRatePage(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.records ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <DownloadButton
              method="get"
              url="/sample/rate/age/sampleAreaDataExport"
              // params={queryParamsCache}
            >
              导出统计结果
            </DownloadButton>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openSampleList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default AgeSamplingRate;
