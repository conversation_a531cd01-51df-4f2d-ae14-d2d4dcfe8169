import { useEffect, useState } from 'react';
import { cloneDeep } from 'lodash';
import ChartsWrapper from '@/components/ChartsWrapper';
import { ECOption } from '@/hooks/useEcharts';

type TChartOptions = {
  xData: string[];
  series: Record<string, any>[];
};

const Chart: React.FC<TChartOptions> = ({ xData, series }) => {
  const [option, setOption] = useState<ECOption>({
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['平均耐药时长', '最高耐药时长', '最低耐药时长'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'category',
    },
    series: [],
  });

  useEffect(() => {
    const _newOptions: any = cloneDeep(option);

    _newOptions.xAxis.data = xData;
    _newOptions.series = series;
    setOption(_newOptions);
  }, [xData, series]);

  return <ChartsWrapper option={option} height={300} />;
};

export default Chart;
