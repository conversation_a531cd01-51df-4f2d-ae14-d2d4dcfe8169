/*
 * @Date: 2024-11-29 08:45:37
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-12-16 11:13:58
 * @FilePath: \xr-qc-jk-web\src\pages\IndexAnalysis\BusinessIndicatorAnalysis\NewDetectionRate\index.tsx
 * @Description: 检测率分析
 */
import React, { useCallback, useEffect, useState } from 'react';
import { Button, Card, DatePicker, Form, message, Select } from 'antd';
import {
  detectionRateCountApi,
  detectionRateListApi,
  samplingRateChartApi,
} from '@/api/businessIndicatorAnalysis';
import { getDict } from '@/api/dict';
import { getPathogenNameList } from '@/api/pathogenDictionary';
import { codeDefinition } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Chart from './components/Chart';
import DownloadButton from '@/components/DownloadButton';

const { RangePicker } = DatePicker;
// 统计维度
const defaultOptions = [
  { label: '区域', value: 'areaName', numValue: 0 },
  { label: '人群', value: 'job', numValue: 1 },
  { label: '年龄', value: 'age', numValue: 2 },
  { label: '月度', value: 'monthValue', numValue: 3 },
  { label: '季节', value: 'quarterValue', numValue: 4 },
];

const defaultSampleDate = [dayjs(`${new Date().getFullYear()}-01-01`), dayjs()];
const NewSamplingRate: React.FC = () => {
  const [searchParams, setSearchParams] = useState<Record<string, any>>();

  // 筛选条件
  const [etiology, setEtiology] = useState<any>();
  const [sampleDate, setSampleDate] = useState<any>(defaultSampleDate);
  // 统计维度
  const [type1, setType1] = useState<string>();
  const [type2, setType2] = useState<string>();
  const type1Options = useCallback(() => {
    return defaultOptions.filter((item) => item.value !== type2);
  }, [type2]);

  const type2Options = useCallback(() => {
    return defaultOptions.filter((item) => item.value !== type1);
  }, [type1]);

  // 病原体
  const [pathogenList, setPathogenList] = useState<
    {
      id: string;
      code: string;
      name: string;
    }[]
  >([]);
  const getPathogenList = async () => {
    try {
      const { code, data, msg } = await getPathogenNameList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setPathogenList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };
  // 人群列表
  const [jobList, setJobList] = useState<
    {
      dictLabel: string;
      dictValue: string;
    }[]
  >([]);
  const getJobList = async () => {
    try {
      const { code, data, msg } = await getDict('job_group');
      if (code === codeDefinition.QUERY_SUCCESS) {
        setJobList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getPathogenList();
    getJobList();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '区域',
      dataIndex: 'areaName',
      hideInSearch: true,
    },
    {
      title: '人群',
      dataIndex: 'job',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        options: jobList,
        fieldNames: {
          label: 'dictLabel',
          value: 'dictValue',
        },
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      hideInSearch: true,
    },
    {
      title: '月度',
      dataIndex: 'monthValue',
      hideInSearch: true,
    },
    {
      title: '季节',
      dataIndex: 'quarterValue',
      hideInSearch: true,
    },
  ];

  const defaultColumns: ProColumns[] = [
    {
      title: '检测样本数',
      dataIndex: 'detectSampleCount',
      hideInSearch: true,
    },
    {
      title: '检测率',
      dataIndex: 'detectSampleRate',
      hideInSearch: true,
    },
  ];

  const [tableColumns, setTableColumns] = useState<ProColumns[]>(
    () => defaultColumns
  );

  const [statisticsText, setStatisticsText] = useState('');

  const getStatisticsText = (count: number) => {
    const _curTypeArr = [type1, type2].filter((item) => item).length
      ? defaultOptions.filter((item) => [type1, type2].includes(item.value))
      : [];
    const _typeName = _curTypeArr.length
      ? _curTypeArr.map((item) => item.label).toString()
      : '';
    const _sampleDateName = `${dayjs(sampleDate[0]).format(
      'YYYY/MM/DD'
    )}-${dayjs(sampleDate[1]).format('YYYY/MM/DD')}`;
    return `病原体：${etiology?.label}，采样日期：${_sampleDateName}，统计维度：
              ${_typeName}
              ，合计检测样本数：${count}`;
  };

  const [loading, setLoading] = useState(false);
  // 合计监测样本数
  const getCount = async (params: Record<string, any>) => {
    try {
      const { code, data, msg } = await detectionRateCountApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setStatisticsText(getStatisticsText(data || 0));
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const [tableData, setTableData] = useState<Record<string, any>[]>([]);
  const [xData, setXData] = useState<string[]>([]);
  const [series, setSeries] = useState<Record<string, any>[]>([]);
  const getTableData = async (params: Record<string, any>) => {
    try {
      setLoading(true);
      const { code, data, msg } = await detectionRateListApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        setTableData(data);
        // if (!type2 && data && data.length) {
        //   // x轴
        //   const _xData = data.map((item: any) => {
        //     if (type1 === 'job') {
        //       return jobList.find((job) => job.dictValue === item[type1!])
        //         ?.dictLabel;
        //     } else {
        //       return item[type1!];
        //     }
        //   });
        //   setXData(_xData);
        //   // 系列
        //   setSeries([
        //     {
        //       name: '采集样本数',
        //       type: 'line',
        //       data: data.map((item: any) => item.gatherSampleCount),
        //     },
        //     {
        //       name: '采集样本率',
        //       type: 'line',
        //       data: data.map((item: any) => item.gatherSampleRate),
        //     },
        //   ]);
        // }
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getChartData = async (params: Record<string, any>) => {
    try {
      const { code, data, msg } = await samplingRateChartApi(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        console.log(data);

        if (!type2) {
        } else if (['quarterValue', 'monthValue'].includes(type2)) {
          // x轴
        } else {
          //
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const handleSearch = () => {
    if (!sampleDate || !sampleDate.length) {
      message.warning('请选择采样时间');
      return;
    }
    if (!etiology) {
      message.warning('请选择病原体');
      return;
    }
    if (!type1) {
      message.warning('请选择统计维度');
      return;
    }
    const _params: Record<string, any> = {
      etiologyId: etiology?.value,
      dimension1: defaultOptions.find((item) => item.value === type1)?.numValue,
      dimension2: defaultOptions.find((item) => item.value === type2)?.numValue,
      sampleStartDate: dayjs(sampleDate[0]).format('YYYY-MM-DD'),
      sampleEndDate: dayjs(sampleDate[1]).format('YYYY-MM-DD'),
    };

    const _columns = columns.filter((item) =>
      [type1, type2].includes(item.dataIndex as string)
    );
    setTableColumns([..._columns, ...defaultColumns]);
    setSearchParams(_params);
    getCount(_params);
    getTableData(_params);
    // if (type2) {
    //   getChartData(_params);
    // }
  };

  const handleReset = () => {
    setSampleDate(() => defaultSampleDate);
    setStatisticsText('');
    setTableColumns(() => defaultColumns);
    setEtiology(undefined);
    setType1('');
    setType2('');
    setTableData([]);
  };

  return (
    <div className="flex flex-col flex-nowrap gap-5">
      <Card title="筛选条件">
        <Form submitter={false} autoComplete="off" layout="inline">
          <Form.Item label="采样日期">
            <RangePicker
              value={sampleDate}
              onChange={(val) => setSampleDate(val)}
            />
          </Form.Item>
          <Form.Item label="病原体">
            <Select
              className="!w-56"
              options={pathogenList}
              showSearch
              fieldNames={{
                label: 'name',
                value: 'id',
              }}
              placeholder="请选择"
              optionFilterProp="name"
              labelInValue
              value={etiology}
              onChange={(val) => setEtiology(val)}
              allowClear
            ></Select>
          </Form.Item>
        </Form>
      </Card>
      <Card title="统计维度">
        <div className="flex gap-5 justify-between">
          <Form.Item label="统计维度">
            <Select
              className="!w-56"
              options={type1Options()}
              fieldNames={{
                label: 'label',
                value: 'value',
              }}
              placeholder="请选择"
              value={type1}
              onChange={(val) => {
                if (
                  ['monthValue', 'quarterValue'].includes(val) &&
                  ['monthValue', 'quarterValue'].includes(type2!)
                ) {
                  message.warning('季节和月度不能同时选择');
                  setType2(undefined);
                } else {
                  setType1(val);
                  if (!val) {
                    setType2(undefined);
                  }
                }
              }}
              allowClear
            ></Select>
            <Select
              className="!w-56"
              options={type2Options()}
              fieldNames={{
                label: 'label',
                value: 'value',
              }}
              disabled={!type1}
              placeholder="请选择"
              value={type2}
              onChange={(val) => {
                if (
                  ['monthValue', 'quarterValue'].includes(val) &&
                  ['monthValue', 'quarterValue'].includes(type1!)
                ) {
                  message.warning('季节和月度不能同时选择');
                  setType2(undefined);
                } else {
                  setType2(val);
                }
              }}
              allowClear
            ></Select>
          </Form.Item>
          <div className="flex gap-3">
            <Button type="primary" onClick={handleSearch} loading={loading}>
              统计
            </Button>
            <Button loading={loading} onClick={handleReset}>
              重置
            </Button>
            {/* <DownloadButton
              url="/data/spreadAnalyze/monitoring/export"
              params={searchParams}
              method="get"
            >
              导出统计结果
            </DownloadButton> */}
          </div>
        </div>
        <div className="text-sm text-red-500">
          注：一般以每年的3~5月为春季，6~8月为夏季，9~11月为秋季，12~2月为冬季
        </div>
      </Card>
      <Card title="统计结果">
        {tableData.length ? (
          <>
            <div className="text-sm text-gray-500 pl-5 py-3">
              {statisticsText}
            </div>

            {/* <Chart xData={xData} series={series} /> */}

            <ProTable
              loading={loading}
              columns={tableColumns}
              cardBordered
              bordered
              headerTitle=""
              dataSource={tableData}
              editable={{
                type: 'multiple',
              }}
              columnsState={{
                persistenceKey: 'pro-table-singe-demos',
                persistenceType: 'localStorage',
                defaultValue: {
                  option: { fixed: 'right', disable: true },
                },
              }}
              rowKey="id"
              search={false}
              options={{
                setting: {
                  listsHeight: 400,
                },
              }}
              pagination={false}
              dateFormatter="string"
            />
          </>
        ) : null}
      </Card>
    </div>
  );
};

export default NewSamplingRate;
