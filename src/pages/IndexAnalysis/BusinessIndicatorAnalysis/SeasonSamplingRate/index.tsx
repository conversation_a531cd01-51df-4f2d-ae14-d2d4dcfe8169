/* eslint-disable @typescript-eslint/no-unused-vars */
// 季节采样率分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  getSeasonSamplingRateEchartsData,
  getSeasonSamplingRatePage,
} from '@/api/businessIndicatorAnalysis';
import { queryDictValueList } from '@/api/settings';
import { codeDefinition } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import ChartsWrapper from '@/components/ChartsWrapper';
import DownloadButton from '@/components/DownloadButton';
import SampleList from './sampleList';

type TRegionalSamplingRateProps = {};

const SeasonSamplingRate: React.FC<TRegionalSamplingRateProps> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  // 市县区数据
  const [seasonList, setSeasonList] = useState<any[]>([]);
  // 当前点击选择的区域id
  const [listId, setListId] = useState<string>('');
  // 样本明细表格Drawer
  const [openSampleList, setOpenSampleList] = useState<boolean>(false);
  // echarts数据 - 横坐标数据
  const [echartsXData, setEchartsXData] = useState<any[]>([]);
  // echarts数据 - 春季样本采集数量集合
  const [echartsSeriesSpringList, setEchartsSeriesSpringList] = useState<any[]>(
    []
  );
  // echarts数据 - 夏季样本采集数量集合
  const [echartsSeriesSummerList, setEchartsSeriesSummerList] = useState<any[]>(
    []
  );
  // echarts数据 - 秋季样本采集数量集合
  const [echartsSeriesAutumnList, setEchartsSeriesAutumnList] = useState<any[]>(
    []
  );
  // echarts数据 - 冬季样本采集数量集合
  const [echartsSeriesWinterList, setEchartsSeriesWinterList] = useState<any[]>(
    []
  );
  // echarts数据 - 春季样本采集占比
  const [echartsSeriesSpringRate, setEchartsSeriesSpringRate] = useState<any[]>(
    []
  );
  // echarts数据 - 夏季样本采集占比
  const [echartsSeriesSummerRate, setEchartsSeriesSummerRate] = useState<any[]>(
    []
  );
  // echarts数据 - 秋季样本采集占比
  const [echartsSeriesAutumnRate, setEchartsSeriesAutumnRate] = useState<any[]>(
    []
  );
  // echarts数据 - 冬季样本采集占比
  const [echartsSeriesWinterRate, setEchartsSeriesWinterRate] = useState<any[]>(
    []
  );

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'sampleYear',
    },
    {
      title: '季节',
      dataIndex: 'seasonLevel',
      valueType: 'select',
      fieldProps: {
        options: seasonList,
        fieldNames: { label: 'dictLabel', value: 'dictValue' },
      },
      render: (_, record) => <span>{record?.seasonName}</span>,
    },
    {
      title: '采集样本数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
      render: (_, record) => (
        <Button
          type="text"
          onClick={() => {
            setListId(record?.id);
            setOpenSampleList(true);
          }}
        >
          <span className="text-blue-600">{record?.sampleNum}</span>
        </Button>
      ),
    },
    {
      title: '采集样本占比',
      dataIndex: 'sampleRate',
      hideInSearch: true,
      render: (_, record) => <span>{record?.sampleRate}%</span>,
    },
  ];

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: [
        '春季',
        '夏季',
        '秋季',
        '冬季',
        '春季采样率',
        '夏季采样率',
        '秋季采样率',
        '冬季采样率',
      ],
    },
    xAxis: [
      {
        type: 'category',
        data: echartsXData,
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '采集样本数量',
        axisLabel: {},
      },
      {
        type: 'value',
        name: '采样率占比',
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '春季',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesSpringList,
      },
      {
        name: '夏季',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesSummerRate,
      },
      {
        name: '秋季',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesAutumnList,
      },
      {
        name: '冬季',
        type: 'bar',
        tooltip: {},
        data: echartsSeriesWinterList,
      },
      {
        name: '春季采样率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesSpringRate,
      },
      {
        name: '夏季采样率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesSpringRate,
      },
      {
        name: '秋季采样率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesAutumnRate,
      },
      {
        name: '冬季采样率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {},
        data: echartsSeriesWinterRate,
      },
    ],
  };

  // 关闭抽屉
  const close = () => setOpenSampleList(false);

  /**
   * 获取人群分类
   */
  const querySeasonList = async () => {
    try {
      const { code, rows, msg } = await queryDictValueList({
        dictType: 'season_type',
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setSeasonList(rows);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取Echarts数据
   * @returns
   */
  const queryReasonSamplingRateEchartsData = async () => {
    try {
      const { code, data, msg } = await getSeasonSamplingRateEchartsData({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      const _xData: any = [];
      // Spring, Summer, Autumn, Winter
      const _springList: any = [];
      const _summerList: any = [];
      const _autumnList: any = [];
      const _winterList: any = [];

      const _springRate: any = [];
      const _summerRate: any = [];
      const _autumnRate: any = [];
      const _winterRate: any = [];
      data?.forEach((_item: any) => {
        _xData.push(_item?.sampleYear);
        _springList.push(_item?.springNum);
        _summerList.push(_item?.summerNum);
        _autumnList.push(_item?.autumnNum);
        _winterList.push(_item?.winterNum);

        _springRate.push(_item?.springRate);
        _summerRate.push(_item?.summerRate);
        _autumnList.push(_item?.autumnRate);
        _winterList.push(_item?.winterRate);
      });
      setEchartsXData(_xData);
      setEchartsSeriesSpringList(_springList);
      setEchartsSeriesSummerList(_summerList);
      setEchartsSeriesAutumnList(_autumnList);
      setEchartsSeriesWinterList(_winterList);

      setEchartsSeriesSpringRate(_springRate);
      setEchartsSeriesSummerRate(_summerRate);
      setEchartsSeriesAutumnRate(_autumnRate);
      setEchartsSeriesWinterRate(_winterRate);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    querySeasonList();
    queryReasonSamplingRateEchartsData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ChartsWrapper option={option as any} width="80vw" height={300} />
      </BlockContainer>
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            const { code, data, msg } = await getSeasonSamplingRatePage(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            return {
              data: data?.records ?? [],
              total: data?.total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <DownloadButton
              method="get"
              url="/sample/rate/season/sampleSeasonDataExport"
              // params={queryParamsCache}
            >
              导出统计结果
            </DownloadButton>,
          ]}
        />
      </BlockContainer>
      <Drawer
        width="60%"
        title="样本明细"
        onClose={close}
        open={openSampleList}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <SampleList close={close} listId={listId} />
      </Drawer>
    </div>
  );
};

export default SeasonSamplingRate;
