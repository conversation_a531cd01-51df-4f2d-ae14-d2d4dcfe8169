/* eslint-disable @typescript-eslint/no-unused-vars */
// 病原复合感染年龄分析
import React, { useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getPathogenList, getSampleAgeList } from '@/api/pathogen';
import { codeDefinition } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';

type TPathogenComplexInfectionAgeProps = {};

const PathogenComplexInfectionAge: React.FC<
  TPathogenComplexInfectionAgeProps
> = () => {
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState<number>(10);
  const [pathogenList, setPathogenList] = useState<Record<string, any>[]>();
  const [ageRangeList, setAgeRangeList] = useState<any[]>([]);

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年龄',
      dataIndex: 'taskType',
      valueType: 'select',
      fieldProps: {
        options: ageRangeList,
        fieldNames: { label: 'detail', value: 'id' },
      },
    },
    {
      title: '病原名称',
      dataIndex: 'sampleYear',
      valueType: 'select',
      fieldProps: {
        options: pathogenList,
        fieldNames: { label: 'etiologyName', value: 'id' },
        showSearch: true,
      },
    },
    {
      title: '复合感染病原A数量',
      dataIndex: 'sampleRate',
      hideInSearch: true,
    },
    {
      title: '复合感染病原B数量',
      dataIndex: 'sampleRate',
      hideInSearch: true,
    },
    {
      title: '复合感染病原C数量',
      dataIndex: 'sampleRate',
      hideInSearch: true,
    },
  ];

  /**
   * 获取年龄段
   */
  const querySampleAgeList = async () => {
    try {
      const { code, data, msg } = await getSampleAgeList({});
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setAgeRangeList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  /**
   * 获取病原列表
   */
  const getPathogenListData = async () => {
    try {
      const { code, data, msg } = await getPathogenList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setPathogenList(data?.rows?.length ? data?.rows : data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
    }
  };

  useEffect(() => {
    querySampleAgeList();
    getPathogenListData();
  }, []);

  return (
    <div className="flex flex-col flex-nowrap gap-4">
      <BlockContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;

            // const { code, data, msg } = await getPathogenPointList(param);
            // if (code !== codeDefinition.QUERY_SUCCESS) {
            //   message.error(msg);
            // }
            // return {
            //   data: data?.rows ?? [],
            //   total: data?.total ?? 0,
            //   success: true,
            // };

            return {
              data: [
                {
                  id: 1,
                  sampleYear: 2024,
                  cityName: '某市',
                  areaName: '某县',
                  sampleNum: 20,
                  sampleRate: '20%',
                },
              ],
              total: 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<ExportOutlined />}
              onClick={() => {}}
              type="primary"
            >
              导出统计结果
            </Button>,
          ]}
        />
      </BlockContainer>
    </div>
  );
};

export default PathogenComplexInfectionAge;
