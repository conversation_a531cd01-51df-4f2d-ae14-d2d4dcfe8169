/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Tag, Tooltip } from 'antd';
import { BackFillTask, queryFillTaskList, unlockFillTask } from '@/api/fill';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from '../Judge/components/Edit';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { waitTime, waitTimePromise } from '@/utils/helpers';

export const statusMap = (status: string) => {
  let content = '';
  switch (status) {
    case '待评判':
      content = '待检验';
      break;
    case '待下发':
      content = '待评判';
      break;
    case '已下发':
      content = '待下发';
      break;
    default:
      content = '上一级';
      break;
  }
  return content;
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const [queryParamsCache, setQueryParamsCache] = useState<any>();
  const actionRef = useRef<ActionType>();

  const [activeKey, setActiveKey] = useState<string>('0');

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailInfo, setDetailInfo] = useState<any>({});
  const [detailReadOnly, setDetailReadOnly] = useState(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
    },
    {
      title: '评判日期',
      dataIndex: 'judgeDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '样品总得分',
      dataIndex: 'sampleScore',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <span>
            {row.stuffScore && row.score
              ? (parseFloat(row.score) - parseFloat(row.stuffScore)).toFixed(1)
              : '-'}
          </span>
        );
      },
    },
    {
      title: '其它（资料）总得分',
      dataIndex: 'stuffScore',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '最终得分',
      dataIndex: 'score',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '考核结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <>
            {row.result === '优秀' && (
              <Tag key="t1" color="success">
                {text}
              </Tag>
            )}
            {row.result === '合格' && (
              <Tag key="t2" color="processing">
                {text}
              </Tag>
            )}
            {row.result === '不合格' && (
              <Tag key="t3" color="error">
                {text}
              </Tag>
            )}
          </>
        );
      },
    },
    {
      title: (
        <Tooltip title="状态流程：待接收>待检验>待评判>待下发" color="blue">
          <span className="mr-1">状态</span>
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'statusStr',
      hideInSearch: true,
      align: 'center',
    },
    {
      // hideInTable: activeKey === '2',
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      render: (text, record, _, action) => {
        let btns: any = [];
        btns = [
          <>
            <Button
              type="link"
              size="small"
              key="view1"
              onClick={() => {
                setDetailInfo(record);
                setDetailReadOnly(true);
                setOpenEdit(true);
              }}
            >
              查看详情
            </Button>
            {(record.isTime !== null || record.isTime === 1) &&
              parseInt(record.status) < 2 && (
                <Popconfirm
                  title={`解锁时间限制？`}
                  onConfirm={() => handleUnlock(record.id)}
                  okText="确定"
                  cancelText="取消"
                  key="unlock"
                >
                  <Button type="link" size="small" key="unlockBtn">
                    解锁时间限制
                  </Button>
                </Popconfirm>
              )}
            {record.statusStr !== '待接收' && (
              <Popconfirm
                title={`退回任务到${statusMap(record.statusStr)}？`}
                onConfirm={() => handleBack(record.id)}
                okText="确定"
                cancelText="取消"
                key="back2"
              >
                <Button type="link" size="small" key="backBtn" danger>
                  退回
                </Button>
              </Popconfirm>
            )}
          </>,
        ];
        return btns;
      },
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };
  /**
   * @TODO 退回任务
   */
  const handleBack = async (id: string) => {
    try {
      const params = { id };
      const { code, data, msg }: any = await BackFillTask(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 解锁时间限制
   */
  const handleUnlock = async (id: string) => {
    try {
      const { code, data, msg }: any = await unlockFillTask(id);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  const [pageSize, setPageSize] = useState<number>(10);
  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { code, rows, total, msg } = await queryFillTaskList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          setQueryParamsCache({
            ...param,
          });
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange(current, size) {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="任务列表"
      />
      <Drawer
        width="85%"
        title="详情"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#eee] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailInfo={detailInfo}
          showType="详情"
          readOnly={true}
        />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
