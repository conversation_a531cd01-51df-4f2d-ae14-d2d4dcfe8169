/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Collapse, message, Spin } from 'antd';
import { editDetail, getDetail, judge } from '@/api/judge';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import BaseForm from '../../Task/components/BaseForm';
import BlockContainer from '@/components/BlockContainer';
import JsonForm from '@/components/JsonForm';
import JsonTable from '@/components/JsonTable';
import './Edit.less';
import JudgeForm from './JudgeForm';
import JudgeItemBlock from './JudgeItemBlock';

type TEditProps = {
  close: () => void;
  detailInfo?: any;
  showType: any;
  readOnly?: boolean;
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailInfo,
  showType,
  readOnly = false,
}) => {
  const formRef1 = useRef<any>(null);
  // 这里通过 useRef 创建一个数组
  const listRefs = useRef<Array<any | null>>([]);

  const [form1Data, setForm1Data] = useState([]);

  const [form2List, setForm2List] = useState([]);

  const { token } = useTokenStore();
  const [loading, setLoading] = useState<boolean>(false);

  // 保存按钮
  const [loadingSave, setLoadingSave] = useState<boolean>(false);
  // 提交按钮
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);

  const [baseInfo, setBaseInfo] = useState<any>({});

  // 整个数据对象
  const [allData, setAllData] = useState<any>({});

  const init = async () => {
    if (detailInfo && detailInfo.id) {
      setLoading(true);
      try {
        const { code, data, msg } = await getDetail(detailInfo.id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          setAllData(data);
          const { valueOne, valueTwo } = data;
          setBaseInfo(data);
          setForm1Data(valueOne);
          setForm2List(valueTwo);
        } else {
          message.error(msg);
        }
      } catch (error) {
        console.error(error);
        throw new Error(`Error: ${error}`);
      }
      setLoading(false);
    }
  };
  useEffect(() => {
    init();
  }, [detailInfo]);

  // 提交
  const handleSubmit = async () => {
    try {
      await judgeFormRef.current?.handleSubmit();
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  // 保存
  const handleSave = async () => {
    judgeFormRef.current?.handleSave();
  };

  const judgeFormRef = useRef<any>(null);

  // 提交
  const handleSaveSubmit = async (params: any) => {
    try {
      setLoadingSubmit(true);
      const realParams = {
        ...allData,
        main: {
          ...allData.main,
          ...params,
        },
      };
      const { code, data, msg }: any = await editDetail(4, realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoadingSubmit(false);
    }
  };

  // 保存评判
  const handleSaveForm = async (params: any) => {
    try {
      setLoadingSave(true);
      const realParams = {
        ...allData,
        main: {
          ...allData.main,
          ...params,
        },
      };
      const { code, data, msg }: any = await editDetail(3, realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        // close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoadingSave(false);
    }
  };

  // 单条人工评判 todo
  const [judging, setJudging] = useState<any>(false);

  const judgeItem = async (type: boolean | null, id: any) => {
    try {
      setJudging(true);
      const realParams = {
        id,
        result: type,
      };
      const { code, data, msg }: any = await judge(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        init();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setJudging(false);
    }
  };

  // 判断是否有未打分的项
  const hasUnJudge = (info: any) => {
    const hasNull = info.score
      ? info.score.find((item: any) => item.systemResult !=='正确' && (item.peopleResult === null ||  item.peopleResult === ''))
      : false;
    return !!hasNull;
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        {(judging || loading) && (
          <div className="absolute z-10 w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.0.1)]">
            <Spin tip="加载中..." size="large"></Spin>
          </div>
        )}
        {/* 基本信息 */}
        <BaseForm
          readonly={true}
          id={baseInfo.taskMain?.id}
          detailInfo={baseInfo.taskMain}
        />
        <BlockContainer title="检验基本信息" className="mt-4">
          <JsonForm
            formDataList={form1Data}
            onRef={formRef1}
            fileCheckTooltipTitle="检验基本信息"
            readOnly={true}
          />
        </BlockContainer>
        <BlockContainer className="mt-4" title="样本检验信息">
          {form2List?.map((item: any, i: any) => {
            return (
              <div
                key={'t' + i}
                className={`mb-3 ${hasUnJudge(item) ? 'item-red' : null}`}
              >
                <Collapse
                  size="small"
                  expandIconPosition="end"
                  defaultActiveKey={[]}
                  items={[
                    {
                      key: i,
                      label: (
                        <span className="text-[15px] font-semibold">
                          样本编号：{item.sampleCode}
                        </span>
                      ),
                      forceRender: true,
                      //  动态表单
                      children: (
                        <>
                          <JsonTable
                            formDataList={baseInfo?.itemInfos}
                            formRef={
                              ((ref: any): void =>
                                (listRefs.current[i] = ref)) as any
                            }
                            readOnly={!item.canUpdate || readOnly}
                            projectDataList={item.items}
                          />
                          {!readOnly && (
                            <JudgeItemBlock
                              list={item.score || []}
                              submit={(type: boolean | null, id: any) => {
                                judgeItem(type, id);
                              }}
                            ></JudgeItemBlock>
                          )}
                        </>
                      ),
                    },
                  ]}
                />
              </div>
            );
          })}
        </BlockContainer>
        {allData?.main?.status !== '1' && (
          <div className="mt-4">
            <BlockContainer title="结果评判">
              <JudgeForm
                onSubmit={(val) => {
                  handleSaveSubmit(val);
                }}
                onSave={(val) => {
                  handleSaveForm(val);
                }}
                showType={showType}
                id={detailInfo?.id}
                allData={allData}
                onRef={judgeFormRef}
                readonly={false}
              />
            </BlockContainer>
          </div>
        )}
      </div>
      {!readOnly ? (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleSave();
            }}
            loading={loadingSave}
          >
            保存
          </Button>
          <Button
            onClick={() => {
              handleSubmit();
            }}
            type="primary"
            loading={loadingSubmit}
          >
            提交评判信息
          </Button>
        </div>
      ) : (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={close} loading={loadingSave}>
            关闭
          </Button>
        </div>
      )}
    </div>
  );
};

export default MenuDetail;
