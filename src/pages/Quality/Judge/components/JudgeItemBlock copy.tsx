/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Popconfirm, Space, Table } from 'antd';
import type { TableProps } from 'antd';

type TEditProps = {
  list: any[];
  submit: (type: boolean, id: any) => void;
};
type DataSourceType = any;

const JudgeItemBlock: React.FC<TEditProps> = ({ list, submit }) => {
  const [tableData, setTableData] = useState<any>([]);
  const confirm = (type: boolean, record: any) => {
    submit(type, record.id);
  };
  const [columns, setColumns] = useState<TableProps<DataSourceType>['columns']>(
    [
      {
        title: '参考结果',
        dataIndex: 'sampleValue',
        key: 'sampleValue',
      },
      {
        title: '考核项目',
        dataIndex: 'itemName',
        key: 'itemName',
      },
      {
        title: '判定标准',
        dataIndex: 'conditionThree', // todo
        key: 'conditionThree',
        render: (text: any, record: any) => {
          return (
            <span>
              {record.conditionThree ? record.conditionThree : '无判定标准'}
            </span>
          );
        },
      },
      {
        title: '系统初判结果',
        dataIndex: 'systemResult',
        key: 'systemResult',
      },
      {
        title: '人工复判',
        dataIndex: 'peopleResult',
        key: 'peopleResult',
        render(_, record) {
          return (
            <Space>
              <Popconfirm
                key={'1'}
                title="提示"
                description="确认复判结果为正确?"
                onConfirm={() => {
                  confirm(true, record);
                }}
                okText="确认"
                cancelText="取消"
              >
                <Button key={'11'} type="primary" size="small">
                  正确
                </Button>
              </Popconfirm>
              <Popconfirm
                key={'2'}
                title="提示"
                description="确认复判结果为错误?"
                onConfirm={() => {
                  confirm(false, record);
                }}
                okText="确认"
                cancelText="取消"
              >
                <Button key={'22'} type="primary" size="small" danger>
                  错误
                </Button>
              </Popconfirm>
            </Space>
          );
        },
      },
      {
        title: '得分',
        dataIndex: 'score',
        key: 'score',
      },
    ]
  );

  const init = () => {
    columns![0].onCell = (_, index) => ({
      rowSpan: index! % list.length ? 0 : list.length,
    });
    setColumns(columns);
    list && setTableData(list || []);
  };
  useEffect(() => {
    list && list.length && init();
  }, [list]);

  return (
    <>
      <Table
        key="id"
        size="small"
        className="border-solid border-[2px] rounded-[5px] border-[#D3D3D3] p-2"
        rowKey={'id'}
        columns={columns}
        dataSource={tableData}
        pagination={false}
      />
    </>
  );
};

export default JudgeItemBlock;
