import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Popconfirm, Row, Space } from 'antd';

type TEditProps = {
  list: any[];
  submit: (type: boolean | null, id: any) => void;
};

const JudgeItemBlock: React.FC<TEditProps> = ({ list, submit }) => {
  const confirm = (type: boolean | null, record: any) => {
    submit(type, record.id);
  };

  return (
    <div>
      <div className="text-[15px]">
        样本参考结果：{list && list.length ? list[0].sampleValue : ''}
      </div>
      <div className="text-[15px]">
        样本真实编号：{list && list.length ? list[0].realCode : ''}
      </div>
      <Row className="mt-4" gutter={15}>
        {list.map((item: any) => (
          <Col xs={24} sm={24} md={12} lg={12} xl={8} key={item.id}>
            <div
              className="flex flex-col gap-2 rounded p-3"
              style={{ boxShadow: '0 0 5px rgba(0,0,0,0.2)' }}
            >
              <div className="flex">
                <div className="font-bold">考核项目：</div>
                <div>{item.itemName}</div>
              </div>
              <div className="flex">
                <div className="font-bold">判定标准：</div>
                <div>
                  <div>正确：{item.conditionThree ? item.conditionThree : '无判定标准'}</div>
                  <div>可疑标准：{item.conditionSix ? item.conditionSix : '无判定标准'}</div>
                  <div>错误：{item.conditionNine ? item.conditionNine : '无判定标准'}</div>
                </div>
              </div>
              <div className="flex">
                <div className="font-bold">系统初判结果：</div>
                <div>{item.systemResult}</div>
              </div>
              <div className="flex">
                <div className="font-bold">人工复判：</div>
                <div>
                  <Space>
                    <Popconfirm
                      key={'1'}
                      title="提示"
                      description="确认复判结果为正确?"
                      onConfirm={() => {
                        confirm(true, item);
                      }}
                      okText="确认"
                      cancelText="取消"
                    >
                      <Button ghost key={'11'} type="primary" size="small">
                        正确（合格）
                      </Button>
                    </Popconfirm>
                    <Popconfirm
                      key={'2'}
                      title="提示"
                      description="确认复判结果为错误?"
                      onConfirm={() => {
                        confirm(false, item);
                      }}
                      okText="确认"
                      cancelText="取消"
                    >
                      <Button key={'22'} size="small" danger>
                        错误（不合格）
                      </Button>
                    </Popconfirm>
                    <Popconfirm
                      key={'3'}
                      title="提示"
                      description="确认复判结果为可疑?"
                      onConfirm={() => {
                        confirm(null, item);
                      }}
                      okText="确认"
                      cancelText="取消"
                    >
                      <Button
                        key={'33'}
                        size="small"
                        color="orange"
                        variant="outlined"
                      >
                        可疑
                      </Button>
                    </Popconfirm>
                  </Space>
                </div>
              </div>
              <div className="flex">
                <div className="font-bold">得分：</div>
                <div>{item.score}</div>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default JudgeItemBlock;
