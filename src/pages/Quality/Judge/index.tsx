/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Table } from 'antd';
import { batchJudge, getFillList } from '@/api/fill';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const [queryParamsCache, setQueryParamsCache] = useState<any>();

  const [queryParamsCache1, setQueryParamsCache1] = useState<any>();

  const [queryParamsCache2, setQueryParamsCache2] = useState<any>();

  const actionRef = useRef<ActionType>();

  const [activeKey, setActiveKey] = useState<string>('2');

  const [pageSize, setPageSize] = useState<number>(10);

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailInfo, setDetailInfo] = useState<any>({});
  const [detailReadOnly, setDetailReadOnly] = useState(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
    },
    // {
    //   title: '得分',
    //   dataIndex: 'score',
    //   hideInSearch: true,
    //   hideInTable: activeKey === '4',
    // },
    // {
    //   title: '考核结果',
    //   dataIndex: 'result',
    //   hideInSearch: true,
    //   hideInTable: activeKey === '4',
    // },
    {
      title: '评判日期',
      dataIndex: 'judgeDate',
      hideInSearch: true,
      hideInTable: activeKey === '2',
    },
    {
      title: '评判人',
      dataIndex: 'judge',
      hideInSearch: true,
      hideInTable: activeKey === '2',
    },
    {
      title: '任务结束日期',
      dataIndex: 'endDate',
      hideInSearch: true,
      hideInTable: activeKey !== '2',
    },
    {
      title: '检验结果提交日期',
      dataIndex: 'fillingDate',
      hideInSearch: true,
      hideInTable: activeKey !== '2',
    },
    {
      title: '是否需要人工复判',
      dataIndex: 'needPeopleJudge',
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '是', value: '是' },
          { label: '否', value: '否' },
        ],
      },
      hideInTable: activeKey !== '2',
      hideInSearch: activeKey !== '2',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      render: (text, record, _, action) => {
        let btns: any = [];
        if (activeKey === '2') {
          btns = [
            <Button
              type="link"
              size="small"
              key="view"
              onClick={() => {
                setDetailInfo(record);
                setDetailReadOnly(false);
                setOpenEdit(true);
              }}
              disabled={record.needPeopleJudge === '否'}
            >
              评判
            </Button>,
          ];
        } else if (activeKey === '4') {
          btns = [
            <Button
              type="link"
              size="small"
              key="view"
              onClick={() => {
                setDetailInfo(record);
                setDetailReadOnly(true);
                setOpenEdit(true);
              }}
            >
              详情
            </Button>,
          ];
        }
        return btns;
      },
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  // 多选
  const [newSelectedRowKey, setNewSelectedRowKeys] = useState<any>([]);
  // 选择表头多选框
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setNewSelectedRowKeys(newSelectedRowKeys);
  };

  // 批量提交
  const [batchJudgeLoading, setBatchJudgeLoading] = useState<any>(false);
  const batchJudgeHandle = async () => {
    setBatchJudgeLoading(true);
    try {
      const { code, msg } = await batchJudge(newSelectedRowKey);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
      } else {
        tableReload();
      }
    } catch (error) {
    } finally {
      setBatchJudgeLoading(false);
    }
  };

  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '2': '-',
    '4': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        rowSelection={
          activeKey === '2'
            ? {
                // 注释该行则默认不显示下拉选项
                selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [],
                onChange: onSelectChange,
                // 当needPeopleJudge为'是'时禁用勾选框
                getCheckboxProps: (record) => ({
                  disabled: record.needPeopleJudge === '是',
                }),
              }
            : undefined
        }
        toolBarRender={() => [
          <Popconfirm
            title="确认提交勾选的任务？"
            onConfirm={() => batchJudgeHandle()}
            okText="确定"
            cancelText="取消"
            key="judge"
          >
            <Button
              key="buttonAll"
              className={activeKey === '2' ? 'btn-show' : 'btn-hide'}
              loading={batchJudgeLoading}
              disabled={!newSelectedRowKey.length}
              type="primary"
            >
              批量提交
            </Button>
          </Popconfirm>,
          <span className={activeKey === '2' ? 'block' : 'hidden'}>
            <DownloadButton
              url="/qa/fillingTasks/exportDetails"
              params={queryParamsCache}
            >
              导出查询明细
            </DownloadButton>
            <DownloadButton
              url="/qa/fillingTasks/exportDetails"
              params={queryParamsCache1}
            >
              导出数据统计
            </DownloadButton>
            <DownloadButton
              url="/qa/fillingTasks/exportDetailsClasser"
              params={queryParamsCache2}
            >
              导出合格情况
            </DownloadButton>
          </span>,
        ]}
        columns={columns}
        actionRef={actionRef}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '2',
                label: <span>待评判（{counts['2']}）</span>,
              },
              {
                key: '4',
                label: <span>已评判（{counts['4']}）</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          setNewSelectedRowKeys([]);
          const param = {
            ...params,
            status: activeKey,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;

          setQueryParamsCache({
            ...param,
          });

          setQueryParamsCache1({
            ...param,
            exportType: 1,
          });

          setQueryParamsCache2({
            ...param,
            exportType: 2,
          });

          const { code, rows, total, msg } = await getFillList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
          const paramsStr = JSON.stringify({
            ...params,
            current: undefined,
            pageSize: undefined,
          });
          if (paramsCache !== paramsStr) {
            setParamsCache(paramsStr);

            // 总是获取另一个状态的总数（同时保留当前状态总数）
            const { total: totalAnother } = await getFillList({
              ...params,
              status: activeKey === '2' ? '4' : '2',
              pageNum: 1,
              pageSize: 0,
              current: undefined,
            });
            setCounts({
              [activeKey]: total ?? 0,
              [activeKey === '2' ? '4' : '2']: totalAnother ?? 0,
            });
          }

          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="检验管理"
      />
      {/* 详情 */}
      <Drawer
        width="85%"
        title="评判"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#eee] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailInfo={detailInfo}
          showType={activeKey === '2' ? '填报' : '详情'}
          readOnly={detailReadOnly}
        />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
