/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Date: 2024-07-11 15:30:24
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-11 14:33:04
 * @FilePath: /xr-qc-jk-web/src/pages/Quality/StatisticalAnalysis/MechanismAnalysis/index.tsx
 * @Description: 统计分析 - 机构分析
 */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { cityListApi } from '@/api/common';
import { orgStatisticApi, TOrgStatisticParams } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { TCityList } from '@/pages/EpidemicTracking/type';

const MechanismAnalysis: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const actionRef = useRef<ActionType>();
  const [exportParams, setExportParams] = useState<{
    labName?: string;
    taskYear?: string;
    cityId?: number;
  }>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const [cityList, setCityList] = useState<TCityList[]>([]);
  const getCityList = async () => {
    try {
      const { code, data, msg } = await cityListApi();
      if (code === 200) {
        setCityList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCityList();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '机构名称',
      dataIndex: 'labName',
    },
    {
      title: '参与任务次数',
      dataIndex: 'taskCount',
      hideInSearch: true,
    },
    {
      title: '已结束任务数',
      dataIndex: 'fineTaskCount',
      hideInSearch: true,
    },
    {
      title: '通过次数',
      dataIndex: 'passedCount',
      hideInSearch: true,
    },
    {
      title: '通过率',
      dataIndex: 'passedPercent',
      hideInSearch: true,
    },
    {
      title: '优秀次数',
      dataIndex: 'excellentCount',
      hideInSearch: true,
    },
    {
      title: '优秀率',
      dataIndex: 'excellentPercent',
      hideInSearch: true,
    },
    {
      title: '合格次数',
      dataIndex: 'onLevelCount',
      hideInSearch: true,
    },
    {
      title: '合格率',
      dataIndex: 'onLevelPercent',
      hideInSearch: true,
    },
    {
      title: '不合格次数',
      dataIndex: 'unPassCount',
      hideInSearch: true,
    },

    // 搜索
    {
      key: 'taskYear',
      title: '年份',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      key: 'cityId',
      title: '区域',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: cityList,
        fieldNames: {
          label: 'cityName',
          value: 'cityId',
        },
      },
    },
  ];
  return (
    <PageContainer>
      {/* 建设中... */}
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param: TOrgStatisticParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            taskYear: params.taskYear,
            cityId: params.cityId,
            labName: params.labName,
          };
          setExportParams({
            taskYear: params.taskYear,
            cityId: params.cityId,
            labName: params.labName,
          });
          const { code, data, msg } = await orgStatisticApi(param);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        // form={{
        //   // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
        //   syncToUrl: (values, type) => {
        //     if (type === 'get') {
        //       return {
        //         ...values,
        //       };
        //     }
        //     return values;
        //   },
        // }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/analyze/labExp/export"
            params={exportParams}
            method="get"
          >
            导出分析列表
          </DownloadButton>,
        ]}
      />
    </PageContainer>
  );
};

export default MechanismAnalysis;
