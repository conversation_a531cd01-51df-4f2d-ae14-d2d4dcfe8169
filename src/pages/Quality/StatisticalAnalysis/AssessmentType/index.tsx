/*
 * @Date: 2024-07-11 15:45:55
 * @LastEditors: 蒙家俊
 * @LastEditTime: 2024-07-24 13:50:58
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\StatisticalAnalysis\AssessmentType\index.tsx
 * @Description: 统计分析 - 考核类型
 */
import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { cityListApi } from '@/api/common';
import { taskTypeStatisticApi, TTaskTypeStatisticParams } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { TCityList } from '@/pages/EpidemicTracking/type';

const AssessmentType: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const actionRef = useRef<ActionType>();
  const [exportParams, setExportParams] = useState<{
    taskYear?: string;
    cityId?: number;
    taskType?: number;
  }>();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);

  const [cityList, setCityList] = useState<TCityList[]>([]);
  const getCityList = async () => {
    try {
      const { code, data, msg } = await cityListApi();
      if (code === 200) {
        setCityList(data);
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCityList();
  }, []);

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '考核类型',
      dataIndex: 'taskType',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '创建任务总数',
      dataIndex: 'taskCount',
      hideInSearch: true,
    },
    {
      title: '被考核机构数',
      dataIndex: 'taskCount',
      hideInSearch: true,
    },
    {
      title: '考核通过机构数',
      dataIndex: 'passedCount',
      hideInSearch: true,
    },
    {
      title: '通过率',
      dataIndex: 'passedPercent',
      hideInSearch: true,
    },
    {
      title: '优秀机构数',
      dataIndex: 'excellentCount',
      hideInSearch: true,
    },
    {
      title: '优秀率',
      dataIndex: 'excellentPercent',
      hideInSearch: true,
    },
    {
      title: '合格机构数',
      dataIndex: 'onLevelCount',
      hideInSearch: true,
    },
    {
      title: '合格率',
      dataIndex: 'onLevelPercent',
      hideInSearch: true,
    },
    {
      title: '不合格机构数',
      dataIndex: 'unPassCount',
      hideInSearch: true,
    },

    // 搜索
    {
      key: 'taskType',
      title: '考核类型',
      hideInTable: true,
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      key: 'taskYear',
      title: '年份',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      key: 'cityId',
      title: '区域',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: cityList,
        fieldNames: {
          label: 'cityName',
          value: 'cityId',
        },
      },
    },
  ];
  return (
    <PageContainer>
      {/* 建设中... */}
      <ProTable
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param: TTaskTypeStatisticParams = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            taskType: params.taskType,
            taskYear: params.taskYear,
            cityId: params.cityId,
          };
          setExportParams({
            taskType: params.taskType,
            taskYear: params.taskYear,
            cityId: params.cityId,
          });
          const { code, data, msg } = await taskTypeStatisticApi(param);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        // form={{
        //   // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
        //   syncToUrl: (values, type) => {
        //     if (type === 'get') {
        //       return {
        //         ...values,
        //       };
        //     }
        //     return values;
        //   },
        // }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/analyze/typeExp/export"
            params={exportParams}
            method="get"
          >
            导出分析列表
          </DownloadButton>,
        ]}
      />
    </PageContainer>
  );
};

export default AssessmentType;
