/*
 * @Date: 2024-07-11 15:56:10
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 11:18:13
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\StatisticalAnalysis\AssessmentTask\index.tsx
 * @Description: 统计分析 - 考核任务
 */
import { useState } from 'react';
import { message } from 'antd';
import { taskStatisticApi } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';

const AssessmentTask: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [exportParams, setExportParams] = useState<{
    taskName?: string;
  }>();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '考核任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '被考核机构数',
      dataIndex: 'taskCount',
      hideInSearch: true,
    },
    {
      title: '考核通过机构数',
      dataIndex: 'passedCount',
      hideInSearch: true,
    },
    {
      title: '通过率',
      dataIndex: 'passedPercent',
      hideInSearch: true,
    },
    {
      title: '优秀机构数',
      dataIndex: 'excellentCount',
      hideInSearch: true,
    },
    {
      title: '优秀率',
      dataIndex: 'excellentPercent',
      hideInSearch: true,
    },
    {
      title: '合格机构数',
      dataIndex: 'onLevelCount',
      hideInSearch: true,
    },
    {
      title: '合格率',
      dataIndex: 'onLevelPercent',
      hideInSearch: true,
    },
    {
      title: '不合格机构数',
      dataIndex: 'unPassCount',
      hideInSearch: true,
    },
  ];
  return (
    <PageContainer>
      {/* 建设中... */}
      <ProTable
        columns={columns}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            pageNum: params.current!,
            pageSize: params.pageSize!,
            taskName: params.taskName,
          };
          setExportParams({
            taskName: params.taskName,
          });
          const { code, data, msg } = await taskStatisticApi(param);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 120,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        // form={{
        //   // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
        //   syncToUrl: (values, type) => {
        //     if (type === 'get') {
        //       return {
        //         ...values,
        //       };
        //     }
        //     return values;
        //   },
        // }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <DownloadButton
            url="/data/analyze/taskExp/export"
            params={exportParams}
            method="get"
          >
            导出分析列表
          </DownloadButton>,
        ]}
      />
    </PageContainer>
  );
};

export default AssessmentTask;
