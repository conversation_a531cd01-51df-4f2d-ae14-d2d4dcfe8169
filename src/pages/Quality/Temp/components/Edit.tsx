/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import {
  addTemp,
  getTempDetail,
  getTempDetailCode,
  getTempList,
  updateTemp,
} from '@/api/temp';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { useTempStore } from '@/store/temp';
import { DragOutlined } from '@ant-design/icons';
import {
  EditableProTable,
  FormInstance,
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import type {
  EditableFormInstance,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import RequiredTag from '@/components/RequiredTag';
import { formItemLayout } from '../data';
import Preview from './Preview';
import SelectConfig from './SelectConfig';

function isEqual(obj1: any, obj2: any) {
  if (obj1 === obj2) return true;

  // 检查两个对象的类型
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  // 检查两个对象的属性个数
  if (keys1.length !== keys2.length) return false;

  for (let i = 0; i < keys1.length; i++) {
    const key = keys1[i];

    // 使用递归比较对象的属性值
    if (!isEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}
type TEditProps = {
  close: () => void;
  detailId?: string;
  modelType: string; // 弹窗类型：  copy-复制新增  add-新增   edit-草稿修改  editing-可使用修改
};

type DataSourceType = any;

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailId: _id,
  modelType: _type,
}) => {
  const [detailId, setDetailId] = useState(_id);
  const [modelType, setModelType] = useState(_type);

  const [messageApi, contextHolder] = message.useMessage();

  // 预览弹窗
  const [previewModel, setPreviewModel] = useState<boolean>(false);

  // 获取table中需要的枚举
  const { assessmentTypesOnForm2, getAssessmentTypesOnForm2 } =
    useQualityStore();
  const { inputTypeOnTable, getInputType } = useTempStore();

  // 模板提交类型  save / submit
  const [submitType, setSubmitType] = useState<string>('save');

  // 保存按钮
  const [loading, setLoading] = useState<boolean>(false);

  // 启用模板按钮
  const [loading2, setLoading2] = useState<boolean>(false);

  // 预览模板页面数据
  const [previewPageData, setPreviewPageData] = useState({});

  // 表单1
  const formRef1 = useRef<FormInstance>(null);

  /* 基本信息表格 */
  const [defaultData2, setDefaultData2] = useState<DataSourceType[]>([]);
  const [editableKeys2, setEditableRowKeys2] = useState<React.Key[]>(() => []);
  const formRef2 = useRef<ProFormInstance<any>>();
  const editorFormRef2 = useRef<EditableFormInstance<DataSourceType>>();
  const columns2: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
      width: '30%',
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 170,
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      fieldProps: {
        getPopupContainer: (triggerNode: HTMLElement) =>
          triggerNode.parentNode as HTMLElement,
        dropdownMatchSelectWidth: true,
        onMouseDown: (e: React.MouseEvent) => {
          e.stopPropagation();
          (e.currentTarget as HTMLElement).click();
        },
        onBlur: () => {
          document.body.click();
        },
        onSelect: () => {
          setTimeout(() => {
            document.body.click();
          }, 0);
        },
      },
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 240,
    },
  ];

  /* 检验基本信息配置表格 */
  const [defaultData3, setDefaultData3] = useState<DataSourceType[]>([]);
  const [editableKeys3, setEditableRowKeys3] = useState<React.Key[]>(() => []);
  const formRef3 = useRef<ProFormInstance<any>>();
  const editorFormRef3 = useRef<EditableFormInstance<DataSourceType>>();
  const columns3: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
      width: '30%',
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 170,
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      fieldProps: {
        getPopupContainer: (triggerNode: HTMLElement) =>
          triggerNode.parentNode as HTMLElement,
        dropdownMatchSelectWidth: true,
        onMouseDown: (e: React.MouseEvent) => {
          e.stopPropagation();
          (e.currentTarget as HTMLElement).click();
        },
        onBlur: () => {
          document.body.click();
        },
        onSelect: () => {
          setTimeout(() => {
            document.body.click();
          }, 0);
        },
      },
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 240,
    },
  ];

  /* 检验项目表格 */
  // 检验项目默认初始至少有一条数据
  const [defaultData4, setDefaultData4] = useState<DataSourceType[]>([
    {
      id: 'fakeId' + (Math.random() * 1000000).toFixed(0),
      name: '',
      tip: '',
    },
  ]);
  const [editableKeys4, setEditableRowKeys4] = useState<React.Key[]>(() => []);
  const formRef4 = useRef<ProFormInstance<any>>();
  const editorFormRef4 = useRef<EditableFormInstance<DataSourceType>>();
  const columns4: ProColumns<DataSourceType>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      key: 'index',
      width: 80,
    },
    {
      title: <RequiredTag title="项目名称" />,
      dataIndex: 'name',
      formItemProps: () => {
        return {
          rules: [
            { required: submitType === 'submit', message: '此项为必填项' },
          ],
        };
      },
    },
    {
      title: '提示信息',
      dataIndex: 'tip',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
    },
  ];

  // 配置下拉项目
  const [showSelect, setShowSelect] = useState(false);
  const [showSelectData, setShowSelectData] = useState([]);
  const [showSelectIdx, setShowSelectIdx] = useState(-1);
  const [showSelectForm, setShowSelectForm] = useState('');
  let dataCaches = {};
  const setDataCaches = (v: any) => {
    dataCaches = v;
  };

  // ====================== drag ==============================
  // 存放可拖拽的数据ID集合
  const [enableDragIdsList, setEnableDragIdsList] = useState<string[]>([]);
  const [enableDragSampleIdsList, setEnableDragSampleIdsList] = useState<
    string[]
  >([]);
  const [enableDragItemIdsList, setEnableDragItemIdsList] = useState<string[]>(
    []
  );
  // ====================== drag ==============================

  /**
   * @TODO 保存模板
   * */
  const saveTemplate = async () => {
    try {
      setSubmitType('save');
      const paramData = await submit('save');
      const params = {
        ...paramData,
        status: 0,
      };
      const api =
        modelType === 'edit' || modelType === 'editing' ? updateTemp : addTemp;
      if (detailId && (modelType === 'edit' || modelType === 'editing')) {
        params.id = detailId;
      }

      setLoading(true);
      const { code, data, msg }: any = await api(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        if (modelType === 'add') {
          // 通过列表接口查询新增的数据
          const res: any = await getTempList({
            code: paramData.code,
            current: 1,
            size: 1,
          });
          setDetailId(res.rows[0].id);
          setModelType('edit');
        }
        setDataCaches(JSON.stringify(paramData));
        getTemplteCode();
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 启用模板
   *
   */
  const enableTemplate = async () => {
    try {
      setSubmitType('submit');
      const params = {
        ...(await submit('submit')),
        status: 1,
      };
      // console.log(params);
      // return
      setLoading2(true);
      // 草稿编辑/可使用编辑
      const isEdit = modelType === 'edit' || modelType === 'editing';
      const api = isEdit ? updateTemp : addTemp;
      if (detailId && isEdit) {
        params.id = detailId;
      }
      const { code, data, msg }: any = await api(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading2(false);
    }
  };

  /**
   * @TODO 预览填报
   * */
  const previewFill = async () => {
    try {
      // 校验-检验基本信息配置
      let form1 = cloneDeep(formRef2.current?.getFieldValue('table'));
      let form2 = cloneDeep(formRef3.current?.getFieldValue('table'));
      if (form1.length) {
        await formRef2.current?.validateFields();
      }
      if (form2.length) {
        await formRef3.current?.validateFields();
      }
      const data = await queryData();
      const params = {
        ...data,
        form4: data?.form4?.map((item: any) => {
          item.itemName = item.name;
          return item;
        }),
      };
      if (params.form2.find((item: any) => !item.inputBox)) {
        throw new Error();
      }
      setPreviewModel(true);
      setPreviewPageData(params);
    } catch (error) {
      messageApi.error(
        '检验基本信息或者样本检验信息配置有数据时，必须填写完整信息才可预览'
      );
      throw new Error(`Error：${error}`);
    }
  };

  /**
   * @TODO 获取页面数据
   * */
  const queryData = async () => {
    let form1 = cloneDeep(formRef1.current?.getFieldsValue());
    let form2 = cloneDeep(formRef2.current?.getFieldValue('table'));
    let form3 = cloneDeep(formRef3.current?.getFieldValue('table'));
    let form4 = cloneDeep(formRef4.current?.getFieldValue('table'));
    return {
      form1,
      form2,
      form3,
      form4,
    };
  };

  /**
   * @TODO 判定数组是否有重复数据
   * */
  const hasDuplicateIds = async (array: any[], str: string) => {
    console.log(array);
    const hash: any = {};
    for (const item of array) {
      if (hash[item[str]]) {
        return true;
      }
      hash[item[str]] = true;
    }
    return false;
  };

  /**
   * @TODO 提交参数校验及组装
   * @params isFirstChoice 是否是第一次选择
   */
  const submit = async (type: 'save' | 'submit') => {
    try {
      await formRef1?.current?.validateFields();
      await formRef2?.current?.validateFields();
      await formRef3?.current?.validateFields();
      await formRef4?.current?.validateFields();
      const { form1, form2, form3, form4 } = await queryData();

      if (type === 'submit') {
        if (await hasDuplicateIds(form2, 'fieldName')) {
          throw Error('检验基本信息配置,填报字段不可重复');
        }
        if (await hasDuplicateIds(form3, 'fieldName')) {
          throw Error('样本检验信息配置,填报字段不可重复');
        }
        if (await hasDuplicateIds(form4, 'name')) {
          throw Error('样本检验项目配置,项目名称不可重复');
        }
        if (!form2.length) {
          throw Error('请设置检验基本信息配置');
        }
        if (!form3.length) {
          throw Error('请设置样本检验信息配置');
        }
      }

      form2.forEach((item: any, idx: any) => {
        item.order = idx + 1;
        if (
          (item.id && item.id.toString().indexOf('fakeId') !== -1) ||
          modelType === 'copy'
        ) {
          item.id = undefined;
        }
      });

      form3.forEach((item: any, idx: any) => {
        item.order = idx + 1;
        if (
          (item.id && item.id.toString().indexOf('fakeId') !== -1) ||
          modelType === 'copy'
        ) {
          item.id = undefined;
        }
      });

      form4.forEach((item: any, idx: any) => {
        item.order = idx + 1;
        if (
          (item.id && item.id.toString().indexOf('fakeId') !== -1) ||
          modelType === 'copy'
        ) {
          item.id = undefined;
        }
      });

      const params = {
        ...form1,
        inspectionBasicInformationList: form2,
        sampleInspectionInformationList: form3,
        templateItemList: form4,
        isFirstChoice: 0, // 原来是否首选项字段，废弃不传空即可
      };
      return params;
    } catch (error: any) {
      if (typeof error === 'string') {
        messageApi.error(error);
      } else {
        messageApi.error('请完善配置信息');
      }
      throw new Error(`${error}`);
    }
  };

  // 详情
  const getDetail = async () => {
    if (detailId) {
      try {
        setLoading(true);
        const { code, data, msg }: any = await getTempDetail(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          // 基本信息
          if (modelType === 'edit' || modelType === 'editing') {
            formRef1.current?.setFieldsValue(data);
          } else if (modelType === 'copy') {
            formRef1.current?.setFieldsValue({
              ...data,
              name: data?.name + '-复制新增',
              code: '',
            });
            await getTemplteCode();
          }

          // 检验基本信息配置
          formRef2.current?.setFieldsValue({
            table:
              data?.inspectionBasicInformationList?.map((item: any) => {
                return { ...item, isRequired: item.isRequired + '' };
              }) || [],
          });
          setEditableRowKeys2(
            data?.inspectionBasicInformationList?.map((item: any) => item.id)
          );
          // 设置基本信息配置列表可拖拽IDS列表
          setEnableDragIdsList(
            data?.inspectionBasicInformationList?.map((item: any) => item.id) ||
              []
          );

          // 样品检验信息
          formRef3.current?.setFieldsValue({
            table:
              data?.sampleInspectionInformationList?.map((item: any) => {
                return {
                  ...item,
                  isRequired: item.isRequired + '',
                  isJudgmentItem: item.isJudgmentItem + '',
                };
              }) || [],
          });
          setEditableRowKeys3(
            data?.sampleInspectionInformationList?.map((item: any) => item.id)
          );
          setEnableDragSampleIdsList(
            data?.sampleInspectionInformationList?.map(
              (item: any) => item.id
            ) || []
          );

          // 样品检验项目信息
          const _data4 =
            data?.templateItemList?.map((item: any) => {
              return {
                ...item,
                isRequired: item.isRequired + '',
                isJudgmentItem: item.isJudgmentItem + '',
              };
            }) || defaultData4;
          //@ts-ignore
          _data4.sort((a, b) => a.order - b.order);
          formRef4.current?.setFieldsValue({
            table: _data4,
          });
          if (data?.templateItemList?.length) {
            setEditableRowKeys4(
              data.templateItemList?.map((item: any) => item.id)
            );
          }
          setEnableDragItemIdsList(
            data.templateItemList?.map((item: any) => item.id) || []
          );
        } else {
          messageApi.error(msg);
        }
      } catch (error) {
        throw new Error(`Error: ${error}`);
      } finally {
        setLoading(false);
      }
    } else {
      getTemplteCode();
    }
  };

  /**
   * @TODO 新增获取模板id
   */
  const getTemplteCode = async () => {
    const { code, data, msg }: any = await getTempDetailCode();
    if (code === codeDefinition.QUERY_SUCCESS) {
      formRef1.current?.setFieldsValue({
        code: data,
      });
    } else {
      messageApi.error(msg);
    }
  };

  useEffect(() => {
    getDetail();
  }, [detailId]);

  // 表格值改变监听
  const onValuesChangeHandle = (v: any, tableFormRef: any) => {
    // 判断"输入框类型是否是文件，若是则修改信息提示"
    if (
      v &&
      v.table &&
      !Array.isArray(v.table) &&
      Object.keys(v.table)?.length &&
      v.table[0] && // 确保v.table[0]存在
      typeof v.table[0] === 'object' && // 确保v.table[0]是对象
      Object.keys(v.table[0])?.includes('inputBox')
    ) {
      const rowKey = Object.keys(v.table)[0];
      const rowData = tableFormRef.current?.getRowData?.(rowKey);

      // 确保rowData存在
      if (rowData) {
        rowData.pomptInformation =
          v.table[rowKey].inputBox === 'file'
            ? '请上传word、excel、ppt、pdf、图片格式文件，每份文件不超过10M'
            : '';
        tableFormRef.current?.setRowData?.(rowKey, {
          ...rowData,
        });
      }
    }
  };

  useEffect(() => {
    const key = defaultData4.map((item) => item.id);
    setEditableRowKeys4(key);
    getInputType();
    getAssessmentTypesOnForm2();
  }, []);

  // ======================= drag ==============================
  /**
   * 拖拽句柄组件
   * @param id 行数据的唯一标识
   * @returns 返回一个可拖拽的图标组件
   * @description 用于表格行的拖拽操作,显示为一个可拖拽的图标
   */
  const DragHandler = ({ id }: { id: string | number }) => {
    // 使用useSortable hook获取拖拽所需的ref和事件监听器
    const { setNodeRef, attributes, listeners } = useSortable({ id });
    return (
      <DragOutlined
        ref={setNodeRef}
        {...listeners}
        style={{ cursor: 'grab' }}
      />
    );
  };

  /**
   * 可拖拽表格行组件
   * @param props 表格行的props属性
   * @returns 返回一个可拖拽的表格行组件
   * @description 用于实现表格行的拖拽功能,包含拖拽时的样式和状态控制
   */
  const TableRow = ({ ...props }) => {
    // 获取行数据的唯一标识
    const id = props['data-row-key'];
    // 使用useSortable hook获取拖拽所需的状态和方法
    const { isDragging, setNodeRef, transform, transition, attributes } =
      useSortable({ id });
    // 设置行的样式,包括拖拽时的位移、过渡效果和透明度
    const rowStyle = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1, // 拖拽时降低透明度以示区分
    };

    // 自定义处理点击事件，确保点击下拉框时不触发拖拽
    const handleRowClick = (e: React.MouseEvent) => {
      // 检查点击的元素是否是下拉框相关元素
      const target = e.target as HTMLElement;
      if (
        target.tagName === 'DIV' ||
        target.tagName === 'SPAN' ||
        target.tagName === 'INPUT' ||
        target.closest('.ant-select') ||
        target.closest('.ant-select-dropdown')
      ) {
        // 允许事件继续传播，不干扰下拉框的点击事件
        e.stopPropagation();
      }
    };

    // 返回带有拖拽功能的表格行，添加点击事件处理
    return (
      <tr
        {...props}
        ref={setNodeRef}
        style={rowStyle}
        {...attributes}
        onClick={handleRowClick}
      />
    );
  };

  /**
   * 处理拖拽结束事件
   * @param event 拖拽事件对象
   * event.active - 当前被拖拽的元素
   * event.over - 拖拽结束时位于其上方的元素
   */
  const handleDragEnd = (event: any, formRef: any) => {
    const _dataSourceCache = formRef.current?.getFieldsValue()?.table;
    const { active, over } = event;
    // 当拖拽结束且目标位置不是自身时进行位置交换
    if (over && active?.id !== over?.id) {
      // 获取拖拽元素的原始索引
      const oldIndex = _dataSourceCache?.findIndex(
        //@ts-ignore
        (item) => item.id === active.id
      );
      // 获取目标位置的索引
      const newIndex = _dataSourceCache?.findIndex(
        //@ts-ignore
        (item) => item?.id === over?.id
      );
      // 使用 arrayMove 方法重新排序数组
      const _newData = arrayMove(_dataSourceCache, oldIndex, newIndex);
      // 更新其中的order属性
      _newData.forEach((item, index) => {
        //@ts-ignore
        item.order = index + 1;
      });
      // 更新数据源
      formRef.current?.setFieldsValue({ table: _newData });
    }
  };
  // ======================= drag ==============================

  return (
    <div className="flex flex-col h-full w-full">
      {contextHolder}
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="基本信息">
          <ProForm
            key="form1"
            formRef={formRef1}
            {...formItemLayout}
            layout="horizontal"
            grid={true}
            submitter={false}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef1.current?.setFieldsValue(values);
            }}
          >
            <ProFormText
              name="code"
              label="模板编号"
              disabled
              placeholder="请输入模板编号"
              rules={[
                {
                  required: submitType === 'submit',
                  message: '请输入模板编号',
                },
              ]}
              colProps={formItemLayout}
            />
            <ProFormSelect
              options={assessmentTypesOnForm2}
              rules={[
                {
                  required: submitType === 'submit',
                  message: '请选择考核类型',
                },
              ]}
              name="assessmentType"
              label="考核类型"
              colProps={formItemLayout}
            />
            <ProFormText
              name="name"
              label="模板名称"
              placeholder="请输入模板名称"
              rules={[
                {
                  required: submitType === 'submit',
                  message: '请输入模板名称',
                },
              ]}
              colProps={formItemLayout}
            />
          </ProForm>
        </BlockContainer>
        <div className="mt-4">
          <BlockContainer title="检验基本信息配置">
            <DndContext
              sensors={useSensors(
                useSensor(PointerSensor, {
                  activationConstraint: {
                    distance: 8,
                    delay: 150,
                    tolerance: 5,
                  },
                }),
                useSensor(KeyboardSensor)
              )}
              collisionDetection={closestCenter}
              onDragEnd={(e) => handleDragEnd(e, formRef2)}
              modifiers={[restrictToVerticalAxis]}
            >
              <SortableContext
                items={enableDragIdsList}
                strategy={verticalListSortingStrategy}
              >
                <ProForm<{
                  table: DataSourceType[];
                }>
                  key="form2"
                  formRef={formRef2}
                  submitter={false}
                  initialValues={{
                    table: defaultData2,
                  }}
                  validateTrigger="onBlur"
                  //@ts-ignore
                  onValuesChange={(v) => {
                    onValuesChangeHandle(v, editorFormRef2);
                  }}
                >
                  <EditableProTable<DataSourceType>
                    rowKey="id"
                    scroll={{
                      x: 960,
                    }}
                    editableFormRef={editorFormRef2}
                    name="table"
                    controlled={true}
                    recordCreatorProps={{
                      position: 'bottom',
                      record: () => ({
                        id: 'fakeId' + (Math.random() * 1000000).toFixed(0),
                      }),
                    }}
                    columns={columns2}
                    editable={{
                      type: 'multiple',
                      editableKeys: editableKeys2,
                      onChange: setEditableRowKeys2,
                      actionRender: (row, config, defaultDom) => {
                        const tableDataSource = formRef2.current?.getFieldValue(
                          'table'
                        ) as DataSourceType[];
                        const btns = [];
                        if (row && row.inputBox! === 'select') {
                          btns.push(
                            <a
                              key="select"
                              onClick={() => {
                                setShowSelectIdx(config.index!);
                                setShowSelectData(
                                  row.enumItems ? row.enumItems.split(',') : []
                                );
                                setShowSelectForm('form2');
                                setShowSelect(true);
                              }}
                            >
                              配置下拉项
                            </a>
                          );
                        }
                        return [
                          <DragHandler key={row?.id} id={row?.id} />,
                          ...btns,
                          defaultDom.delete,
                        ];
                      },
                    }}
                    components={{
                      body: {
                        row: TableRow,
                      },
                    }}
                  />
                </ProForm>
              </SortableContext>
            </DndContext>
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验信息配置">
            <ProForm<{
              table: DataSourceType[];
            }>
              key="form3"
              formRef={formRef3}
              submitter={false}
              initialValues={{
                table: defaultData3,
              }}
              validateTrigger="onBlur"
              //@ts-ignore
              onValuesChange={(v) => {
                onValuesChangeHandle(v, editorFormRef3);
              }}
            >
              <DndContext
                sensors={useSensors(
                  useSensor(PointerSensor, {
                    activationConstraint: {
                      distance: 8,
                      delay: 150,
                      tolerance: 5,
                    },
                  }),
                  useSensor(KeyboardSensor)
                )}
                collisionDetection={closestCenter}
                onDragEnd={(e) => handleDragEnd(e, formRef3)}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={enableDragSampleIdsList}
                  strategy={verticalListSortingStrategy}
                >
                  <EditableProTable<DataSourceType>
                    rowKey="id"
                    scroll={{
                      x: 960,
                    }}
                    editableFormRef={editorFormRef3}
                    name="table"
                    controlled={true}
                    recordCreatorProps={{
                      position: 'bottom',
                      record: () => ({
                        id: 'fakeId' + (Math.random() * 10000000).toFixed(0),
                      }),
                    }}
                    columns={columns3}
                    editable={{
                      type: 'multiple',
                      editableKeys: editableKeys3,
                      onChange: setEditableRowKeys3,
                      actionRender: (row, config, defaultDom) => {
                        const tableDataSource = formRef3.current?.getFieldValue(
                          'table'
                        ) as DataSourceType[];
                        const btns = [];
                        if (row && row.inputBox! === 'select') {
                          btns.push(
                            <a
                              key="select2"
                              onClick={() => {
                                setShowSelectIdx(config.index!);
                                setShowSelectData(
                                  row.enumItems ? row.enumItems.split(',') : []
                                );
                                setShowSelectForm('form3');
                                setShowSelect(true);
                              }}
                            >
                              配置下拉项
                            </a>
                          );
                        }
                        return [
                          <DragHandler key={row?.id} id={row?.id} />,
                          ...btns,
                          defaultDom.delete,
                        ];
                      },
                    }}
                    components={{
                      body: {
                        row: TableRow,
                      },
                    }}
                  />
                </SortableContext>
              </DndContext>
            </ProForm>
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验项目配置">
            <ProForm
              key="form4"
              formRef={formRef4}
              {...formItemLayout}
              layout="horizontal"
              grid={true}
              submitter={false}
              initialValues={{
                table: defaultData4,
              }}
              //@ts-ignore
              onValuesChange={(_, values: any) => {
                for (const key in values) {
                  if (typeof values[key] === 'string') {
                    values[key] = values[key].trim();
                  }
                }
                formRef4.current?.setFieldsValue(values);
              }}
            >
              <DndContext
                sensors={useSensors(
                  useSensor(PointerSensor, {
                    activationConstraint: {
                      distance: 8,
                      delay: 150,
                      tolerance: 5,
                    },
                  }),
                  useSensor(KeyboardSensor)
                )}
                collisionDetection={closestCenter}
                onDragEnd={(e) => handleDragEnd(e, formRef4)}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={enableDragItemIdsList}
                  strategy={verticalListSortingStrategy}
                >
                  <EditableProTable<DataSourceType>
                    rowKey="id"
                    editableFormRef={editorFormRef4}
                    name="table"
                    controlled={true}
                    recordCreatorProps={{
                      position: 'bottom',
                      record: () => ({
                        id: 'fakeId' + (Math.random() * 1000000).toFixed(0),
                      }),
                    }}
                    columns={columns4}
                    editable={{
                      type: 'multiple',
                      editableKeys: editableKeys4,
                      onChange: setEditableRowKeys4,
                      actionRender: (row, config, defaultDom) => {
                        const tableDataSource = formRef4.current?.getFieldValue(
                          'table'
                        ) as DataSourceType[];
                        //@ts-ignore
                        const btns = [];
                        // 至少保留一行数据
                        return [
                          <DragHandler key={row?.id} id={row?.id} />,
                          //@ts-ignore
                          ...btns,
                          config.index! && config.index !== 0
                            ? defaultDom.delete
                            : null,
                        ];
                      },
                    }}
                    components={{
                      body: {
                        row: TableRow,
                      },
                    }}
                  />
                </SortableContext>
              </DndContext>
            </ProForm>
            <div className="text-[red] mt-[-15px] font-blod">
              注：至少需要配置一个检验项目
            </div>
          </BlockContainer>
        </div>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        {modelType !== 'editing' ? (
          <>
            <Button
              onClick={() => {
                saveTemplate();
              }}
              loading={loading}
            >
              保存为草稿
            </Button>
            <Button
              onClick={() => {
                enableTemplate();
              }}
              type="primary"
              loading={loading2}
            >
              启用模板
            </Button>
          </>
        ) : (
          <Button
            type="primary"
            onClick={() => {
              enableTemplate();
            }}
            loading={loading2}
          >
            保存并启用
          </Button>
        )}
        <Button onClick={previewFill}>预览填报页</Button>
      </div>
      {/* 预览模板 */}
      <Drawer
        width="85%"
        title="预览模板"
        onClose={() => setPreviewModel(false)}
        open={previewModel}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Preview
          previewPageData={previewPageData}
          close={() => setPreviewModel(false)}
        />
      </Drawer>
      {/* 角色详情 */}
      <SelectConfig
        open={showSelect}
        list={showSelectData}
        closeDetail={(val) => {
          if (val) {
            if (showSelectForm === 'form2') {
              const d = editorFormRef2.current?.getRowData?.(showSelectIdx);
              editorFormRef2.current?.setRowData?.(showSelectIdx, {
                ...d,
                enumItems: Array.from(
                  new Set(val.map((row: any) => row.item))
                ).join(','),
              });
            }
            if (showSelectForm === 'form3') {
              const d = editorFormRef3.current?.getRowData?.(showSelectIdx);
              editorFormRef3.current?.setRowData?.(showSelectIdx, {
                ...d,
                enumItems: Array.from(
                  new Set(val.map((row: any) => row.item))
                ).join(','),
              });
            }
          }
          setShowSelect(false);
        }}
      />
    </div>
  );
};

export default MenuDetail;
