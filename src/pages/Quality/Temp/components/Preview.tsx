/*
 * @Author: LGX
 * @Date: 2024-02-28 15:27:12
 * @LastEditors: LGX
 * @LastEditTime: 2024-04-01 17:15:10
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\Temp\components\Preview.tsx
 * @Description: 模板预览
 * 
 */
import { useEffect, useRef, useState, MutableRefObject } from 'react';
import { Button, message, Modal, Collapse } from 'antd';
import BlockContainer from '@/components/BlockContainer';
import JsonForm from '@/components/JsonForm';
import JsonTable from '@/components/JsonTable';

type TEditProps = {
  close: () => void;
  readOnly?: boolean;
  previewPageData?: any; // 预览数据
};

const Preview: React.FC<TEditProps> = ({
  close,
  readOnly = false,
  previewPageData
}) => {

  const formRef = useRef<any>(null);
  const formRef2 = useRef<any>(null);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="检验基本信息">
          <JsonForm formDataList={previewPageData?.form2} onRef={formRef} fileCheckTooltipTitle="检验基本信息" readOnly={readOnly} />
        </BlockContainer>
        <div className="mt-4">
          <BlockContainer title="样本检验信息" bg="#F5F5F5">
            {
              // 样本检验信息配置有数据时显示
              previewPageData?.form3?.length ?
                (
                  <>
                    <Collapse
                      size="small"
                      expandIconPosition="end"
                      defaultActiveKey={[0]}
                      items={[
                        {
                          key: 0,
                          label: <span>样本编号：XXX1（<span className='font-extrabold'>已接收</span>）</span>,
                          children: <JsonTable formDataList={previewPageData?.form3} formRef={formRef2} projectDataList={previewPageData?.form4} />
                        },
                      ]}
                    />
                    <Collapse
                      size="small"
                      className='mt-2'
                      expandIconPosition="end"
                      items={[
                        {
                          key: 0,
                          label: <span>样本编号：XXX2（<span className='font-extrabold'>未接收</span>）</span>,
                          children: <JsonTable formDataList={previewPageData?.form3} formRef={formRef2} readOnly={true} projectDataList={previewPageData?.form4} />
                        },
                      ]}
                    />
                  </>
                ) : null
            }
          </BlockContainer>
        </div>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button
          onClick={() => {
            close();
          }}
        >
          关闭预览页面
        </Button>
      </div>
    </div>
  );
};

export default Preview;
