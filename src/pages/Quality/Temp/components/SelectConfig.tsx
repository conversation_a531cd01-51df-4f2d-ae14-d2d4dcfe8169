import { Key, useCallback, useEffect, useRef, useState } from 'react';
import { Col, message, Modal, Row, Skeleton, Tree } from 'antd';
import { EditableProTable, ProForm } from '@ant-design/pro-components';
import type {
  EditableFormInstance,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';

type DataSourceType = {
  id: React.Key;
  title?: string;
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: (val?: any) => void;
  list?: any[];
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  list = [],
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  const close = () => {
    closeDetail();
  };
  useEffect(() => {
    if (list) {
      let keys: any = [];
      setDefaultData(
        list.map((item: any, idx: any) => {
          keys.push(idx);
          return {
            id: idx,
            item: item,
          };
        })
      );
      setEditableRowKeys(keys);
    } else {
      setDefaultData([]);
    }
  }, [list]);

  const [defaultData, setDefaultData] = useState<DataSourceType[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const formRef = useRef<ProFormInstance<any>>();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '选项',
      dataIndex: 'item',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 60,
    },
  ];

  return (
    <Modal
      className="min-w-[600px]"
      title={'配置下拉项'}
      width="40%"
      open={open}
      destroyOnClose
      onCancel={close}
      onOk={async () => {
        try {
          await formRef.current?.validateFields();
          const form = formRef.current?.getFieldValue('table');
          closeDetail(form);
        } catch (error) {
          message.warning('请完善表单');
        }
      }}
      confirmLoading={loading}
    >
      <ProForm<{
        table: DataSourceType[];
      }>
        key="form"
        formRef={formRef}
        submitter={false}
        initialValues={{
          table: defaultData,
        }}
        validateTrigger="onBlur"
      >
        <EditableProTable<DataSourceType>
          rowKey="id"
          scroll={{}}
          editableFormRef={editorFormRef}
          name="table"
          controlled={true}
          recordCreatorProps={{
            position: 'bottom',
            record: () => ({
              id: (Math.random() * 1000000).toFixed(0),
            }),
          }}
          columns={columns}
          editable={{
            type: 'multiple',
            editableKeys: editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (row, config, defaultDom) => {
              return [defaultDom.delete];
            },
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default RoleDetail;
