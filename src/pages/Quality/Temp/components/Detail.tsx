/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { getTempDetail, getTempDetailCode } from '@/api/temp';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { useTempStore } from '@/store/temp';
import {
  EditableProTable,
  FormInstance,
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import RequiredTag from '@/components/RequiredTag';
import { formItemLayout } from '../data';
import Preview from './Preview';

type TEditProps = {
  close: () => void;
  detailId?: string;
};

type DataSourceType = any;

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  // 获取table中需要的枚举
  const { assessmentTypesOnForm, getAssessmentTypes } = useQualityStore();

  const { inputTypeOnTable, getInputType } = useTempStore();

  // 表单1
  const formRef1 = useRef<FormInstance>(null);
  // 表格2
  const formRef2 = useRef<ProFormInstance<any>>();
  const columns2: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      width: '30%',
      editable: false,
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      editable: false,
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 170,
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      editable: false,
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
      editable: false,
    },
  ];
  // 表格3
  const formRef3 = useRef<ProFormInstance<any>>();
  const columns3: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      editable: false,
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 170,
      editable: false,
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      editable: false,
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
      editable: false,
    },
  ];

  // 预览弹窗
  const [previewModel, setPreviewModel] = useState<boolean>(false);

  // 预览模板页面数据
  const [previewPageData, setPreviewPageData] = useState({});

  /* 检验项目表格 */
  const formRef4 = useRef<ProFormInstance<any>>();
  const columns4: ProColumns<DataSourceType>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      key: 'index',
      width: 80,
    },
    {
      title: <RequiredTag title="项目名称" />,
      dataIndex: 'name',
    },
    {
      title: '提示信息',
      dataIndex: 'tip',
    },
  ];

  // 获取详情
  const getDetail = async () => {
    if (detailId) {
      try {
        const { code, data, msg }: any = await getTempDetail(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          // 基本信息
          formRef1.current?.setFieldsValue(data);
          // 检验基本信息配置
          formRef2.current?.setFieldsValue({
            table:
              data.inspectionBasicInformationList.map((item: any) => {
                return { ...item, isRequired: item.isRequired + '' };
              }) || [],
          });
          // 样品检验信息
          formRef3.current?.setFieldsValue({
            table:
              data.sampleInspectionInformationList.map((item: any) => {
                return {
                  ...item,
                  isRequired: item.isRequired + '',
                  isJudgmentItem: item.isJudgmentItem + '',
                };
              }) || [],
          });

          // 样品检验项目信息
          formRef4.current?.setFieldsValue({
            table: data?.templateItemList?.map((item: any) => {
              return {
                ...item,
                isRequired: item.isRequired + '',
                isJudgmentItem: item.isJudgmentItem + '',
              };
            }),
          });
        } else {
          message.error(msg);
        }
      } catch (error) {
        throw new Error(`Error: ${error}`);
      }
    } else {
      const { code, data, msg }: any = await getTempDetailCode();
      if (code === codeDefinition.QUERY_SUCCESS) {
        formRef1.current?.setFieldsValue({
          code: data,
        });
      } else {
        message.error(msg);
      }
    }
  };

  /**
   * @TODO 获取页面数据
   * */
  const queryData = async () => {
    let form1 = cloneDeep(formRef1.current?.getFieldsValue());
    let form2 = cloneDeep(formRef2.current?.getFieldValue('table'));
    let form3 = cloneDeep(formRef3.current?.getFieldValue('table'));
    let form4 = cloneDeep(formRef4.current?.getFieldValue('table'));
    return {
      form1,
      form2,
      form3,
      form4,
    };
  };

  /**
   * @TODO 预览填报
   * */
  const previewFill = async () => {
    try {
      // 校验-检验基本信息配置
      let form1 = cloneDeep(formRef2.current?.getFieldValue('table'));
      let form2 = cloneDeep(formRef3.current?.getFieldValue('table'));
      // await autoSave();
      if (form1.length) {
        await formRef2.current?.validateFields();
      }
      if (form2.length) {
        await formRef3.current?.validateFields();
      }
      const data = await queryData();
      const params = {
        ...data,
        form4: data?.form4?.map((item: any) => {
          item.itemName = item.name;
          return item;
        }),
      };
      if (params.form2.find((item: any) => !item.inputBox)) {
        throw new Error();
      }
      setPreviewModel(true);
      setPreviewPageData(params);
    } catch (error) {
      message.error(
        '检验基本信息或者样本检验信息配置有数据时，必须填写完整信息才可预览'
      );
      throw new Error(`Error：${error}`);
    }
  };

  useEffect(() => {
    getDetail();
  }, [detailId]);

  useEffect(() => {
    getInputType();
    getAssessmentTypes();
  }, []);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="基本信息">
          <ProForm
            key="form1"
            formRef={formRef1}
            {...formItemLayout}
            layout="horizontal"
            grid={true}
            submitter={false}
          >
            <ProFormText
              name="code"
              label="模板编号"
              readonly
              placeholder="请输入模板编号"
              rules={[{ required: true, message: '请输入模板编号' }]}
              colProps={formItemLayout}
            />
            <ProFormSelect
              options={assessmentTypesOnForm}
              rules={[{ required: true, message: '请选择考核类型' }]}
              name="assessmentType"
              label="考核类型"
              colProps={formItemLayout}
              readonly
            />
            <ProFormText
              name="name"
              label="模板名称"
              placeholder="请输入模板名称"
              rules={[{ required: true, message: '请输入模板名称' }]}
              colProps={formItemLayout}
              readonly
            />
          </ProForm>
        </BlockContainer>
        <div className="mt-4">
          <BlockContainer title="检验基本信息配置">
            <ProForm<{
              table: DataSourceType[];
            }>
              key="form2"
              formRef={formRef2}
              submitter={false}
              validateTrigger="onBlur"
            >
              <EditableProTable<DataSourceType>
                rowKey="id"
                scroll={{
                  x: 960,
                }}
                name="table"
                controlled={true}
                recordCreatorProps={false}
                columns={columns2}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验信息配置">
            <ProForm<{
              table: DataSourceType[];
            }>
              key="form3"
              formRef={formRef3}
              submitter={false}
              validateTrigger="onBlur"
            >
              <EditableProTable<DataSourceType>
                rowKey="id"
                scroll={{
                  x: 960,
                }}
                name="table"
                controlled={true}
                recordCreatorProps={false}
                columns={columns3}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验项目配置">
            <ProForm
              key="form4"
              formRef={formRef4}
              {...formItemLayout}
              layout="horizontal"
              grid={true}
              submitter={false}
            >
              <EditableProTable<DataSourceType>
                rowKey="id"
                name="table"
                controlled={true}
                recordCreatorProps={false}
                columns={columns4}
              />
            </ProForm>
          </BlockContainer>
        </div>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button onClick={close}>关闭</Button>
        <Button type="primary" onClick={previewFill}>
          预览填报页
        </Button>
      </div>
      {/* 预览模板 */}
      <Drawer
        width="85%"
        title="预览模板"
        onClose={() => setPreviewModel(false)}
        open={previewModel}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Preview
          previewPageData={previewPageData}
          close={() => setPreviewModel(false)}
        />
      </Drawer>
    </div>
  );
};

export default Detail;
