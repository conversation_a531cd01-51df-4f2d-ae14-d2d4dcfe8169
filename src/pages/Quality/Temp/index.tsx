/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Popconfirm, Tag } from 'antd';
import { deleteTemplate, getTempList, setTemp } from '@/api/temp';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import { useQualityStore } from '@/store/quality';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Detail from './components/Detail';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { waitTime, waitTimePromise } from '@/utils/helpers';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const [pageSize, setPageSize] = useState(10);
  // 获取table中需要的枚举
  const { templateStatusOnTable } = useQualityStore();

  const actionRef = useRef<ActionType>();

  const { isAdmin } = useInfoStore();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();

  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  /**
   * @TODO 详情
   */
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  // 弹窗类型：  copy-复制新增  add-新增   edit-草稿修改  editing-可使用修改
  const [modelType, setModelType] = useState<string>('add');

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },

    {
      title: '考核类型',
      dataIndex: 'assessmentType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '模板编号',
      dataIndex: 'code',
    },
    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '考核类型',
      dataIndex: 'assessmentTypeLabel',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      hideInSearch: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
    },
    {
      title: '最后修改人',
      dataIndex: 'updateUser',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: templateStatusOnTable,
      render(text, row) {
        return (
          <Tag color={row?.status === 1 ? 'processing' : 'default'}>
            {row.statusStr ?? '暂无数据'}
          </Tag>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 140,
      render: (text, record, _, action) => {
        const btns = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDetailId(record.id);
              setOpenDetail(true);
            }}
          >
            查看
          </Button>,
        ];
        // 草稿
        if (record.status === 0) {
          btns.push(
            <Button
              type="link"
              size="small"
              key="view2"
              onClick={() => {
                setDetailId(record.id);
                setOpenEdit(true);
                setModelType('edit');
              }}
            >
              修改
            </Button>
          );
          btns.push(
            <Popconfirm
              title="删除此模板？"
              onConfirm={() => handleDelete(record.id, 1)}
              okText="确定"
              cancelText="取消"
              key="del"
            >
              <Button type="link" size="small" key="set" danger>
                删除
              </Button>
            </Popconfirm>
          );
        }
        // 可使用
        if (record.status === 1) {
          btns.push(
            <Button
              type="link"
              size="small"
              key="view3"
              onClick={() => {
                setDetailId(record.id);
                setOpenEdit(true);
                setModelType('editing');
              }}
            >
              修改
            </Button>
          );
          btns.push(
            <Button
              type="link"
              size="small"
              key="set"
              onClick={() => {
                setDetailId(record.id);
                setOpenEdit(true);
                setModelType('copy');
              }}
            >
              复制新增
            </Button>
          );
          // 超级管理员权限
          if (isAdmin) {
            btns.push(
              <Popconfirm
                title="强制删除此模板？"
                onConfirm={() => handleDelete(record.id, 2)}
                okText="确定"
                cancelText="取消"
                key="del2"
              >
                <Button type="link" size="small" key="delpro" danger>
                  强制删除
                </Button>
              </Popconfirm>
            );
          }
        }
        return btns;
      },
    },
  ];

  /**
   * @TODO
   */
  const handleSet = async (item: any) => {
    try {
      const { code, msg } = await setTemp({
        id: item.id,
        assessmentType: item.assessmentType,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * @TODO 弹窗标题映射
   */
  const titleMap = useCallback(() => {
    switch (modelType) {
      case 'add':
        return <span>新增模板</span>;
      case 'edit':
        return <span>编辑模板（草稿）</span>;
      case 'editing':
        return <span>编辑模板（可使用）</span>;
      default:
        return <span>复制新增模板</span>;
    }
  }, [modelType]);

  /**
   * @TODO 删除模板
   * @params id  模板id
   * @params type 删除类型，1 用户删除 2 管理员删除
   */
  const handleDelete = async (id: string, type: number) => {
    try {
      const params = { id, type };
      const { code, data, msg } = await deleteTemplate(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            current: params.current,
            size: params.pageSize,
          };
          const { code, rows, total, msg } = await getTempList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="模板管理"
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setDetailId('');
              setOpenEdit(true);
              setModelType('add');
            }}
            type="primary"
          >
            新建
          </Button>,
        ]}
      />
      {/* 新建/编辑模板 */}
      <Drawer
        width="85%"
        title={titleMap()}
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit close={closeEdit} detailId={detailId} modelType={modelType} />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="85%"
        title="模板详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
