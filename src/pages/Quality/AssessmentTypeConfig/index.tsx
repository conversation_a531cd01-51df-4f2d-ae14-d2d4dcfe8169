/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { deleteAssessmentType, getAssessmentTypeList } from '@/api/assessment';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useConfigStore } from '@/store/config';
import { useDictStore } from '@/store/dict';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import Detail from './components/Detail';
import EProTable from '@/components/EProTable';
import PageContainer from '@/components/PageContainer';
import { TTableData } from './type';

const RoleManagement: React.FC = () => {
  // 获取table中需要的枚举
  const { getSysYesNo, sysYesNoOnTable } = useConfigStore();
  useEffect(() => {
    getSysYesNo();
  }, []);
  // 获取table中需要的枚举
  const { sysNormalDisableOnTable, getSysNormalDisable } = useDictStore();
  useEffect(() => {
    getSysNormalDisable();
  }, []);

  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // 绑定表格
  const actionRef = useRef<ActionType>();

  const [pageSize, setPageSize] = useState<number>(10);

  // 新增设置
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [detailType, setDetailType] = useState<string>('add');
  const [detailId, setDetailId] = useState<number>(-1);

  // 表头
  const columns: ProColumns<TTableData>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    // {
    //   title: '字典编码',
    //   dataIndex: 'dictCode',
    //   key: 'dictCode',
    //   hideInSearch: true,
    //   width: 80,
    // },
    {
      title: '考核类型',
      dataIndex: 'dictLabel',
      key: 'dictLabel',
    },
    {
      title: '字典键值',
      dataIndex: 'dictValue',
      key: 'dictValue',
      hideInSearch: true,
    },
    {
      title: '字典排序',
      dataIndex: 'dictSort',
      key: 'dictSort',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: sysNormalDisableOnTable,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      render: (text, record, _, action) => {
        let btns = [
          <a
            key="edit"
            onClick={() => {
              setDetailId(record.dictCode);
              setDetailType('edit');
              setOpenDetail(true);
            }}
          >
            编辑
          </a>,
          <Popconfirm
            title="删除此行？"
            onConfirm={() => handleDelete(record.dictCode)}
            okText="确定"
            cancelText="取消"
            key="del"
          >
            <a className="text-red-500">删除</a>
          </Popconfirm>,
        ];

        return btns;
      },
      key: 'option',
    },
  ];

  /**
   * @TODO 删除设置
   */
  const handleDelete = async (id: number) => {
    try {
      const { code, msg } = await deleteAssessmentType(id + '');
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  return (
    <PageContainer>
      <div>
        <EProTable<TTableData>
          className="spec-table"
          scroll={{
            x: true,
          }}
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params: any = {}, sort, filter) => {
            setLoading(true);
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;
            const { code, rows, total, msg } = await getAssessmentTypeList(
              param
            );
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }
            setLoading(false);
            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
          }}
          rowKey="configKey"
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange(current, size) {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setDetailType('add');
                setOpenDetail(true);
              }}
            >
              新增
            </Button>,
          ]}
          tableAlertRender={false}
          tableAlertOptionRender={false}
        />
      </div>

      {/* 设置详情 */}
      <Detail
        open={openDetail}
        closeDetail={() => {
          setOpenDetail(false);
          tableReload();
        }}
        detailType={detailType}
        detailId={detailId}
      />
    </PageContainer>
  );
};

export default RoleManagement;
