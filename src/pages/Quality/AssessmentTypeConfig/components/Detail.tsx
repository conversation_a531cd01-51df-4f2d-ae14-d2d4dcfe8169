import { useCallback, useEffect, useRef, useState } from 'react';
import { message, Modal } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import {
  addAssessmentType,
  getAssessmentTypeDetail,
  updateAssessmentType,
} from '@/api/assessment';
import { getParentDeptNew } from '@/api/department';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useConfigStore } from '@/store/config';
import { useDictStore } from '@/store/dict';
import {
  ProForm,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';

const FormInfoInit = {
  roleName: null,
  status: 0,
  menuIds: [],
  roleKey: null,
  roleType: '01',
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: () => void;
  detailType: string;
  detailId: number;
};

const RoleDetail: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  detailType,
  detailId,
}) => {
  // 获取table中需要的枚举
  const { getSysYesNo, sysYesNoOnForm } = useConfigStore();
  useEffect(() => {
    getSysYesNo();
  }, []);
  const {
    sysNormalDisableOnTable,
    sysNormalDisableOnForm,
    getSysNormalDisable,
  } = useDictStore();
  useEffect(() => {
    getSysNormalDisable();
  }, []);
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  // title
  const detailTitleComputed = useCallback(() => {
    if (detailType === 'add') {
      return '新增考核类型';
    } else if (detailType === 'edit') {
      return '编辑考核类型';
    } else {
      return '考核类型详情';
    }
  }, [detailType]);

  // 表单实例
  const formRef = useRef<FormInstance>();

  const close = () => {
    closeDetail();
    formRef.current?.resetFields();
  };

  /**
   * @TODO 获取角色详情
   */
  const getDetail = async () => {
    try {
      if (detailType !== 'add') {
        const { code, data, msg } = await getAssessmentTypeDetail(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          // data.status = Number(data.status);
          formRef.current?.setFieldsValue(data);
        } else {
          message.error(msg);
        }
      } else {
        formRef.current?.resetFields();
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    getDetail();
  }, [open]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = { ...values, dictType: 'assessment_type' };
    setLoading(true);
    try {
      const { code, msg } = await saveFetch(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  const saveFetch = (params: any) => {
    if (detailType === 'add') {
      return addAssessmentType(params);
    } else {
      params.dictCode = detailId;
      return updateAssessmentType(params);
    }
  };

  return (
    <Modal
      className="min-w-[600px]"
      title={detailTitleComputed()}
      width="40%"
      open={open}
      onCancel={close}
      onOk={() => formRef.current?.submit()}
      confirmLoading={loading}
    >
      <ProForm
        className="mt-6"
        submitter={false}
        formRef={formRef}
        initialValues={FormInfoInit}
        readonly={detailType === 'view'}
        onFinish={handleSave}
        layout="horizontal"
        onValuesChange={(_, values: any) => {
          for (const key in values) {
            if (typeof values[key] === 'string') {
              values[key] = values[key].trim();
            }
          }
          formRef.current?.setFieldsValue(values);
        }}
      >
        <ProFormTreeSelect
          name="depts"
          label="所在部门"
          placeholder="请选择"
          rules={[{ required: true, message: '请选择' }]}
          request={async () => {
            let result: any[] = [];
            // const { code, data, msg } = await getParentDept();
            const { code, data, msg } = await getParentDeptNew();
            if (code === codeDefinition.QUERY_SUCCESS) {
              result = [...data];
            } else {
              message.error(msg);
            }
            return result;
          }}
          fieldProps={{
            fieldNames: { value: 'id' },
            multiple: true,
          }}
        />
        <ProFormText
          label="考核类型"
          name="dictLabel"
          rules={[{ required: true, message: '请输入考核类型' }]}
          placeholder="请输入考核类型"
        />
        <ProFormText
          label="数据键值"
          name="dictValue"
          rules={[{ required: true, message: '请输入数据键值' }]}
          placeholder="请输入数据键值"
        />
        <ProFormDigit
          label="显示排序"
          name="dictSort"
          placeholder="请输入显示排序"
          rules={[{ required: true, message: '请输入入显示排序' }]}
        />
        {/* <ProFormSelect
          label="回显样式"
          name="listClass"
          placeholder="请输入回显样式"
          options={[
            {
              value: 'default',
              label: '默认',
            },
            {
              value: 'primary',
              label: '主要',
            },
            {
              value: 'success',
              label: '成功',
            },
            {
              value: 'info',
              label: '信息',
            },
            {
              value: 'warning',
              label: '警告',
            },
            {
              value: 'danger',
              label: '危险',
            },
          ]}
        /> */}
        <ProFormRadio.Group
          label="状态"
          name="status"
          options={sysNormalDisableOnForm}
          rules={[{ required: true, message: '请选择状态' }]}
        />
        <ProFormTextArea label="备注" name="remark" placeholder="请输入备注" />
      </ProForm>
    </Modal>
  );
};

export default RoleDetail;
