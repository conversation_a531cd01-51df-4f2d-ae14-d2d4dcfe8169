/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Modal } from 'antd';
import { getFillList, receptFill } from '@/api/fill';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();

  const [activeKey, setActiveKey] = useState<string>('1');

  const [pageSize, setPageSize] = useState<number>(10);

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailInfo, setDetailInfo] = useState<any>({});
  const [detailReadOnly, setDetailReadOnly] = useState(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
    },
    {
      hideInTable: activeKey === '2' || activeKey === '3',
      title: '结束日期',
      dataIndex: 'endDate',
      hideInSearch: true,
    },
    {
      hideInTable: activeKey === '1',
      title: '提交日期',
      dataIndex: 'fillingDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      hideInTable: activeKey === '1' || activeKey === '2',
      title: '考核得分',
      dataIndex: 'score',
      hideInSearch: true,
      align: 'right',
    },
    {
      hideInTable: activeKey === '1' || activeKey === '2',
      title: '考核得分结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
    },
    {
      // hideInTable: activeKey === '2',
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        let btns: any = [];
        // if (activeKey === '0') {
        //   btns = [
        //     <Button
        //       type="link"
        //       size="small"
        //       key="view"
        //       onClick={() => {
        //         receptTask(record);
        //       }}
        //     >
        //       确认任务
        //     </Button>,
        //   ];
        // } else
        if (activeKey === '1' && record.status === '1') {
          btns = [
            <Button
              type="link"
              size="small"
              key="write"
              onClick={() => {
                setDetailInfo(record);
                setDetailReadOnly(false);
                setOpenEdit(true);
              }}
            >
              检验填报
            </Button>,
          ];
        } else if (activeKey === '2' || activeKey === '4') {
          btns = [
            <Button
              type="link"
              size="small"
              key="view"
              onClick={() => {
                setDetailInfo(record);
                setDetailReadOnly(true);
                setOpenEdit(true);
              }}
            >
              详情
            </Button>,
          ];
        }
        return btns;
      },
    },
  ];
  const receptTask = async (row: any) => {
    Modal.confirm({
      title: '提示',
      content: '确认任务？',
      onOk: async () => {
        try {
          const { code, msg } = await receptFill({ id: row.id, status: 1 });
          if (code === codeDefinition.QUERY_SUCCESS) {
            message.success(QUERY_SUCCESS_MSG);
            tableReload();
          } else {
            message.error(msg);
          }
        } catch (error) {
          throw new Error(`Error: ${error}`);
        }
      },
    });
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    setParamsCache('refreshCount');
    tableReload();
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '1': '-',
    '2': '-',
    '4': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '1',
                label: <span>待填报（{counts['1']}）</span>,
              },
              {
                key: '2',
                label: <span>已填报（{counts['2']}）</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            status: activeKey,
            pageNum: params.current,
            pageSize: params.pageSize,
            current: undefined,
          };
          const { code, rows, total, msg } = await getFillList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
          const paramsStr = JSON.stringify({
            ...params,
            current: undefined,
            pageSize: undefined,
          });
          if (paramsCache !== paramsStr) {
            setParamsCache(paramsStr);
            let s1 = '';
            let s2 = '';
            if (activeKey === '1') {
              s1 = '2';
              s2 = '4';
            }
            if (activeKey === '2') {
              s1 = '1';
              s2 = '4';
            }
            if (activeKey === '4') {
              s1 = '1';
              s2 = '2';
            }
            const { total: totalAnother1 } = await getFillList({
              ...params,
              status: s1,
              pageNum: 1,
              pageSize: 0,
              current: undefined,
            });
            const { total: totalAnother2 } = await getFillList({
              ...params,
              status: s2,
              pageNum: 1,
              pageSize: 0,
              current: undefined,
            });
            setCounts({
              [activeKey]: total ?? 0,
              [s1]: totalAnother1 ?? 0,
              [s2]: totalAnother2 ?? 0,
            });
          }

          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="检验管理"
      />
      {/* 填报 */}
      <Drawer
        width="98%"
        title={detailReadOnly ? '检验详情' : '检验填报'}
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#eee] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailInfo={detailInfo}
          readOnly={detailReadOnly}
        />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
