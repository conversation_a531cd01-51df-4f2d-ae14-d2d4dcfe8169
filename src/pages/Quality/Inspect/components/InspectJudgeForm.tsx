/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useImperativeHandle } from 'react';
import React, { useRef, useState } from 'react';
import { Card, Col, Row, Table } from 'antd';
import type { TableProps } from 'antd';
import { useInfoStore } from '@/store';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { evaluate } from 'mathjs';

type TEditProps = {
  id?: any;
  allData?: any;
  showType?: any;
  onSubmit?: (params: any) => void;
  onSave?: (params: any) => void;
  onRef?: any;
  readonly: boolean;
};
type DataSourceType = any;

const InspectJudgeForm: React.FC<TEditProps> = ({
  id,
  allData,
  showType,
  onRef,
  onSubmit,
  onSave,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        setIsSubmitType('submit');
        formRef.current?.submit();
        setTemporaryFormData({});
      },
      handleSave: () => {
        setIsSubmitType('save');
        formRef.current?.submit();
        setTemporaryFormData({});
      },
    };
  });
  // 保存or提交
  const [isSubmitType, setIsSubmitType] = useState<string>('submit');

  const formRef = useRef<ProFormInstance<any>>();
  const { userInfo, setUserInfo } = useInfoStore();
  // 临时表单值
  const [temporaryFormData, setTemporaryFormData] = useState<any>({});

  const [columns, setColumns] = useState<TableProps<DataSourceType>['columns']>(
    [
      {
        title: '样本编号',
        dataIndex: 'sampleCode',
        key: 'sampleCode',
      },
      //   {
      //     title: '参考结果',
      //     dataIndex: 'sampleValue',
      //     key: 'sampleValue',
      //   },
      {
        title: '考核项目',
        dataIndex: 'itemName',
        key: 'itemName',
      },
      //   {
      //     title: '判定标准',
      //     dataIndex: 'conditionThree', // todo
      //     key: 'conditionThree',
      //     render: (text: any, record: any) => {
      //       return (
      //         <span>
      //           {record.conditionThree ? record.conditionThree : '无判定标准'}
      //         </span>
      //       );
      //     },
      //   },
      //   {
      //     title: '系统初判结果',
      //     dataIndex: 'systemResult',
      //     key: 'systemResult',
      //   },
      //   {
      //     title: '人工复判',
      //     dataIndex: 'peopleResult',
      //     key: 'peopleResult',
      //   },
      {
        title: '得分',
        dataIndex: 'score',
        key: 'score',
      },
    ]
  );

  // 评判结果列表数据整合
  const [scoreList, setScoreList] = useState<any>([]);
  const [tips, setTips] = useState<any>('');

  const init = () => {
    // 评判结果列表数据整合
    const _scoreList: any = [];
    let _kindLength = 0;
    allData.valueTwo?.forEach((item: any) => {
      _kindLength = item.score?.length;
      let _tips: any = [];
      item.score?.forEach((sItem: any) => {
        _scoreList.push(sItem);
        _tips += `${sItem.itemName} ${sItem.ratio}%；`;
      });
      setTips(_tips);
    });
    columns![0].onCell = (_, index) => ({
      rowSpan: index! % _kindLength ? 0 : _kindLength,
    });
    // columns![1].onCell = (_, index) => ({
    //   rowSpan: index! % _kindLength ? 0 : _kindLength,
    // });
    setColumns(columns);
    setScoreList(_scoreList);
    // 回填表单数据
    formRef.current?.setFieldsValue({
      ...allData.main,
      judgeDate: allData.main.judgeDate ?? dayjs(),
      judge: allData.main.judge ?? userInfo?.user?.nickName,
      stuffScore: allData.main.stuffScore
        ? allData.main.stuffScore
        : parseFloat(allData.qaTaskScoreRule.docScore) === 0
        ? 0
        : null,
    });
    // 优先取用户输入的值
    if (temporaryFormData.stuffScore) {
      formRef.current?.setFieldValue(
        'stuffScore',
        temporaryFormData.stuffScore
      );
    }
    if (temporaryFormData.scoreRemark) {
      formRef.current?.setFieldValue(
        'scoreRemark',
        temporaryFormData.scoreRemark
      );
    }
    if (temporaryFormData.judge) {
      formRef.current?.setFieldValue('judge', temporaryFormData.judge);
    }
    if (temporaryFormData.judgeDate) {
      console.log('temporaryFormData.judgeDate', temporaryFormData.judgeDate);
      formRef.current?.setFieldValue(
        'judgeDate',
        dayjs(temporaryFormData.judgeDate).format('YYYY-MM-DD HH:mm:ss')
      );
    }
  };

  useEffect(() => {
    if (allData && allData.main) {
      init();
    }
  }, [allData]);

  /**
   * @TODO 新增/编辑
   */
  const handleSaveSubmit = async (values: any) => {
    const info = getResultInfos();
    const params = {
      ...values,
      ...info,
      judgeDate: dayjs(values.judgeDate).format('YYYY-MM-DD HH:mm:ss'),
      score: info.totalScore,
    };
    if (isSubmitType === 'submit' && onSubmit) {
      onSubmit(params);
    } else {
      onSave && onSave(params);
    }
  };

  const getResultInfos = () => {
    const stuffScore = formRef.current?.getFieldValue('stuffScore');
    const info = (scoreList as DataSourceType[]).reduce(
      (pre, item) => {
        return {
          totalScore: evaluate(`${pre.totalScore}+${item?.score || 0}`),
        };
      },
      { totalScore: 0, result: '' }
    );
    info.totalScore = evaluate(`${info.totalScore}+${stuffScore || 0}`);
    if (
      info.totalScore < parseFloat(allData?.qaTaskScoreRule?.qualifiedMinScore)
    ) {
      info.result = '不合格';
    }
    if (
      info.totalScore >=
        parseFloat(allData?.qaTaskScoreRule?.qualifiedMinScore) &&
      info.totalScore < parseFloat(allData?.qaTaskScoreRule?.fineMinScore)
    ) {
      info.result = '合格';
    } else if (
      info.totalScore >= parseFloat(allData?.qaTaskScoreRule?.fineMinScore)
    ) {
      info.result = '优秀';
    }
    return info;
  };

  return (
    <>
      <ProForm<{
        table: DataSourceType[];
      }>
        formRef={formRef}
        layout="horizontal"
        submitter={false}
        onFinish={handleSaveSubmit}
        readonly={showType === '详情'}
        //@ts-ignore
        onValuesChange={(values) => {
          const formData = formRef.current?.getFieldsValue();
          setTemporaryFormData(formData);
        }}
      >
        <Table
          size="small"
          key={'table'}
          rowKey={'id'}
          columns={columns}
          pagination={false}
          dataSource={scoreList}
        />
        <ProFormDependency name={['stuffScore']}>
          {({ stuffScore }) => {
            const info = getResultInfos();
            return (
              <>
                <Card
                  className="mt-4"
                  bodyStyle={{
                    padding: '20px 20px 0 20px',
                  }}
                >
                  <Row gutter={16}>
                    <Col className="gutter-row" span={12}>
                      <ProFormDigit
                        label="资料得分"
                        name="stuffScore"
                        required
                        max={
                          allData?.qaTaskScoreRule?.docScore
                            ? (parseFloat(
                                allData?.qaTaskScoreRule?.docScore
                              ) as any)
                            : null
                        }
                        rules={[{ required: true, message: '请输入资料得分' }]}
                      />
                    </Col>
                    <Col className="pt-1 text-red-500" span={12}>
                      注：样品得分{allData?.qaTaskScoreRule?.sampleScore}
                      分，资料得分{allData?.qaTaskScoreRule?.docScore}
                      分；{tips}
                    </Col>
                    <Col className="gutter-row" span={24}>
                      <ProFormTextArea
                        label="资料评定备注"
                        name="scoreRemark"
                      />
                    </Col>
                  </Row>
                </Card>

                <Row gutter={16} className="mt-6">
                  <Col className="gutter-row" span={6}>
                    <div className="pt-1">
                      总得分：
                      {showType === '详情'
                        ? allData?.main?.score
                        : Number.isInteger(info.totalScore) ? info.totalScore.toString() : info.totalScore.toFixed(2)}
                    </div>
                  </Col>
                  <Col className="gutter-row" span={6}>
                    <div className="pt-1">
                      考核结论：
                      {showType === '详情'
                        ? allData?.main?.result
                        : info.result}
                    </div>
                  </Col>
                  <Col className="gutter-row" span={6}>
                    <ProFormText
                      label="评判人"
                      name="judge"
                      required
                      rules={[
                        {
                          required: isSubmitType === 'submit',
                          message: '这是必填项',
                        },
                      ]}
                    />
                  </Col>
                  <Col className="gutter-row" span={6}>
                    <ProFormDatePicker
                      label="评判日期"
                      width="xl"
                      name="judgeDate"
                      required
                      rules={[
                        {
                          required: isSubmitType === 'submit',
                          message: '这是必填项',
                        },
                      ]}
                    />
                  </Col>
                </Row>
              </>
            );
          }}
        </ProFormDependency>
      </ProForm>
    </>
  );
};

export default InspectJudgeForm;
