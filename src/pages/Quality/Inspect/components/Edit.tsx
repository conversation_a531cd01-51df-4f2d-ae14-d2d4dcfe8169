/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Collapse, message, Modal } from 'antd';
import { getFillDetail, saveFill } from '@/api/fill';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { clone } from 'lodash';
import BaseForm from '../../Task/components/BaseForm';
import BlockContainer from '@/components/BlockContainer';
import JsonForm from '@/components/JsonForm';
import JsonTable from '@/components/JsonTable';
import './Edit.less';
import InspectJudgeForm from './InspectJudgeForm';

//结果评判
const { confirm } = Modal;

type TEditProps = {
  close: () => void;
  detailInfo?: any;
  readOnly?: boolean;
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailInfo,
  readOnly = false,
}) => {
  const [loadingSave, setLoadingSave] = useState<boolean>(false);
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);

  // 整个页面数据
  const [baseInfo, setBaseInfo] = useState<any>({});

  const [form1Data, setForm1Data] = useState([]);

  const [form2List, setForm2List] = useState([]);

  const init = async () => {
    if (detailInfo && detailInfo.id) {
      try {
        const { code, data, msg } = await getFillDetail(detailInfo.id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          const { taskMain, itemInfos, qaTaskScoreRule, valueOne, valueTwo } =
            data;
          setBaseInfo(data);
          setForm1Data(valueOne);
          // 2
          setForm2List(valueTwo);
        } else {
          message.error(msg);
        }
      } catch (error) {
        console.error(error);
        throw new Error(`Error: ${error}`);
      }
    }
  };
  useEffect(() => {
    init();
  }, [detailInfo]);

  const formRef1 = useRef<any>(null);
  // 这里通过 useRef 创建一个数组
  const listRefs = useRef<Array<any | null>>([]);

  /**
   * @TODO 循环判断校验
   * @param 表单所有必填项数组
   * @param 操作类型 1-保存  3-提交
   */
  const circularJudgment = async (listRefsArr: any, type: number) => {
    const validationPromises = listRefsArr.current.map(async (item: any) => {
      try {
        // 校验
        if (type === 2) await item?.current?.validateFields();
        const temp = await item?.current.getFormInfo()[0];
        return temp;
      } catch (error: any) {
        // 抛出错误以中断Promise.
        let msg = '';
        let isFile = false;
        let isCharts = false;
        if (error?.errorFields) {
          // 表单必填校验
          // isFile = error?.errorFields.some((item: any) => item.errors![0] === "此文件必须上传")
          // 其他项
          isCharts = error?.errorFields.some(
            (item: any) => item.errors![0] === '此项为必填项'
          );
          if (isCharts && type === 2) {
            msg = '请完善样本检验信息必填项';
          } else {
            msg = '请上传必填文件';
          }
          // 文件上传中校验
        } else {
          msg = error;
        }
        throw msg;
      }
    });
    return await Promise.all(await validationPromises);
  };

  /**
   * @TODO 循环判断校验
   * @type 1-保存  3-提交
   */
  const handleSave = async (type = 1) => {
    try {
      // 检验基本信息-表单校验及数据
      type === 2 && (await formRef1.current.formValidateFields());
      let inspectionBasicInformation = await formRef1.current.getFormInfo();
      // 样本检验信息校验-表格校验及数据
      let sampleInspectionInformation = await circularJudgment(listRefs, type);
      const newSampleInspectionInformation = transformData(
        sampleInspectionInformation
      );
      // 处理样本检验信息数据
      let oldValueTwo: any = clone(form2List);
      for (let i = 0; i < oldValueTwo.length; i++) {
        for (let j = 0; j < newSampleInspectionInformation[i].length; j++) {
          let tempArr = [];
          for (
            let k = 0;
            k < newSampleInspectionInformation[i][j].length;
            k++
          ) {
            let newObj: any = { ...oldValueTwo[i].items[j].valueObject[k] };
            for (const key in newSampleInspectionInformation[i][j][k]) {
              newObj[key] = newSampleInspectionInformation[i][j][k][key];
            }
            if (newObj.value === undefined) newObj.value = null;
            tempArr.push(newObj);
          }
          oldValueTwo[i].items[j].valueObject = [...tempArr];
        }
      }
      // return
      let params = {
        taskId: detailInfo.id,
        ...baseInfo,
        valueOne: inspectionBasicInformation,
        valueTwo: oldValueTwo,
        operationType: type,
      };
      // console.log("提交任务表格数据", oldValueTwo)
      // return
      if (type === 1) {
        setLoadingSave(true);
        const { code, msg } = await saveFill(params);
        if (code === codeDefinition.QUERY_SUCCESS) {
          message.success(QUERY_SUCCESS_MSG);
        } else {
          message.error(msg);
        }
      } else {
        // 判定样本是否全部填报
        const isCanUpdate = form2List.some(
          (item: any) => item.canUpdate === false
        );
        if (isCanUpdate) {
          message.warning('当前有未填报的样品，不可提交');
        } else {
          confirm({
            title: '确认操作?',
            icon: <ExclamationCircleFilled />,
            content: '请确认是否提交检验填报？',
            onOk: async () => {
              let params = {
                taskId: detailInfo.id,
                ...baseInfo,
                valueOne: inspectionBasicInformation,
                valueTwo: oldValueTwo,
                operationType: type,
              };
              setLoadingSubmit(true);
              const { code, msg } = await saveFill(params);
              if (code === codeDefinition.QUERY_SUCCESS) {
                close();
                message.success(QUERY_SUCCESS_MSG);
              } else {
                let tempMessage = '';
                if (msg === '样本检验信息不足，请重新核对数据') {
                  tempMessage = '有样本还未接收，请先接收所有样本';
                } else {
                  tempMessage = msg;
                }
                message.error(tempMessage);
                setLoadingSave(false);
                setLoadingSubmit(false);
              }
            },
          });
        }
      }
    } catch (e: any) {
      let tempMsg = e?.message ?? e;
      let msg = '';
      if (typeof tempMsg === 'string' && tempMsg.indexOf(':') > -1) {
        const msgList = tempMsg.split(':');
        msg = msgList[msgList.length - 1];
      } else if (e.errorFields) {
        msg = '请完善检验基本信息必填项';
      } else {
        msg = tempMsg;
      }
      message.error(msg);
    } finally {
      setLoadingSave(false);
      setLoadingSubmit(false);
    }
  };

  /* 数组转换 */
  const transformData = (originalData: any[][]) => {
    const transformedData: any[][] = [];
    originalData.forEach((group, groupIndex) => {
      const innerArray: any[][] = [];
      group.forEach((item) => {
        const valueObject: any[] = [];
        for (const key in item) {
          if (key !== 'id' && key !== 'itemName' && key !== 'valueObject') {
            valueObject.push({
              templateInstanceItemInfoId: parseInt(key),
              value: item[key],
            });
          }
        }
        innerArray.push(valueObject);
      });
      transformedData.push(innerArray);
    });
    return transformedData;
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        {/* 基本信息 */}
        <BaseForm
          readonly={true}
          id={baseInfo.taskMain?.id}
          isShowChar={false}
          detailInfo={baseInfo.taskMain}
        />
        <BlockContainer title="检验基本信息" className="mt-4">
          <JsonForm
            formDataList={form1Data}
            onRef={formRef1}
            fileCheckTooltipTitle="检验基本信息"
            readOnly={readOnly}
          />
        </BlockContainer>
        <BlockContainer className="mt-4" title="样本检验信息">
          {form2List.map((item: any, i: any) => {
            return (
              <div key={'t' + i} className="mb-3">
                <Collapse
                  size="small"
                  expandIconPosition="end"
                  defaultActiveKey={[item.canUpdate || i === 0 ? i : null]}
                  items={[
                    {
                      key: i,
                      label: (
                        <div>
                          样本编号：{item.sampleCode}
                          {(item.status === '0' || item.status === '1') && (
                            <span>
                              （
                              <span className="font-extrabold">
                                {item.canUpdate ? '已接收' : '未接收'}
                              </span>
                              ）
                            </span>
                          )}
                        </div>
                      ),
                      forceRender: true,
                      //  动态表格
                      children: (
                        <JsonTable
                          formDataList={baseInfo?.itemInfos}
                          formRef={
                            ((ref: any): void =>
                              (listRefs.current[i] = ref)) as any
                          }
                          readOnly={!item.canUpdate || readOnly}
                          projectDataList={item.items}
                          canUpdate={item.canUpdate}
                        />
                      ),
                    },
                  ]}
                />
              </div>
            );
          })}
        </BlockContainer>
        {/* 结果评判 */}
        {baseInfo?.main?.status === '4' ? (
          <BlockContainer title="结果评判" className="mt-4">
            <InspectJudgeForm
              showType="详情"
              readonly={true}
              allData={baseInfo}
            />
          </BlockContainer>
        ) : null}
      </div>

      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        {!readOnly ? (
          <>
            <Button onClick={close} type="default">
              关闭
            </Button>
            <Button
              onClick={() => {
                handleSave(1);
              }}
              type="default"
              loading={loadingSave}
            >
              保存
            </Button>
            <Button
              onClick={() => {
                handleSave(2);
              }}
              type="primary"
              loading={loadingSubmit}
            >
              提交
            </Button>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default MenuDetail;
