/*
 * @Author: LGX
 * @Date: 2024-03-05 10:39:26
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-25 23:10:45
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\Inspect\components\View.tsx
 * @Description: 检验填报详情
 * 
 */
import { useEffect, useRef, useState, MutableRefObject } from 'react';
import { Button, message, Modal, Collapse } from 'antd';
import type { UploadProps } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { getFillDetail, receptFill, saveFill } from '@/api/fill';
import { getTempByType } from '@/api/temp';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import {
  BetaSchemaForm,
  ProFormUploadButton,
  ProFormColumnsType,
  EditableProTable,
  ProFormInstance
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import { getIconByName } from '@/utils/upload';
import { getFileTypeByName } from '@/utils/upload';
import JsonForm from '@/components/JsonForm';
import JsonTable from '@/components/JsonTable';
import './Edit.less';

const { confirm } = Modal;

type TEditProps = {
  close: () => void;
  detailInfo?: any;
  readOnly?: boolean;
};

const InspectDetail: React.FC<TEditProps> = ({
  close,
  detailInfo,
  readOnly = false,
}) => {
  const { token } = useTokenStore();
  const [loading, setLoading] = useState<boolean>(false);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const uploadProps: UploadProps | any = {
    action: uploadFiles,
    fieldProps: {
      disabled: readOnly,
      headers: { Authorization: `Bearer ${token}` },
      listType: 'picture-card',
      // className: 'upload-list-inline',
      async onPreview(file: any) {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.data &&
          file.response.data.ossId
        ) {
          const type = getFileTypeByName(file.response.data.fileName);
          if (type === 'Image') {
            const d = await getFileData(file.response.data.ossId);
            setIsShowFileData({
              name: file.response.data.fileName,
              url: d,
              ossId: file.response.data.ossId
            });
            setIsShowFileView(true);
          } else {
            downloadFile(file.response.data.ossId, file.response.data.fileName);
          }
        }
      },
      iconRender: (file: any) => {
        return (
          <img
            src={getIconByName(file.name)}
            className="!w-[40px] !h-[40px] m-auto mt-2"
            alt="logo"
          />
        );
      },
    },
    onChange(info: any) {
      const { file } = info;
      if (file.status === 'done' || file.status === 'removed') {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.code !== 200
        ) {
          message.error(file.response.msg);
        } else {
          message.success(file.response.msg);
        }
      }
    },
  };

  function setup(orgData: any[]) {
    return orgData.map((item: any) => {
      if (item.inputBox === 'text') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
          fieldProps: {
            maxLength: 200,
          }
        };
      } else if (item.inputBox === 'select') {
        const enums: any = {};
        if (item.enumItems) {
          item.enumItems.split(',').map((en: any) => {
            enums[en] = {
              text: en,
            };
          });
        }
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'select',
          valueEnum: enums,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'digit') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'digit',
          fieldProps: {
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'calendar') {
        return {
          readonly: readOnly,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'date',
          fieldProps: {
            format: 'YYYY-MM-DD',
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'file') {
        let fileList = [];
        try {
          fileList = item.fieldValue
            ? JSON.parse(item.fieldValue).map((item: any) => {
              return {
                uid: item.id,
                name: item.name,
                status: 'done',
                type: 'application/msword',
                url: item.id,
                response: {
                  data: {
                    fileName: item.name,
                    ossId: item.id,
                  },
                },
              };
            })
            : [];
        } catch (error) {
          fileList = [];
        }
        return {
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: fileList,
          colProps: { span: 24 },
          formItemProps: {
            labelWrap: true,
            className: 'inspect-upload-style',
            labelCol: { span: 1.5 },
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
          renderFormItem: () => (
            <ProFormUploadButton
              {...uploadProps}
              name={'f' + item.id}
              wrapperCol={{ span: 24 }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </ProFormUploadButton>
          ),
        };
      }
    });
  }
  const [baseInfo, setBaseInfo] = useState<any>({});
  const [form1Data, setForm1Data] = useState([]);
  const [form2List, setForm2List] = useState([]);

  const init = async () => {
    if (detailInfo && detailInfo.id) {
      try {
        if (readOnly) {
          const {
            code: code1,
            data: data1,
            msg: msg1,
          } = await getFillDetail(detailInfo.id);
          if (code1 === codeDefinition.QUERY_SUCCESS) {
            setBaseInfo(data1);
            const form1 = data1.inspectionBasicInformation;
            const f1: any = setup(form1);
            setForm1Data(f1);
            // 2
            const form2s: any = [];
            data1.sampleInspectionInformation.map((item: any) => {
              const row = { ...item };
              const form2 = item.sampleInspectionInformationList;
              const f2: any = setup(form2);
              row.formInfo = f2;
              form2s.push(row);
            });
            setForm2List(form2s);
            /* 处理表格数据 */

          } else {
            message.error(msg1);
          }
        } else {
          const { code, data, msg } = await getTempByType({
            assessmentType: detailInfo.assessmentType,
            qaFillingTaskId: detailInfo.id,
          });
          if (code === codeDefinition.QUERY_SUCCESS) {
            setBaseInfo(data);
            const form1 = data.inspectionBasicInformation;
            setForm1Data(form1);
            // 2
            const form2: any = [];
            data.sampleInspectionInformation.map((item: any) => {
              const row = { ...item };
              row.formInfo = item.sampleInspectionInformationList;
              form2.push(row);
            });
            setForm2List(form2);
          } else {
            message.error(msg);
          }
        }
      } catch (error) {
        console.error(error);
        throw new Error(`Error: ${error}`);
      }
    }
  };
  useEffect(() => {
    init();
  }, [detailInfo]);

  type DataItem = {
    name: string;
    state: string;
  };


  const formRef1 = useRef<any>(null);
  // 这里通过 useRef 创建一个数组
  const listRefs = useRef<Array<any | null>>([]);

  /** 
   * @TODO 循环判断校验
   * @param 表单所有必填项数组
   * @param 操作类型 1-保存  3-提交
   */
  const circularJudgment = async (listRefsArr: any, type: number) => {
    const validationPromises = listRefsArr.current.map(async (item: any) => {
      try {
        // 校验
        if (type === 2) await item?.current?.validateFields();
        const temp = await item?.current.getFormInfo()[0];
        return temp
      } catch (error) {
        // 抛出错误以中断Promise.all
        throw new Error(`Error: ${error}`);
      }
    });
    return await Promise.all(await validationPromises);
  }

  const columns: ProFormColumnsType<DataItem>[] = [
    {
      title: '列表',
      valueType: 'formList',
      dataIndex: 'list',
      initialValue: [{ state: 'all', title: '标题' }],
      colProps: {
        xs: 24,
        sm: 12,
      },
      columns: [
        {
          valueType: 'group',
          columns: [
            {
              title: '状态',
              dataIndex: 'state',
              valueType: 'select',
              colProps: {
                xs: 24,
                sm: 12,
              },
              width: 'xs',
              valueEnum: {
                1: '未开始',
                2: '进行中',
                3: '已结束',
              },
            },
            {
              title: '标题',
              dataIndex: 'title',
              width: 'md',
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
              },
              colProps: {
                xs: 24,
                sm: 12,
              },
            },
          ],
        },
        {
          valueType: 'dateTime',
          initialValue: new Date(),
          dataIndex: 'currentTime',
          width: 'md',
        },
      ],
    },
  ];

  const handleSave = async (type: 1 | 2) => {
    try {

      // 检验基本信息-表单校验及数据
      let inspectionBasicInformation = await formRef1.current.getFormInfo()
      // 样本检验信息校验-表格校验及数据
      let sampleInspectionInformation = await circularJudgment(listRefs, type)
      if (sampleInspectionInformation.length && typeof sampleInspectionInformation[0] === "string") {
        message.warning(sampleInspectionInformation)
        return
      } else {
        sampleInspectionInformation = sampleInspectionInformation[0]
      }
      confirm({
        title: '确认操作?',
        icon: <ExclamationCircleFilled />,
        content: '请确认是否提交检验填报？',
        onOk: async () => {
          let params = {
            taskId: detailInfo.id,
            ...baseInfo,
            inspectionBasicInformation,
            sampleInspectionInformation,
          };
          console.log("请求参数", params);
          return
          setLoading(true);
          const { code, msg } = await saveFill(params);
          if (code === codeDefinition.QUERY_SUCCESS) {
            if (type === 2) {
              const { code, msg } = await receptFill({
                id: detailInfo.id,
                status: 2,
              });
              if (code === codeDefinition.QUERY_SUCCESS) {
                message.success(QUERY_SUCCESS_MSG);
                close();
              } else {
                message.error(msg);
              }
            }
          } else {
            message.error(msg);
          }
          setLoading(false);
        },
      });

    } catch (e: any) {
      console.error(e);
      if (e?.message === "Error: 文件上传错误") {
        message.warning('样本检验信息表格，有上传未成功的文件');
      } else {
        message.warning('请完善表单');
      }
    }
  };
  return (
    <div className="flex flex-col h -full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="检验基本信息">
          <JsonForm formDataList={form1Data} onRef={formRef1} fileCheckTooltipTitle="检验基本信息" />
        </BlockContainer>
        <div className="mt-4">
          <BlockContainer title="样本检验信息" bg="#F5F5F5">
            {form2List.map((item: any, i: any) => {
              return (
                <div key={'t' + i} className="mb-3">
                  <Collapse
                    size="small"
                    expandIconPosition="end"
                    defaultActiveKey={[0]}
                    items={[
                      {
                        key: i,
                        label: `样本编号：${item.sampleNumber}`,
                        //  动态表单
                        children: <JsonTable formDataList={item?.formInfo} formRef={
                          ((ref: any): void => (listRefs.current[i] = ref)) as any
                        } readOnly={i !== 0} />
                      },
                    ]}
                  />
                </div>
              );
            })}
          </BlockContainer>
        </div>
      </div>
      {!readOnly ? (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleSave(1);
            }}
            type="default"
            loading={loading}
          >
            保存为草稿
          </Button>
          <Button
            onClick={() => {
              handleSave(2);
            }}
            type="primary"
            loading={loading}
          >
            保存并提交
          </Button>
        </div>
      ) : undefined}
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
    </div>
  );
};

export default InspectDetail;
