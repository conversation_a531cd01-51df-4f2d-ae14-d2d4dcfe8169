/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { addDownloadTask, getQATaskList } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { useDictStore } from '@/store/dict';
import { useQualityStore } from '@/store/quality';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';

type TEditProps = {
  close: () => void;
};

const TaskEdit: React.FC<TEditProps> = ({ close }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(10);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const { assessmentTaskTypeEnums, getAssessmentTaskTypeEnums } =
    useDictStore();

  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'particularYear',
      hideInSearch: true,
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      valueType: 'select',
      fieldProps: {
        options: assessmentTaskTypeEnums,
      },
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
      width: 200,
    },
    {
      title: '使用模版名称',
      dataIndex: 'templateName',
      hideInSearch: true,
    },
  ];

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const handleExport = async () => {
    if (selectedRowKeys.length === 0) {
      messageApi.warning('请先选择要导出的任务');
      return;
    }

    setLoading(true);
    try {
      const { code, msg } = await addDownloadTask(
        selectedRowKeys[0].toString()
      );
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success('新增导出任务成功');
        close();
      } else {
        messageApi.error(`导出失败：${msg}`);
      }
    } catch (error) {
      messageApi.error('导出任务创建失败，请检查网络连接');
      console.error('导出任务异常:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full w-full gap-3">
      {contextHolder}
      <div className="flex-1 p-4 overflow-y-auto bg-white">
        <ProTable
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          bordered
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys.slice(-1)),
            type: 'radio',
            preserveSelectedRowKeys: false,
          }}
          request={async (params, sort, filter) => {
            const param = {
              ...params,
              pageNum: params.current,
              pageSize: params.pageSize,
            };
            delete param.current;
            const { code, rows, total, msg } = await getQATaskList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              messageApi.error(msg);
            }
            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 70,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setSelectedRowKeys([]);
            },
          }}
          dateFormatter="string"
          toolBarRender={() => []}
        />
      </div>
      <div className="h-12 bg-white flex justify-center items-center z-10 gap-3">
        <Button type="default" onClick={() => close()}>
          取消
        </Button>
        <Button type="primary" onClick={handleExport} loading={loading}>
          开始导出
        </Button>
      </div>
    </div>
  );
};

export default TaskEdit;
