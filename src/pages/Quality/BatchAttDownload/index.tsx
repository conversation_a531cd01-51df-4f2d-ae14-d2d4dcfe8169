/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Drawer, message } from 'antd';
import { downloadFile } from '@/api/file';
import { getDownloadTaskList, getQATaskList } from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

type TBatchAttDownloadProps = {};

// 导出状态(1排队中，2进行中，3已完成，4出错)
const STATUS_LIST = [
  { label: '排队中', value: '1', color: '#1890FF' },
  { label: '进行中', value: '2', color: '#13C2C2' },
  { label: '已完成', value: '3', color: '#52C41A' },
  { label: '出错', value: '4', color: '#F5222D' },
];

const BatchAttDownload: React.FC<TBatchAttDownloadProps> = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const [pageSize, setPageSize] = useState<number>(10);

  const [messageApi, contextHolder] = message.useMessage();

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
     {
          disable: true,
          title: '年份',
          dataIndex: 'year',
          filters: false,
          valueType: 'select',
          width: 80,
          fieldProps: {
            options: yearListOnTable,
          },
        },
    // {
    //   title: '年份',
    //   dataIndex: 'year',
    //   hideInSearch: true,
    //   width: 80,
    // },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      width: 120,
    },
    {
      title: '操作人',
      dataIndex: 'createName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '导出任务创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '导出任务执行开始时间',
      dataIndex: 'startTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '导出任务执行结束时间',
      dataIndex: 'endTime',
      hideInSearch: true,
      width: 160,
    },
    {
      title: '总耗时(分钟)',
      dataIndex: 'taskTime',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '文件有效期(天)',
      dataIndex: 'validTime',
      hideInSearch: true,
      width: 100,
    },
    // 导出状态(1排队中，2进行中，3已完成，4出错)
    {
      title: '当前状态',
      dataIndex: 'status',
      hideInSearch: true,
      render: (_, record) => {
        const _label = STATUS_LIST.find(
          (item) => item.value === record.status
        )?.label;
        const _color = STATUS_LIST.find(
          (item) => item.value === record.status
        )?.color;
        return <span style={{ color: _color }}>{_label}</span>;
      },
      width: 100,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (text, record, _, action) => [
        <Button
          key="button"
          type="link"
          onClick={async () => {
            await downloadFile(record?.id, undefined, '/task/file/download');
          }}
          disabled={record?.status !== '3' || record?.validTime === '已过期'}
        >
          下载附件
        </Button>,
      ],
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  return (
    <>
      {contextHolder}
      <PageContainer>
        <ProTable
          loading={loading}
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          // search={false}
          request={async (params, sort, filter) => {
            try {
              const param = {
                ...params,
                pageNum: params.current,
                pageSize: params.pageSize,
              };

              delete param.current;

              const { code, data, msg } = await getDownloadTaskList(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                messageApi.error(msg);
                return {};
              }
              return {
                data: data?.rows ?? [],
                total: data?.total ?? 0,
                success: true,
              };
            } catch (err) {
              throw new Error(`Error: err`);
            } finally {
              // finally todo ...
            }
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          tableExtraRender={() => (
            <Alert
              message="操作说明：批量导出被考核单位提交的附件材料，导出时间取决于对应任务被考核单位的数量、上传的文件数量、每份文件大小等，请耐心等待系统处理，生成的附件请在7天内完成下载。"
              type="info"
              style={{ marginBottom: 16, marginTop: 16 }}
            />
          )}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle="批量附件下载"
          toolBarRender={() => [
            <Button
              key="button"
              icon={<PlusOutlined />}
              onClick={() => setOpenEdit(true)}
              type="primary"
            >
              新增导出任务
            </Button>,
          ]}
        />
        {/* 选择导出任务 */}
        <Drawer
          width="60%"
          title="新增导出任务"
          onClose={closeEdit}
          open={openEdit}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
        >
          <Edit close={closeEdit} />
        </Drawer>
      </PageContainer>
    </>
  );
};
export default BatchAttDownload;
