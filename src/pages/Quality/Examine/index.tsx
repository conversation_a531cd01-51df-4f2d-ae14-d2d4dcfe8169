/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Tag } from 'antd';
import { getFillList } from '@/api/fill';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from '../Judge/components/Edit';
import DownloadButton from '@/components/DownloadButton';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { waitTime, waitTimePromise } from '@/utils/helpers';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const [queryParamsCache, setQueryParamsCache] = useState<any>();
  const actionRef = useRef<ActionType>();

  const [activeKey, setActiveKey] = useState<string>('0');

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');
  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailInfo, setDetailInfo] = useState<any>({});
  const [detailReadOnly, setDetailReadOnly] = useState(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
    },
    {
      title: '评判日期',
      dataIndex: 'judgeDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '样品总得分',
      dataIndex: 'sampleScore',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <span>
            {row.stuffScore && row.score
              ? (parseFloat(row.score) - parseFloat(row.stuffScore)).toFixed(1)
              : '-'}
          </span>
        );
      },
    },
    {
      title: '其它（资料）总得分',
      dataIndex: 'stuffScore',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '最终得分',
      dataIndex: 'score',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '考核结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <>
            {row.result === '优秀' && <Tag color="success">{text}</Tag>}
            {row.result === '合格' && <Tag color="processing">{text}</Tag>}
            {row.result === '不合格' && <Tag color="error">{text}</Tag>}
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      render: (text, record, _, action) => {
        let btns: any = [];
        btns = [
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => {
              setDetailInfo(record);
              setDetailReadOnly(true);
              setOpenEdit(true);
            }}
          >
            查看详情
          </Button>,
        ];
        return btns;
      },
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  const [pageSize, setPageSize] = useState<number>(10);
  return (
    <PageContainer>
      {/* 建设中... */}
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            status: 4,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { code, rows, total, msg } = await getFillList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }

          setQueryParamsCache({
            ...param,
          });

          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="考核列表"
        toolBarRender={() => [
          <DownloadButton
            url="/qa/fillingTasks/export"
            params={queryParamsCache}
          >
            导出查询列表
          </DownloadButton>,
          <DownloadButton
            url="/qa/fillingTasks/exportDetails"
            params={queryParamsCache}
          >
            导出查询明细
          </DownloadButton>,
        ]}
      />
      <Drawer
        width="85%"
        title="详情"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#eee] !p-0',
        }}
      >
        <Edit
          close={closeEdit}
          detailInfo={detailInfo}
          showType="详情"
          readOnly={true}
        />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
