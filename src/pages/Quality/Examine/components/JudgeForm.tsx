import { useEffect, useImperativeHandle } from 'react';
import React, { useRef, useState } from 'react';
import { Col, message, Row } from 'antd';
import { getReceptDetail } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore, useTokenStore } from '@/store';
import { useQualityStore } from '@/store/quality';
import type {
  ActionType,
  EditableFormInstance,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import {
  EditableProTable,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormText,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { clone } from 'lodash';
import { evaluate } from 'mathjs';

type TEditProps = {
  id?: any;
  detailInfo?: any;
  showType?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  readonly: boolean;
};
type DataSourceType = any;

const BaseForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  showType,
  onRef,
  onSubmit,
}) => {
  const { userInfo } = useInfoStore();
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const formRef = useRef<ProFormInstance<any>>();
  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance>();
  const columns: ProColumns<DataSourceType>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      search: false,
      editable: false,
    },
    {
      title: parseInt(detailInfo.refType) === 2 ? '参考结果下限' : '参考结果',
      dataIndex: 'refResult',
      search: false,
      editable: false,
    },
    {
      title: '参考结果上限',
      dataIndex: 'refMaxResult',
      search: false,
      hideInTable: parseInt(detailInfo.refType) !== 2,
      editable: false,
    },
    {
      title: '实际检测结果',
      dataIndex: 'actualResult',
      search: false,
      editable: false,
    },
    {
      title: '考核结果得分',
      width: 200,
      dataIndex: 'score',
      valueType: 'digit',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
  ];

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      if (id && detailInfo) {
        try {
          const p = clone(detailInfo);
          if (showType === '填报') {
            formRef.current?.setFieldsValue({
              table: p.taskDetails || [],
              judge: p.judge ?? userInfo?.user?.nickName,
              judgeDate: p.judgeDate ?? dayjs().format('YYYY-MM-DD'),
              scoreRemark: p.scoreRemark,
              docScore: p.stuffScore,
            });
            setEditableRowKeys(p.taskDetails.map((item: any) => item.id));
          } else if (showType === '详情') {
            formRef.current?.setFieldsValue({
              ...p,
              docScore: p.stuffScore ?? "-",
              table: p.taskDetails || [],
            });
          }
        } catch (error) {
          throw new Error(`Error: ${error}`);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = {
      id,
      ...values,
      details: values.table.map((item: any) => {
        return {
          score: item.score,
          id: item.id,
        };
      }),
    };
    params.stuffScore = params.docScore;
    delete params.table;
    onSubmit(params);
  };
  return (
    <>
      <ProForm<{
        table: DataSourceType[];
      }>
        formRef={formRef}
        initialValues={{
          table: [],
        }}
        layout="horizontal"
        submitter={false}
        onFinish={handleSave}
        readonly={showType === '详情'}
      >
        <EditableProTable<DataSourceType>
          rowKey="id"
          scroll={{
            x: true,
          }}
          editableFormRef={editableFormRef}
          controlled
          bordered
          size='small'
          actionRef={actionRef}
          formItemProps={{
            wrapperCol: { span: 24 },
          }}
          maxLength={10}
          name="table"
          columns={columns}
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
          }}
        />
        <ProFormDependency name={['table', 'docScore']}>
          {({ table, docScore }) => {
            const info = (table as DataSourceType[]).reduce(
              (pre, item) => {
                return {
                  totalScore: evaluate(`${pre.totalScore}+${item?.score || 0}`),
                };
              },
              { totalScore: 0, result: '' }
            );
            info.totalScore = evaluate(`${info.totalScore}+${docScore || 0}`);
            if (info.totalScore < parseFloat(detailInfo.qualifiedMinScore)) {
              info.result = '不合格';
            }
            if (
              info.totalScore >= parseFloat(detailInfo.qualifiedMinScore) &&
              info.totalScore < parseFloat(detailInfo.fineMinScore)
            ) {
              info.result = '合格';
            } else if (info.totalScore >= parseFloat(detailInfo.fineMinScore)) {
              info.result = '优秀';
            }
            return (
              <Row gutter={16}>
                <Col className="gutter-row" span={12}>
                  <ProFormDigit
                    label="资料完成度得分"
                    name="docScore"
                    required
                    max={detailInfo.maxDocScore || null}
                    rules={[{ required: true, message: '这是必填项' }]}
                  />
                </Col>
                <Col className="gutter-row" span={12}>
                  <ProFormText label="评分备注" name="scoreRemark" />
                </Col>
                <Col className="gutter-row" span={6}>
                  <div>
                    最终得分：
                    {showType === '详情' ? detailInfo.score : info.totalScore}
                  </div>
                </Col>
                <Col className="gutter-row" span={6}>
                  <div>
                    最终结论：
                    {showType === '详情' ? detailInfo.result : info.result}
                  </div>
                </Col>
                <Col className="gutter-row" span={6}>
                  <ProFormText
                    label="评判人"
                    name="judge"
                    required
                    rules={[{ required: true, message: '这是必填项' }]}
                  />
                </Col>
                <Col className="gutter-row" span={6}>
                  <ProFormDatePicker
                    label="评判日期"
                    width="xl"
                    name="judgeDate"
                    required
                    rules={[{ required: true, message: '这是必填项' }]}
                  />
                </Col>
              </Row>
            );
          }}
        </ProFormDependency>
      </ProForm>
    </>
  );
};

export default BaseForm;
