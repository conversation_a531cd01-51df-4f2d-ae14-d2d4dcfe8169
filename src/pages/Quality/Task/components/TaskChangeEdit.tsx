/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * 任务变更编辑
 */
import { createContext, useEffect, useRef, useState } from 'react';
import React from 'react';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
} from 'antd';
import { FormInstance } from 'antd/lib/form';
import {
  addOrgTask,
  changeOrgTask,
  changeTaskStatus,
  deleteOrgTask,
  getQATask,
  getTaskDetailEdit,
  queryOrgList,
  saveTaskBasicInfo,
  updateSampleNumber,
} from '@/api/quality';
import { codeDefinition } from '@/constants';
import { useTokenStore } from '@/store';
import { useDictStore } from '@/store/dict';
import { useQualityStore } from '@/store/quality';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import EProFormGroup from '@/components/EProFromGroup';
import FileViewByStream from '@/components/FileViewByStream';
import { getFileTypeByName, getIconByName } from '@/utils/upload';
import AddNewOrgInTask, { IAddNewOrgInTaskRef } from './AddNewOrgInTask';

interface ITaskChangeEditProps {
  detailId: string;
  close: () => void;
}

const layoutProps = {
  colProps: { span: 6 },
};

// 创建Context
export const TaskChangeEditContext = createContext<Record<string, any>>({});

// 添加类型定义
interface ParentRow {
  children?: Array<{
    parentName?: string;
    [key: string]: any;
  }>;
  assessedLabName: string;
  [key: string]: any;
}

const TaskChangeEdit: React.FC<ITaskChangeEditProps> = ({
  detailId,
  close,
}) => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<FormInstance>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const { token } = useTokenStore();

  // 文件预览相关状态
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [previewFileId, setPreviewFileId] = useState<string>('');

  const changeOrgFormRef = useRef<FormInstance>(null);
  const updateSampleNumberFormRef = useRef<FormInstance>(null);
  const { assessmentTypesOnTable } = useQualityStore();
  const { assessmentTaskTypeEnums } = useDictStore();

  // 详情数据
  const [detailData, setDetailData] = useState<any>();

  // 子表缓存
  const [childrenCache, setChildrenCache] = useState<Record<string, any[]>>({});

  // 打开更换被考核单位 Modal
  const [openChangeOrgModal, setOpenChangeOrgModal] = useState<boolean>(false);
  // 当前选中的被考核单位名称
  const [currentOrgName, setCurrentOrgName] = useState<string>('');
  // 当前选中的被考核单位ID
  const [currentOrgId, setCurrentOrgId] = useState<string>('');
  // 当前选中的新的被考核单位ID
  const [currentNewOrgId, setCurrentNewOrgId] = useState<string>('');
  // 当前选中的标准品类别
  const [currentSelectedSampleName, setCurrentSelectedSampleName] =
    useState<string>('');
  // 当前选中的样本编号
  const [currentSelectedSampleCode, setCurrentSelectedSampleCode] =
    useState<string>('');
  // 当前新的样本编号
  const [currentNewSampleCode, setCurrentNewSampleCode] = useState<string>('');
  // 被考核单位列表
  const [orgList, setOrgList] = useState<Record<string, any>[]>([]);

  // 是否打开更新样本编号
  const [openUpdateSampleNumberModal, setOpenUpdateSampleNumberModal] =
    useState<boolean>(false);

  // 是否打开新增考核单位
  const [openAddNewOrgInTaskModal, setOpenAddNewOrgInTaskModal] =
    useState<boolean>(false);

  const addNewOrgInTaskRef = useRef<IAddNewOrgInTaskRef>(null);

  const [modalKey, setModalKey] = useState<number>(0); // 用于强制重新渲染模态框内容

  // 考核任务明细表格单位 Columns
  const orgTableColumns: ProColumns<Record<string, any>>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核单位',
      dataIndex: 'assessedLabName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '样品数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
      width: 100,
    },
    // 任务状态,0待确认，1待填报，2已填报，3评分草稿，4已评分
    {
      title: '状态',
      dataIndex: 'status',
      width: 120,
      valueEnum: {
        // 0: { text: '待确认', status: '0' },
        1: { text: '待填报', status: '1' },
        2: { text: '已填报', status: '2' },
        // 3: { text: '评分草稿', status: '3' },
        4: { text: '已评分', status: '4' },
      },
    },
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
      hideInTable: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 240,
      fixed: 'right',
      render: (text, record) => [
        <Button
          type="link"
          size="small"
          key="replace"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            e.stopPropagation();
            setCurrentOrgName(record?.assessedLabName);
            setCurrentOrgId(record?.assessedLabId);
            setOpenChangeOrgModal(true);
          }}
        >
          更换被考核单位
        </Button>,
        <Popconfirm
          title="您确定删除该单位?"
          onConfirm={() => {
            handleDeleteTaskOrg(record?.assessedLabId);
          }}
          onCancel={(e?: React.MouseEvent<HTMLElement, MouseEvent>) => {
            e?.stopPropagation?.();
          }}
          okText="确定"
          cancelText="取消"
          key="delete"
        >
          <Button
            danger
            type="link"
            size="small"
            onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) =>
              e.stopPropagation()
            }
          >
            删除单位
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 添加子表格 columns
  const childColumns: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核单位',
      dataIndex: 'parentName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '标准品类别',
      dataIndex: 'sampleName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      width: 120,
    },
    // 状态(0为未接收，1为已接收，2待评判，3已评判)
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        0: { text: '未接收', status: '0' },
        1: { text: '已接收', status: '1' },
        2: { text: '待评判', status: '2' },
        3: { text: '已评判', status: '3' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <Button
          type="link"
          key="update"
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            e.stopPropagation();
            setCurrentOrgId(record?.assessedLabId);
            setCurrentOrgName(record?.assessedLabName);
            setCurrentSelectedSampleName(record?.sampleName);
            setCurrentSelectedSampleCode(record?.sampleCode);
            setCurrentNewSampleCode('');

            // 直接设置表单字段值，不使用setTimeout
            if (updateSampleNumberFormRef.current) {
              updateSampleNumberFormRef.current.setFieldsValue({
                orgName: record?.assessedLabName,
                currentSelectedSampleName: record?.sampleName,
                currentSelectedSampleCode: record?.sampleCode,
                currentNewSampleCode: '',
              });
            }

            setOpenUpdateSampleNumberModal(true);
          }}
          style={{ padding: '0 4px' }}
        >
          更新样本编号
        </Button>,
      ],
    },
  ];

  /**
   * @TODO 重新加载表格数据
   */
  const tableReload = () => actionRef?.current?.reload();

  /**
   * @TODO 获取考核任务明细数据
   */
  const fetchData = async (params: any) => {
    try {
      const response = await getTaskDetailEdit({
        id: detailId,
        pageNum: params.current,
        pageSize: params.pageSize,
        ...params,
      });

      if (response.code !== codeDefinition.QUERY_SUCCESS || !response.data) {
        messageApi.error(response.msg || '数据加载失败');
        return { data: [], success: false, total: 0 };
      }

      const { rows = [], total = 0 } = response.data;

      // 处理父级数据和子级数据分离
      const parentRows: any[] = [];
      const childrenMap: Record<string, any[]> = {};

      rows
        .filter((parent: ParentRow) => !!parent)
        .forEach((parent: ParentRow) => {
          if (!parent.id) return; // 跳过无效数据

          // 提取父级数据
          const { samples, ...parentData } = parent;
          parentRows.push(parentData);

          // 缓存子级数据
          if (Array.isArray(samples) && samples.length > 0) {
            childrenMap[parent.id] = samples.map((sample) => ({
              ...sample,
              parentName: parent.assessedLabName,
              category: sample.sampleName,
            }));
          } else {
            childrenMap[parent.id] = [];
          }
        });

      // 更新缓存
      setChildrenCache(childrenMap);

      // 去重父级数据，合并相同单位
      const uniqueParentRows = Array.from(
        new Map(parentRows.map((item) => [item.assessedLabName, item])).values()
      );

      return {
        data: uniqueParentRows,
        total: total,
        success: true,
      };
    } catch (error) {
      console.error('接口异常:', error);
      return { data: [], success: false, total: 0 };
    }
  };

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      if (detailId) {
        const { code, data, msg } = await getQATask(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          setDetailData(data);
          // 处理附件数据格式
          data.attachmentIds = data.attachmentIds
            ? JSON.parse(data.attachmentIds).map((item: any) => {
                return {
                  uid: item.id,
                  name: item.name,
                  status: 'done',
                  type: 'application/msword',
                  url: item.id,
                  response: {
                    data: {
                      fileName: item.name,
                      ossId: item.id,
                      url: item.url,
                    },
                  },
                };
              })
            : [];
          // 处理考核类型
          data.assessmentTypeLabel =
            assessmentTypesOnTable[data?.assessmentType]?.text;
          // 处理任务类型
          data.taskTypeLabel = assessmentTaskTypeEnums?.find(
            (item: any) => item.value === data?.taskType
          )?.label;

          formRef.current?.setFieldsValue(data);
        } else {
          messageApi.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 删除考核任务单位
   */
  const handleDeleteTaskOrg = async (orgId: string) => {
    try {
      const { code, msg } = await deleteOrgTask({
        taskId: detailId,
        orgIds: [orgId],
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('操作成功');
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * @TODO 更换被考核单位
   */
  const handleChangeOrgTask = async () => {
    if (!detailId || !currentNewOrgId) {
      messageApi.error('请选择更新单位或任务ID不存在');
      return;
    }

    try {
      setLoading(true);
      const { code, msg } = await changeOrgTask({
        taskId: detailId,
        assessedLabId: currentOrgId,
        newAssessedLabId: currentNewOrgId,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('操作成功');
      setOpenChangeOrgModal(false);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 获取被考核单位列表
   */
  const getOrgList = async () => {
    try {
      const { code, data, msg } = await queryOrgList();
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      setOrgList(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * @TODO 更新样本编号
   */
  const handleUpdateSampleNumber = async () => {
    if (!detailId) {
      messageApi.error('选择的任务ID不存在');
      return;
    }
    try {
      setLoading(true);
      const { code, msg } = await updateSampleNumber({
        id: detailId,
        assessedLabId: currentOrgId,
        sampleCode: currentSelectedSampleCode,
        newSampleCode: currentNewSampleCode,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('操作成功');
      setCurrentNewSampleCode('');
      updateSampleNumberFormRef.current?.resetFields();
      setOpenUpdateSampleNumberModal(false);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 新增被考核单位
   */
  const handleAddNewOrgInTask = async () => {
    if (!addNewOrgInTaskRef.current) {
      messageApi.error('组件引用不存在');
      return;
    }

    const formData = addNewOrgInTaskRef.current.getFormData();

    if (!formData) {
      return; // 表单验证不通过，getFormData 内部已经显示错误消息
    }

    // 判断任务类型
    const taskType = detailData?.taskType;

    // 获取当前任务类型的标签
    const currentTaskTypeLabel = assessmentTaskTypeEnums?.find(
      (item: any) => item.value === taskType
    )?.label;

    // 根据不同的任务类型进行校验
    if (
      ['市县疾控考核', '盲样考核', '双盲考核'].includes(currentTaskTypeLabel)
    ) {
      // 市县疾控考核、盲样考核、双盲考核三种任务类型，每种类别的标准品都需要填写样本编号
      const allSamplesCount = detailData?.qaTaskSampleVos?.length || 0;
      const filledSamplesCount = formData.samples.length;

      if (filledSamplesCount < allSamplesCount) {
        messageApi.error(
          '市县疾控考核、盲样考核、双盲考核任务类型，每种类别的标准品都需要填写样本编号'
        );
        return;
      }
    } else if (currentTaskTypeLabel === '随机考核') {
      // 随机考核这种任务类型, 至少需要填写一个样本编号
      if (formData.samples.length === 0) {
        messageApi.error('随机考核任务类型，至少需要填写一个样本编号');
        return;
      }
    }

    try {
      setLoading(true);

      // 构建符合API要求的参数
      const qaTaskSampleVos = formData.samples.map((sample) => ({
        id: sample.id,
        taskId: detailId,
        name: sample.name || '',
        value: sample.value || '',
        sampleCode: sample.sampleCode,
      }));

      const params = {
        taskId: detailId,
        orgId: formData.orgId,
        qaTaskSampleVos: qaTaskSampleVos,
      };

      // 调用保存接口
      const { code, msg } = await addOrgTask(params);

      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }

      messageApi.success('新增被考核单位成功');
      setOpenAddNewOrgInTaskModal(false);
      // 重置表单数据
      addNewOrgInTaskRef.current?.reset();
      // 更新key，确保下次打开时强制重新渲染
      setModalKey((prevKey) => prevKey + 1);
      tableReload();
    } catch (err) {
      throw new Error(`Error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 保存任务
   */
  const handleSaveTask = async () => {
    try {
      setLoading(true);

      const _formData = formRef.current?.getFieldsValue();

      // 处理附件数据
      // 提交的数据是字符序列化后的包含name、id、url 的List数据
      let attachmentIds = [];
      if (_formData.attachmentIds && _formData.attachmentIds.length > 0) {
        attachmentIds = _formData.attachmentIds
          .filter(
            (item: any) =>
              item.response && item.response.data && item.response.data.ossId
          )
          .map((item: any) => {
            return {
              name: item.response.data.fileName,
              id: item.response.data.ossId,
              url: item.response.data.url,
            };
          });
      }

      const { code, msg } = await saveTaskBasicInfo({
        id: detailId,
        name: _formData.name,
        endDate: _formData.endDate,
        remark: _formData.remark,
        attachmentIds: JSON.stringify(attachmentIds),
        status: '2',
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('操作成功');
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 重新启动任务
   */
  const handleRestartTask = async () => {
    try {
      setLoading(true);
      // 调用保存方法
      const _formData = formRef.current?.getFieldsValue();
      // 处理附件数据
      // 提交的数据是字符序列化后的包含name、id、url 的List数据
      let attachmentIds = [];
      if (_formData.attachmentIds && _formData.attachmentIds.length > 0) {
        attachmentIds = _formData.attachmentIds
          .filter(
            (item: any) =>
              item.response && item.response.data && item.response.data.ossId
          )
          .map((item: any) => {
            return {
              name: item.response.data.fileName,
              id: item.response.data.ossId,
              url: item.response.data.url,
            };
          });
      }

      const _saveResult = await saveTaskBasicInfo({
        id: detailId,
        name: _formData.name,
        endDate: _formData.endDate,
        remark: _formData.remark,
        attachmentIds: JSON.stringify(attachmentIds),
        status: '2',
      });
      if (_saveResult?.code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(_saveResult?.msg);
        return;
      }
      messageApi.success('保存成功');

      // 变更任务状态
      const { code, msg } = await changeTaskStatus({
        id: detailId,
        status: '1',
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('操作成功');
      close();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  // 使用useMemo缓存原始样本数据
  const initialSampleVos = React.useMemo(() => {
    if (!detailData?.qaTaskSampleVos) return [];
    // 确保返回干净的副本，不包含任何样本编号
    return detailData.qaTaskSampleVos.map((item: Record<string, any>) => ({
      ...item,
      sampleCode: '', // 初始样本编号为空
    }));
  }, [detailData?.qaTaskSampleVos]);

  // 创建定时器引用
  const changeOrgFormTimerRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );
  const updateSampleNumberTimerRef = useRef<ReturnType<
    typeof setTimeout
  > | null>(null);

  // 添加这个useEffect来处理表单值设置
  useEffect(() => {
    if (openChangeOrgModal && currentOrgName) {
      // 确保Modal完全打开后再设置表单值
      if (changeOrgFormTimerRef.current) {
        clearTimeout(changeOrgFormTimerRef.current);
      }

      changeOrgFormTimerRef.current = setTimeout(() => {
        changeOrgFormRef.current?.setFieldsValue({
          orgName: currentOrgName,
        });
      }, 100);
    }

    // 清理定时器
    return () => {
      if (changeOrgFormTimerRef.current) {
        clearTimeout(changeOrgFormTimerRef.current);
        changeOrgFormTimerRef.current = null;
      }
    };
  }, [openChangeOrgModal]);

  // 处理更新样本编号Modal
  useEffect(() => {
    if (openUpdateSampleNumberModal && currentOrgName) {
      if (updateSampleNumberTimerRef.current) {
        clearTimeout(updateSampleNumberTimerRef.current);
      }

      updateSampleNumberTimerRef.current = setTimeout(() => {
        updateSampleNumberFormRef.current?.setFieldsValue({
          orgName: currentOrgName,
          currentSelectedSampleName: currentSelectedSampleName,
          currentSelectedSampleCode: currentSelectedSampleCode,
        });
      }, 100);
    }

    // 清理定时器
    return () => {
      if (updateSampleNumberTimerRef.current) {
        clearTimeout(updateSampleNumberTimerRef.current);
        updateSampleNumberTimerRef.current = null;
      }
    };
  }, [openUpdateSampleNumberModal]);

  useEffect(() => {
    if (detailId) {
      getDetailData();
      getOrgList();
    }
  }, [detailId]);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      if (changeOrgFormTimerRef.current) {
        clearTimeout(changeOrgFormTimerRef.current);
        changeOrgFormTimerRef.current = null;
      }

      if (updateSampleNumberTimerRef.current) {
        clearTimeout(updateSampleNumberTimerRef.current);
        updateSampleNumberTimerRef.current = null;
      }
    };
  }, []);

  return (
    <TaskChangeEditContext.Provider value={{}}>
      {contextHolder}
      <div className="relative w-full h-full flex flex-col">
        <div className=" flex-1 overflow-y-auto">
          <ProForm
            formRef={formRef}
            formKey="base-form-use-demo"
            layout="horizontal"
            grid={true}
            rowProps={{
              gutter: [128, 0],
              justify: 'space-between',
            }}
            onFinish={() => {}}
            initialValues={{}}
            submitter={false}
            onValuesChange={(_: any, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef.current?.setFieldsValue(values);
            }}
            style={{
              height: '100%',
            }}
            className="p-4"
          >
            <EProFormGroup title="基本信息">
              <ProFormText
                readonly
                name="particularYear"
                label="年份"
                {...layoutProps}
              />
              <ProFormText
                name="name"
                label="任务名称"
                placeholder="请输入任务名称"
                rules={[
                  {
                    required: true,
                    validator(rule, value, callback) {
                      if (!value) {
                        callback('请输入任务名称');
                      }
                      if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value)) {
                        callback('任务名称只能由中文字符、英文字符、数字组成');
                      } else {
                        callback();
                      }
                    },
                  },
                ]}
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="assessmentTypeLabel"
                label="考核类型"
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="templateName"
                label="使用模板"
                {...layoutProps}
              />
              <ProFormText
                readonly
                label="任务类型"
                name="taskTypeLabel"
                {...layoutProps}
              />
              <ProFormDatePicker
                rules={[{ required: true, message: '请选择' }]}
                initialValue={dayjs()}
                name="endDate"
                label="结束日期"
                width={'lg'}
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="people"
                label="任务负责人"
                {...layoutProps}
              />
              <ProFormText
                readonly
                name="phone"
                label="联系电话"
                {...layoutProps}
              />
              <ProFormTextArea
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                name="remark"
                label="备注信息"
              />
              <EProFormUploadButton
                name="attachmentIds"
                label="附件信息"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={10}
              />
            </EProFormGroup>
            <EProFormGroup title="考核任务明细">
              <div className="text-red-500">
                <p>操作注意事项说明：</p>
                <p>
                  1、【增加被考核单位】将基于本任务增加一家或多家的被考核单位，增加的单位需要按照原定的标准品配置信息进行发样；
                </p>
                <p>
                  2、【更换被考核单位】指将原寄送给A单位的x1、x2、x3等多个考核样改派给另一家B单位，原A单位填报的信息将被清空且不可恢复，考核样将被置于待接收的初始状态给到B单位，由B单位重新填报接收、检验信息，请谨慎操作；
                </p>
                <p>
                  3、【删除单位】指取消本任务对某单位A的考核，删除后，该单位对本任务的所有操作信息将被清空且不可恢复，请谨慎操作；
                </p>
                <p>
                  4、【更新样本编号】指考核样寄送过程中因损坏导致被考核单位无法进行检验，需要按照标准品配置重新寄送新的考核样，且为其粘贴了新的编号，需要将新编号更新到系统中，更新编号不会影响该单位的填报数据，但本操作不可逆，更新后不可恢复到原编号，请谨慎操作；
                </p>
              </div>
              <div
                className="mt-4"
                style={{ width: '100%', overflowX: 'auto' }}
              >
                <ProTable
                  name="orgTable"
                  columns={orgTableColumns}
                  style={{ height: '100%', width: '100%' }}
                  scroll={{ y: 'calc(100% - 48px)', x: 'max-content' }}
                  actionRef={actionRef}
                  cardBordered
                  rowKey="id"
                  expandable={{
                    expandedRowRender: (record) => {
                      // 从缓存中获取子数据
                      const childDataSource = childrenCache[record.id] || [];

                      return childDataSource.length > 0 ? (
                        <ProTable
                          columns={childColumns}
                          dataSource={childDataSource}
                          pagination={false}
                          search={false}
                          rowKey="id"
                          options={false}
                          style={{
                            marginLeft: 40,
                            width: 'calc(100% - 40px)',
                          }}
                          scroll={{ x: 'max-content' }}
                        />
                      ) : (
                        <div style={{ marginLeft: 40, padding: '16px 0' }}>
                          暂无样本数据
                        </div>
                      );
                    },
                    expandRowByClick: true,
                    // 仅当有子数据时才显示展开图标
                    rowExpandable: (record) =>
                      !!childrenCache[record.id]?.length,
                  }}
                  request={async (params) => {
                    return await fetchData(params);
                  }}
                  pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="button"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        // 更新key，确保每次打开时都重新渲染组件
                        setModalKey((prevKey) => prevKey + 1);
                        setOpenAddNewOrgInTaskModal(true);
                      }}
                      type="primary"
                    >
                      增加被考核单位
                    </Button>,
                  ]}
                />
              </div>
            </EProFormGroup>
          </ProForm>
        </div>
        <div className="h-12 bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Space size="small">
            <Button type="primary" loading={loading} onClick={handleSaveTask}>
              保存任务
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={handleRestartTask}
            >
              重新启动任务
            </Button>
          </Space>
        </div>
      </div>
      {/* 更换被考核单位 */}
      <Modal
        open={openChangeOrgModal}
        title="更换被考核单位"
        centered
        width={420}
        okText="更换被考核单位"
        onOk={() => handleChangeOrgTask()}
        onCancel={() => {
          setCurrentOrgName('');
          setCurrentOrgId('');
          setCurrentNewOrgId('');
          setOpenChangeOrgModal(false);
        }}
        confirmLoading={loading}
      >
        <Form
          ref={changeOrgFormRef}
          name="changeOrgForm"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          autoComplete="off"
          className="!mt-6"
        >
          <Form.Item label="原单位名称" name="orgName">
            <Input disabled />
          </Form.Item>
          <Form.Item
            label="更新单位名称"
            name="newOrgName"
            rules={[{ required: true, message: '请选择更新单位' }]}
          >
            <Select
              showSearch
              optionFilterProp="label"
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={orgList.map((item) => ({
                label: item.deptName,
                value: item.deptId,
              }))}
              onChange={(value) => setCurrentNewOrgId(value)}
            />
          </Form.Item>
        </Form>
      </Modal>
      {/* 更新样本编号 */}
      <Modal
        open={openUpdateSampleNumberModal}
        title="更新样本编号"
        centered
        width={420}
        okText="更新样本编号"
        onOk={() => handleUpdateSampleNumber()}
        onCancel={() => {
          setCurrentOrgName('');
          setCurrentSelectedSampleName('');
          setCurrentSelectedSampleCode('');
          setCurrentNewSampleCode('');
          updateSampleNumberFormRef.current?.resetFields();
          setOpenUpdateSampleNumberModal(false);
        }}
        confirmLoading={loading}
      >
        <Form
          ref={updateSampleNumberFormRef}
          name="updateSampleNumberForm"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          autoComplete="off"
          className="!mt-6"
          onValuesChange={(changedValues: Record<string, any>) => {
            if (changedValues.currentNewSampleCode !== undefined) {
              setCurrentNewSampleCode(changedValues.currentNewSampleCode);
            }
          }}
        >
          <Form.Item label="被考核单位名称" name="orgName">
            <Input disabled />
          </Form.Item>
          <Form.Item label="标准品类别" name="currentSelectedSampleName">
            <Input disabled />
          </Form.Item>
          <Form.Item label="原样本编号" name="currentSelectedSampleCode">
            <Input disabled />
          </Form.Item>
          <Form.Item
            label="更新样本编号"
            name="currentNewSampleCode"
            rules={[{ required: true, message: '请输入更新样本编号' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      {/* 新增考核单位 */}
      <Modal
        open={openAddNewOrgInTaskModal}
        title="新增被考核单位"
        centered
        width={700}
        okText="新增被考核单位"
        onOk={() => handleAddNewOrgInTask()}
        onCancel={() => {
          setOpenAddNewOrgInTaskModal(false);
          // 重置表单数据
          addNewOrgInTaskRef.current?.reset();
        }}
        afterClose={() => {
          // 确保模态框完全关闭后重置表单
          addNewOrgInTaskRef.current?.reset();
          // 更新key，确保下次打开时强制重新渲染
          setModalKey((prevKey) => prevKey + 1);
        }}
        confirmLoading={loading}
        destroyOnClose={true} // 确保关闭时销毁内部组件
      >
        <AddNewOrgInTask
          key={modalKey} // 使用key强制刷新组件
          ref={addNewOrgInTaskRef}
          taskId={detailId}
          orgList={orgList}
          qaTaskSampleVos={initialSampleVos} // 使用不含样本编号的初始数据
          qaTaskItemVos={detailData?.qaTaskItemVos}
          taskType={detailData?.taskType}
        />
      </Modal>
      {/* 文件预览模态框 */}
      {openPreview && (
        <Modal
          width="60%"
          title="文件预览"
          onCancel={() => setOpenPreview(false)}
          open={openPreview}
          footer={null}
          destroyOnClose
        >
          <FileViewByStream fileId={previewFileId} isPreview />
        </Modal>
      )}
    </TaskChangeEditContext.Provider>
  );
};

export default TaskChangeEdit;
