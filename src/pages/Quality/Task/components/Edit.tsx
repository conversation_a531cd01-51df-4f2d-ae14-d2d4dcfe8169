/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import {
  createContext,
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button, Drawer, message } from 'antd';
import { addQATask, getQATask, saveOrgTask, sendQATask } from '@/api/quality';
import { randomExporExpress } from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
// 导入useTimeout hook
import useTimeout from '@/hooks/useTimeout';
import download from '@/utils/download';
import BaseForm from './BaseForm';
// 机构考核
import EditBindSample from './BindSample/EditBindSample';
import DoubleBind from './DoubleBind/DoubleBind';
// 随机考核
import ExpressModel from './Model/ExpressModel';
import EditOrganization from './Organization/EditOrganization';
// 盲样考核
import EditRandom from './Random/EditRandom';

type TEditProps = {
  close: () => void;
  detailId?: string;
  getCounts: () => void;
};

// 创建Context
export const TaskContext = createContext<any>({});

const TaskEdit: React.FC<TEditProps> = ({ close, detailId, getCounts }) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(false);
  const [baseInfo, setBaseInfo] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);

  const [messageApi, contextHolder] = message.useMessage();

  // 保存
  const [loadingSave, setSoadingSave] = useState<boolean>(false);
  // 提交
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  // 提交
  const [loadingExpress, setLoadingExpress] = useState<boolean>(false);
  // 维护发样信息弹窗
  const [expressModel, setExpressModel] = useState<boolean>(false);
  const formRef1 = useRef<any>(null);
  const formRef = useRef<any>(null);
  const [refType, setRefType] = useState('1');
  const [loading2, setLoading2] = useState<boolean>(false);

  // 考核任务类型  机构考核    盲样考核   随机考核
  const [examinationTaskType, setExaminationTaskType] = useState<string>('');

  // 在组件函数内添加
  const [setExpressTimer, clearExpressTimer] = useTimeout(() => {
    handleExpress();
  }, 1000);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      if (id) {
        const { code, data, msg } = await getQATask(id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          setBaseInfo(data);
          setReadonly(data.status !== '0');
          setExaminationTaskType(data.taskType);
          return data; // 返回获取到的数据
        } else {
          messageApi.error(msg);
          return null;
        }
      }
      return null;
    } catch (error) {
      console.error('获取详情数据失败:', error);
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 组件替换
   */
  const templeComponentCheck = useCallback(() => {
    try {
      switch (examinationTaskType) {
        case 'org':
          // 机构考核
          return (
            <EditOrganization
              onRef={formRef1}
              baseInfo={baseInfo}
              onSave={handleSave}
            />
          );
        case 'blind':
          // 盲样考核
          return (
            <EditBindSample
              onRef={formRef1}
              baseInfo={baseInfo}
              onSave={handleSave}
            />
          );
        case 'random':
          // 随机考核
          return (
            <EditRandom
              onRef={formRef1}
              baseInfo={baseInfo}
              getDetailData={getDetailData}
            />
          );
        case 'blindTwo':
          // 双盲考核
          return <DoubleBind onRef={formRef1} baseInfo={baseInfo} />;
        default:
          return null;
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [examinationTaskType]);

  /**
   * @TODO 配置任务明细
   */
  const handleConfigureTask = async () => {
    try {
      setLoading(true);
      const params = await formRef.current?.handleSave(false);
      const { code, data, msg }: any = await addQATask(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        setBaseInfo(data);
        setExaminationTaskType(data.taskType);
        setId(detailId);
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 保存草稿
   */
  const handleSave = async (tip: boolean = false) => {
    try {
      const form = await formRef?.current?.handleParams();
      const form1 = await formRef1?.current?.handleParams();
      const params = {
        ...baseInfo,
        ...form,
        ...form1,
      };

      // 遍历 params 中的 qaTaskItemVos, 如果该字段不为空，则为每一项中的 taskId 写入 baseInfo 中的 id
      if (params.qaTaskItemVos?.length > 0) {
        params.qaTaskItemVos.forEach((item: any) => {
          item.taskId = baseInfo.id;
        });
      }

      setSoadingSave(tip);
      const { code, msg }: any = await saveOrgTask(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        tip && messageApi.success(QUERY_SUCCESS_MSG);
        return code;
      } else {
        messageApi.error(msg);
      }
    } catch (error: any) {
      if (error) {
        const msg = error.message;
        messageApi.error(msg);
      }
      throw new Error(`Error: ${error}`);
    } finally {
      setSoadingSave(false);
    }
  };

  /**
   * @TODO 维护发样信息
   */
  const handleExpress = async () => {
    try {
      setLoadingExpress(true);
      const code = await handleSave(false);
      if (code === 200) {
        setExpressModel(true);
      }
    } catch (error) {
      throw new Error(`Error:${error}`);
    } finally {
      setLoadingExpress(false);
    }
  };

  /**
   * @TODO 提交下发
   */
  const handleSend = async () => {
    try {
      // 获取基本信息表单数据
      await formRef.current.handleValidateFields();

      const form = await formRef?.current?.handleParams();
      const form1 = await formRef1?.current?.handleParams();
      const params = {
        ...baseInfo,
        ...form,
        ...form1,
      };

      console.log(params.qaTaskSampleVos, 111);
      
      const sampleVosList:any = []
      if(params.qaTaskSampleVos.length  > 0) {
        params.qaTaskSampleVos.forEach((e:any)=>  {
          console.log(e, "eee");
          sampleVosList.push(e.sampleVos)
        })


        console.log(sampleVosList, "sampleVosList11111");
        


        // 判断每个对象的 assessedLabName 属性是否都有值
        const allAssessedLabNamesHaveValue = sampleVosList.flat().every((item:any) => {
          return item && typeof item.assessedLabName === 'string' && item.assessedLabName.trim() !== '';
        });
        
        if (!allAssessedLabNamesHaveValue  && examinationTaskType !='blindTwo') {
          messageApi.error("请选择每个样本被考核单位");
          return
        }
      }





      // 遍历 params 中的 qaTaskItemVos, 如果该字段不为空，则为每一项中的 taskId 写入 baseInfo 中的 id
      if (params.qaTaskItemVos?.length > 0) {
        params.qaTaskItemVos.forEach((item: any) => {
          item.taskId = baseInfo.id;
        });
      }

      setLoadingSubmit(true);
      const { code, msg }: any = await sendQATask(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        close();
        getCounts();
      } else {
        messageApi.error(msg);
        if (
          msg === '样本快递信息不足，请维护发样信息' ||
          msg === '同种样本类型被考核机构重复！'
        ) {
          setExpressTimer();
        }
      }
    } catch (error: any) {
      let msg = error?.message ?? error;
      if (typeof msg === 'string' && msg.indexOf(':') > -1) {
        const msgList = msg.split(':');
        msg = msgList[msgList.length - 1];
      }
      messageApi.error(msg);
      throw new Error(`${error}`);
    } finally {
      setLoadingSubmit(false);
    }
  };

  /**
   * @TODO 导出样本清单
   */
  const handleExport = async () => {
    try {
      // 先保存
      await handleSave();
      setLoading2(true);
      const params = {
        taskId: baseInfo?.id,
      };
      const data = await randomExporExpress(params);
      await download(data, '随机考核样本清单.xlsx'); //导出方法，传入后端返回的文件流
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading2(false);
    }
  };

  useEffect(() => {
    getDetailData();
    return () => {
      clearExpressTimer();
    };
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  return (
    <div className="flex flex-col h-full w-full">
      {contextHolder}
      <TaskContext.Provider value={{ refType, setRefType }}>
        <div className="flex-1 overflow-auto p-4">
          {/* 基本信息 */}
          <BaseForm
            readonly={readonly}
            onRef={formRef}
            id={baseInfo?.id}
            detailInfo={baseInfo}
            taskType={baseInfo?.taskType}
          />
          {/* 配置明细按钮 */}
          {!examinationTaskType ? (
            <div className="h-[60px] w-full flex justify-center items-center bg-[#FFF] rounded mt-[-10px] relative z-1">
              <Button
                type="primary"
                loading={loading}
                onClick={handleConfigureTask}
              >
                配置任务明细
              </Button>
            </div>
          ) : null}
          {/* 任务类型 */}
          {templeComponentCheck()}
        </div>
      </TaskContext.Provider>
      {/* 快递信息维护 */}
      <Drawer
        width="85%"
        title="发样信息维护"
        onClose={() => setExpressModel(false)}
        open={expressModel}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <ExpressModel
          close={() => setExpressModel(false)}
          detailId={baseInfo?.id}
          onSubmitClose={close}
        />
      </Drawer>
      {examinationTaskType && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => handleSave(true)}
            type="default"
            loading={loadingSave}
          >
            保存
          </Button>
          <Button onClick={handleSend} type="primary" loading={loadingSubmit}>
            启动任务
          </Button>
        </div>
      )}
    </div>
  );
};

export default memo(TaskEdit);
