import { useRef, useState } from 'react';
import { Button, message, Modal, Popconfirm, Space, Upload } from 'antd';
import type { UploadProps } from 'antd';
import { getDepartmentList } from '@/api/department';
import {
  addQATaskItem,
  delQATaskItem,
  getQATaskItemList,
  importDetail,
} from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormDigit,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { handleTree } from '@/utils/index';
import { formItemLayout } from '../data';
import './index.less';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TaskListItem = {
  url: string;
  id: number;
  number: number;
  title: string;
  labels: {
    name: string;
    color: string;
  }[];
  state: string;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
};

type TTaskListByOrgProps = {
  id: string;
  readonly: boolean;
  refType: any; //参考值类型，0文本,1数值,2区间
};

const TaskList: React.FC<TTaskListByOrgProps> = ({
  id,
  readonly,
  refType = 0,
}) => {
  const actionRef = useRef<ActionType>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns<TaskListItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核机构名称',
      dataIndex: 'assessedLabName',
      search: false,
    },
    {
      title: '机构编号',
      dataIndex: 'assessedLabCode',
      search: false,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      search: false,
    },
    {
      title: parseInt(refType) === 2 ? '参考结果下限' : '参考结果',
      dataIndex: 'refResult',
      search: false,
    },
    {
      title: '参考结果上限',
      dataIndex: 'refMaxResult',
      search: false,
      hideInTable: parseInt(refType) !== 2,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 60,
      hideInTable: readonly,
      render: (text, record, _, action) => [
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <Button danger key="editable" type="text">
            删除
          </Button>
        </Popconfirm>,
        ,
      ],
    },
  ];

  const formRef = useRef<FormInstance>(null);

  /**
   * @TODO 新增
   */
  const handleSave = async (values: any) => {
    const params = {
      ...values,
      assessedLabName: values.assessedLabId.label,
      assessedLabId: values.assessedLabId.value,
      taskId: id,
    };
    setLoading(true);
    try {
      const { code, msg }: any = await addQATaskItem(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
        formRef.current?.resetFields();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);

  /**
   * @TODO 删除
   */
  const handleDelete = async (ids: number) => {
    try {
      const { code, msg } = await delQATaskItem({
        id,
        ids,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  const downloadTemplate = async () => {
    window.open(
      `${import.meta.env.VITE_PUBLIC_PATH}template/任务明细导入模板.xlsx`
    );
  };
  const uploadUrl: any = importDetail(id);
  const { token } = useTokenStore();
  const uploadProps: UploadProps = {
    name: 'file',
    maxCount: 1,
    showUploadList: false,
    action: uploadUrl,
    data: {
      refResultType: refType,
    },
    onChange: (info) => {
      handleUploadFiles(info);
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file } = info;
    if (file.status === 'done') {
      if (file.response && file.response.code === 200) {
        message.success(file.response.msg);
        tableReload();
      } else {
        Modal.error({
          title: file.response.msg,
          content: (file.response.data || []).map((item: any, idx: any) => (
            <div key={idx}>{item}</div>
          )),
        });
      }
    }
  };
  const [pageSize, setPageSize] = useState<number>(10);

  return (
    <BlockContainer title="任务明细">
      {!readonly && (
        <ProForm
          formRef={formRef}
          // layout="inline"
          grid={true}
          onFinish={handleSave}
          initialValues={{}}
          submitter={{
            render: (_, dom) => (
              <div className="flex justify-end items-center mb-4">
                <Space size="small">
                  <Button
                    type="primary"
                    loading={loading}
                    htmlType="submit"
                    icon={<PlusOutlined />}
                  >
                    添加
                  </Button>
                  <Button
                    type="default"
                    onClick={() => {
                      downloadTemplate();
                    }}
                  >
                    模板下载
                  </Button>
                  <Upload {...uploadProps}>
                    <Button type="default" loading={loading}>
                      批量导入
                    </Button>
                  </Upload>
                </Space>
              </div>
            ),
          }}
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormTreeSelect
            name="assessedLabId"
            label="被考核机构名称"
            placeholder="请选择"
            rules={[{ required: true, message: '请输入被考核机构名称' }]}
            request={async () => {
              const { code, data, msg } = await getDepartmentList({});
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }
              const d = handleTree(data, 'deptId');
              function setDisabled(d: any) {
                d.forEach((item: any) => {
                  item.selectable = item.labTag === 1;
                  item.disabled = item.labTag !== 1;
                  if (item.children) {
                    setDisabled(item.children);
                  }
                });
              }
              setDisabled(d);
              return d;
            }}
            fieldProps={{
              labelInValue: true,
              fieldNames: {
                label: 'deptName',
                value: 'deptId',
                children: 'children',
              },
            }}
            colProps={formItemLayout}
          />
          <ProFormText
            name="sampleCode"
            label="样本编号"
            placeholder="请输入样本编号"
            rules={[{ required: true, message: '请输入样本编号' }]}
            colProps={formItemLayout}
          />
          {
            // 0文本
            parseInt(refType) === 0 && (
              <ProFormText
                name="refResult"
                label="参考结果"
                placeholder="请输入参考结果"
                rules={[{ required: true, message: '请输入参考结果' }]}
                colProps={formItemLayout}
              />
            )
          }
          {
            // 1数值
            parseInt(refType) === 1 && (
              <ProFormDigit
                name="refResult"
                label="参考结果"
                placeholder="请输入参考结果"
                rules={[{ required: true, message: '请输入参考结果' }]}
                colProps={formItemLayout}
              />
            )
          }
          {
            // 2区间
            parseInt(refType) === 2 && (
              <>
                <ProFormDigit
                  name="refResult"
                  label="参考结果下限"
                  placeholder="请输入参考结果下限"
                  rules={[{ required: true, message: '请输入参考结果下限' }]}
                  // colProps={formItemLayout}
                  colProps={{ span: 4 }}
                />
                <ProFormDigit
                  name="refMaxResult"
                  label="参考结果上限"
                  placeholder="请输入参考结果上限"
                  rules={[{ required: true, message: '请输入参考结果上限' }]}
                  // colProps={formItemLayout}
                  colProps={{ span: 4 }}
                />
              </>
            )
          }
        </ProForm>
      )}
      <ProTable<TaskListItem>
        search={false}
        columns={columns}
        actionRef={actionRef}
        size="small"
        className="task-detail-table"
        bordered
        request={async (params, sort, filter) => {
          const param = {
            id: id,
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { rows, total } = await getQATaskItemList(param);
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        rowKey="id"
        options={false}
        form={{
          // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
              };
            }
            return values;
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true, 
          pageSize: pageSize,
          onShowSizeChange(current, size) {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
    </BlockContainer>
  );
};
export default TaskList;
