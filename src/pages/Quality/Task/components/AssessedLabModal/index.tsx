/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { Button, Input, message, Modal, Pagination, Space, Table } from 'antd';
import { getDeptPage } from '@/api/system';
import { codeDefinition } from '@/constants';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';

// 定义接口类型
interface AssessedLab {
  id: string;
  labCode: string;
  labName: string;
  type: string;
}

interface AssessedLabModalProps {
  open: boolean;
  onCancel: () => void;
  onSelect: (selectedLab: AssessedLab) => void;
  currentRow?: any;
}

const AssessedLabModal: React.FC<AssessedLabModalProps> = ({
  open,
  onCancel,
  onSelect,
  currentRow,
}) => {
  // 状态管理
  const [tableData, setTableData] = useState<any[]>([]);
  const [selectedLabId, setSelectedLabId] = useState<string>('');
  const [selectedLabRow, setSelectedLabRow] = useState<any>(null);
  const [searchValue, setSearchValue] = useState<string>('');
  const [tableLoading, setTableLoading] = useState<boolean>(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取被考核单位列表（分页查询）
  const getAssessedLabList = async (
    pageParams: any,
    searchParams: any = {}
  ) => {
    try {
      setTableLoading(true);
      const params = {
        pageSize: pageParams.pageSize || 10,
        pageNum: pageParams.current || 1,
        ...searchParams,
      };

      const response = await getDeptPage(params);

      if (response.code === codeDefinition.QUERY_SUCCESS && response.data) {
        const { rows, total } = response.data;
        setTableData(rows || []);
        setPagination({
          ...pagination,
          current: params.pageNum,
          pageSize: params.pageSize,
          total: total || 0,
        });
      } else {
        message.error(response.msg || '获取机构列表失败');
      }
      setTableLoading(false);
    } catch (error) {
      setTableLoading(false);
      message.error('获取机构列表失败');
      console.error(error);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    const searchParams: Record<string, any> = {};

    if (searchValue.trim()) {
      searchParams.deptName = searchValue.trim();
    }

    // 重置到第一页
    const newPagination = { ...pagination, current: 1 };
    setPagination(newPagination);

    // 查询数据
    getAssessedLabList(newPagination, searchParams);
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchValue('');

    // 重置到第一页
    const newPagination = { ...pagination, current: 1 };
    setPagination(newPagination);

    // 重新查询数据
    getAssessedLabList(newPagination);
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize,
    };
    setPagination(newPagination);

    // 构建搜索参数
    const searchParams: Record<string, any> = {};
    if (searchValue.trim()) {
      searchParams.deptName = searchValue.trim();
    }

    // 使用新的分页参数查询数据
    getAssessedLabList(newPagination, searchParams);
  };

  // 处理行选择
  const handleRowSelection = (record: any) => {
    return {
      onClick: () => {
        setSelectedLabId(record.deptId);
        setSelectedLabRow(record);
      },
    };
  };

  // 处理确认选择
  const handleSelectAssessedLab = () => {
    if (!selectedLabId || !selectedLabRow) {
      message.warning('请选择一个被考核单位');
      return;
    }

    const selectedLab = {
      id: selectedLabRow.deptId,
      labCode: selectedLabRow.deptCode,
      labName: selectedLabRow.deptName,
      deptId: selectedLabRow.deptId,
      type: getLabType(selectedLabRow.deptName),
    };

    onSelect(selectedLab);
  };

  // 根据机构名称判断类型
  const getLabType = (name: string) => {
    if (name.includes('医院') || name.includes('医疗')) {
      return 'hospital';
    } else if (name.includes('中心')) {
      if (name.includes('市')) {
        return 'city';
      } else {
        return 'district';
      }
    } else {
      return 'other';
    }
  };

  // 弹窗关闭时重置状态
  const handleCancel = () => {
    setSelectedLabId('');
    setSelectedLabRow(null);
    setSearchValue('');
    onCancel();
  };

  // 初始化时获取被考核单位列表
  useEffect(() => {
    if (open) {
      // 重置搜索和分页状态
      setSearchValue('');
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });

      // 根据currentRow决定是否选中机构
      if (currentRow && currentRow.assessedLabId) {
        // 如果有已选机构，设置为选中状态
        setSelectedLabId(currentRow.assessedLabId);
        setSelectedLabRow({
          deptId: currentRow.assessedLabId,
          deptCode: currentRow.assessedLabCode,
          deptName: currentRow.assessedLabName,
        });
      } else {
        // 如果没有已选机构，清空选中状态
        setSelectedLabId('');
        setSelectedLabRow(null);
      }

      // 获取数据
      getAssessedLabList({
        current: 1,
        pageSize: 10,
      });
    }
  }, [open, currentRow]);

  // 定义表格列
  const columns = [
    {
      title: '机构编码',
      dataIndex: 'deptCode',
      key: 'deptCode',
      width: '25%',
    },
    {
      title: '机构名称',
      dataIndex: 'deptName',
      key: 'deptName',
      ellipsis: true,
      width: '35%',
    },
    {
      title: '机构类型',
      dataIndex: 'level',
      key: 'level',
      width: '15%',
      render: (level: number) => {
        switch (level) {
          case 1:
            return '省级';
          case 2:
            return '市（州）级';
          case 3:
            return '区县级';
          case 4:
            return '医疗机构';
          default:
            return '其他';
        }
      },
    },
    {
      title: '所在省',
      dataIndex: 'province',
      key: 'province',
      width: '15%',
    },
    {
      title: '所在市',
      dataIndex: 'city',
      key: 'city',
      width: '15%',
    },
  ];

  return (
    <Modal
      open={open}
      onCancel={handleCancel}
      title="选择被考核单位"
      centered
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSelectAssessedLab}>
          确定
        </Button>,
      ]}
    >
      <div className="w-full h-[600px] flex flex-col py-2">
        {/* 搜索区域 */}
        <div className="flex justify-between items-center bg-[#f0f2f5] p-3 rounded mb-4">
          <Space>
            <Input
              placeholder="搜索机构名称"
              value={searchValue}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchValue(e.target.value)
              }
              style={{ width: 240 }}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined />}
            />
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
              重置
            </Button>
          </Space>
          <div className="text-[#1677ff]">
            {selectedLabId ? '已选择1家机构' : '请选择被考核单位'}
          </div>
        </div>

        {/* 表格区域 */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <Table
            rowKey="deptId"
            dataSource={tableData}
            columns={columns}
            loading={tableLoading}
            pagination={false}
            size="small"
            rowClassName={(record) =>
              record.deptId === selectedLabId ? 'ant-table-row-selected' : ''
            }
            onRow={handleRowSelection}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedLabId ? [selectedLabId] : [],
              onChange: (selectedRowKeys, selectedRows) => {
                if (selectedRowKeys.length > 0) {
                  setSelectedLabId(selectedRowKeys[0] as string);
                  setSelectedLabRow(selectedRows[0]);
                }
              },
              columnWidth: 60,
            }}
            expandable={{ expandedRowKeys: [], showExpandColumn: false }}
            scroll={{ y: 400 }}
          />

          {/* 分页区域 */}
          <div className="mt-3 flex justify-end">
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              showSizeChanger
              showQuickJumper
              showTotal={(total: number) => `共 ${total} 条`}
              onChange={(page: number, pageSize?: number) =>
                handlePaginationChange(page, pageSize)
              }
              onShowSizeChange={(current: number, size: number) => {
                handlePaginationChange(1, size);
              }}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AssessedLabModal;
