/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, useCallback, useEffect, useRef, useState } from 'react';
import { Button, Col, message, Row, Space, Modal } from 'antd';
import { downloadFile, getFileData, getFileOssObjApi } from '@/api/file';
import { getQATask, getTaskDetailNestedData } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { CloudDownloadOutlined } from '@ant-design/icons';
import FileViewByStream from '@/components/FileViewByStream';
import {
  ProForm,
  ProFormDigit,
  ProFormSlider,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import type {
  ActionType,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import DownloadButton from '@/components/DownloadButton';
import FileView from '@/components/FileView';
import useTimeout from '@/hooks/useTimeout';
import { getFileTypeByName } from '@/utils/upload';
import BaseForm from './BaseForm';

type TEditProps = {
  close: () => void;
  detailId?: string;
};
const layoutProps = {
  colProps: { span: 8 },
};
interface SampleData {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  qaTaskSampleItemVoList: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
}
// 状态下拉
const statusOption = {
  0: { text: '待接收' },
  1: { text: '待检验' },
  2: { text: '待评判' },
  3: { text: '已评判' },
};

// 添加类型定义
interface ParentRow {
  children?: Array<{
    parentName?: string;
    [key: string]: any;
  }>;
  assessedLabName: string;
  [key: string]: any;
}

// 创建Context
export const TaskContext = createContext<any>({});

const Detail: React.FC<TEditProps> = ({ close, detailId }) => {
  const actionRef = useRef<ActionType>();
  const [baseInfo, setBaseInfo] = useState<any>();
  // 导出
  const [loading, setLoading] = useState<boolean>(false);
  // 考核结论-合格分
  const [qualifiedScore, setQualifiedScore] = useState<number>(60);
  // 考核结论-优秀分
  const [excellentScore, setExcellentScore] = useState<number>(80);
  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any[]>([]);
  const formRef = useRef<ProFormInstance>(null);
  const formRef2 = useRef<ProFormInstance>(null);

  const [messageApi, contextHolder] = message.useMessage();

  // 子表缓存
  const [childrenCache, setChildrenCache] = useState<Record<string, any[]>>({});

  // 考核任务类型  机构考核    盲样考核   随机考核
  const [taskType, setTaskType] = useState<string>('');

  // 定义判定标准的列定义
  const getJudgmentColumns = (): ProColumns<Record<string, any>>[] => {
    return [
      {
        title: '考核项目',
        dataIndex: 'itemName',
        key: 'itemName',
        hideInSearch: true,
        width: 120,
      },
      {
        title: '判定标准',
        dataIndex: 'conditionThree',
        key: 'judgmentStandard',
        hideInSearch: true,
        render: (_, record) => (
          <div>
            <div>正确(合格)标准: {record?.conditionThree || '无判定标准'}</div>
            <div>可疑标准: {record?.conditionSix || '无判定标准'}</div>
            <div>错误(不合格)标准: {record?.conditionNine || '无判定标准'}</div>
          </div>
        ),
      },
    ];
  };

  // 渲染判定标准表格
  const renderJudgmentTable = (data: any[]) => {
    return (
      <ProTable
        className="w-full px-1"
        toolBarRender={false}
        options={false}
        search={false}
        bordered
        columns={getJudgmentColumns()}
        dataSource={data}
        pagination={false}
      />
    );
  };

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = useCallback(async () => {
    try {
      if (detailId) {
        const { code, data, msg } = await getQATask(detailId);
        if (code === codeDefinition.QUERY_SUCCESS) {
          setBaseInfo(data);
          setSampleListData(data.qaTaskSampleVos);
          setTaskType(data.taskType);
          // 考核项目
          let qaTaskItemVos = '';
          const assessmentProject = data.qaTaskItemVos.filter(
            (item: any) => item.check === 1
          );
          assessmentProject.forEach((item: any) => {
            qaTaskItemVos = qaTaskItemVos + `${item.name} ${item.ratio}%； `;
          });
          const obj = {
            sampleScore: data.sampleScore,
            docScore: data.docScore,
            scoreCount: 100,
            slider: [data.qualifiedMinScore, data.fineMinScore],
            qaTaskItemVos: qaTaskItemVos,
          };
          formRef2.current?.setFieldsValue(obj);
          setQualifiedScore(data.qualifiedMinScore);
          setExcellentScore(data.fineMinScore);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [detailId]);

  /**
   * @TODO 状态值映射
   */
  const getStatusText = (status: number | any) => {
    switch (status) {
      case 0:
        return '待接收';
      case 1:
        return '待检验';
      case 2:
        return '待评判';
      case 3:
        return '已评判';
      default:
        return '未知状态';
    }
  };

  useEffect(() => {
    getDetailData();
  }, []);

  // 预览
  const [previewFile, setPreviewFile] = useState<any>('');
  const [openPreview, setOpenPreview] = useState<any>(false);
  const handlePreview = async (record: any) => {
    console.log('record:', record);
    if (!record.receiptFile.includes('?')) {
      return;
    } 
    setDownloading(record.id);
    const f = record.receiptFile.split('?');
    if (getFileTypeByName(f[1]) === 'Image') {
      const d = await getFileData(f[0]);
      setPreviewFile({
        url: d,
        name: f[1],
      });
      setOpenPreview('img');
    } else {
      const { code, data, msg } = await getFileOssObjApi(f[0]);
      if (code === 200) {
        if (data && data.length) {
          setPreviewFile(data[0].ossId);
          setOpenPreview('pdf');
        } else {
          message.error(msg);
        }
      }
    }

    setDownloading('');
  };

  // 下载回执单
  const [downloading, setDownloading] = useState<any>('');
  const [setDownloadTimer, clearDownloadTimer] = useTimeout(() => {
    setDownloading('');
  }, 3000);

  const onDownload = async (record: any) => {
    setDownloading(record.id);
    const f = record.receiptFile.split('?');
    downloadFile(
      f[0],
      record.assessedLabName + '.' + f[1].split('.')[f[1].split('.').length - 1]
    );
    setDownloadTimer();
  };

  /**
   * @TODO 获取考核任务明细数据
   */
  const fetchData = async (params: any) => {
    try {
      const response = await getTaskDetailNestedData({
        id: detailId,
        pageNum: params.current,
        pageSize: params.pageSize,
        ...params,
      });

      if (response.code !== codeDefinition.QUERY_SUCCESS || !response.data) {
        messageApi.error(response.msg || '数据加载失败');
        return { data: [], success: false, total: 0 };
      }

      const { rows = [], total = 0 } = response.data;

      // 处理父级数据和子级数据分离
      const parentRows: any[] = [];
      const childrenMap: Record<string, any[]> = {};

      rows
        .filter((parent: ParentRow) => !!parent)
        .forEach((parent: ParentRow) => {
          if (!parent.id) return; // 跳过无效数据

          // 提取父级数据
          const { samples, ...parentData } = parent;
          parentRows.push(parentData);

          // 缓存子级数据
          if (Array.isArray(samples) && samples.length > 0) {
            childrenMap[parent.id] = samples.map((sample) => ({
              ...sample,
              parentName: parent.assessedLabName,
              category: sample.sampleName,
            }));
          } else {
            childrenMap[parent.id] = [];
          }
        });

      // 更新缓存
      setChildrenCache(childrenMap);

      // 使用id作为唯一键，确保所有记录都显示
      const uniqueParentRows = Array.from(
        new Map(parentRows.map((item) => [item.id, item])).values()
      );

      return {
        data: uniqueParentRows,
        total: total,
        success: true,
      };
    } catch (error) {
      console.error('接口异常:', error);
      return { data: [], success: false, total: 0 };
    }
  };

  // 考核任务明细表格单位 Columns
  const orgTableColumns: ProColumns<Record<string, any>>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核单位',
      dataIndex: 'assessedLabName',
    },
    {
      title: '样品数量',
      dataIndex: 'sampleNum',
      hideInSearch: true,
    },
    // 任务状态,0待确认，1待填报，2已填报，3评分草稿，4已评分
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        // 0: { text: '待确认', status: '0' },
        1: { text: '待填报', status: '1' },
        2: { text: '已填报', status: '2' },
        // 3: { text: '评分草稿', status: '3' },
        4: { text: '已评分', status: '4' },
      },
    },
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
      hideInTable: true,
    },
    {
      title: '回执单',
      valueType: 'option',
      key: 'option',
      render: (item, record) => {
        return record.receiptFile ? (
          <Space>
            <Button
              type="primary"
              size="small"
              loading={downloading === record.id}
              onClick={() => handlePreview(record)}
            >
              查看回执单
            </Button>
            <Button
              type="primary"
              size="small"
              loading={downloading === record.id}
              onClick={() => {
                onDownload(record);
              }}
            >
              下载回执单
            </Button>
          </Space>
        ) : (
          ''
        );
      },
    },
  ];

  // 添加子表格 columns
  const childColumns: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核单位',
      dataIndex: 'parentName',
    },
    {
      title: '标准品类别',
      dataIndex: 'sampleName',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
    },
    // 状态(0为未接收，1为已接收，2待评判，3已评判)
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '未接收', status: '0' },
        1: { text: '已接收', status: '1' },
        2: { text: '待评判', status: '2' },
        3: { text: '已评判', status: '3' },
      },
    },
  ];

  return (
    <div className="flex flex-col h-full w-full">
      {contextHolder}
      <div className="flex-1 overflow-auto p-4">
        {/* 基本信息 */}
        <BaseForm
          readonly={true}
          onRef={formRef}
          id={detailId}
          detailInfo={baseInfo}
        />
        <BlockContainer title="任务配置" className="mt-3">
          <ProForm
            formRef={formRef2}
            layout="horizontal"
            grid={true}
            submitter={false}
            validateTrigger="onBlur"
          >
            <ProFormDigit
              {...layoutProps}
              readonly={true}
              name="sampleScore"
              label="样品得分"
              max={100}
              min={0}
            />
            <ProFormDigit
              readonly={true}
              {...layoutProps}
              name="docScore"
              max={100}
              min={0}
              label="资料得分"
            />
            <ProFormText
              readonly={true}
              {...layoutProps}
              disabled
              label="总分"
              name="scoreCount"
            />
            <div className="relative flex h-[80px] w-full py-5">
              <ProFormSlider
                disabled
                name="slider"
                label="考核结论"
                marks={{
                  0: '0分',
                  100: '100分',
                }}
                range
                fieldProps={{
                  range: { draggableTrack: true },
                  max: 100,
                  min: 0,
                  className: 'w-[75%] flex-1',
                  tooltip: {
                    formatter: (value) => <span>{value}分</span>,
                    placement: 'bottom',
                    open: true,
                    color: '#1677FF',
                  },
                  styles: {
                    track: {
                      background: '#90C9FE',
                    },
                  },
                }}
              />
              <div className="text-[#FF3D00] w-[180px] absolute top-[25px] right-10">
                <div>优&nbsp;&nbsp;&nbsp;秀：{excellentScore} ≤ 得分</div>
                <div>
                  合&nbsp;&nbsp;&nbsp;格：{qualifiedScore} ≤ 得分 ＜
                  {excellentScore}
                </div>
                <div>不合格：&nbsp;0 ≤ 得分 ＜ {qualifiedScore}</div>
              </div>
            </div>
            <ProFormText
              readonly={true}
              disabled
              colProps={{ span: 24 }}
              label="考核项目"
              name="qaTaskItemVos"
            />
          </ProForm>
          <Row gutter={[25, 25]} className="mt-3">
            {sampleListData?.map((item: any, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                >
                  <div className="h-[300px] overflow-auto overflow-x-hidden">
                    <ProForm<{ table: any[] }>
                      readonly={true}
                      layout="horizontal"
                      grid={true}
                      submitter={false}
                    >
                      <ProFormText
                        readonly={true}
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        label="参考结果"
                        fieldProps={{
                          value: item.value,
                        }}
                      />

                  <ProFormText
                        readonly={true}
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="realCode"
                        label="真实编号"
                        fieldProps={{
                          value: item.realCode,
                        }}
                      />

                      {renderJudgmentTable(item?.qaTaskSampleItemVoList)}
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
          </Row>
        </BlockContainer>
        <BlockContainer
          title="明细配置"
          bg="#F5F5F5"
          bodyStyle={{ padding: '10px' }}
          className="mt-3 border-[#D9D9D9]"
        >
          <ProTable
            name="orgTable"
            columns={orgTableColumns}
            style={{ height: '100%' }}
            scroll={{ y: 'calc(100% - 48px)' }}
            actionRef={actionRef}
            cardBordered
            rowKey="id"
            expandable={{
              expandedRowRender: (record) => {
                // 从缓存中获取子数据
                const childDataSource = childrenCache[record.id] || [];

                return childDataSource.length > 0 ? (
                  <ProTable
                    columns={childColumns}
                    dataSource={childDataSource}
                    pagination={false}
                    search={false}
                    rowKey="id"
                    options={false}
                    style={{
                      marginLeft: 40,
                      width: 'calc(100% - 40px)',
                    }}
                  />
                ) : (
                  <div style={{ marginLeft: 40, padding: '16px 0' }}>
                    暂无样本数据
                  </div>
                );
              },
              expandRowByClick: true,
              // 仅当有子数据时才显示展开图标
              rowExpandable: (record) => !!childrenCache[record.id]?.length,
            }}
            request={async (params) => {
              return await fetchData(params);
            }}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            toolBarRender={() => [
              <DownloadButton
                url="/qa/receipt-forms/downFile"
                params={{
                  taskId: detailId,
                }}
                method="get"
                type="primary"
              >
                下载全部回执单
              </DownloadButton>,
            ]}
            options={false}
          />
        </BlockContainer>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button onClick={close} type="default">
          关闭
        </Button>
      </div>
      
      <Modal
        width="90%"
        title="文件预览"
        onCancel={() => setOpenPreview(false)}
        open={openPreview === 'pdf'}
        footer={null}
        destroyOnClose
      >
        <FileViewByStream fileId={previewFile} isPreview />
      </Modal>
      <FileView
        open={openPreview === 'img'}
        file={previewFile}
        closeDetail={() => {
          setOpenPreview(false);
        }}
      />
    </div>
  );
};

export default Detail;
