/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-throw-literal */

/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  memo,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Drawer, message, Modal, Tooltip } from 'antd';
import { downloadFile } from '@/api/file';
import { getYearList } from '@/api/settings';
import { getTempList } from '@/api/temp';
import taskTypeImge from '@/assets/taskType.png';
import { codeDefinition } from '@/constants';
import { useTokenStore } from '@/store';
import { useDictStore } from '@/store/dict';
import { useQualityStore } from '@/store/quality';
import {
  FormInstance,
  ProForm,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import FileView from '@/components/FileView';
import FileViewByStream from '@/components/FileViewByStream';
import downloadLocalFile from '@/utils/downloadLocalFile';
import { FormInitVal, formItemLayout } from '../data';
import AdjustTemplate from './AdjustTemplate';
import { TaskContext } from './Edit';

const layoutProps = {
  colProps: { ...formItemLayout },
};
type TEditProps = {
  id?: any;
  detailInfo?: any;
  onRef?: any;
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isShowChar?: boolean; // 是否显示使用模板和任务类型字段
  taskType?: string;
};

type Options = {
  label: string;
  value: string;
};

const BaseForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onRef,
  readonly = false,
  readonlyAll = false,
  isShowChar = true,
  taskType,
}) => {
  const { assessmentTaskTypeEnums } = useDictStore();

  const { setRefType } = useContext(TaskContext);
  const [templateOnform, setTemplateOnform] = useState<any>([]);
  // 表单实例
  const formRef = useRef<FormInstance>(null);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  // word等文件预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [fileUrl, setFileUrl] = useState<any>();
  // 是否是草稿
  const [isDraft, setIsDraft] = useState<boolean>(false);
  // 模板预览弹窗
  const [openPreviewTemplate, setOpenPreviewTemplate] =
    useState<boolean>(false);
  // 考核类型ID
  const [assessmentTypeId, setAssessmentTypeId] = useState<string>('');
  const { token } = useTokenStore();

  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');

  // 获取table中需要的枚举
  const {
    assessmentTypesOnForm, //所有
    assessmentTypesOnForm2, // 带权限
    getAssessmentTypes,
    getAssessmentTaskTypes,
    getAssessmentTypesOnForm2,
  } = useQualityStore();

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSave,
      getTaskType: async () => {
        const task = await formRef.current?.getFieldValue('taskType');
        return task;
      },
      handleParams: async () => {
        const formDataInfo = formRef.current?.getFieldsValue();
        let attachmentIds = [];
        if (
          formDataInfo.attachmentIds &&
          formDataInfo.attachmentIds.length > 0
        ) {
          attachmentIds = formDataInfo.attachmentIds
            .filter(
              (item: any) =>
                item.response && item.response.data && item.response.data.ossId
            )
            .map((item: any) => {
              return {
                name: item.response.data.fileName,
                id: item.response.data.ossId,
                url: item.response.data.url,
              };
            });
        }
        return {
          ...formDataInfo,
          attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
        };
      },
      handleValidateFields: async () => {
        try {
          await formRef.current?.validateFields();
        } catch (error) {
          throw '请完善基本信息';
        }
      },
    };
  });

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    try {
      if (id && detailInfo) {
        const p = clone(detailInfo);
        // try {
        p.attachmentIds = p.attachmentIds
          ? JSON.parse(p.attachmentIds).map((item: any) => {
              return {
                uid: item.id,
                name: item.name,
                status: 'done',
                type: 'application/msword',
                url: item.id,
                response: {
                  data: {
                    fileName: item.name,
                    ossId: item.id,
                    url: item.url,
                  },
                },
              };
            })
          : [];
        setIsDraft(detailInfo.status === '0');
        formRef.current?.setFieldsValue(p);
        setAssessmentTypeId(detailInfo.assessmentType);
      } else {
        formRef.current?.setFieldsValue({
          particularYear: new Date().getFullYear(),
        });
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 获取使用模板
   */
  const getTemplateOnform = async (templateTypeId: number | string) => {
    formRef.current?.setFieldValue('templateType', null);
    if (!templateTypeId) {
      setTemplateOnform([]);
      return;
    }
    try {
      const params = {
        assessmentType: templateTypeId,
        current: 1,
        size: 100,
        status: 1,
      };
      const { code, rows } = await getTempList(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        const newData = rows?.map((_item: any) => ({
          label: _item.name,
          value: _item.id,
          code: _item.code,
        }));
        setTemplateOnform(newData);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async () => {
    await formRef.current?.validateFields();
    let formInfo = cloneDeep(formRef.current?.getFieldsValue());
    // 模板名称-模板code
    const templateObj =
      templateOnform.filter(
        (_item: Options) => formInfo.templateId === _item.value
      )![0] || {};
    let attachmentIds = [];
    if (formInfo.attachmentIds && formInfo.attachmentIds.length > 0) {
      attachmentIds = formInfo.attachmentIds
        .filter(
          (item: any) =>
            item.response && item.response.data && item.response.data.ossId
        )
        .map((item: any) => {
          return {
            name: item.response.data.fileName,
            id: item.response.data.ossId,
            url: item.response.data.url,
          };
        });
    }
    const params = {
      ...formInfo,
      templateName: templateObj.label,
      templateCode: templateObj.code,
      attachmentIds: attachmentIds ? JSON.stringify(attachmentIds) : '',
    };
    return params;
  };
  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };
  /**
   * @TODO 预览模板
   */
  const handlePreviewTemplate = (info: any) => {
    setOpenPreviewTemplate(true);
  };

  /**
   * @TODO 任务类型图示下载
   */
  const handleDownLoadPhoto = (info: any) => {
    downloadLocalFile(taskTypeImge, '考核任务类型图示.png');
  };

  /**
   * 处理文件下载
   * @param info 文件信息
   */
  const handleDownload = (info: any) => {
    // 检查文件信息
    if (!info) {
      message.error('文件信息不完整，无法下载');
      return;
    }

    // 获取文件ID
    const fileId = info.response?.data?.id || info.id || info.uid;
    if (!fileId) {
      message.error('文件ID不存在，无法下载');
      return;
    }

    // 获取文件名
    const fileName = info.name || '附件';

    // 调用API下载文件
    downloadFile(fileId, fileName)
      .then(() => {
        message.success(`${fileName} 下载成功`);
      })
      .catch((error) => {
        console.error('下载文件失败:', error);
        message.error('下载文件失败，请稍后重试');
      });
  };

  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  useEffect(() => {
    queryYearList();
    getAssessmentTaskTypes();
    if (!readonly && !isDraft) {
      getAssessmentTypesOnForm2();
    } else {
      getAssessmentTypes();
    }
  }, []);

  useEffect(() => {
    assessmentTypeId && getTemplateOnform(assessmentTypeId);
  }, [assessmentTypeId]);

  /**
   * 获取年份List
   */
  const [yearList, setYearList] = useState<any>([]);
  const queryYearList = async () => {
    try {
      const { data } = await getYearList();
      setYearList(data || []);
    } catch (err) {
      throw new Error(`Error: ${err}`);
    }
  };

  return (
    <>
      <BlockContainer title="基本信息">
        <ProForm
          readonly={readonly || readonlyAll}
          formRef={formRef}
          {...formItemLayout}
          layout="horizontal"
          grid={true}
          submitter={false}
          initialValues={FormInitVal}
          onValuesChange={(_: any, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormSelect
            readonly={readonly || isDraft}
            options={yearList}
            rules={[{ required: true, message: '请选择年份' }]}
            name="particularYear"
            label="年份"
            {...layoutProps}
          />
          <ProFormText
            name="name"
            label="任务名称"
            placeholder="请输入任务名称"
            rules={[
              {
                required: true,
                validator(rule, value, callback) {
                  if (!value) {
                    callback('请输入任务名称');
                  }
                  if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value)) {
                    callback('任务名称只能由中文字符、英文字符、数字组成');
                  } else {
                    callback();
                  }
                },
              },
            ]}
            {...layoutProps}
          />
          <ProFormSelect
            readonly={readonly || isDraft}
            options={
              !readonly && !isDraft
                ? assessmentTypesOnForm2
                : assessmentTypesOnForm
            }
            rules={[{ required: true, message: '请选择考核类型' }]}
            name="assessmentType"
            label="考核类型"
            {...layoutProps}
            onChange={(v: any) => {
              formRef.current?.setFieldValue('templateId', '');
              setAssessmentTypeId(v);
            }}
          />
          {isShowChar && (
            <>
              {
                // 草稿显示
                !readonly && isDraft ? (
                  <ProForm.Item noStyle>
                    <ProFormText
                      readonly={readonly || isDraft}
                      name="templateName"
                      label="使用模板"
                      placeholder="请输入使用模板"
                      rules={[{ required: true, message: '请输入使用模板' }]}
                      {...layoutProps}
                    >
                      <Tooltip title="点击预览模板">
                        <span
                          className="text-[#1677FF] cursor-pointer"
                          onClick={handlePreviewTemplate}
                        >
                          {formRef.current?.getFieldValue('templateName')}
                        </span>
                      </Tooltip>
                    </ProFormText>
                  </ProForm.Item>
                ) : (
                  <ProForm.Item noStyle shouldUpdate>
                    {(form: FormInstance) => {
                      // 根据考核类型获取使用模板
                      // const assessmentTypeItem = form.getFieldValue('assessmentType')
                      // setAssessmentTypeId(assessmentTypeItem)  // 这个代码要报错
                      return (
                        <ProFormSelect
                          readonly={readonly || isDraft}
                          options={templateOnform}
                          rules={[
                            { required: true, message: '请选择使用模板' },
                          ]}
                          name="templateId"
                          label="使用模板"
                          disabled={!assessmentTypeId}
                          {...layoutProps}
                        />
                      );
                    }}
                  </ProForm.Item>
                )
              }
            </>
          )}
          {isShowChar && (
            <ProFormSelect
              readonly={readonly || isDraft}
              options={assessmentTaskTypeEnums}
              rules={[{ required: true, message: '请选择任务类型' }]}
              name="taskType"
              label={
                readonly || isDraft ? (
                  <span>任务类型</span>
                ) : (
                  <Tooltip title="点击下载图示解读">
                    <span
                      className="text-[#1677FF] cursor-pointer"
                      onClick={handleDownLoadPhoto}
                    >
                      任务类型
                    </span>
                  </Tooltip>
                )
              }
              {...layoutProps}
            />
          )}
          <ProFormDatePicker
            readonly={readonly}
            rules={[{ required: true, message: '请选择' }]}
            initialValue={dayjs()}
            name="endDate"
            label="结束日期"
            width={'lg'}
            {...layoutProps}
          />
          <ProFormText
            readonly={readonly}
            name="people"
            label="任务负责人"
            placeholder="请输入任务负责人"
            rules={[{ required: true, message: '请输入任务负责人' }]}
            {...layoutProps}
          />
          <ProFormText
            readonly={readonly}
            name="phone"
            label="联系电话"
            placeholder="请输入联系电话"
            rules={[{ required: true, message: '请输入联系电话' }]}
            {...layoutProps}
          />
          <ProFormTextArea
            readonly={readonly}
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.005 }}
            name="remark"
            label="备注信息"
          />
          <EProFormUploadButton
            name="attachmentIds"
            label="附件信息"
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.005 }}
            max={10}
            readonly={readonly}
          />
        </ProForm>
      </BlockContainer>
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />

      {/* 预览模板 */}
      <Drawer
        width="85%"
        title={formRef.current?.getFieldValue('templateName')}
        onClose={() => setOpenPreviewTemplate(false)}
        open={openPreviewTemplate}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <AdjustTemplate
          close={() => setOpenPreviewTemplate(false)}
          taskId={id}
          isPreview
        />
      </Drawer>
      {/* 预览pdf\word */}
      {openPreview && (
        <>
          <Modal
            width="60%"
            title="文件预览"
            onCancel={() => setOpenPreview(false)}
            open={openPreview}
            footer={null}
            destroyOnClose
          >
            <FileViewByStream fileId={previewFileId} isPreview />
          </Modal>
        </>
      )}
    </>
  );
};

export default memo(BaseForm);
