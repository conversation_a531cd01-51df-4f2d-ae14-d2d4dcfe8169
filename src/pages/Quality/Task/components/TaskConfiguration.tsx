/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable no-throw-literal */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable no-self-assign */
import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Checkbox,
  Col,
  Input,
  InputNumber,
  message,
  Modal,
  Pagination,
  Row,
  Select,
  Space,
  Table,
} from 'antd';
import { queryOrgList } from '@/api/quality';
import { getDeptPage } from '@/api/system';
import { codeDefinition } from '@/constants';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormDigit,
  ProFormSlider,
  ProFormText,
} from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import RequiredTag from '@/components/RequiredTag';
import './index.less';

type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit?: (params: any) => void;
  onRef?: any;
  taskConfigurationForm: any; // 任务配置信息
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  setFormInfo: (val: any) => void;
};
const layoutProps = {
  colProps: { span: 8 },
};

const TaskConfiguration: React.FC<TEditProps> = ({
  onRef,
  readonly,
  readonlyAll = false,
  taskConfigurationForm,
  setFormInfo,
}) => {
  // 表单实例
  const formRef = useRef<FormInstance>(null);
  // 考核结论-合格分
  const [qualifiedScore, setQualifiedScore] = useState<number>(60);
  // 考核结论-优秀分
  const [excellentScore, setExcellentScore] = useState<number>(80);
  // 考核机构-初始数据
  const [organizationCheckBox, setOrganizationCheckBox] = useState<any[]>([]);
  /* 考核机构-当前选中 */
  const [checkedOrgList, setCheckedOrgList] = useState<any[]>([]);
  // 考核项目-初始数据
  const [assessmentCheckBox, setAssessmentCheckBox] = useState<any[]>([]);
  //  考核项目-当前选中
  const [checkedList, setCheckedList] = useState<any[]>([]);
  // 考核项目输入
  const [inputValues, setInputValues] = useState<{
    [key: string]: number | null;
  }>({});

  // 机构选择Modal相关状态
  const [orgModalVisible, setOrgModalVisible] = useState<boolean>(false);
  const [orgTableData, setOrgTableData] = useState<any[]>([]);
  const [orgPagination, setOrgPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [orgTableLoading, setOrgTableLoading] = useState<boolean>(false);
  const [selectedOrgRows, setSelectedOrgRows] = useState<any[]>([]);
  const [selectedOrgRowKeys, setSelectedOrgRowKeys] = useState<any[]>([]);

  // 搜索相关状态
  const [searchValue, setSearchValue] = useState<string>('');
  const [filteredOrgList, setFilteredOrgList] = useState<any[]>([]);

  // Modal搜索状态
  const [modalNameSearch, setModalNameSearch] = useState<string>('');
  const [modalTypeSearch, setModalTypeSearch] = useState<string[] | undefined>(
    undefined
  );

  // 统计数据
  const [statsData, setStatsData] = useState<{
    total: number;
    cityCount: number;
    countyCount: number;
  }>({
    total: 0,
    cityCount: 0,
    countyCount: 0,
  });

  // 主表格分页状态
  const [mainTablePagination, setMainTablePagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleFormParams,
      handleValidateFields,
    };
  });

  /**
   * @TODO 表单校验
   */
  const handleValidateFields = async () => {
    try {
      await formRef.current?.validateFields();
      if (JSON.stringify(inputValues) === '{}') {
        throw new Error('至少选择一个考核项目');
      }
      // 判定分数比例是否等于100
      let sum = Object.entries(inputValues)
        // .filter(([key, value]) => typeof value === 'number')
        .map(([key, value]) => parseFloat(value as any))
        .reduce((accumulator, value) => accumulator + value! * 100, 0);

      if (sum !== 10000) {
        throw new Error('已选中考核项目分数比例之和必须为100%');
      }
    } catch (error: any) {
      // 表单校验不通过返回值
      if (typeof error === 'object' && error?.errorFields) {
        throw '请完善任务配置';
      } else {
        throw new Error(`${error}`);
      }
    }
  };

  /**
   * @TODO 获取配置任务表单参数
   */
  const handleFormParams = async () => {
    try {
      let form1 = cloneDeep(formRef.current?.getFieldsValue());
      let params: any = {
        docScore: form1.docScore,
        scoreCount: form1.scoreCount,
        sampleScore: form1.sampleScore,
        qualifiedMinScore: form1.slider![0],
        fineMinScore: form1.slider![1],
        qaTaskItemVos: assessmentCheckBox.map((_item) => ({
          ..._item,
          ratio: inputValues[_item.id],
        })),
      };
      if (taskConfigurationForm.taskType === 'org') {
        params['orgVos'] = selectedOrgRows.map((_item) => ({
          assessedLabId: _item.deptId,
          assessedLabCode: _item.deptCode,
          assessedLabName: _item.deptName,
        }));
      }
      return params;
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 分页查询机构列表
   */
  const queryDeptPageList = async (pageParams: any, searchParams: any = {}) => {
    try {
      setOrgTableLoading(true);
      const params = {
        pageSize: pageParams.pageSize || 10,
        pageNum: pageParams.current || 1,
        ...searchParams,
      };

      const response = await getDeptPage(params);

      if (response.code === codeDefinition.QUERY_SUCCESS && response.data) {
        const { rows, total } = response.data;
        setOrgTableData(rows || []);
        setOrgPagination({
          ...orgPagination,
          current: params.pageNum,
          pageSize: params.pageSize,
          total: total || 0,
        });
      } else {
        message.error(response.msg || '获取机构列表失败');
      }
      setOrgTableLoading(false);
    } catch (error) {
      setOrgTableLoading(false);
      message.error('获取机构列表失败');
      console.error(error);
    }
  };

  /**
   * @TODO 搜索机构
   */
  const handleSearchOrg = () => {
    if (!searchValue.trim()) {
      setFilteredOrgList([]);
      return;
    }

    // 在已选机构中搜索
    getDeptPage({
      deptName: searchValue,
      pageSize: 9999, // 使用大数值确保获取所有匹配结果
      pageNum: 1,
    })
      .then((response: any) => {
        if (response.code === codeDefinition.QUERY_SUCCESS && response.data) {
          const searchResults = response.data.rows || [];

          // 只保留已选中的机构
          const filteredResults = searchResults.filter((item: any) =>
            selectedOrgRowKeys.includes(item.deptId)
          );

          setFilteredOrgList(filteredResults);

          // 重置主表格分页到第一页
          setMainTablePagination({
            ...mainTablePagination,
            current: 1,
            total: filteredResults.length,
          });
        }
      })
      .catch((error) => {
        console.error('搜索机构失败', error);
        message.error('搜索机构失败');
      });
  };

  /**
   * @TODO 重置搜索
   */
  const handleResetSearch = () => {
    setSearchValue('');
    setFilteredOrgList([]);

    // 重置主表格分页
    setMainTablePagination({
      ...mainTablePagination,
      current: 1,
      total: selectedOrgRows.length,
    });
  };

  /**
   * @TODO 更新统计数据
   */
  const updateStatsData = (selectedIds: any[] = [], selectedOrgs?: any[]) => {
    // 如果提供了机构数据，直接使用，否则从selectedOrgRows获取
    const orgs = selectedOrgs || selectedOrgRows;

    // 统计市州级机构数量 (level = 2)
    const cityCount = orgs.filter(
      (item) => item.level === 2 || item.level === '2'
    ).length;

    // 统计区县级机构数量 (level = 3)
    const countyCount = orgs.filter(
      (item) => item.level === 3 || item.level === '3'
    ).length;

    setStatsData({
      total: selectedIds.length,
      cityCount,
      countyCount,
    });
  };

  /**
   * @TODO 移出选中机构
   */
  const handleRemoveSelected = () => {
    if (!checkedOrgList.length) return;

    // 移除已选机构
    const newSelectedOrgRows = selectedOrgRows.filter(
      (item) => !checkedOrgList.includes(item.deptId)
    );
    const newSelectedOrgKeys = selectedOrgRows
      .filter((item) => !checkedOrgList.includes(item.deptId))
      .map((item) => item.deptId);

    // 更新状态
    setSelectedOrgRows(newSelectedOrgRows);
    setSelectedOrgRowKeys(newSelectedOrgKeys);
    setCheckedOrgList([]);

    // 更新表单值
    formRef.current?.setFieldValue('orgVos', newSelectedOrgKeys);

    // 更新统计信息
    updateStatsData(newSelectedOrgKeys, newSelectedOrgRows);

    // 重置主表格分页到第一页
    setMainTablePagination({
      ...mainTablePagination,
      current: 1,
    });

    message.success('已移出选中机构');
  };

  /**
   * @TODO 打开机构选择弹窗
   */
  const openOrgSelectModal = () => {
    // 初始化选中状态 - 使用checkedOrgList作为已选择的机构ID列表
    setSelectedOrgRowKeys(checkedOrgList);

    // 初始化表格数据
    queryDeptPageList(orgPagination);

    // 重置搜索状态
    setModalNameSearch('');
    setModalTypeSearch(undefined);
    setOrgModalVisible(true);
  };

  /**
   * @TODO 关闭机构选择弹窗
   */
  const closeOrgSelectModal = () => {
    setOrgModalVisible(false);
  };

  /**
   * @TODO 确认机构选择
   */
  const confirmOrgSelection = () => {
    // 保存Modal中选择的机构数据，但不自动选中它们
    // 只保存selectedOrgRowKeys作为表单值，但不设置为已选中状态
    formRef.current?.setFieldValue('orgVos', selectedOrgRowKeys);

    // 更新统计信息
    updateStatsData(selectedOrgRowKeys, selectedOrgRows);

    // 重置主表格分页到第一页
    setMainTablePagination({
      ...mainTablePagination,
      current: 1,
      total: selectedOrgRows.length,
    });

    // 清空当前选中项，让用户可以手动选择要移除的机构
    setCheckedOrgList([]);

    // 关闭弹窗
    closeOrgSelectModal();
  };

  /**
   * @TODO 机构表格行选择变化
   */
  const handleOrgRowSelectionChange = (
    selectedRowKeys: any[],
    selectedRows: any[]
  ) => {
    // 更新选中的行keys
    setSelectedOrgRowKeys(selectedRowKeys);

    // 获取当前页显示的机构ID列表
    const currentPageIds = orgTableData.map((item) => item.deptId);

    // 从selectedOrgRows中移除当前页已取消选择的行
    const remainingRows = selectedOrgRows.filter(
      (row) =>
        !currentPageIds.includes(row.deptId) ||
        selectedRowKeys.includes(row.deptId)
    );

    // 添加当前页新选中的行
    const newSelectedRows = orgTableData.filter(
      (row) =>
        selectedRowKeys.includes(row.deptId) &&
        !selectedOrgRows.some((selected) => selected.deptId === row.deptId)
    );

    // 合并保存的行和新选中的行
    setSelectedOrgRows([...remainingRows, ...newSelectedRows]);
  };

  /**
   * @TODO 考核项目
   */
  // 考核项目-是否全选
  const checkAllAssessment = useCallback(() => {
    if (!assessmentCheckBox?.length) return;
    if (assessmentCheckBox.length === checkedList.length) {
      return true;
    } else {
      return;
    }
  }, [checkedList]);

  // 考核项目-全选
  const onCheckAllChange = (type?: string) => {
    if (type) {
      assessmentCheckBox.forEach((_item) => {
        _item.check = 1;
      });
      setFormInfo([...assessmentCheckBox]);
      setAssessmentCheckBox([...assessmentCheckBox]);
      setCheckedList(assessmentCheckBox.map((item) => item.id));
      formRef.current?.setFieldValue(
        'qaTaskItemVos',
        assessmentCheckBox.map((item) => item.id)
      );
    } else {
      assessmentCheckBox.forEach((_item) => {
        _item.check = 0;
      });
      setFormInfo([]);
      setAssessmentCheckBox(assessmentCheckBox);
      setCheckedList([]);
      formRef.current?.setFieldValue('qaTaskItemVos', []);
    }
  };

  // 考核项目-选中change
  const onChangeCheckBox = (newCheckedValue: any) => {
    assessmentCheckBox.forEach((_item) => {
      _item.check = 0;
      newCheckedValue.forEach((_item2: any) => {
        if (_item2 === _item.id) {
          _item.check = 1;
          _item.ratio = _item.ratio;
        }
      });
    });
    setFormInfo([...assessmentCheckBox]);
    setCheckedList(newCheckedValue);
    // 清除未选中 Checkbox 对应的 input 值
    const newInputValues = assessmentCheckBox.reduce((acc: any, item) => {
      if (newCheckedValue.includes(item.id)) {
        acc[item.id] = inputValues[item.id] ?? '';
      }
      return acc;
    }, {});
    setInputValues(newInputValues);
  };

  // 滑块
  const onSliderChange = (value: number | number[]) => {
    if (value && Array.isArray(value)) {
      const [left, right] = [...value];
      if (left === right && right === 0) {
        setExcellentScore(qualifiedScore + 1);
        message.error('优秀分和及格分不能同时等于0');
      } else {
        setQualifiedScore(value[0]);
        setExcellentScore(value[1]);
      }
    }
  };

  useEffect(() => {
    // 考核项目
    const { qaTaskItemVos, orgVos } = taskConfigurationForm;
    taskConfigurationForm && setAssessmentCheckBox(qaTaskItemVos);
    // 选中考核项目
    const selected = qaTaskItemVos.filter((item: any) => item.check === 1);
    // 选中考核项目Id
    const selectCheckBoxKey = selected?.map((_item: any) => _item.id);
    setCheckedList(selectCheckBoxKey);
    // 考核任务分数比例
    let obj: any = {};
    selected?.forEach((item: any) => {
      obj[item.id] = item.ratio ? Number(item.ratio) : item.ratio;
    });
    setInputValues(obj);

    // 清空原有的机构数据，确保不会显示之前的数据
    setSelectedOrgRows([]);
    setSelectedOrgRowKeys([]);
    setCheckedOrgList([]);

    // 考核机构 - 处理已选择的机构数据
    if (orgVos && orgVos.length > 0) {
      // 将orgVos中的数据直接格式化后设置为已选机构
      const formattedOrgs = orgVos.map((item: any) => ({
        deptId: item.assessedLabId,
        deptCode: item.assessedLabCode,
        deptName: item.assessedLabName,
        // 其他字段可能需要通过API获取
      }));

      // 获取已选机构的ID列表
      const orgListArr = orgVos.map((item: any) => item.assessedLabId);

      // 如果需要获取完整机构信息（包括level等字段），使用API
      getDeptPage({
        deptIds: orgListArr.join(','),
        pageSize: 9999, // 确保获取所有匹配的记录
        pageNum: 1,
      })
        .then((response: any) => {
          if (response.code === codeDefinition.QUERY_SUCCESS && response.data) {
            const selectedOrgs = response.data.rows || [];

            // 确保只保留orgVos中的机构，过滤掉可能多返回的其他机构
            const filteredOrgs = selectedOrgs.filter((org: any) =>
              orgListArr.includes(org.deptId)
            );

            // 设置已选择的机构作为表格数据源
            setSelectedOrgRows(
              filteredOrgs.length > 0 ? filteredOrgs : formattedOrgs
            );
            setSelectedOrgRowKeys(orgListArr);

            // 更新统计信息
            updateStatsData(
              orgListArr,
              filteredOrgs.length > 0 ? filteredOrgs : formattedOrgs
            );

            // 设置主表格分页
            setMainTablePagination({
              ...mainTablePagination,
              current: 1,
              total: filteredOrgs.length || formattedOrgs.length,
            });
          } else {
            // 如果API调用失败，使用格式化的数据
            setSelectedOrgRows(formattedOrgs);
            setSelectedOrgRowKeys(orgListArr);
            updateStatsData(orgListArr, formattedOrgs);
            setMainTablePagination({
              ...mainTablePagination,
              current: 1,
              total: formattedOrgs.length,
            });
          }
        })
        .catch((error) => {
          console.error('获取机构详情失败', error);
          // 如果API调用异常，使用格式化的数据
          setSelectedOrgRows(formattedOrgs);
          setSelectedOrgRowKeys(orgListArr);
          updateStatsData(orgListArr, formattedOrgs);
          setMainTablePagination({
            ...mainTablePagination,
            current: 1,
            total: formattedOrgs.length,
          });
        });
    } else {
      // 没有已选机构，确保表格为空
      setMainTablePagination({
        ...mainTablePagination,
        current: 1,
        total: 0,
      });
    }

    // 考核结论合格分数
    const { qualifiedMinScore, fineMinScore } = taskConfigurationForm;
    setQualifiedScore(qualifiedMinScore ?? 60);
    setExcellentScore(fineMinScore ?? 80);
    formRef.current?.setFieldsValue({
      ...taskConfigurationForm,
      slider: [
        qualifiedMinScore ?? qualifiedScore,
        fineMinScore ?? excellentScore,
      ],
      orgVos: orgVos?.map((item: any) => item.assessedLabId) || [],
    });
  }, [taskConfigurationForm]);

  useEffect(() => {
    // 只在Modal弹窗打开时加载机构列表数据
    if (orgModalVisible) {
      // 在弹窗打开时，使用当前分页和搜索条件查询数据
      queryDeptPageList(orgPagination, {
        deptName: modalNameSearch || undefined,
        level: modalTypeSearch || undefined,
      });
    }
  }, [
    orgModalVisible,
    orgPagination.current,
    orgPagination.pageSize,
    modalNameSearch,
    modalTypeSearch,
  ]);

  useEffect(() => {
    // 考核机构列表不再在初始化时全量加载
    // 改为打开弹窗时按需分页加载
  }, []);

  // 在数据加载或改变时更新统计数据
  useEffect(() => {
    updateStatsData(checkedOrgList);
  }, [checkedOrgList]);

  // 定义机构选择表格列
  const orgColumns = [
    {
      title: '机构编码',
      dataIndex: 'deptCode',
      key: 'deptCode',
      width: '25%',
    },
    {
      title: '机构名称',
      dataIndex: 'deptName',
      key: 'deptName',
      ellipsis: true,
      width: '20%',
    },
    {
      title: '机构类型',
      dataIndex: 'level',
      key: 'level',
      width: '10%',
      render: (level: number) => {
        switch (level) {
          case 1:
            return '省级';
          case 2:
            return '市（州）级';
          case 3:
            return '区县级';
          case 4:
            return '医疗机构';
          default:
            return '其他';
        }
      },
    },
    {
      title: '所在省',
      dataIndex: 'province',
      key: 'province',
      width: '15%',
    },
    {
      title: '所在市',
      dataIndex: 'city',
      key: 'city',
      width: '15%',
    },
    {
      title: '所在区县',
      dataIndex: 'district',
      key: 'district',
      width: '15%',
    },
  ];

  const selectedTableColumns = [
    {
      title: '机构名称',
      dataIndex: 'deptName',
      key: 'deptName',
      ellipsis: true,
    },
    {
      title: '机构类型',
      dataIndex: 'level',
      key: 'level',
      width: '30%',
      render: (level: number) => {
        switch (level) {
          case 1:
            return '省级';
          case 2:
            return '市（州）级';
          case 3:
            return '区县级';
          case 4:
            return '医疗机构';
          default:
            return '其他';
        }
      },
    },
  ];

  /**
   * @TODO 分页变化处理
   */
  const handleOrgPaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...orgPagination,
      current: page,
      pageSize: pageSize || orgPagination.pageSize,
    };
    setOrgPagination(newPagination);

    // 构建搜索参数
    const searchParams: Record<string, any> = {};
    if (modalNameSearch) {
      searchParams.deptName = modalNameSearch;
    }
    if (modalTypeSearch && modalTypeSearch.length > 0) {
      searchParams.level = modalTypeSearch.join(',');
    }

    // 使用新的分页参数查询数据
    queryDeptPageList(newPagination, searchParams);
  };

  /**
   * @TODO 弹窗搜索机构
   */
  const handleModalSearch = () => {
    const searchParams: Record<string, any> = {};

    // 按名称筛选
    if (modalNameSearch.trim()) {
      searchParams.deptName = modalNameSearch;
    }

    // 按类型筛选（多选情况，用逗号连接）
    if (modalTypeSearch && modalTypeSearch.length > 0) {
      searchParams.level = modalTypeSearch.join(',');
    }

    // 重置到第一页
    const newPagination = { ...orgPagination, current: 1 };
    setOrgPagination(newPagination);

    // 查询数据
    queryDeptPageList(newPagination, searchParams);
  };

  /**
   * @TODO 重置弹窗搜索
   */
  const handleModalResetSearch = () => {
    setModalNameSearch('');
    setModalTypeSearch(undefined);

    // 重置到第一页
    const newPagination = { ...orgPagination, current: 1 };
    setOrgPagination(newPagination);

    // 查询数据
    queryDeptPageList(newPagination);
  };

  /**
   * @TODO 主表格分页变化处理
   */
  const handleMainTablePaginationChange = (page: number, pageSize?: number) => {
    setMainTablePagination({
      ...mainTablePagination,
      current: page,
      pageSize: pageSize || mainTablePagination.pageSize,
    });
  };

  return (
    <>
      <div className="mt-4">
        <BlockContainer title="任务配置">
          <ProForm
            formRef={formRef}
            layout="horizontal"
            grid={true}
            submitter={false}
            validateTrigger="onBlur"
            autoFocusFirstInput={true}
            initialValues={{
              sampleScore: null,
              orgVos: [],
              qaTaskItemVos: [],
              docScore: null,
              scoreCount: 100,
              slider: [qualifiedScore, excellentScore],
            }}
            onValuesChange={(
              changeValue: Record<string, any>,
              values: Record<string, any>
            ) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              // 计算总得分
              if ('sampleScore' in changeValue) {
                values.docScore = 100 - (values.sampleScore || 0);
              }
              if ('docScore' in changeValue) {
                values.sampleScore = 100 - (values.docScore || 0);
              }
              formRef.current?.setFieldsValue(values);
            }}
          >
            <ProFormDigit
              {...layoutProps}
              name="sampleScore"
              label="样品得分"
              max={100}
              min={0}
              rules={[{ required: true, message: '请输入样品得分' }]}
            />
            <ProFormDigit
              {...layoutProps}
              name="docScore"
              max={100}
              min={0}
              label="资料得分"
              rules={[{ required: true, message: '请输入资料得分' }]}
            />
            <ProFormText
              {...layoutProps}
              disabled
              label="总分"
              name="scoreCount"
            />
            <div className="relative flex h-[80px] w-full py-5">
              <ProFormSlider
                name="slider"
                label="考核结论"
                marks={{
                  0: '0分',
                  100: '100分',
                }}
                range
                rules={[{ required: true, message: '考核结论必填' }]}
                fieldProps={{
                  range: { draggableTrack: true },
                  max: 100,
                  min: 0,
                  className: 'w-[75%] flex-1',

                  onChange: onSliderChange,
                  tooltip: {
                    formatter: (value) => <span>{value}分</span>,
                    placement: 'bottom',
                    open: true,
                    color: '#1677FF',
                    getPopupContainer: () =>
                      document.querySelector(
                        '.ant-form-item-control-input-content'
                      ) as HTMLElement,
                  },
                  styles: {
                    track: {
                      background: '#90C9FE',
                    },
                  },
                }}
              />
              <div className="text-[#FF3D00] w-[180px] absolute top-[25px] right-10">
                <div>优&nbsp;&nbsp;&nbsp;秀：{excellentScore} ≤ 得分</div>
                <div>
                  合&nbsp;&nbsp;&nbsp;格：{qualifiedScore} ≤ 得分 ＜
                  {excellentScore}
                </div>
                <div>不合格：&nbsp;0 ≤ 得分 ＜ {qualifiedScore}</div>
              </div>
            </div>
            {taskConfigurationForm.taskType === 'org' ? (
              <ProForm.Item
                label={<RequiredTag title="被考核机构" />}
                className="w-[90%] mt-6"
              >
                <div className="w-full min-h-[120px] rounded-[7px] border border-solid border-[#D9D9D9] p-3">
                  <div className="mb-3 flex justify-between">
                    <Space>
                      <Input
                        placeholder="搜索机构名称"
                        value={searchValue}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setSearchValue(e.target.value)
                        }
                        style={{ width: 200 }}
                        onPressEnter={handleSearchOrg}
                        prefix={<SearchOutlined />}
                      />
                    </Space>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={handleResetSearch}
                      >
                        重置
                      </Button>
                      <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        onClick={handleSearchOrg}
                      >
                        搜索
                      </Button>
                      <Button type="primary" onClick={openOrgSelectModal}>
                        选择
                      </Button>
                      <Button
                        danger
                        onClick={handleRemoveSelected}
                        disabled={!checkedOrgList.length}
                      >
                        移出
                      </Button>
                    </Space>
                  </div>
                  <ProForm.Item
                    name="orgVos"
                    rules={[
                      {
                        required: taskConfigurationForm.taskType === 'org',
                        message: '请选择被考核机构',
                      },
                    ]}
                  >
                    <Table
                      rowKey="deptId"
                      dataSource={
                        searchValue && filteredOrgList.length
                          ? filteredOrgList
                          : selectedOrgRows
                      }
                      columns={selectedTableColumns}
                      pagination={{
                        current: mainTablePagination.current,
                        pageSize: mainTablePagination.pageSize,
                        total:
                          searchValue && filteredOrgList.length
                            ? filteredOrgList.length
                            : selectedOrgRows.length,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        onChange: handleMainTablePaginationChange,
                        onShowSizeChange: (current, size) =>
                          handleMainTablePaginationChange(1, size),
                        showTotal: (total) => `共 ${total} 条`,
                      }}
                      rowSelection={{
                        selectedRowKeys: checkedOrgList,
                        onChange: (selectedRowKeys, selectedRows) => {
                          // 只更新选中项列表，不影响表单中保存的所有机构
                          setCheckedOrgList(selectedRowKeys);

                          // 统计数据仍然基于所有已添加的机构
                          updateStatsData(selectedOrgRowKeys, selectedOrgRows);
                        },
                        preserveSelectedRowKeys: true,
                        columnWidth: 60,
                      }}
                      expandable={{
                        expandedRowKeys: [],
                        showExpandColumn: false,
                      }}
                      size="small"
                    />
                  </ProForm.Item>
                  <div className="mt-3">
                    <div className="text-[#faad14]">
                      已选机构: 共 {selectedOrgRows.length} 家单位，包含市州级{' '}
                      {
                        selectedOrgRows.filter(
                          (item) => item.level === 2 || item.level === '2'
                        ).length
                      }{' '}
                      家， 区县院控{' '}
                      {
                        selectedOrgRows.filter(
                          (item) => item.level === 3 || item.level === '3'
                        ).length
                      }{' '}
                      家
                    </div>
                  </div>
                </div>
              </ProForm.Item>
            ) : null}

            <ProForm.Item
              label={<RequiredTag title="考核项目" />}
              className="w-[90%]"
            >
              <div className="w-full min-h-[10px] rounded-[7px] border border-solid border-[#D9D9D9] p-3">
                <div className="mb-3">
                  {!checkAllAssessment() ? (
                    <Button
                      key="btn-select-all-assessment"
                      type="primary"
                      onClick={() => onCheckAllChange('all')}
                    >
                      全选考核项目
                    </Button>
                  ) : (
                    <Button
                      key="btn-deselect-all-assessment"
                      onClick={() => onCheckAllChange()}
                    >
                      取消全选考核项目
                    </Button>
                  )}
                  {checkedList.length ? (
                    <span className="text-[red] ml-5">
                      已选{checkedList.length}个考核项目
                    </span>
                  ) : null}
                </div>
                <ProForm.Item
                  name="qaTaskItemVos"
                  rules={[{ required: true, message: '请选择考核项目' }]}
                >
                  <Checkbox.Group
                    className="flex flex-col"
                    value={checkedList}
                    onChange={onChangeCheckBox}
                  >
                    {assessmentCheckBox?.map((_item, idx) => {
                      const isItemChecked = checkedList.includes(_item.id);
                      return (
                        <div
                          className="h-[30px] flex items-center mb-2"
                          key={_item.id}
                        >
                          <Checkbox
                            key={_item.id}
                            value={_item.id}
                            className="min-w-[20%]"
                          >
                            {_item.name}
                          </Checkbox>
                          {isItemChecked && (
                            <InputNumber
                              min={0}
                              max={100}
                              precision={2}
                              key={`${_item.id}+input`}
                              className="w-[200px]"
                              placeholder="请输入项目得分比例"
                              value={inputValues[_item.id]}
                              onChange={(e: number | null) => {
                                // 更新当前 Checkbox 对应的 input 值
                                setInputValues({
                                  ...inputValues,
                                  [_item.id]: e || null,
                                });
                              }}
                              addonAfter="%"
                            />
                          )}
                        </div>
                      );
                    })}
                  </Checkbox.Group>
                </ProForm.Item>
                <div className="text-[red]">
                  注：请输入考核项目得分比例，比例之和必须为100.00%
                </div>
              </div>
            </ProForm.Item>
          </ProForm>
        </BlockContainer>
      </div>

      {/* 机构选择Modal */}
      <Modal
        title="选择考核机构"
        open={orgModalVisible}
        onCancel={closeOrgSelectModal}
        width={1000}
        footer={[
          <Button key="cancel" onClick={closeOrgSelectModal}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={confirmOrgSelection}>
            确定
          </Button>,
        ]}
      >
        <div className="mb-3 flex justify-between">
          <Space size="middle">
            <div>
              <span>机构名称：</span>
              <Input
                placeholder="搜索机构名称"
                value={modalNameSearch}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setModalNameSearch(e.target.value)
                }
                style={{ width: 200 }}
                onPressEnter={handleModalSearch}
              />
            </div>
            <div>
              <span>机构类型：</span>
              <Select
                placeholder="选择机构类型"
                value={modalTypeSearch}
                onChange={(value) => setModalTypeSearch(value)}
                style={{ width: 200 }}
                mode="multiple"
                allowClear
                options={[
                  { value: '1', label: '省级' },
                  { value: '2', label: '市（州）级' },
                  { value: '3', label: '区县级' },
                  { value: '4', label: '医疗机构' },
                ]}
              />
            </div>
          </Space>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleModalResetSearch}>
              重置
            </Button>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleModalSearch}
            >
              搜索
            </Button>
            <span className="text-[red] ml-3">
              已选{selectedOrgRowKeys.length}家机构
            </span>
          </Space>
        </div>
        <Table
          rowKey="deptId"
          loading={orgTableLoading}
          dataSource={orgTableData}
          columns={orgColumns}
          pagination={false}
          rowSelection={{
            selectedRowKeys: selectedOrgRowKeys,
            onChange: handleOrgRowSelectionChange,
            preserveSelectedRowKeys: true,
            columnWidth: 60,
          }}
          expandable={{ expandedRowKeys: [], showExpandColumn: false }}
          size="small"
        />
        <div className="mt-3 flex justify-end">
          <Pagination
            current={orgPagination.current}
            pageSize={orgPagination.pageSize}
            total={orgPagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `共 ${total} 条`}
            onChange={handleOrgPaginationChange}
            onShowSizeChange={(current, size) => {
              handleOrgPaginationChange(1, size);
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default TaskConfiguration;
