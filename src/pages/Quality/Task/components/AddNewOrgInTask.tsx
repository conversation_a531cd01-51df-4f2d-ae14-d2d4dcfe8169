/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * 在当前任务中新增考核单位
 */
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Form, FormInstance, message, Select } from 'antd';
import { EditableProTable } from '@ant-design/pro-components';

interface IAddNewOrgInTaskProps {
  taskId: string;
  orgList: Record<string, any>[];
  qaTaskSampleVos: Record<string, any>[];
  qaTaskItemVos: Record<string, any>[];
  taskType?: string;
}

export interface IAddNewOrgInTaskRef {
  getFormData: () => {
    orgId: string;
    samples: Array<{
      id: string | number;
      sampleCode: string;
      name?: string;
      value?: string;
    }>;
  } | null;
  reset: () => void;
}

const AddNewOrgInTask = forwardRef<IAddNewOrgInTaskRef, IAddNewOrgInTaskProps>(
  ({ taskId, orgList, qaTaskSampleVos, qaTaskItemVos }, ref) => {
    const addNewOrgInTaskFormRef = useRef<FormInstance>(null);
    // 添加编辑行的状态管理
    const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
    // 添加数据状态
    const [dataSource, setDataSource] =
      useState<Record<string, any>[]>(qaTaskSampleVos);

    const [messageApi, contextHolder] = message.useMessage();

    // 初始化所有行为可编辑状态
    useEffect(() => {
      if (qaTaskSampleVos && qaTaskSampleVos.length > 0) {
        // 拷贝数据，确保不会直接修改props
        const initialDataSource = qaTaskSampleVos.map((item) => ({
          ...item,
          id: item.id || Math.random().toString(36).substr(2, 9),
          sampleCode: '', // 初始化时就确保样本编号为空
        }));
        setDataSource(initialDataSource);

        // 设置所有行为可编辑状态
        const keys = initialDataSource.map((item) => item.id);
        setEditableRowKeys(keys);
      }
    }, [qaTaskSampleVos]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      getFormData: () => {
        if (!addNewOrgInTaskFormRef.current) return null;

        const formValues = addNewOrgInTaskFormRef.current.getFieldsValue();
        if (!formValues.orgName) {
          messageApi.error('请选择被考核单位');
          return null;
        }

        // 获取样本编号数据
        const samples = dataSource
          .filter((item) => item.sampleCode && item.sampleCode.trim() !== '')
          .map((item) => ({
            id: item.id,
            sampleCode: item.sampleCode,
            name: item.name,
            value: item.value,
          }));

        if (samples.length === 0) {
          messageApi.error('请至少填写一个样本编号');
          return null;
        }

        return {
          orgId: formValues.orgName,
          samples,
        };
      },
      // 重置方法：清空表单和样本编号数据
      reset: () => {
        // 重置表单
        addNewOrgInTaskFormRef.current?.resetFields();

        // 强制重置数据源中的样本编号
        if (qaTaskSampleVos && qaTaskSampleVos.length > 0) {
          const resetDataSource = qaTaskSampleVos.map((item) => ({
            ...item,
            id: item.id || Math.random().toString(36).substr(2, 9),
            sampleCode: '', // 清空样本编号
          }));
          setDataSource(resetDataSource);

          // 确保所有行处于可编辑状态
          const keys = resetDataSource.map((item) => item.id);
          setEditableRowKeys(keys);
        }
      },
    }));

    return (
      <>
        {contextHolder}
        <Form
          ref={addNewOrgInTaskFormRef}
          name="addNewOrgInTaskForm"
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
          autoComplete="off"
          className="!mt-6"
          onValuesChange={(changedValues: Record<string, any>) => {}}
        >
          <Form.Item
            label="被考核单位名称"
            name="orgName"
            className=""
            rules={[{ required: true, message: '请选择被考核单位名称' }]}
          >
            <Select
              showSearch
              optionFilterProp="label"
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              style={{ width: '240px' }}
              options={orgList.map((item) => ({
                label: item.deptName,
                value: item.deptId,
              }))}
            />
          </Form.Item>
          <Form.Item label="考核项目" name="item">
            {qaTaskItemVos?.map((item, idx) => (
              <span key={item.id} className="mr-1">
                {item.name} {item.ratio}%
                {idx === qaTaskItemVos.length - 1 ? '' : ','}
              </span>
            ))}
          </Form.Item>
        </Form>
        <EditableProTable
          rowKey="id"
          columns={[
            {
              title: '序号',
              dataIndex: 'index',
              valueType: 'indexBorder',
              width: 60,
            },
            {
              title: '标准品类别',
              dataIndex: 'name',
              readonly: true,
            },
            {
              title: '参考结果',
              dataIndex: 'value',
              readonly: true,
            },
            {
              title: '样本编号',
              dataIndex: 'sampleCode',
              width: 240,
              formItemProps: {
                rules: [{ required: true, message: '请输入样本编号' }],
              },
              fieldProps: (form, { rowKey, rowIndex, entity }) => {
                return {
                  onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
                    const newValue = e.target.value;

                    // 直接更新 dataSource
                    const newDataSource = [...dataSource];
                    const index = newDataSource.findIndex(
                      (item) => item.id === rowKey
                    );
                    if (index > -1) {
                      newDataSource[index] = {
                        ...newDataSource[index],
                        sampleCode: newValue,
                      };
                      setDataSource(newDataSource);
                    }
                  },
                };
              },
            },
          ]}
          recordCreatorProps={false}
          value={dataSource}
          onChange={(value) => {
            setDataSource([...value]);
          }}
          className="mb-2"
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
            onValuesChange: (record, recordList) => {
              setDataSource(recordList);
            },
            onSave: async (rowKey, data, row) => {
              // 更新dataSource中的样本编号
              const newDataSource = [...dataSource];
              const index = newDataSource.findIndex(
                (item) => item.id === rowKey
              );
              if (index > -1) {
                newDataSource[index] = { ...newDataSource[index], ...data };
                setDataSource(newDataSource);
              }
              messageApi.success('编辑成功');
              return true;
            },
            onCancel: (rowKey) => {
              messageApi.info('取消编辑');
              return Promise.resolve();
            },
          }}
        />
        <div className="text-red-500">
          <p>注意事项: </p>
          <p>
            1、市县疾控考核、盲样考核、双盲考核三种任务类型,
            每种类别的标准品都需要填写样本编号;
          </p>
          <p>2、随机考核这种任务类型, 至少需要填写一个样本编号;</p>
        </div>
      </>
    );
  }
);

export default AddNewOrgInTask;
