/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Col,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import {
  getQATask,
  queryOrgList,
  randomForRandomTask,
  randomlySampleNum,
  randomlySampleNumV2,
  randomSampleCode,
} from '@/api/quality';
import { blindlyExporExpress } from '@/api/quality';
//标准规则维护
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusCircleOutlined } from '@ant-design/icons';
import { DownloadOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { log } from 'console';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import download from '@/utils/download';
import { FormInitVal, formItemLayout } from '../../data';
import AssessedLabModal from '../AssessedLabModal';
import AssessedLabModalMultiple from '../AssessedLabModalMultiple';
import { TaskContext } from '../Edit';
//任务配置模块
import StandardMaintenance from '../Model/StandardMaintenanceModel';
import TaskConfiguration from '../TaskConfiguration';

// 样本列表初始数据
let tableData = [{ name: '第1种样本', value: '', qaTaskSampleItemVoList: [] }];

type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit?: (params: any) => void;
  onSave?: () => Promise<void>;
  onRef?: any;
  baseInfo?: any; // 表单数据
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isUpdateJudgCriteria?: boolean;
};
type SampleData = {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  qaTaskSampleItemVoList: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
};

interface DataType {
  key: string;
  itemName: string;
  conditionThree: string;
}
type RefObjectMap = {
  [key: string]: ProFormInstance<any> | null;
};

// 定义后缀数字位数
const suffixNumList = [
  { label: '2位', value: 2 },
  { label: '3位', value: 3 },
  { label: '4位', value: 4 },
  { label: '5位', value: 5 },
  { label: '6位', value: 6 },
  { label: '7位', value: 7 },
  { label: '8位', value: 8 },
  { label: '9位', value: 9 },
  { label: '10位', value: 10 },
];

// Add type for sampleVos items
interface SampleVosItem {
  assessedLabId: string;
  assessedLabCode: string;
  assessedLabName: string;
  sampleBox: string;
  sampleCode?: string;
  [key: string]: any; // 添加索引签名
}

const EditBindSample: React.FC<TEditProps> = ({
  id,
  onRef,
  baseInfo,
  onSave,
  isUpdateJudgCriteria,
}) => {
  const { setRefType } = useContext(TaskContext);
  const [messageApi, contextHolder] = message.useMessage();
  // 表单实例2
  // const formRef2 = useRef<FormInstance>(null);
  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any[]>([]);
  // 样本ref集合
  const listRefs = useRef<RefObjectMap>({});
  // 考核项目-已选项目
  const [assessmentCheckBox, setAssessmentCheckBox] = useState<any[]>([]);
  // 当前点击样本
  const [sampleDetails, setSampleDetails] = useState<any>({});
  // 随机生成的编号表格数据
  const [sampleNumList, setSampleNumList] = useState<any[]>([]);
  // 随机样本编号弹窗
  const [randomlySampleNumModel, setRandomlySampleNumModel] =
    useState<boolean>(false);
  // 编号表头
  const [sampleNumHeader, setSampleNumHeader] = useState<any[]>([]);
  const [randomlySampleLoading, setRandomlySampleLoading] =
    useState<boolean>(false);
  // 标准判定维护弹窗
  const [standardModel, setStandardModel] = useState<boolean>(false);
  const formRefTask = useRef<any>(null);
  const formRef = useRef<ProFormInstance>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loading2, setLoading2] = useState<boolean>(false);

  const [radioGroupValue, setRadioGroupValue] = useState<any>('uniform');

  // 当前选择的判定标准的类型，正确、可疑或错误
  const [curSelectedStandardType, setCurSelectedStandardType] = useState<
    'correct' | 'suspicious' | 'error'
  >('correct');

  // 考核样编号生成 Modal 是否打开
  const [isOpenGenerateSampleNumModal, setIsOpenGenerateSampleNumModal] =
    useState<boolean>(false);

  const { token } = useTokenStore();

  // 添加状态管理考核单位选择
  const [assessedLabModalVisible, setAssessedLabModalVisible] = useState(false);

  const [assessedLabModalMultipleVisible, setAssessedLabModalMultipleVisible] =
    useState(false);

  const [currentRow, setCurrentRow] = useState<any>(null);

  const [currentMultipleRow, setCurrentMultipleRow] = useState<any>(null);

  const [assessedLabList, setAssessedLabList] = useState<any[]>([]);
  // 修改为单选模式
  const [selectedLabId, setSelectedLabId] = useState<string>('');
  // 添加搜索关键词状态
  const [searchKeyword, setSearchKeyword] = useState('');
  // 添加过滤后的单位列表状态
  const [filteredLabList, setFilteredLabList] = useState<any[]>([]);

  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  const [sampleModalVisible, setSampleModalVisible] = useState<boolean>(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [currentSampleIdentifier, setCurrentSampleIdentifier] =
    useState<string>(''); // 追踪当前操作的样本

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '考核项目',
      dataIndex: 'itemName',
      key: 'itemName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
      hideInSearch: true,
      render: (text, record) => {
        // 检查是否所有标准字段都为空
        const hasCorrectStandard =
          record?.condition || record?.conditionTwo || record?.conditionThree;
        const hasSuspiciousStandard =
          record?.conditionFour ||
          record?.conditionFive ||
          record?.conditionSix;
        const hasErrorStandard =
          record?.conditionSeven ||
          record?.conditionEight ||
          record?.conditionNine;

        // 如果所有标准都为空，则显示"维护判定标准"按钮
        if (
          !hasCorrectStandard &&
          !hasSuspiciousStandard &&
          !hasErrorStandard
        ) {
          return (
            <a
              onClick={() => handleSampleRules('correct', record)}
              className="text-primary"
            >
              维护判定标准
            </a>
          );
        }

        // 否则，显示现有的三种标准
        return (
          <div className="w-full flex flex-col">
            <a onClick={() => handleSampleRules('correct', record)}>
              正确(合格)标准:{' '}
              {record?.conditionThree ? record?.conditionThree : '未维护'}
            </a>
            <a onClick={() => handleSampleRules('suspicious', record)}>
              可疑标准: {record?.conditionSix ? record?.conditionSix : '未维护'}
            </a>
            <a onClick={() => handleSampleRules('error', record)}>
              错误(不合格)标准:{' '}
              {record?.conditionNine ? record?.conditionNine : '未维护'}
            </a>
          </div>
        );
      },
    },
  ];

  let columns2: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      key: 'index',
      width: 100,
    },
    {
      title: '被考核单位',
      dataIndex: 'assessedLabName',
      key: 'assessedLabName',
      hideInSearch: true,
      render: (text, record, index) => (
        <Button type="link" onClick={() => openAssessedLabModal(record)}>
          {record.assessedLabId ? record.assessedLabName : '请选择被考核单位'}
        </Button>
      ),
    },
    ...sampleNumHeader,
  ];

  // 新增状态管理每个样本的编号配置
  const [numberingConfigs, setNumberingConfigs] = useState<any>({});

  // 随机编号生成的数量
  const [randomNumber, setRandomNumber] = useState<number | null>(null);

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleParams,
      handleValidateFields,
    };
  });

  /**
   * @TODO 添加一个样本
   */
  const handleAddSampleItem = async () => {
    const newItemSample = {
      name: `第${sampleListData.length + 1}种样本`,
      value: '',
      // 取第一个样本的值
      qaTaskSampleItemVoList:
        sampleListData && sampleListData![0]?.qaTaskSampleItemVoList?.length
          ? sampleListData![0]?.qaTaskSampleItemVoList.map((_item: any) => {
              const Item = {
                code: `第${sampleListData.length + 1}种样本`,
                itemName: _item.itemName ?? _item.assessmentName,
                condition: '',
                conditionTwo: '',
                conditionThree: '',
              };
              return Item;
            })
          : [],
    };
    setSampleListData([...sampleListData, newItemSample]);
  };

  /**
   * @TODO 设置样本规则
   */
  const handleSampleRules = (
    type: 'correct' | 'suspicious' | 'error',
    obj: any
  ) => {
    // 设置当前样本名称
    const currentSampleName = obj.sampleName || obj.code;
    // 保存当前样本标识符
    setCurrentSampleIdentifier(currentSampleName);
    // 设置当前选中的标准类型
    setCurSelectedStandardType(type);
    // 保存样本详情，确保包含sampleName
    setSampleDetails({
      ...obj,
      sampleName: currentSampleName,
    });
    setStandardModel(true);
  };

  /**
   * @TODO 删除一个样本
   * @parms code 样本编号
   */
  const handleDeleteSampleItem = async (code: string) => {
    const delSampleList =
      sampleListData.filter((_item) => _item.name !== code) ?? [];
    // 重新生成样本编号
    delSampleList.forEach((_item, idx) => {
      _item.name = `第${idx + 1}种样本`;
    });

    // 重新排序样本编号
    const reorderedSampleNumList = sampleNumList.map((item) => {
      // 创建新对象,保留基础字段
      const newItem: any = {
        assessedLabCode: item.assessedLabCode,
        assessedLabId: item.assessedLabId,
        assessedLabName: item.assessedLabName,
        sampleBox: item.sampleBox,
        _key: item._key,
      };

      // 获取所有"第n种样本"的键
      const sampleKeys = Object.keys(item)
        .filter((key) => /^第\d+种样本$/.test(key))
        .filter((keyName) => keyName !== code);
      // 按照数字大小排序
      sampleKeys.sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

      // 重新按顺序添加样本值
      sampleKeys.forEach((key, index) => {
        newItem[`第${index + 1}种样本`] = item[key];
      });

      return newItem;
    });

    setSampleNumList(reorderedSampleNumList);

    setSampleListData(delSampleList);
  };

  /**
   * @TODO 导出样本清单
   */
  const handleExport = async () => {
    try {
      // 先保存
      onSave && (await onSave());
      setLoading2(true);
      const params = {
        taskId: baseInfo?.id,
      };
      const data = await blindlyExporExpress(params);
      await download(data, data.fileName || '样本清单.xlsx'); //导出方法，传入后端返回的文件流
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading2(false);
    }
  };

  const handleOpenAssessedLabModal = async () => {
    setAssessedLabModalMultipleVisible(true);
  };

  /**
   * @TODO 随机生成样本编号
   */
  const handleRandomlySampleNum = async () => {
    const boxNum = formRef.current?.getFieldValue('boxNum');
    try {
      // 处理数据
      setLoading(true);
      const params = {
        boxNum,
        sampleNum: sampleListData.length,
      };
      const { code, data, msg }: any = await randomlySampleNum(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        const Item = data?.map((_item: any, idx: number) => {
          const obj: any = {};
          _item?.sampleCodes.forEach((_item2: any, idx2: number) => {
            obj[`第${idx2 + 1}种样本`] = _item2;
          });
          return {
            sampleBox: _item.sampleBox,
            ...obj,
          };
        });
        setSampleNumList(Item);
        setRandomlySampleNumModel(false);
        formRef.current?.resetFields();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 生成规则
   */
  const queryRulesObj = (rules: any) => {
    // 创建新的数据副本，避免直接修改状态
    const updatedSampleListData = [...sampleListData];

    // 只更新匹配当前设置的样本标识的样本
    const sampleIndex = updatedSampleListData.findIndex(
      (item) => item.name === currentSampleIdentifier
    );

    if (sampleIndex !== -1) {
      // 只更新找到的特定样本
      const targetSample = updatedSampleListData[sampleIndex];

      // 更新该样本中匹配的考核项目
      targetSample.qaTaskSampleItemVoList =
        targetSample.qaTaskSampleItemVoList.map((item: any) => {
          if (item.itemName === sampleDetails.itemName) {
            // 确保rules.rulesStorage已经是字符串
            const rulesStorage =
              typeof rules.rulesStorage === 'string'
                ? rules.rulesStorage
                : JSON.stringify(rules.rulesStorage);

            if (curSelectedStandardType === 'correct') {
              return {
                ...item,
                condition: rules.rules,
                conditionTwo: rulesStorage,
                conditionThree: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'suspicious') {
              return {
                ...item,
                conditionFour: rules.rules,
                conditionFive: rulesStorage,
                conditionSix: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'error') {
              return {
                ...item,
                conditionSeven: rules.rules,
                conditionEight: rulesStorage,
                conditionNine: rules.rulesView,
              };
            }
          }
          return item;
        });
    }

    // 更新状态
    setSampleListData(updatedSampleListData);
  };

  /**
   * @TODO 获取配置任务参数
   */
  const handleParams = async () => {
    try {
      const form2 = await formRefTask.current?.handleFormParams({
        requireSampleCodes: true, // 要求样品编码格式校验
      });

      // 创建被考核样本数据的深度拷贝，避免修改原始状态
      const sampleListDataNew = cloneDeep(sampleListData);

      // 遍历每种样本类型
      sampleListDataNew.forEach((sample) => {
        // 重置样本实例列表，将从表格数据中重建
        const sampleInstances: any[] = [];

        // 遍历表格数据，将每行的样本编号提取出来
        sampleNumList.forEach((row) => {
          // 只检查该行是否有这种样本的编号，不再强制要求assessedLabId
          if (row[sample.name]) {
            // 创建包含完整字段的样本实例
            sampleInstances.push({
              assessedLabId: row.assessedLabId || null,
              assessedLabCode: row.assessedLabCode || null,
              assessedLabName: row.assessedLabName || null,
              // 确保sampleBox是数字ID格式 - 如果不是，则生成一个
              sampleBox:
                row.sampleBox && /^\d+$/.test(row.sampleBox)
                  ? row.sampleBox
                  : Date.now().toString() +
                    Math.floor(Math.random() * 1000).toString(),
              sampleCode: row[sample.name] || '',
            });
          }
        });

        // 设置样本的sampleVos
        sample.sampleVos = sampleInstances;
      });

      // 校验所有样品是否绑定样品编码
      const hasMissingSampleCode = sampleListDataNew.some((sample) =>
        sample.sampleVos.some((vo: any) => !vo.sampleCode)
      );

      if (hasMissingSampleCode) {
        throw new Error('有样本还未分配样本编号，请重新生成样本编号');
      }

      // 处理数据
      const params = {
        ...form2,
        qaTaskSampleVos: sampleListDataNew,
      };

      console.log('提交参数:', params);
      return params;
    } catch (error) {
      // message.warning("请完善明细配置")
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 任务参数-校验
   */
  const handleValidateFields = async () => {
    try {
      await formRefTask.current?.handleValidateFields();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce((promises, item) => {
        const formRef = listRefs.current[item.name];
        if (formRef) {
          promises.push(async () => {
            await formRef?.validateFields();
            return formRef?.getFieldValue('value') ?? '';
          });
        }
        return promises;
      }, [] as Array<() => Promise<any>>);
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }
      if (!sampleNumList.length) {
        throw new Error('还未随机生成样本编号');
      }
      // 随机编号临时数据
      let sampleNumListTemp: any = [];
      sampleListData.forEach((item) => {
        sampleNumListTemp = [];
        sampleNumList.forEach((item2) => {
          sampleNumListTemp.push({
            sampleBox: item2.sampleBox,
            sampleCode: item2[item.name],
          });
        });
        item.sampleVos = sampleNumListTemp;
      });
      // 校验所有样品是否绑定样品编码
      const isEnty = sampleNumListTemp.some((item: any) => !item.sampleCode);
      if (isEnty) {
        throw new Error('有样本还未分配样本编号，请重新生成样本编号');
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      if (baseInfo?.id) {
        const { code, data, msg } = await getQATask(baseInfo?.id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          // ========================================
          const qaTaskSampleVosNew = cloneDeep(data?.qaTaskSampleVos);
          let newSampleListData: any = [];
          if (qaTaskSampleVosNew) {
            qaTaskSampleVosNew.forEach((item: any) => {
              item.qaTaskSampleItemVoList.forEach((item2: any) => {
                item2.code = item.name;
              });

              // 确保sampleVos已经初始化
              if (!item.sampleVos) {
                item.sampleVos = [];
              }
            });
            newSampleListData = qaTaskSampleVosNew;
          } else {
            newSampleListData = tableData;
          }
          setSampleListData([...newSampleListData]);
          // ========================================

          // ========================================
          const { qaTaskSampleVos } = data;
          if (qaTaskSampleVos?.length) {
            // 使用数组来存储结果
            const resultArr: any[] = [];

            // 创建一个映射表，通过sampleBox分组
            const sampleBoxGroups: Record<string, any[]> = {};

            // 遍历所有样本及其被考核单位
            qaTaskSampleVos.forEach((sample: any) => {
              if (sample.sampleVos && sample.sampleVos.length) {
                sample.sampleVos.forEach((sampleVo: any) => {
                  // 使用sampleBox作为组ID
                  const sampleBoxId =
                    sampleVo.sampleBox && /^\d+$/.test(sampleVo.sampleBox)
                      ? sampleVo.sampleBox
                      : Date.now().toString() +
                        Math.floor(Math.random() * 1000).toString();

                  // 如果这个组ID还没有记录，创建一个新记录
                  if (!sampleBoxGroups[sampleBoxId]) {
                    sampleBoxGroups[sampleBoxId] = [
                      {
                        sampleType: sample.name,
                        sampleCode: sampleVo.sampleCode,
                        assessedLabId: sampleVo.assessedLabId,
                        assessedLabCode: sampleVo.assessedLabCode,
                        assessedLabName: sampleVo.assessedLabName,
                      },
                    ];
                  } else {
                    // 添加到已有的组
                    sampleBoxGroups[sampleBoxId].push({
                      sampleType: sample.name,
                      sampleCode: sampleVo.sampleCode,
                      assessedLabId: sampleVo.assessedLabId,
                      assessedLabCode: sampleVo.assessedLabCode,
                      assessedLabName: sampleVo.assessedLabName,
                    });
                  }
                });
              }
            });

            // 将分组数据转换为表格行
            Object.entries(sampleBoxGroups).forEach(
              ([sampleBoxId, groupItems]) => {
                // 创建基本行结构，使用第一个项目的被考核单位信息
                const newRow: any = {
                  assessedLabId: groupItems[0].assessedLabId,
                  assessedLabCode: groupItems[0].assessedLabCode,
                  assessedLabName: groupItems[0].assessedLabName || null,
                  sampleBox: sampleBoxId,
                  _key: `${sampleBoxId}_${Date.now()}_${Math.random()
                    .toString(36)
                    .slice(2)}`,
                };

                // 添加每种样本的编号
                groupItems.forEach((item) => {
                  newRow[item.sampleType] = item.sampleCode;
                });

                resultArr.push(newRow);
              }
            );

            // 设置表格数据
            setSampleNumList(resultArr);
            // 设置样本数据
            setSampleListData(qaTaskSampleVos);
          }
          // ========================================
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  /**
   * @TODO 处理配置变化
   * @param sampleIndex 样本索引
   * @param field 字段
   * @param value 值
   */
  const handleConfigChange = (
    sampleIndex: number,
    field: string,
    value: any
  ) => {
    setNumberingConfigs((prev: any) => {
      // 创建一个新的配置对象
      const newConfig = { ...prev };

      // 确保sampleIndex对应的配置对象存在
      if (!newConfig[sampleIndex]) {
        newConfig[sampleIndex] = {};
      }

      // 更新字段值
      newConfig[sampleIndex] = {
        ...newConfig[sampleIndex],
        [field]: value,
      };

      // 当类型改变时，确保相关字段有默认值
      if (
        radioGroupValue === 'uniform' &&
        !newConfig[sampleIndex].uniformCode
      ) {
        newConfig[sampleIndex].uniformCode = '';
      } else if (radioGroupValue === 'random') {
        newConfig[sampleIndex].prefix = newConfig[sampleIndex].prefix || '';
        newConfig[sampleIndex].suffixDigits =
          newConfig[sampleIndex].suffixDigits || 3;
      } else if (radioGroupValue === 'customize') {
        console.log(newConfig[sampleIndex]);

        newConfig[sampleIndex].prefix = newConfig[sampleIndex].prefix || '';
        newConfig[sampleIndex].digit = newConfig[sampleIndex].digit || '';
        newConfig[sampleIndex].incrementCodeLengthType =
          newConfig[sampleIndex].incrementCodeLengthType || '0';
        newConfig[sampleIndex].incrementLength =
          newConfig[sampleIndex].incrementLength || '';
        console.log(newConfig[sampleIndex], 77777);
      }
      console.log(newConfig, newConfig);

      return newConfig;
    });
  };
  const handleConfigChangeAll = (field: string, value: any) => {
    setRadioGroupValue(value);
  };

  /**
   * @TODO 随机生成样本编号-V2
   * 对第一版 handleRandomlySampleNum 的升级
   * 支持不同样本编号规则
   */
  const handleRandomlySampleNumV2 = async () => {
    try {
      setRandomlySampleLoading(true);

      // 检查是否所有配置都是统一编号
      const IsUniform = radioGroupValue === 'uniform';

      // 如果所有的编号都是统一编号
      if (IsUniform) {
        // 检查 numberingConfigs 是否为空对象
        const isNumberingConfigsEmpty =
          Object.keys(numberingConfigs).length === 0;
        if (isNumberingConfigsEmpty) {
          message.warning('请填写统一编号');
          setRandomlySampleLoading(false);
          return;
        }

        if (Object.keys(numberingConfigs).length < sampleListData.length) {
          message.warning('请为所有样本设置统一编号');
          setRandomlySampleLoading(false);
          return;
        }
        // 检查是否每个样本都有设置统一编号
        const allHaveUniformCode = Object.keys(numberingConfigs).every(
          (key) => !!numberingConfigs[Number(key)]?.uniformCode
        );

        if (!allHaveUniformCode) {
          message.warning('请为所有样本设置统一编号');
          setRandomlySampleLoading(false);
          return;
        }

        // 处理全部为统一编号的情况
        const _sampleListData = cloneDeep(sampleListData);

        // 获取需要生成的被考核单位数量
        const numOfUnits = randomNumber || 0;

        if (numOfUnits <= 0) {
          message.warning('请设置被考核单位数量');
          setRandomlySampleLoading(false);
          return;
        }

        // 为每个样本创建或更新 sampleVos 数组
        _sampleListData.forEach((item, idx) => {
          const config: any = numberingConfigs[idx];
          console.log(numberingConfigs[idx], 11111, numberingConfigs);

          config.incrementCodeLengthType = '0';
          // 为每个被考核单位创建唯一的样本盒ID
          /*const sampleBoxIds = new Array(numOfUnits).fill(null).map(
            () =>
              // 生成数字ID格式的样本盒ID (时间戳 + 随机数)
              Date.now().toString() +
              Math.floor(Math.random() * 1000).toString()
          );*/

          // 为每个被考核单位创建唯一的样本盒ID
          const rawSampleBoxIds = new Set<string>();

          while (rawSampleBoxIds.size < numOfUnits) {
            const id =
              Date.now().toString() +
              Math.floor(Math.random() * 1000000)
                .toString()
                .padStart(6, '0'); // 加大随机范围
            rawSampleBoxIds.add(id);
          }

          const sampleBoxIds = Array.from(rawSampleBoxIds);

          // 重置样本的sampleVos数组，完全替换旧数据
          item.sampleVos = new Array(numOfUnits).fill(null).map((_, index) => ({
            assessedLabId: null,
            assessedLabCode: null,
            assessedLabName: null,
            sampleBox: sampleBoxIds[index],
            sampleCode: config.uniformCode,
          }));
        });

        // 生成表格数据
        const resultArr: any[] = [];

        // 对于每个被考核单位，创建一行表格数据
        for (let unitIndex = 0; unitIndex < numOfUnits; unitIndex++) {
          const rowData: any = {
            assessedLabId: null,
            assessedLabCode: null,
            assessedLabName: null,
            _key: `unit_${unitIndex}_${Date.now()}_${Math.random()
              .toString(36)
              .slice(2)}`,
          };

          // 为每个样本类型添加对应的编号
          _sampleListData.forEach((sample, sampleIndex) => {
            // 确保该单位在该样本中有对应的样本项
            if (sample.sampleVos && sample.sampleVos[unitIndex]) {
              rowData[sample.name] = sample.sampleVos[unitIndex].sampleCode;
              rowData.sampleBox = sample.sampleVos[unitIndex].sampleBox;
            }
          });

          resultArr.push(rowData);
        }

        console.log('生成的表格数据:', resultArr);

        setSampleListData(_sampleListData);
        setSampleNumList(resultArr); // 更新表格数据源
        setIsOpenGenerateSampleNumModal(false);
        setRandomlySampleLoading(false);

        // 清除表单数据
        setRandomNumber(null);
        setNumberingConfigs({});

        message.success('统一编号生成成功');
        return;
      }
      const IsRandom = radioGroupValue === 'random';

      // 处理随机编号的情况
      if (IsRandom) {
        // 检查被考核单位数量是否设置
        if (!randomNumber || randomNumber <= 0) {
          message.warning('请设置被考核单位数量');
          setRandomlySampleLoading(false);
          return;
        }

        // 构建新的请求参数结构，符合后端API要求
        const ranSampleCode = Object.keys(numberingConfigs)
          .map((indexStr) => {
            const index = Number(indexStr);
            const config = numberingConfigs[index];
            const sample = sampleListData[index];

            return {
              prefix: config.prefix || null,
              digit: config.suffixDigits || 0,
              type: config.type,
              label: sample.name,
              referenceResult: sample.value || '',
            };
          })
          .filter(Boolean);

        if (ranSampleCode.length === 0) {
          message.warning('请至少设置一个随机编号样本');
          setRandomlySampleLoading(false);
          return;
        }

        // 调用API获取随机编号，使用正确的参数结构
        const { code, data, msg } = await randomlySampleNumV2({
          num: randomNumber,
          contents: ranSampleCode,
          taskId: baseInfo?.id,
          type: 'random',
        });

        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg || '生成编号失败');
          setRandomlySampleLoading(false);
          return;
        }

        if (!data || !Array.isArray(data) || data.length === 0) {
          message.error('返回的数据格式不正确');
          setRandomlySampleLoading(false);
          return;
        }

        console.log('API返回数据:', data, sampleNumHeader);

        // 创建临时数据结构，用于储存样本列表
        const _sampleListData = cloneDeep(sampleListData);
        console.log('处理前的样本数据:', _sampleListData);

        // 清空所有样本的sampleVos
        _sampleListData.forEach((sample) => {
          sample.sampleVos = [];
        });

        // 处理新格式的数据: [{sampleBox: "xxx", samples: ["YUU987", "QWE981"]}]
        if (data && Array.isArray(data)) {
          // 遍历每个样本盒

          data.forEach((boxData: any, boxIndex: number) => {
            const sampleBox = boxData.sampleBox;
            const samples = boxData.samples;

            // 检查 samples 是否为对象
            if (typeof samples === 'object' && samples !== null) {
              // 遍历对象的键值对
              Object.entries(samples).forEach(([sampleType, sampleCode]) => {
                const sampleIndex = _sampleListData.findIndex(
                  (sample) => sample.name === sampleType
                );
                if (sampleIndex !== -1) {
                  _sampleListData[sampleIndex].sampleVos.push({
                    assessedLabId: null,
                    assessedLabCode: null,
                    assessedLabName: null,
                    sampleBox: sampleBox,
                    sampleCode: sampleCode as string,
                  });
                }
              });
            }
          });
        }

        // 为表格生成数据
        const labs: any[] = [];

        // 计算实际创建的行数，基于API返回的数据
        const actualRows = Math.max(
          1, // 至少有一行
          ..._sampleListData.map((sample) => sample.sampleVos.length)
        );

        // 为每个被考核单位创建一行数据
        for (let i = 0; i < data.length; i++) {
          const row: any = {
            assessedLabId: null,
            assessedLabCode: null,
            assessedLabName: null,
            _key: `unit_${i}_${Date.now()}_${Math.random()
              .toString(36)
              .slice(2)}`,
            sampleBox: null,
            ...data[i].samples,
          };

          // 为每个样本类型添加样本编号
          _sampleListData.forEach((sample, sampleIndex) => {
            if (sample.sampleVos && i < sample.sampleVos.length) {
              row[sample.name] = sample.sampleVos[i].sampleCode;
              // 只用第一个样本的sampleBox作为行的sampleBox
              if (sampleIndex === 0) {
                row.sampleBox = sample.sampleVos[i].sampleBox;
              }
            }
          });

          labs.push(row);
        }

        console.log('处理后的表格数据:', labs);

        // 更新状态
        setSampleListData(_sampleListData);
        setSampleNumList(labs);
        setIsOpenGenerateSampleNumModal(false);
        setRandomlySampleLoading(false);

        // 清除表单数据
        setRandomNumber(null);
        setNumberingConfigs({});

        message.success('样本编号生成成功');
      }

      //自增编号

      const IsCustomize = radioGroupValue === 'customize';

      // 处理随机编号的情况
      if (IsCustomize) {
        // 检查被考核单位数量是否设置
        if (!randomNumber || randomNumber <= 0) {
          message.warning('请设置被考核单位数量');
          setRandomlySampleLoading(false);
          return;
        }
        const isNumberingConfigsEmpty =
          Object.keys(numberingConfigs).length === 0;
        if (isNumberingConfigsEmpty) {
          message.warning('请完善信息');
          setRandomlySampleLoading(false);
          return;
        }

        if (Object.keys(numberingConfigs).length < sampleListData.length) {
          message.warning('请为所有样本设置起始编号');
          setRandomlySampleLoading(false);
          return;
        }

        // 检查是否每个样本都有设置统一编号
        const allHaveUniformCode = Object.keys(numberingConfigs).every(
          (key) => {
            const val: any =
              numberingConfigs[Number(key)]?.digit &&
              numberingConfigs[Number(key)]?.incrementCodeLengthType === '0';
            const val1: any =
              numberingConfigs[Number(key)]?.digit &&
              numberingConfigs[Number(key)]?.incrementCodeLengthType === '1' &&
              numberingConfigs[Number(key)]?.incrementLength;

            console.log(numberingConfigs[Number(key)], val, val1);

            return val || val1;
          }
        );

        if (!allHaveUniformCode) {
          message.warning('请完善所有样本信息');
          setRandomlySampleLoading(false);
          return;
        }

        // 构建新的请求参数结构，符合后端API要求
        const customizeSampleCode = Object.keys(numberingConfigs)
          .map((indexStr) => {
            const index = Number(indexStr);
            const config = numberingConfigs[index];
            const sample = sampleListData[index];
            return {
              prefix: config.prefix || '',
              digit: config.digit || '',
              incrementCodeLengthType: config.incrementCodeLengthType || '0',
              incrementLength: config.incrementLength || '0',
              label: sample.name,
              referenceResult: sample.value || '',
            };
          })
          .filter(Boolean);

        // 调用API获取随机编号，使用正确的参数结构
        const { code, data, msg } = await randomSampleCode({
          num: randomNumber,
          contents: customizeSampleCode,
          type: 'increment',
        });

        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg || '生成编号失败');
          setRandomlySampleLoading(false);
          return;
        }

        if (!data || !Array.isArray(data) || data.length === 0) {
          message.error('返回的数据格式不正确');
          setRandomlySampleLoading(false);
          return;
        }

        console.log('API返回数据:', data);

        // 创建临时数据结构，用于储存样本列表
        const _sampleListData = cloneDeep(sampleListData);
        console.log('处理前的样本数据:', _sampleListData);

        // 清空所有样本的sampleVos
        _sampleListData.forEach((sample) => {
          sample.sampleVos = [];
        });

        // 处理新格式的数据: [{sampleBox: "xxx", samples: ["YUU987", "QWE981"]}]
        if (data && Array.isArray(data)) {
          // 遍历每个样本盒
          data.forEach((boxData: any, boxIndex: number) => {
            const sampleBox = boxData.sampleBox;
            const samples = boxData.samples;

            // 检查 samples 是否为对象
            if (typeof samples === 'object' && samples !== null) {
              // 遍历对象的键值对
              Object.entries(samples).forEach(([sampleType, sampleCode]) => {
                const sampleIndex = _sampleListData.findIndex(
                  (sample) => sample.name === sampleType
                );
                if (sampleIndex !== -1) {
                  _sampleListData[sampleIndex].sampleVos.push({
                    assessedLabId: null,
                    assessedLabCode: null,
                    assessedLabName: null,
                    sampleBox: sampleBox,
                    sampleCode: sampleCode as string,
                  });
                }
              });
            }
          });
        }

        // 为表格生成数据
        const labs: any[] = [];

        // 计算实际创建的行数，基于API返回的数据
        const actualRows = Math.max(
          1, // 至少有一行
          data.length
        );

        // 为每个被考核单位创建一行数据
        for (let i = 0; i < data.length; i++) {
          const row: any = {
            assessedLabId: null,
            assessedLabCode: null,
            assessedLabName: null,
            _key: `unit_${i}_${Date.now()}_${Math.random()
              .toString(36)
              .slice(2)}`,
            sampleBox: null,
            ...data[i].samples,
          };

          // 为每个样本类型添加样本编号
          _sampleListData.forEach((sample, sampleIndex) => {
            if (sample.sampleVos && i < sample.sampleVos.length) {
              row[sample.name] = sample.sampleVos[i].sampleCode;
              // 只用第一个样本的sampleBox作为行的sampleBox
              if (sampleIndex === 0) {
                row.sampleBox = sample.sampleVos[i].sampleBox;
              }
            }
          });

          labs.push(row);
        }

        console.log('处理后的表格数据:', labs);
        console.log(
          '处理后的样本数据详情:',
          _sampleListData.map((sample) => ({
            name: sample.name,
            sampleVos: sample.sampleVos,
          }))
        );

        // 更新状态
        setSampleListData(_sampleListData);
        setSampleNumList(labs);
        setIsOpenGenerateSampleNumModal(false);
        setRandomlySampleLoading(false);

        // 清除表单数据
        setRandomNumber(null);
        setNumberingConfigs({});

        message.success('样本编号生成成功');
      }
    } catch (error) {
      setRandomlySampleLoading(false);
      message.error('保存任务失败，请重试');
      console.error('保存任务失败:', error);
    }
  };

  // 生成配置块的函数
  const renderNumberingBlocks = () => {
    const RadioGroup = (
      <div className="w-full flex flex-row flex-nowrap gap-4 mb-4">
        <span className="mr-2">编号生成方式:</span>
        <Radio.Group
          value={radioGroupValue}
          onChange={(e) => handleConfigChangeAll('type', e.target.value)}
        >
          <Radio value="uniform">全部统一为一个编号</Radio>
          <Radio value="random">随机生成编号</Radio>
          <Radio value="customize">自增编号</Radio>
        </Radio.Group>
      </div>
    );

    const sampleList = sampleListData?.map((item, index) => {
      let config: any = numberingConfigs[index] || { type: 'uniform' };

      return (
        <div
          key={index}
          className="w-full p-4 border border-solid border-[#D9D9D9] rounded-md"
        >
          <div className="text-lg font-medium mb-2">{item.name}</div>
          {/* 根据类型显示不同配置 */}
          {radioGroupValue === 'uniform' && (
            <div className="w-full flex flex-row flex-nowrap items-center gap-2 mb-4">
              <span className="text-red-500 mr-1">*</span>
              <span className="mr-2">统一编号:</span>
              <Input
                value={config.uniformCode || ''}
                onChange={(e: any) =>
                  handleConfigChange(index, 'uniformCode', e.target.value)
                }
                placeholder="请输入统一编号"
                style={{ width: '200px' }}
              />
            </div>
          )}
          {radioGroupValue === 'random' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>后缀位数:</span>
                </span>
                <Select
                  value={config.suffixDigits}
                  onChange={(value) =>
                    handleConfigChange(index, 'suffixDigits', value)
                  }
                  options={suffixNumList}
                  style={{ width: '200px' }}
                  placeholder="请选择后缀位数"
                />
                <span className="text-red-500 ml-2">示例: XJK001</span>
              </div>
            </div>
          )}
          {radioGroupValue === 'customize' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>起始编号:</span>
                </span>
                <Input
                  value={config.digit || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'digit', e.target.value)
                  }
                  placeholder="请输入起始编号"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  输入01则会从01开始编号
                </span>
              </div>

              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>固定编号长度:</span>
                </span>
                <Radio.Group
                  value={config.incrementCodeLengthType || '0'}
                  onChange={(e: any) => {
                    console.log('选中的值:', e.target.value); // 添加调试输出
                    handleConfigChange(
                      index,
                      'incrementCodeLengthType',
                      e.target.value
                    );
                  }}
                >
                  <Radio value="0">不固定长度</Radio>
                  <Radio value="1">固定长度</Radio>
                </Radio.Group>
                {config.incrementCodeLengthType === 1 && (
                  <Input
                    value={config.incrementLength || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleConfigChange(
                        index,
                        'incrementLength',
                        e.target.value
                      )
                    }
                    placeholder="请输入"
                    style={{ width: '200px' }}
                  />
                )}
              </div>
              <div style={{ color: 'gray', fontSize: '12px' }}>
                设置固定长度后编号位数不够时会自动补0，补0是指在数字编号前添加零，使其达到固定位数的操作。
                例如固定3位长度，001 002 003 ... 100 101
              </div>
            </div>
          )}
        </div>
      );
    });

    return (
      <div>
        {RadioGroup}
        <div
          className="w-full flex-1 overflow-x-hidden overflow-y-auto pr-4  pd-10"
          style={{ height: '400px' }}
        >
          {sampleList}
        </div>
      </div>
    );
  };

  /**
   * @TODO 获取被考核单位列表
   */
  const getAssessedLabList = async () => {
    try {
      const { code, data, msg } = await queryOrgList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        // 转换数据格式适配列表显示，添加类型字段
        const formattedData = data.map((item: any) => ({
          id: item.deptId,
          labCode: item.deptCode,
          labName: item.deptName,
          // 根据名称判断类型，这里仅作示例，实际可能需要根据API返回数据确定
          type:
            item.deptName.includes('医院') || item.deptName.includes('医疗')
              ? 'hospital'
              : item.deptName.includes('中心')
              ? item.deptName.includes('市')
                ? 'city'
                : 'district'
              : 'other',
        }));
        setAssessedLabList(formattedData);
      } else {
        message.error(msg);
      }
    } catch (error) {
      console.error('获取被考核单位列表失败:', error);
      message.error('获取被考核单位列表失败');
    }
  };

  /**
   * @TODO 打开被考核单位选择弹窗
   */
  const openAssessedLabModal = (record: any) => {
    setCurrentRow(record);
    setAssessedLabModalVisible(true);
  };

  /**
   * @TODO 处理搜索关键词变化
   */
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value;
    setSearchKeyword(keyword);

    // 根据关键词过滤单位列表
    if (keyword) {
      const filtered = assessedLabList.filter(
        (lab) =>
          lab.labName.toLowerCase().includes(keyword.toLowerCase()) ||
          lab.labCode.toLowerCase().includes(keyword.toLowerCase())
      );
      setFilteredLabList(filtered);
    } else {
      setFilteredLabList(assessedLabList);
    }
  };

  /**
   * @TODO 处理考核单位选择变化
   */
  const handleLabSelectionChange = (labId: string) => {
    setSelectedLabId(labId);
  };

  /**
   * @TODO 选择被考核单位
   */
  const handleSelectAssessedLab = (selectedLab: any) => {
    if (!currentRow) {
      message.warning('请选择一个被考核单位');
      return;
    }

    // 获取当前表格行的样本盒ID（作为样本组标识）
    const sampleBoxId =
      currentRow._key && /^\d+$/.test(currentRow._key)
        ? currentRow._key
        : currentRow.sampleBox && /^\d+$/.test(currentRow.sampleBox)
        ? currentRow.sampleBox
        : Date.now().toString() + Math.floor(Math.random() * 1000).toString();

    // 更新表格中当前行
    console.log(sampleNumList, 'sampleNumList');

    const updatedSampleNumList = sampleNumList.map((item) => {
      if (item === currentRow) {
        return {
          ...item,
          assessedLabId: selectedLab.id,
          assessedLabCode: selectedLab.labCode,
          assessedLabName: selectedLab.labName,
          deptId: selectedLab.deptId,
          sampleBox: sampleBoxId, // 保留原样本盒ID作为组标识
        };
      }
      return item;
    });

    // 更新样本数据中相关样本盒的被考核单位信息
    const updatedSampleListData = sampleListData.map((sample) => {
      if (sample.sampleVos) {
        // 找到所有匹配当前行的记录
        const matchingVosIndex = sample.sampleVos.findIndex((vos: any) => {
          // 使用样本编号或样本盒ID进行匹配
          return (
            vos.sampleCode === currentRow[sample.name] ||
            (currentRow.assessedLabId &&
              vos.assessedLabId === currentRow.assessedLabId)
          );
        });

        if (matchingVosIndex >= 0) {
          // 更新匹配到的记录
          sample.sampleVos[matchingVosIndex] = {
            ...sample.sampleVos[matchingVosIndex],
            assessedLabId: selectedLab.id,
            assessedLabCode: selectedLab.labCode,
            assessedLabName: selectedLab.labName,
            sampleBox: sampleBoxId, // 使用一致的样本盒ID
          };
        }
      }
      return sample;
    });

    // 判断 updatedSampleNumList 中是否有重复的 deptId
    const deptIds = updatedSampleNumList
      .filter((item) => item.deptId !== undefined) // 排除 deptId 未定义的情况
      .map((item) => item.deptId);
    const uniqueDeptIds = new Set(deptIds);
    const hasDuplicateDeptId = deptIds.length !== uniqueDeptIds.size;

    if (hasDuplicateDeptId) {
      message.warning('机构名称不能重复添加');
      return;
    }

    setSampleNumList(updatedSampleNumList);
    setSampleListData(updatedSampleListData);
    setAssessedLabModalVisible(false);
    message.success('被考核单位选择成功');
  };

  const handleSelectlMultipleAssessedLab = (selectedLab: any) => {
    console.log(selectedLab, 'selectedLab1111111');
    console.log(selectedRowKeys, 'selectedRowKeys11111111');
    console.log(sampleNumList, 'sampleNumList111111');

    console.log(selectedRowKeys, 11111, 'selectedRowKeys');

    const updatedSampleNumList: any = [];

    const selectedLabCopy = [...selectedLab];

    sampleNumList.forEach((item, index) => {
      if (selectedRowKeys.includes(item._key) && selectedLab.length > 0) {
        updatedSampleNumList.push({
          ...item,
          assessedLabId: selectedLab[0].id,
          assessedLabCode: selectedLab[0].labCode,
          assessedLabName: selectedLab[0].labName,
          deptId: selectedLab[0].deptId,
          sampleBox: item.sampleBox, // 保留原样本盒ID作为组标识
        });
        selectedLab.shift();
      } else {
        updatedSampleNumList.push({
          ...item,
        });
      }
    });

    console.log(sampleListData, selectedRowKeys);

    // const updatedSampleListData = sampleListData.map((sample, index) => {
    //   if (sample.sampleVos) {
    //     // 找到所有匹配当前行的记录
    //     const matchingVosIndex = sample.sampleVos.findIndex((vos: any) => {
    //       // 使用样本编号或样本盒ID进行匹配
    //       return (
    //         vos.sampleCode === currentRow[sample.name] ||
    //         (currentRow.assessedLabId &&
    //           vos.assessedLabId === currentRow.assessedLabId)
    //       );
    //     });

    //     if (matchingVosIndex >= 0) {
    //       // 更新匹配到的记录
    //       sample.sampleVos[matchingVosIndex] = {
    //         ...sample.sampleVos[matchingVosIndex],
    //         assessedLabId: selectedLab[index].id,
    //         assessedLabCode: selectedLab[index].labCode,
    //         assessedLabName: selectedLab[index].labName,
    //         sampleBox: sample.sampleBox, // 保留原样本盒ID作为组标识
    //       };
    //     }
    //   }
    //   return sample;
    // });

    // const updatedSampleListData:any = [];

    console.log(sampleListData, selectedRowKeys, 'updatedSampleNumList');
    // ... 已有代码 ...

    // 存储匹配项的索引
    let matchingIndices: { sampleListIndex: number; sampleVosIndex: number }[] =
      [];

    // 遍历 sampleListData
    sampleListData.forEach((sample, sampleListIndex) => {
      if (sample.sampleVos) {
        // 遍历 sampleVos 数组
        sample.sampleVos.forEach((sampleVo: any, sampleVosIndex: any) => {
          const sampleBoxValue = sampleVo.sampleBox;
          // 检查 sampleBox 的值是否部分包含在 selectedRowKeys 中每个值 _ 之前的那段字段里
          const isMatch: any = selectedRowKeys.some((rowKey: any) => {
            const beforeUnderscore = rowKey.split('_')[0];
            return beforeUnderscore.includes(sampleBoxValue);
          });

          if (isMatch) {
            // 记录匹配项的索引
            matchingIndices.push({
              sampleListIndex,
              sampleVosIndex,
            });
          }
        });
      }
    });

    console.log(
      matchingIndices.length / sampleListData.length !== selectedLabCopy.length
    );

    if (
      matchingIndices.length / sampleListData.length !==
      selectedLabCopy.length
    ) {
      // 计算匹配索引数量与样本列表长度的商
      const matchRatio = matchingIndices.length / sampleListData.length;
      // 计算差值
      const diff =
        matchingIndices.length -
        (matchRatio - selectedLabCopy.length) * sampleListData.length;
      console.log(diff, 'diff');

      matchingIndices = matchingIndices.slice(0, diff);
    }

    const newselectedLabCopy: any = selectedLabCopy.flatMap((item) =>
      Array.from({ length: sampleListData.length }, () => item)
    );

    console.log(matchingIndices, 'matchingIndices');

    const updatedSampleListData = sampleListData.map(
      (sample: any, index1: any) => {
        if (sample.sampleVos) {
          sample.sampleVos.forEach((element: any, index2: any) => {
            // 判断 index1 和 index2 是否存在于 matchingIndices 中
            const isMatchIndex = matchingIndices.some(
              ({ sampleListIndex, sampleVosIndex }) => {
                return sampleListIndex === index1 && sampleVosIndex === index2;
              }
            );

            if (isMatchIndex) {
              sample.sampleVos[index2] = {
                assessedLabId: newselectedLabCopy[0].id,
                assessedLabCode: newselectedLabCopy[0].labCode,
                assessedLabName: newselectedLabCopy[0].labName,
                deptId: newselectedLabCopy[0].deptId,
                sampleBox: element.sampleBox, // 保留原样本盒ID作为组标识
              };
              newselectedLabCopy.shift();
            }
          });
        }

        return sample;
      }
    );

    setSampleNumList(updatedSampleNumList);
    setSampleListData(updatedSampleListData);
    setAssessedLabModalMultipleVisible(false);

    message.success('被考核单位选择成功');
  };

  // 添加选择行变更处理函数
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 确定每个记录的唯一标识符，仅用于前端删除操作
  const getRowKey = (record: any) => {
    // 使用_key作为唯一标识符，如果不存在则使用assessedLabId或生成一个随机值
    return (
      record?._key ||
      record?.assessedLabId ||
      Math.random().toString(36).slice(2)
    );
  };

  // 添加删除选中行的处理函数
  const handleDeleteSelectedRows = () => {
    setDeleteLoading(true);

    try {
      console.log('删除前的数据：', sampleNumList);
      console.log('选中行的key：', selectedRowKeys);

      // 使用确定的rowKey函数来过滤数据
      const newSampleNumList = [...sampleNumList].filter((item) => {
        const rowKey = getRowKey(item);
        const shouldKeep = !selectedRowKeys.includes(rowKey);
        console.log('行key:', rowKey, '保留?', shouldKeep);
        return shouldKeep;
      });

      console.log('删除后的数据：', newSampleNumList);

      // 更新表格数据
      setSampleNumList(newSampleNumList);

      // 更新样本数据，从每个样本的 sampleVos 中删除对应的被考核单位
      const updatedSampleListData = cloneDeep(sampleListData);

      // 记录已删除的sampleBox值，用于匹配删除sampleVos中的项
      const deletedSampleBoxes = sampleNumList
        .filter((item) => selectedRowKeys.includes(getRowKey(item)))
        .map((item) => item.sampleBox);

      console.log('删除的sampleBox:', deletedSampleBoxes);

      updatedSampleListData.forEach((sample) => {
        if (sample.sampleVos && sample.sampleVos.length > 0) {
          // 使用sampleBox匹配删除项
          sample.sampleVos = sample.sampleVos.filter(
            (sampleVo: SampleVosItem) =>
              !deletedSampleBoxes.includes(sampleVo.sampleBox)
          );
        }
      });

      setSampleListData(updatedSampleListData);

      // 清空选中行
      setSelectedRowKeys([]);

      // 删除成功提示
      messageApi.success('删除成功');
    } catch (error) {
      messageApi.error('删除失败，请重试');
      console.error('删除失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 设置行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  // 初始化时获取被考核单位列表
  useEffect(() => {
    getAssessedLabList();
  }, []);

  // 当单位列表更新时，更新过滤后的列表
  useEffect(() => {
    setFilteredLabList(assessedLabList);
  }, [assessedLabList]);

  useEffect(() => {
    // 初始样本列表
    const qaTaskSampleVosNew = cloneDeep(baseInfo.qaTaskSampleVos);
    let newSampleListData: any = [];
    if (qaTaskSampleVosNew) {
      qaTaskSampleVosNew.forEach((item: any) => {
        item.qaTaskSampleItemVoList.forEach((item2: any) => {
          item2.code = item.name;
        });
      });
      newSampleListData = qaTaskSampleVosNew;
    } else {
      newSampleListData = tableData;
    }
    setSampleListData([...newSampleListData]);
  }, [baseInfo]);

  useEffect(() => {
    // 初始化随机生成编码表格
    const { qaTaskSampleVos } = baseInfo;
    if (qaTaskSampleVos?.length) {
      // 创建一个新的结果数组
      const resultArr: any[] = [];

      // 创建一个映射表，通过sampleBox分组
      const sampleBoxGroups: Record<string, any[]> = {};

      // 遍历所有样本及其被考核单位
      qaTaskSampleVos.forEach((sample: any) => {
        if (sample.sampleVos && sample.sampleVos.length) {
          sample.sampleVos.forEach((sampleVo: any) => {
            // 使用sampleBox作为组ID
            const sampleBoxId =
              sampleVo.sampleBox && /^\d+$/.test(sampleVo.sampleBox)
                ? sampleVo.sampleBox
                : Date.now().toString() +
                  Math.floor(Math.random() * 1000).toString();

            // 如果这个组ID还没有记录，创建一个新记录
            if (!sampleBoxGroups[sampleBoxId]) {
              sampleBoxGroups[sampleBoxId] = [
                {
                  sampleType: sample.name,
                  sampleCode: sampleVo.sampleCode,
                  assessedLabId: sampleVo.assessedLabId,
                  assessedLabCode: sampleVo.assessedLabCode,
                  assessedLabName: sampleVo.assessedLabName,
                },
              ];
            } else {
              // 添加到已有的组
              sampleBoxGroups[sampleBoxId].push({
                sampleType: sample.name,
                sampleCode: sampleVo.sampleCode,
                assessedLabId: sampleVo.assessedLabId,
                assessedLabCode: sampleVo.assessedLabCode,
                assessedLabName: sampleVo.assessedLabName,
              });
            }
          });
        }
      });

      // 将分组数据转换为表格行
      Object.entries(sampleBoxGroups).forEach(([sampleBoxId, groupItems]) => {
        // 创建基本行结构，使用第一个项目的被考核单位信息
        const newRow: any = {
          assessedLabId: groupItems[0].assessedLabId,
          assessedLabCode: groupItems[0].assessedLabCode,
          assessedLabName: groupItems[0].assessedLabName,
          sampleBox: sampleBoxId,
          _key: `${sampleBoxId}_${Date.now()}_${Math.random()
            .toString(36)
            .slice(2)}`,
        };

        // 添加每种样本的编号
        groupItems.forEach((item) => {
          newRow[item.sampleType] = item.sampleCode;
        });

        resultArr.push(newRow);
      });

      console.log('初始化表格数据:', resultArr);
      setSampleNumList(resultArr);
    }
  }, [baseInfo]);

  useEffect(() => {
    // 考核项目选中
    const sampleListDataNew = cloneDeep(sampleListData);
    if (sampleListDataNew?.length) {
      sampleListDataNew?.forEach((_item: any) => {
        let CopyQaTaskSampleItemVoList = cloneDeep(
          _item?.qaTaskSampleItemVoList
        );
        const checked = assessmentCheckBox
          ?.filter((Item) => Item.check === 1)
          ?.map((_item2: any) => {
            let Item = {
              code: _item.name, // 样本编号
              itemName: _item2.name,
              ratio: _item2.ratio,
              condition: '',
              conditionTwo: '',
              conditionThree: '',
              conditionFour: '',
              conditionFive: '',
              conditionSix: '',
              conditionSeven: '',
              conditionEight: '',
              conditionNine: '',
            };
            // 保留映射原有已选规则
            if (CopyQaTaskSampleItemVoList.length) {
              CopyQaTaskSampleItemVoList?.forEach((_item3: any) => {
                if (_item3.itemName === _item2.name) {
                  Item = {
                    ...Item,
                    condition: _item3.condition ?? '',
                    conditionTwo: _item3.conditionTwo ?? '',
                    conditionThree: _item3.conditionThree ?? '',
                    conditionFour: _item3.conditionFour ?? '',
                    conditionFive: _item3.conditionFive ?? '',
                    conditionSix: _item3.conditionSix ?? '',
                    conditionSeven: _item3.conditionSeven ?? '',
                    conditionEight: _item3.conditionEight ?? '',
                    conditionNine: _item3.conditionNine ?? '',
                  };
                }
              });
            }
            return Item;
          });
        _item.qaTaskSampleItemVoList = checked;
      });
      setSampleListData([...sampleListDataNew]);
    }
  }, [assessmentCheckBox]);

  useEffect(() => {
    // 随机编号表头
    const header = sampleListData.map((item, idx) => {
      return {
        title: item.name,
        dataIndex: item.name,
        key: item.name,
        hideInSearch: true,
      };
    });
    // 初始样本列表
    setSampleNumHeader([...header]);
  }, [sampleListData]);

  useEffect(() => {
    if (sampleListData?.length) {
      sampleListData.forEach((item: any) => {
        if (listRefs.current[item.name]) {
          listRefs.current[item.name]?.setFieldValue('value', item.value);
          listRefs.current[item.name]?.setFieldValue('realCode', item.realCode);
        }
      });
    }
  }, [sampleListData]);

  const props: UploadProps = {
    action: import.meta.env.VITE_URL + '/qa/tasks/importSampleCode',
    headers: { Authorization: 'Bearer ' + token },
    data: { taskId: baseInfo?.id },
    onChange: ({ file, fileList }) => {
      if (file.status === 'done') {
        if (file.response?.code === 200) {
          getDetailData();
          message.success('上传成功');
        } else {
          message.error(file.response?.msg);
        }
      }
    },
    showUploadList: false,
    capture: undefined,
  };

  /**
   * 打开样本编号生成弹窗并初始化数据
   */
  const openGenerateSampleNumModal = () => {
    // 清空被考核单位数量值
    setRandomNumber(null);

    // 清空编号配置
    setNumberingConfigs({});

    // 打开弹窗
    setIsOpenGenerateSampleNumModal(true);
  };

  return (
    <>
      {contextHolder}
      {/* 任务配置 */}
      {!isUpdateJudgCriteria ? (
        <div className="mt-4">
          <TaskConfiguration
            onRef={formRefTask}
            setFormInfo={(val) => setAssessmentCheckBox(val)}
            taskConfigurationForm={baseInfo}
          />
        </div>
      ) : null}
      {/* 明细配置 */}
      <div className="mt-4">
        <BlockContainer title="明细配置" bodyStyle={{ padding: '20px' }}>
          <Row gutter={[25, 25]}>
            {sampleListData?.map((item: SampleData, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                  extra={
                    sampleListData.length > 1 ? (
                      <Popconfirm
                        title="删除此样本？"
                        onConfirm={() => handleDeleteSampleItem(item.name)}
                        okText="确定"
                        cancelText="取消"
                        key="delpro"
                      >
                        <a className="text-[red]">删除</a>
                      </Popconfirm>
                    ) : null
                  }
                >
                  <div className="h-[300px] overflow-auto overflow-x-hidden">
                    <ProForm
                      formRef={
                        ((ref: any): void =>
                          (listRefs.current[item.name] = ref)) as any
                      }
                      {...formItemLayout}
                      layout="horizontal"
                      grid={true}
                      submitter={false}
                      autoFocusFirstInput={false}
                      onValuesChange={(values: any) => {
                        console.log(values, 788899);

                        if (values.value || values.realCode) {
                          sampleListData.forEach((Items: any) => {
                            if (Items.name === item.name && values.value) {
                              Items.value = values.value;
                            }

                            if (Items.name === item.name && values.realCode) {
                              Items.realCode = values.realCode;
                            }
                          });
                          setSampleListData([...sampleListData]);
                        }
                      }}
                    >
                      <ProFormTextArea
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        allowClear
                        label="参考结果"
                        rules={[
                          { required: true, message: '参考结果为必填项' },
                        ]}
                      />
                      <ProFormText
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="realCode"
                        label="真实编号"
                      />
                      <div className="real-code-tips">被考核机构不可见</div>

                      <ProTable
                        key="itemName"
                        className="w-full px-1"
                        toolBarRender={false}
                        options={false}
                        search={false}
                        bordered
                        columns={columns}
                        dataSource={item?.qaTaskSampleItemVoList}
                        pagination={false}
                      />
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
            <Col xs={24} sm={8} md={8}>
              <BlockContainer
                title={`第${sampleListData.length + 1}种样本`}
                className="border-[#D9D9D9]"
              >
                <div className="h-[300px] flex justify-center items-center">
                  <Tooltip title="点击添加样本">
                    <PlusCircleOutlined
                      className="text-[50px] text-[#A8A8A8] cursor-pointer"
                      onClick={handleAddSampleItem}
                    />
                  </Tooltip>
                </div>
              </BlockContainer>
            </Col>
          </Row>
          {!isUpdateJudgCriteria ? (
            <BlockContainer className="mt-6">
              <ProTable
                columns={columns2}
                dataSource={sampleNumList}
                pagination={false}
                search={false}
                options={false}
                rowSelection={rowSelection}
                rowKey={getRowKey}
                headerTitle={
                  <div className="w-full">
                    <div className="w-full flex flex-row flex-nowrap gap-4">
                      <Button
                        type="primary"
                        loading={randomlySampleLoading}
                        onClick={openGenerateSampleNumModal}
                      >
                        随机生成样本编号
                      </Button>

                      <Button
                        onClick={handleOpenAssessedLabModal}
                        loading={loading2}
                        type="default"
                        disabled={selectedRowKeys.length === 0}
                      >
                        选择被考核单位
                      </Button>

                      <Button
                        onClick={handleExport}
                        loading={loading2}
                        icon={<DownloadOutlined />}
                        type="default"
                      >
                        导出样本清单
                      </Button>
                      <Upload {...props}>
                        <Button
                          type="primary"
                          disabled={!sampleNumList?.length}
                          title={
                            !sampleNumList?.length ? '样本随机编号列表为空' : ''
                          }
                        >
                          使用excel更新样本编号
                        </Button>
                      </Upload>
                      <Popconfirm
                        title="确认删除所选记录?"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={handleDeleteSelectedRows}
                      >
                        <Button
                          danger
                          type="primary"
                          disabled={selectedRowKeys.length === 0}
                          loading={deleteLoading}
                        >
                          删除所选
                        </Button>
                      </Popconfirm>
                    </div>
                    <div className="w-full mt-4 text-sm text-red-500">
                      温馨提示：如您已经完成样本编号粘贴工作，可通过【导出样本清单】功能，将下方列表数据导出为excel，在excel中填写实际样本编号，最后通过【使用excel更新样本编号】导入系统。
                    </div>
                  </div>
                }
              />
            </BlockContainer>
          ) : null}
        </BlockContainer>
        {/* 标准判定弹窗 */}
        <StandardMaintenance
          close={() => {
            setSampleDetails(null);
            setStandardModel(false);
          }}
          open={standardModel}
          getRuler={queryRulesObj}
          rulesDetails={sampleDetails}
          taskId={baseInfo.id}
          taskType={baseInfo.assessmentType}
          curSelectedStandardType={curSelectedStandardType}
          onStandardTypeChange={(type) => setCurSelectedStandardType(type)}
        />
      </div>
      {/* 随机编号输入弹窗 */}
      <Modal
        className="min-w-[600px]"
        title="随机生成样本编号"
        width="40%"
        open={randomlySampleNumModel}
        onCancel={() => setRandomlySampleNumModel(false)}
        onOk={() => formRef.current?.submit()}
        confirmLoading={loading}
      >
        <ProForm
          className="pt-4"
          submitter={false}
          formRef={formRef}
          onFinish={handleRandomlySampleNum}
          layout="horizontal"
          onValuesChange={(
            _: Record<string, any>,
            values: Record<string, any>
          ) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormDigit
            min={0}
            fieldProps={{ precision: 0 }}
            label="样本盒数量"
            name="boxNum"
            rules={[{ required: true, message: '请输入样本编号数量' }]}
            placeholder="请输入样本编号数量"
          />
        </ProForm>
      </Modal>
      {/* 生成样本编号 Modal */}
      <Modal
        open={isOpenGenerateSampleNumModal}
        onCancel={() => setIsOpenGenerateSampleNumModal(false)}
        title="考核样编号生成规则"
        centered
        width={800}
        footer={
          <Button
            type="primary"
            onClick={() => {
              handleRandomlySampleNumV2();
            }}
          >
            开始生成编号
          </Button>
        }
      >
        <div className="w-full max-h-[70vh] overflow-hidden flex flex-col gap-4">
          <div className="bg-[#fff7f7] p-4 border border-solid border-[#ffccc7] rounded-md">
            <div className="text-[#f5222d] font-bold">
              【非常重要】温馨提示:{' '}
            </div>
            <div className="mt-2">
              1、使用"全部统一为一个编号"，所有被考核单位对应种类的样本均为该编号。例如：设置第1种样本统一编号为A01，则所有被考核单位第1种样本编号都将为A01；
            </div>
            <div className="mt-1">
              2、使用"随机生成编号"，编号前缀可不填，后缀随机数位数最多支持5位。例如：前缀设置为XJK，随机数选择3，则对应种类的考核样编号将为XJK001、XJK002...
            </div>
          </div>
          <div className="w-full flex flex-row flex-nowrap gap-2 my-2 items-center">
            <span>被考核单位总数量: </span>
            <InputNumber
              min={1}
              style={{ width: '180px' }}
              value={randomNumber}
              onChange={(val: number | null) => setRandomNumber(val)}
              precision={0}
              placeholder="请输入被考核单位数量"
            />
          </div>
          <div>{renderNumberingBlocks()}</div>
        </div>
      </Modal>
      {/* 被考核单位选择弹窗 */}
      <AssessedLabModal
        open={assessedLabModalVisible}
        onCancel={() => setAssessedLabModalVisible(false)}
        onSelect={handleSelectAssessedLab}
        currentRow={currentRow}
      />

      {/* 批量被考核单位选择弹窗 */}
      <AssessedLabModalMultiple
        open={assessedLabModalMultipleVisible}
        onCancel={() => setAssessedLabModalMultipleVisible(false)}
        onSelect={handleSelectlMultipleAssessedLab}
        currentRow={setCurrentMultipleRow}
        selectLength={selectedRowKeys.length}
        sampleNumList={sampleNumList}
      />
    </>
  );
};

export default EditBindSample;
