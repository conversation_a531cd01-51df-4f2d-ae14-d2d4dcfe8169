/* eslint-disable array-callback-return */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { adjustTemplate, adjustTemplateView } from '@/api/quality';
import { getTempDetailCode } from '@/api/temp';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { useTempStore } from '@/store/temp';
import {
  EditableProTable,
  FormInstance,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import type {
  EditableFormInstance,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import RequiredTag from '@/components/RequiredTag';
import Preview from '@/pages/Quality/Temp/components/Preview';
import { formItemLayout } from '../data';
import SelectConfig from './SelectConfig';

type TEditProps = {
  close: () => void;
  taskId?: string; // 模板id
  isPreview?: boolean; // 是否预览模板
};

type DataSourceType = any;

const AdjustTemplate: React.FC<TEditProps> = ({
  close,
  taskId,
  isPreview = false,
}) => {
  // 预览弹窗
  const [previewModel, setPreviewModel] = useState<boolean>(false);

  // 获取table中需要的枚举
  const { assessmentTypesOnForm, getAssessmentTypes } = useQualityStore();
  const { inputTypeOnTable, getInputType } = useTempStore();

  useEffect(() => {
    getInputType();
    getAssessmentTypes();
  }, []);

  // 启用模板按钮
  const [loading, setLoading] = useState<boolean>(false);

  // 样品检验项目信息
  const [sampleInformation, setSampleInformation] = useState<any>([]);

  // 预览模板页面数据
  const [previewPageData, setPreviewPageData] = useState({});

  const [formData, setFormData] = useState<any>({});

  // 表单1
  const formRef1 = useRef<FormInstance>(null);

  /* 基本信息表格 */
  const [editableKeys2, setEditableRowKeys2] = useState<React.Key[]>(() => []);
  const formRef2 = useRef<ProFormInstance<any>>();
  const editorFormRef2 = useRef<EditableFormInstance<DataSourceType>>();
  const columns2: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      width: '30%',
      editable: false,
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 200,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      editable: false,
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 240,
    },
  ];

  /* 检验基本信息配置表格 */
  const [editableKeys3, setEditableRowKeys3] = useState<React.Key[]>(() => []);
  const formRef3 = useRef<ProFormInstance<any>>();
  const editorFormRef3 = useRef<EditableFormInstance<DataSourceType>>();
  const columns3: ProColumns<DataSourceType>[] = [
    {
      title: <RequiredTag title="填报字段名称" />,
      dataIndex: 'fieldName',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
      width: '30%',
      editable: false,
    },
    {
      title: <RequiredTag title="是否必填" />,
      dataIndex: 'isRequired',
      valueType: 'radio',
      valueEnum: {
        1: {
          text: '必填',
        },
        0: {
          text: '非必填',
        },
      },
      width: 200,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
    {
      title: <RequiredTag title="输入框类型" />,
      key: 'inputBox',
      dataIndex: 'inputBox',
      valueType: 'select',
      valueEnum: inputTypeOnTable,
      editable: false,
    },
    {
      title: '信息提示',
      dataIndex: 'pomptInformation',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 240,
    },
  ];
  const columns4: ProColumns<DataSourceType>[] = [
    {
      title: '序号',
      dataIndex: 'fieldName',
      valueType: 'indexBorder',
      width: 150,
    },
    {
      title: '考核项目',
      dataIndex: 'project',
    },
  ];
  const editorFormRef4 = useRef<EditableFormInstance<DataSourceType>>();
  const [tableDate4, setTableDate4] = useState<any[]>([]);

  // 配置下拉项目
  const [showSelect, setShowSelect] = useState(false);
  const [showSelectData, setShowSelectData] = useState([]);
  const [showSelectIdx, setShowSelectIdx] = useState(-1);
  const [showSelectForm, setShowSelectForm] = useState('');

  // 已保存的配置下拉项
  const [selectEditableKey, setSelectEditableKey] = useState([]);

  /**
   * @TODO 保存并启用模板
   * */
  const enableTemplate = async () => {
    try {
      await formRef2.current?.validateFields();
      await formRef3.current?.validateFields();
      setLoading(true);
      const { form2, form3 } = await temporaryConfiguration();
      const params = {
        ...formData,
        inspects: form2,
        itemInfos: form3,
      };

      const { code, data, msg }: any = await adjustTemplate(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        deleteTemporaryConfiguration(); // 删除暂存的下拉配置的sessageStorge
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      message.warning('请完善表单');
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 预览填报
   * */
  const previewFill = async () => {
    setPreviewModel(true);
    // 预览数据处理
    const data = await temporaryConfiguration();
    // form2-检验基本信息    form3-样本信息    form4-样本项目信息
    setPreviewPageData({
      ...data,
      form4: sampleInformation?.map((item: any) => {
        item.itemName = item.name;
        return item;
      }),
    });
  };

  /**
   * @TODO 提取暂存的配置下拉选项
   * */
  const temporaryConfiguration = async () => {
    const formInfo1 = cloneDeep(formRef2.current?.getFieldValue('table'));
    const formInfo2 = cloneDeep(formRef3.current?.getFieldValue('table'));
    formInfo1.forEach((item: any, idx: number) => {
      const sessageStorgeObj = sessionStorage.getItem(item.id + 'form2');
      if (sessageStorgeObj) {
        item.enumItems = JSON.parse(sessageStorgeObj).enumItems;
      }
    });
    formInfo2.forEach((item: any, idx: number) => {
      const sessageStorgeObj = sessionStorage.getItem(item.id + 'form3');
      if (sessageStorgeObj) {
        item.enumItems = JSON.parse(sessageStorgeObj).enumItems;
      }
    });
    return { form2: formInfo1, form3: formInfo2 };
  };
  /**
   * @TODO 删除暂存下拉项
   * */
  const deleteTemporaryConfiguration = () => {
    const formInfo1 = cloneDeep(formRef2.current?.getFieldValue('table'));
    const formInfo2 = cloneDeep(formRef3.current?.getFieldValue('table'));
    formInfo1?.map((item: any) => {
      if (item.inputBox === 'select') {
        sessionStorage.removeItem(item.id + 'form2');
      }
    });
    formInfo2?.map((item: any) => {
      if (item.inputBox === 'select') {
        sessionStorage.removeItem(item.id + 'form3');
      }
    });
  };

  // 详情
  const getDetail = async () => {
    if (taskId) {
      try {
        const { code, data, msg }: any = await adjustTemplateView({
          id: taskId,
        });
        if (code === codeDefinition.QUERY_SUCCESS) {
          setFormData(data);
          // 基本信息
          formRef1.current?.setFieldsValue(data);
          // 检验基本信息配置
          formRef2.current?.setFieldsValue({
            table:
              data?.inspects?.map((item: any) => {
                return { ...item, isRequired: item.isRequired + '' };
              }) || [],
          });
          setEditableRowKeys2(data?.inspects?.map((item: any) => item.id));
          // 样品检验信息
          formRef3.current?.setFieldsValue({
            table:
              data?.itemInfos?.map((item: any) => {
                return {
                  ...item,
                  isRequired: item.isRequired + '',
                  isJudgmentItem: item.isJudgmentItem + '',
                };
              }) || [],
          });
          setEditableRowKeys3(data?.itemInfos?.map((item: any) => item.id));
          // 样品检验项目信息
          setSampleInformation(
            data?.items.map((item: string, idx: number) => ({
              id: idx,
              name: item,
            })) ?? []
          );
          // 考核项目
          const tableData4 = data.items.map((item: string, idx: number) => ({
            id: idx,
            project: item,
          }));
          setTableDate4(tableData4);
        } else {
          message.error(msg);
        }
      } catch (error) {
        throw new Error(`Error: ${error}`);
      }
    } else {
      getTemplteCode();
    }
  };

  /**
   * @TODO 新增获取模板id
   */
  const getTemplteCode = async () => {
    const { code, data, msg }: any = await getTempDetailCode();
    if (code === codeDefinition.QUERY_SUCCESS) {
      formRef1.current?.setFieldsValue({
        code: data,
      });
    } else {
      message.error(msg);
    }
  };

  useEffect(() => {
    getDetail();
  }, [taskId]);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="基本信息">
          <ProForm
            key="form1"
            formRef={formRef1}
            {...formItemLayout}
            layout="horizontal"
            grid={true}
            submitter={false}
            //@ts-ignore
            onValuesChange={(_, values: any) => {
              for (const key in values) {
                if (typeof values[key] === 'string') {
                  values[key] = values[key].trim();
                }
              }
              formRef1.current?.setFieldsValue(values);
            }}
          >
            <ProFormText
              name="templateCode"
              label="模板编号"
              readonly
              placeholder="请输入模板编号"
              rules={[{ required: true, message: '请输入模板编号' }]}
              colProps={formItemLayout}
            />
            <ProFormSelect
              options={assessmentTypesOnForm}
              readonly
              rules={[{ required: true, message: '请选择考核类型' }]}
              name="assessmentType"
              label="考核类型"
              colProps={formItemLayout}
            />
            <ProFormText
              name="templateName"
              readonly
              label="模板名称"
              placeholder="请输入模板名称"
              rules={[{ required: true, message: '请输入模板名称' }]}
              colProps={formItemLayout}
            />
          </ProForm>
        </BlockContainer>
        <div className="mt-4">
          <BlockContainer title="检验基本信息配置">
            <ProForm<{
              table: DataSourceType[];
            }>
              key="form2"
              formRef={formRef2}
              submitter={false}
              validateTrigger="onBlur"
            >
              <EditableProTable<DataSourceType>
                rowKey="id"
                scroll={{
                  x: 960,
                }}
                editableFormRef={editorFormRef2}
                name="table"
                controlled={true}
                recordCreatorProps={false}
                columns={columns2}
                editable={{
                  type: 'multiple',
                  editableKeys: editableKeys2,
                  onChange: setEditableRowKeys2,
                  actionRender: (row, config, defaultDom) => {
                    const btns = [];
                    if (row && row.inputBox! === 'select') {
                      btns.push(
                        <a
                          key="select"
                          onClick={() => {
                            setShowSelectIdx(config.index!);
                            setShowSelectForm('form2');
                            setShowSelect(true);
                            // 未保存
                            const jsonObj =
                              sessionStorage.getItem(row.id + 'form2') ?? '';
                            if (jsonObj) {
                              const obj = JSON.parse(jsonObj);
                              setShowSelectData(obj.enumItems.split(','));
                            } else {
                              setShowSelectData(
                                row.enumItems ? row.enumItems.split(',') : []
                              );
                            }
                            // 已保存
                            setSelectEditableKey(
                              row.enumItems ? row.enumItems.split(',') : []
                            );
                          }}
                        >
                          {isPreview ? '预览下拉项' : '配置下拉项'}
                        </a>
                      );
                    }
                    return [...btns];
                  },
                }}
              />
            </ProForm>
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验信息配置">
            <ProForm<{
              table: DataSourceType[];
            }>
              key="form3"
              formRef={formRef3}
              submitter={false}
              validateTrigger="onBlur"
            >
              <EditableProTable<DataSourceType>
                rowKey="id"
                scroll={{
                  x: 960,
                }}
                editableFormRef={editorFormRef3}
                name="table"
                controlled={true}
                recordCreatorProps={false}
                columns={columns3}
                editable={{
                  type: 'multiple',
                  editableKeys: editableKeys3,
                  onChange: setEditableRowKeys3,
                  actionRender: (row, config, defaultDom) => {
                    const btns = [];
                    if (row && row.inputBox! === 'select') {
                      btns.push(
                        <a
                          key="select"
                          onClick={() => {
                            setShowSelectIdx(config.index!);
                            setShowSelectForm('form3');
                            setShowSelect(true);
                            // 未保存
                            const jsonObj =
                              sessionStorage.getItem(row.id + 'form3') ?? '';
                            if (jsonObj) {
                              const obj = JSON.parse(jsonObj);
                              setShowSelectData(obj.enumItems.split(','));
                            } else {
                              setShowSelectData(
                                row.enumItems ? row.enumItems.split(',') : []
                              );
                            }
                            // 已保存
                            setSelectEditableKey(
                              row.enumItems ? row.enumItems.split(',') : []
                            );
                          }}
                        >
                          {isPreview ? '预览下拉项' : '配置下拉项'}
                        </a>
                      );
                    }
                    return [...btns];
                  },
                }}
              />
            </ProForm>
          </BlockContainer>
        </div>
        {isPreview ? (
          <div className="mt-4">
            <BlockContainer title="样本检验项目配置">
              <ProTable<DataSourceType>
                key="id"
                pagination={false}
                search={false}
                toolBarRender={false}
                columns={columns4}
                dataSource={tableDate4}
              />
            </BlockContainer>
          </div>
        ) : null}
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        {!isPreview ? (
          <>
            <Button
              type="primary"
              onClick={() => {
                enableTemplate();
              }}
              loading={loading}
            >
              保存
            </Button>
            <Button onClick={previewFill}>预览填报页</Button>
          </>
        ) : (
          <Button type="default" onClick={close} loading={loading}>
            关闭
          </Button>
        )}
      </div>
      {/* 预览模板 */}
      <Drawer
        width="85%"
        title="预览模板"
        onClose={() => setPreviewModel(false)}
        open={previewModel}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Preview
          previewPageData={previewPageData}
          close={() => setPreviewModel(false)}
        />
      </Drawer>
      {/* 选项下拉配置弹窗 */}
      <SelectConfig
        open={showSelect}
        list={showSelectData}
        editableKey={selectEditableKey}
        readonly={isPreview}
        closeDetail={(val: any) => {
          console.log(val);
          console.log(showSelectForm);
          if (val) {
            if (showSelectForm === 'form2') {
              const d = editorFormRef2.current?.getRowData?.(showSelectIdx);
              sessionStorage.setItem(
                d.id + showSelectForm,
                JSON.stringify({
                  ...d,
                  enumItems: Array.from(
                    new Set(val.map((row: any) => row.item))
                  ).join(','),
                })
              );
            }
            if (showSelectForm === 'form3') {
              const d = editorFormRef3.current?.getRowData?.(showSelectIdx);
              sessionStorage.setItem(
                d.id + showSelectForm,
                JSON.stringify({
                  ...d,
                  enumItems: Array.from(
                    new Set(val.map((row: any) => row.item))
                  ).join(','),
                })
              );
            }
          }
          setShowSelect(false);
        }}
      />
    </div>
  );
};

export default AdjustTemplate;
