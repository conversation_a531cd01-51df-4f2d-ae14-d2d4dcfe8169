/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Col,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import {
  queryOrgList,
  randomForRandomTask,
  randomlySampleNum,
} from '@/api/quality';
import { randomExporExpress } from '@/api/quality';
// 新版本随机编号生成API
//标准规则维护
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { DownloadOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import download from '@/utils/download';
import { formItemLayout } from '../../data';
import AssessedLabModal from '../AssessedLabModal';
import AssessedLabModalMultiple from '../AssessedLabModalMultiple';
import { TaskContext } from '../Edit';
//任务配置模块
import StandardMaintenance from '../Model/StandardMaintenanceModel';
import TaskConfiguration from '../TaskConfiguration';

// 样本列表初始数据
let tableData = [
  { name: '第1种样本', value: '', qaTaskSampleItemVoList: [], sampleVos: [] },
];

type TEditProps = {
  id?: any;
  onSubmit?: (params: any) => void;
  onSave?: () => Promise<void>;
  onRef?: any;
  baseInfo?: any; // 表单数据
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isUpdateJudgCriteria?: boolean;
  getDetailData: () => void;
};

type SampleData = {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  id?: string; // 添加id字段，可选
  qaTaskSampleItemVoList: any[];
  sampleVos: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
};

type RefObjectMap = {
  [key: string]: ProFormInstance<any> | null;
};

// 定义后缀数字位数
const suffixNumList = [
  // { label: '2位', value: 2 },
  { label: '3位', value: 3 },
  { label: '4位', value: 4 },
  { label: '5位', value: 5 },
  { label: '6位', value: 6 },
  { label: '7位', value: 7 },
  { label: '8位', value: 8 },
  // { label: '9位', value: 9 },
  // { label: '10位', value: 10 },
];
interface TaskDetailData {
  qaTaskSampleVos?: SampleData[];
  [key: string]: any;
}

const EditRandom: React.FC<TEditProps> = ({
  id,
  onSubmit,
  onSave,
  onRef,
  baseInfo,
  readonly,
  readonlyAll = false,
  hideRank = false,
  isUpdateJudgCriteria,
  getDetailData,
}) => {
  const { setRefType } = useContext(TaskContext);

  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any>([]);
  // 样本ref集合
  const listRefs = useRef<RefObjectMap>({});
  // 考核项目-已选项目
  const [assessmentCheckBox, setAssessmentCheckBox] = useState<any[]>([]);
  // 当前点击样本
  const [sampleDetails, setSampleDetails] = useState<any>({});
  const [currentSampleIdentifier, setCurrentSampleIdentifier] =
    useState<string>(''); // 追踪当前操作的样本
  // 随机生成的编号表格数据
  const [sampleNumList, setSampleNumList] = useState<any[]>([]);
  // 随机样本编号弹窗
  const [randomlySampleNumModel, setRandomlySampleNumModel] =
    useState<boolean>(false);
  // 选中样本盒
  const [boxName, setBoxName] = useState<string>('');
  // 编号表头
  const [sampleNumHeader, setSampleNumHeader] = useState<any[]>([]);
  const [randomlySampleLoading, setRandomlySampleLoading] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  // 标准判定维护弹窗
  const [standardModel, setStandardModel] = useState<boolean>(false);
  const formRefTask = useRef<any>(null);
  const formRef = useRef<ProFormInstance>(null);

  // 当前选择的判定标准的类型，正确、可疑或错误
  const [curSelectedStandardType, setCurSelectedStandardType] = useState<
    'correct' | 'suspicious' | 'error'
  >('correct');

  // 考核样编号生成 Modal 是否打开
  const [isOpenGenerateSampleNumModal, setIsOpenGenerateSampleNumModal] =
    useState<boolean>(false);

  // 新增状态管理每个样本的编号配置
  const [numberingConfigs, setNumberingConfigs] = useState<{
    [key: string]: {
      type?: any;
      uniformCode?: string;
      prefix?: string;
      suffixDigits?: number;
      generateCount?: number; // 添加生成数量
    };
  }>({});

  const [messageApi, contextHolder] = message.useMessage();

  const { token } = useTokenStore();

  // 添加上传状态管理
  const [uploadLoading, setUploadLoading] = useState<boolean>(false);

  // 添加选中行状态变量
  const [assesedLabListLoading, setAssesedLabListLoading] =
    useState<boolean>(false);

  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '考核项目',
      dataIndex: 'itemName',
      key: 'itemName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
      hideInSearch: true,
      render: (text, record) => {
        // 检查是否所有标准字段都为空
        const hasCorrectStandard =
          record?.condition || record?.conditionTwo || record?.conditionThree;
        const hasSuspiciousStandard =
          record?.conditionFour ||
          record?.conditionFive ||
          record?.conditionSix;
        const hasErrorStandard =
          record?.conditionSeven ||
          record?.conditionEight ||
          record?.conditionNine;

        // 如果所有标准都为空，则显示"维护判定标准"按钮
        if (
          !hasCorrectStandard &&
          !hasSuspiciousStandard &&
          !hasErrorStandard
        ) {
          return (
            <a
              onClick={() => handleSampleRules('correct', record)}
              className="text-primary"
            >
              维护判定标准
            </a>
          );
        }

        // 否则，显示现有的三种标准
        return (
          <div className="w-full flex flex-col">
            <a onClick={() => handleSampleRules('correct', record)}>
              正确(合格)标准:{' '}
              {record?.conditionThree ? record?.conditionThree : '未维护'}
            </a>
            <a onClick={() => handleSampleRules('suspicious', record)}>
              可疑标准: {record?.conditionSix ? record?.conditionSix : '未维护'}
            </a>
            <a onClick={() => handleSampleRules('error', record)}>
              错误(不合格)标准:{' '}
              {record?.conditionNine ? record?.conditionNine : '未维护'}
            </a>
          </div>
        );
      },
    },
  ];

  let columns2: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      fixed: 'left' as const,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '被考核单位',
      dataIndex: 'assessedLabName',
      fixed: 'left' as const,
      width: 180,
      render: (_: any, record: any, index: number) => (
        <div className="flex flex-row">
          <div
            className="text-[#1890ff] cursor-pointer"
            onClick={() => openAssessedLabModal(record, index)}
          >
            {record.assessedLabId ? record.assessedLabName : '请选择被考核单位'}
          </div>
        </div>
      ),
    },
    {
      title: '标准品类别',
      dataIndex: 'standardType',
      width: 180,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      width: 180,
    },
  ];

  // 添加被考核单位相关状态
  const [assessedLabModalVisible, setAssessedLabModalVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState<any>(null);

  const [currentMultipleRow, setCurrentMultipleRow] = useState<any>(null);

  const [assessedLabModalMultipleVisible, setAssessedLabModalMultipleVisible] =
    useState(false);

  const [assessedLabList, setAssessedLabList] = useState<any[]>([]);
  const [filteredLabList, setFilteredLabList] = useState<any[]>([]);
  // 添加当前行索引状态
  const [currentRowIndex, setCurrentRowIndex] = useState<number>(-1);

  // 添加质量控制点相关状态
  const [qcPointModalVisible, setQcPointModalVisible] = useState(false);
  const [qcPointList, setQcPointList] = useState<any[]>([]);
  const [selectedQcPointId, setSelectedQcPointId] = useState<string>('');
  const [qcPointSearchKeyword, setQcPointSearchKeyword] = useState('');
  const [filteredQcPointList, setFilteredQcPointList] = useState<any[]>([]);

  const [radioGroupValue, setRadioGroupValue] = useState<any>('random');

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleParams,
      handleValidateFields,
    };
  });

  /**
   * @TODO 添加一个样本
   */
  const handleAddSampleItem = async () => {
    const newItemSample = {
      name: `第${sampleListData.length + 1}种样本`,
      value: '',
      // 取第一个样本的值
      qaTaskSampleItemVoList:
        sampleListData && sampleListData![0]?.qaTaskSampleItemVoList?.length
          ? sampleListData![0]?.qaTaskSampleItemVoList.map((_item: any) => {
              const Item = {
                code: `第${sampleListData.length + 1}种样本`,
                itemName: _item.itemName ?? _item.assessmentName,
                condition: '',
                conditionTwo: '',
                conditionThree: '',
              };
              return Item;
            })
          : [],
      sampleVos: [],
    };
    setSampleListData([...sampleListData, newItemSample]);
  };

  /**
   * @TODO 设置样本规则
   */
  const handleSampleRules = (
    type: 'correct' | 'suspicious' | 'error',
    obj: any
  ) => {
    // 设置当前样本名称
    const currentSampleName = obj.sampleName || obj.code;
    // 保存当前样本标识符
    setCurrentSampleIdentifier(currentSampleName);
    // 设置当前选中的标准类型
    setCurSelectedStandardType(type);
    // 保存样本详情，确保包含sampleName
    setSampleDetails({
      ...obj,
      sampleName: currentSampleName,
    });
    setStandardModel(true);
  };

  const handleOpenAssessedLabModal = async () => {
    setAssessedLabModalMultipleVisible(true);
  };

  /**
   * @TODO 删除一个样本
   * @parms code 样本编号
   */
  const handleDeleteSampleItem = async (code: string) => {
    // 过滤当前样本列表，删除指定的样本
    const delSampleList =
      sampleListData.filter((_item: any) => _item.name !== code) ?? [];

    // 同时删除随机生成编号表格中关联的数据
    const filteredSampleNumList = sampleNumList.filter(
      (row) => row.standardType !== code
    );

    // 更新编号配置，移除被删除样本的配置
    const updatedNumberingConfigs = { ...numberingConfigs };
    // 查找要删除的样本索引
    const sampleIndex = sampleListData.findIndex(
      (item: any) => item.name === code
    );
    if (sampleIndex !== -1) {
      // 删除该索引的配置
      delete updatedNumberingConfigs[sampleIndex];

      // 重新调整索引
      const newConfigs: typeof numberingConfigs = {};
      Object.keys(updatedNumberingConfigs).forEach((key) => {
        const keyNum = parseInt(key);
        if (keyNum > sampleIndex) {
          // 大于删除索引的配置前移一位
          newConfigs[keyNum - 1] = updatedNumberingConfigs[keyNum];
        } else {
          // 小于删除索引的配置保持不变
          newConfigs[keyNum] = updatedNumberingConfigs[keyNum];
        }
      });

      setNumberingConfigs(newConfigs);
    }

    // 清理要删除样本的表单引用
    const updatedListRefs = { ...listRefs.current };
    delete updatedListRefs[code];

    // 重新生成样本编号并更新表格中的样本类型名称
    const updatedSampleNumList: any[] = [];
    const nameMapping: Record<string, string> = {}; // 用于存储旧名称到新名称的映射
    const newListRefs: RefObjectMap = {}; // 存储新的引用映射

    delSampleList.forEach((_item: any, idx: any) => {
      const oldName = _item.name;
      const newName = `第${idx + 1}种样本`;

      // 记录名称映射关系
      nameMapping[oldName] = newName;

      // 如果当前样本有表单引用，需要更新键名
      if (updatedListRefs[oldName]) {
        newListRefs[newName] = updatedListRefs[oldName];
        delete updatedListRefs[oldName];
      }

      // 更新样本名称
      _item.name = newName;

      // 同时更新样本中每个项的code属性
      if (
        _item.qaTaskSampleItemVoList &&
        Array.isArray(_item.qaTaskSampleItemVoList)
      ) {
        _item.qaTaskSampleItemVoList.forEach((item: any) => {
          if (item) {
            item.code = newName;
          }
        });
      }
    });

    // 创建一个映射，记录每个唯一的被考核单位和样本编号组合
    // 用于确保相同样本编号但不同被考核单位的数据保持唯一
    const labSampleMapping: Record<
      string,
      {
        assessedLabId: string;
        assessedLabCode: string;
        assessedLabName: string;
        sampleBox: string;
      }
    > = {};

    // 更新表格中的standardType和相关引用
    filteredSampleNumList.forEach((row) => {
      // 检查该行的standardType是否需要更新
      if (nameMapping[row.standardType]) {
        // 为每一行创建唯一的sampleBox标识
        // 使用行的assessedLabId和sampleCode组合作为key
        const mappingKey = `${row.assessedLabId || 'null'}_${row.sampleCode}`;

        // 确保每个被考核单位和样本编号组合都有唯一的sampleBox
        if (!labSampleMapping[mappingKey] && row.assessedLabId) {
          labSampleMapping[mappingKey] = {
            assessedLabId: row.assessedLabId,
            assessedLabCode: row.assessedLabCode,
            assessedLabName: row.assessedLabName,
            // 创建新的唯一sampleBox标识
            sampleBox: `${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 10)}`,
          };
        }

        // 如果有被考核单位信息，更新sampleBox
        if (row.assessedLabId && labSampleMapping[mappingKey]) {
          updatedSampleNumList.push({
            ...row,
            standardType: nameMapping[row.standardType],
            sampleBox: labSampleMapping[mappingKey].sampleBox,
          });
        } else {
          updatedSampleNumList.push({
            ...row,
            standardType: nameMapping[row.standardType],
          });
        }
      } else {
        updatedSampleNumList.push(row);
      }
    });

    // 确保样本数据中的sampleVos引用保持一致，同时更新sampleBox
    delSampleList.forEach((sample: any) => {
      if (sample.sampleVos && Array.isArray(sample.sampleVos)) {
        // 使用一个新数组来存储更新后的sampleVos
        const updatedSampleVos: any[] = [];

        sample.sampleVos.forEach((sampleVo: any, index: any) => {
          if (sampleVo) {
            // 创建映射键
            const mappingKey = `${sampleVo.assessedLabId || 'null'}_${
              sampleVo.sampleCode
            }`;

            // 更新sampleVo的_key属性
            const existingKey = sampleVo._key || '';
            let newKey = existingKey;

            // 从现有key中提取部分信息
            const keyParts = existingKey.split('_');
            if (keyParts.length >= 3) {
              // 如果是格式化的key，更新样本名称部分
              newKey = `${sample.name}_${index}_${keyParts.slice(2).join('_')}`;
            } else {
              // 如果不是标准格式的key，生成新key
              newKey = `${sample.name}_${index}_${Date.now()}_${Math.random()
                .toString(36)
                .substr(2, 8)}`;
            }

            // 检查是否需要更新sampleBox
            if (sampleVo.assessedLabId && labSampleMapping[mappingKey]) {
              // 使用labSampleMapping中已建立的sampleBox
              updatedSampleVos.push({
                ...sampleVo,
                _key: newKey,
                sampleBox: labSampleMapping[mappingKey].sampleBox,
              });
            } else if (sampleVo.assessedLabId) {
              // 如果有被考核单位但没有对应的映射，创建新的sampleBox
              const newSampleBox = `${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 10)}`;

              // 将新生成的sampleBox添加到映射中
              labSampleMapping[mappingKey] = {
                assessedLabId: sampleVo.assessedLabId,
                assessedLabCode: sampleVo.assessedLabCode,
                assessedLabName: sampleVo.assessedLabName,
                sampleBox: newSampleBox,
              };

              updatedSampleVos.push({
                ...sampleVo,
                _key: newKey,
                sampleBox: newSampleBox,
              });
            } else {
              // 没有被考核单位的情况
              updatedSampleVos.push({
                ...sampleVo,
                _key: newKey,
              });
            }
          }
        });

        // 更新样本的sampleVos数组
        sample.sampleVos = updatedSampleVos;
      }
    });

    // 更新表单引用
    listRefs.current = { ...newListRefs };

    // 更新状态
    setSampleListData(delSampleList);
    setSampleNumList(updatedSampleNumList);

    // 更新样本表头信息
    const header = delSampleList.map((item: any) => {
      return {
        title: item.name,
        dataIndex: item.name,
        key: item.name,
        hideInSearch: true,
      };
    });
    setSampleNumHeader([...header]);

    // 显示提示消息
    messageApi.success(`${code}已删除`);
  };

  /**
   * 导出样本清单
   */
  const handleExport = async () => {
    try {
      // 先保存
      onSave && (await onSave());
      setLoading(true);
      const params = {
        taskId: baseInfo?.id,
      };
      const data = await randomExporExpress(params);
      await download(data, '随机考核样本清单.xlsx'); //导出方法，传入后端返回的文件流
    } catch (error) {
      console.error('导出失败:', error);
      messageApi.error('导出失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理编号配置变更
   */
  const handleConfigChange = (
    sampleIndex: number,
    field: string,
    value: any
  ) => {
    setNumberingConfigs((prev) => {
      // 创建一个新的配置对象
      const newConfig = { ...prev };

      // 确保sampleIndex对应的配置对象存在
      if (!newConfig[sampleIndex]) {
        newConfig[sampleIndex] = { type: 'uniform' };
      }

      // 更新字段值
      newConfig[sampleIndex] = {
        ...newConfig[sampleIndex],
        [field]: value,
      };

      // 当类型改变时，确保相关字段有默认值
      if (field === 'type') {
        if (value === 'uniform' && !newConfig[sampleIndex].uniformCode) {
          newConfig[sampleIndex].uniformCode = '';
        } else if (value === 'random') {
          newConfig[sampleIndex].prefix = newConfig[sampleIndex].prefix || '';
          newConfig[sampleIndex].suffixDigits =
            newConfig[sampleIndex].suffixDigits || 3;
        }
      }

      return newConfig;
    });
  };

  /**
   * 随机生成样本编号-V2
   * 支持不同样本编号规则
   */
  const handleRandomlySampleNumV2 = async () => {
    try {
      setRandomlySampleLoading(true);
      // 检查样本列表
      if (!sampleListData || sampleListData.length === 0) {
        messageApi.error('请先添加样本');
        setRandomlySampleLoading(false);
        return;
      }

      // 检查是否所有配置都是统一编号
      const IsUniform = radioGroupValue === 'uniform';

      // 如果所有的编号都是统一编号
      if (IsUniform) {
        // 检查是否每个样本都有设置统一编号
        const allHaveUniformCode = Object.keys(numberingConfigs).every(
          (key) => !!numberingConfigs[Number(key)]?.uniformCode
        );

        if (!allHaveUniformCode) {
          messageApi.warning('请为所有样本设置统一编号');
          setRandomlySampleLoading(false);
          return;
        }

        // 处理全部为统一编号的情况
        const _sampleListData = cloneDeep(sampleListData);
        const resultArr: any[] = [];

        // 为每个样本按指定数量生成数据
        _sampleListData.forEach((sample: any, sampleIndex: any) => {
          const config = numberingConfigs[sampleIndex];
          // 获取当前样本的生成数量
          const numToGenerate = config.generateCount || 0;

          if (numToGenerate <= 0) {
            messageApi.warning(`请为${sample.name}设置生成数量`);
            setRandomlySampleLoading(false);
            return;
          }

          // 为每个被考核单位创建唯一的样本盒ID
          const sampleBoxIds = new Array(numToGenerate).fill(null).map(
            () =>
              // 生成数字ID格式的样本盒ID (时间戳 + 随机数)
              Date.now().toString() +
              Math.floor(Math.random() * 10000).toString()
          );

          // 确保qaTaskSampleItemVoList存在
          if (
            !sample.qaTaskSampleItemVoList ||
            !Array.isArray(sample.qaTaskSampleItemVoList)
          ) {
            sample.qaTaskSampleItemVoList = [];
          }

          // 创建测试项数据 - 这部分数据应该从之前已存在的保留
          // 这里只保留一个初始的测试项数据结构，实际使用时可能需要调整

          console.log(sample.name, 1111);

          let sampleName = sample.name;
          console.log(sample.name.indexOf('种样本') > -1);

          if (sample.name.indexOf('种样本') > -1) {
            sampleName = '';
          }

          if (sample.qaTaskSampleItemVoList.length === 0) {
            sample.qaTaskSampleItemVoList.push({
              assessedLabId: null,
              assessedLabCode: null,
              assessedLabName: null,
              itemName: sampleName,
              taskId: baseInfo?.id || null, // 使用baseInfo中的id或null
            });
          }

          console.log(_sampleListData, 1111);

          // 创建sampleVos数据 - 修复：确保每个样本实例有唯一的sampleCode
          sample.sampleVos = new Array(numToGenerate)
            .fill(null)
            .map((_, index) => {
              // 生成唯一编号，加上索引作为区分
              const uniqueCode = `${config.uniformCode}`;
              return {
                assessedLabId: null,
                assessedLabCode: null,
                assessedLabName: null,
                sampleBox: sampleBoxIds[index], // 每个样本都有唯一的sampleBox
                sampleCode: uniqueCode,
                // 添加一个额外的索引标记，确保即使相同编号也能区分不同样本
                sampleIndex: index,
              };
            });

          // 为每个样本项创建单独的表格行
          sample.sampleVos.forEach((item: any, itemIndex: number) => {
            // 生成唯一且稳定的key，只用于表格行的内部标识，不影响实际数据
            const uniqueKey = `${
              sample.name
            }_${itemIndex}_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 10)}`;

            // 将生成的 _key 同时保存到 sampleVos 中
            item._key = uniqueKey;

            // 构建表格行数据
            const rowData: any = {
              _key: uniqueKey, // 用于UI渲染和行标识
              rowIndex: itemIndex, // 保存原始索引，用于后续操作
              assessedLabId: null,
              assessedLabCode: null,
              assessedLabName: null,
              sampleBox: item.sampleBox,
              standardType: sample.name, // 仅表格显示用，不保存到数据结构中
              sampleCode: item.sampleCode, // 使用样本的编号
            };

            resultArr.push(rowData);
          });
        });

        setSampleListData(_sampleListData);
        setSampleNumList(resultArr); // 更新表格数据源
        setIsOpenGenerateSampleNumModal(false);
        setRandomlySampleLoading(false);
        messageApi.success('统一编号生成成功');
        return;
      }

      // 处理随机编号的情况

      const IsRandom = radioGroupValue === 'random';
      if (IsRandom) {
        try {
          // 构建请求参数 - 新的API格式
          const apiParams = Object.keys(numberingConfigs)
            .map((indexStr) => {
              const index = Number(indexStr);
              const config = numberingConfigs[index];
              const sample = sampleListData[index];
              console.log(config, sample, '1111');

              if (!config || !sample) return null;

              // 检查必填项
              if (!config.suffixDigits) {
                messageApi.warning(`请为${sample.name}设置后缀位数`);
                throw new Error('参数不完整');
              }

              if (!config.generateCount || config.generateCount <= 0) {
                messageApi.warning(`请为${sample.name}设置生成数量`);
                throw new Error('参数不完整');
              }

              return {
                label: sample.name,
                num: config.generateCount,
                digit: config.suffixDigits,
                prefix: config.prefix || '',
                type: 'random',
              };
            })
            .filter(Boolean);

          if (apiParams.length === 0) {
            messageApi.warning('请至少设置一个随机编号样本');
            setRandomlySampleLoading(false);
            return;
          }

          const params: any = {
            type: 'random',
            contents: apiParams,
          };

          // 调用新的API
          const { code, data, msg } = await randomForRandomTask(params);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            messageApi.error(msg || '生成编号失败');
            setRandomlySampleLoading(false);
            return;
          }

          if (!data || !Array.isArray(data) || data.length === 0) {
            messageApi.error('返回的数据格式不正确');
            setRandomlySampleLoading(false);
            return;
          }

          // 创建临时数据结构
          const _sampleListData = cloneDeep(sampleListData);
          const resultArr: any[] = [];

          // 按样本类型分组处理返回的数据
          const groupedData: Record<string, any[]> = {};

          // 将返回的数据按样本名称分组
          data.forEach((item: any, index) => {
            if (!groupedData[item.label]) {
              groupedData[item.label] = [];
            }
            groupedData[item.label].push(item);
            const uniqueKey = `${index}_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 10)}`;

            resultArr.push({
              _key: uniqueKey,
              // rowIndex: sample.sampleVos.length - 1, // 保存原始索引
              assessedLabId: null,
              assessedLabCode: null,
              assessedLabName: null,
              sampleBox: item.label,
              standardType: item.label, // 仅表格显示用，不保存到数据结构中
              sampleCode: item.sampleCode,
            });
          });

          Object.keys(numberingConfigs).forEach((indexStr) => {
            const index = Number(indexStr);
            const config = numberingConfigs[index];
            const sample = sampleListData[index];
            const count = config.generateCount;
            if (config.type === 'random') return null;
            Array(count)
              .fill(null)
              .forEach((_, i) => {
                if (!groupedData[sample.name]) {
                  groupedData[sample.name] = [];
                }
                groupedData[sample.name].push({
                  label: sample.name,
                  sampleCode: `${config.uniformCode}`,
                });
              });
          });

          // 为每个样本类型更新数据
          _sampleListData.forEach((sample: any, idx: any) => {
            const sampleCodes = groupedData[sample.name] || [];

            // 确保qaTaskSampleItemVoList存在
            if (
              !sample.qaTaskSampleItemVoList ||
              !Array.isArray(sample.qaTaskSampleItemVoList)
            ) {
              sample.qaTaskSampleItemVoList = [];
            }

            // 创建测试项数据 - 这部分数据应该从之前已存在的保留
            // 这里只保留一个初始的测试项数据结构，实际使用时可能需要调整
            if (
              sample.qaTaskSampleItemVoList.length === 0 &&
              sample.name.indexOf('种样本') === -1
            ) {
              sample.qaTaskSampleItemVoList.push({
                assessedLabId: null,
                assessedLabCode: null,
                assessedLabName: null,
                itemName: sample.name,
                taskId: baseInfo?.id || null, // 使用baseInfo中的id或null
              });
            }

            console.log(sample.qaTaskSampleItemVoList);

            // 创建sampleVos数据
            sample.sampleVos = [];

            // 添加新生成的编号数据
            sampleCodes.forEach((codeData: any, index: number) => {
              console.log(codeData, 'codeData');

              // 创建样本对象
              const sampleItem: any = {
                assessedLabId: null,
                assessedLabCode: null,
                assessedLabName: null,
                sampleBox:
                  Date.now().toString() +
                  Math.floor(Math.random() * 10000).toString(),
                sampleCode: codeData.sampleCode,
                // 添加索引标记，确保能够区分具有相同编号的不同样本
                sampleIndex: index,
              };

              // 添加到样本列表
              sample.sampleVos.push(sampleItem);
            });
          });

          // 更新状态
          setSampleListData(_sampleListData);
          setSampleNumList(resultArr);
          setIsOpenGenerateSampleNumModal(false);
          setRandomlySampleLoading(false);
          messageApi.success('样本编号生成成功');
        } catch (error) {
          setRandomlySampleLoading(false);
          messageApi.error('编号生成失败，请重试');
          console.error('随机生成编号失败:', error);
        }
      }

      // 处理自增编号的情况

      const IsCustomize = radioGroupValue === 'customize';
      if (IsCustomize) {
        try {
          // 构建请求参数 - 新的API格式
          const apiParams = Object.keys(numberingConfigs)
            .map((indexStr) => {
              const index = Number(indexStr);
              const config: any = numberingConfigs[index];
              const sample = sampleListData[index];
              console.log(config, sample, '1111');

              if (!config || !sample) return null;

              // 检查必填项
              if (!config.digit) {
                messageApi.warning(`请为${sample.name}设置起始编号`);
                throw new Error('参数不完整');
              }

              if (!config.generateCount || config.generateCount <= 0) {
                messageApi.warning(`请为${sample.name}设置生成数量`);
                throw new Error('参数不完整');
              }

              return {
                prefix: config.prefix || '',
                digit: config.digit || '',
                incrementCodeLengthType: config.incrementCodeLengthType || '0',
                incrementLength: config.incrementLength || '0',
                label: sample.name,
                referenceResult: sample.value || '',
                num: config.generateCount,
              };
            })
            .filter(Boolean);

          if (apiParams.length === 0) {
            messageApi.warning('请至少设置一个随机编号样本');
            setRandomlySampleLoading(false);
            return;
          }

          const params: any = {
            type: 'increment',
            contents: apiParams,
          };

          // 调用新的API
          const { code, data, msg } = await randomForRandomTask(params);

          if (code !== codeDefinition.QUERY_SUCCESS) {
            messageApi.error(msg || '生成编号失败');
            setRandomlySampleLoading(false);
            return;
          }

          if (!data || !Array.isArray(data) || data.length === 0) {
            messageApi.error('返回的数据格式不正确');
            setRandomlySampleLoading(false);
            return;
          }

          // 创建临时数据结构
          const _sampleListData = cloneDeep(sampleListData);
          const resultArr: any[] = [];

          // 按样本类型分组处理返回的数据
          const groupedData: any = {};

          // 将返回的数据按样本名称分组
          data.forEach((item: any, index) => {
            if (!groupedData[item.label]) {
              groupedData[item.label] = [];
            }
            groupedData[item.label].push(item);
            const uniqueKey = `${index}_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 10)}`;

            resultArr.push({
              _key: uniqueKey,
              // rowIndex: sample.sampleVos.length - 1, // 保存原始索引
              assessedLabId: null,
              assessedLabCode: null,
              assessedLabName: null,
              sampleBox: item.label,
              standardType: item.label, // 仅表格显示用，不保存到数据结构中
              sampleCode: item.sampleCode,
            });
          });

          Object.keys(numberingConfigs).forEach((indexStr) => {
            const index = Number(indexStr);
            const config = numberingConfigs[index];
            const sample = sampleListData[index];
            const count = config.generateCount;
            if (config.type === 'random') return null;
            Array(count)
              .fill(null)
              .forEach((_, i) => {
                if (!groupedData[sample.name]) {
                  groupedData[sample.name] = [];
                }
                groupedData[sample.name].push({
                  label: sample.name,
                  sampleCode: `${config.uniformCode}`,
                });
              });
          });

          // 为每个样本类型更新数据
          _sampleListData.forEach((sample: any, idx: any) => {
            const sampleCodes = groupedData[sample.name] || [];

            // 确保qaTaskSampleItemVoList存在
            if (
              !sample.qaTaskSampleItemVoList ||
              !Array.isArray(sample.qaTaskSampleItemVoList)
            ) {
              sample.qaTaskSampleItemVoList = [];
            }

            // 创建测试项数据 - 这部分数据应该从之前已存在的保留
            // 这里只保留一个初始的测试项数据结构，实际使用时可能需要调整
            if (
              sample.qaTaskSampleItemVoList.length === 0 &&
              sample.name.indexOf('种样本') === -1
            ) {
              sample.qaTaskSampleItemVoList.push({
                assessedLabId: null,
                assessedLabCode: null,
                assessedLabName: null,
                itemName: sample.name,
                taskId: baseInfo?.id || null, // 使用baseInfo中的id或null
              });
            }
            // 创建sampleVos数据
            sample.sampleVos = [];

            // 添加新生成的编号数据
            sampleCodes.forEach((codeData: any, index: number) => {
              console.log(codeData, 'codeData11111111');

              // 创建样本对象
              const sampleItem: any = {
                assessedLabId: null,
                assessedLabCode: null,
                assessedLabName: null,
                sampleBox:
                  Date.now().toString() +
                  Math.floor(Math.random() * 10000).toString(),
                sampleCode: codeData.sampleCode,
                // 添加索引标记，确保能够区分具有相同编号的不同样本
                sampleIndex: index,
              };

              // 添加到样本列表
              sample.sampleVos.push(sampleItem);
            });
          });

          // 更新状态
          setSampleListData(_sampleListData);
          setSampleNumList(resultArr);
          setIsOpenGenerateSampleNumModal(false);
          setRandomlySampleLoading(false);
          messageApi.success('样本编号生成成功');
        } catch (error) {
          setRandomlySampleLoading(false);
          messageApi.error('编号生成失败，请重试');
          console.error('随机生成编号失败:', error);
        }
      }
    } catch (error) {
      setRandomlySampleLoading(false);
      messageApi.error('保存任务失败，请重试');
      console.error('保存任务失败:', error);
    }
  };
  const handleConfigChangeAll = (field: string, value: any) => {
    setRadioGroupValue(value);
  };

  /**
   * 生成编号配置块的函数
   */
  const renderNumberingBlocks = () => {
    //  <Radio value="uniform">全部统一为一个编号</Radio>
    const RadioGroup = (
      <div className="w-full flex flex-row flex-nowrap gap-4 mb-4">
        <span className="mr-2">编号生成方式:</span>
        <Radio.Group
          value={radioGroupValue}
          onChange={(e) => handleConfigChangeAll('type', e.target.value)}
        >
          <Radio value="random">随机生成编号</Radio>
          <Radio value="customize">自增编号</Radio>
        </Radio.Group>
      </div>
    );
    const sampleList = sampleListData?.map((item: any, index: any) => {
      const config: any = numberingConfigs[index] || { type: 'uniform' };
      return (
        <div
          key={index}
          className="w-full p-4 border border-solid border-[#D9D9D9] rounded-md"
        >
          <div className="text-lg font-medium mb-2">{item.name}</div>
          <div className="text-red-500 text-sm mt-2">
            参考结果: {item.value || '暂无'}
          </div>
          <div className="my-3 flex items-center">
            <span className="text-red-500 mr-1">*</span>
            <span className="mr-2">生成数量:</span>
            <InputNumber
              style={{ width: '200px' }}
              min={1}
              value={config.generateCount}
              onChange={(value: number | null) =>
                handleConfigChange(index, 'generateCount', value || 0)
              }
              placeholder="请输入生成数量"
            />
          </div>

          {/* 根据类型显示不同配置 */}
          {radioGroupValue === 'uniform' && (
            <div className="w-full flex flex-row flex-nowrap items-center gap-2 mb-4">
              <span className="text-red-500 mr-1">*</span>
              <span className="mr-2">统一编号:</span>
              <Input
                value={config.uniformCode || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleConfigChange(index, 'uniformCode', e.target.value)
                }
                placeholder="请输入统一编号"
                style={{ width: '200px' }}
              />
            </div>
          )}

          {radioGroupValue === 'random' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[80px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[80px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>后缀位数:</span>
                </span>
                <Select
                  value={config.suffixDigits}
                  onChange={(value) =>
                    handleConfigChange(index, 'suffixDigits', value)
                  }
                  options={suffixNumList}
                  style={{ width: '200px' }}
                  placeholder="请选择后缀位数"
                />
                <span className="text-red-500 ml-2">示例: XJK001</span>
              </div>
            </div>
          )}

          {radioGroupValue === 'customize' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[80px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[80px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>起始编号:</span>
                </span>
                <Input
                  value={config.digit || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'digit', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  输入01则会从01开始编号
                </span>
              </div>

              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>固定编号长度:</span>
                </span>
                <Radio.Group
                  value={config.incrementCodeLengthType || '0'}
                  onChange={(e: any) => {
                    console.log('选中的值:', e.target.value); // 添加调试输出
                    handleConfigChange(
                      index,
                      'incrementCodeLengthType',
                      e.target.value
                    );
                  }}
                >
                  <Radio value="0">不固定长度</Radio>
                  <Radio value="1">固定长度</Radio>
                </Radio.Group>
                {config.incrementCodeLengthType === 1 && (
                  <Input
                    value={config.incrementLength || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleConfigChange(
                        index,
                        'incrementLength',
                        e.target.value
                      )
                    }
                    placeholder="请输入前缀"
                    style={{ width: '200px' }}
                  />
                )}
              </div>
              <div style={{ color: 'gray', fontSize: '12px' }}>
                设置固定长度后编号位数不够时会自动补0，补0是指在数字编号前添加零，使其达到固定位数的操作。
                例如固定3位长度，001 002 003 ... 100 101
              </div>
            </div>
          )}
        </div>
      );
    });

    return (
      <div>
        {RadioGroup}
        <div
          className="w-full flex-1 overflow-x-hidden overflow-y-auto pr-4  pd-10"
          style={{ height: '400px' }}
        >
          {sampleList}
        </div>
      </div>
    );
  };

  /**
   * @TODO 生成规则
   */
  const queryRulesObj = (rules: any) => {
    // 创建新的数据副本，避免直接修改状态
    const updatedSampleListData = [...sampleListData];

    // 只更新匹配当前设置的样本标识的样本
    const sampleIndex = updatedSampleListData.findIndex(
      (item) => item.name === currentSampleIdentifier
    );

    if (sampleIndex !== -1) {
      // 只更新找到的特定样本
      const targetSample = updatedSampleListData[sampleIndex];

      // 更新该样本中匹配的考核项目
      targetSample.qaTaskSampleItemVoList =
        targetSample.qaTaskSampleItemVoList.map((item: any) => {
          if (item.itemName === sampleDetails.itemName) {
            // 确保rules.rulesStorage已经是字符串
            const rulesStorage =
              typeof rules.rulesStorage === 'string'
                ? rules.rulesStorage
                : JSON.stringify(rules.rulesStorage);

            if (curSelectedStandardType === 'correct') {
              return {
                ...item,
                condition: rules.rules,
                conditionTwo: rulesStorage,
                conditionThree: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'suspicious') {
              return {
                ...item,
                conditionFour: rules.rules,
                conditionFive: rulesStorage,
                conditionSix: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'error') {
              return {
                ...item,
                conditionSeven: rules.rules,
                conditionEight: rulesStorage,
                conditionNine: rules.rulesView,
              };
            }
          }
          return item;
        });
    }

    // 更新状态
    setSampleListData(updatedSampleListData);
  };

  /**
   * @TODO 获取配置任务参数
   */
  const handleParams = async () => {
    try {
      const form2 = await formRefTask.current?.handleFormParams();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce(
        (promises: any, item: any) => {
          const formRef = listRefs.current[item.name];
          if (formRef) {
            promises.push(async () => {
              return formRef?.getFieldValue('value') ?? '';
            });
          }
          return promises;
        },
        [] as Array<() => Promise<any>>
      );
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        // throw new Error("请完善样品参考结果")
      }

      // 使用深拷贝确保不会互相影响
      const updatedSampleListData = cloneDeep(sampleListData);

      // 清空所有样本的 sampleVos 数组，然后只添加表格中存在的行
      updatedSampleListData.forEach((sample: any) => {
        // 先保存空数组
        sample.sampleVos = [];
      });

      console.log(updatedSampleListData, 'updatedSampleListData');

      // 只处理表格中显示的数据
      sampleNumList.forEach((row) => {
        const targetSample = updatedSampleListData.find(
          (sample: any) => sample.name === row.standardType
        );

        if (targetSample) {
          // 构建样本数据
          const sampleVoItem = {
            assessedLabId: row.assessedLabId,
            assessedLabCode: row.assessedLabCode,
            assessedLabName: row.assessedLabName,
            sampleBox:
              row.sampleBox ||
              `${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
            sampleCode: row.sampleCode,
            _key: row._key,
            // 添加行索引作为样本索引，确保能够区分具有相同编号的不同样本
            sampleIndex:
              row.rowIndex !== undefined
                ? row.rowIndex
                : targetSample.sampleVos.length,
          };

          // 添加到对应样本的 sampleVos 数组
          targetSample.sampleVos.push(sampleVoItem);
        }
      });

      // 确保没有重复数据
      updatedSampleListData.forEach((sample: any) => {
        if (sample.sampleVos && Array.isArray(sample.sampleVos)) {
          // 使用 Map 去重
          const uniqueMap = new Map();

          console.log(sample.sampleVos);

          sample.sampleVos.forEach((vo: any) => {
            // 修改：不再只根据sampleCode过滤，改为使用sampleCode、sampleBox和sampleIndex组合作为唯一标识
            // 这样即使多行有相同的sampleCode，只要sampleBox或sampleIndex不同，就会作为不同记录保留
            if (vo) {
              // 使用样本编号、样本盒以及索引组合作为唯一键
              const key = `${vo.sampleCode}_${vo.sampleBox}_${
                vo.sampleIndex !== undefined ? vo.sampleIndex : ''
              }`;
              uniqueMap.set(key, vo);
            }
          });

          // 更新为去重后的数组
          sample.sampleVos = Array.from(uniqueMap.values());
        }
      });

      // 处理数据
      const params = {
        ...form2,
        qaTaskSampleVos: updatedSampleListData,
      };

      return params;
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 获取配置任务参数
   */
  const handleValidateFields = async () => {
    try {
      await formRefTask.current?.handleValidateFields();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce(
        (promises: any, item: any) => {
          const formRef = listRefs.current[item.name];
          if (formRef) {
            promises.push(async () => {
              await formRef?.validateFields();
              return formRef?.getFieldValue('value') ?? '';
            });
          }
          return promises;
        },
        [] as Array<() => Promise<any>>
      );
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }

      // 校验所有样品是否绑定样品编码
      const isEnty = sampleListData.some(
        (item: any) => !item.sampleVos?.length
      );
      if (isEnty) {
        throw new Error('有样本还未分配样本编号，请随机生成样本编号');
      }

      // 注释掉或修改对被考核单位的强制校验
      // 下面的校验逻辑改为仅提示警告而不阻止保存
      let hasUnselectedLab = false;
      const unselectedSampleTypes: string[] = [];

      // 遍历所有表格数据检查是否有未选择被考核单位的行
      sampleNumList.forEach((row) => {
        if (!row.assessedLabId) {
          hasUnselectedLab = true;
          if (!unselectedSampleTypes.includes(row.standardType)) {
            unselectedSampleTypes.push(row.standardType);
          }
        }
      });

      if (hasUnselectedLab) {
        // 不再抛出错误，而是仅在控制台记录警告信息
        const unselectedTypes = unselectedSampleTypes.join('、');
        console.warn(
          `${unselectedTypes}中有样本未选择被考核单位，但仍允许保存`
        );
        // 可以使用messageApi.warning进行警告提示但不阻止保存
        messageApi.warning(
          `${unselectedTypes}中有样本未选择被考核单位，保存后请记得选择被考核单位`
        );
      }

      // 使用深拷贝确保不会互相影响
      const updatedSampleListData = cloneDeep(sampleListData);

      // 清空所有样本的 sampleVos 数组，然后只添加表格中存在的行
      updatedSampleListData.forEach((sample: any) => {
        // 先保存空数组
        sample.sampleVos = [];
      });

      // 只处理表格中显示的数据
      sampleNumList.forEach((row) => {
        const targetSample = updatedSampleListData.find(
          (sample: any) => sample.name === row.standardType
        );

        if (targetSample) {
          // 构建样本数据
          const sampleVoItem = {
            assessedLabId: row.assessedLabId,
            assessedLabCode: row.assessedLabCode,
            assessedLabName: row.assessedLabName,
            sampleBox:
              row.sampleBox ||
              `${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
            sampleCode: row.sampleCode,
            _key: row._key,
            // 添加行索引作为样本索引，确保能够区分具有相同编号的不同样本
            sampleIndex:
              row.rowIndex !== undefined
                ? row.rowIndex
                : targetSample.sampleVos.length,
          };

          // 添加到对应样本的 sampleVos 数组
          targetSample.sampleVos.push(sampleVoItem);
        }
      });

      // 确保没有重复数据
      updatedSampleListData.forEach((sample: any) => {
        if (sample.sampleVos && Array.isArray(sample.sampleVos)) {
          // 使用 Map 去重
          const uniqueMap = new Map();

          sample.sampleVos.forEach((vo: any) => {
            // 修改：不再只根据sampleCode过滤，改为使用sampleCode、sampleBox和sampleIndex组合作为唯一标识
            // 这样即使多行有相同的sampleCode，只要sampleBox或sampleIndex不同，就会作为不同记录保留
            if (vo) {
              // 使用样本编号、样本盒以及索引组合作为唯一键
              const key = `${vo.sampleCode}_${vo.sampleBox}_${
                vo.sampleIndex !== undefined ? vo.sampleIndex : ''
              }`;
              uniqueMap.set(key, vo);
            }
          });

          // 更新为去重后的数组
          sample.sampleVos = Array.from(uniqueMap.values());
        }
      });

      // 更新样本列表数据
      setSampleListData(updatedSampleListData);
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  useEffect(() => {
    // 初始样本列表
    const qaTaskSampleVosNew = cloneDeep(baseInfo.qaTaskSampleVos);
    let newSampleListData: any = [];
    if (qaTaskSampleVosNew) {
      qaTaskSampleVosNew.forEach((item: any) => {
        item.qaTaskSampleItemVoList.forEach((item2: any) => {
          item2.code = item.name;
        });
      });
      newSampleListData = qaTaskSampleVosNew;
    } else {
      newSampleListData = tableData;
    }
    setSampleListData([...newSampleListData]);
  }, []);

  useEffect(() => {
    const sampleListDataNew = cloneDeep(sampleListData);
    if (sampleListDataNew?.length) {
      sampleListDataNew?.forEach((_item: any) => {
        let CopyQaTaskSampleItemVoList = cloneDeep(
          _item?.qaTaskSampleItemVoList
        );
        const checked = assessmentCheckBox
          ?.filter((Item) => Item.check === 1)
          ?.map((_item2: any) => {
            let Item = {
              code: _item.name, // 样本编号
              itemName: _item2.name,
              ratio: _item2.ratio,
              condition: '',
              conditionTwo: '',
              conditionThree: '',
            };
            // 保留映射原有已选规则
            if (CopyQaTaskSampleItemVoList.length) {
              CopyQaTaskSampleItemVoList?.forEach((_item3: any) => {
                if (_item3.itemName === _item2.name) {
                  Item = {
                    ...Item,
                    condition: _item3.condition ?? '',
                    conditionTwo: _item3.conditionTwo ?? '',
                    conditionThree: _item3.conditionThree ?? '',
                  };
                }
              });
            }
            return Item;
          });
        _item.qaTaskSampleItemVoList = checked;
      });
      setSampleListData([...sampleListDataNew]);
    }
  }, [assessmentCheckBox]);

  useEffect(() => {
    // 随机编号表头
    const header = sampleListData.map((item: any, idx: any) => {
      return {
        title: item.name,
        dataIndex: item.name,
        key: item.name,
        hideInSearch: true,
      };
    });
    // 初始样本列表
    setSampleNumHeader([...header]);
  }, [sampleListData]);

  useEffect(() => {
    // 初始化随机生成编码表格
    if (baseInfo?.qaTaskSampleVos?.length) {
      const tempMap: any = {};
      const tableData: any[] = [];

      // 处理每个样本类型
      baseInfo.qaTaskSampleVos?.forEach((item: any) => {
        item.sampleVos?.forEach((sampleVo: any, index: number) => {
          // 跳过null或undefined的sampleVo
          if (!sampleVo) return;

          // 使用可选链操作符安全访问assessedLabId
          const assessedLabId = sampleVo?.assessedLabId;

          // 向表格数据中添加一行，包含标准品类别(standardType)
          tableData.push({
            _key: `${item.name}_${index}_${sampleVo.sampleCode}`,
            assessedLabId: assessedLabId,
            assessedLabCode: sampleVo?.assessedLabCode || null,
            assessedLabName: sampleVo?.assessedLabName || null,
            sampleBox: sampleVo?.sampleBox || '',
            standardType: item.name, // 从样本项的name属性获取标准品类别
            sampleCode: sampleVo?.sampleCode || '',
          });

          // 跳过没有assessedLabId的记录(仅用于构建tempMap)
          if (!assessedLabId) return;

          // 如果实验室ID已在映射表中，则更新样本信息
          if (tempMap[assessedLabId]) {
            tempMap[assessedLabId][item.name] = sampleVo.sampleCode;
          } else {
            // 否则将实验室信息和当前样本信息放入映射表
            tempMap[assessedLabId] = {
              sampleBox: sampleVo.sampleBox,
              [item.name]: sampleVo.sampleCode,
            };
          }
        });
      });

      setSampleNumList(tableData); // 更新表格数据
      setSampleListData(baseInfo.qaTaskSampleVos); // 更新样本列表数据
    }
  }, [baseInfo]);

  useEffect(() => {
    // 参考结果赋值
    if (sampleListData?.length) {
      sampleListData.forEach((item: any) => {
        if (listRefs.current[item.name]) {
          listRefs.current[item.name]?.setFieldValue('value', item.value);
          listRefs.current[item.name]?.setFieldValue('realCode', item.realCode);
        }
      });
    }
  }, [sampleListData]);

  useEffect(() => {
    // 安全地调用 setRefType，确保它是一个函数
    if (typeof setRefType === 'function') {
      setRefType(1);
    }
    if (id) {
      // TODO: 在这里获取详情
    }
  }, [id]);

  const props: UploadProps = {
    action: import.meta.env.VITE_URL + '/qa/tasks/importSampleCode',
    headers: { Authorization: 'Bearer ' + token },
    data: { taskId: baseInfo?.id },
    showUploadList: false,
    onChange: async ({ file }) => {
      if (file.status === 'uploading') {
        setUploadLoading(true);
      }
      if (file.status === 'done') {
        setUploadLoading(false);
        if (file.response?.code === 200) {
          try {
            const result = await getDetailData();
            // 类型断言
            const typedResult = result as unknown as TaskDetailData;

            if (typedResult?.qaTaskSampleVos) {
              // 手动更新表格数据
              const tableData: any[] = [];

              // 创建深拷贝以避免引用问题
              const updatedSampleVos = cloneDeep(typedResult.qaTaskSampleVos);

              // 处理每个样本类型
              updatedSampleVos.forEach((item) => {
                if (Array.isArray(item.sampleVos)) {
                  // 处理每个样本
                  item.sampleVos.forEach((sampleVo, index) => {
                    if (!sampleVo) return;

                    // 确保样本有盒ID
                    if (!sampleVo.sampleBox) {
                      sampleVo.sampleBox = `${Date.now()}_${Math.random()
                        .toString(36)
                        .substring(2, 10)}`;
                    }

                    // 为表格数据生成行
                    tableData.push({
                      _key: `${item.name}_${index}_${Date.now()}`,
                      assessedLabId: sampleVo.assessedLabId,
                      assessedLabCode: sampleVo.assessedLabCode,
                      assessedLabName: sampleVo.assessedLabName,
                      sampleBox: sampleVo.sampleBox,
                      standardType: item.name,
                      sampleCode: sampleVo.sampleCode,
                    });
                  });
                }
              });

              setSampleNumList(tableData);
              setSampleListData(updatedSampleVos);
              messageApi.success('上传成功');
            }
          } catch (error) {
            console.error('更新数据失败:', error);
            messageApi.error('更新数据失败，请重试');
          }
        } else {
          messageApi.error(file.response?.msg || '上传失败');
        }
      } else if (file.status === 'error') {
        setUploadLoading(false);
        messageApi.error('上传失败，请重试');
      }
    },
    capture: undefined,
  };

  /**
   * 打开被考核单位选择弹窗
   */
  const openAssessedLabModal = (record: any, index: number) => {
    setCurrentRow(record);
    // 保存当前行索引
    setCurrentRowIndex(index);
    setAssessedLabModalVisible(true);
  };

  /**
   * 处理被考核单位选择
   */
  const handleSelectAssessedLab = (selectedLab: any) => {
    if (!currentRow) {
      messageApi.warning('请选择一个被考核单位');
      return;
    }

    // 为每个被考核单位和样本编号组合创建唯一的样本盒ID
    const uniqueSampleBoxId = `${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 10)}`;

    // 创建表格数据副本
    const updatedSampleNumList = [...sampleNumList];

    // 如果索引有效，则直接更新对应索引的行
    if (currentRowIndex >= 0 && currentRowIndex < updatedSampleNumList.length) {
      // 更新当前行数据
      updatedSampleNumList[currentRowIndex] = {
        ...updatedSampleNumList[currentRowIndex],
        assessedLabId: selectedLab.id,
        assessedLabCode: selectedLab.labCode,
        assessedLabName: selectedLab.labName,
        deptId: selectedLab.deptId,
        sampleBox: uniqueSampleBoxId,
      };
    } else {
      messageApi.warning('未找到当前行索引，请重试');
      return;
    }

    // 获取当前行数据，此时已更新被考核单位信息
    const currentTableRow = updatedSampleNumList[currentRowIndex];

    // 使用深拷贝确保不会互相影响
    const updatedSampleListData = cloneDeep(sampleListData);

    // 在样本数据中查找对应的样本类型
    const targetSample = updatedSampleListData.find(
      (sample: any) => sample.name === currentTableRow.standardType
    );

    if (targetSample && targetSample.sampleVos) {
      // 首先尝试通过_key进行精确匹配
      let matchingVoIndex = -1;

      if (currentTableRow._key) {
        matchingVoIndex = targetSample.sampleVos.findIndex(
          (vo: any) => vo._key === currentTableRow._key
        );
      }

      // 如果没有找到匹配的_key，则尝试通过sampleCode匹配（作为后备方案）
      if (matchingVoIndex === -1) {
        matchingVoIndex = targetSample.sampleVos.findIndex(
          (vos: any) => vos.sampleCode === currentTableRow.sampleCode
        );
      }

      // 如果找到匹配的样本数据，更新它的被考核单位信息
      if (matchingVoIndex !== -1) {
        // 更新对应位置的样本数据
        targetSample.sampleVos[matchingVoIndex] = {
          ...targetSample.sampleVos[matchingVoIndex],
          assessedLabId: selectedLab.id,
          assessedLabCode: selectedLab.labCode,
          assessedLabName: selectedLab.labName,
          sampleBox: uniqueSampleBoxId,
          _key: currentTableRow._key, // 保存表格行的_key到样本数据中以便后续匹配
        };
      } else {
        console.warn(
          `未找到匹配的样本数据: ${currentTableRow.standardType}, sampleCode: ${currentTableRow.sampleCode}`
        );
      }

      // 更新表格中所有相同样本编号和被考核单位的行
      // 这确保在表格视图中保持一致性
      for (let i = 0; i < updatedSampleNumList.length; i++) {
        const row = updatedSampleNumList[i];

        // 如果找到相同样本类型、样本编号和被考核单位的行，更新它的sampleBox
        // 这确保在用户界面上看到的数据保持一致
        if (
          row.standardType === currentTableRow.standardType &&
          row.sampleCode === currentTableRow.sampleCode &&
          row.assessedLabId === selectedLab.id
        ) {
          updatedSampleNumList[i] = {
            ...row,
            sampleBox: uniqueSampleBoxId,
          };
        }
      }
    } else {
      console.warn(`未找到匹配的样本类型: ${currentTableRow.standardType}`);
    }

    // 判断 updatedSampleNumList 中是否有重复的 deptId
    const deptIds = updatedSampleNumList
      .filter((item) => item.deptId !== undefined) // 排除 deptId 未定义的情况
      .map((item) => item.deptId);
    const uniqueDeptIds = new Set(deptIds);
    const hasDuplicateDeptId = deptIds.length !== uniqueDeptIds.size;

    // if (hasDuplicateDeptId) {
    //   message.warning('机构名称不能重复添加');
    //   return;
    // }

    // 更新状态
    setSampleNumList(updatedSampleNumList);
    setSampleListData(updatedSampleListData);
    setAssessedLabModalVisible(false);
    // 重置当前行索引
    setCurrentRowIndex(-1);
    messageApi.success('被考核单位选择成功');
  };

  const handleSelectlMultipleAssessedLab = (selectedLab: any) => {
    const updatedSampleNumList: any = [];

    const selectedLabCopy = [...selectedLab];

    sampleNumList.forEach((item, index) => {
      if (selectedRowKeys.includes(item._key) && selectedLab.length > 0) {
        updatedSampleNumList.push({
          ...item,
          assessedLabId: selectedLab[0].id,
          assessedLabCode: selectedLab[0].labCode,
          assessedLabName: selectedLab[0].labName,
          deptId: selectedLab[0].deptId,
          sampleBox: item.sampleBox, // 保留原样本盒ID作为组标识
        });
        selectedLab.shift();
      } else {
        updatedSampleNumList.push({
          ...item,
        });
      }
    });

    // 存储匹配项的索引
    let matchingIndices: { sampleListIndex: number; sampleVosIndex: number }[] =
      [];

    // 遍历 sampleListData
    sampleListData.forEach((sample: any, sampleListIndex: any) => {
      if (sample.sampleVos) {
        // 遍历 sampleVos 数组
        sample.sampleVos.forEach((sampleVo: any, sampleVosIndex: any) => {
          const sampleBoxValue = sampleVo.sampleBox;
          // 检查 sampleBox 的值是否部分包含在 selectedRowKeys 中每个值 _ 之前的那段字段里
          const isMatch: any = selectedRowKeys.some((rowKey: any) => {
            const beforeUnderscore = rowKey.split('_')[0];
            return beforeUnderscore.includes(sampleBoxValue);
          });

          if (isMatch) {
            // 记录匹配项的索引
            matchingIndices.push({
              sampleListIndex,
              sampleVosIndex,
            });
          }
        });
      }
    });

    console.log(
      matchingIndices.length / sampleListData.length !== selectedLabCopy.length
    );

    if (
      matchingIndices.length / sampleListData.length !==
      selectedLabCopy.length
    ) {
      // 计算匹配索引数量与样本列表长度的商
      const matchRatio = matchingIndices.length / sampleListData.length;
      // 计算差值
      const diff =
        matchingIndices.length -
        (matchRatio - selectedLabCopy.length) * sampleListData.length;
      console.log(diff, 'diff');

      matchingIndices = matchingIndices.slice(0, diff);
    }

    const newselectedLabCopy: any = selectedLabCopy.flatMap((item) =>
      Array.from({ length: sampleListData.length }, () => item)
    );

    console.log(matchingIndices, 'matchingIndices');

    const updatedSampleListData = sampleListData.map(
      (sample: any, index1: any) => {
        if (sample.sampleVos) {
          sample.sampleVos.forEach((element: any, index2: any) => {
            // 判断 index1 和 index2 是否存在于 matchingIndices 中
            const isMatchIndex = matchingIndices.some(
              ({ sampleListIndex, sampleVosIndex }) => {
                return sampleListIndex === index1 && sampleVosIndex === index2;
              }
            );

            if (isMatchIndex) {
              sample.sampleVos[index2] = {
                assessedLabId: newselectedLabCopy[0].id,
                assessedLabCode: newselectedLabCopy[0].labCode,
                assessedLabName: newselectedLabCopy[0].labName,
                deptId: newselectedLabCopy[0].deptId,
                sampleBox: element.sampleBox, // 保留原样本盒ID作为组标识
              };
              newselectedLabCopy.shift();
            }
          });
        }

        return sample;
      }
    );

    setSampleNumList(updatedSampleNumList);
    setSampleListData(updatedSampleListData);
    setAssessedLabModalMultipleVisible(false);

    message.success('被考核单位选择成功');
  };

  /**
   * 打开质量控制点选择弹窗
   */
  const openQcPointModal = (record: any) => {
    // 如果质量控制点列表为空，先获取列表
    if (qcPointList.length === 0) {
      getQcPointList();
    }

    setCurrentRow(record);
    // 重置选中状态和搜索状态
    setSelectedQcPointId('');
    setQcPointSearchKeyword('');
    setFilteredQcPointList(qcPointList);
    setQcPointModalVisible(true);
  };

  /**
   * 获取质量控制点列表
   */
  const getQcPointList = async () => {
    try {
      // 这里替换为实际的API调用
      // 模拟数据，实际项目中替换为真实API调用
      const mockData = [
        { id: '1', pointCode: 'QC001', pointName: '温度控制点' },
        { id: '2', pointCode: 'QC002', pointName: '湿度控制点' },
        { id: '3', pointCode: 'QC003', pointName: 'pH值控制点' },
        { id: '4', pointCode: 'QC004', pointName: '设备校准点' },
        { id: '5', pointCode: 'QC005', pointName: '样品前处理点' },
      ];

      // 格式化数据
      const formattedList = mockData.map((item: any) => ({
        id: item.id,
        pointId: item.id,
        pointCode: item.pointCode || '',
        pointName: item.pointName || '',
      }));

      setQcPointList(formattedList);
      setFilteredQcPointList(formattedList);
    } catch (error) {
      console.error('获取质量控制点列表失败:', error);
    }
  };

  /**
   * 处理质量控制点搜索关键词变化
   */
  const handleQcPointSearchChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const keyword = e.target.value;
    setQcPointSearchKeyword(keyword);

    // 根据关键词过滤质量控制点列表
    if (keyword) {
      const filtered = qcPointList.filter(
        (point) =>
          point.pointName.toLowerCase().includes(keyword.toLowerCase()) ||
          point.pointCode.toLowerCase().includes(keyword.toLowerCase())
      );
      setFilteredQcPointList(filtered);
    } else {
      setFilteredQcPointList(qcPointList);
    }
  };

  /**
   * 处理质量控制点选择变化
   */
  const handleQcPointSelectionChange = (pointId: string) => {
    setSelectedQcPointId(pointId);
  };

  /**
   * 选择质量控制点
   */
  const handleSelectQcPoint = () => {
    if (!currentRow || !selectedQcPointId) {
      messageApi.warning('请选择一个质量控制点');
      return;
    }

    // 获取选中的质量控制点
    const selectedPoint = qcPointList.find(
      (point) => point.id === selectedQcPointId
    );
    if (!selectedPoint) {
      messageApi.warning('所选质量控制点不存在');
      return;
    }

    // 更新表格中当前行
    const updatedSampleNumList = sampleNumList.map((item) => {
      if (item._key === currentRow._key) {
        return {
          ...item,
          qcPointId: selectedPoint.id,
          qcPointCode: selectedPoint.pointCode,
          qcPointName: selectedPoint.pointName,
        };
      }
      return item;
    });

    // 更新样本数据中的质量控制点信息
    const updatedSampleListData = sampleListData.map((sample: any) => {
      // 检查当前行是否包含此样本的编号
      if (sample.name === currentRow.standardType && sample.sampleVos) {
        // 找到相应的样本项
        const matchingVosIndex = sample.sampleVos.findIndex(
          (vos: any) =>
            vos.sampleCode === currentRow.sampleCode ||
            (vos.sampleBox && vos.sampleBox === currentRow.sampleBox)
        );

        if (matchingVosIndex >= 0) {
          // 更新样本数据
          sample.sampleVos[matchingVosIndex] = {
            ...sample.sampleVos[matchingVosIndex],
            qcPointId: selectedPoint.id,
            qcPointCode: selectedPoint.pointCode,
            qcPointName: selectedPoint.pointName,
          };
        }
      }
      return sample;
    });

    // 更新状态
    setSampleNumList([...updatedSampleNumList]);
    setSampleListData([...updatedSampleListData]);

    // 关闭弹窗
    setQcPointModalVisible(false);
    messageApi.success('质量控制点关联成功');

    // 创建一个临时变量强制组件重新渲染
    setTimeout(() => {
      setSampleNumList([...updatedSampleNumList]);
    }, 0);
  };

  // 添加选择行变更处理函数
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 添加删除选中行的处理函数
  const handleDeleteSelectedRows = () => {
    setDeleteLoading(true);

    try {
      // 创建一个映射，记录要删除的行标识
      const deleteKeysMap = selectedRowKeys.reduce((map, key) => {
        map[key.toString()] = true;
        return map;
      }, {} as Record<string, boolean>);

      // 删除选中的行，使用与表格一致的行标识方式
      const newSampleNumList = sampleNumList.filter((item) => {
        // 使用与表格相同的行标识逻辑
        const rowKey =
          item._key ||
          item.assessedLabId ||
          Math.random().toString(36).slice(2);
        return !deleteKeysMap[rowKey.toString()];
      });

      // 更新表格数据
      setSampleNumList(newSampleNumList);

      // 更新样本数据，从每个样本的 sampleVos 中删除对应的项
      const updatedSampleListData = cloneDeep(sampleListData);
      updatedSampleListData.forEach((sample: any) => {
        if (sample.sampleVos && sample.sampleVos.length > 0) {
          // 过滤掉被删除的行
          sample.sampleVos = sample.sampleVos.filter((sampleVo: any) => {
            // 使用相同的行标识逻辑
            const voKey = sampleVo._key || sampleVo.assessedLabId || '';
            return !deleteKeysMap[voKey.toString()];
          });
        }
      });

      setSampleListData(updatedSampleListData);

      // 清空选中行
      setSelectedRowKeys([]);

      // 添加成功消息提示
      messageApi.success('删除成功');

      // 通过延时更新触发强制重新渲染
      setTimeout(() => {
        setSampleNumList([...newSampleNumList]);
        setSampleListData([...updatedSampleListData]);
      }, 0);
    } catch (error) {
      messageApi.error('删除失败，请重试');
      console.error('删除失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 设置行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <>
      {contextHolder}
      {/* 任务配置 */}
      {!isUpdateJudgCriteria ? (
        <div className="mt-4">
          <TaskConfiguration
            onRef={formRefTask}
            setFormInfo={(val) => setAssessmentCheckBox(val)}
            taskConfigurationForm={baseInfo}
          />
        </div>
      ) : null}
      {/* 明细配置 */}
      <div className="mt-4">
        <BlockContainer title="明细配置" bodyStyle={{ padding: '20px' }}>
          <Row gutter={[35, 25]}>
            {sampleListData?.map((item: SampleData, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                  extra={
                    sampleListData.length > 1 ? (
                      <Popconfirm
                        title="删除此样本？"
                        onConfirm={() => handleDeleteSampleItem(item.name)}
                        okText="确定"
                        cancelText="取消"
                        key="delpro"
                      >
                        <a className="text-[red]"> 删除</a>
                      </Popconfirm>
                    ) : null
                  }
                >
                  <div className="w-full h-[300px] overflow-y-auto overflow-x-hidden">
                    <ProForm<{ table: any[] }>
                      formRef={
                        ((ref: any): void =>
                          (listRefs.current[item.name] = ref)) as any
                      }
                      {...formItemLayout}
                      layout="horizontal"
                      className="w-full"
                      grid={true}
                      submitter={false}
                      autoFocusFirstInput={false}
                      onValuesChange={(
                        _: Record<string, any>,
                        values: Record<string, any>
                      ) => {
                        if (values.value || values.realCode) {
                          sampleListData.forEach((Items: any) => {
                            console.log(Items.name, item.name, values);
                            if (Items.name === item.name && values.value) {
                              Items.value = values.value;
                            }
                            if (Items.name === item.name && values.realCode) {
                              Items.realCode = values.realCode;
                            }
                          });
                          setSampleListData([...sampleListData]);
                        }
                      }}
                    >
                      <ProFormTextArea
                        readonly={readonlyAll}
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        label="参考结果"
                        rules={[
                          { required: true, message: '参考结果为必填项' },
                        ]}
                      />
                      <ProFormText
                        readonly={readonlyAll}
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="realCode"
                        label="真实编号"
                      />
                      <div className="real-code-tips">被考核机构不可见</div>

                      {item?.qaTaskSampleItemVoList[0]?.itemName !== '' && (
                        <ProTable
                          key="itemName"
                          rowKey={(record) =>
                            record._key ||
                            record.assessedLabId ||
                            Math.random().toString(36).slice(2)
                          }
                          name="qaTaskSampleItemVoList"
                          className="w-full px-1"
                          toolBarRender={false}
                          options={false}
                          search={false}
                          bordered
                          columns={columns}
                          dataSource={item?.qaTaskSampleItemVoList}
                          pagination={false}
                          onChange={(newDataSource) => {
                            console.log(newDataSource);
                          }}
                        />
                      )}
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
            <Col xs={24} sm={8} md={8}>
              <BlockContainer
                title={`第${sampleListData.length + 1}种样本`}
                className="border-[#D9D9D9]"
              >
                <div className="h-[300px] flex justify-center items-center">
                  <Tooltip title="点击添加样本">
                    <PlusCircleOutlined
                      className="text-[50px] text-[#A8A8A8] cursor-pointer"
                      onClick={handleAddSampleItem}
                    />
                  </Tooltip>
                </div>
              </BlockContainer>
            </Col>
          </Row>
          {!isUpdateJudgCriteria ? (
            <BlockContainer className="mt-6">
              <ProTable
                key="_key"
                rowKey={(record) =>
                  record._key ||
                  record.assessedLabId ||
                  Math.random().toString(36).slice(2)
                }
                className="w-full"
                options={false}
                search={false}
                bordered
                rowSelection={rowSelection}
                columns={columns2}
                dataSource={sampleNumList}
                pagination={false}
                headerTitle={
                  <div className="w-full">
                    <div className="w-full flex flex-row flex-nowrap gap-4">
                      <Button
                        type="primary"
                        loading={randomlySampleLoading}
                        onClick={() => setIsOpenGenerateSampleNumModal(true)}
                      >
                        随机生成样本编号
                      </Button>

                      <Button
                        onClick={handleOpenAssessedLabModal}
                        type="default"
                        disabled={selectedRowKeys.length === 0}
                      >
                        选择被考核单位
                      </Button>

                      <Button
                        onClick={handleExport}
                        loading={loading}
                        icon={<DownloadOutlined />}
                        type="default"
                      >
                        导出样本清单
                      </Button>
                      <Upload {...props}>
                        <Button
                          type="primary"
                          loading={uploadLoading}
                          disabled={!sampleNumList?.length}
                          title={
                            !sampleNumList?.length ? '样本随机编号列表为空' : ''
                          }
                        >
                          使用excel更新被考核单位
                        </Button>
                      </Upload>
                      <Popconfirm
                        title="确认删除所选记录?"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={handleDeleteSelectedRows}
                      >
                        <Button
                          danger
                          type="primary"
                          disabled={selectedRowKeys.length === 0}
                          loading={deleteLoading}
                        >
                          删除所选
                        </Button>
                      </Popconfirm>
                    </div>
                    <div className="w-full mt-4 text-sm text-red-500">
                      温馨提示：如您已经完成样本编号粘贴工作，可通过【导出样本清单】功能，将下方列表数据导出为excel，在excel中填写实际样本编号，最后通过【使用excel更新样本编号】导入系统。
                    </div>
                  </div>
                }
              />
            </BlockContainer>
          ) : null}
        </BlockContainer>
        {/* 标准判定弹窗 */}
        <StandardMaintenance
          close={() => {
            setSampleDetails(null);
            setStandardModel(false);
          }}
          open={standardModel}
          getRuler={queryRulesObj}
          rulesDetails={sampleDetails}
          taskId={baseInfo.id}
          taskType={baseInfo.assessmentType}
          curSelectedStandardType={curSelectedStandardType}
          onStandardTypeChange={(type) => setCurSelectedStandardType(type)}
        />
        {/* 考核样编号生成规则弹窗 */}
        <Modal
          open={isOpenGenerateSampleNumModal}
          onCancel={() => setIsOpenGenerateSampleNumModal(false)}
          title="考核样编号生成规则"
          centered
          width={800}
          footer={
            <Button
              type="primary"
              onClick={() => {
                handleRandomlySampleNumV2();
              }}
            >
              开始生成编号
            </Button>
          }
        >
          <div className="w-full max-h-[70vh] overflow-hidden flex flex-col gap-4">
            <div className="bg-[#fff7f7] p-4 border border-solid border-[#ffccc7] rounded-md">
              <div className="text-[#f5222d] font-bold">
                【非常重要】温馨提示:{' '}
              </div>
              <div className="mt-2">
                1、使用"全部统一为一个编号"，所有被考核单位对应种类的样本均为该编号。例如：设置第1种样本统一编号为A01，则所有被考核单位第1种样本编号都将为A01；
              </div>
              <div className="mt-1">
                2、使用"随机生成编号"，编号前缀可不填，后缀随机数位数最多支持5位。例如：前缀设置为XJK，随机数选择3，则对应种类的考核样编号将为XJK001、XJK002...
              </div>
              <div className="mt-1">
                3、请注意：设置的"生成数量"即为表格中将显示的记录行数，每行代表一个样本。
              </div>
            </div>
            <div className="w-full flex-1 overflow-x-hidden overflow-y-auto">
              {renderNumberingBlocks()}
            </div>
          </div>
        </Modal>
      </div>
      {/* 被考核单位选择弹窗 */}
      <AssessedLabModal
        open={assessedLabModalVisible}
        onCancel={() => setAssessedLabModalVisible(false)}
        onSelect={handleSelectAssessedLab}
        currentRow={currentRow}
      />

      {/* 批量被考核单位选择弹窗 */}
      <AssessedLabModalMultiple
        open={assessedLabModalMultipleVisible}
        onCancel={() => setAssessedLabModalMultipleVisible(false)}
        onSelect={handleSelectlMultipleAssessedLab}
        currentRow={setCurrentMultipleRow}
        selectLength={selectedRowKeys.length}
        sampleNumList={sampleNumList}
      />

      {/* 选择质量控制点的弹窗 */}
      <Modal
        title="关联质量控制点"
        open={qcPointModalVisible}
        onCancel={() => setQcPointModalVisible(false)}
        onOk={handleSelectQcPoint}
        width={1000}
        styles={{
          body: {
            maxHeight: '60vh',
            overflow: 'auto',
          },
        }}
        destroyOnClose
      >
        <div className="mb-4 bg-gray-50 p-3">
          <Input.Search
            placeholder="搜索控制点名称或代码"
            onChange={handleQcPointSearchChange}
            style={{ width: '100%' }}
            value={qcPointSearchKeyword}
          />
        </div>
        <div style={{ maxHeight: '400px', overflow: 'auto' }}>
          <Radio.Group
            onChange={(e) => handleQcPointSelectionChange(e.target.value)}
            value={selectedQcPointId}
          >
            <Row gutter={[16, 16]}>
              {filteredQcPointList.length > 0 ? (
                filteredQcPointList.map((point) => (
                  <Col key={point.id} span={8}>
                    <div className="border rounded p-2 hover:bg-gray-50">
                      <Radio value={point.id}>
                        <div className="flex items-center">
                          <span className="font-medium">{point.pointCode}</span>
                          <span
                            className="ml-2 text-sm truncate"
                            title={point.pointName}
                          >
                            {point.pointName || '未命名控制点'}
                          </span>
                        </div>
                      </Radio>
                    </div>
                  </Col>
                ))
              ) : (
                <Col span={24}>
                  <div className="text-center py-4 text-gray-500">
                    未找到符合条件的质量控制点
                  </div>
                </Col>
              )}
            </Row>
          </Radio.Group>
        </div>
      </Modal>
    </>
  );
};

export default EditRandom;
