/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Col,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import {
  randomlySampleNum,
  randomlySampleNumV2,
  getQATask,
  sendQATask,
} from '@/api/quality';
import { blindlyExporExpress } from '@/api/quality';
//标准规则维护
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusCircleOutlined } from '@ant-design/icons';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import download from '@/utils/download';
import { FormInitVal, formItemLayout } from '../../data';
import { TaskContext } from '../Edit';
//任务配置模块
import StandardMaintenance from '../Model/StandardMaintenanceModel';
import TaskConfiguration from '../TaskConfiguration';

// 样本列表初始数据
let tableData = [{ name: '第1种样本', value: '', qaTaskSampleItemVoList: [] }];

type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit?: (params: any) => void;
  onSave?: () => Promise<void>;
  onRef?: any;
  baseInfo?: any; // 表单数据
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isUpdateJudgCriteria?: boolean;
};
type SampleData = {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  qaTaskSampleItemVoList: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
};

interface DataType {
  key: string;
  itemName: string;
  conditionThree: string;
}
type RefObjectMap = {
  [key: string]: ProFormInstance<any> | null;
};

// 定义后缀数字位数
const suffixNumList = [
  // { label: '2位', value: 2 },
  { label: '3位', value: 3 },
  { label: '4位', value: 4 },
  { label: '5位', value: 5 },
  { label: '6位', value: 6 },
  { label: '7位', value: 7 },
  { label: '8位', value: 8 },
  // { label: '9位', value: 9 },
  // { label: '10位', value: 10 },
];

// Add type for sampleVos items
interface SampleVosItem {
  assessedLabId: string;
  assessedLabCode: string;
  assessedLabName: string;
  sampleBox: string;
  sampleCode?: string;
  [key: string]: any; // 添加索引签名
}

const DoubleBind: React.FC<TEditProps> = ({
  id,
  onRef,
  baseInfo,
  onSave,
  isUpdateJudgCriteria,
}) => {
  const { token } = useTokenStore();
  // const { setRefType } = useContext(TaskContext);

  const [messageApi, contextHolder] = message.useMessage();

  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any[]>([]);
  // 样本ref集合
  const listRefs = useRef<RefObjectMap>({});
  // 考核项目-已选项目
  const [assessmentCheckBox, setAssessmentCheckBox] = useState<any[]>([]);
  // 当前点击样本
  const [sampleDetails, setSampleDetails] = useState<any>({});
  // 随机生成的编号表格数据
  const [sampleNumList, setSampleNumList] = useState<any[]>([]);
  // 随机样本编号弹窗
  const [randomlySampleNumModel, setRandomlySampleNumModel] =
    useState<boolean>(false);
  // 编号表头
  const [sampleNumHeader, setSampleNumHeader] = useState<any[]>([]);
  const [randomlySampleLoading, setRandomlySampleLoading] =
    useState<boolean>(false);
  // 标准判定维护弹窗
  const [standardModel, setStandardModel] = useState<boolean>(false);
  const formRefTask = useRef<any>(null);
  const formRef = useRef<ProFormInstance>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loading2, setLoading2] = useState<boolean>(false);

  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  // 当前选择的判定标准的类型，正确、可疑或错误
  const [curSelectedStandardType, setCurSelectedStandardType] = useState<
    'correct' | 'suspicious' | 'error'
  >('correct');

  // 添加当前操作的样本标识符
  const [currentSampleIdentifier, setCurrentSampleIdentifier] =
    useState<string>('');

  // 考核样编号生成 Modal 是否打开
  const [isOpenGenerateSampleNumModal, setIsOpenGenerateSampleNumModal] =
    useState<boolean>(false);

  // 新增状态管理每个样本的编号配置
  const [numberingConfigs, setNumberingConfigs] = useState<{
    [key: string]: {
      type: 'uniform' | 'random';
      uniformCode?: string;
      prefix?: string;
      suffixDigits?: number;
    };
  }>({});

  // 随机编号生成的数量
  const [randomNumber, setRandomNumber] = useState<number>(0);

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '考核项目',
      dataIndex: 'itemName',
      key: 'itemName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
      hideInSearch: true,
      render: (text, record) => {
        // 检查是否所有标准字段都为空
        const hasCorrectStandard =
          record?.condition || record?.conditionTwo || record?.conditionThree;
        const hasSuspiciousStandard =
          record?.conditionFour ||
          record?.conditionFive ||
          record?.conditionSix;
        const hasErrorStandard =
          record?.conditionSeven ||
          record?.conditionEight ||
          record?.conditionNine;

        // 如果所有标准都为空，则显示"维护判定标准"按钮
        if (
          !hasCorrectStandard &&
          !hasSuspiciousStandard &&
          !hasErrorStandard
        ) {
          return (
            <a
              onClick={() => handleSampleRules(record, 'correct')}
              className="text-primary"
            >
              维护判定标准
            </a>
          );
        }

        // 否则，显示现有的三种标准
        return (
          <div className="w-full flex flex-col">
            <a onClick={() => handleSampleRules(record, 'correct')}>
              正确(合格)标准:{' '}
              {record?.conditionThree ? record?.conditionThree : '未维护'}
            </a>
            <a onClick={() => handleSampleRules(record, 'suspicious')}>
              可疑标准: {record?.conditionSix ? record?.conditionSix : '未维护'}
            </a>
            <a onClick={() => handleSampleRules(record, 'error')}>
              错误(不合格)标准:{' '}
              {record?.conditionNine ? record?.conditionNine : '未维护'}
            </a>
          </div>
        );
      },
    },
  ];

  let columns2: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      key: 'index',
      hideInSearch: true,
      width: 100,
    },
    ...sampleNumHeader,
  ];

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleParams,
      handleValidateFields,
    };
  });

  /**
   * @TODO 添加一个样本
   */
  const handleAddSampleItem = async () => {
    const sampleName = `第${sampleListData.length + 1}种样本`;
    const newItemSample = {
      name: sampleName,
      value: '',
      // 取第一个样本的值
      qaTaskSampleItemVoList:
        sampleListData && sampleListData![0]?.qaTaskSampleItemVoList?.length
          ? sampleListData![0]?.qaTaskSampleItemVoList.map((_item: any) => {
              const Item = {
                code: sampleName,
                sampleName: sampleName, // 添加样本名称标识
                itemName: _item.itemName ?? _item.assessmentName,
                condition: '',
                conditionTwo: '',
                conditionThree: '',
                conditionFour: '',
                conditionFive: '',
                conditionSix: '',
                conditionSeven: '',
                conditionEight: '',
                conditionNine: '',
              };
              return Item;
            })
          : [],
    };
    setSampleListData([...sampleListData, newItemSample]);
  };

  /**
   * @TODO 样本规则配置
   * @params obj 样本详情
   */
  const handleSampleRules = async (
    obj: any,
    type: 'correct' | 'suspicious' | 'error'
  ) => {
    // 设置当前操作的样本名称
    const currentSampleName = obj.sampleName;

    // 保存当前操作的样本标识
    setCurrentSampleIdentifier(currentSampleName);

    // 设置当前选择的标准类型
    setCurSelectedStandardType(type);

    // 确保sampleDetails中包含正确的样本信息
    setSampleDetails({
      ...obj,
      sampleName: currentSampleName,
    });

    setStandardModel(true);
  };

  /**
   * @TODO 修改样本规则
   * @params obj 样本详情
   */
  const handleChangeRules = async (obj: any) => {
    // 设置当前操作的样本名称
    const currentSampleName = obj.sampleName;

    // 保存当前操作的样本标识
    setCurrentSampleIdentifier(currentSampleName);

    setStandardModel(true);
    setSampleDetails({
      ...obj,
      sampleName: currentSampleName,
    });
  };

  /**
   * @TODO 删除一个样本
   * @parms code 样本编号
   */
  const handleDeleteSampleItem = async (code: string) => {
    const delSampleList =
      sampleListData.filter((_item) => _item.name !== code) ?? [];
    // 重新生成样本编号
    delSampleList.forEach((_item, idx) => {
      _item.name = `第${idx + 1}种样本`;
    });
    // 重新排序样本编号
    const reorderedSampleNumList = sampleNumList.map((item) => {
      // 创建新对象,保留基础字段
      const newItem: any = {
        assessedLabCode: item.assessedLabCode,
        assessedLabId: item.assessedLabId,
        assessedLabName: item.assessedLabName,
        sampleBox: item.sampleBox,
        _key: item._key,
      };

      // 获取所有"第n种样本"的键
      const sampleKeys = Object.keys(item)
        .filter((key) => /^第\d+种样本$/.test(key))
        .filter((keyName) => keyName !== code);
      // 按照数字大小排序
      sampleKeys.sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

      // 重新按顺序添加样本值
      sampleKeys.forEach((key, index) => {
        newItem[`第${index + 1}种样本`] = item[key];
      });

      return newItem;
    });

    setSampleNumList(reorderedSampleNumList);

    setSampleListData(delSampleList);
  };

  /**
   * @TODO 导出样本清单
   */
  const handleExport = async () => {
    try {
      // 先保存
      onSave && (await onSave());
      setLoading2(true);
      const params = {
        taskId: baseInfo?.id,
      };
      const data = await blindlyExporExpress(params);
      await download(data, data.fileName || '样本清单.xlsx'); //导出方法，传入后端返回的文件流
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading2(false);
    }
  };

  /**
   * @TODO 随机生成样本编号
   */
  const handleRandomlySampleNum = async () => {
    try {
      // 处理数据
      setLoading(true);
      const params = {
        sampleNum: sampleListData.length,
      };
      const { code, data, msg }: any = await randomlySampleNum(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        const Item = data?.map((_item: any, idx: number) => {
          const obj: any = {};
          _item?.sampleCodes.forEach((_item2: any, idx2: number) => {
            obj[`第${idx2 + 1}种样本`] = _item2;
          });
          console.log(_item.sampleBox, '_item.sampleBox55');

          return {
            sampleBox: _item.sampleBox,
            ...obj,
          };
        });
        console.log(Item, 'ItemItemItem');

        setSampleNumList(Item);
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 生成的规则
   */
  const queryRulesObj = (rules: any) => {
    // 创建新的数据副本，避免直接修改状态
    const updatedSampleListData = [...sampleListData];

    // 只更新匹配当前设置的样本标识的样本
    const sampleIndex = updatedSampleListData.findIndex(
      (item) => item.name === currentSampleIdentifier
    );

    if (sampleIndex !== -1) {
      // 只更新找到的特定样本
      const targetSample = updatedSampleListData[sampleIndex];

      // 更新该样本中匹配的考核项目
      targetSample.qaTaskSampleItemVoList =
        targetSample.qaTaskSampleItemVoList.map((item: any) => {
          if (item.itemName === sampleDetails.itemName) {
            // 确保rules.rulesStorage已经是字符串
            const rulesStorage =
              typeof rules.rulesStorage === 'string'
                ? rules.rulesStorage
                : JSON.stringify(rules.rulesStorage);

            if (curSelectedStandardType === 'correct') {
              return {
                ...item,
                condition: rules.rules,
                conditionTwo: rulesStorage,
                conditionThree: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'suspicious') {
              return {
                ...item,
                conditionFour: rules.rules,
                conditionFive: rulesStorage,
                conditionSix: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'error') {
              return {
                ...item,
                conditionSeven: rules.rules,
                conditionEight: rulesStorage,
                conditionNine: rules.rulesView,
              };
            }
          }
          return item;
        });
    }

    // 更新状态
    setSampleListData(updatedSampleListData);
  };

  /**
   * @TODO 获取配置任务参数
   */
  const handleParams = async () => {
    try {
      const form2 = await formRefTask.current?.handleFormParams();
      // 校验所有样本参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce((promises, item) => {
        const formRef = listRefs.current[item.name];
        if (formRef) {
          promises.push(async () => {
            return formRef?.getFieldValue('value') ?? '';
          });
        }
        return promises;
      }, [] as Array<() => Promise<any>>);
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }

      // 使用当前最新的sampleNumList数据来构建提交的sampleVos
      const sampleListDataNew = cloneDeep(sampleListData);
      sampleListDataNew.forEach((item) => {
        const sampleVosTemp: any[] = [];

        // console.log(sampleNumList,sampleListDataNew, "sampleNumList");

        // 遍历当前表格中的数据（已经反映了删除操作）
        sampleNumList.forEach((item2) => {
          // console.log(item2, "item2");

          if (item2[item.name]) {
            // console.log(item2.sampleBox, "item2.sampleBox1");

            // 只添加存在该样本类型编号的数据
            sampleVosTemp.push({
              sampleBox: item2.sampleBox,
              sampleCode: item2[item.name],
              // 双盲考核不需要指定被考核机构
              assessedLabId: '',
              assessedLabCode: '',
              assessedLabName: '',
            });
          }
        });
        item.sampleVos = sampleVosTemp;
      });

      // 检查是否有样本编号
      if (sampleNumList.length === 0) {
        throw new Error('还未随机生成样本编号');
      }

      // 校验所有样品是否有编码
      const hasEmptySampleCode = sampleListDataNew.some(
        (sample) =>
          sample.sampleVos.length === 0 ||
          sample.sampleVos.some((sv: any) => !sv.sampleCode)
      );

      if (hasEmptySampleCode) {
        throw new Error('有样本还未分配样本编号，请重新生成样本编号');
      }

      console.log('最终提交的数据:', sampleListDataNew);

      // 处理数据
      const params = {
        ...form2,
        qaTaskSampleVos: sampleListDataNew,
      };
      return params;
    } catch (error) {
      // message.warning("请完善明细配置")
      throw new Error(`${error}`);
    }
  };
  /**
   * @TODO 任务参数-校验
   */
  const handleValidateFields = async () => {
    try {
      await formRefTask.current?.handleValidateFields();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce((promises, item) => {
        const formRef = listRefs.current[item.name];
        if (formRef) {
          promises.push(async () => {
            await formRef?.validateFields();
            return formRef?.getFieldValue('value') ?? '';
          });
        }
        return promises;
      }, [] as Array<() => Promise<any>>);
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }
      if (!sampleNumList.length) {
        throw new Error('还未随机生成样本编号');
      }

      // 更新校验逻辑，确保与handleParams方法一致
      // 使用当前最新的sampleNumList数据来构建提交的sampleVos
      const updatedSampleListData = cloneDeep(sampleListData);
      updatedSampleListData.forEach((item) => {
        const sampleVosTemp: any[] = [];
        // 遍历当前表格中的数据（已经反映了删除操作）
        sampleNumList.forEach((item2) => {
          if (item2[item.name]) {
            // 只添加存在该样本类型编号的数据
            console.log(item2.sampleBox, 'item2.sampleBox2');

            sampleVosTemp.push({
              sampleBox: item2.sampleBox,
              sampleCode: item2[item.name],
              // 双盲考核不需要指定被考核机构
              assessedLabId: '',
              assessedLabCode: '',
              assessedLabName: '',
            });
          }
        });
        item.sampleVos = sampleVosTemp;
      });

      // 校验所有样品是否有编码
      const hasEmptySampleCode = updatedSampleListData.some(
        (sample) =>
          sample.sampleVos.length === 0 ||
          sample.sampleVos.some((sv: any) => !sv.sampleCode)
      );

      if (hasEmptySampleCode) {
        throw new Error('有样本还未分配样本编号，请重新生成样本编号');
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 生成配置块
   * @returns
   */
  const renderNumberingBlocks = () => {
    return sampleListData?.map((item, index) => {
      const config = numberingConfigs[index] || { type: 'uniform' };
      return (
        <div
          key={index}
          className="w-full mb-4   mb-10 p-4 border border-solid border-[#D9D9D9] rounded-md"
        >
          <div className="text-lg font-medium mb-2">{item.name}</div>
          <div className="flex flex-col gap-4">
            <div className="w-full flex flex-row flex-nowrap items-center gap-2">
              <span className="w-[80px] text-right inline-block">
                编号前缀:
              </span>
              <Input
                value={config.prefix || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleConfigChange(index, 'prefix', e.target.value)
                }
                placeholder="请输入前缀"
                style={{ width: '200px' }}
              />
              <span className="text-red-500 ml-2">最多支持三位字母、数字</span>
            </div>
            <div className="w-full flex flex-row flex-nowrap items-center gap-2">
              <span className="w-[80px] flex items-center justify-end">
                <span className="text-red-500">*</span>
                <span>后缀位数:</span>
              </span>
              <Select
                value={config.suffixDigits}
                onChange={(value) =>
                  handleConfigChange(index, 'suffixDigits', value)
                }
                options={suffixNumList}
                style={{ width: '200px' }}
                placeholder="请选择后缀位数"
              />
              <span className="text-red-500 ml-2">示例: XJK001</span>
            </div>
          </div>
        </div>
      );
    });
  };

  /**
   * @TODO 随机生成样本编号-V2
   * 对第一版 handleRandomlySampleNum 的升级
   * 支持不同样本编号规则
   */
  const handleRandomlySampleNumV2 = async () => {
    // 判断获取的编号配置数量是否与 sampleListData 数量一致
    if (Object.keys(numberingConfigs).length !== sampleListData.length) {
      messageApi.warning('请先配置所有样本编号规则');
      return;
    }

    // 检查随机编号生成数量是否已填写
    if (!randomNumber) {
      messageApi.warning('请先填写被考核单位总数量');
      return;
    }

    try {
      // 构建请求参数
      console.log(sampleListData, 111111);
      const configList = sampleListData.map((_, index) => {
        const config = numberingConfigs[index];
        return {
          prefix: config?.prefix || '',
          digit: config?.suffixDigits,
          label: _.name,
          // type: 'random',
        };
      });

      // 调用新的后端接口
      const { code, data, msg }: any = await randomlySampleNumV2({
        num: randomNumber,
        contents: configList,
        type: 'random',
      });

      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }

      const _sampleListData = cloneDeep(sampleListData);

      // 更新样本数据 - 适应新的数据结构
      // _sampleListData.forEach((sample, sampleIndex) => {
      //   sample.sampleVos = [];

      //   const sampleBox = sample.sampleBox;
      //    const samples = sample.samples;
      //   // 遍历每组数据（每个被考核单位的数据）
      //   if (data && data.length > 0) {
      //     data.forEach(
      //       (
      //         boxItem: { sampleBox: string; samples: string[] },
      //         labIndex: number
      //       ) => {
      //         // 确保该样本类型在该组数据中有对应的编号
      //         if (sampleIndex < boxItem.samples.length) {
      //           sample.sampleVos.push({
      //             assessedLabId: '', // 双盲考核不需要指定被考核机构
      //             assessedLabCode: '',
      //             assessedLabName: '',
      //             sampleBox: boxItem.sampleBox, // 使用返回的 sampleBox 值
      //             sampleCode: sampleCode, // 使用正确的索引获取编号
      //           });
      //         }
      //       }
      //     );
      //   }
      // });

      // 更新样本数据 - 适应新的数据结构
      if (data && Array.isArray(data)) {
        // 遍历每个样本盒
        data.forEach((boxData: any, boxIndex: number) => {
          const sampleBox = boxData.sampleBox;
          const samples = boxData.samples;

          console.log(boxData, sampleBox);

          // 检查 samples 是否为对象
          if (typeof samples === 'object' && samples !== null) {
            // 遍历对象的键值对
            Object.entries(samples).forEach(([sampleType, sampleCode]) => {
              const sampleIndex = _sampleListData.findIndex(
                (sample) => sample.name === sampleType
              );
              if (sampleIndex !== -1) {
                if (!_sampleListData[sampleIndex].sampleVos) {
                  _sampleListData[sampleIndex].sampleVos = [];
                }

                _sampleListData[sampleIndex].sampleVos.push({
                  assessedLabId: null,
                  assessedLabCode: null,
                  assessedLabName: null,
                  sampleBox: sampleBox as string,
                  sampleCode: sampleCode as string,
                });
                console.log(
                  'Updated sampleVos:',
                  _sampleListData[sampleIndex].sampleVos
                ); // 打印更新后的 sampleVos
              }
            });
          }
        });
      }

      // 复用生成表格数据的逻辑
      // const newSampleNumList = _sampleListData.reduce<SampleVosItem[]>(
      //   (acc, sample) => {
      //     console.log(acc, sample, "acc, sample");

      //     sample.sampleVos?.forEach((orgSample: SampleVosItem) => {
      //       // 先检查是否已存在相同sampleBox的记录
      //       const existing = acc.find(
      //         (item) =>
      //           item.sampleBox === orgSample.sampleBox ||
      //           (orgSample.assessedLabId &&
      //             item.assessedLabId === orgSample.assessedLabId)
      //       );

      //       if (existing) {
      //         // 如果存在，则添加该样本的编号到现有记录
      //         existing[sample.name] = orgSample.sampleCode;
      //       } else {
      //         // 否则创建新记录
      //         // 生成一个仅用于前端标识的唯一key
      //         const uniqueId = `row_init_${acc.length}_${Math.random().toString(
      //           36
      //         )}_${Math.random().toString(36).slice(2)}`;
      //         acc.push({
      //           _key: uniqueId, // 仅用于前端识别
      //           // 保留原始数据中的字段值
      //           assessedLabId: '', // 双盲考核中应为空
      //           assessedLabCode: '',
      //           assessedLabName: '',
      //           sampleBox: orgSample.sampleBox,
      //           [sample.name]: orgSample.sampleCode,
      //         });
      //       }
      //     });
      //     return acc;
      //   },
      //   []
      // );

      const labs: any[] = [];
      // 为每个被考核单位创建一行数据
      for (let i = 0; i < data.length; i++) {
        const row: any = {
          assessedLabId: null,
          assessedLabCode: null,
          assessedLabName: null,
          _key: `unit_${i}_${Date.now()}_${Math.random()
            .toString(36)
            .slice(2)}`,
          sampleBox: data[i].sampleBox,
          ...data[i].samples,
        };
        labs.push(row);
      }

      // 更新状态
      console.log(_sampleListData, 11155454);

      setSampleListData(_sampleListData);
      setSampleNumList(labs);
      setIsOpenGenerateSampleNumModal(false);
      messageApi.success('样本编号生成成功');
    } catch (error) {
      messageApi.error('编号生成失败，请重试');
      console.error('随机生成编号失败:', error);
    }
  };
  // 使用 useEffect 监听 sampleListData 变化
  useEffect(() => {
    console.log('Updated sampleListData:', sampleListData);
  }, [sampleListData]);
  /**
   * @TODO 处理配置变化
   * @param sampleIndex 样本索引
   * @param field 字段
   * @param value 值
   */
  const handleConfigChange = (
    sampleIndex: number,
    field: string,
    value: any
  ) => {
    setNumberingConfigs((prev) => ({
      ...prev,
      [sampleIndex]: {
        ...prev[sampleIndex],
        [field]: value,
      },
    }));
  };

  // 确定每个记录的唯一标识符，仅用于前端删除操作
  const getRowKey = (record: any) => {
    // 只使用_key作为唯一标识符，不再使用assessedLabId、assessedLabCode等字段
    return record?._key || Math.random().toString(36).slice(2);
  };

  // 添加选择行变更处理函数
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('选中的行: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 添加删除选中行的处理函数
  const handleDeleteSelectedRows = () => {
    setDeleteLoading(true);

    try {
      console.log('删除前的数据：', sampleNumList);
      console.log('选中行的key：', selectedRowKeys);

      // 使用确定的rowKey函数来过滤数据
      const newSampleNumList = [...sampleNumList].filter((item) => {
        const rowKey = getRowKey(item);
        const shouldKeep = !selectedRowKeys.includes(rowKey);
        console.log('行key:', rowKey, '保留?', shouldKeep);
        return shouldKeep;
      });

      console.log('删除后的数据：', newSampleNumList);

      // 更新表格数据
      setSampleNumList(newSampleNumList);

      // 更新样本数据，从每个样本的 sampleVos 中删除对应的被考核单位
      const updatedSampleListData = cloneDeep(sampleListData);

      // 记录已删除的sampleBox值，用于匹配删除sampleVos中的项
      const deletedSampleBoxes = sampleNumList
        .filter((item) => selectedRowKeys.includes(getRowKey(item)))
        .map((item) => item.sampleBox);

      console.log('删除的sampleBox:', deletedSampleBoxes);

      updatedSampleListData.forEach((sample) => {
        if (sample.sampleVos && sample.sampleVos.length > 0) {
          // 使用sampleBox匹配删除项
          sample.sampleVos = sample.sampleVos.filter(
            (sampleVo: SampleVosItem) =>
              !deletedSampleBoxes.includes(sampleVo.sampleBox)
          );
        }
      });

      setSampleListData(updatedSampleListData);

      // 清空选中行
      setSelectedRowKeys([]);

      messageApi.success('删除成功');
    } catch (error) {
      messageApi.error('删除失败，请重试');
      console.error('删除失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 设置行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const initSampleList = async (TableData?: any) => {
    let qaTaskSampleVosNew = cloneDeep(baseInfo.qaTaskSampleVos);
    // 初始样本列表
    if (TableData && TableData.length) {
      qaTaskSampleVosNew = cloneDeep(TableData);
    }
    
    let newSampleListData: any = [];
    if (qaTaskSampleVosNew) {
      qaTaskSampleVosNew.forEach((item: any) => {
        item.qaTaskSampleItemVoList.forEach((item2: any) => {
          item2.code = item.name;
        });
      });
      newSampleListData = qaTaskSampleVosNew;
    } else {
      newSampleListData = tableData;
    }
    setSampleListData([...newSampleListData]);

    console.log(newSampleListData);

    // 初始化随机生成编码表格
    const qaTaskSampleVos = cloneDeep(newSampleListData);;
    if (qaTaskSampleVos?.length) {
      // 使用一个更新后的方法来处理二维数组的数据
      const generateSampleGridData = () => {
        const labEntries: any[] = [];
        let labCounter = 0;

        // 确定所有样本的数量
        const samplesCount = qaTaskSampleVos.length;
        // 确定第一个样本中的labs数量（应该所有样本的labs数量一致）
        const labsCount = qaTaskSampleVos[0]?.sampleVos?.length || 0;

        // 创建一个映射表来存储sampleBox信息
        const sampleBoxMap: Record<number, string> = {};

        // 首先收集所有的sampleBox信息
        qaTaskSampleVos.forEach((sample: any) => {
          sample.sampleVos?.forEach((sampleVo: any, index: number) => {
            if (sampleVo.sampleBox) {
              sampleBoxMap[index] = sampleVo.sampleBox;
            }
          });
        });

        // 为每个lab创建一个条目
        for (let labIndex = 0; labIndex < labsCount; labIndex++) {
          // 生成仅用于前端标识的唯一key
          const uniqueId = `row_init_${labCounter}_${Math.random()
            .toString(36)
            .slice(2)}`;

          // 获取当前lab在第一个样本中的原始数据
          const originalLabData =
            qaTaskSampleVos[0]?.sampleVos?.[labIndex] || {};

          const labEntry: any = {
            _key: uniqueId, // 仅用于前端识别
            // 双盲考核中这些字段应为空
            assessedLabId: '',
            assessedLabCode: '',
            assessedLabName: '',
            sampleBox: sampleBoxMap[labIndex] || '', // 使用收集到的sampleBox
          };

          // 添加每个样本类型的编号到这个lab条目
          qaTaskSampleVos.forEach(
            (sample: {
              sampleVos: { sampleCode: any }[];
              name: string | number;
            }) => {
              if (sample?.sampleVos?.[labIndex]) {
                labEntry[sample.name] = sample.sampleVos[labIndex].sampleCode;
              }
            }
          );

          labEntries.push(labEntry);
          labCounter++;
        }

        return labEntries;
      };

      const result = generateSampleGridData();
      console.log('初始化的表格数据:', result);
      setSampleNumList(result);

      // 为qaTaskSampleVos中的每个样本项目添加sampleName标识
      const enhancedQaTaskSampleVos = baseInfo.qaTaskSampleVos.map(
        (sample: any) => {
          // 为每个考核项目添加样本名称标识
          if (
            sample.qaTaskSampleItemVoList &&
            Array.isArray(sample.qaTaskSampleItemVoList)
          ) {
            sample.qaTaskSampleItemVoList = sample.qaTaskSampleItemVoList.map(
              (item: any) => ({
                ...item,
                sampleName: sample.name, // 添加样本名称标识
              })
            );
          }
          return sample;
        }
      );

      setSampleListData(enhancedQaTaskSampleVos);
    }
  };

  useEffect(() => {
    initSampleList();
  }, [baseInfo]);

  useEffect(() => {
    // 考核项目选中
    const sampleListDataNew = cloneDeep(sampleListData);
    if (sampleListDataNew?.length) {
      sampleListDataNew?.forEach((_item: any) => {
        let CopyQaTaskSampleItemVoList = cloneDeep(
          _item?.qaTaskSampleItemVoList
        );
        const checked = assessmentCheckBox
          ?.filter((Item) => Item.check === 1)
          ?.map((_item2: any) => {
            let Item = {
              code: _item.name, // 样本编号
              itemName: _item2.name,
              ratio: _item2.ratio,
              condition: '',
              conditionTwo: '',
              conditionThree: '',
              conditionFour: '',
              conditionFive: '',
              conditionSix: '',
              conditionSeven: '',
              conditionEight: '',
              conditionNine: '',
            };
            // 保留映射原有已选规则
            if (CopyQaTaskSampleItemVoList.length) {
              CopyQaTaskSampleItemVoList?.forEach((_item3: any) => {
                if (_item3.itemName === _item2.name) {
                  Item = {
                    ...Item,
                    condition: _item3.condition ?? '',
                    conditionTwo: _item3.conditionTwo ?? '',
                    conditionThree: _item3.conditionThree ?? '',
                    conditionFour: _item3.conditionFour ?? '',
                    conditionFive: _item3.conditionFive ?? '',
                    conditionSix: _item3.conditionSix ?? '',
                    conditionSeven: _item3.conditionSeven ?? '',
                    conditionEight: _item3.conditionEight ?? '',
                    conditionNine: _item3.conditionNine ?? '',
                  };
                }
              });
            }
            return Item;
          });
        _item.qaTaskSampleItemVoList = checked;
      });
      setSampleListData([...sampleListDataNew]);
    }
  }, [assessmentCheckBox]);

  useEffect(() => {
    // 随机编号表头
    const header = sampleListData.map((item, idx) => {
      return {
        title: item.name,
        dataIndex: item.name,
        key: item.name,
        hideInSearch: true,
      };
    });
    // 初始样本列表
    setSampleNumHeader([...header]);
  }, [sampleListData]);

  useEffect(() => {
    // 参考结果赋值
    if (sampleListData?.length) {
      sampleListData.forEach((item: any) => {
        if (listRefs.current[item.name]) {
          listRefs.current[item.name]?.setFieldValue('value', item.value);
          listRefs.current[item.name]?.setFieldValue('realCode', item.realCode);
        }
      });
    }
  }, [sampleListData]);

  const getDetailData = async () => {
    try {
      if (baseInfo?.id) {
        const { code, data, msg } = await getQATask(baseInfo?.id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          initSampleList(data.qaTaskSampleVos)
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  // 导入excel更新样本编号-配置
  const UploadProps: UploadProps = {
    action: import.meta.env.VITE_URL + '/qa/tasks/importSampleCode',
    headers: { Authorization: 'Bearer ' + token },
    data: { taskId: baseInfo?.id },
    onChange: ({ file, fileList }) => {
      if (file.status === 'done') {
        if (file.response?.code === 200) {
          console.log(getDetailData, baseInfo);
          // 重新获取详情数据
          getDetailData();
          messageApi.success('导入成功');
        } else {
          messageApi.error(file.response?.msg);
        }
      }
    },
    showUploadList: false,
    capture: undefined,
  };

  return (
    <>
      {contextHolder}
      {/* 任务配置 */}
      {!isUpdateJudgCriteria ? (
        <div className="mt-4">
          <TaskConfiguration
            onRef={formRefTask}
            setFormInfo={(val) => setAssessmentCheckBox(val)}
            taskConfigurationForm={baseInfo}
          />
        </div>
      ) : null}
      {/* 明细配置 */}
      <div className="mt-4">
        <BlockContainer title="明细配置" bodyStyle={{ padding: '20px' }}>
          <Row gutter={[25, 25]}>
            {sampleListData?.map((item: SampleData, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                  extra={
                    sampleListData.length > 1 ? (
                      <Popconfirm
                        title="删除此样本？"
                        onConfirm={() => handleDeleteSampleItem(item.name)}
                        okText="确定"
                        cancelText="取消"
                        key="delpro"
                      >
                        <a className="text-[red]">删除</a>
                      </Popconfirm>
                    ) : null
                  }
                >
                  <div className="h-[300px] overflow-auto overflow-x-hidden">
                    <ProForm
                      formRef={
                        ((ref: any): void =>
                          (listRefs.current[item.name] = ref)) as any
                      }
                      {...formItemLayout}
                      layout="horizontal"
                      grid={true}
                      submitter={false}
                      autoFocusFirstInput={false}
                      onValuesChange={(values: any) => {
                        if (values.value || values.realCode) {
                          sampleListData.forEach((Items: any) => {
                            if (Items.name === item.name && values.value) {
                              Items.value = values.value;
                            }
                            if (Items.name === item.name && values.realCode) {
                              Items.realCode = values.realCode;
                            }
                          });
                          setSampleListData([...sampleListData]);
                        }
                      }}
                    >
                      <ProFormTextArea
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        allowClear
                        label="参考结果"
                        rules={[
                          { required: true, message: '参考结果为必填项' },
                        ]}
                      />

                      <ProFormText
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="realCode"
                        label="真实编号"
                      />
                      <div className="real-code-tips">被考核机构不可见</div>

                      <ProTable
                        key="id"
                        rowKey="id"
                        className="w-full px-1"
                        toolBarRender={false}
                        options={false}
                        search={false}
                        bordered
                        columns={columns}
                        dataSource={item?.qaTaskSampleItemVoList.map(
                          (taskItem: any) => ({
                            ...taskItem,
                            sampleName: item.name, // 添加当前样本名称标识
                            sampleId: `sample_${item.name}_${Math.random()
                              .toString(36)
                              .substring(2, 10)}`, // 添加唯一标识
                          })
                        )}
                        pagination={false}
                      />
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
            <Col xs={24} sm={8} md={8}>
              <BlockContainer
                title={`第${sampleListData.length + 1}种样本`}
                className="border-[#D9D9D9]"
              >
                <div className="h-[300px] flex justify-center items-center">
                  <Tooltip title="点击添加样本">
                    <PlusCircleOutlined
                      className="text-[50px] text-[#A8A8A8] cursor-pointer"
                      onClick={handleAddSampleItem}
                    />
                  </Tooltip>
                </div>
              </BlockContainer>
            </Col>
          </Row>
          {!isUpdateJudgCriteria ? (
            <BlockContainer className="mt-6">
              <ProTable
                key="_key"
                rowKey={getRowKey}
                className="w-full"
                options={false}
                search={false}
                bordered
                rowSelection={rowSelection}
                columns={columns2}
                dataSource={sampleNumList}
                pagination={false}
                headerTitle={[
                  <Space className="">
                    <Button
                      key="generate-sample-num-btn"
                      type="primary"
                      loading={loading}
                      onClick={() => setIsOpenGenerateSampleNumModal(true)}
                    >
                      随机生成样本编号
                    </Button>
                    <Upload {...UploadProps}>
                      <Button
                        key="import-sample-list-btn"
                        loading={loading2}
                        icon={<UploadOutlined />}
                        type="primary"
                      >
                        导入excel更新样本编号
                      </Button>
                    </Upload>
                    <Button
                      key="export-sample-list-btn"
                      onClick={handleExport}
                      loading={loading2}
                      icon={<DownloadOutlined />}
                      type="default"
                    >
                      导出样本清单
                    </Button>
                    <Popconfirm
                      key="delete-selected-btn"
                      title="确认删除所选记录?"
                      okText="确定"
                      cancelText="取消"
                      onConfirm={handleDeleteSelectedRows}
                    >
                      <Button
                        danger
                        type="primary"
                        disabled={selectedRowKeys.length === 0}
                        loading={deleteLoading}
                      >
                        删除所选
                      </Button>
                    </Popconfirm>
                  </Space>,
                ]}
              />
            </BlockContainer>
          ) : null}
        </BlockContainer>
        {/* 标准判定弹窗 */}
        <StandardMaintenance
          close={() => setStandardModel(false)}
          open={standardModel}
          getRuler={queryRulesObj}
          rulesDetails={sampleDetails}
          taskId={baseInfo.id}
          taskType={baseInfo.assessmentType}
          curSelectedStandardType={curSelectedStandardType}
          onStandardTypeChange={(type) => setCurSelectedStandardType(type)}
        />
      </div>
      {/* 随机编号输入弹窗 */}
      <Modal
        className="min-w-[600px]"
        title="随机生成样本编号"
        width="40%"
        open={randomlySampleNumModel}
        onCancel={() => setRandomlySampleNumModel(false)}
        onOk={() => formRef.current?.submit()}
        confirmLoading={loading}
      >
        <ProForm
          className="pt-4"
          submitter={false}
          formRef={formRef}
          onFinish={handleRandomlySampleNum}
          layout="horizontal"
          onValuesChange={(
            _: Record<string, any>,
            values: Record<string, any>
          ) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormDigit
            min={0}
            fieldProps={{ precision: 0 }}
            label="样本盒数量"
            name="boxNum"
            rules={[{ required: true, message: '请输入样本编号数量' }]}
            placeholder="请输入样本编号数量"
          />
        </ProForm>
      </Modal>
      {/* 生成样本编号 Modal */}
      <Modal
        open={isOpenGenerateSampleNumModal}
        onCancel={() => setIsOpenGenerateSampleNumModal(false)}
        title="考核样编号生成规则"
        centered
        width={800}
        footer={
          <Button
            type="primary"
            onClick={() => {
              handleRandomlySampleNumV2();
            }}
          >
            开始生成编号
          </Button>
        }
        maskClosable={false}
      >
        <div className="w-full max-h-[70vh] overflow-hidden flex flex-col gap-4">
          <div className="bg-[#fff7f7] p-4 border border-solid border-[#ffccc7] rounded-md">
            <div className="text-[#f5222d] font-bold">
              【非常重要】温馨提示:{' '}
            </div>
            <div className="mt-2">
              1、双盲考核只能使用"随机生成编号"，编号前缀可不填，后缀随机数位数最多支持5位。例如：前缀设置为XJK，随机数选择3，则对应种类的考核样编号将为XJK001、XJK002...
            </div>
          </div>
          <div className="w-full flex flex-row flex-nowrap gap-2 my-4 items-center">
            <span>被考核单位总数量: </span>
            <InputNumber
              style={{ width: '180px' }}
              placeholder="请输入"
              onChange={(value: React.SetStateAction<number>) =>
                setRandomNumber(value)
              }
            />
          </div>
          <div className="w-full flex-1 overflow-x-hidden overflow-y-auto pr-4  pd-10">
            {renderNumberingBlocks()}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default DoubleBind;
