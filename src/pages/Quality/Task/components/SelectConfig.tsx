/*
 * @Author: LGX
 * @Date: 2024-03-06 10:56:41
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-25 10:21:08
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\Task\components\SelectConfig.tsx
 * @Description: 调整模板-配置下拉项
 * 
 */
import { Key, useCallback, useEffect, useRef, useState } from 'react';
import { Col, message, Modal, Row, Skeleton, Tree } from 'antd';
import { EditableProTable, ProForm } from '@ant-design/pro-components';
import type {
  EditableFormInstance,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';

type DataSourceType = {
  id: React.Key;
  title?: string;
};

type TRoleDetailProps = {
  open: boolean;
  closeDetail: (val?: any) => void;
  list?: any[]; // 已保存数据项 + 后面新增未保存提交的数据项
  editableKey?: any[]; // 已保存的可编辑数据项
  readonly?: boolean; //不可新增修改
};

const SelectConfig: React.FC<TRoleDetailProps> = ({
  open,
  closeDetail,
  list = [],
  editableKey,
  readonly = false
}) => {
  // loading
  const [loading, setLoading] = useState<boolean>(false);

  const [defaultData, setDefaultData] = useState<DataSourceType[]>([]);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const close = () => {
    closeDetail();
  };
  useEffect(() => {
    if (list && editableKey) {
      const tableData = list.map((item: any, idx: any) => (
        {
          id: idx,
          item: item,
        }
      ))
      setDefaultData(tableData);
      // 找出没有保存的数据，可进行修改、删除
      const difference = tableData.filter(value => !editableKey?.includes(value.item));
      setEditableRowKeys(difference.map(item => item.id));
    } else {
      setDefaultData([]);
    }
  }, [list]);

  const formRef = useRef<ProFormInstance<any>>();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '选项',
      dataIndex: 'item',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 180,
    },
  ];

  return (
    <Modal
      className="min-w-[600px]"
      title={'配置下拉项'}
      width="40%"
      open={open}
      destroyOnClose
      onCancel={close}
      onOk={async () => {
        try {
          await formRef.current?.validateFields();
          const form = formRef.current?.getFieldValue('table');
          closeDetail(form);
        } catch (error) {
          message.warning('请完善表单');
        }
      }}
      confirmLoading={loading}
    >
      <ProForm<{
        table: DataSourceType[];
      }>
        key="form"
        formRef={formRef}
        submitter={false}
        initialValues={{
          table: defaultData,
        }}
        validateTrigger="onBlur"
      >
        <EditableProTable<DataSourceType>
          rowKey="id"
          scroll={{}}
          editableFormRef={editorFormRef}
          name="table"
          controlled={true}
          recordCreatorProps={readonly ? false : {
            position: 'bottom',
            record: () => ({
              id: (Math.random() * 1000000).toFixed(0),
            }),
          }}
          columns={columns}
          editable={{
            type: 'multiple',
            editableKeys: editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (row, config, defaultDom) => {
              const dom = <span className='text-[red] ml-[-13px]'>（新增未保存）</span>
              return [defaultDom.delete, dom];
            },
          }}
        />
      </ProForm>
      {
        readonly ? null : <div className='text-[red] mt-[-15px] font-blod'>注：只可新增数据，或对新增未保存的数据修改</div>
      }

    </Modal>
  );
};

export default SelectConfig;
