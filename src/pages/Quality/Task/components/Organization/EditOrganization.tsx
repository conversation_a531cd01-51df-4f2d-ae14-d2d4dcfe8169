/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable jsx-a11y/anchor-is-valid */
import {
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Col,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import { uploadFiles } from '@/api/file';
import {
  getQATask,
  randomlySampleNum,
  randomlySampleNumV2,
} from '@/api/quality';
import { orginExporExpress } from '@/api/quality';
//标准规则维护
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusCircleOutlined } from '@ant-design/icons';
import { DownloadOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import download from '@/utils/download';
import { formItemLayout } from '../../data';
import { TaskContext } from '../Edit';
//任务配置模块
import StandardMaintenance from '../Model/StandardMaintenanceModel';
import TaskConfiguration from '../TaskConfiguration';

// 样本列表初始数据
let tableData = [{ name: '第1种样本', value: '', qaTaskSampleItemVoList: [] }];

type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit?: (params: any) => void;
  onSave?: () => Promise<void>;
  onRef?: any;
  baseInfo?: any; // 表单数据
  readonly?: boolean;
  readonlyAll?: boolean;
  hideRank?: boolean;
  isUpdateJudgCriteria?: boolean;
};
type SampleData = {
  name: string; // 假设sampleListData中的每个item都有一个id字段作为标识符
  value: string;
  qaTaskSampleItemVoList: any[];
  defaultData?: Record<string, any>; // 示例：每个item可选的初始数据
};
interface DataType {
  key: string;
  itemName: string;
  conditionThree: string;
}
type RefObjectMap = {
  [key: string]: ProFormInstance<any> | null;
};

// 定义后缀数字位数
const suffixNumList = [
  // { label: '2位', value: 2 },
  { label: '3位', value: 3 },
  { label: '4位', value: 4 },
  { label: '5位', value: 5 },
  { label: '6位', value: 6 },
  { label: '7位', value: 7 },
  { label: '8位', value: 8 },
  // { label: '9位', value: 9 },
  // { label: '10位', value: 10 },
];

// Add type for sampleVos items
interface SampleVosItem {
  assessedLabId: string;
  assessedLabCode: string;
  assessedLabName: string;
  sampleBox: string;
  sampleCode?: string;
  [key: string]: any; // 添加索引签名
}

const EditOrganization: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onSave,
  onRef,
  baseInfo,
  isUpdateJudgCriteria,
}) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { token } = useTokenStore();
  const { setRefType } = useContext(TaskContext);
  // 样本明细配置列表
  const [sampleListData, setSampleListData] = useState<any[]>([]);
  // 样本ref集合
  const listRefs = useRef<RefObjectMap>({});
  // 考核项目-已选项目
  const [assessmentCheckBox, setAssessmentCheckBox] = useState<any[]>([]);
  // 当前点击样本
  const [sampleDetails, setSampleDetails] = useState<any>({});
  // 随机生成的编号表格数据
  const [sampleNumList, setSampleNumList] = useState<any[]>([]);
  // 编号表头
  const [sampleNumHeader, setSampleNumHeader] = useState<any[]>([]);
  const [randomlySampleLoading, setRandomlySampleLoading] =
    useState<boolean>(false);
  // 标准判定维护弹窗
  const [standardModel, setStandardModel] = useState<boolean>(false);
  const [loading2, setLoading2] = useState<boolean>(false);
  const formRefTask = useRef<any>(null);

  const [radioGroupValue, setRadioGroupValue] = useState<any>('uniform');

  // 添加选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);

  // 当前选择的判定标准的类型，正确、可疑或错误
  const [curSelectedStandardType, setCurSelectedStandardType] = useState<
    'correct' | 'suspicious' | 'error'
  >('correct');

  // 添加当前操作的样本标识符
  const [currentSampleIdentifier, setCurrentSampleIdentifier] =
    useState<string>('');

  // 考核样编号生成 Modal 是否打开
  const [isOpenGenerateSampleNumModal, setIsOpenGenerateSampleNumModal] =
    useState<boolean>(false);

  // 新增状态管理每个样本的编号配置
  const [numberingConfigs, setNumberingConfigs] = useState<{
    [key: string]: {
      type: 'uniform' | 'random';
      uniformCode?: string;
      prefix?: string;
      suffixDigits?: number;
    };
  }>({});

  // 新增初始化配置的 useEffect
  useEffect(() => {
    setNumberingConfigs((prev) => ({
      // ...prev,
      ...sampleListData.reduce(
        (acc, _, index) => ({
          ...acc,
          [index]: {
            type: 'uniform',
            uniformCode: '',
            prefix: '',
            suffixDigits: undefined,
          },
        }),
        {}
      ),
    }));
  }, [sampleListData]); // 当样本数量变化时重新初始化

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '考核项目',
      dataIndex: 'itemName',
      key: 'itemName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
      hideInSearch: true,
      render: (text, record) => {
        // 检查是否所有标准字段都为空
        const hasCorrectStandard =
          record?.condition || record?.conditionTwo || record?.conditionThree;
        const hasSuspiciousStandard =
          record?.conditionFour ||
          record?.conditionFive ||
          record?.conditionSix;
        const hasErrorStandard =
          record?.conditionSeven ||
          record?.conditionEight ||
          record?.conditionNine;

        // 如果所有标准都为空，则显示"维护判定标准"按钮
        if (
          !hasCorrectStandard &&
          !hasSuspiciousStandard &&
          !hasErrorStandard
        ) {
          return (
            <a
              onClick={() => handleSampleRules(record, 'correct')}
              className="text-primary"
            >
              维护判定标准
            </a>
          );
        }

        // 否则，显示现有的三种标准
        return (
          <div className="w-full flex flex-col">
            <a onClick={() => handleSampleRules(record, 'correct')}>
              正确(合格)标准:{' '}
              {record?.conditionThree ? record?.conditionThree : '未维护'}
            </a>
            <a onClick={() => handleSampleRules(record, 'suspicious')}>
              可疑标准: {record?.conditionSix ? record?.conditionSix : '未维护'}
            </a>
            <a onClick={() => handleSampleRules(record, 'error')}>
              错误(不合格)标准:{' '}
              {record?.conditionNine ? record?.conditionNine : '未维护'}
            </a>
          </div>
        );
      },
    },
  ];

  let columns2: ProColumns<Record<string, any>>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '被考核单位',
      dataIndex: 'assessedLabName',
      key: 'assessedLabName',
      hideInSearch: true,
    },
    ...sampleNumHeader,
  ];

  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleParams,
      handleValidateFields,
    };
  });

  /**
   * @TODO 导出样本清单
   */
  const handleExport = async () => {
    try {
      // 先保存
      onSave && (await onSave());
      setLoading2(true);
      const params = {
        taskId: baseInfo?.id,
      };
      const data = await orginExporExpress(params);
      await download(data, '机构考核样本清单.xlsx'); //导出方法，传入后端返回的文件流
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading2(false);
    }
  };

  /**
   * @TODO 添加一个样本
   */
  const handleAddSampleItem = async () => {
    const newItemSample = {
      name: `第${sampleListData.length + 1}种样本`,
      value: '',
      // 取第一个样本的值
      qaTaskSampleItemVoList:
        sampleListData && sampleListData![0]?.qaTaskSampleItemVoList?.length
          ? sampleListData![0]?.qaTaskSampleItemVoList.map((_item: any) => {
              const Item = {
                code: `第${sampleListData.length + 1}种样本`,
                itemName: _item.itemName ?? _item.assessmentName,
                condition: '',
                conditionTwo: '',
                conditionThree: '',
              };
              return Item;
            })
          : [],
    };
    setSampleListData([...sampleListData, newItemSample]);
  };

  /**
   * @TODO 样本规则配置
   * @params obj 样本详情
   */
  const handleSampleRules = async (
    obj: any,
    type: 'correct' | 'suspicious' | 'error'
  ) => {
    // 设置当前操作的样本名称
    const currentSampleName = obj.sampleName || obj.code;

    // 保存当前操作的样本标识
    setCurrentSampleIdentifier(currentSampleName);

    // 设置当前选择的标准类型
    setCurSelectedStandardType(type);

    // 确保sampleDetails中包含正确的样本信息
    setSampleDetails({
      ...obj,
      sampleName: currentSampleName,
    });

    setStandardModel(true);
  };

  /**
   * @TODO 删除一个样本
   * @parms code 样本编号
   */
  const handleDeleteSampleItem = async (code: string) => {
    const delSampleList =
      sampleListData.filter((_item) => _item.name !== code) ?? [];
    // 重新生成样本编号
    delSampleList.forEach((_item, idx) => {
      _item.name = `第${idx + 1}种样本`;
    });

    // 重新排序样本编号
    const reorderedSampleNumList = sampleNumList.map((item) => {
      // 创建新对象,保留基础字段
      const newItem: any = {
        assessedLabCode: item.assessedLabCode,
        assessedLabId: item.assessedLabId,
        assessedLabName: item.assessedLabName,
        sampleBox: item.sampleBox,
        _key: item._key,
      };

      // 获取所有"第n种样本"的键
      const sampleKeys = Object.keys(item)
        .filter((key) => /^第\d+种样本$/.test(key))
        .filter((keyName) => keyName !== code);
      // 按照数字大小排序
      sampleKeys.sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

      // 重新按顺序添加样本值
      sampleKeys.forEach((key, index) => {
        newItem[`第${index + 1}种样本`] = item[key];
      });

      return newItem;
    });

    setSampleNumList(reorderedSampleNumList);

    setSampleListData(delSampleList);
  };

  /**
   * @TODO 随机生成样本编号
   */
  const handleRandomlySampleNum = async () => {
    try {
      const form = await formRefTask.current?.handleFormParams();
      const { orgVos } = form;
      setRandomlySampleLoading(true);
      if (!orgVos?.length) {
        messageApi.warning('请先选择被考核单位');
        return;
      }
      // 处理数据
      const params = {
        boxNum: orgVos.length,
        sampleNum: sampleListData.length,
      };
      const { code, data, msg }: any = await randomlySampleNum(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        messageApi.success(QUERY_SUCCESS_MSG);
        const Item = data?.map((_item: any, idx: number) => {
          const obj: any = {};
          _item?.sampleCodes.forEach((_item2: any, idx2: number) => {
            obj[`第${idx2 + 1}种样本`] = _item2;
          });
          return {
            ...orgVos[idx],
            sampleBox: _item.sampleBox,
            ...obj,
          };
        });
        setSampleNumList(Item);
      } else {
        messageApi.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setRandomlySampleLoading(false);
    }
  };

  /**
   * @TODO 生成的规则
   */
  const queryRulesObj = (rules: any) => {
    // 创建新的数据副本，避免直接修改状态
    const updatedSampleListData = [...sampleListData];

    // 只更新匹配当前设置的样本标识的样本
    const sampleIndex = updatedSampleListData.findIndex(
      (item) => item.name === currentSampleIdentifier
    );

    if (sampleIndex !== -1) {
      // 只更新找到的特定样本
      const targetSample = updatedSampleListData[sampleIndex];

      // 更新该样本中匹配的考核项目
      targetSample.qaTaskSampleItemVoList =
        targetSample.qaTaskSampleItemVoList.map((item: any) => {
          if (item.itemName === sampleDetails.itemName) {
            // 确保rules.rulesStorage已经是字符串
            const rulesStorage =
              typeof rules.rulesStorage === 'string'
                ? rules.rulesStorage
                : JSON.stringify(rules.rulesStorage);

            if (curSelectedStandardType === 'correct') {
              return {
                ...item,
                condition: rules.rules,
                conditionTwo: rulesStorage,
                conditionThree: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'suspicious') {
              return {
                ...item,
                conditionFour: rules.rules,
                conditionFive: rulesStorage,
                conditionSix: rules.rulesView,
              };
            } else if (curSelectedStandardType === 'error') {
              return {
                ...item,
                conditionSeven: rules.rules,
                conditionEight: rulesStorage,
                conditionNine: rules.rulesView,
              };
            }
          }
          return item;
        });
    }

    // 更新状态
    setSampleListData(updatedSampleListData);
  };

  /**
   * @TODO 获取配置任务参数
   */
  const handleParams = async () => {
    try {
      const form2 = await formRefTask.current?.handleFormParams();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce((promises, item) => {
        const formRef = listRefs.current[item.name];
        if (formRef) {
          promises.push(async () => {
            return formRef?.getFieldValue('value') ?? '';
          });
        }
        return promises;
      }, [] as Array<() => Promise<any>>);
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }

      // 直接使用已经生成好的sampleVos，避免重新构建导致数据丢失
      const sampleListDataNew = sampleListData.map((item) => {
        // 确保item.sampleVos存在并且包含正确的字段
        if (!item.sampleVos || item.sampleVos.length === 0) {
          // 如果没有生成过编号，使用原来的逻辑构建
          let sampleNumListTemp: any = [];
          sampleNumList.forEach((item2) => {
            sampleNumListTemp.push({
              assessedLabCode: item2.assessedLabCode,
              assessedLabId: item2.assessedLabId,
              assessedLabName: item2.assessedLabName,
              sampleBox: item2.sampleBox || '',
              sampleCode: item2[item.name] || '',
            });
          });
          item.sampleVos = sampleNumListTemp;
        } else {
          // 确保每个sampleVos项都有sampleCode字段
          item.sampleVos = item.sampleVos.map((sampleVo: any) => {
            // 从sampleNumList中查找对应机构的编号
            const matchedSampleNum = sampleNumList.find(
              (numItem: any) => numItem.assessedLabId === sampleVo.assessedLabId
            );

            return {
              ...sampleVo,
              sampleBox:
                sampleVo.sampleBox || matchedSampleNum?.sampleBox || '',
              // 增强数据合并逻辑，确保即使sampleCode为空字符串也能使用表格中的数据
              sampleCode:
                sampleVo.sampleCode && sampleVo.sampleCode.trim() !== ''
                  ? sampleVo.sampleCode
                  : matchedSampleNum?.[item.name] || '',
            };
          });
        }
        return item;
      });

      // 处理数据
      const params = {
        ...form2,
        qaTaskSampleVos: sampleListDataNew,
      };
      return params;
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 配置任务参数-校验
   */
  const handleValidateFields = async () => {
    try {
      await formRefTask.current?.handleValidateFields();
      // 校验参考结果
      // 先创建一个只包含非null和非undefined ref对象的数组
      const validRefsPromises = sampleListData.reduce((promises, item) => {
        const formRef = listRefs.current[item.name];
        if (formRef) {
          promises.push(async () => {
            await formRef?.validateFields();
            return formRef?.getFieldValue('value') ?? '';
          });
        }
        return promises;
      }, [] as Array<() => Promise<any>>);
      try {
        const results = await Promise.all(
          validRefsPromises?.map((promiseFn: any) => promiseFn())
        );
        results.forEach((result, index) => {
          sampleListData[index].value = result;
        });
      } catch (error) {
        throw new Error('请完善样品参考结果');
      }
      if (!sampleNumList.length) {
        throw new Error('还未随机生成样本编号');
      }
      // 随机编号临时数据
      let sampleNumListTemp: any = [];
      const sampleListDataNew = sampleListData.map((item) => {
        sampleNumListTemp = [];
        sampleNumList.forEach((item2) => {
          sampleNumListTemp.push({
            assessedLabCode: item2.assessedLabCode,
            assessedLabId: item2.assessedLabId,
            assessedLabName: item2.assessedLabName,
            sampleBox: item2.sampleBox,
            sampleCode: item2[item.name],
          });
        });
        item.sampleVos = sampleNumListTemp;
        return item;
      });
      // 校验所有样品是否绑定样品编码
      const isEnty = sampleNumListTemp.some((item: any) => !item.sampleCode);
      if (isEnty) {
        throw new Error('有样本还未分配样本编号，请重新生成样本编号');
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = async () => {
    try {
      if (baseInfo?.id) {
        const { code, data, msg } = await getQATask(baseInfo?.id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          // ========================================
          const qaTaskSampleVosNew = cloneDeep(data?.qaTaskSampleVos);
          let newSampleListData: any = [];
          if (qaTaskSampleVosNew) {
            qaTaskSampleVosNew.forEach((item: any) => {
              item.qaTaskSampleItemVoList.forEach((item2: any) => {
                item2.code = item.name;
              });
            });
            newSampleListData = qaTaskSampleVosNew;
          } else {
            newSampleListData = tableData;
          }
          setSampleListData([...newSampleListData]);
          // ========================================

          // ========================================
          const { qaTaskSampleVos } = data;
          if (qaTaskSampleVos?.length) {
            const tempMap: any = {};
            qaTaskSampleVos.forEach((item: any) => {
              item.sampleVos.forEach((sampleVo: any) => {
                // 如果实验室ID已在映射表中，则更新样本信息
                if (tempMap[sampleVo.assessedLabId]) {
                  tempMap[sampleVo.assessedLabId][item.name] =
                    sampleVo.sampleCode;
                } else {
                  // 否则将实验室信息和当前样本信息放入映射表
                  tempMap[sampleVo.assessedLabId] = {
                    assessedLabId: sampleVo.assessedLabId,
                    assessedLabCode: sampleVo.assessedLabCode,
                    assessedLabName: sampleVo.assessedLabName,
                    sampleBox: sampleVo.sampleBox,
                    [item.name]: sampleVo.sampleCode,
                  };
                }
              });
            });
            const result = Object.values(tempMap);
            setSampleNumList(result);
            // setSampleListData(baseInfo.qaTaskSampleVos);
            setSampleListData(qaTaskSampleVos);
          }
          // ========================================
        } else {
          messageApi.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    // 初始样本列表
    const qaTaskSampleVosNew = cloneDeep(baseInfo.qaTaskSampleVos);
    let newSampleListData: any = [];
    if (qaTaskSampleVosNew) {
      qaTaskSampleVosNew.forEach((item: any) => {
        item.qaTaskSampleItemVoList.forEach((item2: any) => {
          item2.code = item.name;
        });
      });
      newSampleListData = qaTaskSampleVosNew;
    } else {
      newSampleListData = tableData;
    }
    setSampleListData([...newSampleListData]);
  }, []);

  useEffect(() => {
    const sampleListDataNew = cloneDeep(sampleListData);
    if (sampleListDataNew?.length) {
      sampleListDataNew?.forEach((_item) => {
        let CopyQaTaskSampleItemVoList = cloneDeep(
          _item?.qaTaskSampleItemVoList
        );
        const checked = assessmentCheckBox
          ?.filter((Item) => Item.check === 1)
          ?.map((_item2: any) => {
            let Item = {
              code: _item.name, // 样本编号
              itemName: _item2.name,
              ratio: _item2.ratio,
              condition: '',
              conditionTwo: '',
              conditionThree: '',
              conditionFour: '',
              conditionFive: '',
              conditionSix: '',
              conditionSeven: '',
              conditionEight: '',
              conditionNine: '',
            };
            // 保留映射原有已选规则
            if (CopyQaTaskSampleItemVoList.length) {
              CopyQaTaskSampleItemVoList?.forEach((_item3: any) => {
                if (_item3.itemName === _item2.name) {
                  Item = {
                    ...Item,
                    condition: _item3.condition ?? '',
                    conditionTwo: _item3.conditionTwo ?? '',
                    conditionThree: _item3.conditionThree ?? '',
                    conditionFour: _item3.conditionFour ?? '',
                    conditionFive: _item3.conditionFive ?? '',
                    conditionSix: _item3.conditionSix ?? '',
                    conditionSeven: _item3.conditionSeven ?? '',
                    conditionEight: _item3.conditionEight ?? '',
                    conditionNine: _item3.conditionNine ?? '',
                  };
                }
              });
            }
            return Item;
          });
        _item.qaTaskSampleItemVoList = checked;
      });
      setSampleListData([...sampleListDataNew]);
    }
  }, [assessmentCheckBox]);

  useEffect(() => {
    // 随机编号表头
    const header = sampleListData.map((item, idx) => {
      return {
        title: item.name,
        dataIndex: item.name,
        key: item.name,
        hideInSearch: true,
      };
    });
    // 初始样本列表
    setSampleNumHeader([...header]);
  }, [sampleListData]);

  useEffect(() => {
    // 初始化随机生成编码表格
    const { qaTaskSampleVos } = baseInfo;
    if (qaTaskSampleVos?.length) {
      const tempMap: any = {};
      qaTaskSampleVos.forEach((item: any) => {
        item.sampleVos.forEach((sampleVo: any) => {
          // 如果实验室ID已在映射表中，则更新样本信息
          if (tempMap[sampleVo.assessedLabId]) {
            tempMap[sampleVo.assessedLabId][item.name] = sampleVo.sampleCode;
          } else {
            // 否则将实验室信息和当前样本信息放入映射表
            tempMap[sampleVo.assessedLabId] = {
              assessedLabId: sampleVo.assessedLabId,
              assessedLabCode: sampleVo.assessedLabCode,
              assessedLabName: sampleVo.assessedLabName,
              sampleBox: sampleVo.sampleBox,
              [item.name]: sampleVo.sampleCode,
            };
          }
        });
      });
      const result = Object.values(tempMap);
      setSampleNumList(result);
      setSampleListData(baseInfo.qaTaskSampleVos);
    }
  }, [baseInfo]);

  useEffect(() => {
    // 参考结果赋值
    if (sampleListData?.length) {
      sampleListData.forEach((item: any) => {
        if (listRefs.current[item.name]) {
          listRefs.current[item.name]?.setFieldValue('value', item.value);
          listRefs.current[item.name]?.setFieldValue('realCode', item.realCode);
        }
      });
    }
  }, [sampleListData]);

  const props: UploadProps = {
    action: import.meta.env.VITE_URL + '/qa/tasks/importSampleCode',
    headers: { Authorization: 'Bearer ' + token },
    data: { taskId: baseInfo?.id },
    onChange: ({ file, fileList }) => {
      if (file.status === 'done') {
        if (file.response?.code === 200) {
          getDetailData();
          messageApi.success('上传成功');
        } else {
          messageApi.error(file.response?.msg);
        }
      }
    },
    showUploadList: false,
    capture: undefined,
  };

  // 处理配置变化
  const handleConfigChange = (
    sampleIndex: number,
    field: string,
    value: any
  ) => {
    setNumberingConfigs((prev) => ({
      ...prev,
      [sampleIndex]: {
        ...prev[sampleIndex],
        [field]: value,
      },
    }));
  };

  const handleConfigChangeAll = (field: string, value: any) => {
    setRadioGroupValue(value);
  };

  // 生成配置块的函数
  const renderNumberingBlocks = () => {
    const RadioGroup = (
      <div className="w-full flex flex-row flex-nowrap gap-4 mb-4">
        <span className="mr-2">编号生成方式:</span>
        <Radio.Group
          value={radioGroupValue}
          onChange={(e) => handleConfigChangeAll('type', e.target.value)}
        >
          <Radio value="uniform">全部统一为一个编号</Radio>
          <Radio value="random">随机生成编号</Radio>
          <Radio value="customize">自增编号</Radio>
        </Radio.Group>
      </div>
    );

    const sampleList = sampleListData?.map((item, index) => {
      let config: any = numberingConfigs[index] || { type: 'uniform' };

      return (
        <div
          key={index}
          className="w-full p-4 border border-solid border-[#D9D9D9] rounded-md"
        >
          <div className="text-lg font-medium mb-2">{item.name}</div>
          {/* 根据类型显示不同配置 */}
          {radioGroupValue === 'uniform' && (
            <div className="w-full flex flex-row flex-nowrap items-center gap-2 mb-4">
              <span className="text-red-500 mr-1">*</span>
              <span className="mr-2">统一编号:</span>
              <Input
                value={config.uniformCode || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleConfigChange(index, 'uniformCode', e.target.value)
                }
                placeholder="请输入统一编号"
                style={{ width: '200px' }}
              />
            </div>
          )}
          {radioGroupValue === 'random' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>后缀位数:</span>
                </span>
                <Select
                  value={config.suffixDigits}
                  onChange={(value) =>
                    handleConfigChange(index, 'suffixDigits', value)
                  }
                  options={suffixNumList}
                  style={{ width: '200px' }}
                  placeholder="请选择后缀位数"
                />
                <span className="text-red-500 ml-2">示例: XJK001</span>
              </div>
            </div>
          )}
          {radioGroupValue === 'customize' && (
            <div className="flex flex-col gap-4">
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] text-right inline-block">
                  编号前缀:
                </span>
                <Input
                  value={config.prefix || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'prefix', e.target.value)
                  }
                  placeholder="请输入前缀"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  最多支持三位字母、数字
                </span>
              </div>
              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>起始编号:</span>
                </span>
                <Input
                  value={config.digit || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleConfigChange(index, 'digit', e.target.value)
                  }
                  placeholder="请输入起始编号"
                  style={{ width: '200px' }}
                />
                <span className="text-red-500 ml-2">
                  输入01则会从01开始编号
                </span>
              </div>

              <div className="w-full flex flex-row flex-nowrap items-center gap-2">
                <span className="w-[100px] flex items-center justify-end">
                  <span className="text-red-500">*</span>
                  <span>固定编号长度:</span>
                </span>
                <Radio.Group
                  value={config.incrementCodeLengthType || '0'}
                  onChange={(e: any) => {
                    console.log('选中的值:', e.target.value); // 添加调试输出
                    handleConfigChange(
                      index,
                      'incrementCodeLengthType',
                      e.target.value
                    );
                  }}
                >
                  <Radio value="0">不固定长度</Radio>
                  <Radio value="1">固定长度</Radio>
                </Radio.Group>
                {config.incrementCodeLengthType === 1 && (
                  <Input
                    value={config.incrementLength || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleConfigChange(
                        index,
                        'incrementLength',
                        e.target.value
                      )
                    }
                    placeholder="请输入"
                    style={{ width: '200px' }}
                  />
                )}
              </div>
              <div style={{ color: 'gray', fontSize: '12px' }}>
                设置固定长度后编号位数不够时会自动补0，补0是指在数字编号前添加零，使其达到固定位数的操作。
                例如固定3位长度，001 002 003 ... 100 101
              </div>
            </div>
          )}
        </div>
      );
    });

    return (
      <div>
        {RadioGroup}
        <div
          className="w-full flex-1 overflow-x-hidden overflow-y-auto pr-4  pd-10"
          style={{ height: '400px' }}
        >
          {sampleList}
        </div>
      </div>
    );
  };

  /**
   * @TODO 随机生成样本编号-V2
   * 对第一版 handleRandomlySampleNum 的升级
   * 支持不同样本编号规则
   */
  const handleRandomlySampleNumV2 = async () => {
    // 判断获取的编号配置数量是否与 sampleListData 数量一致
    if (Object.keys(numberingConfigs).length !== sampleListData.length) {
      message.warning('请先配置所有样本编号规则');
      return;
    }

    // 遍历 numberingConfigs 中的配置, 如果每项的 type 都为uniform
    // 则认为所有的样本编号规则都为统一编号
    const allIsUniform = Object.values(numberingConfigs)?.every(
      (item: Record<string, any>) => {
        return item?.type === 'uniform';
      }
    );

    // 获取被考核单位
    const { orgVos } = await formRefTask.current?.handleFormParams();
    if (!orgVos?.length) {
      message.warning('请先选择被考核单位');
      return;
    }

    try {
      setRandomlySampleLoading(true);

      // 创建一个深拷贝确保不影响原始数据
      const _sampleListData = cloneDeep(sampleListData);

      // 收集当前被考核单位的ID，用于后续过滤
      const currentOrgIds = new Set(
        orgVos.map((org: any) => org.assessedLabId || org.id || '')
      );

      // 清理样本数据中已不存在的被考核单位
      _sampleListData.forEach((sample) => {
        if (sample.sampleVos && sample.sampleVos.length > 0) {
          // 仅保留当前选中的被考核单位
          sample.sampleVos = sample.sampleVos.filter(
            (sampleVo: SampleVosItem) =>
              currentOrgIds.has(sampleVo.assessedLabId)
          );
        }
      });
      // 检查是否所有配置都是统一编号
      const IsUniform = radioGroupValue === 'uniform';

      // 如果所有的编号都是统一编号
      if (IsUniform) {
        // 先构建统一编号的 sampleNumList
        const newSampleNumList = orgVos.map((org: any) => {
          const result: any = {
            assessedLabId: org.assessedLabId || org.id || '',
            assessedLabCode: org.assessedLabCode || org.code || '',
            assessedLabName: org.assessedLabName || org.name || '',
            sampleBox: '',
          };

          // 为每种样本直接添加配置的统一编号
          _sampleListData.forEach((sample, idx) => {
            const config = numberingConfigs[idx];
            if (config?.type === 'uniform' && config?.uniformCode) {
              result[sample.name] = config.uniformCode;
            }
          });

          return result;
        });

        // 更新每个样本的 sampleVos
        _sampleListData.forEach((item, idx) => {
          const config = numberingConfigs[idx];
          if (config?.type === 'uniform' && config?.uniformCode) {
            // 确保每个样本都有 sampleVos
            if (!item.sampleVos || item.sampleVos.length === 0) {
              item.sampleVos = orgVos.map((org: any) => ({
                assessedLabId: org.assessedLabId || org.id || '',
                assessedLabCode: org.assessedLabCode || org.code || '',
                assessedLabName: org.assessedLabName || org.name || '',
                sampleBox: '',
                sampleCode: config.uniformCode,
              }));
            } else {
              // 构建一个Map用于快速查找
              const orgMap = new Map();

              // 将现有机构放入 Map 中
              item.sampleVos.forEach((orgSample: SampleVosItem) => {
                orgMap.set(orgSample.assessedLabId, orgSample);
                // 更新编号
                orgSample.sampleCode = config.uniformCode;
              });

              // 检查是否有缺失的机构，如果有则添加
              orgVos.forEach((org: any) => {
                const orgId = org.assessedLabId || org.id || '';
                if (!orgMap.has(orgId)) {
                  item.sampleVos.push({
                    assessedLabId: orgId,
                    assessedLabCode: org.assessedLabCode || org.code || '',
                    assessedLabName: org.assessedLabName || org.name || '',
                    sampleBox: '',
                    sampleCode: config.uniformCode,
                  });
                }
              });
            }
          }
        });

        setSampleListData(_sampleListData);
        setSampleNumList(newSampleNumList); // 更新表格数据源
        setIsOpenGenerateSampleNumModal(false);
        message.success('统一编号生成成功');
        return;
      }

      const IsRandom = radioGroupValue === 'random';

      // 处理随机编号的情况
      if (IsRandom) {
        // 调用新的后端接口
        const ranSampleCode = sampleListData.map((_, index) => {
          console.log(_, index, 11111111);

          const config = numberingConfigs[index];
          return {
            prefix: config?.prefix || '',
            digit: config?.suffixDigits || 0,
            label: _.name,
          };
        });

        // 调用新的后端接口
        const { code, data, msg }: any = await randomlySampleNumV2({
          num: orgVos?.length,
          contents: ranSampleCode,
          type: 'random',
        });

        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }

        // 如果 data 不是数组，则转换为数组
        const dataArray = Array.isArray(data) ? data : [data];

        // 初始化样本结构 - 确保每个样本的sampleVos只包含当前选中的被考核单位
        _sampleListData.forEach((sample) => {
          // 创建一个新的sampleVos数组，仅包含当前选中的机构
          const newSampleVos: SampleVosItem[] = [];

          // 为每个当前选中的机构准备一个样本项
          orgVos.forEach((org: any) => {
            const orgId = org.assessedLabId || org.id || '';

            // 尝试从现有sampleVos中找到这个机构
            const existingSampleVo = sample.sampleVos?.find(
              (vo: SampleVosItem) => vo.assessedLabId === orgId
            );

            if (existingSampleVo) {
              // 如果存在，重用它，但清除编号相关信息
              newSampleVos.push({
                ...existingSampleVo,
                sampleBox: '',
                sampleCode: '',
              });
            } else {
              // 如果不存在，创建新的
              newSampleVos.push({
                assessedLabId: orgId,
                assessedLabCode: org.assessedLabCode || org.code || '',
                assessedLabName: org.assessedLabName || org.name || '',
                sampleBox: '',
                sampleCode: '',
              });
            }
          });

          // 用新数组替换旧数组
          sample.sampleVos = newSampleVos;
        });

        // 处理数据并更新样本编号
        // 根据API返回数据结构，将样本编号分配给被考核单位
        orgVos.forEach((org: any, orgIndex: number) => {
          if (dataArray[orgIndex]) {
            const boxInfo = dataArray[orgIndex];
            const sampleBox = boxInfo.sampleBox || '';
            const samples = boxInfo.samples || [];

            // 为每种样本类型更新编号
            _sampleListData.forEach((sample, sampleIndex) => {
              // 找到对应的样本
              const sampleVos = sample.sampleVos || [];
              const orgSample = sampleVos.find(
                (item: SampleVosItem) =>
                  item.assessedLabId === (org.assessedLabId || org.id || '')
              );

              if (orgSample) {
                // 更新样本盒编号
                orgSample.sampleBox = sampleBox;

                // 根据编号类型分配样本编号
                if (radioGroupValue === 'uniform') {
                  orgSample.sampleCode =
                    numberingConfigs[sampleIndex]?.uniformCode || '';
                } else if (radioGroupValue === 'random') {
                  // orgSample.sampleCode = samples[sampleIndex] || '';
                  orgSample.sampleCode = samples || '';
                } else if (radioGroupValue === 'customize') {
                  // orgSample.sampleCode = samples[sampleIndex] || '';
                  orgSample.sampleCode = samples || '';
                }
              }
            });
          }
        });

        // 构建表格数据
        const newSampleNumList = orgVos.map((org: any, orgIndex: number) => {
          const orgId = org.assessedLabId || org.id || '';
          const boxInfo = dataArray[orgIndex] || {};
          console.log(orgId, boxInfo, 'boxInfo');

          let result: any = {
            assessedLabId: orgId,
            assessedLabCode: org.assessedLabCode || org.code || '',
            assessedLabName: org.assessedLabName || org.name || '',
            sampleBox: boxInfo.sampleBox || '',
          };

          // 为每种样本添加编号
          _sampleListData.forEach((sample, sampleIndex) => {
            // 如果是统一编号

            if (radioGroupValue === 'uniform') {
              result[sample.name] =
                numberingConfigs[sampleIndex]?.uniformCode || '';
            } else if (radioGroupValue === 'random') {
              // 如果是随机编号，从API返回的samples数组获取
              const samples = boxInfo.samples || [];
              result = { ...result, ...samples };
            } else if (radioGroupValue === 'customize') {
              // 如果是随机编号，从API返回的samples数组获取
              const samples = boxInfo.samples || [];
              result = { ...result, ...samples };
            }
          });
          console.log(result, 'result');

          return result;
        });

        // 修复随机编号模式下数据不同步问题
        // 确保从表格数据更新到样本数据
        _sampleListData.forEach((sample, sampleIndex) => {
          if (sample.sampleVos && sample.sampleVos.length > 0) {
            sample.sampleVos.forEach((sampleVo: SampleVosItem) => {
              // 从表格数据中找到对应的行
              const matchedRow = newSampleNumList.find(
                (row: any) => row.assessedLabId === sampleVo.assessedLabId
              );

              if (matchedRow) {
                // 确保从表格数据同步样本编号和样本盒
                sampleVo.sampleBox =
                  matchedRow.sampleBox || sampleVo.sampleBox || '';
                sampleVo.sampleCode =
                  matchedRow[sample.name] || sampleVo.sampleCode || '';
              }
            });
          }
        });

        // 更新状态
        setSampleListData(_sampleListData);
        setSampleNumList(newSampleNumList);
        setIsOpenGenerateSampleNumModal(false);
        message.success('样本编号生成成功');
      }

      const IsCustomize = radioGroupValue === 'customize';

      // 处理随机编号的情况
      if (IsCustomize) {
        // 调用新的后端接口

        // 构建新的请求参数结构，符合后端API要求
        const customizeSampleCode = Object.keys(numberingConfigs)
          .map((indexStr) => {
            const index = Number(indexStr);
            const config: any = numberingConfigs[index];
            const sample = sampleListData[index];
            return {
              prefix: config.prefix || '',
              digit: config.digit || '',
              incrementCodeLengthType: config.incrementCodeLengthType || '0',
              incrementLength: config.incrementLength || '0',
              label: sample.name,
              referenceResult: sample.value || '',
            };
          })
          .filter(Boolean);

        // 调用新的后端接口
        const { code, data, msg }: any = await randomlySampleNumV2({
          num: orgVos?.length,
          contents: customizeSampleCode,
          type: 'increment',
        });

        if (code !== codeDefinition.QUERY_SUCCESS) {
          message.error(msg);
          return;
        }

        // 如果 data 不是数组，则转换为数组
        const dataArray = Array.isArray(data) ? data : [data];

        // 初始化样本结构 - 确保每个样本的sampleVos只包含当前选中的被考核单位
        _sampleListData.forEach((sample) => {
          // 创建一个新的sampleVos数组，仅包含当前选中的机构
          const newSampleVos: SampleVosItem[] = [];

          // 为每个当前选中的机构准备一个样本项
          orgVos.forEach((org: any) => {
            const orgId = org.assessedLabId || org.id || '';

            // 尝试从现有sampleVos中找到这个机构
            const existingSampleVo = sample.sampleVos?.find(
              (vo: SampleVosItem) => vo.assessedLabId === orgId
            );

            if (existingSampleVo) {
              // 如果存在，重用它，但清除编号相关信息
              newSampleVos.push({
                ...existingSampleVo,
                sampleBox: '',
                sampleCode: '',
              });
            } else {
              // 如果不存在，创建新的
              newSampleVos.push({
                assessedLabId: orgId,
                assessedLabCode: org.assessedLabCode || org.code || '',
                assessedLabName: org.assessedLabName || org.name || '',
                sampleBox: '',
                sampleCode: '',
              });
            }
          });

          // 用新数组替换旧数组
          sample.sampleVos = newSampleVos;
        });

        // 处理数据并更新样本编号
        // 根据API返回数据结构，将样本编号分配给被考核单位
        orgVos.forEach((org: any, orgIndex: number) => {
          if (dataArray[orgIndex]) {
            const boxInfo = dataArray[orgIndex];
            const sampleBox = boxInfo.sampleBox || '';
            const samples = boxInfo.samples || [];

            // 为每种样本类型更新编号
            _sampleListData.forEach((sample, sampleIndex) => {
              // 找到对应的样本
              const sampleVos = sample.sampleVos || [];
              const orgSample = sampleVos.find(
                (item: SampleVosItem) =>
                  item.assessedLabId === (org.assessedLabId || org.id || '')
              );

              if (orgSample) {
                // 更新样本盒编号
                orgSample.sampleBox = sampleBox;

                // 根据编号类型分配样本编号
                if (radioGroupValue === 'uniform') {
                  orgSample.sampleCode =
                    numberingConfigs[sampleIndex]?.uniformCode || '';
                } else if (radioGroupValue === 'random') {
                  // orgSample.sampleCode = samples[sampleIndex] || '';
                  orgSample.sampleCode = samples || '';
                } else if (radioGroupValue === 'customize') {
                  // orgSample.sampleCode = samples[sampleIndex] || '';
                  orgSample.sampleCode = samples || '';
                }
              }
            });
          }
        });

        // 构建表格数据
        const newSampleNumList = orgVos.map((org: any, orgIndex: number) => {
          const orgId = org.assessedLabId || org.id || '';
          const boxInfo = dataArray[orgIndex] || {};
          console.log(orgId, boxInfo, 'boxInfo');

          let result: any = {
            assessedLabId: orgId,
            assessedLabCode: org.assessedLabCode || org.code || '',
            assessedLabName: org.assessedLabName || org.name || '',
            sampleBox: boxInfo.sampleBox || '',
          };

          // 为每种样本添加编号
          _sampleListData.forEach((sample, sampleIndex) => {
            // 如果是统一编号

            if (radioGroupValue === 'uniform') {
              result[sample.name] =
                numberingConfigs[sampleIndex]?.uniformCode || '';
            } else if (radioGroupValue === 'random') {
              // 如果是随机编号，从API返回的samples数组获取
              const samples = boxInfo.samples || [];
              result = { ...result, ...samples };
            } else if (radioGroupValue === 'customize') {
              // 如果是随机编号，从API返回的samples数组获取
              const samples = boxInfo.samples || [];
              result = { ...result, ...samples };
            }
          });
          console.log(result, 'result');

          return result;
        });

        // 修复随机编号模式下数据不同步问题
        // 确保从表格数据更新到样本数据
        _sampleListData.forEach((sample, sampleIndex) => {
          if (sample.sampleVos && sample.sampleVos.length > 0) {
            sample.sampleVos.forEach((sampleVo: SampleVosItem) => {
              // 从表格数据中找到对应的行
              const matchedRow = newSampleNumList.find(
                (row: any) => row.assessedLabId === sampleVo.assessedLabId
              );

              if (matchedRow) {
                // 确保从表格数据同步样本编号和样本盒
                sampleVo.sampleBox =
                  matchedRow.sampleBox || sampleVo.sampleBox || '';
                sampleVo.sampleCode =
                  matchedRow[sample.name] || sampleVo.sampleCode || '';
              }
            });
          }
        });

        // 更新状态
        setSampleListData(_sampleListData);
        setSampleNumList(newSampleNumList);
        setIsOpenGenerateSampleNumModal(false);
        message.success('样本编号生成成功');
      }
    } catch (error) {
      message.error('编号生成失败，请重试');
      console.error('随机生成编号失败:', error);
    } finally {
      setRandomlySampleLoading(false);
    }
  };

  // 添加选择行变更处理函数
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 添加删除选中行的处理函数
  const handleDeleteSelectedRows = () => {
    setDeleteLoading(true);

    try {
      // 删除选中的行
      const newSampleNumList = sampleNumList.filter(
        (item) => !selectedRowKeys.includes(item.assessedLabId || item._key)
      );

      // 更新表格数据
      setSampleNumList(newSampleNumList);

      // 更新样本数据，从每个样本的 sampleVos 中删除对应的被考核单位
      const updatedSampleListData = cloneDeep(sampleListData);
      updatedSampleListData.forEach((sample) => {
        if (sample.sampleVos && sample.sampleVos.length > 0) {
          sample.sampleVos = sample.sampleVos.filter(
            (sampleVo: SampleVosItem) =>
              !selectedRowKeys.includes(sampleVo.assessedLabId || sampleVo._key)
          );
        }
      });

      setSampleListData(updatedSampleListData);

      // 清空选中行
      setSelectedRowKeys([]);
    } catch (error) {
      messageApi.error('删除失败，请重试');
      console.error('删除失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 设置行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <>
      {contextHolder}
      {/* 任务配置 */}
      {!isUpdateJudgCriteria ? (
        <div className="mt-4">
          <TaskConfiguration
            onRef={formRefTask}
            setFormInfo={(val) => setAssessmentCheckBox(val)}
            taskConfigurationForm={baseInfo}
          />
        </div>
      ) : null}
      {/* 明细配置 */}
      <div className="mt-4">
        <BlockContainer title="明细配置" bodyStyle={{ padding: '20px' }}>
          <Row gutter={[25, 25]}>
            {sampleListData?.map((item: SampleData, idx: number) => (
              <Col xs={24} sm={8} md={8} key={item.name || Math.random()}>
                <BlockContainer
                  title={item.name}
                  hoverable
                  className="border-[#D9D9D9]"
                  extra={
                    sampleListData.length > 1 ? (
                      <Popconfirm
                        title="确认删除此样本？"
                        description="删除后,此样本已经生成的样本编号也会做对应删除"
                        onConfirm={() => handleDeleteSampleItem(item.name)}
                        okText="确定"
                        cancelText="取消"
                        key="delpro"
                      >
                        <a className="text-[red]"> 删除</a>
                      </Popconfirm>
                    ) : null
                  }
                >
                  <div className="h-[300px] w-full overflow-auto overflow-x-hidden">
                    <ProForm<{ table: any[] }>
                      formRef={
                        ((ref: any): void =>
                          (listRefs.current[item.name] = ref)) as any
                      }
                      {...formItemLayout}
                      layout="horizontal"
                      grid={true}
                      className="w-full"
                      submitter={false}
                      autoFocusFirstInput={false}
                      //@ts-ignore
                      onValuesChange={(values) => {
                        if (values.value || values.realCode) {
                          sampleListData.forEach((Items: any) => {
                            console.log(Items.name, item.name, values.value);
                            if (Items.name === item.name && values.value) {
                              Items.value = values.value;
                            }
                            if (Items.name === item.name && values.realCode) {
                              Items.realCode = values.realCode;
                            }
                          });
                          setSampleListData([...sampleListData]);
                        }
                      }}
                    >
                      <ProFormTextArea
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="value"
                        label="参考结果"
                        rules={[
                          { required: true, message: '参考结果为必填项' },
                        ]}
                      />

                      <ProFormText
                        colProps={{ span: 24 }}
                        labelCol={{ flex: 0.005 }}
                        name="realCode"
                        label="真实编号"
                      />
                      <div className="real-code-tips">被考核机构不可见</div>

                      <ProTable
                        key="id"
                        rowKey="id"
                        name="qaTaskSampleItemVoList"
                        className="w-full px-1"
                        toolBarRender={false}
                        options={false}
                        search={false}
                        bordered
                        columns={columns}
                        // dataSource={item?.qaTaskSampleItemVoList}
                        dataSource={item?.qaTaskSampleItemVoList.map(
                          (taskItem: any) => ({
                            ...taskItem,
                            sampleName: item.name, // 添加当前样本名称标识
                            sampleId: `sample_${item.name}_${Math.random()
                              .toString(36)
                              .substring(2, 10)}`, // 添加唯一标识
                          })
                        )}
                        pagination={false}
                        onChange={(newDataSource) => {
                          console.log(newDataSource);
                        }}
                      />
                    </ProForm>
                  </div>
                </BlockContainer>
              </Col>
            ))}
            <Col xs={24} sm={8} md={8}>
              <BlockContainer
                title={`第${sampleListData.length + 1}种样本`}
                className="border-[#D9D9D9]"
              >
                <div className="h-[300px] flex justify-center items-center">
                  <Tooltip title="点击添加样本">
                    <PlusCircleOutlined
                      className="text-[50px] text-[#A8A8A8] cursor-pointer"
                      onClick={handleAddSampleItem}
                    />
                  </Tooltip>
                </div>
              </BlockContainer>
            </Col>
          </Row>
          {!isUpdateJudgCriteria ? (
            <BlockContainer className="mt-6">
              <ProTable
                key="sampleBox"
                rowKey={(record) =>
                  record._key ||
                  record.assessedLabId ||
                  Math.random().toString(36).slice(2)
                }
                className="w-full"
                options={false}
                search={false}
                bordered
                rowSelection={rowSelection}
                columns={columns2}
                dataSource={sampleNumList}
                pagination={false}
                headerTitle={
                  <div className="w-full">
                    <div className="w-full flex flex-row flex-nowrap gap-4">
                      <Button
                        type="primary"
                        loading={randomlySampleLoading}
                        onClick={() => setIsOpenGenerateSampleNumModal(true)}
                      >
                        随机生成样本编号
                      </Button>
                      <Button
                        onClick={handleExport}
                        loading={loading2}
                        icon={<DownloadOutlined />}
                        type="default"
                      >
                        导出样本清单
                      </Button>
                      <Upload {...props}>
                        <Button
                          type="primary"
                          disabled={!sampleNumList?.length}
                          title={
                            !sampleNumList?.length ? '样本随机编号列表为空' : ''
                          }
                        >
                          使用excel更新样本编号
                        </Button>
                      </Upload>
                      <Popconfirm
                        title="确认删除所选记录?"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={handleDeleteSelectedRows}
                      >
                        <Button
                          danger
                          type="primary"
                          disabled={selectedRowKeys.length === 0}
                          loading={deleteLoading}
                        >
                          删除所选
                        </Button>
                      </Popconfirm>
                    </div>
                    <div className="w-full mt-4 text-sm text-red-500">
                      温馨提示：如您已经完成样本编号粘贴工作，可通过【导出样本清单】功能，将下方列表数据导出为excel，在excel中填写实际样本编号，最后通过【使用excel更新样本编号】导入系统。
                    </div>
                  </div>
                }
              />
            </BlockContainer>
          ) : null}
        </BlockContainer>
        {/* 标准判定弹窗 */}
        <StandardMaintenance
          close={() => {
            setSampleDetails(null);
            setStandardModel(false);
          }}
          open={standardModel}
          getRuler={queryRulesObj}
          rulesDetails={sampleDetails}
          taskId={baseInfo.id}
          taskType={baseInfo.assessmentType}
          curSelectedStandardType={curSelectedStandardType}
          onStandardTypeChange={(type) => setCurSelectedStandardType(type)}
        />
      </div>
      {/* 生成样本编号 Modal */}
      <Modal
        open={isOpenGenerateSampleNumModal}
        onCancel={() => setIsOpenGenerateSampleNumModal(false)}
        title="考核样编号生成规则"
        centered
        width={800}
        footer={
          <Button
            type="primary"
            onClick={() => {
              handleRandomlySampleNumV2();
            }}
          >
            开始生成编号
          </Button>
        }
      >
        <div className="w-full max-h-[70vh] overflow-hidden flex flex-col gap-4">
          <div className="bg-[#fff7f7] p-4 border border-solid border-[#ffccc7] rounded-md">
            <div className="text-[#f5222d] font-bold">
              【非常重要】温馨提示:{' '}
            </div>
            <div className="mt-2">
              1、使用"全部统一为一个编号"，所有被考核单位对应种类的样本均为该编号。例如：设置第1种样本统一编号为A01，则所有被考核单位第1种样本编号都将为A01；
            </div>
            <div className="mt-1">
              2、使用"随机生成编号"，编号前缀可不填，后缀随机数位数最多支持5位。例如：前缀设置为XJK，随机数选择3，则对应种类的考核样编号将为XJK001、XJK002...
            </div>
          </div>
          <div className="w-full flex-1 overflow-x-hidden overflow-y-auto pr-4  pd-10">
            {renderNumberingBlocks()}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EditOrganization;
