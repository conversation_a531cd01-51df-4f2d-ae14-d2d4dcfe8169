import { useRef, useState } from 'react';
import { But<PERSON>, message, Popconfirm, Space } from 'antd';
import { getDepartmentList } from '@/api/department';
import { addQATaskItem, delQATaskItem, getQATaskItemList } from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import { handleTree } from '@/utils/index';
import { formItemLayout } from '../data';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TaskListItem = {
  url: string;
  id: number;
  number: number;
  title: string;
  labels: {
    name: string;
    color: string;
  }[];
  state: string;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
};

type TTaskListByOrgProps = {
  id: string;
  readonly: boolean;
};

const TaskList: React.FC<TTaskListByOrgProps> = ({ id, readonly }) => {
  const actionRef = useRef<ActionType>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns<TaskListItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
      search: false,
    },
    {
      title: '参考结果',
      dataIndex: 'refResult',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 60,
      hideInTable: readonly,
      render: (text, record, _, action) => [
        <Popconfirm
          title="删除此行？"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
          key="del"
        >
          <Button danger key="editable" type="text">
            删除
          </Button>
        </Popconfirm>,
        ,
      ],
    },
  ];

  const formRef = useRef<FormInstance>(null);

  /**
   * @TODO 新增
   */
  const handleSave = async (values: any) => {
    const params = {
      ...values,
      taskId: id,
    };
    setLoading(true);
    try {
      const { code, msg }: any = await addQATaskItem(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
        formRef.current?.resetFields();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);

  /**
   * @TODO 删除
   */
  const handleDelete = async (ids: number) => {
    try {
      const { code, msg } = await delQATaskItem({
        id,
        ids,
      });
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  return (
    <BlockContainer title="任务明细">
      {!readonly && (
        <ProForm
          formRef={formRef}
          // layout="inline"
          grid={true}
          onFinish={handleSave}
          initialValues={{}}
          submitter={{
            render: (_, dom) => (
              <div className="flex justify-end items-center mb-4">
                <Space size="small">
                  <Button
                    type="primary"
                    loading={loading}
                    htmlType="submit"
                    icon={<PlusOutlined />}
                  >
                    添加
                  </Button>
                  {/* <Button type="primary" loading={loading} htmlType="submit">
                  模板下载
                </Button>
                <Button type="primary" loading={loading} htmlType="submit">
                  批量导入
                </Button> */}
                </Space>
              </div>
            ),
          }}
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormText
            name="sampleCode"
            label="样本编号"
            placeholder="请输入样本编号"
            rules={[{ required: true, message: '请输入样本编号' }]}
            colProps={formItemLayout}
          />
          <ProFormText
            name="refResult"
            label="参考结果"
            placeholder="请输入参考结果"
            rules={[{ required: true, message: '请输入参考结果' }]}
            colProps={formItemLayout}
          />
        </ProForm>
      )}
      <ProTable<TaskListItem>
        search={false}
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            id: id,
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { rows, total } = await getQATaskItemList(param);
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        form={{
          // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
              };
            }
            return values;
          },
        }}
        pagination={{size: 'default',showSizeChanger: true,
          pageSize: 10,
        }}
        dateFormatter="string"
        headerTitle="考核任务"
      />
    </BlockContainer>
  );
};
export default TaskList;
