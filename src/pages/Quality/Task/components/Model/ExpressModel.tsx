/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import {
  Button,
  Col,
  message,
  Modal,
  Radio,
  Row,
  Space,
  Tabs,
  Upload,
} from 'antd';
import type { TabsProps, UploadProps } from 'antd';
import {
  blindlyImportExpress,
  queryExpressInfoEdit,
  queryExpressList,
  queryOrgList,
  randomImportExpress,
  sendExpressTask,
} from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import type { ProColumns } from '@ant-design/pro-components';
import {
  ActionType,
  ProForm,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import DownloadButton from '@/components/DownloadButton';

type TEditProps = {
  close: () => void;
  onSubmitClose?: () => void; // 填报页面提交
  detailId?: string;
};
interface Result {
  name: string;
  value: string;
}
interface SampleData {
  sampleBox: string;
  sampleCode: string[];
  sampleBenType: string;
  assessedLabCode: string;
  assessedLabId: string;
  assessedLabName: string;
}
interface OrgListData {
  label: string;
  value: string;
  code: string;
}
type DataSourceType = any;

const ExpressModel: React.FC<TEditProps> = ({
  close,
  detailId,
  onSubmitClose,
}) => {
  // 表头
  const [tableTitleList, setTableTitleList] = useState<any[]>([]);
  // 当前选中样本盒
  const [sampleItem, setSampleItem] = useState<any>({});
  // 参考结果
  const [sampleContent, setSampleContent] = useState<any[]>([]);
  // 导入结果
  const [importList, setImportList] = useState<any>({});
  // 导入结果弹窗
  const [openImportList, setOpenImportList] = useState<boolean>(false);
  // tabs选中
  const [tabsSelectKey, setTabsSelectKey] = useState<string>('true');
  // 导入显示列表
  const [dataSource, setDataSource] = useState<any[]>([]);
  const actionRef = useRef<ActionType>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingSub, setLoadingSub] = useState<boolean>(false);
  // 任务类型
  const [taskType, setTaskType] = useState<string>('');
  // 启用任务
  const [loadingSend, setLoadingSend] = useState<boolean>(false);
  // 被考核机构-弹窗
  const [orgModel, setOrgModel] = useState<boolean>(false);
  // 被考核机构-ref
  const formRef = useRef<ProFormInstance>(null);
  // 被考核机构-列表
  const [orgList, setOrgList] = useState<OrgListData[]>([]);
  // 样本数据量
  const [sampDataListLength, setSampDataListLength] = useState<number>(0);
  const { token } = useTokenStore();

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
      key: 'assessedLabName',
      render: (text, record) =>
        !record?.assessedLabName ? (
          <a onClick={() => handleOrg(record)}>维护考核机构</a>
        ) : (
          <a onClick={() => handleOrg(record)}>{record?.assessedLabName}</a>
        ),
    },
    ...(taskType === 'blind'
      ? [
          {
            title: '样本盒编号',
            dataIndex: 'sampleBox',
            key: 'sampleBox',
          },
        ]
      : []),
    ...(taskType === 'blind' ? tableTitleList : []),
    ...(taskType === 'random'
      ? [
          {
            title: '样本类型',
            dataIndex: 'sampleBenType',
            key: 'sampleBenType',
            hideInSearch: true,
          },
          {
            title: '样本编号',
            dataIndex: 'sampleCode',
            key: 'sampleCode',
          },
        ]
      : []),
  ];

  // 导入表格
  const columns2: ProColumns<Record<string, any>>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      valueType: 'index',
      width: 100,
    },
    ...(taskType === 'blind'
      ? [
          {
            title: tabsSelectKey === 'org' ? '被考核机构' : '样本盒编号',
            dataIndex: 'value',
            key: 'value',
          },
        ]
      : []),
    ...(taskType === 'random'
      ? [
          {
            title: tabsSelectKey === 'org' ? '被考核机构' : '样本编号',
            dataIndex: 'value',
            key: 'value',
          },
        ]
      : []),
  ];

  const items: TabsProps['items'] = [
    {
      key: 'true',
      label: <span>导入成功（{importList.true?.length}）</span>,
    },
    {
      key: 'org',
      label: <span>未匹配的机构名称（{importList.org?.length}）</span>,
    },
    {
      key: 'false',
      label: <span>未匹配的编号（{importList.false?.length}）</span>,
    },
  ];

  /**
   * @TODO 列表初始数据
   */
  const onTabsChange = async (key: string) => {
    setTabsSelectKey(key);
    setDataSource(importList[key] ?? []);
  };

  /**
   * @TODO 列表初始数据
   * @params 查询列表参数
   */
  const handleQueryExpressList = async (param?: any) => {
    try {
      if (detailId) {
        const params = {
          id: detailId,
          ...param,
        };
        setLoading(true);
        const { code, data, msg }: any = await queryExpressList(params);
        if (code === codeDefinition.QUERY_SUCCESS) {
          const {
            sampleMYList,
            sampleSJList,
            sampleType,
            sampleContent,
            taskType,
          } = data;
          // 参考结果
          setSampleContent(sampleContent);
          setTaskType(taskType);
          // 表头数据
          if (taskType === 'blind') {
            setTableTitleList(
              sampleType.map((item: string) => {
                return {
                  title: item,
                  dataIndex: item,
                  key: item,
                  hideInSearch: true,
                };
              })
            );
          }
          let sampleData = [];
          // 盲样-表格数据
          if (taskType === 'blind') {
            sampleData = sampleMYList?.map((item: SampleData, idx: number) => {
              const samplCodeobj: any = {};
              item.sampleCode?.forEach((sample: string, index: number) => {
                samplCodeobj[`第${index + 1}种样本`] = sample;
              });
              return {
                assessedLabCode: item.assessedLabCode,
                assessedLabId: item.assessedLabId,
                assessedLabName: item.assessedLabName,
                sampleBox: item.sampleBox,
                ...samplCodeobj,
              };
            });
          }
          // 随机-表格数据
          if (taskType === 'random') {
            sampleData = sampleSJList?.map((item: SampleData, idx: number) => {
              return {
                assessedLabCode: item.assessedLabCode,
                assessedLabId: item.assessedLabId,
                assessedLabName: item.assessedLabName,
                sampleCode: item.sampleCode,
                sampleBenType: item.sampleBenType,
              };
            });
          }
          setSampDataListLength(sampleData.length);
          return sampleData;
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @TODO 被考核机构选中
   */
  const handleCheckOrg = async () => {
    try {
      const radioSelect = await formRef.current?.getFieldValue('assessedLabId');
      const checkBoxObj: OrgListData = orgList?.filter(
        (item) => item.value === radioSelect
      )![0];
      const params: any = {
        assessedLabId: checkBoxObj.value,
        assessedLabCode: checkBoxObj.code,
        assessedLabName: checkBoxObj.label,
      };
      if (taskType === 'blind') {
        params['sampleBox'] = sampleItem.sampleBox;
      }
      if (taskType === 'random') {
        params['sampleCode'] = sampleItem.sampleCode;
      }
      setLoadingSub(true);
      const { code, data, msg }: any = await queryExpressInfoEdit(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        setOrgModel(false);
        // handleQueryExpressList()
        tableReload();
        formRef.current?.resetFields();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoadingSub(false);
    }
  };

  /**
   * @TODO 维护考核机构按钮
   */
  const handleOrg = async (sampleObj: any) => {
    setSampleItem(sampleObj);
    setOrgModel(true);
    sampleObj.assessedLabId &&
      formRef.current?.setFieldsValue({
        assessedLabId: sampleObj.assessedLabId,
      });
  };

  /**
   * @TODO 导入考核机构
   */
  const uploadProps: UploadProps = {
    name: 'file',
    maxCount: 1,
    showUploadList: false,
    action: taskType === 'blind' ? blindlyImportExpress : randomImportExpress,
    onChange: (info) => {
      handleUploadFiles(info);
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
    capture: undefined,
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file } = info;
    if (file.status === 'done') {
      const { msg, data, code } = file.response;
      if (file.response && code === 200) {
        message.success(msg);
        for (let i in data) {
          data[i] = data[i] && data[i].map((item: string) => ({ value: item }));
        }
        setOpenImportList(true);
        setImportList(data);
        setDataSource(data.true ?? []);
        // handleQueryExpressList()
        tableReload();
      } else {
        Modal.error({
          title: file.response.msg,
          content: (file.response.data || []).map((item: any, idx: any) => (
            <div key={idx}>{item}</div>
          )),
        });
      }
    }
  };

  /**
   * @TODO 考核机构列表
   */
  const queryOrg = async () => {
    try {
      let res: any = [];
      const { code, data, msg } = await queryOrgList();
      if (code === codeDefinition.QUERY_SUCCESS) {
        res = data.map((item: any) => ({
          label: item.deptName,
          value: item.deptId,
          code: item.deptCode,
        }));
        setOrgList(res);
      } else {
        setOrgList([]);
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  };

  /**
   * @TODO 表格刷新
   */
  const tableReload = async () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 任务下发
   */
  const handleSend = async () => {
    try {
      const params = {
        id: detailId,
      };
      setLoadingSend(true);
      const { code, data, msg } = await sendExpressTask(params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
        onSubmitClose && onSubmitClose();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoadingSend(false);
    }
  };

  useEffect(() => {
    // 被考核机构
    queryOrg();
  }, []);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BlockContainer title="样本参考结果">
          {sampleContent?.map((item: Result) => (
            <div className="text-[red] mb-1 text-[15px]">
              <span>{item.name}：</span>
              <span>{item.value ?? '-'}</span>
            </div>
          ))}
        </BlockContainer>
        <BlockContainer title="被考核机构维护" className="mt-4">
          <ProTable
            key="id"
            className="w-full"
            actionRef={actionRef}
            options={{
              setting: {
                listsHeight: 400,
              },
            }}
            loading={loading}
            bordered
            columns={columns}
            request={async (params) => {
              const data = await handleQueryExpressList(params);
              return {
                data: data,
                total: data.length,
                success: true,
              };
            }}
            pagination={false}
            toolBarRender={() => [
              <Space className="">
                <DownloadButton
                  url={
                    taskType === 'blind'
                      ? '/system/taskDetailsOrg/exportBlindData'
                      : '/system/taskDetailsOrg/exportRandomData'
                  }
                  params={{ taskId: detailId }}
                >
                  导出样本清单
                </DownloadButton>
                <Upload {...uploadProps}>
                  <Button type="default">导入考核机构</Button>
                </Upload>
              </Space>,
            ]}
          />
        </BlockContainer>
      </div>
      <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
        <Button onClick={close} type="default">
          保存发样信息
        </Button>
      </div>
      {/* 被考核机构 */}
      <Modal
        open={orgModel}
        width="55%"
        title="被考核机构"
        onCancel={() => setOrgModel(false)}
        footer={[
          <Space className="w-[60px] m-auto flex justify-around">
            <Button
              key="submit"
              loading={loadingSub}
              type="primary"
              onClick={handleCheckOrg}
            >
              确认
            </Button>
            <Button
              key="close"
              onClick={() => {
                setOrgModel(false);
                formRef.current?.resetFields();
              }}
            >
              关闭
            </Button>
          </Space>,
        ]}
      >
        <ProForm
          formRef={formRef}
          layout="horizontal"
          className="w-[98%]"
          grid={true}
          submitter={false}
          onValuesChange={(_: any, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProForm.Item label="被考核机构" name="assessedLabId">
            <Radio.Group className="flex flex-col">
              <Row gutter={[0, 10]} className="mt-2 pl-2">
                {orgList?.map((_item) => {
                  return (
                    <Col key={_item.value} span={8}>
                      <Radio value={_item.value}>{_item.label}</Radio>
                    </Col>
                  );
                })}
              </Row>
            </Radio.Group>
          </ProForm.Item>
        </ProForm>
      </Modal>
      {/* 被考核机构导入结果弹窗 */}
      <Modal
        open={openImportList}
        width="55%"
        title="被考核机构导入结果"
        onCancel={() => setOpenImportList(false)}
        footer={[
          <Space className="w-[60px] m-auto flex justify-around">
            <Button
              key="close3"
              onClick={() => {
                setOpenImportList(false);
                formRef.current?.resetFields();
              }}
            >
              关闭
            </Button>
          </Space>,
        ]}
      >
        <Tabs defaultActiveKey="1" items={items} onChange={onTabsChange} />
        <BlockContainer className="mt-1">
          <ProTable
            key="id"
            className="w-full"
            bordered
            columns={columns2}
            dataSource={dataSource}
            pagination={false}
            toolBarRender={false}
            search={false}
          />
        </BlockContainer>
        ,
      </Modal>
    </div>
  );
};

export default ExpressModel;
