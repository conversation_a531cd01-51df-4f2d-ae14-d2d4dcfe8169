/* eslint-disable jsx-a11y/anchor-is-valid */

/* eslint-disable @typescript-eslint/no-unused-vars */

/*
 * @Author: LGX
 * @Date: 2024-03-12 17:40:15
 * @LastEditors: LGX
 * @LastEditTime: 2024-03-28 11:25:56
 * @FilePath: \xr-qc-jk-web\src\pages\Quality\Task\components\Model\StandardQuoteModel.tsx
 * @Description: 标准判定-引用弹窗
 *
 */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Modal } from 'antd';
import { queryStandardList } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

type TEditProps = {
  close: () => void;
  getRulerItem: (val: any[]) => void;
  open: boolean;
  detailId?: string;
  rulesType: 'correct' | 'suspicious' | 'error';
};

interface DataType {
  key: string | number;
  name: string;
  value: any;
}
type QualityTaskItem = Record<string, any>;

const StandardQuoteModel: React.FC<TEditProps> = ({
  close,
  detailId,
  open,
  getRulerItem,
  rulesType,
}) => {
  const [tableData, setTableData] = useState<DataType[]>([]);
  const [pageSize, setPageSize] = useState<number>(10);
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '判定标准',
      dataIndex: 'conditionThree',
      key: 'conditionThree',
    },
    {
      title: '操作',
      key: 'action',
      width: 110,
      render: (_, record) => <a onClick={() => handleQuote(record)}>引用</a>,
    },
  ];

  /**
   * @TODO 引用
   */
  const handleQuote = async (rules: any) => {
    // 结构
    const viewStrage = rules.conditionTwo && JSON.parse(rules.conditionTwo);
    getRulerItem(viewStrage);
    close();
    message.success('引用成功');
  };
  const handleCancel = async () => {
    close();
  };

  useEffect(() => {
    if (open) {
      actionRef.current?.reload();
    }
  }, [open]);

  return (
    <div className="flex flex-col h-full w-full">
      <Modal
        open={open}
        width="40%"
        title="引用历史标准"
        onCancel={handleCancel}
        footer={false}
        styles={{
          body: {
            paddingBottom: '18px',
          },
        }}
      >
        <div className="h-[450px] mt-5">
          <ProTable<QualityTaskItem>
            columns={columns}
            actionRef={actionRef}
            cardBordered
            bordered
            scroll={{ y: 380 }}
            sticky
            toolBarRender={false}
            request={async (params) => {
              const param = {
                pageNum: params.current,
                pageSize: params.pageSize || pageSize,
                rulesType,
              };
              const { code, rows, total, msg } = await queryStandardList(param);
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
                return {};
              }
              return {
                data: rows ?? [],
                total: total ?? 0,
                success: true,
              };
            }}
            editable={{
              type: 'multiple',
            }}
            rowKey={(record, index) =>
              record.id || `row-${index}-${Date.now()}`
            }
            search={false}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              pageSize: pageSize,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, size) => {
                setPageSize(size);
                actionRef.current?.reload();
              },
            }}
            dateFormatter="string"
          />
        </div>
      </Modal>
    </div>
  );
};

export default StandardQuoteModel;
