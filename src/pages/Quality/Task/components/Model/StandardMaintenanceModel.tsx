/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable no-useless-escape */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import {
  Button,
  Descriptions,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Space,
} from 'antd';
import type { DescriptionsProps } from 'antd';
import { adjustTemplateView } from '@/api/quality';
import { codeDefinition } from '@/constants';
import { CloseCircleFilled } from '@ant-design/icons';
import StandardCheckModel from './StandardCheckModel';
import StandardQuoteModel from './StandardQuoteModel';

// 自定义包装组件，用于消除警告
// 因为这是库内部组件的警告，而不是我们代码的问题
const StrictModeDroppable = ({ children, ...props }: any) => {
  const [enabled, setEnabled] = useState(false);

  useEffect(() => {
    // 在渲染周期后启用 Droppable
    const animation = requestAnimationFrame(() => setEnabled(true));
    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, []);

  if (!enabled) {
    return null;
  }

  return <Droppable {...props}>{children}</Droppable>;
};

type TEditProps = {
  close: () => void;
  getRuler: (val: any, type?: 'correct' | 'suspicious' | 'error') => void; // 获取规则
  rulesDetails: any; // 当前选中规则回显
  open: boolean;
  taskId?: string; //任务id
  taskType?: string; //任务类型
  curSelectedStandardType: 'correct' | 'suspicious' | 'error'; // 当前选择的判定标准的类型，正确、可疑或错误
  onStandardTypeChange?: (type: 'correct' | 'suspicious' | 'error') => void; // 新增: 当标准类型变更时的回调
};
// 运算符
const OperatorList = [
  { id: 1, name: '小于', value: '<' },
  { id: 2, name: '小于等于', value: '<=' },
  { id: 3, name: '大于', value: '>' },
  { id: 4, name: '大于等于', value: '>=' },
  { id: 5, name: '等于', value: '==' },
  { id: 6, name: '不等于', value: '!=' },
  { id: 7, name: '左括号', value: '(' },
  { id: 8, name: '右括号', value: ')' },
  { id: 9, name: '且', value: '&&' },
  { id: 10, name: '或', value: '||' },
];

const StandardMaintenanceModel: React.FC<TEditProps> = ({
  close,
  open,
  getRuler,
  rulesDetails,
  taskId,
  taskType,
  curSelectedStandardType,
  onStandardTypeChange,
}) => {
  // 标准检验弹窗Check
  const [openCheckModel, setOpenCheckModel] = useState<boolean>(false);
  // 引用历史标准弹窗Quote
  const [openQuoteModel, setOpenQuoteModel] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  // 判定标准-暂存可编辑
  const [judgingStandard, setJudgingStandard] = useState<any[]>([]);
  // 判定标准-预览/后端存储用于回显
  const [judgingStandardText, setJudgingStandardText] = useState<string>('');
  // 判定标准-规则(后端接收公式)
  const [judgingStandardRules, setJudgingStandardRules] = useState<string>('');
  // 数据值
  const [dataValue, setDataValue] = useState<string>('');
  // 引用后存在当前模板没有的字段
  const [isNoExistChart, setIsNoExistChart] = useState<boolean>(false);
  // 字段列表
  const [chartList, setChartList] = useState<any[]>([]);

  // 拖拽排序后回调
  const onDragEnd = (result: any) => {
    if (!result.destination) return;
    // 复制当前数组并进行重新排序
    const newItems = Array.from(judgingStandard);
    const [reorderedItem] = newItems.splice(result.source.index, 1);
    newItems.splice(result.destination.index, 0, reorderedItem);
    // 更新状态以保存新的顺序
    setJudgingStandard(newItems);
  };

  /**
   * @TODO 数据名称点击
   * @params obj 当前数据名称所有数据
   */
  const handleDataName = async (obj: any) => {
    const newData = [
      ...judgingStandard,
      {
        standardId: `data-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 9)}`,
        standardName: obj.fieldName,
        thName: obj.thName,
        fieldName: obj.fieldName,
        inputBox: obj.inputBox,
        isRequired: obj.isRequired,
        pomptInformation: obj.pomptInformation,
        enumItems: obj.enumItems ?? '',
        identification: 'Character',
      },
    ];
    setJudgingStandard(newData);
  };

  /**
   * @TODO 运算符点击
   * @params obj 运算符所有数据
   */
  const handleOperator = async (obj: any) => {
    const newData = [
      ...judgingStandard,
      {
        standardId: `operator-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 9)}`,
        standardName: obj.name,
        value: obj.value,
        identification: 'Operation',
      },
    ];
    setJudgingStandard(newData);
  };

  /**
   * @TODO 数据值插入
   */
  const handleInputInsert = () => {
    const newData = [
      ...judgingStandard,
      {
        standardId: `value-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 9)}`,
        standardName: dataValue,
        identification: judgeIsNum(dataValue) ? 'input_number' : 'input_text',
      },
    ];
    dataValue && setJudgingStandard(newData);
    setDataValue('');
  };

  /**
   * @TODO 删除标准项
   */
  const handleDelStandardItem = async (id: string) => {
    const newJudgingStandard = judgingStandard?.filter(
      (_item) => _item.standardId !== id
    );
    setJudgingStandard([...newJudgingStandard]);
    // 判定当前是否有不存在的字段
    handleIsExistChart(newJudgingStandard);
  };

  /**
   * @TODO 提交
   */
  const handleOk = async () => {
    if (handleIsExistChart(judgingStandard)) {
      message.error('当前标准有不存在的字段，请删除后提交');
      return;
    }
    // 规则文本-保存草稿时使用
    const viewFormula = JSON.stringify(judgingStandard);
    const rules = await ruleFormatting('thName');
    const rulerObj = {
      rules, // 后端计算接收规则
      rulesStorage: judgingStandard.length ? viewFormula : '', // 草稿编辑带结构的数据，转为JSON字符串
      rulesView: judgingStandardText ?? '', // 详情回显
    };
    getRuler(rulerObj, curSelectedStandardType);
    close();
  };

  /**
   * @TODO 规则格式化
   */
  const ruleFormatting = async (chartName: string) => {
    // 规则公式
    const formula = judgingStandard.map((_item) => {
      // 创建副本而不是直接修改原对象
      let result;

      switch (_item.identification) {
        // 字段名称
        case 'Character':
          result = _item[chartName];
          break;
        // 运算符
        case 'Operation':
          result = _item.value;
          break;
        // 输入数字
        case 'input_number':
          result = Number(_item.standardName);
          break;
        // 输入汉字
        default:
          result = `\"${_item.standardName}\"`;
          break;
      }
      return result;
    });

    // 使用新数组处理等于运算
    const processedFormula = [...formula];

    for (let idx = 0; idx < processedFormula.length; idx++) {
      if (processedFormula[idx] === '==') {
        const b1 = typeof processedFormula[idx - 1] === 'number';
        const b2 = typeof processedFormula[idx + 1] === 'number';
        const a1 = !b1 && String(processedFormula[idx - 1]).indexOf(`"`) !== -1;
        const a2 = !b2 && String(processedFormula[idx + 1]).indexOf(`"`) !== -1;

        // 汉字在等于前后时替换为 .equals("\汉字"\) 格式
        if (a1 || a2) {
          processedFormula[idx + 1] = `.equals(${processedFormula[idx + 1]})`;
          processedFormula.splice(idx, 1);
          // 由于删除了元素，需要调整索引
          idx--;
        }
      }
    }

    return processedFormula.join('');
  };

  /**
   * @TODO 验证标准
   */
  const handleCheck = async () => {
    if (!judgingStandard.length) {
      message.error('请先制定判定标准');
      return;
    }
    const isChart = judgingStandard.some(
      (_item) => _item.identification === 'Character'
    );
    if (!isChart) {
      message.error('当前未选择需要判定的数据名称');
      return;
    }
    if (handleIsExistChart(judgingStandard)) {
      message.error('当前标准有不存在的字段，请删除后提交');
      return;
    }

    const rules = await ruleFormatting('thName');
    setJudgingStandardRules(rules);
    setOpenCheckModel(true);
  };

  /**
   * @TODO 判定当前是否有不存在的字段
   * @data 判定数组
   */
  const handleIsExistChart = (data: any[]) => {
    const IS = data.some((item: any) => item.isExist === true);
    setIsNoExistChart(Boolean(IS));
    return Boolean(IS);
  };

  /**
   * @TODO 引用标准
   */
  const handleQuote = async () => {
    setOpenQuoteModel(true);
  };

  /**
   * @TODO 引用标准回调
   */
  const QuoteRuler = async (ruler: any) => {
    ruler.forEach((item1: any) => {
      if (item1.fieldName) {
        const isFiledName = chartList.some(
          (item2: any) => item1.fieldName === item2.fieldName
        );
        if (!isFiledName) {
          item1.isExist = true;
        }
      }
    });
    handleIsExistChart(ruler);
    setJudgingStandard(ruler);
  };

  /**
   * @TODO 判定是否是数字
   */
  const judgeIsNum = (val: string) => {
    if (!val) return;
    const isAllNumbers = /^[\d]+$/;
    if (isAllNumbers.test(val)) {
      return true;
    } else {
      return false;
    }
  };

  /**
   * @TODO 查询模板字段
   */
  const getTempDetail = async () => {
    try {
      const { code, data, msg }: any = await adjustTemplateView({ id: taskId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        const newData = data.itemInfos.filter(
          (item: any) =>
            item.inputBox === 'select' ||
            item.inputBox === 'text' ||
            item.inputBox === 'digit'
        );
        const chartList = newData.map((item: any, idx: number) => ({
          ...item,
          thName: `str${idx}`,
        }));
        setChartList(chartList);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  const handleCancel = async () => {
    close();
  };

  const items: DescriptionsProps['items'] = [
    {
      key: '00',
      label: <h4>判定标准类型：</h4>,
      children: (
        <Radio.Group
          options={[
            {
              label: '正确(合格)判定标准维护',
              value: 'correct',
              disabled: false,
            },
            {
              label: '可疑判定标准维护',
              value: 'suspicious',
              disabled: false,
            },
            {
              label: '错误(不合格)判定标准维护',
              value: 'error',
              disabled: false,
            },
          ]}
          value={curSelectedStandardType}
          onChange={(e) => {
            const newType = e.target.value as
              | 'correct'
              | 'suspicious'
              | 'error';
            if (onStandardTypeChange) {
              onStandardTypeChange(newType);
            }
          }}
        />
      ),
    },
    {
      key: '1',
      label: <h4>数据名称：</h4>,
      children: (
        <>
          {chartList?.map((_item) => (
            <Button
              ghost
              type="primary"
              key={_item.id || `chart-${Math.random().toString(36).slice(2)}`}
              className="mx-2 my-1 border-solid"
              onClick={() => handleDataName(_item)}
            >
              {_item.fieldName}
            </Button>
          ))}
          <div className="relative bottom-[-12px] left-[6px] text-[red]">
            注：此处只展示类型为文本、数字、下拉框的数据名称
          </div>
        </>
      ),
    },
    {
      key: '2',
      label: <h4>运算符：</h4>,
      children: OperatorList?.map((_item) => (
        <Button
          key={_item.id}
          ghost
          type="primary"
          className="mx-2 my-1 border-solid"
          onClick={() => handleOperator(_item)}
        >
          {_item.name}
        </Button>
      )),
    },
    {
      key: '3',
      label: <h4>数据值：</h4>,
      children: (
        <div className="flex">
          <Input
            className="w-[300px]"
            value={dataValue}
            allowClear
            placeholder="请输入数据值，点击插入"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setDataValue(e.target.value)
            }
          />
          <Button type="primary" className="ml-2" onClick={handleInputInsert}>
            插入数据值
          </Button>
        </div>
      ),
    },
    {
      key: '4',
      label: <h4>判定标准：</h4>,
      children: (
        <>
          {
            <DragDropContext onDragEnd={onDragEnd}>
              <StrictModeDroppable
                droppableId="my-droppable"
                direction="horizontal"
              >
                {(provided: any) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="w-full min-h-[100px] flex items-start flex-wrap overflow-auto p-2 border border-solid border-[#D9D9D9] rounded"
                  >
                    {judgingStandard?.map((item, index) => (
                      <Draggable
                        key={item.standardId || `standard-${index}`}
                        draggableId={item.standardId || `standard-id-${index}`}
                        index={index}
                      >
                        {(provided: any) => (
                          <div
                            className={`min-h-[20px] min-w-[20px] border border-solid border-[#C1C1C1] rounded-[10px] px-5 py-2 mr-2 mb-1 relative cursor-pointer ${
                              item.isExist ? 'text-[red] border-[red]' : ''
                            }`}
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            {item?.standardName}
                            <CloseCircleFilled
                              className="hover:text-[#FF4D4F] cursor-pointer absolute top-[2px] right-[2px] text-[#D9D9D9]"
                              onClick={() =>
                                handleDelStandardItem(item.standardId)
                              }
                            />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </StrictModeDroppable>
            </DragDropContext>
          }
          {/* </div> */}
          {isNoExistChart && (
            <div className="mt-1 text-[red]">
              注意：标红的标准为当前模板不存在的数据名称,请删除
            </div>
          )}
          {/* 公式预览 */}
          {judgingStandard.length ? (
            <div className="mt-2">
              <span className="font-[800]">判定标准公式预览：</span>
              <span>{judgingStandardText}</span>
            </div>
          ) : null}
        </>
      ),
    },
  ];

  useEffect(() => {
    if (rulesDetails) {
      const {
        condition,
        conditionTwo,
        conditionThree,
        conditionFour,
        conditionFive,
        conditionSix,
        conditionSeven,
        conditionEight,
        conditionNine,
      } = rulesDetails;

      try {
        if (curSelectedStandardType === 'correct') {
          // 检查conditionTwo是否为字符串，如果是则解析它
          setJudgingStandard(
            conditionTwo
              ? typeof conditionTwo === 'string'
                ? JSON.parse(conditionTwo)
                : conditionTwo
              : []
          );
          setJudgingStandardText(conditionThree ?? '');
          setJudgingStandardRules(condition ?? '');
        } else if (curSelectedStandardType === 'suspicious') {
          setJudgingStandard(
            conditionFive
              ? typeof conditionFive === 'string'
                ? JSON.parse(conditionFive)
                : conditionFive
              : []
          );
          setJudgingStandardText(conditionSix ?? '');
          setJudgingStandardRules(conditionFour ?? '');
        } else if (curSelectedStandardType === 'error') {
          setJudgingStandard(
            conditionEight
              ? typeof conditionEight === 'string'
                ? JSON.parse(conditionEight)
                : conditionEight
              : []
          );
          setJudgingStandardText(conditionNine ?? '');
          setJudgingStandardRules(conditionSeven ?? '');
        }
      } catch (error) {
        console.error('解析判定标准数据出错:', error);
        // 错误处理：重置数据
        setJudgingStandard([]);
        setJudgingStandardText('');
        setJudgingStandardRules('');
      }
    }
  }, [rulesDetails, curSelectedStandardType]);

  useEffect(() => {
    // 判定标准-预览/后端存储用于回显
    if (judgingStandard.length) {
      const viewFormula2 = judgingStandard
        .map((_item, idx) => {
          // 创建项目的副本而不是直接修改原对象
          const item = { ..._item };

          if (
            item.standardName === '左括号' ||
            item.standardName === '右括号'
          ) {
            item.standardName = item.value;
          }

          if (idx === 0) {
            return item.standardName + ' ';
          } else if (idx === judgingStandard.length - 1) {
            return ' ' + item.standardName;
          } else {
            return ' ' + item.standardName + ' ';
          }
        })
        .join('');
      setJudgingStandardText(viewFormula2);
    } else {
      setJudgingStandardText('');
    }
  }, [judgingStandard]);

  useEffect(() => {
    if (taskId) {
      getTempDetail();
    }
  }, [taskId]);

  return (
    <div className="flex flex-col h-full w-full">
      <Modal
        open={open}
        width="70%"
        title="判定标准规则制定"
        onCancel={handleCancel}
        footer={
          <Space className="w-[40%] m-auto flex justify-around">
            <Button key="history-btn" onClick={handleQuote}>
              引用历史标准
            </Button>
            <Button
              key="check-btn"
              type="primary"
              loading={loading}
              onClick={handleCheck}
            >
              标准验证
            </Button>
            <Button
              key="confirm-btn"
              type="primary"
              loading={loading}
              onClick={handleOk}
            >
              确认
            </Button>
            <Button key="close-btn" onClick={handleCancel}>
              关闭
            </Button>
            {judgingStandard.length ? (
              <Popconfirm
                title="删除当前编写的判定标准？"
                onConfirm={() => {
                  setJudgingStandard([]);
                  setIsNoExistChart(false);
                }}
                okText="确定"
                cancelText="取消"
                key="popconfirm-clear"
              >
                <Button key="clear-btn">清空当前判定标准</Button>
              </Popconfirm>
            ) : null}
          </Space>
        }
      >
        <div className="p-2">
          <Descriptions
            styles={{
              label: {
                width: '120px',
              },
            }}
            column={1}
            bordered
            items={items}
          />
        </div>
      </Modal>
      {/* 标准检验弹窗 */}
      {openCheckModel ? (
        <StandardCheckModel
          close={() => setOpenCheckModel(false)}
          open={openCheckModel}
          projectDataList={judgingStandard}
          standardRuler={judgingStandardRules}
          viewFormula={judgingStandardText}
          taskType={taskType}
          sampleName={rulesDetails?.itemName}
          storgeText={judgingStandard}
        />
      ) : null}
      {/* 引用历史标准弹窗 */}
      <StandardQuoteModel
        close={() => setOpenQuoteModel(false)}
        open={openQuoteModel}
        getRulerItem={(val) => QuoteRuler(val)}
        rulesType={curSelectedStandardType}
      />
    </div>
  );
};

export default StandardMaintenanceModel;
