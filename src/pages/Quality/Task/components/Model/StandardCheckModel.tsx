/* eslint-disable array-callback-return */

/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Modal, Space, Tooltip } from 'antd';
import { checkStandardItem } from '@/api/quality';
import { QUERY_SUCCESS_MSG } from '@/constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type {
  ActionType,
  EditableFormInstance,
} from '@ant-design/pro-components';
import { cloneDeep, uniqBy } from 'lodash';
import EEditableProTable from '@/components/EditableProTable';

type TEditProps = {
  close: () => void;
  open: boolean;
  projectDataList?: any[];
  standardRuler?: any; // 规则
  viewFormula?: string; // 回显值
  taskType?: string; // 任务类型
  sampleName?: string; // 考核项目
  storgeText: any; // 规则结构
};
type DataSourceType = any;

const StandardCheckModel: React.FC<TEditProps> = ({
  close,
  sampleName,
  open,
  standardRuler,
  projectDataList,
  taskType = '1',
  viewFormula,
  storgeText,
}) => {
  // 可编辑项
  const [editableKeys, setEditableKeys] = useState<any[]>([1]);
  // 表头
  const [tableTitleList, setTableTitleList] = useState<any[]>([]);
  // 表数据
  const [tableData, setTableData] = useState<any[]>([
    {
      id: 1,
      name: sampleName,
    },
  ]);
  // 校验按钮
  const [loading, setLoading] = useState<boolean>(false);
  const editableFormRef = useRef<EditableFormInstance<DataSourceType>>(null);
  const actionRef = useRef<ActionType>(null);

  // 初始化表头
  const init = () => {
    try {
      const newProjectDataList = projectDataList?.filter(
        (_item) => _item.identification === 'Character'
      );
      // 去重 uniqBy(newProjectDataList, 'id')
      const temp: any = setup(
        cloneDeep(uniqBy(newProjectDataList, 'fieldName') as any[])
      );
      temp?.length &&
        temp.unshift({
          title: '考核项目',
          dataIndex: 'name',
          width: 120,
          editable: false,
        });
      temp?.length &&
        temp.push({
          title: '判定结果',
          dataIndex: 'result',
          width: 120,
          editable: false,
        });
      setTableTitleList(temp);
    } catch (error) {
      console.log(error);
    }
  };
  // 表头数据转换处理
  const setup = (orgData: any[]) => {
    return orgData?.map((item: any) => {
      if (item.inputBox === 'text') {
        return {
          disable: true,
          title: (
            <div>
              {item.isRequired ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.thName,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
          fieldProps: {
            maxLength: 200,
          },
        };
      } else if (item.inputBox === 'digit') {
        return {
          title: (
            <div>
              {item.isRequired ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.thName,
          valueType: 'digit',
          fieldProps: {
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'select') {
        const enums: any = {};
        if (item.enumItems) {
          item.enumItems.split(',')?.map((en: any) => {
            enums[en] = {
              text: en,
            };
          });
        }
        return {
          title: (
            <div>
              {item.isRequired ? (
                <span className="text-[red] mr-1">*</span>
              ) : null}
              <span>{item.fieldName}</span>
              {item.pomptInformation ? (
                <Tooltip title={item.pomptInformation} className="cursor-help">
                  <QuestionCircleOutlined className="text-[#949494] ml-1" />
                </Tooltip>
              ) : null}
            </div>
          ),
          dataIndex: item.thName,
          valueType: 'select',
          valueEnum: enums,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        };
      }
    });
  };
  /**
   * @TODO 验证
   */
  const handleCheck = async () => {
    try {
      await editableFormRef?.current?.validateFields();
      const tableDataInfo = await editableFormRef?.current?.getFieldsValue();
      const params = {
        variables: tableDataInfo[1],
        formula: standardRuler,
        viewFormula: viewFormula,
        // variablesKey: JSON.stringify(storgeText),
        type: taskType,
        isSave: 0, // 1-保存 0-不保存
      };
      setLoading(true);
      const { code, data, msg }: any = await checkStandardItem(params);
      if (msg.length < 10) {
        message.success(QUERY_SUCCESS_MSG);
        setTableData([{ ...tableData[0], result: msg }]);
        actionRef.current?.reload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      message.warning('请填写完整数据');
      throw new Error(`${error}`);
    } finally {
      setLoading(false);
    }
  };
  // 关闭弹窗
  const handleCancel = async () => {
    close();
  };

  useEffect(() => {
    projectDataList?.length && init();
  }, [projectDataList]);

  return (
    <div className="flex flex-col h-full w-full">
      <Modal
        open={open}
        width="75%"
        title="判定标准检验"
        onCancel={handleCancel}
        footer={[
          <Space
            key="footer-space"
            className="w-[50px] m-auto flex justify-around"
          >
            <Button
              loading={loading}
              key="submit"
              type="primary"
              onClick={handleCheck}
            >
              校验
            </Button>
            <Button key="close" onClick={handleCancel}>
              关闭
            </Button>
          </Space>,
        ]}
      >
        <div className="min-h-[200px]">
          <EEditableProTable<DataSourceType>
            search={false}
            rowKey="id"
            editableFormRef={editableFormRef}
            actionRef={actionRef}
            recordCreatorProps={false}
            columns={tableTitleList as any}
            editable={{
              type: 'multiple',
              editableKeys: editableKeys,
              onChange: setEditableKeys,
            }}
            request={async () => ({
              data: tableData && tableData,
              total: 0,
              success: true,
            })}
          />
        </div>
      </Modal>
    </div>
  );
};

export default StandardCheckModel;
