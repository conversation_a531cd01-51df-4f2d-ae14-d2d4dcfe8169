/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import {
  Button,
  DatePicker,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Tag,
} from 'antd';
import {
  changeTaskStatus,
  delQATask,
  getQATaskList,
  getTaskCount,
  updateEndDate,
} from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useInfoStore } from '@/store';
import { useDictStore } from '@/store/dict';
import { useQualityStore } from '@/store/quality';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProTable,
  TableDropdown,
} from '@ant-design/pro-components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import AdjustTemplate from './components/AdjustTemplate';
import Detail from './components/Detail';
import Edit from './components/Edit';
import ExpressModel from './components/Model/ExpressModel';
import TaskChangeEdit from './components/TaskChangeEdit';
import UpdateJudgCriteria from './components/UpdateJudgCriteria';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const { assessmentTaskTypeEnums, getAssessmentTaskTypeEnums } =
    useDictStore();

  // 管理员
  const { isAdmin } = useInfoStore();

  // 任务ID
  const [taskId, setTaskId] = useState<string>('');

  const [pageSize, setPageSize] = useState<number>(10);

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  // 获取table中需要的枚举
  const {
    assessmentTypesOnTable,
    getAssessmentTypes,
    assessmentTaskTypesOnTable,
    getAssessmentTaskTypes,
    taskStatusOnTable,
  } = useQualityStore();

  // 打开任务变更Drawer
  const [openTaskChangeEdit, setOpenTaskChangeEdit] = useState<boolean>(false);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 模板调整
  const [openAdjust, setOpenAdjust] = useState<boolean>(false);
  // 维护发样信息弹窗
  const [expressModel, setExpressModel] = useState<boolean>(false);
  // 详情弹窗
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const [activeKey, setActiveKey] = useState<string>('0');

  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
    '2': '-',
    '3': '-',
  });

  // 当前选择的数据的结束日期
  const [currentSelectedEndDate, setCurrentSelectedEndDate] =
    useState<string>('');
  // 当前需要更新的结束日期
  const [currentUpdateEndDate, setCurrentUpdateEndDate] = useState<string>('');
  // 打开修改结束日期Popover
  const [openUpdateEndDateModal, setOpenUpdateEndDateModal] =
    useState<boolean>(false);

  // Form实例，用于重置表单
  const [endDateFormRef] = Form.useForm();

  // 打开更新判定标准弹窗
  const [openUpdateJudgCriteriaModal, setOpenUpdateJudgCriteriaModal] =
    useState<boolean>(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
      width: 80,
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 220,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      valueType: 'select',
      fieldProps: {
        options: assessmentTaskTypeEnums,
      },
      width: 120,
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      disable: true,
      title: '使用模板名称',
      dataIndex: 'templateName',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      key: 'endDate1',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '最后更新日期',
      dataIndex: 'updateTime',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: taskStatusOnTable,
      hideInSearch: true,
      width: 80,
      render(text, row) {
        return (
          <Tag color={row.status === '1' ? 'processing' : 'default'}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      key: 'endDate2',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          if (value) {
            return {
              endDateFrom: value[0],
              endDateTo: value[1],
            };
          }
        },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        // 创建各种状态下的按钮
        interface ButtonItem {
          key: string;
          name: string;
          danger?: boolean;
          onClick: () => void;
        }

        const allBtns: ButtonItem[] = [];
        const dropdownMenus = [];

        // 已结束
        if (record.status !== '0' && record.status !== '2') {
          allBtns.push({
            key: 'view',
            name: '详情',
            onClick: () => {
              setDetailId(record.id);
              setOpenDetail(true);
            },
          });
        }

        // 草稿状态 - status为0
        if (record.status === '0') {
          allBtns.push({
            key: 'edit',
            name: '编辑',
            onClick: () => {
              setDetailId(record.id);
              setOpenEdit(true);
            },
          });

          allBtns.push({
            key: 'del',
            name: '删除',
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: '删除此行？',
                onOk: () => handleDelete(record.id),
                okText: '确定',
                cancelText: '取消',
              });
            },
          });
        }

        // 进行中状态 - status为1
        else if (record.status === '1') {
          allBtns.push({
            key: 'edit2',
            name: '调整模板',
            onClick: () => {
              setTaskId(record.id);
              setOpenAdjust(true);
            },
          });

          allBtns.push({
            key: 'update',
            name: '更新判定标准',
            onClick: () => {
              setDetailId(record.id);
              setOpenUpdateJudgCriteriaModal(true);
            },
          });

          allBtns.push({
            key: 'endDate',
            name: '修改结束日期',
            onClick: () => {
              setDetailId(record.id);
              setCurrentSelectedEndDate(record?.endDate);
              // 重置表单并设置当前结束日期
              endDateFormRef.resetFields();
              endDateFormRef.setFieldsValue({
                endDate: record?.endDate,
              });
              setOpenUpdateEndDateModal(true);
            },
          });

          allBtns.push({
            key: 'change',
            name: '变更任务',
            onClick: () => {
              Modal.confirm({
                title: '您确定变更该任务?',
                onOk: () => handleChangeTask(record.id),
                okText: '确定',
                cancelText: '取消',
              });
            },
          });
        }

        // 变更中状态 - status为2
        else if (record.status === '2') {
          allBtns.push({
            key: 'change',
            name: '编辑',
            onClick: () => {
              setDetailId(record.id);
              setOpenTaskChangeEdit(true);
            },
          });
        }

        // 当前登录人为管理员时在所有类型中显示强制删除
        if (isAdmin) {
          allBtns.push({
            key: 'delpro',
            name: '强制删除',
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: '强制删除此任务？',
                onOk: () => handleDelete(record.id),
                okText: '确定',
                cancelText: '取消',
              });
            },
          });
        }

        // 保留前两个按钮显示，其余的放入下拉菜单中
        const visibleBtns = allBtns.slice(0, 2).map((btn) => {
          if (btn.danger) {
            return (
              <Button
                type="link"
                size="small"
                key={btn.key}
                danger
                onClick={btn.onClick}
              >
                {btn.name}
              </Button>
            );
          }
          return (
            <Button
              type="link"
              size="small"
              key={btn.key}
              onClick={btn.onClick}
            >
              {btn.name}
            </Button>
          );
        });

        // 如果有超过2个按钮，将剩余的放入下拉菜单
        if (allBtns.length > 2) {
          const dropdownItems = allBtns.slice(2).map((btn) => ({
            key: btn.key,
            name: btn.name,
            danger: btn.danger,
          }));

          if (dropdownItems.length > 0) {
            visibleBtns.push(
              <TableDropdown
                key="actionGroup"
                onSelect={(key) => {
                  const selectedBtn = allBtns.find((btn) => btn.key === key);
                  if (selectedBtn) {
                    selectedBtn.onClick();
                  }
                }}
                menus={dropdownItems}
              />
            );
          }
        }

        return visibleBtns;
      },
    },
  ];

  /**
   * @TODO 删除
   */
  const handleDelete = async (ids: string) => {
    try {
      const { code, msg } = await delQATask(ids);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };
  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };

  /**
   * @TODO 关闭快递维护
   */
  const closeEdit1 = () => {
    setExpressModel(false);
    tableReload();
  };

  /**
   * @TODO 关闭模板调整
   */
  const closeAdjust = () => {
    setOpenAdjust(false);
  };

  /**
   * 修改结束日期
   */
  const renderUpdateEndDate = () => {
    return (
      <Form
        name="basic"
        form={endDateFormRef}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        initialValues={{ remember: true }}
        onFinish={() => {}}
        autoComplete="off"
        className="!mt-4"
      >
        <Form.Item label="现结束日期" name="endDate" className="!mb-4">
          <Input disabled value={currentSelectedEndDate} />
        </Form.Item>
        <Form.Item
          label="更新结束日期"
          name="newEndDate"
          rules={[{ required: true, message: '请选择新的结束日期' }]}
          className="!mb-6"
        >
          <DatePicker
            style={{ width: '100%' }}
            placeholder="请选择日期"
            format="YYYY-MM-DD"
            onChange={(value) =>
              setCurrentUpdateEndDate(dayjs(value).format('YYYY-MM-DD'))
            }
          />
        </Form.Item>
      </Form>
    );
  };

  /**
   * @TODO 更新结束日期
   */
  const handleUpdateEndDate = async () => {
    if (!currentUpdateEndDate) {
      message.error('请选择新的结束日期');
      return;
    }
    if (!detailId) {
      message.error('请选择需要修改结束日期的任务');
      return;
    }
    setLoading(true);
    try {
      const { code, msg } = await updateEndDate({
        id: detailId,
        endDate: currentUpdateEndDate,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setOpenUpdateEndDateModal(false);
      setCurrentSelectedEndDate('');
      setCurrentUpdateEndDate('');
      setTaskId('');
      setLoading(false);
      // 重置表单
      endDateFormRef.resetFields();
    }
  };

  /**
   *  获取所有类型任务的数量
   */
  const getCounts = async () => {
    try {
      const { code, data, msg } = await getTaskCount(queryParams);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      setCounts(data);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  /**
   * @TODO 变更任务
   */
  const handleChangeTask = async (id: string) => {
    if (!id) {
      message.error('请选择需要变更的任务');
      return;
    }
    setLoading(true);
    try {
      const { code, msg } = await changeTaskStatus({
        id,
        status: '2',
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
      }
      message.success(QUERY_SUCCESS_MSG);
      tableReload();
      getCounts();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
    !assessmentTaskTypeEnums?.length && getAssessmentTaskTypeEnums();
    getCounts();
  }, []);

  const [queryParams, setQueryParams] = useState({});

  useEffect(() => {
    getCounts();
  }, [queryParams]);

  useEffect(() => {
    console.log('assessmentTaskTypeEnums', assessmentTaskTypeEnums);
  }, [assessmentTaskTypeEnums]);

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '0',
                label: <span>草稿（{counts?.saveCount || 0}）</span>,
              },
              {
                key: '1',
                label: <span>进行中（{counts?.proceedingCount || 0}）</span>,
              },
              {
                key: '4',
                label: <span>已结束（{counts?.finishedCount || 0}）</span>,
              },
              {
                key: '2',
                label: <span>变更中（{counts?.changingCount || 0}）</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            status: activeKey,
          };
          delete param.current;
          const { code, rows, total, msg } = await getQATaskList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          let cache = {
            ...params,
          };
          delete cache.current;
          delete cache.pageSize;
          setQueryParams(cache);
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="考核任务"
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setDetailId('');
              setOpenEdit(true);
            }}
            type="primary"
          >
            新增任务
          </Button>,
        ]}
      />
      {/* 新增 */}
      <Drawer
        width="85%"
        title="新建/编辑任务"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit close={closeEdit} detailId={detailId} getCounts={getCounts} />
      </Drawer>
      {/* 详情 */}
      <Drawer
        width="85%"
        title="详情"
        onClose={() => setOpenDetail(false)}
        open={openDetail}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Detail close={() => setOpenDetail(false)} detailId={detailId} />
      </Drawer>
      {/* 调整模板 */}
      <Drawer
        width="85%"
        title="调整模板"
        onClose={closeAdjust}
        open={openAdjust}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <AdjustTemplate close={closeAdjust} taskId={taskId} />
      </Drawer>
      {/* 快递信息维护 */}
      <Drawer
        width="85%"
        title="发样信息维护"
        onClose={closeEdit1}
        open={expressModel}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <ExpressModel close={closeEdit1} detailId={detailId} />
      </Drawer>
      {/* 修改结束日期 */}
      <Modal
        title="修改结束日期"
        open={openUpdateEndDateModal}
        width={380}
        centered
        onCancel={() => setOpenUpdateEndDateModal(false)}
        footer={() => (
          <Button
            type="primary"
            onClick={handleUpdateEndDate}
            loading={loading}
          >
            更新结束日期
          </Button>
        )}
      >
        {renderUpdateEndDate()}
      </Modal>
      {/* 更新判定标准 */}
      <Drawer
        title="更新判定标准"
        open={openUpdateJudgCriteriaModal}
        footer={false}
        width="70%"
        onClose={() => setOpenUpdateJudgCriteriaModal(false)}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <UpdateJudgCriteria
          close={() => setOpenUpdateJudgCriteriaModal(false)}
          detailId={detailId}
          getCounts={getCounts}
        />
      </Drawer>
      {/* 任务变更 */}
      <Drawer
        width="65%"
        title="任务变更"
        open={openTaskChangeEdit}
        onClose={() => setOpenTaskChangeEdit(false)}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <TaskChangeEdit
          detailId={detailId}
          close={() => {
            setOpenTaskChangeEdit(false);
            getCounts();
            tableReload();
          }}
        />
      </Drawer>
    </PageContainer>
  );
};
export default QualityTask;
