import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { message } from 'antd';
import { getDepartmentList } from '@/api/department';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { codeDefinition } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormInstance,
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDigit,
  ProFormTextArea,
  ProFormTreeSelect,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { clone } from 'lodash';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import { handleTree } from '@/utils/index';
import { getIconByName } from '@/utils/upload';
import { getFileTypeByName } from '@/utils/upload';
import { FormInitVal, formItemLayout } from '../data';

type TEditProps = {
  id?: any;
  detailInfo?: any;
  onSubmit: (params: any) => void;
  onRef: any;
  readonly: boolean;
};

const ReceiveForm: React.FC<TEditProps> = ({
  id,
  detailInfo,
  onSubmit,
  onRef,
  readonly,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  const { token } = useTokenStore();
  // 表单实例
  const formRef = useRef<FormInstance>(null);

  /**
   * @TODO 获取详情数据
   */
  const getDetailData = () => {
    try {
      if (id && detailInfo) {
        const p = clone(detailInfo);
        if (!p.receiptDate) {
          p.receiptDate = dayjs(new Date()).format('YYYY-MM-DD');
        }
        p.attachmentIds = []; // todo
        formRef.current?.setFieldsValue(p);
      } else {
        formRef.current?.setFieldsValue({
          attachmentIds: [], // todo
          receiptDate: dayjs(new Date()).format('YYYY-MM-DD'),
        });
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    getDetailData();
  }, [detailInfo]);

  /**
   * @TODO 新增/编辑
   */
  const handleSave = async (values: any) => {
    const params = {
      ...values,
      receiptUnitName: values.receiptUnitId.label,
      receiptUnitId: values.receiptUnitId.value,
    };
    onSubmit(params);
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();
  return (
    <>
      <BlockContainer title="接收登记">
        <ProForm
          readonly={readonly}
          formRef={formRef}
          labelCol={{
            offset: 1,
          }}
          layout="horizontal"
          grid={true}
          onFinish={handleSave}
          submitter={false}
          initialValues={FormInitVal}
          onValuesChange={(_, values: any) => {
            for (const key in values) {
              if (typeof values[key] === 'string') {
                values[key] = values[key].trim();
              }
            }
            formRef.current?.setFieldsValue(values);
          }}
        >
          <ProFormTreeSelect
            name="receiptUnitId"
            label="接收单位名称"
            placeholder="请选择"
            rules={[{ required: true, message: '请输入接收单位名称' }]}
            request={async () => {
              const { code, data, msg } = await getDepartmentList({});
              if (code !== codeDefinition.QUERY_SUCCESS) {
                message.error(msg);
              }
              const d = handleTree(data, 'deptId');
              function setDisabled(d: any) {
                d.forEach((item: any) => {
                  item.selectable = item.labTag === 1;
                  item.disabled = item.labTag !== 1;
                  if (item.children) {
                    setDisabled(item.children);
                  }
                });
              }
              setDisabled(d);
              return d;
            }}
            fieldProps={{
              labelInValue: true,
              fieldNames: {
                label: 'deptName',
                value: 'deptId',
                children: 'children',
              },
            }}
            colProps={formItemLayout}
          />
          <ProFormDatePicker
            width="lg"
            initialValue={dayjs()}
            name="receiptDate"
            label="接收日期"
            colProps={formItemLayout}
          />
          <ProFormDigit
            name="sampleNum"
            label="样本数量"
            placeholder="请输入样本数量"
            max={
              detailInfo && detailInfo.taskDetails
                ? detailInfo.taskDetails.length
                : null
            }
            rules={[{ required: true, message: '请输入样本数量' }]}
            colProps={formItemLayout}
          />
          <ProFormCheckbox.Group
            name="sampleStatus"
            layout="horizontal"
            label="样本状态"
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.015 }}
            rules={[{ required: true, message: '请选择样本状态' }]}
            options={[
              {
                label: '冷藏',
                value: '0',
              },
              {
                label: '常温',
                value: '1',
              },
              {
                label: '完好',
                value: '2',
              },
              {
                label: '撒漏',
                value: '3',
              },
              {
                label: '其他',
                value: '4',
              },
            ]} // 0冷藏，1常温，2完好，3撒漏，4其他
          />
          {/* <ProFormTextArea
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.02 }}
            name="remark"
            label="备注信息"
          />
          <ProFormUploadButton
            colProps={{ span: 24 }}
            labelCol={{ flex: 0.02 }}
            name="attachmentIds"
            label="附件信息"
            max={10}
            fieldProps={{
              iconRender: (file) => {
                return (
                  <img
                    src={getIconByName(file.name)}
                    className="!w-[40px] !h-[40px] m-auto mt-2"
                    alt="logo"
                  />
                );
              },
              disabled: readonly,
              name: 'file',
              listType: 'picture-card',
              onChange: (info) => {
                handleUploadFiles(info);
              },
              headers: {
                Authorization: `Bearer ${token}`,
              },
              async onPreview(file: any) {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.data &&
          file.response.data.ossId
        ) {
          const type = getFileTypeByName(file.response.data.fileName);
          if (type === 'Image') {
            const d = await getFileData(file.response.data.ossId);
            setIsShowFileData({
              name: file.response.data.fileName,
              url: d,
              ossId: file.response.data.ossId
            });
            setIsShowFileView(true);
          } else {
            downloadFile(file.response.data.ossId, file.response.data.fileName);
          }
        }
      },
            }}
            action={uploadFiles}
            wrapperCol={{
              span: 24,
            }}
            extra={
              readonly
                ? ''
                : '注意：1、支持上传Excel、Word、PDF格式文件；2、每份文件不得超过10M；3、最多支持上传10份文件；'
            }
          >
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </div>
          </ProFormUploadButton> */}
        </ProForm>
        <FileView
          open={isShowFileView}
          file={isShowFileData}
          closeDetail={() => {
            setIsShowFileView(false);
          }}
        />
      </BlockContainer>
    </>
  );
};

export default ReceiveForm;
