import { useRef, useState } from 'react';
import { Table } from 'antd';
import BlockContainer from '@/components/BlockContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TTaskListByOrgProps = {
  list: any[];
  refType: any;
};

const TaskList: React.FC<TTaskListByOrgProps> = ({
  list = [],
  refType = 0,
}) => {
  const columns: any[] = [
    {
      title: '被考核机构名称',
      dataIndex: 'assessedLabName',
    },
    {
      title: '机构编号',
      dataIndex: 'assessedLabCode',
    },
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
    },
    // {
    //   title: parseInt(refType) === 2 ? '参考结果下限' : '参考结果',
    //   dataIndex: 'refResult',
    // },
    // {
    //   title: '参考结果上限',
    //   dataIndex: 'refMaxResult',
    //   search: false,
    //   hideInTable: parseInt(refType) !== 2,
    // },
  ];

  return (
    <BlockContainer title="样品列表" noPadding>
      <Table
        size="small"
        key={'id'}
        dataSource={list}
        columns={columns}
        pagination={false}
      />
    </BlockContainer>
  );
};
export default TaskList;
