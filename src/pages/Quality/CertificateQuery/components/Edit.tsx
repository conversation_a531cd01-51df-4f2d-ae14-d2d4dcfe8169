import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getReceptDetail, recept } from '@/api/recept';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import BaseForm from '../../Task/components/BaseForm';
import ReceiveForm from './ReceiveForm';
import TaskListByOrg from './TaskListByOrg';
import TaskListBySample from './TaskListBySample';

type TEditProps = {
  close: () => void;
  detailId?: string;
};

const MenuDetail: React.FC<TEditProps> = ({ close, detailId }) => {
  const [id, setId] = useState<any>('');
  const [readonly, setReadonly] = useState<boolean>(false);
  const [baseInfo, setBaseInfo] = useState<any>();
  /**
   * @TODO 获取详情数据
   */
  const getDetailData = useCallback(async () => {
    try {
      if (id) {
        const { code, data, msg } = await getReceptDetail(id);
        if (code === codeDefinition.QUERY_SUCCESS) {
          data.sampleStatus = data.sampleStatus
            ? data.sampleStatus.split(',')
            : [];
          setBaseInfo(data);
          setReadonly(data.status === '1');
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  }, [id]);

  useEffect(() => {
    getDetailData();
  }, [id]);

  useEffect(() => {
    if (detailId) {
      setId(detailId);
    }
  }, [detailId]);

  /**
   * @TODO 发布任务
   */
  const handleSave = async () => {
    formRef2.current?.handleSubmit();
  };

  const handleSaveSubmit = async (params: any) => {
    try {
      setLoading(true);
      const realParams = {
        assessmentTaskId: baseInfo.assessmentTaskId,
        id: baseInfo.id,
        ...params,
        sampleStatus: params.sampleStatus.join(','),
      };
      const { code, data, msg }: any = await recept(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const [loading, setLoading] = useState(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BaseForm
          readonlyAll={true}
          readonly={true}
          hideRank={true}
          onRef={formRef1}
          id={id}
          detailInfo={baseInfo}
        />
        {baseInfo && (
          <div className="mt-4">
            {baseInfo &&
            (baseInfo.taskType === 'lab' ||
              baseInfo.taskType === '考核机构方式') ? (
              <TaskListByOrg
                list={baseInfo.taskDetails}
                refType={baseInfo.refType}
              />
            ) : (
              <TaskListBySample list={baseInfo.taskDetails} />
            )}
          </div>
        )}
        <div className="mt-4">
          <ReceiveForm
            readonly={readonly}
            onRef={formRef2}
            id={id}
            detailInfo={baseInfo}
            onSubmit={(val) => {
              handleSaveSubmit(val);
            }}
          />
        </div>
      </div>
      {!readonly && (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button onClick={handleSave} type="primary" loading={loading}>
            提交登记信息
          </Button>
        </div>
      )}
    </div>
  );
};

export default MenuDetail;
