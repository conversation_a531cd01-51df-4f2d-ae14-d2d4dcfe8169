import { useRef, useState } from 'react';
import { Table } from 'antd';
import BlockContainer from '@/components/BlockContainer';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type TTaskListByOrgProps = {
  list: any[];
};

const TaskList: React.FC<TTaskListByOrgProps> = ({ list = [] }) => {
  const columns: any[] = [
    {
      title: '样本编号',
      dataIndex: 'sampleCode',
    },
    {
      title: '参考结果',
      dataIndex: 'refResult',
    },
  ];

  return (
    <BlockContainer title="样品列表">
      <Table dataSource={list} columns={columns} pagination={false} />
    </BlockContainer>
  );
};
export default TaskList;
