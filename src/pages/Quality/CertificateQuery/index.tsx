/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Modal, Tag } from 'antd';
import { getlistOrg } from '@/api/certificate';
import { downloadFile, getFileOssObjApi } from '@/api/file';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import FileViewByStream from '@/components/FileViewByStream';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { waitTime, waitTimePromise } from '@/utils/helpers';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();
  // 预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const [loadingRowIndex, setLoadingRowIndex] = useState<string>('');
  const [loadingRowIndexDownload, setLoadingRowIndexDownloa] =
    useState<string>('');
  const [fileUrl, setFileUrl] = useState<any>();

  const [pageSize, setPageSize] = useState<number>(10);

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  /**
   * @TODO 新增、编辑
   */
  const [openEdit, setOpenEdit] = useState<boolean>(false);
  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const [activeKey, setActiveKey] = useState<string>('0');

  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'year',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
      hideInSearch: true,
    },
    {
      title: '被考核机构',
      dataIndex: 'deptName',
      hideInSearch: true,
    },
    {
      title: '评判日期',
      dataIndex: 'judgeDate',
      valueType: 'date',
      hideInSearch: true,
    },
    // {
    //   title: '样品得分',
    //   dataIndex: 'sampleScore',
    //   hideInSearch: true,
    //   align: 'center',
    // },
    // {
    //   title: '资料得分',
    //   dataIndex: 'dataScore',
    //   hideInSearch: true,
    //   align: 'center',
    // },
    // {
    //   title: '得分',
    //   dataIndex: 'score',
    //   hideInSearch: true,
    //   align: 'center',
    // },
    {
      title: '考核结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <>
            {row.result === '优秀' && <Tag color="success">{text}</Tag>}
            {row.result === '合格' && <Tag color="processing">{text}</Tag>}
            {row.result === '不合格' && <Tag color="error">{text}</Tag>}
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _) => {
        const btns = [
          <Button
            type="link"
            size="small"
            key="download"
            loading={loadingRowIndexDownload === record.id}
            onClick={() => {
              setLoadingRowIndexDownloa(record.id);
              downloadCertificate(record.file);
            }}
          >
            下载证书
          </Button>,
          <Button
            type="link"
            size="small"
            key="preview"
            loading={loadingRowIndex === record.id}
            onClick={() => {
              previewCertificate(record);
              setLoadingRowIndex(record.id);
            }}
          >
            预览证书
          </Button>,
        ];

        return btns;
      },
    },
  ];

  // 预览证书
  const previewCertificate = async (record: any) => {
    try {
      if (!record.file) return;

      const { code, data, msg } = await getFileOssObjApi(record.file);
      if (code === 200) {
        if (data && data.length) {
          setFileUrl(data[0].url);
          setPreviewFileId(data[0].ossId);
          setOpenPreview(true);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoadingRowIndex('');
    }
  };
  // 下载证书
  const downloadCertificate = async (file: any) => {
    try {
      if (file) {
        await downloadFile(file, '质控考核证书.pdf');
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoadingRowIndexDownloa('');
    }
  };

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    tableReload();
  };
  useEffect(() => {
    tableReload();
  }, [activeKey]);

  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        bordered
        request={async (params) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
          };
          delete param.current;
          const { code, rows, total, msg } = await getlistOrg(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
        headerTitle="证书查询"
      />
      {/* 详情 */}
      <Drawer
        width="90%"
        title="登记信息"
        onClose={closeEdit}
        open={openEdit}
        destroyOnClose
        classNames={{
          body: 'bg-[#F5F5F5] !p-0',
        }}
      >
        <Edit close={closeEdit} detailId={detailId} />
      </Drawer>
      {/* 预览 */}
      {openPreview && (
        <>
          {/* {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )} */}
          <Modal
            width="60%"
            title="文件预览"
            onCancel={() => setOpenPreview(false)}
            open={openPreview}
            footer={null}
            destroyOnClose
          >
            <FileViewByStream fileId={previewFileId} isPreview />
          </Modal>
        </>
      )}
    </PageContainer>
  );
};
export default QualityTask;
