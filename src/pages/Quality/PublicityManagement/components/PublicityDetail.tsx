/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useState } from 'react';
import { Card, Drawer, message, Tag } from 'antd';
import { homePublicityDetailTaskListApi } from '@/api/publicity';
import { codeDefinition } from '@/constants';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PageContainer from '@/components/PageContainer';
import EventFile from '@/pages/EpidemicTracking/components/EventFile';

type TPublicityDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  taskId: string;
  fileGroupId: string;
};

const PublicityDetail: React.FC<TPublicityDetailProps> = ({
  open,
  setOpen,
  taskId,
  fileGroupId,
}) => {
  const [pageSize, setPageSize] = useState(10);
  const actionRef = useRef<ActionType>();
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: '年份',
      dataIndex: 'particularYear',
      hideInSearch: true,
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      hideInSearch: true,
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      hideInSearch: true,
    },
    {
      title: '被考核机构',
      dataIndex: 'assessedLabName',
    },
    {
      title: '样品得分',
      dataIndex: 'sampleScore',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <span>
            {row.stuffScore && row.score
              ? (parseFloat(row.score) - parseFloat(row.stuffScore)).toFixed(1)
              : '-'}
          </span>
        );
      },
    },
    {
      title: '资料得分',
      dataIndex: 'stuffScore',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '得分',
      dataIndex: 'score',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '考核结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <>
            {row.result === '优秀' && <Tag color="success">{text}</Tag>}
            {row.result === '合格' && <Tag color="processing">{text}</Tag>}
            {row.result === '不合格' && <Tag color="error">{text}</Tag>}
          </>
        );
      },
    },
  ];
  return (
    <Drawer
      width="80%"
      title="公示详情"
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
    >
      <PageContainer>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            const _params = {
              pageNum: params.current!,
              pageSize: params.pageSize!,
              taskId,
              orgName: params.assessedLabName,
            };
            const { code, rows, total, msg } =
              await homePublicityDetailTaskListApi(_params);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              message.error(msg);
            }

            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          // form={{
          //   // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
          //   syncToUrl: (values, type) => {
          //     if (type === 'get') {
          //       return {
          //         ...values,
          //       };
          //     }
          //     return values;
          //   },
          // }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
        />

        <Card title="公示红头文件" className=" mt-4">
          {fileGroupId ? <EventFile fileGroupId={fileGroupId} /> : null}
        </Card>
      </PageContainer>
    </Drawer>
  );
};

export default PublicityDetail;
