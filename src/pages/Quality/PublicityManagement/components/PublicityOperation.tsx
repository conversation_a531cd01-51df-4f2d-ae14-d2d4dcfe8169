/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Button, Drawer, message, Modal } from 'antd';
import { fileGroupApi } from '@/api/common';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import {
  publicityApi,
  publicitySaveApi,
  TPublicitySaveParams,
} from '@/api/publicity';
import { getQATask, getQATaskList } from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { DownloadOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import EProFormUploadButton from '@/components/EProFormUploadButton';
import FileViewByStream from '@/components/FileViewByStream';
import { getFileTypeByName, getIconByName } from '@/utils/upload';

type TPublicityDetailProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  taskId: string;
  fileGroupId: string;
  operateType: 1 | 2;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const layoutProps = {
  colProps: { span: 8 },
};

const PublicityOperation: React.FC<TPublicityDetailProps> = ({
  open,
  setOpen,
  taskId,
  operateType,
}) => {
  const { token } = useTokenStore();
  const formRef = useRef<ProFormInstance>(null);
  const baseInfoRef = useRef<ProFormInstance>(null);
  const [btnLoading, setBtnLoading] = useState(false);
  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');
  const [fileUrl, setFileUrl] = useState<any>();
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  const close = () => {
    setOpen(false);
    formRef.current?.resetFields();
  };

  /**
   * 获取任务的详情信息
   * @param businessId
   */
  const getTaskDetail = async (taskId: string) => {
    try {
      const { code, data, msg } = await getQATask(taskId);
      if (code !== codeDefinition.QUERY_SUCCESS) {
        message.error(msg);
        return;
      }
      baseInfoRef.current?.setFieldValue('particularYear', data.particularYear);
      baseInfoRef.current?.setFieldValue('name', data.name);
      baseInfoRef.current?.setFieldValue(
        'assessmentTypeLabel',
        data.assessmentTypeLabel
      );
      baseInfoRef.current?.setFieldValue('endDate', dayjs(data.endDate));
      baseInfoRef.current?.setFieldValue('people', data.people);
      baseInfoRef.current?.setFieldValue('phone', data.phone);
      baseInfoRef.current?.setFieldValue('remark', data.remark);
      formRef.current?.setFieldValue('files', data.fileGroup);
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      // finally todo ...
    }
  };

  // 获取公示红头文件
  const getFile = async (businessId: string) => {
    try {
      const { code, data, msg } = await fileGroupApi({ businessId });
      if (code === codeDefinition.QUERY_SUCCESS) {
        if (data && data.length) {
          const _filesList = data.map((_item: any) => ({
            response: {
              code: 200,
              data: {
                fileName: _item.originalName,
                url: _item.url || `https://www.xxx.com/${_item?.fileName}`,
                ossId: _item.ossId,
              },
            },
            fileName: _item.originalName,
            url: _item.url || `https://www.xxx.com/${_item?.fileName}`,
            status: 'done',
            name: _item.originalName,
            uid: _item.ossId,
          }));
          formRef.current?.setFieldValue('file', _filesList);
        }
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getFile(taskId);
    getTaskDetail(taskId);
  }, [taskId]);

  const handleSave = async (type: number) => {
    try {
      setBtnLoading(true);
      const files = formRef.current?.getFieldValue('file');
      const _params: TPublicitySaveParams = {
        taskId,
      };
      if (files && files.length) {
        const _newFiles = files.filter(
          (_item: any) => _item.response.code === 200
        );
        if (_newFiles.length) {
          _params.fileGroup = {
            fileName: _newFiles[0].response.data.fileName,
            fileAddr: _newFiles[0].response.data.url,
            //@ts-ignore
            fileSize: _newFiles[0].size,
            ossId: _newFiles[0].response.data.ossId,
          };
        }
      }
      const saveFetch = type === 1 ? publicitySaveApi : publicityApi;
      const { code, msg } = await saveFetch(_params);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
    } finally {
      setBtnLoading(false);
    }
  };

  /**
   * @TODO 文件上传
   */
  const handleUploadFiles = (info: any) => {
    const { file, fileList } = info;
    if (file.status === 'done' || file.status === 'removed') {
      if (
        file.status === 'done' &&
        file.response &&
        file.response.code !== 200
      ) {
        message.error(file.response.msg);
      } else {
        message.success(file.response.msg);
      }
    }
  };

  return (
    <Drawer
      width="80%"
      title={operateType === 1 ? '公示' : '详情'}
      onClose={() => setOpen(false)}
      open={open}
      destroyOnClose
      classNames={{
        body: 'bg-[#F5F5F5] !p-0',
      }}
      footer={
        operateType === 1 ? (
          <div className="h-full flex justify-center items-center shadow-2xl z-10 gap-3">
            <Button type="default" onClick={close}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => handleSave(1)}
              loading={btnLoading}
            >
              保存
            </Button>
            <Button
              type="primary"
              onClick={() => handleSave(2)}
              loading={btnLoading}
            >
              公示
            </Button>
          </div>
        ) : null
      }
    >
      <div className="p-4 flex flex-col gap-4">
        {/* 基本信息 */}
        <BlockContainer title="基本信息">
          <ProForm
            readonly={true}
            formRef={baseInfoRef}
            {...formItemLayout}
            layout="horizontal"
            grid={true}
            submitter={false}
          >
            <ProFormSelect
              readonly={true}
              options={[]}
              rules={[{ required: true, message: '请选择年份' }]}
              name="particularYear"
              label="年份"
              {...layoutProps}
            />
            <ProFormText
              readonly={true}
              name="name"
              label="任务名称"
              placeholder="请输入任务名称"
              rules={[
                {
                  required: true,
                  validator(rule, value, callback) {
                    if (!value) {
                      callback('请输入任务名称');
                    }
                    if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value)) {
                      callback('任务名称只能由中文字符、英文字符、数字组成');
                    } else {
                      callback();
                    }
                  },
                },
              ]}
              {...layoutProps}
            />
            <ProFormText
              readonly={true}
              rules={[{ required: true, message: '请选择考核类型' }]}
              name="assessmentTypeLabel"
              label="考核类型"
              {...layoutProps}
            />
            <ProFormDatePicker
              readonly={true}
              rules={[{ required: true, message: '请选择' }]}
              initialValue={dayjs()}
              name="endDate"
              label="结束日期"
              width={'lg'}
              {...layoutProps}
            />
            <ProFormText
              readonly={true}
              name="people"
              label="任务负责人"
              placeholder="请输入任务负责人"
              rules={[{ required: true, message: '请输入任务负责人' }]}
              {...layoutProps}
            />
            <ProFormText
              readonly={true}
              name="phone"
              label="联系电话"
              placeholder="请输入联系电话"
              rules={[{ required: true, message: '请输入联系电话' }]}
              {...layoutProps}
            />
            <ProFormTextArea
              readonly={true}
              colProps={{ span: 24 }}
              labelCol={{ flex: 0.036 }}
              name="remark"
              label="备注信息"
            />
            <ProFormUploadButton
              name="attachmentIds"
              label="附件信息"
              colProps={{ span: 24 }}
              labelCol={{ flex: 0.036 }}
              valuePropName="fileList"
              max={1}
              fieldProps={{
                iconRender: (file) => {
                  return (
                    <img
                      src={getIconByName(file.name)}
                      className="!w-[40px] !h-[40px] m-auto mt-2"
                      alt="logo"
                    />
                  );
                },
                maxCount: 1,
                name: 'file',
                listType: 'picture-card',
                headers: {
                  Authorization: `Bearer ${token}`,
                },
                async onPreview(file: any) {
                  if (
                    file.status === 'done' &&
                    file.response &&
                    file.response.data &&
                    file.response.data.ossId
                  ) {
                    const type = getFileTypeByName(file.response.data.fileName);
                    const d = await getFileData(file.response.data.ossId);
                    setPreviewFileId(file?.uid);

                    if (type === 'Image') {
                      setIsShowFileData({
                        name: file.response.data.fileName,
                        url: d,
                        ossId: file.response.data.ossId,
                      });
                      setIsShowFileView(true);
                    } else {
                      if (file.response.data.url) {
                        setFileUrl(file.response.data.url);
                        setOpenPreview(true);
                      } else {
                        downloadFile(
                          file.response.data.ossId,
                          file.response.data.fileName
                        );
                      }
                    }
                  }
                },
              }}
              action={uploadFiles}
              wrapperCol={{
                span: 24,
              }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </ProFormUploadButton>
          </ProForm>
        </BlockContainer>
        {/* 红头文件 */}
        <BlockContainer
          title={
            <div className="flex flex-row items-center gap-1">
              <span className="text-red-500">*</span>
              <span>公示红头文件</span>
            </div>
          }
        >
          <div className="p-4">
            <ProForm
              formRef={formRef}
              layout="horizontal"
              grid={true}
              rowProps={{
                gutter: [24, 0],
              }}
              {...formItemLayout}
              submitter={false}
            >
              <ProFormUploadButton
                name="file"
                colProps={{ span: 24 }}
                labelCol={{ flex: 0.005 }}
                max={1}
                fieldProps={{
                  maxCount: 1,
                  iconRender: (file) => {
                    return (
                      <img
                        src={getIconByName(file.name)}
                        className="!w-[40px] !h-[40px] m-auto mt-2"
                        alt="logo"
                      />
                    );
                  },
                  name: 'file',
                  listType: 'picture-card',
                  disabled: operateType === 2,
                  onChange: async (info) => {
                    await handleUploadFiles(info);
                  },
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                  accept: '.pdf',
                  showUploadList: {
                    showDownloadIcon: true,
                    downloadIcon: <DownloadOutlined />,
                    showPreviewIcon: true,
                    previewIcon: <EyeOutlined />,
                  },
                  onDownload: (file: any) => {
                    downloadFile(
                      file.response.data.ossId,
                      file.response.data.fileName
                    );
                  },
                  async onPreview(file: any) {
                    if (
                      file.status === 'done' &&
                      file.response &&
                      file.response.data &&
                      file.response.data.ossId
                    ) {
                      const type = getFileTypeByName(
                        file.response.data.fileName
                      );
                      const d = await getFileData(file.response.data.ossId);
                      setPreviewFileId(file.response.data.ossId);

                      if (type === 'Image') {
                        setIsShowFileData({
                          name: file.response.data.fileName,
                          url: d,
                          ossId: file.response.data.ossId,
                        });
                        setIsShowFileView(true);
                      } else {
                        if (file.response.data.url) {
                          setFileUrl(file.response.data.url);
                          setOpenPreview(true);
                        } else {
                          downloadFile(
                            file.response.data.ossId,
                            file.response.data.fileName
                          );
                        }
                      }
                    }
                  },
                }}
                action={uploadFiles}
                wrapperCol={{
                  span: 24,
                }}
                extra="注意: 请上传一个pdf格式的红头文件"
              >
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              </ProFormUploadButton>
            </ProForm>
          </div>
        </BlockContainer>

        {/* 预览pdf\word */}
        {openPreview && (
          <>
            <Modal
              width="60%"
              title="文件预览"
              onCancel={() => setOpenPreview(false)}
              open={openPreview}
              footer={null}
              destroyOnClose
            >
              <FileViewByStream fileId={previewFileId} />
            </Modal>
          </>
        )}
      </div>
    </Drawer>
  );
};

export default PublicityOperation;
