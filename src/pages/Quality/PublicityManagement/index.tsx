/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import {
  homePublicityTopApi,
  publicityList<PERSON>pi,
  TPublicityListParams,
} from '@/api/publicity';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useQualityStore } from '@/store/quality';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import PublicityDetail from './components/PublicityDetail';
import PublicityOperation from './components/PublicityOperation';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

const PublicityManagement: React.FC = () => {
  const [pageSize, setPageSize] = useState(10);
  const [activeKey, setActiveKey] = useState('0');
  // 统计数量
  const [counts, setCounts] = useState({
    unPub: 0,
    pub: 0,
  });
  const [init, setInit] = useState(true);

  const [curTaskId, setCurTaskId] = useState('');
  const [curFileGroupId, setCurFileGroupId] = useState('');
  const [publicityDetailOpen, setPublicityDetailOpen] = useState(false);
  // 1 编辑公示 2 详情
  const [operateType, setOperateType] = useState<1 | 2>(1);

  const actionRef = useRef<ActionType>();

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    setInit(true);
    actionRef.current?.reload();
  };

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes } = useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
  }, []);

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 60,
      title: '序号',
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'taskYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
      width: 80,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      ellipsis: true,
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
      width: 200,
    },
    {
      title: '任务负责人',
      dataIndex: 'creator',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '考核机构数量',
      dataIndex: 'orgCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '通过机构数量',
      dataIndex: 'passedCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '通过率',
      dataIndex: 'passedPercent',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '优秀机构数量',
      dataIndex: 'excellentCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '合格机构数量',
      dataIndex: 'onLevelCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '不合格机构数量',
      dataIndex: 'unPassedCount',
      hideInSearch: true,
      width: 120,
    },
  ];

  // 置顶
  const handlePubTop = async (taskId: string) => {
    try {
      const { code, msg } = await homePublicityTopApi(taskId);
      if (code === 200) {
        message.success(QUERY_SUCCESS_MSG);
        tableReload();
      } else {
        message.error(msg);
      }
    } catch (error) {}
  };

  const getColumns = useCallback((): ProColumns[] => {
    if (activeKey === '0') {
      return [
        ...columns,
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 80,
          render: (text, record, _, action) => (
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurTaskId(record.taskId);
                setCurFileGroupId(record.fileGroupId);
                setOperateType(1);
                setPublicOpen(true);
              }}
            >
              公示
            </Button>
          ),
          fixed: 'right',
        },
      ];
    } else {
      return [
        ...columns,
        {
          title: '操作',
          valueType: 'option',
          key: 'option',
          width: 120,
          render: (text, record, _, action) => [
            <Button
              type="link"
              size="small"
              onClick={() => {
                setCurTaskId(record.taskId);
                setCurFileGroupId(record.fileGroupId);
                setOperateType(2);
                setPublicOpen(true);
              }}
            >
              详情
            </Button>,
            <Button
              type="link"
              size="small"
              onClick={() => handlePubTop(record.taskId)}
            >
              置顶
            </Button>,
          ],
          fixed: 'right',
        },
      ];
    }
  }, [activeKey]);

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  // 公示
  const [publicOpen, setPublicOpen] = useState(false);

  return (
    <PageContainer>
      <ProTable
        columns={getColumns()}
        actionRef={actionRef}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey,
            items: [
              {
                key: '0',
                label: <span>待公示（{counts.unPub}）</span>,
              },
              {
                key: '1',
                label: <span>已公示（{counts.pub}）</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        cardBordered
        bordered
        request={async (params, sort, filter) => {
          let err = false;
          const _params: TPublicityListParams = {
            pageSize: params.pageSize!,
            pageNum: params.current!,
            isPublished: activeKey,
            taskYear: params.taskYear,
            taskName: params.taskName,
            taskType: params.taskType,
            ...params,
          };
          const { code, data, msg } = await publicityListApi(_params);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
            err = true;
          }
          if (!err) {
            const res = await publicityListApi({
              ..._params,
              isPublished: activeKey === '1' ? '0' : '1',
            });
            setCounts({
              unPub: activeKey === '0' ? data.total : res.data.total,
              pub: activeKey === '1' ? data.total : res.data.total,
            });
            // setInit(false);
          }
          return {
            data: data.rows ?? [],
            total: data.total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="taskId"
        search={{
          defaultCollapsed: false,
          labelWidth: 80,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        scroll={{ x: 'auto' }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 详情 */}
      <PublicityDetail
        open={publicityDetailOpen}
        setOpen={setPublicityDetailOpen}
        taskId={curTaskId}
        fileGroupId={curFileGroupId}
      />
      {/* 公示 */}
      {publicOpen && (
        <PublicityOperation
          open={publicOpen}
          setOpen={(val) => {
            tableReload();
            setPublicOpen(val);
          }}
          taskId={curTaskId}
          fileGroupId={curFileGroupId}
          operateType={operateType}
        />
      )}
    </PageContainer>
  );
};

export default PublicityManagement;
