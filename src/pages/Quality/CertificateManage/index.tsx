/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { Button, message, Modal, Popconfirm, Table, Tag } from 'antd';
import { createOneApi, getCertificateList, sendBatch } from '@/api/certificate';
import { getFileOssObjApi } from '@/api/file';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import EExcelFileView from '@/components/EExcelFileView';
import EFileView from '@/components/EFileView';
import FileViewByStream from '@/components/FileViewByStream';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';
import { waitTime, waitTimePromise } from '@/utils/helpers';
import './index.less';

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();
  const [fileUrl, setFileUrl] = useState<any>();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();
  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);
  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    actionRef.current?.reload();
  };
  const [loadingRowIndex, setLoadingRowIndex] = useState<string>('');
  // 代放发 已发放
  const [activeKey, setActiveKey] = useState<string>('0');
  // 批量发放按钮
  const [batchSendLoading, setBatchSendLoading] = useState<boolean>(false);
  // 全部发放按钮
  const [allSendLoading, setAllSendLoading] = useState<boolean>(false);
  // 预览
  const [openPreview, setOpenPreview] = useState<boolean>(false);

  const [newSelectedRowKey, setNewSelectedRowKeys] = useState<any>([]);

  const [pageSize, setPageSize] = useState<number>(10);

  // 当前需要预览的文件ID
  const [previewFileId, setPreviewFileId] = useState<string>('');

  /**
   * @TODO 批量发放
   */
  const createBatch = async (type: any) => {
    let param = {};
    if (type === 0) {
      if (!newSelectedRowKey.length) {
        message.warning('请选择要下发的证书');
        return;
      }
      setBatchSendLoading(true);
      param = {
        type,
        ids: newSelectedRowKey.join(','),
      };
    } else {
      setAllSendLoading(true);
      param = {
        type,
      };
    }
    try {
      const { code, msg } = await sendBatch(param);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success('发放成功');
        actionRef.current?.reload();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setAllSendLoading(false);
      setBatchSendLoading(false);
    }
  };

  // 当行证书发放
  const createBatchOne = async (id: any) => {
    const param = {
      type: 1,
      id: id,
    };
    const { code, msg } = await createOneApi(param);
    if (code === codeDefinition.QUERY_SUCCESS) {
      message.success('发放成功');
      actionRef.current?.reload();
    } else {
      message.error(msg);
    }
  };

  // 预览证书
  const previewCertificate = async (record: any) => {
    try {
      let fileId = '';
      if (record.file) {
        fileId = record.file;
      } else {
        const param = {
          type: 0,
          id: record.id,
        };
        const { msg } = await createOneApi(param);
        fileId = msg;
        tableReload();
      }

      const { code, data, msg } = await getFileOssObjApi(fileId);
      if (code === 200) {
        if (data && data.length) {
          setFileUrl(data[0].url);
          setPreviewFileId(data[0].ossId);
          setOpenPreview(true);
        } else {
          message.error(msg);
        }
      }
    } catch (error) {
      throw new Error(`${error}`);
    } finally {
      setLoadingRowIndex('');
    }
  };

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'year',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '被考核机构',
      dataIndex: 'deptName',
      hideInSearch: true,
    },
    {
      title: '评判日期',
      dataIndex: 'judgeDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '样品总得分',
      dataIndex: 'sampleScore',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '其它(资料)总得分',
      dataIndex: 'dataScore',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '最终得分',
      dataIndex: 'score',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '考核结论',
      dataIndex: 'result',
      hideInSearch: true,
      align: 'center',
      render(text, row) {
        return (
          <>
            {row.result === '优秀' && <Tag color="success">{text}</Tag>}
            {row.result === '合格' && <Tag color="processing">{text}</Tag>}
            {row.result === '不合格' && <Tag color="error">{text}</Tag>}
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      render: (text, record, _) => {
        let btns: any = [];
        if (activeKey === '0') {
          btns = [
            // <Button
            //   type="link"
            //   size="small"
            //   key="edit"
            //   loading={loadingRowIndex === record.id}
            //   onClick={() => {
            //     setLoadingRowIndex(record.id);
            //     previewCertificate(record);
            //   }}
            // >
            //   预览证书
            // </Button>,
            <Popconfirm
              title="发放此证书？"
              onConfirm={() => createBatchOne(record.id)}
              okText="确定"
              cancelText="取消"
              key="edit2"
            >
              <Button type="link" size="small" key="send">
                发放证书
              </Button>
            </Popconfirm>,
          ];
        } else {
          btns = [
            <Button
              type="link"
              size="small"
              key="edit"
              loading={loadingRowIndex === record.id}
              onClick={() => {
                setLoadingRowIndex(record.id);
                previewCertificate(record);
              }}
            >
              预览证书
            </Button>,
          ];
        }

        return btns;
      },
    },
  ];

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  // 选择表头多选框
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setNewSelectedRowKeys(newSelectedRowKeys);
    // console.log('selectedRowKeys changed: ', newSelectedRowKeys);
  };
  return (
    <PageContainer>
      <ProTable<QualityTaskItem>
        columns={columns}
        actionRef={actionRef}
        rowSelection={
          activeKey === '0'
            ? {
                // 注释该行则默认不显示下拉选项
                selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [],
                onChange: onSelectChange,
              }
            : undefined
        }
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: '0',
                label: <span>待发放</span>,
              },
              {
                key: '1',
                label: <span>已发放</span>,
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        toolBarRender={() => [
          <Button
            key="buttonAll"
            className={activeKey === '1' ? 'btn-hide' : 'btn-show'}
            loading={batchSendLoading}
            disabled={allSendLoading}
            type="primary"
            onClick={() => {
              createBatch(0);
            }}
          >
            批量发放
          </Button>,
          <Button
            key="button"
            className={activeKey === '1' ? 'btn-hide' : 'btn-show'}
            disabled={batchSendLoading}
            loading={allSendLoading}
            type="primary"
            onClick={() => {
              createBatch(1);
            }}
          >
            全部发放
          </Button>,
        ]}
        cardBordered
        bordered
        request={async (params) => {
          const param = {
            ...params,
            pageNum: params.current,
            pageSize: params.pageSize,
            isSend: activeKey,
          };
          delete param.current;
          const { code, rows, total, msg } = await getCertificateList(param);
          if (code !== codeDefinition.QUERY_SUCCESS) {
            message.error(msg);
          }
          return {
            data: rows ?? [],
            total: total ?? 0,
            success: true,
          };
        }}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 70,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          size: 'default',
          showSizeChanger: true,
          pageSize: pageSize,
          onShowSizeChange: (current, size) => {
            setPageSize(size);
          },
        }}
        dateFormatter="string"
      />
      {/* 
      {/* 预览 */}
      {openPreview && (
        <>
          {/* {fileUrl?.includes('xlsx') ? (
            <EExcelFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          ) : (
            <EFileView
              open={openPreview}
              close={() => setOpenPreview(false)}
              blobUrl={fileUrl}
            />
          )} */}
          <Modal
            width="60%"
            title="文件预览"
            onCancel={() => setOpenPreview(false)}
            open={openPreview}
            footer={null}
            destroyOnClose
          >
            <FileViewByStream fileId={previewFileId} isPreview />
          </Modal>
        </>
      )}
    </PageContainer>
  );
};
export default QualityTask;
