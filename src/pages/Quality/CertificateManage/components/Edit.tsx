import { useEffect, useRef, useState } from 'react';
import { Button, message } from 'antd';
import type { UploadProps } from 'antd';
import { downloadFile, getFileData, uploadFiles } from '@/api/file';
import { getFillDetail, judge, receptFill } from '@/api/fill';
import { addQATask, getQATask, sendQATask, updateQATask } from '@/api/quality';
import { codeDefinition, QUERY_SUCCESS_MSG } from '@/constants';
import { useTokenStore } from '@/store';
import { PlusOutlined } from '@ant-design/icons';
import {
  BetaSchemaForm,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import BlockContainer from '@/components/BlockContainer';
import FileView from '@/components/FileView';
import { getIconByName } from '@/utils/upload';
import { getFileTypeByName } from '@/utils/upload';
import BaseForm from './BaseForm';
import './Edit.less';
import JudgeForm from './JudgeForm';

type TEditProps = {
  close: () => void;
  detailInfo?: any;
  showType?: any;
  detailId?: any
  readOnly?: boolean;
};

const MenuDetail: React.FC<TEditProps> = ({
  close,
  detailInfo,
  showType,
  readOnly = false,
}) => {
  const { token } = useTokenStore();
  const [loading, setLoading] = useState<boolean>(false);

  // 文件预览
  const [isShowFileView, setIsShowFileView] = useState(false);
  const [isShowFileData, setIsShowFileData] = useState<any>();

  const uploadProps: UploadProps | any = {
    action: uploadFiles,
    fieldProps: {
      disabled: true,
      headers: { Authorization: `Bearer ${token}` },
      listType: 'picture-card',
      // className: 'upload-list-inline',
      async onPreview(file: any) {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.data &&
          file.response.data.ossId
        ) {
          const type = getFileTypeByName(file.response.data.fileName);
          if (type === 'Image') {
            const d = await getFileData(file.response.data.ossId);
            setIsShowFileData({
              name: file.response.data.fileName,
              url: d,
              ossId: file.response.data.ossId
            });
            setIsShowFileView(true);
          } else {
            downloadFile(file.response.data.ossId, file.response.data.fileName);
          }
        }
      },
      iconRender: (file: any) => {
        return (
          <img
            src={getIconByName(file.name)}
            className="!w-[40px] !h-[40px] m-auto mt-2"
            alt="logo"
          />
        );
      },
    },
    onChange(info: any) {
      const { file } = info;
      if (file.status === 'done' || file.status === 'removed') {
        if (
          file.status === 'done' &&
          file.response &&
          file.response.code !== 200
        ) {
          message.error(file.response.msg);
        } else {
          message.success(file.response.msg);
        }
      }
    },
  };

  function setup(orgData: any[]) {
    return orgData.map((item: any) => {
      if (item.inputBox === 'text') {
        return {
          readonly: true,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'select') {
        const enums: any = {};
        if (item.enumItems) {
          item.enumItems.split(',').map((en: any) => {
            enums[en] = {
              text: en,
            };
          });
        }
        return {
          readonly: true,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'select',
          valueEnum: enums,
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'digit') {
        return {
          readonly: true,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'digit',
          fieldProps: {
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'calendar') {
        return {
          readonly: true,
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: item.fieldValue,
          valueType: 'date',
          fieldProps: {
            format: 'YYYY-MM-DD',
            style: { width: '100%' },
          },
          formItemProps: {
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
        };
      } else if (item.inputBox === 'file') {
        let fileList = [];
        try {
          fileList = item.fieldValue
            ? JSON.parse(item.fieldValue).map((item: any) => {
              return {
                uid: item.id,
                name: item.name,
                status: 'done',
                type: 'application/msword',
                url: item.id,
                response: {
                  data: {
                    fileName: item.name,
                    ossId: item.id,
                  },
                },
              };
            })
            : [];
        } catch (error) {
          fileList = [];
        }
        return {
          title: item.fieldName,
          dataIndex: 'f' + item.id,
          initialValue: fileList,
          colProps: { span: 24 },
          formItemProps: {
            className: 'inspect-upload-style',
            labelCol: { span: 1 },
            tooltip: item.pomptInformation,
            rules: [
              {
                required: item.isRequired,
                message: '此项为必填项',
              },
            ],
          },
          renderFormItem: () => (
            <ProFormUploadButton
              {...uploadProps}
              name={'f' + item.id}
              wrapperCol={{ span: 24 }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </ProFormUploadButton>
          ),
        };
      }
    });
  }
  const [taskBaseInfo, setTaskBaseInfo] = useState<any>({});
  const [baseInfo, setBaseInfo] = useState<any>({});
  const [form1Data, setForm1Data] = useState([]);
  const [form2List, setForm2List] = useState([]);

  const init = async () => {
    if (detailInfo && detailInfo.id) {
      try {
        const { code, data, msg } = await getQATask(
          detailInfo.assessmentTaskId
        );
        if (code === codeDefinition.QUERY_SUCCESS) {
          setTaskBaseInfo(data);
        } else {
          message.error(msg);
        }

        const {
          code: code1,
          data: data1,
          msg: msg1,
        } = await getFillDetail(detailInfo.id);
        if (code1 === codeDefinition.QUERY_SUCCESS) {
          setBaseInfo(data1);
          const form1 = data1.inspectionBasicInformation;
          const f1: any = setup(form1);
          setForm1Data(f1);
          // 2
          const form2s: any = [];
          data1.sampleInspectionInformation.map((item: any) => {
            const row = { ...item };
            const form2 = item.sampleInspectionInformationList;
            const f2: any = setup(form2);
            row.formInfo = f2;
            form2s.push(row);
          });
          setForm2List(form2s);
        } else {
          message.error(msg1);
        }
      } catch (error) {
        console.error(error);
        throw new Error(`Error: ${error}`);
      }
    }
  };
  useEffect(() => {
    init();
  }, [detailInfo]);

  type DataItem = {
    name: string;
    state: string;
  };

  // 这里通过 useRef 创建一个数组
  const formRef1 = useRef<any>(null);
  const listRefs = useRef<Array<any | null>>([]);

  const handleSave = async () => {
    judgeFormRef.current?.handleSubmit();
  };

  const judgeFormRef = useRef<any>(null);

  const handleSaveSubmit = async (params: any) => {
    try {
      setLoading(true);
      const realParams = {
        ...params,
      };
      const { code, data, msg }: any = await judge(realParams);
      if (code === codeDefinition.QUERY_SUCCESS) {
        message.success(QUERY_SUCCESS_MSG);
        close();
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };
  const formRefBase = useRef();
  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-auto p-4">
        <BaseForm
          hideRank
          readonly={true}
          onRef={formRefBase}
          id={detailInfo.assessmentTaskId}
          detailInfo={taskBaseInfo}
          onSubmit={(val) => { }}
        />
        <div className="mt-4">
          <BlockContainer title="检验基本信息">
            <BetaSchemaForm<DataItem>
              layoutType={'Form'}
              layout="horizontal"
              formRef={formRef1}
              rowProps={{
                gutter: [0, 0],
              }}
              labelCol={{
                offset: 1,
              }}
              colProps={{
                span: 6,
              }}
              grid={true}
              columns={form1Data as any}
              submitter={false}
            />
          </BlockContainer>
        </div>
        <div className="mt-4">
          <BlockContainer title="样本检验信息" bg="#F5F5F5">
            {form2List.map((item: any, i: any) => {
              return (
                <div
                  key={'t' + i}
                  className={i < form2List.length - 1 ? 'mb-2' : ''}
                >
                  <BlockContainer title={`样本编号：${item.sampleNumber}`}>
                    <BetaSchemaForm<DataItem>
                      formRef={
                        ((ref: any): void => (listRefs.current[i] = ref)) as any
                      }
                      layoutType={'Form'}
                      layout="horizontal"
                      rowProps={{
                        gutter: [0, 0],
                      }}
                      // labelCol={{
                      //   span: 6,
                      // }}
                      colProps={{
                        span: 6,
                      }}
                      grid={true}
                      columns={item.formInfo as any}
                      submitter={false}
                    />
                  </BlockContainer>
                </div>
              );
            })}
          </BlockContainer>
        </div>
        {baseInfo && baseInfo.taskDetails ? (
          <div className="mt-4">
            <BlockContainer title="结果评判">
              {baseInfo.remarks && (
                <div className="text-red-500 pb-2">
                  评分规则说明：{baseInfo.remarks || '-'}
                </div>
              )}

              <JudgeForm
                onSubmit={(val) => {
                  handleSaveSubmit(val);
                }}
                showType={showType}
                id={detailInfo.id}
                detailInfo={baseInfo}
                onRef={judgeFormRef}
                readonly={false}
              />
            </BlockContainer>
          </div>
        ) : undefined}
      </div>
      {!readOnly ? (
        <div className="h-[50px] bg-white flex justify-center items-center shadow-2xl shadow-black z-10 gap-3">
          <Button
            onClick={() => {
              handleSave();
            }}
            type="primary"
            loading={loading}
          >
            提交评判信息
          </Button>
        </div>
      ) : undefined}
      <FileView
        open={isShowFileView}
        file={isShowFileData}
        closeDetail={() => {
          setIsShowFileView(false);
        }}
      />
    </div>
  );
};

export default MenuDetail;
