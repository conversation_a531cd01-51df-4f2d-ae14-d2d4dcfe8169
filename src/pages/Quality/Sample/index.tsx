/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useRef, useState } from 'react';
import { Alert, Button, Drawer, Input, message, Modal, Space } from 'antd';
import {
  getReceptList,
  getTaskBySampleNum,
  handleConfirmTask,
} from '@/api/recept';
import { codeDefinition } from '@/constants';
import { useQualityStore } from '@/store/quality';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import Edit from './components/Edit';
import PageContainer from '@/components/PageContainer';
import { yearListOnTable } from '@/pages/data';

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type QualityTaskItem = Record<string, any>;

type TQualityTaskProps = {};

const QualityTask: React.FC<TQualityTaskProps> = () => {
  const actionRef = useRef<ActionType>();

  const [messageApi, contextHolder] = message.useMessage();

  // 获取table中需要的枚举
  const { assessmentTypesOnTable, getAssessmentTypes, getAssessmentTaskTypes } =
    useQualityStore();

  const [pageSize, setPageSize] = useState<number>(10);

  // 接收
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  // 详情ID
  const [detailId, setDetailId] = useState<string>('');

  const [activeKey, setActiveKey] = useState<string>('0');

  // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
  const [counts, setCounts] = useState<any>({
    '0': '-',
    '1': '-',
  });
  const [paramsCache, setParamsCache] = useState('');

  // 是否已上传回执单
  const [isUploadReceipt, setIsUploadReceipt] = useState<boolean>(false);
  // 是否打开上传回执单询问 Modal
  const [openUploadReceiptModal, setOpenUploadReceiptModal] =
    useState<boolean>(false);

  // 录入样品编号相关状态
  const [openInputSampleCodeModal, setOpenInputSampleCodeModal] =
    useState<boolean>(false);
  const [sampleCode, setSampleCode] = useState<string>('');
  const [searchResult, setSearchResult] = useState<Array<QualityTaskItem>>([]);
  const [searchError, setSearchError] = useState<string>('');
  const [showSearchResult, setShowSearchResult] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<Record<string, any>>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const columns: ProColumns<QualityTaskItem>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      disable: true,
      title: '年份',
      dataIndex: 'particularYear',
      filters: false,
      valueType: 'select',
      fieldProps: {
        options: yearListOnTable,
      },
    },
    {
      title: '任务名称',
      dataIndex: 'name',
    },
    {
      disable: true,
      title: '考核类型',
      dataIndex: 'assessmentType',
      valueType: 'select',
      valueEnum: assessmentTypesOnTable,
    },
    {
      title: '结束日期',
      dataIndex: 'endDate',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 160,
      render: (text, record, _, action) => {
        const btns = [
          <Button
            type="link"
            size="small"
            key="edit"
            onClick={() => {
              setDetailId(record.id);
              setOpenEdit(true);
            }}
          >
            {activeKey === '0'
              ? '接收登记(含回执单上传功能)'
              : '查看登记信息(含回执单上传功能)'}
          </Button>,
        ];

        return btns;
      },
    },
  ];

  /**
   * @TODO 关闭抽屉
   */
  const closeEdit = () => {
    setOpenEdit(false);
    setParamsCache('refreshCount');
    tableReload();
  };

  /**
   * @TODO 刷新
   */
  const tableReload = () => {
    // 重置分页状态
    // actionRef.current?.reset?.();
    // 然后重新加载数据
    actionRef.current?.reload();
  };

  useEffect(() => {
    tableReload();
  }, [activeKey]);

  useEffect(() => {
    getAssessmentTypes();
    getAssessmentTaskTypes();
  }, []);

  // 查询样品编号
  const handleSearchSample = async () => {
    if (!sampleCode.trim()) {
      messageApi.error('请输入样品编号');
      return;
    }

    try {
      // 清除之前的错误信息
      setSearchError('');

      // 这里应该调用实际的API查询样品编号
      const { code, data, msg } = await getTaskBySampleNum({
        sampleCode,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
      }

      setSearchResult(data);
      setShowSearchResult(true);
    } catch (error) {
      setSearchResult([]);
      setShowSearchResult(true);
      setSearchError('编号确实不存在，请确认编号是否输入正确');
    }
  };

  // 确认任务
  const confirmTask = async (currentTask: Record<string, any>) => {
    setConfirmLoading(true); // 开始loading
    try {
      const { code, msg } = await handleConfirmTask({
        ...currentTask,
      });
      if (code !== codeDefinition.QUERY_SUCCESS) {
        messageApi.error(msg);
        return;
      }
      messageApi.success('任务确认成功');
      setShowSearchResult(false);
      setOpenInputSampleCodeModal(false);
      tableReload();
    } catch (err) {
      throw new Error(`Error: err`);
    } finally {
      setConfirmLoading(false); // 结束loading
      // finally todo ...
    }
  };

  // 添加useEffect监听Modal打开状态变化
  useEffect(() => {
    if (openInputSampleCodeModal) {
      setSampleCode('');
      setSearchResult([]);
      setSearchError('');
      setShowSearchResult(false);
      setSelectedTask({});
    }
  }, [openInputSampleCodeModal]);

  return (
    <>
      {contextHolder}
      <PageContainer>
        <ProTable<QualityTaskItem>
          columns={columns}
          actionRef={actionRef}
          // key={activeKey}
          toolbar={{
            menu: {
              type: 'tab',
              activeKey: activeKey,
              items: [
                {
                  key: '0',
                  label: <span>待接收（{counts['0']}）</span>,
                },
                {
                  key: '1',
                  label: <span>已接收（{counts['1']}）</span>,
                },
              ],
              onChange: (key) => {
                setActiveKey(key as string);
                // actionRef.current?.reset?.();
                // 不再重置分页大小，让用户自己控制
              },
            },
          }}
          cardBordered
          bordered
          request={async (params, sort, filter) => {
            await waitTime(100);

            const param = {
              ...params,
              status: activeKey,
              pageNum: params.current || 1,
              pageSize: params.pageSize || pageSize,
              current: undefined,
            };
            const { code, rows, total, msg } = await getReceptList(param);
            if (code !== codeDefinition.QUERY_SUCCESS) {
              messageApi.error(msg);
            }

            // 为了用列表接口获取总计 缓存查询条件 查询条件不同时 多调用接口获取多个总计
            const paramsStr = JSON.stringify({
              ...params,
              current: undefined,
              pageSize: undefined,
            });
            if (paramsCache !== paramsStr) {
              setParamsCache(paramsStr);
              const { total: totalAnother } = await getReceptList({
                ...params,
                status: activeKey === '1' ? '0' : '1',
                pageNum: 1,
                pageSize: 0,
                current: undefined,
              });
              setCounts({
                [activeKey]: total ?? 0,
                [activeKey === '1' ? '0' : '1']: totalAnother ?? 0,
              });
            }

            return {
              data: rows ?? [],
              total: total ?? 0,
              success: true,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            defaultCollapsed: false,
            labelWidth: 80,
          }}
          form={{
            syncToUrl: false,
            resetOnTabChange: true,
          }}
          tableExtraRender={() => (
            <Alert
              message="注意事项：如在下方搜检任务中未查询到您实际接收的考核样品品，请您试用本页顶部的【录入考核样品】功能，手动输入一件样品编号来完成样品接收工作。"
              type="info"
              showIcon
              style={{ marginBottom: 16, marginTop: 16 }}
            />
          )}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          pagination={{
            size: 'default',
            showSizeChanger: true,
            pageSize: pageSize,
            defaultPageSize: 10,
            defaultCurrent: 1,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }}
          dateFormatter="string"
          headerTitle="标本接收"
          toolBarRender={() => [
            <Button
              key="input-sample"
              type="primary"
              onClick={() => {
                setSampleCode('');
                setSearchResult([]);
                setSearchError('');
                setShowSearchResult(false);
                setSelectedTask({});
                setOpenInputSampleCodeModal(true);
              }}
            >
              录入考核样品
            </Button>,
          ]}
        />
        {/* 详情 */}
        <Drawer
          width="90%"
          title={activeKey === '0' ? '接收登记' : '查看登记信息'}
          onClose={() =>
            isUploadReceipt ? closeEdit() : setOpenUploadReceiptModal(true)
          }
          open={openEdit}
          destroyOnClose
          classNames={{
            body: 'bg-[#F5F5F5] !p-0',
          }}
          maskClosable={false}
        >
          <Edit
            close={() =>
              isUploadReceipt ? closeEdit() : setOpenUploadReceiptModal(true)
            }
            detailId={detailId}
            setIsUploadReceipt={setIsUploadReceipt}
            mode={activeKey === '0' ? 'edit' : 'view'}
          />
        </Drawer>
        <Modal
          title="操作提示"
          centered
          width={360}
          open={openUploadReceiptModal}
          okText="继续上传回执单"
          cancelText="不需要上传回执单"
          onOk={() => {
            setOpenUploadReceiptModal(false);
          }}
          onCancel={() => {
            setOpenUploadReceiptModal(false);
            closeEdit();
          }}
          zIndex={9999}
          maskClosable={false}
        >
          <div>请再次确认本任务是否需要上传回执单</div>
        </Modal>
        {/* 录入查找样本编号 */}
        <Modal
          title="录入考核样品"
          open={openInputSampleCodeModal}
          footer={null}
          onCancel={() => {
            setOpenInputSampleCodeModal(false);
            setSampleCode('');
            setSearchResult([]);
            setSearchError('');
            setShowSearchResult(false);
            setSelectedTask({});
          }}
          width={showSearchResult ? 820 : 520}
        >
          {!showSearchResult ? (
            <div className="p-4">
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <span className="text-red-500 mr-1">*</span>
                  <span>样品编号：</span>
                </div>
                <Input
                  placeholder="请输入样品编号，多个编号请以半角逗号分隔"
                  value={sampleCode}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSampleCode(e.target.value)
                  }
                  onPressEnter={handleSearchSample}
                />
                {searchError && (
                  <div className="text-red-500 mt-2">{searchError}</div>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button onClick={() => setOpenInputSampleCodeModal(false)}>
                  取消
                </Button>
                <Button type="primary" onClick={handleSearchSample}>
                  查询
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4">
              <h3 className="text-lg font-medium mb-4">查询结果</h3>

              {searchError && (
                <Alert
                  message={searchError}
                  type="error"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              <div className="overflow-auto">
                <ProTable<QualityTaskItem>
                  columns={[
                    {
                      title: ' ',
                      dataIndex: 'select',
                      width: 60,
                      render: (_, record) => (
                        <input
                          type="radio"
                          name="selectedTask"
                          checked={selectedTask?.id === record.id}
                          onChange={() => setSelectedTask(record)}
                        />
                      ),
                    },
                    {
                      title: '年份',
                      dataIndex: 'year',
                      width: 80,
                    },
                    {
                      title: '任务名称',
                      dataIndex: 'taskName',
                    },
                    {
                      title: '样本编号',
                      dataIndex: 'sampleCodes',
                    },
                    {
                      title: '接收单位',
                      dataIndex: 'receiveUnit',
                    },
                    {
                      title: '任务负责人',
                      dataIndex: 'people',
                    },
                    {
                      title: '联系电话',
                      dataIndex: 'phone',
                    },
                  ]}
                  dataSource={searchResult}
                  rowKey="id"
                  pagination={false}
                  search={false}
                  options={false}
                  toolBarRender={false}
                  bordered
                  size="small"
                  cardProps={{
                    bodyStyle: { padding: 0 },
                  }}
                  locale={{
                    emptyText: '没有找到匹配的考核样品',
                  }}
                />
              </div>

              <div className="mt-4">
                <Alert
                  message="注意事项："
                  description={
                    <>
                      <p className="text-red-500">
                        1、请根据您所获取考核样的编号，仔细确认您本次样品属于哪个任务；
                      </p>
                      <p className="text-red-500">
                        2、如果收到的考核样具体单位误送误收，请联系任务负责人变更任务；
                      </p>
                    </>
                  }
                  type="info"
                  showIcon
                />
              </div>

              <div className="flex justify-end space-x-2 mt-4">
                <Button onClick={() => setShowSearchResult(false)}>取消</Button>
                <Button
                  type="primary"
                  onClick={() => {
                    if (searchResult.length === 0) {
                      messageApi.error('没有可选择的任务');
                      return;
                    }
                    if (selectedTask) {
                      confirmTask(selectedTask);
                    } else {
                      messageApi.error('请选择一个任务');
                    }
                  }}
                  disabled={searchResult.length === 0}
                  loading={confirmLoading}
                >
                  确认任务
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </PageContainer>
    </>
  );
};
export default QualityTask;
