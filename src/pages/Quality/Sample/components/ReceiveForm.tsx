/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, message, Modal } from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  FormInstance,
  ProForm,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import BlockContainer from '@/components/BlockContainer';
import CheckboxWithOther from '@/components/CheckboxWithOther';
import RequiredTag from '@/components/RequiredTag';
import ReceiveFormPitchAcceptDialog from './ReceiveFormPitchAcceptDialog';

type TEditProps = {
  loading?: boolean;
  baseInfo: any;
  onSubmit: (params: any) => void;
  onRef: any;
  mode: 'edit' | 'view'; // 编辑模式或查看模式
};

const ReceiveForm: React.FC<TEditProps> = ({
  loading = false,
  baseInfo,
  onSubmit,
  onRef,
  mode,
}) => {
  // 暴露给父组件
  useImperativeHandle(onRef, () => {
    return {
      handleSubmit: () => {
        formRef.current?.submit();
      },
    };
  });

  // 表单实例
  const formRef = useRef<FormInstance>(null);
  const tableRef = useRef<FormInstance>(null);

  type DataSourceType = {
    canEdit: boolean;
    id: React.Key;
    title?: string;
    readonly?: string;
    receiveMan?: string;
    decs?: string;
    state?: number;
    created_at?: number;
    update_at?: number;
    children?: DataSourceType[];
  };

  const defaultData: DataSourceType[] = [];

  //  配置表格数据
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const [isEdit, setIsEdit] = useState<boolean>(mode === 'edit');

  function init() {
    const editKeys = baseInfo?.taskDetails
      .filter((item: any) => item.status !== '1' && item.status !== 1)
      .map((item: any) => item.id);
    setEditableRowKeys(editKeys);
    setIsEdit(mode === 'edit' && editKeys.length > 0);
    formRef.current?.setFieldValue(
      'table',
      baseInfo?.taskDetails?.map((item: any) => {
        item.receiptDate = item.receiptDate ?? dayjs();
        return item;
      }) || []
    );
  }

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '样品编号',
      dataIndex: 'sampleCode',
      readonly: true,
      width: '10%',
    },
    {
      title: '接收单位',
      dataIndex: 'receiptUnitName',
      readonly: true,
      width: '15%',
    },
    {
      title: <RequiredTag title="接收日期" />,
      dataIndex: 'receiptDate',
      valueType: 'date',
      width: 140,
      readonly: mode === 'view',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择接收日期',
          },
        ],
      },
    },
    {
      title: <RequiredTag title="样本状态" />,
      dataIndex: 'sampleStatus',
      renderFormItem: () => (
        <CheckboxWithOther inline disabled={mode === 'view'} />
      ),
      width: 540,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择样本状态',
          },
        ],
      },
      render(value) {
        return <CheckboxWithOther value={value as string} disabled inline />;
      },
    },
    {
      title: '接收人',
      dataIndex: 'receiptPersonName',
      readonly: true,
      width: 100,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 60,
      hideInTable: mode === 'view',
      render: (text, record, _, action) => {
        return null;
      },
    },
  ];

  // 选择表头多选框
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 提交数据
  const handleSave = async (values: any) => {
    Modal.confirm({
      title: '确认接收？',
      onOk() {
        onSubmit(values);
      },
      onCancel() {},
    });
  };

  // 批量接收
  const [showPitchDialog, setShowPitchDialog] = useState(false);
  const acceptPitch = () => {
    if (!selectedRowKeys.length) {
      message.warning('请先勾选需要批量接收的样本');
      return;
    }
    setShowPitchDialog(true);
  };

  useEffect(() => {
    baseInfo && init();
  }, [baseInfo]);

  return (
    <>
      <BlockContainer title="样本列表">
        <ProForm<{
          table: DataSourceType[];
        }>
          formRef={formRef}
          initialValues={{
            table: [],
          }}
          submitter={false}
          onFinish={handleSave}
        >
          <EditableProTable<DataSourceType>
            rowKey="id"
            name="table"
            editableFormRef={tableRef}
            loading={loading}
            scroll={{
              x: 960,
            }}
            rowSelection={
              isEdit
                ? {
                    defaultSelectedRowKeys: [],
                    onChange: onSelectChange,
                    hideSelectAll: false,
                    fixed: true,
                    getCheckboxProps: (record: any) => ({
                      disabled: record.status === '1',
                    }),
                  }
                : undefined
            }
            recordCreatorProps={false}
            columns={columns}
            editable={{
              type: 'multiple',
              editableKeys,
              // saveText: '接收',
              onSave: async (rowKey, data, row) => {},
              actionRender: (row: any, config, defaultDom) => [
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    // 手动校验
                    if (!row.receiptDate) {
                      message.warning('请选择接收日期');
                      return;
                    }
                    if (!row.sampleStatus) {
                      message.warning('请选择样本状态');
                      return;
                    }
                    console.log(row, 'row');
                    
                    onSubmit([row]);
                  }}
                >
                  接收
                </Button>,
              ],
            }}
          />
        </ProForm>
        {isEdit ? (
          <Button
            type="primary"
            onClick={() => {
              acceptPitch();
            }}
            disabled={loading}
          >
            批量接收
          </Button>
        ) : (
          ''
        )}
        <div className="text-[13px] text-[red] mt-3">
          注：如因单份样本破损等其他情形导致无法检验的，不要接收样本，请直接联系任务负责人，重新补发考核样本。
        </div>
      </BlockContainer>
      <ReceiveFormPitchAcceptDialog
        open={showPitchDialog}
        onOk={(v) => {
          const list = formRef.current?.getFieldsValue().table;
          const newData = list.map((item: any) => {
            if (selectedRowKeys.includes(item.id)) {
              item.receiptDate = v.receiptDate;
              item.sampleStatus = v.sampleStatus;
            }
            return item;
          });
          onSubmit(
            newData.filter((item: any) => selectedRowKeys.includes(item.id))
          );
          setShowPitchDialog(false);
        }}
        onClose={() => {
          setShowPitchDialog(false);
        }}
      />
    </>
  );
};

export default ReceiveForm;
