import { useEffect, useRef } from 'react';

/**
 * 安全的setInterval hook，确保组件卸载时清理定时器
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）。传入null会暂停定时器
 */
const useInterval = (callback: () => void, delay: number | null) => {
  const callbackRef = useRef(callback);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // 每次callback变化时更新引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 创建/更新/暂停定时器
  useEffect(() => {
    // 如果delay为null，停止定时器
    if (delay === null) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // 创建定时器
    intervalRef.current = setInterval(() => {
      callbackRef.current();
    }, delay);

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [delay]);

  // 手动清理定时器的方法
  const clear = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  return clear;
};

export default useInterval;
