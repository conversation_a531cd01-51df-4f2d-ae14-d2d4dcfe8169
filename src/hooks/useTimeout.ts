import { useEffect, useRef } from 'react';

/**
 * 安全的setTimeout hook，确保组件卸载时清理定时器
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）
 * @returns [启动定时器的函数, 清理定时器的函数]
 */
const useTimeout = (callback: () => void, delay: number) => {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // 每次callback变化时更新引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 清理函数
  const clear = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // 设置新的定时器
  const set = () => {
    // 先清除已存在的定时器
    clear();

    // 设置新的定时器
    timeoutRef.current = setTimeout(() => {
      callbackRef.current();
    }, delay);
  };

  // 组件卸载时自动清理
  useEffect(() => {
    return clear;
  }, []);

  return [set, clear];
};

export default useTimeout;
