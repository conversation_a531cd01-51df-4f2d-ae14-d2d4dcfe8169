import * as echarts from 'echarts/core';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>lot<PERSON><PERSON>,
  Candlestick<PERSON>hart,
  <PERSON>auge<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>ctorialBar<PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  Treemap<PERSON>hart,
} from 'echarts/charts';
import type {
  // 系列类型的定义后缀都为 SeriesOption
  BarSeriesOption,
  BoxplotSeriesOption,
  CandlestickSeriesOption,
  GaugeSeriesOption,
  LineSeriesOption,
  MapSeriesOption,
  PictorialBarSeriesOption,
  PieSeriesOption,
  RadarSeriesOption,
  ScatterSeriesOption,
  TreemapSeriesOption,
} from 'echarts/charts';
import {
  // 数据集组件
  DatasetComponent,
  DataZoomComponent,
  GeoComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent, // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  VisualMapComponent,
} from 'echarts/components';
import type {
  DatasetComponentOption,
  GeoComponentOption,
  GridComponentOption,
  LegendComponentOption, // 组件类型的定义后缀都为 ComponentOption
  TitleComponentOption,
  TooltipComponentOption,
  VisualMapComponentOption,
} from 'echarts/components';
import type { ComposeOption } from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
export type ECOption = ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
  | LegendComponentOption
  | CandlestickSeriesOption
  | BoxplotSeriesOption
  | ScatterSeriesOption
  | TreemapSeriesOption
  | PieSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | VisualMapComponentOption
  | MapSeriesOption
  | GeoComponentOption
  | PictorialBarSeriesOption
>;

const useEcharts = () => {
  // 注册必须的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    BarChart,
    LineChart,
    LabelLayout,
    UniversalTransition,
    CanvasRenderer,
    LegendComponent,
    CandlestickChart,
    BoxplotChart,
    ScatterChart,
    DataZoomComponent,
    PieChart,
    TreemapChart,
    RadarChart,
    GaugeChart,
    VisualMapComponent,
    MapChart,
    GeoComponent,
    PictorialBarChart,
  ]);

  return [echarts];
};

export default useEcharts;
