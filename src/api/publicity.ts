import request from '@/utils/request';

enum API {
  GET_PUBLICITY_LIST = '/publish/info',
  POST_PUBLICITY_OLD = '/publish/pub',
  GET_HOME_PUBLICITY_LIST = '/publish/titles',
  POST_HOME_PUBLICITY_TOP = '/publish/top',
  GET_HOME_PUBLICITY_DETAIL_TASK_LIST = '/publish/detail',
  // 保存公示
  POST_PUBLICITY_SAVE = '/publish/upload',
  // 公示
  POST_PUBLICITY = '/publish/publicity',
  // 公示红头文件
  GET_PUBLICITY_FILE = '/publish/files',
}
export type TPublicityListParams = {
  pageNum: number;
  pageSize: number;
  isPublished: string; // 0未公示 1已公示
  taskYear?: number;
  taskName?: string;
  taskType?: string;
  orderByColumn?: string;
  isAsc?: string;
};
/**
 * @description 公示列表
 */
export const publicityListApi = (params: TPublicityListParams) =>
  request.get(API.GET_PUBLICITY_LIST, { params });

export type TPublicitySaveParams = {
  taskId: string;
  fileGroup?: {
    fileName: string;
    fileAddr: string;
    fileSize: string;
    ossId: string;
  };
};
/**
 * @description 保存公示
 */
export const publicitySaveApi = (data: TPublicitySaveParams) =>
  request.post(API.POST_PUBLICITY_SAVE, { data });

/**
 * @description 公示
 */
export const publicityApi = (data: TPublicitySaveParams) =>
  request.post(API.POST_PUBLICITY, { data });

/**
 * @description 首页公示列表
 */
export const homePublicityListApi = (params: {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
}) => request.get(API.GET_HOME_PUBLICITY_LIST, { params });

/**
 * @description 公示置顶
 */
export const homePublicityTopApi = (taskId: string) =>
  request.post(API.POST_HOME_PUBLICITY_TOP + `?taskId=${taskId}`);

/**
 * @description 首页公示详情考核任务列表
 */
export const homePublicityDetailTaskListApi = (params: {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
  taskId: string;
  orgName?: string;
}) => request.get(API.GET_HOME_PUBLICITY_DETAIL_TASK_LIST, { params });
