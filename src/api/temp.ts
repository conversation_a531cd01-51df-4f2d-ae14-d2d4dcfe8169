import request from '@/utils/request';

export enum API {
  GET_TEMP_LIST = '/template/page',
  ADD_TEMP = '/template/insert',
  UPDATE_TEMP = '/template/update',
  DETAIL_TEMP = '/template/details',
  SET_TEMP = '/template/updateFirstChoice',
  GET_TEMP_BY_TYPE = '/template/loadTemplateByAssessmentType',
  GET_TEMP_CODE = '/template/getTemplateCode',

  // 删除模板
  GET_DELETE_TEMPLATE = '/template/del',
}

/**
 * @description 模板-分页
 */
export const getTempList = async (params: any) =>
  request.get(API.GET_TEMP_LIST, { params });

/**
 * @description 模板详情
 */
export const getTempDetail = async (id: any) =>
  request.get(API.DETAIL_TEMP + '/' + id);

/**
 * @description 新增模板 
 */
export const addTemp = async (data: any) =>
  request.post(API.ADD_TEMP, { data });

/**
 * @description 修改模板 
 */
export const updateTemp = async (data: any) =>
  request.put(API.UPDATE_TEMP, { data });
/**
 * @description 设为首选模板
 
 */
export const setTemp = async (data: any) =>
  request.put(API.SET_TEMP, { data });

/**
 * @description 根据考核类型和质量考核填报任务ID加载首选模板
 */
export const getTempByType = async (params: any) =>
  request.get(API.GET_TEMP_BY_TYPE, { params });

/**
 * @description 模板详情
 */
export const getTempDetailCode = async () =>
  request.get(API.GET_TEMP_CODE);

/**
 * @description 删除模板
 */
export const deleteTemplate = async (params: { id: string, type: number }) =>
  request.get(API.GET_DELETE_TEMPLATE, { params });