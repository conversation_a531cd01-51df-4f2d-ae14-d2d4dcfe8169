import request from '@/utils/request';

export enum API {
  // ================= 区域采样率分析 =================
  // 列表数据
  Get_Regional_Sampling_Rate_Page = '/sample/rate/area/sampleAreaData',
  // 下转列表页数据
  Get_Regional_Sampling_Rate_Child_Page = '/sample/rate/area/sampleAreaNumDataPage',
  // 根据样本id获取详情
  Get_Details_By_Id = '/sample/rate/area/findById',
  // 导出统计结果
  Export_Regional_Sampling_Rate_Result = '/sample/rate/area/sampleAreaDataExport',
  // echarts图表数据
  Get_Regional_Sampling_Rate_Echarts_Data = '/sample/rate/area/sampleAreaStatistics',

  // ================= 年龄采样率分析 =================
  // 列表数据
  Get_Age_Sampling_Rate_Page = '/sample/rate/age/sampleAreaData',
  // 下转列表页数据
  Get_Age_Sampling_Rate_Child_Page = '/sample/rate/age/sampleAreaNumDataPage',
  // 根据样本id获取详情
  Get_Age_Sampling_Rate_Details_By_Id = '/sample/rate/age/findById',
  // echarts图表数据
  Get_Age_Sampling_Rate_Echarts_Data = '/sample/rate/age/sampleAreaStatistics',

  // ================= 人群采样率分析 =================
  // 列表数据
  Get_Population_Sampling_Rate_Page = '/sample/rate/people/samplePeopleList',
  // 下转列表页数据
  Get_Population_Sampling_Rate_Child_Page = '/sample/rate/people/samplePeopleNumDataPage',
  // 根据样本id获取详情
  Get_Population_Sampling_Rate_Details_By_Id = '/sample/rate/people/findById',
  // echarts图表数据
  Get_Population_Sampling_Rate_Echarts_Data = '/sample/rate/people/samplePeopleStatistics',

  // ================= 季节采样率分析 =================
  // 列表数据
  Get_Season_Sampling_Rate_Page = '/sample/rate/season/sampleSeasonData',
  // 下转列表页数据
  Get_Season_Sampling_Rate_Child_Page = '/sample/rate/season/sampleSeasonNumDataPage',
  // 根据样本id获取详情
  Get_Season_Sampling_Rate_Details_By_Id = '/sample/rate/season/findById',
  // echarts图表数据
  Get_Season_Sampling_Rate_Echarts_Data = '/sample/rate/season/sampleSeasonStatistics',

  // ================= 月度采样率分析 =================
  // 列表数据
  Get_Monthly_Sampling_Rate_Page = '/sample/rate/month/sampleAreaData',
  // 下转列表页数据
  Get_Monthly_Sampling_Rate_Child_Page = '/sample/rate/month/sampleAreaNumDataPage',
  // 根据样本id获取详情
  Get_Monthly_Sampling_Rate_Details_By_Id = '/sample/rate/month/findById',
  // echarts图表数据
  Get_Monthly_Sampling_Rate_Echarts_Data = '/sample/rate/month/sampleAreaStatistics',

  // ================= 区域检出率分析 =================
  // 列表数据
  Get_Regional_Detection_Rate_Page = '/detection/rate/area/detectionAreaData',
  // 下转列表页数据
  Get_Regional_Detection_Rate_Child_Page = '/detection/rate/area/detectionAreaNumDataPage',
  // 根据样本id获取详情
  Get_Regional_Detection_Details_By_Id = '/detection/rate/area/findById',
  // echarts图表数据
  Get_Regional_Detection_Rate_Echarts_Data = '/detection/rate/area/detectionAreaStatistics',

  // ================= 年龄检出率分析 =================
  // 列表数据
  Get_Age_Detection_Rate_Page = '/detection/rate/age/detectionAgeStatisticsPage',
  // 下转列表页数据
  Get_Age_Detection_Rate_Child_Page = '/detection/rate/age/detectionAgeNumDataPage',
  // 根据样本id获取详情
  Get_Age_Detection_Details_By_Id = '/detection/rate/age/findById',
  // echarts图表数据
  Get_Age_Detection_Rate_Echarts_Data = '/detection/rate/age/detectionAgeStatistics',

  // ================= 人群检出率分析 =================
  // 列表数据
  Get_Population_Detection_Rate_Page = '/detection/rate/people/detectionPeopleStatisticsPage',
  // 下转列表页数据
  Get_Population_Detection_Rate_Child_Page = '/detection/rate/people/detectionPeopleNumDataPage',
  // 根据样本id获取详情
  Get_Population_Detection_Details_By_Id = '/detection/rate/people/findById',
  // echarts图表数据
  Get_Population_Detection_Rate_Echarts_Data = '/detection/rate/people/detectionPeopleList',

  // ================= 季节检出率分析 =================
  // 列表数据
  Get_Season_Detection_Rate_Page = '/detection/rate/season/detectionSeasonStatisticsPage',
  // 下转列表页数据
  Get_Season_Detection_Rate_Child_Page = '/detection/rate/season/detectionSeasonNumDataPage',
  // 根据样本id获取详情
  Get_Season_Detection_Details_By_Id = '/detection/rate/season/findById',
  // echarts图表数据
  Get_Season_Detection_Rate_Echarts_Data = '/detection/rate/season/detectionSeasonList',

  // ================= 月度检出率分析 =================
  // 列表数据
  Get_Monthly_Detection_Rate_Page = '/detection/rate/month/detectionMonthStatisticsPage',
  // 下转列表页数据
  Get_Monthly_Detection_Rate_Child_Page = '/detection/rate/month/detectionMonthNumDataPage',
  // 根据样本id获取详情
  Get_Monthly_Detection_Details_By_Id = '/detection/rate/month/findById',
  // echarts图表数据
  Get_Monthly_Detection_Rate_Echarts_Data = '/detection/rate/month/detectionMonthStatistics',

  // ================= 病原阳性区域性分析 =================
  // 列表数据
  Get_Pathogen_Positive_Regionality_Page = '/data/etiology/sun/area/etiologyAreaData',
  // 下转列表页数据
  Get_Pathogen_Positive_Regionality_Child_Page = '/data/etiology/sun/area/etiologyAreaNumDataPage',
  // 根据样本id获取详情
  Get_Pathogen_Positive_Regionality_Details_By_Id = '/data/etiology/sun/area/findById',

  // ================= 病原阳性季节性分析 =================
  // 列表数据
  Get_Pathogen_Positive_Seasonality_Page = '/data/etiology/sun/season/etiologySeasonStatisticsPage',
  // 下转列表页数据
  Get_Pathogen_Positive_Seasonality_Child_Page = '/data/etiology/sun/season/etiologySeasonNumDataPage',
  // 根据样本id获取详情
  Get_Pathogen_Positive_Seasonality_Details_By_Id = '/data/etiology/positive/findById',
  // echarts图表数据
  Get_Pathogen_Positive_Seasonality_Echarts_Data = '/data/etiology/sun/season/etiologySeasonList',

  // 新接口
  // 采样分析率
  // 统计
  GET_SAMPLING_RATE_COUNT = '/data/etiology/sun/season/collectSampleCount',
  // 列表
  GET_SAMPLING_RATE_LIST = '/data/etiology/sun/season/collectSample',
  // 图表
  GET_SAMPLING_RATE_CHART = '/data/etiology/sun/season/collectSampleTable',
  // 检测率分析
  // 统计
  GET_DETECTION_RATE_COUNT = '/data/etiology/sun/season/detectSamplesCount',
  // 列表
  GET_DETECTION_RATE_LIST = '/data/etiology/sun/season/detectSamples',
  // 耐药率分析
  // 列表
  GET_DRUG_RESISTANCE_LIST = '/data/index/etiology/info/antimicrobialResistance',
  // 耐药谱
  // 列表
  GET_DRUG_RESISTANCE_PROFILE_LIST = '/data/index/etiology/info/resistanceSpectrum',
  // 病原阳性趋势分析
  // 列表
  GET_PATHOGEN_POSITIVE_TREND_LIST = '/data/index/etiology/info/positiveAnalysis',
  // 病原谱分析
  GET_PATHOGEN_SPECTRUM_ANALYSIS_DATA = '/data/index/etiology/info/pathogenSpectrum',
  // 复合感染分析
  GET_PATHOGEN_COMPLEX_INFECTION_LIST = '/data/etiology/sun/season/complexInfection',
  GET_PATHOGEN_COMPLEX_INFECTION_COUNT = '/data/etiology/sun/season/complexInfectiontotal',
}

/**
 * 区域采样率分析列表接口
 * @param params
 * @returns
 */
export const getRegionalSamplingRatePage = async (params: any) =>
  request.get(API.Get_Regional_Sampling_Rate_Page, { params });

/**
 * 获取下转页面列表数据
 * @param params
 * @returns
 */
export const getRegionalSamplingRateChildPage = async (params: any) =>
  request.get(API.Get_Regional_Sampling_Rate_Child_Page, { params });

/**
 * 根据样本id获取详情
 * @param params
 * @returns
 */
export const getDetailsById = async (params: any) =>
  request.get(API.Get_Details_By_Id, { params });

/**
 * 导出区域采样率分析数据
 * @param params
 * @returns
 */
export const exportRegionalSamplingRateResult = async (params: any) =>
  request.get(API.Export_Regional_Sampling_Rate_Result, { params });

/**
 * 获取区域采样率分析的图表数据
 * @param params
 * @returns
 */
export const getRegionalSamplingRateEchartsData = async (params: any) =>
  request.get(API.Get_Regional_Sampling_Rate_Echarts_Data, { params });

/**
 * 获取年龄采样率分析列表数据
 * @param params
 * @returns
 */
export const getAgeSamplingRatePage = async (params: any) =>
  request.get(API.Get_Age_Sampling_Rate_Page, { params });

/**
 * 获取年龄采样率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getAgeSamplingRateChildPage = async (params: any) =>
  request.get(API.Get_Age_Sampling_Rate_Child_Page, { params });

/**
 * 获取年龄采样率分析详情
 * @param params
 * @returns
 */
export const getAgeSamplingRateDetailsById = async (params: any) =>
  request.get(API.Get_Age_Sampling_Rate_Details_By_Id, { params });

/**
 * 获取年龄采样率分析echarts数据
 * @param params
 * @returns
 */
export const getAgeSamplingRateEchartsData = async (params: any) =>
  request.get(API.Get_Age_Sampling_Rate_Echarts_Data, { params });

/**
 * 人群采样率列表数据
 * @param params
 * @returns
 */
export const getPopulationSamplingRatePage = async (params: any) =>
  request.get(API.Get_Population_Sampling_Rate_Page, { params });

/**
 * 获取人群采样率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getPopulationSamplingRateChildPage = async (params: any) =>
  request.get(API.Get_Population_Sampling_Rate_Child_Page, { params });

/**
 * 获取人群采样率分析详情
 * @param params
 * @returns
 */
export const getPopulationSamplingRateDetailsById = async (params: any) =>
  request.get(API.Get_Population_Sampling_Rate_Details_By_Id, { params });

/**
 * 获取人群采样率分析echarts数据
 * @param params
 * @returns
 */
export const getPopulationSamplingRateEchartsData = async (params: any) =>
  request.get(API.Get_Population_Sampling_Rate_Echarts_Data, { params });

/**
 * 季节采样率列表数据
 * @param params
 * @returns
 */
export const getSeasonSamplingRatePage = async (params: any) =>
  request.get(API.Get_Season_Sampling_Rate_Page, { params });

/**
 * 获取季节采样率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getSeasonSamplingRateChildPage = async (params: any) =>
  request.get(API.Get_Season_Sampling_Rate_Child_Page, { params });

/**
 * 获取季节采样率分析详情
 * @param params
 * @returns
 */
export const getSeasonSamplingRateDetailsById = async (params: any) =>
  request.get(API.Get_Season_Sampling_Rate_Details_By_Id, { params });

/**
 * 获取季节采样率分析echarts数据
 * @param params
 * @returns
 */
export const getSeasonSamplingRateEchartsData = async (params: any) =>
  request.get(API.Get_Season_Sampling_Rate_Echarts_Data, { params });

/**
 * 月度采样率列表数据
 * @param params
 * @returns
 */
export const getMonthlySamplingRatePage = async (params: any) =>
  request.get(API.Get_Monthly_Sampling_Rate_Page, { params });

/**
 * 获取月度采样率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getMonthlySamplingRateChildPage = async (params: any) =>
  request.get(API.Get_Monthly_Sampling_Rate_Child_Page, { params });

/**
 * 获取月度采样率分析详情
 * @param params
 * @returns
 */
export const getMonthlySamplingRateDetailsById = async (params: any) =>
  request.get(API.Get_Monthly_Sampling_Rate_Details_By_Id, { params });

/**
 * 获取月度采样率分析echarts数据
 * @param params
 * @returns
 */
export const getMonthlySamplingRateEchartsData = async (params: any) =>
  request.get(API.Get_Monthly_Sampling_Rate_Echarts_Data, { params });

/**
 * 区域检出率分析列表数据
 * @param params
 * @returns
 */
export const getRegionalDetectionRatePage = async (params: any) =>
  request.get(API.Get_Regional_Detection_Rate_Page, { params });

/**
 * 获取区域检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getRegionalDetectionRateChildPage = async (params: any) =>
  request.get(API.Get_Regional_Detection_Rate_Child_Page, { params });

/**
 * 获取区域检出率分析详情
 * @param params
 * @returns
 */
export const getRegionalDetectionDetailsById = async (params: any) =>
  request.get(API.Get_Regional_Detection_Details_By_Id, { params });

/**
 * 获取区域检出率分析echarts数据
 * @param params
 * @returns
 */
export const getRegionalDetectionRateEchartsData = async (params: any) =>
  request.get(API.Get_Regional_Detection_Rate_Echarts_Data, { params });

/**
 * 年龄检出率分析列表数据
 * @param params
 * @returns
 */
export const getAgeDetectionRatePage = async (params: any) =>
  request.get(API.Get_Age_Detection_Rate_Page, { params });

/**
 * 获取年龄检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getAgeDetectionRateChildPage = async (params: any) =>
  request.get(API.Get_Age_Detection_Rate_Child_Page, { params });

/**
 * 获取年龄检出率分析详情
 * @param params
 * @returns
 */
export const getAgeDetectionDetailsById = async (params: any) =>
  request.get(API.Get_Age_Detection_Details_By_Id, { params });

/**
 * 获取年龄检出率分析echarts数据
 * @param params
 * @returns
 */
export const getAgeDetectionRateEchartsData = async (params: any) =>
  request.get(API.Get_Age_Detection_Rate_Echarts_Data, { params });

/**
 * 人群检出率分析列表数据
 * @param params
 * @returns
 */
export const getPopulationDetectionRatePage = async (params: any) =>
  request.get(API.Get_Population_Detection_Rate_Page, { params });

/**
 * 获取人群检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getPopulationDetectionRateChildPage = async (params: any) =>
  request.get(API.Get_Population_Detection_Rate_Child_Page, { params });

/**
 * 获取人群检出率分析详情
 * @param params
 * @returns
 */
export const getPopulationDetectionDetailsById = async (params: any) =>
  request.get(API.Get_Population_Detection_Details_By_Id, { params });

/**
 * 获取人群检出率分析echarts数据
 * @param params
 * @returns
 */
export const getPopulationDetectionRateEchartsData = async (params: any) =>
  request.get(API.Get_Population_Detection_Rate_Echarts_Data, { params });

/**
 * 季节检出率分析列表数据
 * @param params
 * @returns
 */
export const getSeasonDetectionRatePage = async (params: any) =>
  request.get(API.Get_Season_Detection_Rate_Page, { params });

/**
 * 获取人群检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getSeasonDetectionRateChildPage = async (params: any) =>
  request.get(API.Get_Season_Detection_Rate_Child_Page, { params });

/**
 * 获取人群检出率分析详情
 * @param params
 * @returns
 */
export const getSeasonDetectionDetailsById = async (params: any) =>
  request.get(API.Get_Season_Detection_Details_By_Id, { params });

/**
 * 获取人群检出率分析echarts数据
 * @param params
 * @returns
 */
export const getSeasonDetectionRateEchartsData = async (params: any) =>
  request.get(API.Get_Season_Detection_Rate_Echarts_Data, { params });

/**
 * 季节检出率分析列表数据
 * @param params
 * @returns
 */
export const getMonthlyDetectionRatePage = async (params: any) =>
  request.get(API.Get_Monthly_Detection_Rate_Page, { params });

/**
 * 获取人群检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getMonthlyDetectionRateChildPage = async (params: any) =>
  request.get(API.Get_Monthly_Detection_Rate_Child_Page, { params });

/**
 * 获取人群检出率分析详情
 * @param params
 * @returns
 */
export const getMonthlyDetectionDetailsById = async (params: any) =>
  request.get(API.Get_Monthly_Detection_Details_By_Id, { params });

/**
 * 获取人群检出率分析echarts数据
 * @param params
 * @returns
 */
export const getMonthlyDetectionRateEchartsData = async (params: any) =>
  request.get(API.Get_Monthly_Detection_Rate_Echarts_Data, { params });

/**
 * 病原阳性区域性分析列表数据
 * @param params
 * @returns
 */
export const getPathogenPositiveRegionalityPage = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Regionality_Page, { params });

/**
 * 获取病原阳性区域性分析下转列表页面数据
 * @param params
 * @returns
 */
export const getPathogenPositiveRegionalityChildPage = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Regionality_Child_Page, { params });

/**
 * 获取病原阳性区域性分析详情
 * @param params
 * @returns
 */
export const getPathogenPositiveRegionalityDetailsById = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Regionality_Details_By_Id, { params });

/**
 * 病原阳性季节性分析列表数据
 * @param params
 * @returns
 */
export const getPathogenPositiveSeasonalityPage = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Seasonality_Page, { params });

/**
 * 获取人群检出率分析下转列表页面数据
 * @param params
 * @returns
 */
export const getPathogenPositiveSeasonalityChildPage = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Seasonality_Child_Page, { params });

/**
 * 获取人群检出率分析详情
 * @param params
 * @returns
 */
export const getPathogenPositiveSeasonalityDetailsById = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Seasonality_Details_By_Id, { params });

/**
 * 获取人群检出率分析echarts数据
 * @param params
 * @returns
 */
export const getPathogenPositiveSeasonalityEchartsData = async (params: any) =>
  request.get(API.Get_Pathogen_Positive_Seasonality_Echarts_Data, { params });

// 新接口
/**
 * @description 采样率分析
 */
export const samplingRateCountApi = (params: Record<string, any>) =>
  request.get(API.GET_SAMPLING_RATE_COUNT, { params });
export const samplingRateListApi = (params: Record<string, any>) =>
  request.get(API.GET_SAMPLING_RATE_LIST, { params });
export const samplingRateChartApi = (params: Record<string, any>) =>
  request.get(API.GET_SAMPLING_RATE_CHART, { params });

/**
 * @description 检测率分析
 */
export const detectionRateCountApi = (params: Record<string, any>) =>
  request.get(API.GET_DETECTION_RATE_COUNT, { params });
export const detectionRateListApi = (params: Record<string, any>) =>
  request.get(API.GET_DETECTION_RATE_LIST, { params });

/**
 * @description 耐药性分析
 */
export const drugResistanceListApi = (params: Record<string, any>) =>
  request.get(API.GET_DRUG_RESISTANCE_LIST, { params });
export const drugResistanceProfileListApi = (params: Record<string, any>) =>
  request.get(API.GET_DRUG_RESISTANCE_PROFILE_LIST, { params });

/**
 * @description 病原阳性趋势分析
 */
export const pathOgenPositiveTrendListApi = (params: Record<string, any>) =>
  request.get(API.GET_PATHOGEN_POSITIVE_TREND_LIST, { params });

/**
 * @description 病原谱分析
 */
export const pathogenSpectrumAnalysisDataApi = (params: Record<string, any>) =>
  request.get(API.GET_PATHOGEN_SPECTRUM_ANALYSIS_DATA, { params });

/**
 * @description 复合感染分析
 */
export const pathogenComplexInfectionListApi = (params: Record<string, any>) =>
  request.get(API.GET_PATHOGEN_COMPLEX_INFECTION_LIST, { params });
export const pathogenComplexInfectionCountApi = (params: Record<string, any>) =>
  request.get(API.GET_PATHOGEN_COMPLEX_INFECTION_COUNT, { params });
