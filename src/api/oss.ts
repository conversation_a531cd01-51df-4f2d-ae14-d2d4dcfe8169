/*
 * @description 编号管理相关 API 请求
 */
import request from '@/utils/request';

enum API {
  QUERY_CONFIG_LIST = '/system/oss/config/list',
  CONFIG_DETAIL = '/system/oss/config/',
  // 根据ossId获取文件对象
  GET_OSS_OBJECT = '/system/oss/listByIds',
}

/**
 * @description 查询系统设置列表
 */
export const queryConfigList = async (params?: Record<string, any>) =>
  request.get(API.QUERY_CONFIG_LIST, { params });

/**
 * @description 查询系统设置详情
 */
export const queryConfigDetail = async (id: string | number) =>
  request.get(API.CONFIG_DETAIL + id);

/**
 * @description 修改系统设置
 */
export const editConfigDetail = async (data: any) =>
  request.put(API.CONFIG_DETAIL, { data });

/**
 * @description 新增系统设置
 */
export const addConfigDetail = async (data: any) =>
  request.post(API.CONFIG_DETAIL, { data });

/**
 * @description 删除系统设置
 */
export const DeleteConfig = async (data: any) =>
  request.delete(API.CONFIG_DETAIL + data);

/**
 * @description 设为默认
 */
export const setDefaultConfig = async (data: any) =>
  request.put(API.CONFIG_DETAIL + 'changeStatus', {
    data,
  });

/**
 * @description 根据ossId获取文件对象
 */
export const ossObjectApi = (ossId: string) =>
  request.get(API.GET_OSS_OBJECT + `/${ossId}`);
