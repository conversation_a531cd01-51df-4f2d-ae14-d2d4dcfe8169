import request from '@/utils/request';

export enum API {
  // 部门列表
  GET_DEPT_LIST = '/system/dept/list',
  // 部门列表分页
  GET_DEPT_LIST_BY_PAGE = '/system/dept/listByPage',
  // 上级部门
  GET_PARENT_DEPT = '/system/user/deptTree',
  // 上级部门(新)
  GET_PARENT_DEPT_NEW = '/system/user/deptTreeTaskType',
  // 部门详情
  GET_DEPT_DETAIL = '/system/dept',
  // 新增部门
  POST_ADD_DEPT = '/system/dept',
  // 编辑部门
  POST_EDIT_DEPT = '/system/dept',
  // 删除部门
  POST_DELETE_DEPT = '/system/dept',
  // 导出部门
  POST_EXPORT_DEPT = '/system/dept/export',
  // 导入
  POST_IMPORT_DEPT = '/system/dept/import',
  // 导入模板下载
  GET_IMPORT_TEMPLATE = '/system/dept/deptDownload',
}

export type TGetDeptListParam = {
  parentId?: number;
  status?: number;
  deptName?: string;
};

export interface ISaveDeptParam {
  parentId: string;
  deptName: string;
  leaderId: string;
  leader: string;
  orderNum: string;
  status: string;
  deptId?: number;
}

export interface IDeleteDeptParam {
  ids: string;
}

/**
 * @description 获取部门列表
 */
export const getDepartmentList = async (params: TGetDeptListParam) =>
  request.get(API.GET_DEPT_LIST, { params });

export type TDeptListByPageParams = TGetDeptListParam & {
  pageNum: number;
  pageSize: number
}
/**
 * @description 获取部门列表(分页)
 */
export const getDepartmentListByPage = async (params: TDeptListByPageParams) =>
  request.get(API.GET_DEPT_LIST_BY_PAGE, { params });

/**
 * @description 获取上级部门树
 */
export const getParentDept = async () => request.get(API.GET_PARENT_DEPT);

/**
 * @description 获取上级部门树(新)
 */
export const getParentDeptNew = async () => request.get(API.GET_PARENT_DEPT_NEW);

/**
 * @description 获取部门详情
 */
export const getDepartmentDetail = async (id: number) =>
  request.get(API.GET_DEPT_DETAIL + '/' + id);

/**
 * @description 新增部门
 */
export const postAddDepartment = async (data: ISaveDeptParam) =>
  request.post(API.POST_ADD_DEPT, { data });

/**
 * @description 编辑部门
 */
export const postEditDepartment = async (data: ISaveDeptParam) =>
  request.put(API.POST_EDIT_DEPT, { data });

/**
 * @description 删除部门
 */
export const postDeleteDepartment = async (data: IDeleteDeptParam) =>
  request.delete(API.POST_DELETE_DEPT + '/' + data.ids);

/**
 * @description 导出部门
 */
export const postExportDepartment = async (data: any) =>
  request.post(API.POST_EXPORT_DEPT, { data });

/**
 * @description 导入
 */
export const postImportDepartment = () =>
  import.meta.env.VITE_URL + API.POST_IMPORT_DEPT;

/**
 * @description 导入模板下载
 */
export const getImportTemplate = () =>
  import.meta.env.VITE_URL + API.GET_IMPORT_TEMPLATE
