import request from '@/utils/request';

export enum API {
  LIST1 = '/system/certificate/list',
  createBatchApi = '/system/certificate/createBatch',
  createOneApi = '/system/certificate/create',
  LIST2 = '/system/certificate/listOrg',
}

// 查询证书管理列表
export const getCertificateList = async (params: any) =>
  request.get(API.LIST1, { params });

// 批量发放

export const sendBatch = async (params: any) =>
  request.get(API.createBatchApi, { params });


// 单条数据操作
export const createOneApi = async (params: any) =>
request.get(API.createOneApi, { params });

// 查询证书列表
export const getlistOrg = async (params: any) =>
  request.get(API.LIST2, { params });
