import request from '@/utils/request';

enum API {
  // 病原列表
  GET_ETIOLOGY_LIST = '/index/etiology/info/listByItem',
  //新增病原
  POST_ETIOLOGY_ADD = '/index/etiology/info/insertData',
  //修改病原
  POST_ETIOLOGY_UPDATE = '/index/etiology/info/updateData',
  //删除病原
  POST_ETIOLOGY_DELETE = '/index/etiology/info/deleteDataList',
  //病原详情
  GET_ETIOLOGY_DETAIL = '/index/etiology/info/findById',
  //提交病原(把草稿改成提交)
  GET_ETIOLOGY_SUBMIT = '/index/etiology/info/updateByDraftId',

  // 传染病列表
  GET_INFECT_LIST = '/index/infect/etiology/listByItem',
  //新增传染病
  POST_INFECT_ADD = '/index/infect/etiology/insertData',
  //修改传染病
  POST_INFECT_UPDATE = '/index/infect/etiology/updateData',
  //删除传染病
  POST_INFECT_DELETE = '/index/infect/etiology/deleteDataList',
  //传染病详情
  GET_INFECT_DETAIL = '/index/infect/etiology/findById',
  //提交传染病(把草稿改成提交)
  GET_INFECT_SUBMIT = '/index/infect/etiology/updateByDraftId',

  // 病原体名称列表
  GET_PATHOGEN_NAME_LIST = '/index/pathogen/listPathogenSelection',
  // 根据传染病分类ID获取当前分类下的所有传染病数据
  Get_Infect_List_By_ClassId = '/index/infect/etiology/listInfectSelection',

  // 监测项目库
  // 获取监测项目库列表页数据
  Get_Monitor_Item_List = '/etiology/project/listByItem',
  // 监测项目库搜索项目名称接口
  Search_By_Project_Name = '/etiology/project/listProjectSelector',

  GET_MONITOR_LIST = '/etiology/project/listProjectSelector',
  // 通过项目ID获取已选择的病原列表
  Get_Monitor_Item_Selected_List = '/etiology/project/detail/listByItem',
  // 新增监测项目
  Add_Monitor_Item = '/etiology/project/insert',
  // 编辑监测项目
  Edit_Monitor_Item = '/etiology/project/edit',
  // 删除监测项目
  Delete_Monitor_Item = '/etiology/project/delete',
  // 监测项目库明细
  Get_Monitor_Item_Detail = '/etiology/project/details',

  // 药品维护模块
  // 获取药品清单列表数据
  Get_Medicine_List = '/drug/listByItem',
}

export type TEtiologyListParams = {
  pageSize: number;
  pageNum: number;
  infectClass?: string;
  etiologyName?: string;
  etiologyClass?: string;
  spreadApproach?: string;
  etiologyBank?: number; //病原库  0-全库   1-贵州库
  isDraft?: string; // 1:草稿 0:已上报
};
/**
 * @description 病原列表
 */
export const etiologyListApi = (params: TEtiologyListParams) =>
  request.get(API.GET_ETIOLOGY_LIST, { params });

/**
 * @description 新增病原信息
 *
 */
export type TEtiologyAddParams = {
  id: string;
  etiologyName: string;
  etiologyClass: string;
  spreadApproach: string;
  symptomDesc: string;
  preventMeasure: string;
  cureMethod: string;
  fashionCondition: string;
  researchProgress: string;
  etiologyBank: number; //病原库  0-全库   1-贵州库
  isDraft: number; // 1:草稿 0:已上报
};
export const etiologyAddApi = (data: TEtiologyAddParams) =>
  request.post(API.POST_ETIOLOGY_ADD, { data });

/**
 * @description 修改病原信息
 *
 */
export const etiologyUpdateApi = (data: TEtiologyAddParams) =>
  request.post(API.POST_ETIOLOGY_UPDATE, { data });

/**
 * @description 删除病原信息
 */
export const etiologyDeleteApi = (data: string[]) =>
  request.post(API.POST_ETIOLOGY_DELETE, { data });

/**
 * @description 根据ID查询病原详情
 */
export const etiologyDetailApi = (params: { id: string }) =>
  request.get(API.GET_ETIOLOGY_DETAIL, { params });

/**
 * @description 提交病原
 */
export const etiologySubmitApi = (params: { id: string }) =>
  request.get(API.GET_ETIOLOGY_SUBMIT, { params });

export type TInfectListParams = {
  pageSize: number;
  pageNum: number;
  infectClass: number;
  infectCode: string;
  infectName: string;
  nature: string;
  isDraft?: string; // 1:草稿 0:已上报
};
/**
 * @description 传染病列表
 */
export const infectListApi = (params: TInfectListParams) =>
  request.get(API.GET_INFECT_LIST, { params });
/**
 * @description 新增传染病信息
 *
 */
export type TInfectAddParams = {
  id: string;
  infectClass: number;
  infectName: string;
  nature: string;
  lurkCycle: string;
  reportTime: string;
  infectionSource: string;
  spreadApproach: string;
  fashionFeature: string;
  etiology: string;
  diagnoseWay: string;
  isDraft: number;
};
export const infectAddApi = (data: TInfectAddParams) =>
  request.post(API.POST_INFECT_ADD, { data });

/**
 * @description 修改传染病信息
 *
 */
export const infectUpdateApi = (data: TInfectAddParams) =>
  request.post(API.POST_INFECT_UPDATE, { data });

/**
 * @description 删除传染病信息
 */
export const infectDeleteApi = (data: string[]) =>
  request.post(API.POST_INFECT_DELETE, { data });

/**
 * @description 根据ID查询传染病详情
 */
export const infectDetailApi = (params: { id: string }) =>
  request.get(API.GET_INFECT_DETAIL, { params });

/**
 * @description 提交传染病
 */
export const infectSubmitApi = (params: { id: string }) =>
  request.get(API.GET_INFECT_SUBMIT, { params });

/**
 *  @description 获取病原体名称列表
 */
export const getPathogenNameList = () =>
  request.get(API.GET_PATHOGEN_NAME_LIST);

/**
 *  @description 根据传染病分类ID获取该分类下的所有传染病列表数据
 */
export const getInfectListByClassId = (params?: Record<string, any>) =>
  request.get(API.Get_Infect_List_By_ClassId, { params });

/**
 *  @description 监测项目库列表页数据
 */
export const getMonitorItemList = (params?: Record<string, any>) =>
  request.get(API.Get_Monitor_Item_List, { params });

export const monitorListApi = (params?: Record<string, any>) =>
  request.get(API.GET_MONITOR_LIST, { params });

/**
 *  @description 监测项目库列表页数据
 */
export const getMonitorItemSelectedList = (params?: Record<string, any>) =>
  request.get(API.Get_Monitor_Item_Selected_List, { params });

/**
 *  @description 新增监测项目
 */
export const postAddNewMonitorItem = (data: Record<string, any>) =>
  request.post(API.Add_Monitor_Item, { data });

/**
 *  @description 编辑监测项目
 */
export const editMonitorItem = async (data: Record<string, any>) =>
  request.put(API.Edit_Monitor_Item, { data });

/**
 * @description 删除监测项目
 */
export const deleteMonitorItem = async (id: string | number) =>
  request.delete(`${API.Delete_Monitor_Item}?id=${id}`);

/**
 *  @description 获取监测项目库详情
 */
export const getMonitorItemDetail = (id: string) =>
  request.get(`${API.Get_Monitor_Item_Detail}/${id}`);

/**
 *  @description 获取药品清单数据
 */
export const getMedicineList = (params?: Record<string, any>) =>
  request.get(API.Get_Medicine_List, { params });
