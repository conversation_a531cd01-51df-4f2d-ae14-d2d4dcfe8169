import request from '@/utils/request';

export enum API {
  // 数据字典
  GET_DICT = '/system/dict/data/type',
  GET_DICT_AUTH = '/system/dict/data/typeAuth',
  DICT = '/system/dict/type',
  DICT_CONFIG = '/system/dict/data',
}

/**
 * @description 数据字典
 */
export const getDict = async (params: string) =>
  request.get(API.GET_DICT + '/' + params);
/**
 * @description 数据字典-带权限
 */
export const getDictAuth = async (params: string) =>
  request.get(API.GET_DICT_AUTH + '/' + params);

/**
 * @description 数据字典列表
 */
export const getDictList = async (params: any) =>
  request.get(API.DICT + '/list', {
    params
  });
/**
 * @description  
 */
export const addDictType = async (data: any) =>
  request.post(API.DICT, {
    data
  });
/**
 * @description  
 */
export const editDictType = async (data: any) =>
  request.put(API.DICT, {
    data
  });
/**
 * @description  
 */
export const queryDictType = async (id: any) =>
  request.get(API.DICT + '/' + id);
/**
 * @description  
 */
export const deleteDictType = async (id: any) =>
  request.delete(API.DICT + '/' + id);



// 字典详情


/**
 * @description 数据字典列表
 */
export const getDictDetailList = async (params: any) =>
  request.get(API.DICT_CONFIG + '/list', {
    params
  });
/**
* @description  
*/


/**
 * @description  
 */
export const addDictDetailType = async (data: any) =>
  request.post(API.DICT_CONFIG, {
    data
  });
/**
 * @description  
 */
export const editDictDetailType = async (data: any) =>
  request.put(API.DICT_CONFIG, {
    data
  });
/**
 * @description  
 */
export const queryDictDetailType = async (id: any) =>
  request.get(API.DICT_CONFIG + '/' + id);
/**
 * @description  
 */
export const deleteDictDetailType = async (id: any) =>
  request.delete(API.DICT_CONFIG + '/' + id);