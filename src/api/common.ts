import request from '@/utils/request';

enum API {
  GET_CITY_LIST = '/common/cities',
  GET_AREA_LIST = '/common/districts',
  GET_FILES_LIST = '/system/oss/listByBusinessId',
}

/**
 * @description 获取城市
 */
export const cityListApi = () => request.get(API.GET_CITY_LIST);

/**
 * @description 获取城市下的区县
 */
export const areaListApi = (params: { cityId: number }) =>
  request.get(API.GET_AREA_LIST, { params });

export type TFileGroupRes = {
  id: number;
  groupId: number;
  fileName: string;
  originalName: string;
  fileAddr: string;
  fileSize: string;
  ossId: string
};
/**
 * @description 获取文件
 */
export const fileGroupApi = (params: { businessId: string }) =>
  request.get(API.GET_FILES_LIST, { params });
