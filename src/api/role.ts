import request from '@/utils/request';

export enum API {
  // 角色列表
  GET_ROLE_LIST = '/system/role/list',
  // 新增角色
  POST_ADD_ROLE = '/system/role',
  // 删除角色
  POST_DELETE_ROLE = '/system/role',
  // 授权用户
  POST_AUTH_USER = '/system/role/authUser/selectAll',
  // 取消授权
  POST_CANCEL_AUTH = '/system/role/authUser/cancelAll',
  // 角色下的授权用户
  GET_ROLE_AUTH_USER = '/system/role/authUser/allocatedList',
  // 角色列表（不分页）
  GET_ROLE_SELECT_LIST = '/system/user/',
  // 角色详情
  GET_ROLE_DETAIL = '/system/role',
  // 编辑角色
  POST_EDIT_ROLE = '/system/role',
  // 查询角色绑定的菜单
  GET_EDIT_ROLE_MENU = '/system/menu/roleMenuTreeselect',
  // 查询角色数据权限数据
  GET_EDIT_ROLE_DEPT_TREE = '/system/role/deptTree',
  // 修改角色数据权限数据
  UPDATE_ROLE_DATASCOPE = '/system/role/dataScope',
}

/**
 * @interface 获取角色管理列表参数
 */
export interface IGetRoleListParam {
  pageSize?: number;
  pageNum?: number;
}
/**
 * @description 获取角色列表
 */
export const queryRoleList = async (params: IGetRoleListParam) =>
  request.get(API.GET_ROLE_LIST, { params });

/**
 * @interface 新增/编辑角色参数
 */
export interface ISaveRoleParam {
  roleName: string;
  status: number;
  menuIds: number[];
  roleId?: number;
  roleKey: string;
}
/**
 * @description 新增角色
 */
export const queryAddRole = async (data: ISaveRoleParam) =>
  request.post(API.POST_ADD_ROLE, { data });

/**
 * @description 删除角色
 */
export const queryDeleteRole = async (roleIds: string) =>
  request.delete(API.POST_DELETE_ROLE + '/' + roleIds);

/**
 * @interface 授权用户参数
 */
export interface IRoleAuthUserParam {
  roleId: number;
  userIds: number[];
}

/**
 * @description 授权用户
 */
export const queryAuthUser = async (data: IRoleAuthUserParam) =>
  request.post(API.POST_AUTH_USER, { data });

/**
 * @description 取消授权
 */
export const queryCancelAuth = async (data: IRoleAuthUserParam) =>
  request.post(API.POST_CANCEL_AUTH, { data });

/**
 * @description 获取角色下的授权用户
 */
export const queryRoleAuthUser = async (params: { roleId: number }) =>
  request.get(API.GET_ROLE_AUTH_USER, { params });

/**
 * @description 获取角色列表（不分页）
 */
export const queryRoleSelectList = async () =>
  request.get(API.GET_ROLE_SELECT_LIST);

/**
 * @description 获取角色详情
 */
export const queryRoleDetail = async (id: number) =>
  request.get(API.GET_ROLE_DETAIL + '/' + id);

/**
 * @description 编辑角色
 */
export const queryEditRole = async (data: ISaveRoleParam) =>
  request.put(API.POST_EDIT_ROLE, { data });
/**
 * @description 编辑角色
 */
export const queryEditRoleMenu = async (id: string) =>
  request.get(API.GET_EDIT_ROLE_MENU + '/' + id);
/**
 * @description 查询角色数据权限数据
 */
export const queryEditRoleDeptData = async (id: string) =>
  request.get(API.GET_EDIT_ROLE_DEPT_TREE + '/' + id);
/**
 * @description 修改角色数据权限数据
 */
export const updateRoleDataScope = async (data: any) =>
  request.put(API.UPDATE_ROLE_DATASCOPE, { data }); 