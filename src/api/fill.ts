
import request from '@/utils/request';

enum API {
  FILL_TASK = "/qa/fillingTasks",
  // 考核列表
  ADD_FILL = "/inspectionFilling/insert",
  // 任务列表
  GET_QUERY_FILL_TASK_LIST = "/qa/fillingTasks/new",
  // 任务列表-退回
  GET_BACK_FILL_TASK = "//qa/fillingTasks/back",
  // 保存评判信息
  PUT_SAVE_JUDGE = "/qa/fillingTasks/draft",
  // 退回当前评判数据
  PUT_RETURN_TASK = "/qa/fillingTasks/returnTask",
  // 检验填报-详情
  GET_INSPECT_WRITE_DETAIL = "/qa/fillingTasks/detail",
  // 检验填报-编辑
  POST_INSPECT_WRITE_EDIT = "/qa/fillingTasks/edit",
  // 批量评判
  POST_BATCH_JUDGE = "/qa/fillingTasks/batchEdit",
  // 解除任务结束时间限制
  PUT_UNLOCK = "/qa/fillingTasks/changeIsTime",
}

//查询质量考核填报任务列表 
export interface IGetQATaskListParams {
  pageSize?: number;
  pageNum?: number;
  [keys: string]: any;
}

// 考核列表
export const getFillList = async (params: IGetQATaskListParams) =>
  request.get(API.FILL_TASK, {
    params
  });
// 任务列表
export const queryFillTaskList = async (params: Record<string, any>) =>
  request.get(API.GET_QUERY_FILL_TASK_LIST, { params });
// 任务列表-退回
export const BackFillTask = async (params: Record<string, any>) =>
  request.get(API.GET_BACK_FILL_TASK, { params });

// 确认或者提交质量考核填报任务 
export const receptFill = async (data: any) =>
  request.put(API.FILL_TASK + '/' + data.id + '/status/' + data.status);

// 获取检验填报任务详细信息 
export const getFillDetail = async (id: any) =>
  request.get(API.GET_INSPECT_WRITE_DETAIL + '/' + id);

// 新增检验填报
export const saveFill = async (data: any) =>
  request.post(API.POST_INSPECT_WRITE_EDIT + "/" + data.operationType, { data });

// 评分
export const judge = async (data: any) =>
  request.put(API.FILL_TASK + '/' + data.id, { data });

// 保存评判信息
export const judgeSave = async (data: any) =>
  request.put(API.PUT_SAVE_JUDGE + '/' + data.id, { data });

// 退回当前评判数据
export const returnTask = async (id: any) =>
  request.put(API.PUT_RETURN_TASK + '/' + id);

// 批量评判
export const batchJudge = async (data: any) =>
  request.post(API.POST_BATCH_JUDGE, { data });

// 解除任务结束时间限制
export const unlockFillTask = async (id: any) =>
  request.put(API.PUT_UNLOCK + '/' + id);

