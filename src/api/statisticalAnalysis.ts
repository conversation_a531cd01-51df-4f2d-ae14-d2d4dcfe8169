import request from '@/utils/request';

enum API {
  GET_TASK_STATISTIC = '/data/taskAnalyze/overview',
  GET_TASK_STATISTIC_LIST_OLD = '/data/taskAnalyze/sentinels',
  GET_TASK_STATISTIC_LIST = '/data/taskAnalyze/taskProgress',
  GET_STRAIN_REVIEW_STATISTIC_OLD = '/data/strainAnalyze/overview',
  GET_STRAIN_REVIEW_STATISTIC = '/data/strainAnalyze/overviewStrain',
  GET_PATHOGEN_LIST_OLD = '/data/strainAnalyze/strains',
  GET_PATHOGEN_LIST = '/data/strainAnalyze/strainReview',
  GET_AREA_DISTRIBUTION_LIST = '/data/spreadAnalyze/areaInfo',
  GET_AGE_DISTRIBUTION_LIST = '/data/spreadAnalyze/ageInfo',
  GET_POPULATION_DISTRIBUTION_LIST = '/data/spreadAnalyze/genderInfo',

  GET_DISTRIBUTION_LIST = '/data/spreadAnalyze/monitoring',

  // 初检阳性列表
  POST_INIT_POSITIVE_LIST = '/data/spreadAnalyze/drillPre',
  // 复核阳性列表
  POST_DOUBLE_POSITIVE_LIST = '/data/spreadAnalyze/drillCheck',
}
type TCommonPage = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
};

/**
 * @description 监测计划统计
 */
export const taskStatisticApi = () => request.get(API.GET_TASK_STATISTIC);

export type TTaskStatisticInfoParams = TCommonPage & {
  planId?: number;
};
/**
 * @description 监测计划统计 - 计划部署哨点列表
 */
export const taskStatisticInfoApi = (params: TTaskStatisticInfoParams) =>
  request.get(API.GET_TASK_STATISTIC_LIST, { params });

/**
 * @description 菌株复核统计
 */
export const strainReviewStatisticApi = () =>
  request.get(API.GET_STRAIN_REVIEW_STATISTIC);

export type TPathogenListParams = TCommonPage & { strainId?: number };
/**
 * @description 菌株复核统计 - 病原明细列表
 */
export const pathogenListApi = (params: TPathogenListParams) =>
  request.get(API.GET_PATHOGEN_LIST, { params });


/**
 * @description 监测地区分布列表
 */
export const distributionListApi = (params: Record<string,  any>) =>
  request.get(API.GET_DISTRIBUTION_LIST, { params });

export type TAreaDistributionListParams = TCommonPage & {
  year?: number;
  cityId?: number;
  ageId?: number;
};
/**
 * @description 监测地区分布列表
 */
export const areaDistributionListApi = (params: TAreaDistributionListParams) =>
  request.get(API.GET_AREA_DISTRIBUTION_LIST, { params });

export type TAgeDistributionListParams = TAreaDistributionListParams;
/**
 * @description 监测年龄分布列表
 */
export const ageDistributionListApi = (params: TAgeDistributionListParams) =>
  request.get(API.GET_AGE_DISTRIBUTION_LIST, { params });

export type TPopulationDistributionListParams = TAreaDistributionListParams;
/**
 * @description 监测人群分布列表
 */
export const populationDistributionListApi = (
  params: TPopulationDistributionListParams
) => request.get(API.GET_POPULATION_DISTRIBUTION_LIST, { params });
/**
 * @description 初检阳性列表
 */
export const initPositiveListApi = (
  params: TCommonPage,
  data: Record<string, any>
) => request.post(API.POST_INIT_POSITIVE_LIST, { params, data });

/**
 * @description 复核阳性列表
 */
export const doublePositiveListApi = (
  params: TCommonPage,
  data: Record<string, any>
) => request.post(API.POST_DOUBLE_POSITIVE_LIST, { params, data });
