import request from '@/utils/request';

enum API {
  QA_TASK = '/qa/tasks',
  GET_QUERY_ORG_LIST = '/system/dept/listTwo',
  PUT_SAVE_ORG_TASK = '/qa/tasks',
  // 获取任务数量
  GET_TASK_COUNT = '/qa/tasks/count',
  // 更新结束日期
  POST_UPDATE_END_DATE = '/qa/tasks/updateEndDate',
  // 变更任务
  POST_CHANGE_TASK_STATUS = '/qa/tasks/updateTaskStatus',
  // 变更任务编辑中查询任务明细
  GET_TASK_DETAIL_EDIT = '/qa/tasks/getTaskDetails',
  // 变更中任务编辑中删除单位
  POST_DELETE_ORG_TASK = '/qa/tasks/delTaskOrg',
  // 变更中任务编辑中更换被考核单位
  POST_CHANGE_ORG_TASK = '/qa/tasks/changeTaskOrg',
  // 更新样本编号
  POST_UPDATE_SAMPLE_NUMBER = '/qa/tasks/changeSampleCode',
  // 新增被考核单位
  POST_ADD_ORG_TASK = '/qa/tasks/addTaskOrg',
  // 保存任务基本信息(主要用于变更中类型任务的保存)
  POST_SAVE_TASK_BASIC_INFO = '/qa/tasks/changeQaTask',
  // 详情页获取任务嵌套数据
  GET_TASK_DETAIL_NESTED_DATA = '/qa/tasks/getTaskSampleDetailNew',

  // =============== 模版 ===============
  //调整模板-模板查询
  GET_ADJUST_TEMPLATE_VIEW = '/qa/tasks/getTaskTemplate',
  // 调整模板-编辑提交
  POST_ADJUST_TEMPLATE = '/qa/tasks/editTaskTemplate',
  // 随机生成样本编号
  GET_RANDOMLY_SAMPLE_NUM = '/qa/tasks/getSampleCode',
  // 随机生成样本编号-V2
  GET_RANDOMLY_SAMPLE_NUM_V2 = '/qa/tasks/randomSampleCode',
  // 为随机考核类型生成样本编号
  POST_RANDOM_SAMPLE_CODE = '/qa/tasks/randomForRandomTask',
  // 快递信息维护-列表
  GET_EXPRESS_LIST = '/qa/tasks/getSampleOrgRela',
  // 快递信息维护-编辑
  POST_GET_EXPRESS_EDIT = '/qa/tasks/setSampleOrgRela',
  // 重新评分
  POST_RE_SCORE = '/qa/tasks/reJudgeTaskResult',
  // 考核任务详情-样品明细
  GET_TASK_DETAIL_SAMPLE = '/qa/tasks/getTaskSampleDetail',
  // 考核任务-随机考核-导出快递数据
  POST_RANDOM_EXPORT_EXPRESS = '/system/taskDetailsOrg/exportSJSampleNo',
  // 考核任务-随机考核-导入快递数据
  POST_RANDOM_IMPORT_EXPRESS = '/system/taskDetailsOrg/importRandomData',
  // 考核任务-机构考核草稿-导出样本
  POST_ORGIN_EXPORT_EXPRESS = '/system/taskDetailsOrg/exportOrgTask',
  // 考核任务-盲样考核-导出快递数据
  POST_BLINDLY_EXPORT_EXPRESS = '/system/taskDetailsOrg/exportBlindData',
  // 考核任务-盲样考核-导入快递数据
  POST_BLINDLY_IMPORT_EXPRESS = '/system/taskDetailsOrg/importBlindData',
  // //考核任务-详情-导出样本
  GET_TASK_DETAIL_EXPORT_EXPRESS = '/system/taskDetailsOrg/exportOrgDataDetail',
  // 维护规则-历史规则
  GET_QUERY_STANDARD_LIST = '/qa/fillingTasks/formulaList',
  // 维护规则-规则验证
  POST_CHECK_STANDAR_ITEM = '/formula/generate',

  // 检验填报-详情
  GET_INSPECT_WRITE_DETAIL = '/qa/fillingTasks/detail/',
  // 检验填报-编辑
  POST_INSPECT_WRITE_EDIT = '/qa/fillingTasks/edit/',

  // ----- 统计分析 -----
  GET_ORG_STATISTIC = '/data/analyze/lab',
  GET_ORG_STATISTIC_EXPORT = '/data/analyze/labExp/export',
  GET_TASK_TAPE_STATISTIC = '/data/analyze/taskType',
  GET_TASK_TAPE_STATISTIC_EXPORT = '/analyze/typeExp/export',
  GET_TASK_STATISTIC = '/data/analyze/task',
  GET_TASK_STATISTIC_EXPORT = '/data/analyze/taskExp/export',

  // -------- 批量附件下载 -------------
  // 下载任务列表
  GET_DOWNLOAD_TASK_LIST = '/task/file/list',
  // 新增下载任务
  POST_ADD_DOWNLOAD_TASK = '/task/file/add',
}

//查询质量考核任务列表
export interface IGetQATaskListParams {
  pageSize?: number;
  pageNum?: number;
}
export const getQATaskList = async (params: IGetQATaskListParams) =>
  request.get(API.QA_TASK, {
    params,
  });

// 获取任务数量
export const getTaskCount = async (params: Record<string, any>) => request.get(API.GET_TASK_COUNT, { params });

// 更新结束日期
export const updateEndDate = async (data: Record<string, any>) =>
  request.post(API.POST_UPDATE_END_DATE, { data });

// 变更任务
export const changeTaskStatus = async (data: Record<string, any>) =>
  request.post(API.POST_CHANGE_TASK_STATUS, { data });

// 变更任务编辑中查询任务明细
export const getTaskDetailEdit = async (params: Record<string, any>) =>
  request.get(API.GET_TASK_DETAIL_EDIT, { params });

// 变更中任务编辑中删除单位
export const deleteOrgTask = async (data: Record<string, any>) =>
  request.post(API.POST_DELETE_ORG_TASK, { data });

// 变更中任务编辑中更换被考核单位
export const changeOrgTask = async (data: Record<string, any>) =>
  request.post(API.POST_CHANGE_ORG_TASK, { data });

// 更新样本编号
export const updateSampleNumber = async (data: Record<string, any>) =>
  request.post(API.POST_UPDATE_SAMPLE_NUMBER, { data });

// 新增被考核单位
export const addOrgTask = async (data: Record<string, any>) =>
  request.post(API.POST_ADD_ORG_TASK, { data });

// 保存任务基本信息(主要用于变更中类型任务的保存)
export const saveTaskBasicInfo = async (data: Record<string, any>) =>
  request.post(API.POST_SAVE_TASK_BASIC_INFO, { data });

// 详情页获取任务嵌套数据
export const getTaskDetailNestedData = async (params: Record<string, any>) =>
  request.get(API.GET_TASK_DETAIL_NESTED_DATA, { params });

//新增质量考核任务草稿
export const addQATask = async (data: Record<string, any>) =>
  request.post(API.QA_TASK, {
    data,
  });
//修改质量考核任务
export const updateQATask = async (data: Record<string, any>) =>
  request.put(API.QA_TASK + '/' + data.id, {
    data,
  });

//获取质量考核任务详细信息
export const getQATask = async (id: string) =>
  request.get(API.QA_TASK + '/' + id);

//详情页-下发质量考核任务
export const sendQATask = async (data: Record<string, any>) =>
  request.put(API.QA_TASK + '/' + data.id + '/info/status/1', { data });
//快递维护-下发质量考核任务
export const sendExpressTask = async (data: Record<string, any>) =>
  request.put(API.QA_TASK + '/' + data.id + '/list/status/1', { data });

//分页查询质量考核任务任务明细   pageQuery
export const getQATaskItemList = async (params: Record<string, any>) => {
  const id = params.id;
  delete params.id;
  return request.get(API.QA_TASK + '/' + id + '/details', {
    params,
  });
};

//新增一条任务明细
export const addQATaskItem = async (data: Record<string, any>) =>
  request.post(API.QA_TASK + '/' + data.taskId + '/details', {
    data,
  });

//批量删除任务明细
export const delQATaskItem = async (data: Record<string, any>) =>
  request.delete(API.QA_TASK + '/' + data.id + '/details/' + data.ids, {
    data,
  });
//批量删除任务
export const delQATask = async (id: string) =>
  request.delete(API.QA_TASK + '/' + id);

//导入质量考核任务明细
export const importDetail = (id: any) => {
  return import.meta.env.VITE_URL + API.QA_TASK + '/' + id + '/details/excel';
};

//被考核机构列表
export const queryOrgList = async (data?: Record<string, any>) =>
  request.get(API.GET_QUERY_ORG_LIST);

//机构考核-保存
export const saveOrgTask = async (data: Record<string, any>) =>
  request.put(API.PUT_SAVE_ORG_TASK + `/${data.id}`, { data });

// 重新评分
export const reScoreTask = async (data: Record<string, any>) =>
  request.post(API.POST_RE_SCORE, { data });

//调整模板-模板查询
export const adjustTemplateView = async (params: Record<string, any>) =>
  request.get(API.GET_ADJUST_TEMPLATE_VIEW, { params });

//调整模板-编辑提交
export const adjustTemplate = async (data: Record<string, any>) =>
  request.post(API.POST_ADJUST_TEMPLATE, { data });

//随机生成样本编号
export const randomlySampleNum = async (params: Record<string, any>) =>
  request.get(API.GET_RANDOMLY_SAMPLE_NUM, { params });

//随机生成样本编号-V2
export const randomlySampleNumV2 = async (data: Record<string, any>) =>
  request.post(API.GET_RANDOMLY_SAMPLE_NUM_V2, { data });

//自增编号-V2

export const randomSampleCode = async (data: Record<string, any>) =>
  request.post("/qa/tasks/randomSampleCode", { data });


// 为随机考核类型生成样本编号
export const randomForRandomTask = async (data: Record<string, any>) =>
  request.post(API.POST_RANDOM_SAMPLE_CODE, { data });

//快递信息维护-列表
export const queryExpressList = async (params: Record<string, any>) =>
  request.get(API.GET_EXPRESS_LIST, { params });

//快递信息维护-编辑
export const queryExpressInfoEdit = async (data: Record<string, any>) =>
  request.post(API.POST_GET_EXPRESS_EDIT, { data });

//考核任务详情-样品明细
export const taskDetailSample = async (params: Record<string, any>) =>
  request.get(API.GET_TASK_DETAIL_SAMPLE, { params });

//考核任务-随机考核草稿-导出样本
export const randomExporExpress = async (params: Record<string, any>) =>
  request.post(API.POST_RANDOM_EXPORT_EXPRESS, { params });

//考核任务-机构考核草稿-导出样本
export const orginExporExpress = async (params: Record<string, any>) =>
  request.post(API.POST_ORGIN_EXPORT_EXPRESS, { params });

//考核任务-详情-导出样本
export const taskDetailExporExpress = async (params: Record<string, any>) =>
  request.post(API.GET_TASK_DETAIL_EXPORT_EXPRESS, { params });

//考核任务-随机考核-导入快递数据
export const randomImportExpress = async () =>
  import.meta.env.VITE_URL + API.POST_RANDOM_IMPORT_EXPRESS;

//考核任务-盲样考核-导出快递数据
export const blindlyExporExpress = async (params: Record<string, any>) =>
  request.post(API.POST_BLINDLY_EXPORT_EXPRESS, { params });

//考核任务-盲样考核-导入快递数据
export const blindlyImportExpress = async () =>
  import.meta.env.VITE_URL + API.POST_BLINDLY_IMPORT_EXPRESS;

//维护规则-历史规则
export const queryStandardList = async (params: Record<string, any>) =>
  request.get(API.GET_QUERY_STANDARD_LIST, { params });

//维护规则-规则验证
export const checkStandardItem = async (data: Record<string, any>) =>
  request.post(API.POST_CHECK_STANDAR_ITEM, { data });

//检验填报-详情
export const inspectWriteDetail = async (params: Record<string, any>) =>
  request.get(API.GET_INSPECT_WRITE_DETAIL + params.id);
//检验填报-编辑
export const inspectWriteEdit = async (data: Record<string, any>) =>
  request.post(API.POST_INSPECT_WRITE_EDIT + data.operationType, { data });

// ----- 统计分析 -----
/**
 * @description 被考核机构统计分析
 */
export type TOrgStatisticParams = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
  labName?: string;
  taskYear?: string;
  cityId?: number;
};
export const orgStatisticApi = (params: TOrgStatisticParams) =>
  request.get(API.GET_ORG_STATISTIC, { params });

/**
 * @description 考核类型统计
 */
export type TTaskTypeStatisticParams = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
  taskYear?: string;
  cityId?: number;
  taskType?: number;
};
export const taskTypeStatisticApi = (params: TTaskTypeStatisticParams) =>
  request.get(API.GET_TASK_TAPE_STATISTIC, { params });

/**
 * @description 考核任务统计
 */
export type TTaskStatisticParams = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
  taskName?: string;
};
export const taskStatisticApi = (params: TTaskStatisticParams) =>
  request.get(API.GET_TASK_STATISTIC, { params });

/**
 * 下载任务列表
 */
export const getDownloadTaskList = (params?: Record<string, any>) =>
  request.get(API.GET_DOWNLOAD_TASK_LIST, { params });

/**
 * 新增下载任务
 */
export const addDownloadTask = (taskId: string) =>
  request.get(`${API.POST_ADD_DOWNLOAD_TASK}/${taskId}`);
