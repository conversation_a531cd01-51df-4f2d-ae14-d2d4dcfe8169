
import request from '@/utils/request';

enum API {
  // 查看详情
  GET_DETAIL = "/qa/fillingTasks/detail/",
  // 编辑
  EDIT = "/qa/fillingTasks/edit/",
  // 评分
  JUDGE = "/qa/fillingTasks/editScore",
}

// 查看详情
export const getDetail = async (params: any) =>
  request.get(API.GET_DETAIL + params);

// 编辑
export const editDetail = async (type: any, data: any) =>
  request.post(API.EDIT + type, { data });

// 评分
export const judge = async (params: any) =>
  request.get(API.JUDGE, { params });