import request from '@/utils/request';

enum API {
  // ========== 监测哨点 ===========
  // 查询所有市县区域数据
  Get_City_Area_List = '/common/districts',
  // 查询病原信息列表数据
  Get_Pathogen_List = '/index/etiology/info/findEtiologyName',
  // 获取监测哨点列表数据
  Get_Pathogen_Point_List = '/monitor/sentinel/listByItem',
  // 提交新建哨点数据
  Post_Pathogen_Point = '/monitor/sentinel/insertData',
  // 根据ID获取哨点详情信息数据
  Get_Pathogen_Point_Detail = '/monitor/sentinel/findById',
  // 修改哨点数据
  Update_Pathogen_Point = '/monitor/sentinel/updateData',

  // =========== 监测计划 =============
  // 查询监测计划列表数据
  GET_Pathogen_Plan_List = '/monitor/plan/listByItem',
  // 查询监测计划状态为执行中、已结束的列表数据，
  GET_Pathogen_Plan_List_With_PendingFinished = '/monitor/plan/listByItemToEndData',
  // 新增监测计划
  Post_Pathogen_Plan = '/monitor/plan/insertData',
  // 启动计划
  Start_Pathogen_Plan = '/monitor/plan/updateIsStart',
  // 获取计划详情
  Get_Pathogen_Plan_Detail = '/monitor/plan/findById',
  // 修改更新计划
  Update_Pathogen_Plan = '/monitor/plan/updateData',
  // 删除计划
  Delete_Pathogen_Plan = '/monitor/plan/deletePlan',
  // 获取不同状态的计划数量
  Get_Pathogen_Plan_Count = '/monitor/plan/countStatusNum',
  // 获取监测项目列表数据
  Get_Monitor_Project_List = '/monitor/plan/listEtiologyProject',
  // 根据监测项目id获取该项目下的病原列表
  Get_Pathogen_List_By_ProjectId = '/etiology/project/listByProject',

  // =========== 监测任务 ==============
  // 查询监测任务列表数据
  Get_Pathogen_Task_List = '/monitor/task/listByItem',
  // 根据计划 id 查询任务列表数据
  Get_Pathogen_Task_List_By_PlanId = '/monitor/task/findTaskByPlanId',
  // 接收任务操作
  Receive_Pathogen_Task = '/monitor/task/taskAccept',
  // 查询任务详情
  Get_Pathogen_Task_Detail = '/monitor/task/findById',
  // 获取任务待接收、已接收的数量
  Get_Pathogen_Task_Count = '/monitor/task/findAcceptCount',
  // 通过任务 id ，查询当前任务对应的监测病原信息
  Get_Pathogen_Info_By_TaskId = '/monitor/task/findByIdToEtiologyInfo',

  // ============ 样本检测信息 ==========
  // 根据计划 id 查询任务详情
  Get_Sample_List_By_PlanId = '/sample/detection/basic/info/listByItem',
  // 新增数据
  Post_New_Sample_Data = '/sample/detection/basic/info/saveData',
  // 编辑数据
  // Update_Sample_Data = '/sample/detection/basic/info/updateFirstData',
  Update_Sample_Data = '/sample/detection/basic/info/updateData',
  // 根据样本信息 id 获取详情数据
  Get_Sample_By_Id = '/sample/detection/basic/info/findById', // 样本检测信息获取详情
  Get_Sample_Detail_By_Id = '/sample/detection/basic/info/findByResultId', // 样本检测结果获取详情
  // 查询所有的采样人员年龄段
  Get_Sample_Age_List = '/monitor/age/findAll',
  // 删除样本检测
  Delete_Sample_Data = '/sample/detection/basic/info/deletedById',
  // 统计草稿、已提交数量
  Get_Sample_Count = '/sample/detection/basic/info/findDraftCount',
  // 根据监测任务查询病原信息
  Get_Pathogen_Info_By_Task = '/sample/detection/basic/info/listProjectEtiology',
  // 保存提交待检测状态的样本
  Submit_Sample_Data = '/sample/detection/basic/info/submitData',

  // =========== 检测结果巡查 ==============
  Get_Test_Result_ReCheck = '/sample/detection/basic/info/detectionResultCheck',

  // =========== 复核任务 ==============
  // 获取复核任务列表数据
  Get_Strain_Verification_Task_List = '/sample/detection/recheck/listRecheckByItem',
  // 复核填报获取详情
  Get_Strain_Verification_Task_Detail = '/sample/detection/recheck/details',
  // 获取登陆用户信息
  Get_User_Info = '/sample/detection/recheck/getFieldInfo',
  // 保存&提交 复核数据
  Update_Strain_Verification_Task = '/sample/detection/recheck/submitResult',
  // 统计待复核/已复核记录数量
  Get_Strain_Verification_Task_Count = '/sample/detection/recheck/countReCheck',
  // 批量保存符合
  Batch_Save_Strain_Verification_Task = '/sample/detection/recheck/submitResultBatch',

  // =========== 复核检测结果巡查 ===========
  // 获取列表数据
  Get_ReCheck_Result_List = '/sample/detection/basic/info/recheckDetectionResultCheck',

  // =========== 耐药性检测 ==============
  // 获取耐药性列表数据
  Get_Drug_Resistance_Testing_List = '/resistance/drug/listByItem',
  // 统计耐药性待检测、已检测数量
  Get_Drug_Resistance_Testing_Count = '/resistance/drug/countStatusNum',
  // 获取耐药性详情
  Get_Drug_Resistance_Testing_Detail = '/resistance/drug/details',
  // 保存&提交耐药性检查
  Save_Drug_Resistance_Testing = '/resistance/drug/edit',

  // =========== 增量监测 ==============
  //
}

//
export interface IetAssessmentTypeListParams {
  pageSize?: number;
  pageNum?: number;
}

/**
 * 获取城市信息列表数据
 * @param params
 * @returns
 */
export const getAllCityAreaList = async (params: Record<string, any>) =>
  request.get(API.Get_City_Area_List, { params });

export const getPathogenList = async (params?: Record<string, any>) =>
  request.get(API.Get_Pathogen_List, { params });

/**
 * 获取病原信息列表数据
 * @param params
 * @returns
 */
export const getPathogenPointList = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.Get_Pathogen_Point_List, {
    params,
  });

/**
 * 新增哨点
 * @param data
 * @returns
 */
export const submitNewPathogenPoint = async (data: Record<string, any>) =>
  request.post(API.Post_Pathogen_Point, { data });

/**
 * 根据ID获取哨点详情信息数据
 * @param params
 * @returns
 */
export const getPathogenDetailsById = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Point_Detail, { params });

/**
 * 更新哨点数据
 * @param data
 * @returns
 */
export const updatePathogenPoint = async (data: Record<string, any>) =>
  request.post(API.Update_Pathogen_Point, { data });

/**
 * 获取监测计划列表数据
 * @param params
 * @returns
 */
export const getPathogenPlanList = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.GET_Pathogen_Plan_List, {
    params,
  });

// GET_Pathogen_Plan_List_With_PendingFinished
/**
 * 获取监测计划为执行中、已完成的列表数据
 * @param params
 * @returns
 */
export const getPathogenPlanListWithPedingFinished = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.GET_Pathogen_Plan_List_With_PendingFinished, {
    params,
  });

/**
 * 新增监测计划
 * @param data
 * @returns
 */
export const submitNewPathogenPlan = async (data: Record<string, any>) =>
  request.post(API.Post_Pathogen_Plan, { data });

/**
 * 启动计划
 * @param params
 * @returns
 */
export const startPathogenPlan = async (params: Record<string, any>) =>
  request.get(API.Start_Pathogen_Plan, { params });

/**
 * 获取计划详情
 * @param params
 * @returns
 */
export const getPathogenPlanDetail = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Plan_Detail, { params });

/**
 * 修改更新计划
 * @param data
 * @returns
 */
export const updatePathogenPlan = async (data: Record<string, any>) =>
  request.post(API.Update_Pathogen_Plan, { data });

/**
 * 删除计划
 * @param params
 * @returns
 */
export const deletePathogenPlan = async (params: Record<string, any>) =>
  request.get(API.Delete_Pathogen_Plan, { params });

/**
 * 获取不同状态的计划数量
 * @param params
 * @returns
 */
export const getPathogenPlanCount = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Plan_Count, { params });

/**
 * 获取监测项目列表数据
 * @param params
 * @returns
 */
export const getMonitorProjectList = async (params: Record<string, any>) =>
  request.get(API.Get_Monitor_Project_List, { params });

// Get_Pathogen_List_By_ProjectId
/**
 * 获取监测项目列表数据
 * @param params
 * @returns
 */
export const getPathogenListByProjectId = async (projectId: string) =>
  request.get(`${API.Get_Pathogen_List_By_ProjectId}/${projectId}`);

/**
 * 获取监测任务列表数据
 * @param params
 * @returns
 */
export const getPathogenTaskList = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.Get_Pathogen_Task_List, {
    params,
  });

/**
 * 根据计划 id 查询任务列表数据
 * @param params
 * @returns
 */
export const getPathogenTaskListByPlanId = async (
  params: Record<string, any>
) =>
  request.get(API.Get_Pathogen_Task_List_By_PlanId, {
    params,
  });

/**
 * 接收任务
 * @param params
 * @returns
 */
export const receivePathogenTask = async (params: Record<string, any>) =>
  request.get(API.Receive_Pathogen_Task, {
    params,
  });

/**
 * 查询任务详情
 * @param params
 * @returns
 */
export const getPathogenTaskDetail = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Task_Detail, {
    params,
  });

/**
 * 获取待接收任务、已接收任务数量
 * @param params
 * @returns
 */
export const getPathogenTaskCount = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Task_Count, {
    params,
  });

/**
 * 根据任务id获取对应任务的病原信息
 * @param params
 * @returns
 */
export const getPathogenInfoByTaskId = async (params: {
  taskId: string | number;
}) =>
  request.get(API.Get_Pathogen_Info_By_TaskId, {
    params,
  });

// Submit_Sample_Data
/**
 * 保存提交待检测的样本数据
 * @param data
 * @returns
 */
export const submitDecteSampleData = async (data: Record<string, any>) =>
  request.post(API.Submit_Sample_Data, {
    data,
  });

/**
 * 根据任务 id 查询该任务下所有样本列表数据
 * @param params
 * @returns
 */
export const getSampleListByPlanId = async (params: Record<string, any>) =>
  request.get(API.Get_Sample_List_By_PlanId, {
    params,
  });

/**
 * 新增样本检测信息
 * @param data
 * @returns
 */
export const postNewSampleData = async (data: Record<string, any>) =>
  request.post(API.Post_New_Sample_Data, {
    data,
  });

/**
 * 更新样本检测信息
 * @param data
 * @returns
 */
export const updateSampleData = async (data: Record<string, any>) =>
  request.post(API.Update_Sample_Data, {
    data,
  });

/**
 * 根据样本检测 id 获取详情,适用于样本检测信息模块
 * @param params
 * @returns
 */
export const getSampleById = async (params: Record<string, any>) =>
  request.get(API.Get_Sample_By_Id, {
    params,
  });

/**
 * 根据样本检测 id 获取详情， 适用于样本检测结果模块
 * @param params
 * @returns
 */
export const getSampleDetailById = async (params: Record<string, any>) =>
  request.get(API.Get_Sample_Detail_By_Id, {
    params,
  });

/**
 * 获取年龄段
 * @param params
 * @returns
 */
export const getSampleAgeList = async (params: Record<string, any>) =>
  request.get(API.Get_Sample_Age_List, {
    params,
  });

/**
 * 通过 id 删除样本检测信息
 * @param params
 * @returns
 */
export const deleteSampleData = async (params: Record<string, any>) =>
  request.get(API.Delete_Sample_Data, {
    params,
  });

/**
 * 统计样本检测记录草稿、已提交数量
 * @param params
 * @returns
 */
export const getSampleStatusCount = async (params: Record<string, any>) =>
  request.get(API.Get_Sample_Count, {
    params,
  });

/**
 * 根据监测任务查询病原信息
 * @param params
 * @returns
 */
export const getPathogenInfoByTask = async (params: Record<string, any>) =>
  request.get(API.Get_Pathogen_Info_By_Task, {
    params,
  });

/**
 * 获取检测结果巡查列表数据
 * @param params
 * @returns
 */
export const getTestResultReCheck = async (params: Record<string, any>) =>
  request.get(API.Get_Test_Result_ReCheck, {
    params,
  });

/**
 * 菌株复核任务列表数据查询
 * @param params
 * @returns
 */
export const getStrainVerificationTaskList = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.Get_Strain_Verification_Task_List, {
    params,
  });

/**
 * 复核填报详情
 * @param params
 * @returns
 */
export const getStrainVerificationTaskDetail = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.Get_Strain_Verification_Task_Detail, {
    params,
  });

// Get_User_Info
/**
 * 复核填报详情
 * @param params
 * @returns
 */
export const getUserInfoAtReCheck = async (params?: Record<string, any>) =>
  request.get(API.Get_User_Info, {
    params,
  });

/**
 * 修改复核任务数据
 * @param data
 * @returns
 */
export const updateStrainVerificationTask = async (data: Record<string, any>) =>
  request.post(API.Update_Strain_Verification_Task, { data });

/**
 * 统计待复核,已复核数量
 * @param params
 * @returns
 */
export const getStrainVerificationTaskCount = async (
  params: IetAssessmentTypeListParams & Record<string, any>
) =>
  request.get(API.Get_Strain_Verification_Task_Count, {
    params,
  });

/**
 * 批量保存符合的条目
 * @param data
 * @returns
 */
export const batchSaveStrainVerificationTask = async (
  data: Record<string, any>[]
) => request.post(API.Batch_Save_Strain_Verification_Task, { data });

/**
 * 获取复核结果列表数据
 * @param params
 * @returns
 */
export const getReCheckResultList = async (params: Record<string, any>) =>
  request.get(API.Get_ReCheck_Result_List, {
    params,
  });

/**
 * 获取耐药性检测列表数据
 * @param params
 * @returns
 */
export const getDrugResistanceTestingList = async (
  params: Record<string, any>
) =>
  request.get(API.Get_Drug_Resistance_Testing_List, {
    params,
  });

/**
 * 统计耐药性待检测、已检测状态数量
 * @param params
 * @returns
 */
export const getDrugResistanceTestingCount = async (
  params: Record<string, any>
) =>
  request.get(API.Get_Drug_Resistance_Testing_Count, {
    params,
  });

/**
 * 获取耐药性记录详情
 * @param params
 * @returns
 */
export const getDrugResistanceTestingDetail = async (
  params: Record<string, any>
) =>
  request.get(API.Get_Drug_Resistance_Testing_Detail, {
    params,
  });

// Save_Drug_Resistance_Testing
/**
 * 获取耐药性记录详情
 * @param params
 * @returns
 */
export const saveDrugResistanceTesting = async (data: Record<string, any>) =>
  request.put(API.Save_Drug_Resistance_Testing, { data });
