import request from '@/utils/request';

export enum API {
  RECEPT = '/qa/receipt-forms',
  // 根据样本编号查询任务
  GET_TASK_BY_SAMPLE_NUM = '/qa/fillingTasks/getFillingTaskBySampleCode',
  // 确认任务
  CONFIRM_TASK = '/qa/fillingTasks/correctTask',
  // 现场评审-已处理-查询组员评价列表
  GET_EVALUATION_LIST = '/grade/review/evaluationList',
  // 现场评审-已处理-提交评价
  POST_SUBMIT_EVALUATION = '/grade/review/evaluation',
}

// 查询标本接收登记单列表
export const getReceptList = async (params: any) =>
  request.get(API.RECEPT, { params });

// 接收标本登记单
export const recept = async (data: any) =>
  request.put(API.RECEPT + '/' + data.id, { data: data.list });

// 获取标本接收登记单详细信息
export const getReceptDetail = async (id: any) =>
  request.get(API.RECEPT + '/' + id);

// 上传附件
export const attachment = async (data: any) =>
  request.put(API.RECEPT + '/attachment', { data });

/**
 * 根据样本编号查询任务
 */
export const getTaskBySampleNum = async (params: { sampleCode: string }) =>
  request.get(API.GET_TASK_BY_SAMPLE_NUM, { params });

/**
 * 确认任务
 */
export const handleConfirmTask = async (data: Record<string, any>) =>
  request.post(API.CONFIRM_TASK, { data });

/**
 * 现场评审-已处理-查询组员评价列表
 */
export const queryEvaluationList = async (id: string | number) =>
  request.get(API.GET_EVALUATION_LIST + '/' + id,);

/**
 * 现场评审-已处理-提交评价
 */
export const submitEvaluationList = async (data: Record<string, any>) =>
  request.post(API.POST_SUBMIT_EVALUATION, { data });
