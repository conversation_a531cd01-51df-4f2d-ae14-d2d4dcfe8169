import request from '@/utils/request';

enum API {
  // 获取艾滋病列表数据
  Get_Acquired_Immunodeficiency_List = '/data/topicAnalyze/getBaseInfo',
  // 艾滋病监测分布
  Get_Acquired_Immunodeficiency_Distribution = '/data/topicAnalyze/countBacterialStrainByArea',
  // 时间分布数据
  Get_Time_Distribution_List = '/data/topicAnalyze/timeGroup',
  // 地区分布信息
  Get_Area_Distribution_List = '/data/topicAnalyze/areaGroup',
  // 人群分布数据
  Get_Population_Distribution_List = '/data/topicAnalyze/peopleGroup',
  // 传播途径数据
  Get_Transmission_Route_List = '/data/topicAnalyze/wayGroup',

  // 高校传染病
  // 获取高校名称列表
  Get_School_List = '/data/topicAnalyze/getUnit',
  // 获取高校概述信息
  Get_School_Overview = '/data/topicAnalyze/getUnitBaseInfo',
  // 病例分布数据
  Get_School_Case_Distribution_List = '/data/topicAnalyze/getUnitDiseaseGroup',
  // 发展趋势数据
  Get_School_Development_Trend_List = '/data/topicAnalyze/getUnitTrend',
  // 高校人群分布
  Get_School_Population_Distribution_List = '/data/topicAnalyze/peopleGroupUnit',
  // 高校传播途径
  Get_School_Spread_Way_List = '/data/topicAnalyze/wayGroupUnit',
}

/**
 *  @description 获取艾滋病列表数据
 */
export const getAcquiredImmunodeficiencyList = (params?: Record<string, any>) =>
  request.get(API.Get_Acquired_Immunodeficiency_List, { params });

/**
 *  @description 获取艾滋病监测分布数据
 */
export const getAcquiredImmunodeficiencyDistribution = (
  params?: Record<string, any>
) => request.get(API.Get_Acquired_Immunodeficiency_Distribution, { params });

/**
 *  @description 获取时间分布数据
 */
export const getTimeDistributionList = (params?: Record<string, any>) =>
  request.get(API.Get_Time_Distribution_List, { params });

/**
 *  @description 获取地区分布数据
 */
export const getAreaDistributionList = (params?: Record<string, any>) =>
  request.get(API.Get_Area_Distribution_List, { params });

/**
 *  @description 获取人群分布数据
 */
export const getPopulationDistributionList = (params?: Record<string, any>) =>
  request.get(API.Get_Population_Distribution_List, { params });

/**
 *  @description 获取传播途径数据
 */
export const getTransmissionRouteList = (params?: Record<string, any>) =>
  request.get(API.Get_Transmission_Route_List, { params });

/**
 *  @description 获取学校名称列表
 */
export const getSchoolList = (params?: Record<string, any>) =>
  request.get(API.Get_School_List, { params });

/**
 *  @description 获取高校概述信息
 */
export const getSchoolOverview = (params?: Record<string, any>) =>
  request.get(API.Get_School_Overview, { params });

/**
 *  @description 获取高校病例分布
 */
export const getSchoolCaseDistributionList = (params?: Record<string, any>) =>
  request.get(API.Get_School_Case_Distribution_List, { params });

/**
 *  @description 获取发展趋势分布
 */
export const getSchoolDevelopmentTrendList = (params?: Record<string, any>) =>
  request.get(API.Get_School_Development_Trend_List, { params });

/**
 *  @description 获取发展趋势分布
 */
export const getSchoolPopulationDistributionList = (
  params?: Record<string, any>
) => request.get(API.Get_School_Population_Distribution_List, { params });

/**
 *  @description 获取高校趋势分布
 */
export const getSchoolSpreadWayList = (params?: Record<string, any>) =>
  request.get(API.Get_School_Spread_Way_List, { params });


/**
 *  @description 专题分析报告列表

 */
export const listReport = (params?: Record<string, any>) =>
  request.get("/data/topicAnalyze/listReport", { params });



/**
 *  @description 专题分析报告列表

 */
export const getYear = () =>
  request.get("/monitor/plan/getYear");




/**
 *  @description 专题分析报告列表

 */
export const createReport = (data:any) =>
  request.post("/data/topicAnalyze/createReport", {
    data
  });
