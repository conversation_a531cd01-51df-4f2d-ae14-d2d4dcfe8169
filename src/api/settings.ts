/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2022-11-15 09:25:34
 * @LastEditTime: 2024-12-03 13:38:48
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /xr-qc-jk-web/src/api/settings.ts
 * @Description:
 */

/*
 * @description 编号管理相关 API 请求
 */
import request from '@/utils/request';

enum API {
  // 数据字典 - 查询列表
  QUERY_DICT_LIST = '/system/dict/type/list',
  // 数据字典 = 新增字典
  ADD_DICT = '/system/dict/type/add',
  // 数据字典 = 修改字典
  EDIT_DICT = '/system/dict/type/edit',
  // 数据字典 = 删除字典
  DELETE_DICT = '/system/dict/type/remove',
  // 数据字典 - 获取字典值列表数据
  QUERY_DICT_VALUE_LIST = '/system/dict/data/list',
  // 数据字典 = 新增字典值
  ADD_DICT_VALUE = '/system/dict/data/add',
  // 数据字典 = 修改编辑字典值
  EDIT_DICT_VALUE = '/system/dict/data/edit',
  // 数据字典 = 删除字典值
  DELETE_DICT_VALUE = '/system/dict/data/remove',
  // 数据字典 - 批量新增
  BATCH_ADD_DICT_VALUE = '/system/dict/data/batchAdd',
  // 数据字典 - 获取字典
  GET_DICT_VAL = '/system/dict/type/gain',

  // 获取年份数据
  Get_Year_List = '/monitor/plan/getYear',
}

/**
 * @description 字典列表数据查询
 */
export const queryDictList = async (params?: Record<string, any>) =>
  request.get(API.QUERY_DICT_LIST, { params });

/**
 * @description 新增字典
 */
export const addDict = async (data: Record<string, any>) =>
  request.post(API.ADD_DICT, { data });

/**
 * @description 修改字典
 */
export const editDict = async (data: Record<string, any>) =>
  request.post(API.EDIT_DICT, { data });

/**
 * @description 删除字典
 */
export const deleteDict = async (data: Record<string, any>) =>
  request.post(API.DELETE_DICT, { data });

/**
 * @description 字典值列表数据查询
 */
export const queryDictValueList = async (params?: Record<string, any>) =>
  request.get(API.QUERY_DICT_VALUE_LIST, { params });

/**
 * @description 字典值新增
 */
export const addDictValue = async (data: Record<string, any>) =>
  request.post(API.ADD_DICT_VALUE, { data });

/**
 * @description 字典值修改
 */
export const editDictValue = async (data: Record<string, any>) =>
  request.post(API.EDIT_DICT_VALUE, { data });

/**
 * @description 字典值删除
 */
export const deleteDictValue = async (data: Record<string, any>) =>
  request.post(API.DELETE_DICT_VALUE, { data });

/**
 * @description 字典值批量新增
 */
export const batchAddDictValue = async (data: Record<string, any>) =>
  request.post(API.BATCH_ADD_DICT_VALUE, { data });

/**
 * @type 获取字典值参数
 */
export type TGetDictValParam = {
  code: string;
};
/**
 * @description 获取字典值
 */
export const getDictValue = async (params: TGetDictValParam) =>
  request.get(API.GET_DICT_VAL, { params });

/**
 * @description 获取年份数据
 */
export const getYearList = async () => request.get(API.Get_Year_List);
