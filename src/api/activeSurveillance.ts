import request from '@/utils/request';

enum API {
  // ----- 主动监测 -----
  // 样本监测信息/监测结果巡查列表
  GET_SAMPLE_TEST_INFO_LIST = '/initiative/monitor/detectionResultCheck',
  // 详情
  GET_SAMPLE_TEST_INFO_DETAIL = '/initiative/monitor/findById',
  // 新增
  POST_ADD_SAMPLE_TEST_INFO = '/initiative/monitor/saveData',
  // 修改
  POST_UPDATE_SAMPLE_TEST_INFO = '/initiative/monitor/updateFirstData',
  // 删除
  GET_DELETE_SAMPLE_TEST_INFO = '/initiative/monitor/deletedById',
  // 采样年龄段枚举
  GET_YEAR_ENUM = '/monitor/age/findAll',
  // ----- 主动监测统计分析 -----
  // 分布统计
  GET_DISTRIBUTION_LIST = '/data/spreadAnalyzeActive/activeMonitoring',
  // 区域分布统计列表
  GET_AREA_DISTRIBUTION_LIST = '/spreadAnalyzeActive/areaInfo',
  // 年龄分布统计列表
  GET_AGE_DISTRIBUTION_LIST = '/spreadAnalyzeActive/ageInfo',
  // 人群分布统计列表
  GET_POPULATION_DISTRIBUTION_LIST = '/spreadAnalyzeActive/genderInfo',
  // 初检阳性列表
  POST_INIT_POSITIVE_LIST = '/spreadAnalyzeActive/drillPre',
  // 复核阳性列表
  POST_DOUBLE_POSITIVE_LIST = '/spreadAnalyzeActive/drillCheck',

  // -----  主动（增量）检测 ---------
  // 获取不同状态样本数量
  Get_Sample_Status_Count = '/increment/sample/countNum',
  // 样本检测信息
  Get_Sample_Detection_Info = '/increment/sample/listByItem',
  // 新增样本检测信息（保存草稿）
  Post_Sample_Detection_Info = '/increment/sample/saveData',
  // 修改样本检测信息（填报信息）
  Put_Sample_Detection_Info = '/increment/sample/updateData',
  // 删除样本信息
  Delete_Sample_Detection_Info = '/increment/sample/deletedById',
  // 获取详情
  Get_Sample_Detection_Info_Detail = '/increment/sample/details',
}

type TCommonPage = {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: string;
};

/**
 * @description 样本监测信息/监测结果巡查列表
 */
export const sampleTestInfoListApi = (
  params: TCommonPage & {
    isAccordMonitor: number; // 是否主动监测（0-非主动监测，1-主动监测）
    isDraft: string; // 是否是草稿（0-非草稿    1-草稿）
    sampleName?: string;
    sampleNo?: string;
    planYear?: string;
    planName?: string;
  }
) => request.get(API.GET_SAMPLE_TEST_INFO_LIST, { params });

/**
 * @description 详情
 */
export const sampleTestInfoDetailApi = (params: { id: string }) =>
  request.get(API.GET_SAMPLE_TEST_INFO_DETAIL, { params });

/**
 * @description 新增
 */
export const addSampleTestInfoApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_TEST_INFO, { data });

/**
 * @description 删除
 */
export const deleteSampleTestInfoApi = (id: string) =>
  request.get(API.GET_DELETE_SAMPLE_TEST_INFO + `?id=${id}`);

/**
 * @description 修改
 */
export const updateSampleTestInfoApi = (data: Record<string, any>) =>
  request.post(API.POST_UPDATE_SAMPLE_TEST_INFO, { data });

/**
 * @description 采样年龄段枚举
 */
export const yearEnumApi = () => request.get(API.GET_YEAR_ENUM);

// ----- 统计分析 -----

/**
 * @description 分布统计列表
 */
export const distributionListApi = (params: Record<string, any>) =>
  request.get(API.GET_DISTRIBUTION_LIST, { params });

/**
 * @description 区域分布统计列表
 */
export const areaDistributionListApi = (
  params: TCommonPage & {
    sampleYear?: string;
    cityId?: string;
  }
) => request.get(API.GET_AREA_DISTRIBUTION_LIST, { params });

/**
 * @description 年龄分布统计列表
 */
export const ageDistributionListApi = (
  params: TCommonPage & {
    sampleYear?: string;
    ageId?: string;
  }
) => request.get(API.GET_AGE_DISTRIBUTION_LIST, { params });

/**
 * @description 人群分布统计列表
 */
export const populationDistributionListApi = (
  params: TCommonPage & {
    sampleYear?: string;
  }
) => request.get(API.GET_POPULATION_DISTRIBUTION_LIST, { params });

/**
 * @description 初检阳性列表
 */
export const initPositiveListApi = (
  params: TCommonPage,
  data: Record<string, any>
) => request.post(API.POST_INIT_POSITIVE_LIST, { params, data });

/**
 * @description 复核阳性列表
 */
export const doublePositiveListApi = (
  params: TCommonPage,
  data: Record<string, any>
) => request.post(API.POST_DOUBLE_POSITIVE_LIST, { params, data });

/**
 * @description 获取不同状态样本数量
 */
export const getActiveSampleStatusCount = (params: Record<string, any>) =>
  request.get(API.Get_Sample_Status_Count, { params });

/**
 * @description 样本检测信息
 */
export const getSampleDetectionInfo = (params: Record<string, any>) =>
  request.get(API.Get_Sample_Detection_Info, { params });

/**
 * @description 新增样本检测信息
 */
export const postSampleDetectionInfo = (data: Record<string, any>) =>
  request.post(API.Post_Sample_Detection_Info, { data });

//
/**
 * @description 保存样本检测信息（填报阶段）
 */
export const putSampleDetectionInfo = (data: Record<string, any>) =>
  request.post(API.Put_Sample_Detection_Info, { data });

/**
 * @description 删除样本检测信息
 */
export const deleteSampleDetectionInfo = (params: Record<string, any>) =>
  request.get(API.Delete_Sample_Detection_Info, { params });

/**
 * @description 获取样本检测信息详情
 */
export const getSampleDetectionInfoDetail = (params: Record<string, any>) =>
  request.get(API.Get_Sample_Detection_Info_Detail, { params });
