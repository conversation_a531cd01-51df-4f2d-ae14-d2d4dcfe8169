
import request from '@/utils/request';

enum API {
  ASS = "/assessment-type/",
}

//  
export interface IetAssessmentTypeListParams {
  pageSize?: number;
  pageNum?: number;
}
export const getAssessmentTypeList = async (params: IetAssessmentTypeListParams) =>
  request.get(API.ASS + 'list', {
    params
  });
//   
export const deleteAssessmentType = async (id: any) =>
  request.delete(API.ASS + id);
//   
export const getAssessmentTypeDetail = async (id: any) =>
  request.get(API.ASS + id);

//  
export const addAssessmentType = async (data: any) =>
  request.post(API.ASS, { data });

//  
export const updateAssessmentType = async (data: any) =>
  request.put(API.ASS + data.dictCode, { data });