import request from '@/utils/request';

enum API {
  // 病原阳性
  // 列表
  GET_POSITIVE_WARNING_LIST = '/data/etiology/positive',
  // 详情
  GET_POSITIVE_WARNING_DETAIL = '/data/etiology/positive/getByWarnId',
  GET_ETIOLOGY_NAME = '/data/etiology/positive/selectEtiology',
  GET_WARNING_DETAIL = '/data/etiology/positive/findById',
  //急性病原列表
  GET_ACUTE_WARNING_LIST = '/data/etiology/positive/warnTypeList',
  //区域态势预警列表
  GET_AREA_WARNING_LIST = '/data/etiology/positive/area',
  //区域态势预警详情
  GET_AREA_WARNING_DETAIL = '/data/etiology/positive/warnDetail',

  //年龄态势预警列表
  GET_AGE_WARNING_LIST = '/data/etiology/positive/age',
  //人群态势预警列表
  GET_PEOPLE_WARNING_LIST = '/data/etiology/positive/people',
}

export type TPositiveWarningListParams = {
  pageSize: number;
  pageNum: number;
  startDate?: string;
  endDate?: string;
  etiologyName: string;
  strainCode: string;
  // null 病原阳性列表 1-急性 2-高致病性 3-新发 4-突发 5-输入性
  infectiousNature: string
};
/**
 * @description 病原阳性列表
 */
export const positiveWarningListApi = (params: TPositiveWarningListParams) =>
  request.get(API.GET_POSITIVE_WARNING_LIST, { params });
export const positiveWarningDetailApi = (id: string) =>
  request.get(API.GET_POSITIVE_WARNING_DETAIL + `?id=${id}`);

/**
 * @description 获取病原名称
 */
export const etiologyNameApi = () => request.get(API.GET_ETIOLOGY_NAME);

/**
 * @description 获取病原详情
 */
export const warningDetailApi = (params: { id: string }) =>
  request.get(API.GET_WARNING_DETAIL, { params });

export type TAcuteWarningListParams = {
  pageSize: number;
  pageNum: number;
  startDate?: string;
  endDate?: string;
  etiologyId: string;
  warnType: string; //1急性病原,2高致病性病原,3新发性病原,4突发病原,5输入性病原
};
/**
 * @description 急性/高致病性/新发性/突发/输入性病原列表
 */
export const acuteWarningListApi = (params: TAcuteWarningListParams) =>
  request.get(API.GET_ACUTE_WARNING_LIST, { params });

export type TAreaWarningListParams = {
  pageSize: number;
  pageNum: number;
  startDate?: string;
  endDate?: string;
  etiologyId: string;
};
/**
 * @description 区域态势预警列表
 */
export const areaWarningListApi = (params: TAreaWarningListParams) =>
  request.get(API.GET_AREA_WARNING_LIST, { params });

/**
 * @description 区域预警/年龄预警/人群预警详情
 */
export type TAreaWarningDetailParams = {
  etiologyId: string;
  type: string; //1区域预警，2年龄预警，3人群预警
};
export const areaWarningDetailApi = (params: TAreaWarningDetailParams) =>
  request.get(API.GET_AREA_WARNING_DETAIL, { params });

/**
 * @description 年龄预警列表
 */
export const ageWarningListApi = (params: TAreaWarningListParams) =>
  request.get(API.GET_AGE_WARNING_LIST, { params });

/**
 * @description 人群预警列表
 */
export const peopleWarningListApi = (params: TAreaWarningListParams) =>
  request.get(API.GET_PEOPLE_WARNING_LIST, { params });
