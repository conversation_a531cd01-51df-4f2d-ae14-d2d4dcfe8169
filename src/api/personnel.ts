import request from '@/utils/request';

export enum API {
  // 人员列表
  GET_USER_LIST = '/system/user/list',
  // 删除员工
  POST_DELETE_USER = '/system/user',
  // 新增员工
  POST_ADD_USER = '/system/user',
  // 编辑员工
  POST_EDIT_USER = '/system/user',
  // 修改用户信息
  UPDATE_USER = '/system/user/profile',
  // 用户授权角色
  POST_USER_AUTH_ROLE = '/system/user/authRole',
  // 人员列表（不分页）
  GET_USER_SELECT_LIST = '/system/user/selectList',
  // 用户详情
  GET_USER_DETAIL = '/system/user',
  // 批量授权角色
  POST_BATCH_AUTH = '/system/user/dathAuthRole',

  // 重置用户密码
  PUT_RESET_USER_PASSWORD = '/system/user/resetPwd',
  // 解锁用户
  GET_UNLOCK_USER = '/system/user/unlockUser',
}

/**
 * @interface 获取人员列表参数
 */
export interface IGetUserListParam {
  pageSize?: number;
  pageNum?: number;
  deptId: number;
  status?: number;
  nickName?: string;
  phonenumber?: string;
  email?: string;
}
/**
 * @description 获取人员列表
 */
export const queryUserList = async (params: IGetUserListParam) =>
  request.get(API.GET_USER_LIST, { params });

/**
 * @description 删除员工
 */
export const queryDeleteUser = async (data: { ids: string }) =>
  request.delete(API.POST_DELETE_USER + '/' + data.ids);

/**
 * @interface 新增/编辑员工参数
 */
export interface ISaveEmployeeParam {
  userId?: number;
  deptId?: number;
  nickName?: string;
  sex?: string;
  userName?: string;
  password?: string;
  phonenumber?: string;
  ebsUserId?: number;
  email?: string;
  status?: string;
  roleIds?: number[];
  notPassword?: string;
  signature?: string;
}
/**
 * @description 新增员工
 */
export const queryAddUser = async (data: ISaveEmployeeParam) =>
  request.post(API.POST_ADD_USER, { data });

/**
 * @description 编辑员工
 */
export const queryEditUser = async (data: ISaveEmployeeParam) =>
  request.put(API.POST_EDIT_USER, { data });

/**
 * @interface 用户授权角色参数
 */
export interface IUserAuthRoleParam {
  userId: number;
  roleIds: string;
}
/**
 * @description 用户授权角色
 */
export const queryUserAuthRole = async (params: IUserAuthRoleParam) =>
  request.put(API.POST_USER_AUTH_ROLE, { params });

/**
 * @description 获取用户列表（不分页）
 */
export const queryUserSelectList = async (params: { deptId: number }) =>
  request.get(API.GET_USER_SELECT_LIST, { params });

/**
 * @description 根据用户编号获取授权角色
 */
export const queryUserAuthRoleList = async (id: number) =>
  request.get(API.POST_USER_AUTH_ROLE + '/' + id);

/**
 * @description 获取用户详情
 */
export const queryUserDetail = async (id: number) =>
  request.get(API.GET_USER_DETAIL + '/' + id);

/**
 * @interface 批量授权角色参数
 */
export interface IBatchAuthParam {
  userIds: number[];
  roleIds: number[];
}
/**
 * @description 批量授权角色
 */
export const queryBatchAuth = async (data: IBatchAuthParam) =>
  request.post(API.POST_BATCH_AUTH, { data });
/**
 * @description 修改用户信息
 */
export const updateUserInfo = async (data: any) =>
  request.put(API.UPDATE_USER, { data });

/**
 * @description 重置用户密码
 */
export const queryResetUserPassword = async (data: any) =>
  request.put(API.PUT_RESET_USER_PASSWORD, { data });

/**
 * @description 解锁用户
 */
export const unlockUserApi = (params: { userId: number }) =>
  request.get(API.GET_UNLOCK_USER, { params });
