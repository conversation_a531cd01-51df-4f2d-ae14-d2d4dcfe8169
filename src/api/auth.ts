/**
 * @description 权限校验相关的 API 请求定义，包含用户登录、登出等
 */
import request from '@/utils/request';

enum API {
  UserInfo = '/api/getUser',
  // 获取权限路由
  GET_PERMISSION_ROUTER = '/getRouters',
  // 统一认证访问
  Unified_Auth = '/validateSingleSignOn',
}

export const getUserInfo = () => request.get(API.UserInfo, {});

/**
 * @description 获取权限路由
 */
export const queryPermissionRouter = async () =>
  request.get(API.GET_PERMISSION_ROUTER);

/**
 * @description 统一认证访问
 */
export const onHandleUnifiedAuth = async (code: string) =>
  request.get(`${API.Unified_Auth}/?code=${code}`);
