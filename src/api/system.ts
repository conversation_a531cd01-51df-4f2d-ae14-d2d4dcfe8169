import { codeDefinition } from '@/constants';
import request from '@/utils/request';

/**
 * 机构分页查询接口
 * @param params 查询参数
 * @returns Promise
 */
export const getDeptPage = async (params: Record<string, any>) => {
  return request
    .get('/system/dept/page', {
      params,
    })
    .then((res: any) => {
      // 如果后端直接返回数据，不需要处理
      if (res.code === codeDefinition.QUERY_SUCCESS) {
        return res;
      }
      // 处理错误情况
      return Promise.reject(new Error(res.msg || '获取机构列表失败'));
    });
};
