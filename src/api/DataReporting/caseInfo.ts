import request from '@/utils/request';

enum API {
  // 患者画像
  // 基本信息
  GET_PATIENT_BASE_INFO = '/data/emrPatient/info/patientPortraits',
  // 诊疗活动信息
  GET_PATIENT_MEDICAL_INFO = '/data/emrActivity/info/portraitsByPatientId',
  // 传染病报告卡
  GET_PATIENT_INFECTIOUS_INFO = '/data/emrInf/report/portraitsByPatientId',
  // 检查报告
  GET_PATIENT_INSPECTION_INFO = '/data/emrEx/clinical/portraitsByPatientId',
  // 检验报告
  GET_PATIENT_CHECK_INFO = '/data/emrEx/lab/portraitByPatientId',
}
export const patientBaseInfoApi = (name: string) =>
  request.get(API.GET_PATIENT_BASE_INFO + `?name=${name}`);
export const patientMedicalInfoApi = (patientId: string) =>
  request.get(API.GET_PATIENT_MEDICAL_INFO + `?patientId=${patientId}`);
export const patientInfectiousInfoApi = (patientId: string) =>
  request.get(API.GET_PATIENT_INFECTIOUS_INFO + `?patientId=${patientId}`);
export const patientInspectionInfoApi = (patientId: string) =>
  request.get(API.GET_PATIENT_INSPECTION_INFO + `?patientId=${patientId}`);
export const patientCheckInfoApi = (patientId: string) =>
  request.get(API.GET_PATIENT_CHECK_INFO + `?patientId=${patientId}`);
