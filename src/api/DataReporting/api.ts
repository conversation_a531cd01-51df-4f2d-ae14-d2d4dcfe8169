import request from '@/utils/request';

enum API {
  // 样本采集记录列表
  GET_Sample_Collection_Record_LIST = '/data/report/process/sampleCollectionRecord/listByItem',
  // 详情
  GET_Sample_Collection_Record_Detail = '/data/report/process/sampleCollectionRecord/findById',
  // 新增
  POST_ADD_SAMPLE_COLLECTION_RECORD = '/report/process/sampleCollectionRecord/insert',
  // 修改
  PUT_UPDATE_SAMPLE_COLLECTION_RECORD = '/report/process/sampleCollectionRecord/edit',
  // 样本接收记录列表
  GET_Sample_Receipt_Record_LIST = '/data/report/process/sampleAcceptRecord/listByItem',
  // 详情
  GET_Sample_Receipt_Record_Detail = '/data/report/process/sampleAcceptRecord/findById',
  // 新增
  POST_ADD_SAMPLE_RECEIPT_RECORD = '/report/process/sampleAcceptRecord/insert',
  // 修改
  PUT_UPDATE_SAMPLE_RECEIPT_RECORD = '/report/process/sampleAcceptRecord/edit',
  // 领样记录列表
  GET_Sample_Record_LIST = '/data/report/process/getSampleRecord/listByItem',
  // 详情
  GET_Sample_Record_Detail = '/data/report/process/getSampleRecord/findById',
  // 新增
  POST_ADD_SAMPLE_RECORD = '/report/process/getSampleRecord/insert',
  // 修改
  PUT_UPDATE_SAMPLE_RECORD = '/report/process/getSampleRecord/edit',
  // 样本前期处理记录列表
  GET_Sample_Previous_DisposeRecord_LIST = '/data/report/process/samplePreviousDisposeRecord/listByItem',
  // 详情
  GET_Sample_Previous_DisposeRecord_Detail = '/data/report/process/samplePreviousDisposeRecord/findById',
  // 新增
  POST_ADD_SAMPLE_PRE_PROCESSING_RECORD = '/report/process/samplePreviousDisposeRecord/insert',
  // 修改
  PUT_UPDATE_SAMPLE_PRE_PROCESSING_RECORD = '/report/process/samplePreviousDisposeRecord/edit',
  // 检测过程记录列表
  GET_Test_Process_Record_LIST = '/data/report/process/detectionProcessRecord/listByItem',
  // 详情
  GET_Test_Process_Record_Detail = '/data/report/process/detectionProcessRecord/findById',
  // 新增
  POST_ADD_TEST_PROCESS_RECORD = '/report/process/detectionProcessRecord/insert',
  // 修改
  PUT_UPDATE_TEST_PROCESS_RECORD = '/report/process/detectionProcessRecord/edit',
  // 报告编制记录列表
  GET_Report_Preparation_Record_LIST = '/data/report/process/reportPreparation/listByItem',
  // 详情
  GET_Report_Preparation_Record_Detail = '/data/report/process/reportPreparation/findById',
  // 新增
  POST_ADD_REPORT_PREPARATION_RECORD = '/report/process/reportPreparation/insert',
  // 修改
  PUT_UPDATE_REPORT_PREPARATION_RECORD = '/report/process/reportPreparation/edit',
  // 样本处置记录列表
  GET_Sample_Handling_Record_LIST = '/data/report/process/sampleDisposeRecord/listByItem',
  // 详情
  GET_Sample_Handling_Record_Detail = '/data/report/process/sampleDisposeRecord/findById',
  // 新增
  POST_ADD_SAMPLE_HANDLING_RECORD = '/report/process/sampleDisposeRecord/insert',
  // 修改
  PUT_UPDATE_SAMPLE_HANDLING_RECORD = '/report/process/sampleDisposeRecord/edit',

  // ----- 机构能力数据 -----
  // 资质能力数据列表
  GET_QUALITY_INFO_LIST = '/data/report/power/certification/listByItem',
  // 详情
  GET_QUALITY_INFO_DETAIL = '/data/report/power/certification/findById',
  // 新增
  POST_ADD_QUALITY_INFO = '/report/power/certification/insert',
  // 修改
  PUT_UPDATE_QUALITY_INFO = '/report/power/certification/edit',
  // 质量控制记录列表
  GET_QUALITY_CONTROL_RECORD_LIST = '/data/report/power/quality/control/listByItem',
  // 详情
  GET_QUALITY_CONTROL_RECORD_DETAIL = '/data/report/power/quality/control/findById',
  // 新增
  POST_ADD_QUALITY_CONTROL_RECORD = '/report/power/quality/control/insert',
  // 修改
  PUT_UPDATE_QUALITY_CONTROL_RECORD = '/report/power/quality/control/edit',
  // 检测人员列表
  GET_TEST_MEMBER_LIST = '/data/report/power/person/info/listByItem',
  // 详情
  GET_TEST_MEMBER_DETAIL = '/data/report/power/person/info/findById',
  // 新增
  POST_ADD_TEST_MEMBER = '/report/power/person/info/insert',
  // 修改
  PUT_UPDATE_TEST_MEMBER = '/report/power/person/info/edit',
  // 人员培训记录列表
  GET_PERSONNEL_TRAINING_RECORD_LIST = '/data/report/power/person/train/listByItem',
  // 详情
  GET_PERSONNEL_TRAINING_RECORD_DETAIL = '/data/report/power/person/train/findById',
  // 新增
  POST_ADD_PERSONNEL_TRAINING_RECORD = '/report/power/person/train/insert',
  // 修改
  PUT_UPDATE_PERSONNEL_TRAINING_RECORD = '/report/power/person/train/edit',

  // ----- 机构设备信息 -----
  // 设备信息列表
  GET_DEVICE_INFO_LIST = '/data/report/equip/equips',
  // 详情
  GET_DEVICE_INFO_DETAIL = '/data/report/equip/equips/detail',
  // 新增
  POST_ADD_DEVICE_INFO = '/data/report/equip/insertEquip',
  // 修改
  PUT_UPDATE_DEVICE_INFO = '/data/report/equip/editEquip',
  // 设备检定校准记录列表
  GET_DEVICE_CALIBRATE_RECORD_LIST = '/data/report/equip/calibrations',
  // 详情
  GET_DEVICE_CALIBRATE_RECORD_DETAIL = '/data/report/equip/calibrations/detail',
  // 新增
  POST_ADD_DEVICE_CALIBRATE_RECORD = '/data/report/equip/insertCalibration',
  // 修改
  PUT_UPDATE_DEVICE_CALIBRATE_RECORD = '/data/report/equip/editCalibration',
  // 设备核查记录列表
  GET_DEVICE_INSPECT_RECORD_LIST = '/data/report/equip/checks',
  // 详情
  GET_DEVICE_INSPECT_RECORD_DETAIL = '/data/report/equip/checks/detail',
  // 新增
  POST_ADD_DEVICE_INSPECT_RECORD = '/data/report/equip/insertEquCheck',
  // 修改
  PUT_UPDATE_DEVICE_INSPECT_RECORD = '/data/report/equip/editEquCheck',
  // 温湿度监控列表
  GET_DEVICE_MONITOR_LIST = '/data/report/equip/envs',
  // 详情
  GET_DEVICE_MONITOR_DETAIL = '/data/report/equip/envs/detail',
  // 新增
  POST_ADD_DEVICE_MONITOR = '/data/report/equip/insertEquEnvironment',
  // 修改
  PUT_UPDATE_DEVICE_MONITOR = '/data/report/equip/editEquEnvironment',

  // ---- 病例信息 ----
  // 患者信息列表
  GET_PATIENT_BASE_INFO_LIST = '/data/emrPatient/info/page',
  // 详情
  GET_PATIENT_BASE_INFO_DETAIL = '/data/emrPatient/info/findById',
  // 诊疗活动信息列表
  GET_MEDICAL_ACTIVITY_INFO_LIST = '/data/emrActivity/info/page',
  // 详情
  GET_MEDICAL_ACTIVITY_INFO_DETAIL = '/data/emrActivity/info/findById',
  // 传染病报告卡列表
  GET_INFECTIOUS_DISEASE_REPORT_CARD_LIST = '/data/emrInf/report/page',
  // 详情
  GET_INFECTIOUS_DISEASE_REPORT_CARD_DETAIL = '/data/emrInf/report/findById',
  // 检查报告列表
  GET_INSPECTION_REPORT_LIST = '/data/emrEx/clinical/page',
  // 详情
  GET_INSPECTION_REPORT_DETAIL = '/data/emrEx/clinical/findById',
  // 检验报告列表
  GET_CHECKOUT_REPORT_LIST = '/data/emrEx/lab/page',
  // 详情
  GET_CHECKOUT_REPORT_DETAIL = '/data/emrEx/lab/findById',
}

//查询质量考核任务列表
export interface IGetQATaskListParams {
  pageSize?: number;
  pageNum?: number;
  [key: string]: any;
}
// 样本采集记录
// 列表
export const getSampleCollectionRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Sample_Collection_Record_LIST, {
    params,
  });
// 详情
export const getSampleCollectionRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Sample_Collection_Record_Detail, {
    params,
  });
// 新增
export const addSampleCollectionRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_COLLECTION_RECORD, { data });
// 修改
export const updateSampleCollectionRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_SAMPLE_COLLECTION_RECORD, { data });

// 样本接收记录
// 列表
export const getSampleReceiptRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Sample_Receipt_Record_LIST, {
    params,
  });
// 详情
export const getSampleReceiptRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Sample_Receipt_Record_Detail, {
    params,
  });
// 新增
export const addSampleReceiptRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_RECEIPT_RECORD, { data });
// 修改
export const updateSampleReceiptRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_SAMPLE_RECEIPT_RECORD, { data });

// 领样记录
// 列表
export const getSampleRecordDataList = async (params: IGetQATaskListParams) =>
  request.get(API.GET_Sample_Record_LIST, {
    params,
  });
// 详情
export const getSampleRecordDetailsById = async (params: Record<string, any>) =>
  request.get(API.GET_Sample_Record_Detail, {
    params,
  });
// 新增
export const addSampleRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_RECORD, { data });
// 修改
export const updateSampleRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_SAMPLE_RECORD, { data });

// 样本前期处理记录
// 列表
export const getSamplePreviousDisposeRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Sample_Previous_DisposeRecord_LIST, {
    params,
  });
// 详情
export const getSamplePreviousDisposeRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Sample_Previous_DisposeRecord_Detail, {
    params,
  });
// 新增
export const addSamplePreProcessingRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_PRE_PROCESSING_RECORD, { data });
// 修改
export const updateSamplePreProcessingRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_SAMPLE_PRE_PROCESSING_RECORD, { data });

// 检测过程记录
// 列表
export const getTestProcessRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Test_Process_Record_LIST, {
    params,
  });
// 详情
export const getTestProcessRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Test_Process_Record_Detail, {
    params,
  });
// 新增
export const addTestProcessRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_TEST_PROCESS_RECORD, { data });
// 修改
export const updateTestProcessRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_TEST_PROCESS_RECORD, { data });

// 报告编制记录
// 列表
export const getReportPreparationRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Report_Preparation_Record_LIST, {
    params,
  });
// 详情
export const getReportPreparationRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Report_Preparation_Record_Detail, {
    params,
  });
// 新增
export const addReportPreparationApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_REPORT_PREPARATION_RECORD, { data });
// 修改
export const updateReportPreparationRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_REPORT_PREPARATION_RECORD, { data });

//样本处置记录
// 列表
export const getSampleHandlingRecordDataList = async (
  params: IGetQATaskListParams
) =>
  request.get(API.GET_Sample_Handling_Record_LIST, {
    params,
  });
// 详情
export const getSampleHandlingRecordDetailsById = async (
  params: Record<string, any>
) =>
  request.get(API.GET_Sample_Handling_Record_Detail, {
    params,
  });
// 新增
export const addSampleHandingRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_SAMPLE_HANDLING_RECORD, { data });
// 修改
export const updateSampleHandingRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_SAMPLE_HANDLING_RECORD, { data });

/**
 * @description 资质能力数据列表
 */
export const qualityInfoListApi = (params: {
  pageSize: number;
  pageNum: number;
  projectName?: string;
  methodShort?: string;
  methodAll?: string;
}) => request.get(API.GET_QUALITY_INFO_LIST, { params });
/**
 * @description 资质能力数据详情
 */
export const qualityInfoDetailApi = (id: string) =>
  request.get(API.GET_QUALITY_INFO_DETAIL + `?id=${id}`);
// 新增
export const addQualityInfoApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_QUALITY_INFO, { data });
// 修改
export const updateQualityInfoApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_QUALITY_INFO, { data });

/**
 * @description 质量控制记录列表
 */
export const qualityControlRecordListApi = (params: {
  pageSize: number;
  pageNum: number;
  fileNo?: string;
  fileName?: string;
  fileType?: string;
}) => request.get(API.GET_QUALITY_CONTROL_RECORD_LIST, { params });
/**
 * @description 质量控制记录详情
 */
export const qualityControlRecordDetailApi = (id: string) =>
  request.get(API.GET_QUALITY_CONTROL_RECORD_DETAIL + `?id=${id}`);
// 新增
export const addQualityControlRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_QUALITY_CONTROL_RECORD, { data });
// 修改
export const updateQualityControlRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_QUALITY_CONTROL_RECORD, { data });

/**
 * @description 检测人员列表
 */
export const testMemberListApi = (params: {
  pageSize: number;
  pageNum: number;
  name?: string;
  peopleClassify?: string;
  peopleNo?: string;
  sex?: string;
}) => request.get(API.GET_TEST_MEMBER_LIST, { params });
/**
 * @description 检测人员详情
 */
export const testMemberDetailApi = (id: string) =>
  request.get(API.GET_TEST_MEMBER_DETAIL + `?id=${id}`);
// 新增
export const addTestMemberApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_TEST_MEMBER, { data });
// 修改
export const updateTestMemberApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_TEST_MEMBER, { data });

/**
 * @description 人员培训记录列表
 */
export const personnelTrainingRecordListApi = (params: {
  pageSize: number;
  pageNum: number;
  classify?: string;
  name?: string;
  unit?: string;
}) => request.get(API.GET_PERSONNEL_TRAINING_RECORD_LIST, { params });
/**
 * @description 人员培训记录详情
 */
export const personnelTrainingRecordDetailApi = (id: string) =>
  request.get(API.GET_PERSONNEL_TRAINING_RECORD_DETAIL + `?id=${id}`);
// 新增
export const addPersonnelTrainingRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_PERSONNEL_TRAINING_RECORD, { data });
// 修改
export const updatePersonnelTrainingRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_PERSONNEL_TRAINING_RECORD, { data });

/**
 * @description 设备信息列表
 */
export const deviceInfoListApi = (params: {
  pageSize: number;
  pageNum: number;
  equName?: string;
  codeFinance?: string;
  equCode?: string;
  specs?: string;
  productNo?: string;
}) => request.get(API.GET_DEVICE_INFO_LIST, { params });
/**
 * @description 设备信息详情
 */
export const deviceInfoDetailApi = (id: string) =>
  request.get(API.GET_DEVICE_INFO_DETAIL + `?id=${id}`);
// 新增
export const addDeviceInfoApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_DEVICE_INFO, { data });
// 修改
export const updateDeviceInfoApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_DEVICE_INFO, { data });

/**
 * @description 设备检定校准记录列表
 */
export const deviceCalibrateRecordListApi = (params: {
  pageSize: number;
  pageNum: number;
  certNo?: string;
  judgeResult?: string;
  unit?: string;
}) => request.get(API.GET_DEVICE_CALIBRATE_RECORD_LIST, { params });
/**
 * @description 设备检定校准记录详情
 */
export const deviceCalibrateRecordDetailApi = (id: string) =>
  request.get(API.GET_DEVICE_CALIBRATE_RECORD_DETAIL + `?id=${id}`);
// 新增
export const addDeviceCalibrateRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_DEVICE_CALIBRATE_RECORD, { data });
// 修改
export const updateDeviceCalibrateRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_DEVICE_CALIBRATE_RECORD, { data });

/**
 * @description 设备核查记录列表
 */
export const deviceInspectRecordListApi = (params: {
  pageSize: number;
  pageNum: number;
  planName?: string;
  checkDept?: string;
  equName?: string;
  equCode?: string;
}) => request.get(API.GET_DEVICE_INSPECT_RECORD_LIST, { params });
/**
 * @description 设备核查记录详情
 */
export const deviceInspectRecordDetailApi = (id: string) =>
  request.get(API.GET_DEVICE_INSPECT_RECORD_DETAIL + `?id=${id}`);
// 新增
export const addDeviceInspectRecordApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_DEVICE_INSPECT_RECORD, { data });
// 修改
export const updateDeviceInspectRecordApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_DEVICE_INSPECT_RECORD, { data });

/**
 * @description 温湿度监控列表
 */
export const deviceMonitorListApi = (params: {
  pageSize: number;
  pageNum: number;
  equName?: string;
  equCode?: string;
}) => request.get(API.GET_DEVICE_MONITOR_LIST, { params });
/**
 * @description 温湿度监控详情
 */
export const deviceMonitorDetailApi = (id: string) =>
  request.get(API.GET_DEVICE_MONITOR_DETAIL + `?id=${id}`);
// 新增
export const addDeviceMonitorApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_DEVICE_MONITOR, { data });
// 修改
export const updateDeviceMonitorApi = (data: Record<string, any>) =>
  request.put(API.PUT_UPDATE_DEVICE_MONITOR, { data });

// ---- 病例信息 ----
type TCommonPage = {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: 'desc' | 'asc';
};
/**
 * @description 患者详细信息列表
 */
export const patientBaseInfoListApi = (params: TCommonPage) =>
  request.get(API.GET_PATIENT_BASE_INFO_LIST, { params });
/**
 * @description 患者详细信息详情
 */
export const patientBaseInfoDetailApi = (id: string) =>
  request.get(API.GET_PATIENT_BASE_INFO_DETAIL + `?id=${id}`);
/**
 * @description 诊疗活动信息列表
 */
export const medicalActivityInfoListApi = (params: TCommonPage) =>
  request.get(API.GET_MEDICAL_ACTIVITY_INFO_LIST, { params });
/**
 * @description 诊疗活动信息详情
 */
export const medicalActivityInfoDetailApi = (id: string) =>
  request.get(API.GET_MEDICAL_ACTIVITY_INFO_DETAIL + `?id=${id}`);
/**
 * @description 传染病报告卡列表
 */
export const infectiousDiseaseReportCardListApi = (params: TCommonPage) =>
  request.get(API.GET_INFECTIOUS_DISEASE_REPORT_CARD_LIST, { params });
/**
 * @description 传染病报告卡详情
 */
export const infectiousDiseaseReportCardDetailApi = (id: string) =>
  request.get(API.GET_INFECTIOUS_DISEASE_REPORT_CARD_DETAIL + `?id=${id}`);
/**
 * @description 检查报告列表
 */
export const inspectionReportListApi = (params: TCommonPage) =>
  request.get(API.GET_INSPECTION_REPORT_LIST, { params });
/**
 * @description 检查报告详情
 */
export const inspectionReportDetailApi = (id: string) =>
  request.get(API.GET_INSPECTION_REPORT_DETAIL + `?id=${id}`);
/**
 * @description 检验报告列表
 */
export const checkoutReportListApi = (params: TCommonPage) =>
  request.get(API.GET_CHECKOUT_REPORT_LIST, { params });
/**
 * @description 检验报告详情
 */
export const checkoutReportDetailApi = (id: string) =>
  request.get(API.GET_CHECKOUT_REPORT_DETAIL + `?id=${id}`);
