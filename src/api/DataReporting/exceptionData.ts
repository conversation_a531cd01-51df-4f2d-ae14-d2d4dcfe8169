import request from '@/utils/request';

enum API {
  // 数据校验规则
  // 列表
  GET_DATA_CHECK_RULE_LIST = '/data/abnormalRule/getList',
  // 启用
  GET_DATA_CHECK_RULE_ENABLE = '/abnormalRule/enable',
  // 禁用
  GET_DATA_CHECK_RULE_DISABLE = '/abnormalRule/disable',
  // 新增
  POST_ADD_DATA_CHECK_RULE = '/abnormalRule/add',
  // 更新
  POST_UPDATE_DATA_CHECK_RULE = '/abnormalRule/update',
  // 异常数据信息
  // 列表
  GET_EXCEPTION_DATA_INFO_LIST = '/data/abnormalFiledRecord/getList',
  // 样品检测数据管理
  // 列表
  GET_SAMPLE_TEST_DATA_LIST = '/data/abnormalSample/listByItem',
  // 样本采集记录详情
  GET_SAMPLE_COLLECTION_RECORD_DETAIL = '/data/abnormalSample/getCollectionRecordByCode',
  // 样本接收记录详情
  GET_SAMPLE_RECEIPT_RECORD_DETAIL = '/data/abnormalSample/getSampleAcceptRecord',
  // 领样记录详情
  GET_SAMPLE_ACQUISITION_RECORD_DETAIL = '/data/abnormalSample/getGetSampleRecord',
  // 样本前期处理记录详情
  GET_SAMPLE_PRE_PROCESSING_RECORD_DETAIL = '/data/abnormalSample/getSamplePreviousDisposeRecord',
  // 检测过程记录详情
  GET_TEST_PROCESS_RECORD_DETAIL = '/data/abnormalSample/getDetectionProcessRecord',
  // 报告编制记录详情
  GET_REPORT_PREPARATION_RECORD_DETAIL = '/data/abnormalSample/getReportPreparationRecord',
  // 样本处置记录详情
  GET_SAMPLE_HANDING_RECORD_DETAIL = '/data/abnormalSample/getSampleDisposeRecord',
  // 异常数据核实
  // 列表
  GET_EXCEPTION_VERIFY_PENDING_LIST = '/data/abnormalFiledRecord/getWaitVerifiedList',
  GET_EXCEPTION_VERIFY_LIST = '/data/abnormalFiledRecord/getVerifiedList',
  // 保存
  POST_SAVE_EXCEPTION_VERIFY = '/abnormalHandel/verifiedSave',
  // 提交
  POST_SUBMIT_EXCEPTION_VERIFY = '/abnormalHandel/verified',
  // 忽略全部
  GET_EXCEPTION_VERIFY_IGNORE_ALL = '/abnormalHandel/verifiedIgnoreAll',
  // 异常数据
  GET_EXCEPTION_DATA_LIST = '/data/abnormalHandel/getList',
  // 异常数据整改
  // 列表
  GET_EXCEPTION_RECTIFICATION_PENDING_LIST = '/data/abnormalFiledRecord/getWaitRectificationList',
  GET_EXCEPTION_RECTIFICATION_LIST = '/data/abnormalFiledRecord/getRectificationList',
  // 保存
  POST_SAVE_EXCEPTION_RECTIFICATION = '/abnormalHandel/rectificationSave',
  // 提交
  POST_SUBMIT_EXCEPTION_RECTIFICATION = '/abnormalHandel/rectification',
  // 异常整改审核
  // 列表
  GET_EXCEPTION_AUDIT_PENDING_LIST = '/data/abnormalFiledRecord/getWaitReviewList',
  GET_EXCEPTION_AUDIT_LIST = '/data/abnormalFiledRecord/getReviewList',
  // 保存
  POST_SAVE_EXCEPTION_AUDIT = '/abnormalHandel/auditSave',
  // 提交
  POST_SUBMIT_EXCEPTION_AUDIT = '/abnormalHandel/audit',
  // 异常数据统计
  // 异常类型统计
  GET_EXCEPTION_TYPE_STATISTICS = '/data/abnormalFiledRecord/getTypeVisual',
  // 机构统计
  GET_ORG_STATISTICS = '/data/abnormalFiledRecord/getOrgVisual',
}
/**
 * @description 数据校验规则
 */
export const dataCheckRuleListApi = (params: Record<string, any>) =>
  request.get(API.GET_DATA_CHECK_RULE_LIST, { params });
export const dataCheckRuleEnableApi = (ids: string) =>
  request.get(API.GET_DATA_CHECK_RULE_ENABLE + `?ids=${ids}`);
export const dataCheckRuleDisableApi = (ids: string) =>
  request.get(API.GET_DATA_CHECK_RULE_DISABLE + `?ids=${ids}`);
export const addDataCheckRuleApi = (data: Record<string, any>) =>
  request.post(API.POST_ADD_DATA_CHECK_RULE, { data });
export const updateDataCheckRuleApi = (data: Record<string, any>) =>
  request.post(API.POST_UPDATE_DATA_CHECK_RULE, { data });

/**
 * @description 异常数据信息
 */
export const exceptionDataInfoListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_DATA_INFO_LIST, { params });

/**
 * @description 样品检测数据管理
 */
export const sampleTestDataListApi = (params: Record<string, any>) =>
  request.get(API.GET_SAMPLE_TEST_DATA_LIST, { params });
export const sampleCollectionRecordDetailApi = (code: string) =>
  request.get(API.GET_SAMPLE_COLLECTION_RECORD_DETAIL + `?code=${code}`);
export const sampleReceiptRecordDetailApi = (code: string) =>
  request.get(API.GET_SAMPLE_RECEIPT_RECORD_DETAIL + `?code=${code}`);
export const sampleAcquisitionRecordDetailApi = (code: string) =>
  request.get(API.GET_SAMPLE_ACQUISITION_RECORD_DETAIL + `?code=${code}`);
export const samplePreProcessingRecordDetailApi = (code: string) =>
  request.get(API.GET_SAMPLE_PRE_PROCESSING_RECORD_DETAIL + `?code=${code}`);
export const testProcessRecordDetailApi = (code: string) =>
  request.get(API.GET_TEST_PROCESS_RECORD_DETAIL + `?code=${code}`);
export const reportPreparationRecordDetailApi = (code: string) =>
  request.get(API.GET_REPORT_PREPARATION_RECORD_DETAIL + `?code=${code}`);
export const sampleHandingRecordDetailApi = (code: string) =>
  request.get(API.GET_SAMPLE_HANDING_RECORD_DETAIL + `?code=${code}`);

/**
 * @description 异常数据核实
 */
export const exceptionVerifyPendingListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_VERIFY_PENDING_LIST, { params });
export const exceptionVerifyListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_VERIFY_LIST, { params });
export const saveExceptionVerifyApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SAVE_EXCEPTION_VERIFY, { data });
export const submitExceptionVerifyApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SUBMIT_EXCEPTION_VERIFY, { data });
export const exceptionVerifyIgnoreAllApi = (sampleCode: string) =>
  request.get(
    API.GET_EXCEPTION_VERIFY_IGNORE_ALL + `?sampleCode=${sampleCode}`
  );

/**
 * @description 异常数据
 */
export const exceptionDataListApi = (sampleCode: string) =>
  request.get(API.GET_EXCEPTION_DATA_LIST + `?sampleCode=${sampleCode}`);

/**
 * @description 异常数据整改
 */
export const exceptionRectificationPendingListApi = (
  params: Record<string, any>
) => request.get(API.GET_EXCEPTION_RECTIFICATION_PENDING_LIST, { params });
export const exceptionRectificationListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_RECTIFICATION_LIST, { params });
export const saveExceptionRectificationApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SAVE_EXCEPTION_RECTIFICATION, { data });
export const submitExceptionRectificationApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SUBMIT_EXCEPTION_RECTIFICATION, { data });

/**
 * @description 异常整改审核
 */
export const exceptionAuditPendingListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_AUDIT_PENDING_LIST, { params });
export const exceptionAuditListApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_AUDIT_LIST, { params });
export const saveExceptionAuditApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SAVE_EXCEPTION_AUDIT, { data });
export const submitExceptionAuditApi = (data: Record<string, any>[]) =>
  request.post(API.POST_SUBMIT_EXCEPTION_AUDIT, { data });

/**
 * @description 异常数据统计
 */
export const exceptionTypeStatisticsApi = (params: Record<string, any>) =>
  request.get(API.GET_EXCEPTION_TYPE_STATISTICS, { params });
export const orgStatisticsApi = (params: Record<string, any>) =>
  request.get(API.GET_ORG_STATISTICS, { params });
