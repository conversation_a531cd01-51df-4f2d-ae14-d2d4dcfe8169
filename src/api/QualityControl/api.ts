import request from '@/utils/request';

enum API {
  // ---- 送检及时率分析 ----
  GET_DELIVERY_TIMELY_RATE_LIST = '/data/qc/analyze/submissionList',
  // ---- 病原检测率分析 ----
  GET_PATHOGEN_TEST_RATE_LIST = '/data/qc/analyze/etiologyList',
  // ---- 病原检测及时率分析 ----
  GET_PATHOGEN_TEST_TIMELY_RATE_LIST = '/data/qc/analyze/etiologyTimelyList',
  // ---- 报告及时性分析 ----
  GET_REPORT_TIMELINESS_LIST = '/data/qc/analyze/resultTimelyList',
  // 阳性样本
  GET_POSITIVE_SAMPLE_LIST = '/data/qc/analyze/qcAnalyzeSampleList',
}

type TCommonPage = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: 'desc' | 'asc';
};

/**
 * @description 送检及时率分析列表
 */
export const deliveryTimelyRageListApi = (
  params: TCommonPage & {
    orgName?: string;
    sampleStartDate?:string;
    sampleEndDate?:string
  }
) => request.get(API.GET_DELIVERY_TIMELY_RATE_LIST, { params });

/**
 * @description 送检及时率分析列表
 */
export const pathogenTestRageListApi = (
  params: TCommonPage & {
    orgName?: string;
    sampleStartDate?:string;
    sampleEndDate?:string
  }
) => request.get(API.GET_PATHOGEN_TEST_RATE_LIST, { params });

/**
 * @description 送检及时率分析列表
 */
export const pathogenTestTimelyRageListApi = (
  params: TCommonPage & {
    orgName?: string;
    sampleStartDate?:string;
    sampleEndDate?:string
  }
) => request.get(API.GET_PATHOGEN_TEST_TIMELY_RATE_LIST, { params });

/**
 * @description 送检及时率分析列表
 */
export const reportTimelinessListApi = (
  params: TCommonPage & {
    orgName?: string;
  }
) => request.get(API.GET_REPORT_TIMELINESS_LIST, { params });

/**
 * @description 阳性样本列表
 */
export const positiveSampleListApi = (
  params: TCommonPage & {
    analyzeType: number;
    sampleCode?: string;
    sampleName?: string;
    orgId: string;
  }
) => request.get(API.GET_POSITIVE_SAMPLE_LIST, { params });


export const submissionStackChart = (
  params: any
) => request.get("/data/qc/analyze/submissionStackChart", { params });

