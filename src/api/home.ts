/**
 * @description 首页相关的 API 请求定义
 */
import request from '@/utils/request';

enum API {
  // 获取操作指引列表数据
  GET_OPERATION_GUIDE_LIST = '/system/operation/guide',
  // 获取待办事项列表数据
  GET_TODO_LIST = '/commission',
  //
  GET_DASHBOARD_SUMMARY = '/banner',
  //
  GET_PATHOGEN_DATA = '/pathogenDetectionData',
  //
  GET_ANNUAL_DISTRIBUTION = '/pathogenDetectionYearData',
  //
  GET_ASSESSMENT_RESULTS = '/dashboard/assessment-results',
  //
  GET_PLAN_EXECUTION = '/cityMonitExecuteAnalyse',
}

/**
 * @description 获取操作指引列表数据
 */
export const getOperationGuideList = async (params?: Record<string, any>) =>
  request.get(API.GET_OPERATION_GUIDE_LIST, { params });

/**
 * @description 获取待办事项列表
 */
export const getTodoList = async (params?: Record<string, any>) =>
  request.get(API.GET_TODO_LIST, { params });

/**
 * @description 获取仪表盘顶部汇总卡片数据
 */
export const getSummaryDataApi = () => request.get(API.GET_DASHBOARD_SUMMARY);

/**
 * @description 获取病原检测数据表格
 * @param params - 查询参数，例如 { labType, dateRange }
 */
export const getPathogenDataApi = (params: any) =>
  request.get(API.GET_PATHOGEN_DATA, { params });

/**
 * @description 获取病原样本年度分布图表数据
 * @param 
 */
export const getAnnualDistributionApi = () =>
  request.get(API.GET_ANNUAL_DISTRIBUTION);

/**
 * @description 获取待办事项数据
 */
export const getTodoListApi = () => request.get(API.GET_TODO_LIST);

/**
 * @description 获取地市州监测计划执行情况
 * @param year - 年份
 */
export const getPlanExecutionApi = (year: string) =>
  request.get(API.GET_PLAN_EXECUTION, { params: { year } });
