
/*
 * @description 编号管理相关 API 请求
 */
import request from '@/utils/request';

enum API {
  QUERY_CONFIG_LIST = '/system/config/list',
  CONFIG_DETAIL = '/system/config/',
}

/**
 * @description 查询系统设置列表
 */
export const queryConfigList = async (params?: Record<string, any>) =>
  request.get(API.QUERY_CONFIG_LIST, { params });

/**
 * @description 查询系统设置详情
 */
export const queryConfigDetail = async (id: string | number) =>
  request.get(API.CONFIG_DETAIL + id);

/**
 * @description 修改系统设置
 */
export const editConfigDetail = async (data: any) =>
  request.put(API.CONFIG_DETAIL, { data });

/**
 * @description 新增系统设置
 */
export const addConfigDetail = async (data: any) =>
  request.post(API.CONFIG_DETAIL, { data });

/**
 * @description 删除系统设置
 */
export const DeleteConfig = async (data: any) =>
  request.delete(API.CONFIG_DETAIL + data);
