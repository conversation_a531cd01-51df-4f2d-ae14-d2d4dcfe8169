import request from '@/utils/request';

enum API {
  // 质控在线
  // 整体概况
  GET_OVERALL_OVERVIEW_DATA = '/data/visual/qcOnline/bigPicture',
  // 机构风险数据
  GET_ORG_RISK_DATA = '/data/orgRiskEvaluation/orgSelectorList',
  // 风险线索数据
  GET_RISK_CUES_DATA = '/data/visual/qcOnline/riskCues',

  // 机构风险分析
  Get_Org_Risk_Assisy = '/data/visual/qcOnline/ana',

  // 机构风险评价
  Get_Org_Risk_Comment = '/data/visual/qcOnline/evaluation',
}
export const overallOverviewDataApi = () =>
  request.get(API.GET_OVERALL_OVERVIEW_DATA);
export const orgRiskDataApi = () => request.get(API.GET_ORG_RISK_DATA);
export const riskCuesDataApi = () => request.get(API.GET_RISK_CUES_DATA);

/**
 * 获取机构风险分析数据
 */
export const getOrgRiskAssisy = (params?: Record<string, any>) =>
  request.get(API.Get_Org_Risk_Assisy, { params });

/**
 * 获取机构风险评价数据
 */
export const getOrgRiskComment = (params?: Record<string, any>) =>
  request.get(API.Get_Org_Risk_Comment, { params });
