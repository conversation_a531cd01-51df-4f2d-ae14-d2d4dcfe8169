import request from '@/utils/request';

enum API {
  // 质控指标
  // 送检及时率分析
  GET_SAMPLE_SUBMISSION_TIMELINESS_RATE_DATA = '/data/visual/qcIndex/submisVisualization',
  GET_SAMPLE_SUBMISSION_TIMELINESS_COUNT = '/data/visual/qcIndex/visualizationCount',
  // 病原检测及时率分析
  GET_PATHOGEN_DETECTION_TIMELINESS_RATE_DATA = '/data/visual/qcIndex/etiologyTimeVisualization',
  GET_PATHOGEN_DETECTION_TIMELINESS_COUNT = '/data/visual/qcIndex/visualizationNum',
  // 病原检测率分析
  GET_PATHOGEN_DETECTION_RATE_DATA = '/data/visual/qcIndex/etiologyVisualization',
  // 及时性分析
  GET_TIMELINESS_ANALYSIS_DATA = '/data/visual/qcIndex/resultTimelyVisualization',
}
export const sampleSubmissionTimelinessRateDataApi = (params:any)  =>
  request.get(API.GET_SAMPLE_SUBMISSION_TIMELINESS_RATE_DATA, { params });
export const sampleSubmissionTimelinessCountApi =(params:any) =>
  request.get(API.GET_SAMPLE_SUBMISSION_TIMELINESS_COUNT, { params });
export const pathogenDetectionTimelinessRateDataApi =  (params:any) =>
  request.get(API.GET_PATHOGEN_DETECTION_TIMELINESS_RATE_DATA , { params });
export const pathogenDetectionTimelinessCountApi = (params:any) =>
  request.get(API.GET_PATHOGEN_DETECTION_TIMELINESS_COUNT, { params });
export const pathogenDetectionRateDataApi = (params:any) =>
  request.get(API.GET_PATHOGEN_DETECTION_RATE_DATA, { params });
export const timelinessRateApi = (params:any) =>
  request.get(API.GET_TIMELINESS_ANALYSIS_DATA, { params });

export const qcIndexMap = (params:any) =>
  request.get("/data/visual/qcIndex/qcIndexMap", { params });




export const reportNum = (params:any) =>
  request.get("/data/visual/qcIndex/reportNum", { params });







