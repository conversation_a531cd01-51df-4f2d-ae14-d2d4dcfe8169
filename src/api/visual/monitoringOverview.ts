import request from '@/utils/request';

enum API {
  // 监测概况
  // 任务概况
  GET_TASK_OVERVIEW_DATA = '/data/visual/monitOverView/task/overview',
  // 任务进度
  GET_TASK_PROGRESS_DATA = '/data/visual/monitOverView/task/progress',
  // 区域分析
  GET_MAP_CONTAINER_ANALYSIS_DATA = '/data/visual/monitOverView/region/analysis',
  // 监测哨点任务执行情况
  GET_IMPLEMENTATION_STATUS_DATA = '/data/visual/monitOverView/sentinel',
  // 年龄特征分析
  GET_AGE_ANALYSIS_DATA = '/data/visual/monitOverView/ageCharacteristicAnalysis',
  // 人群特征分析
  GET_PEOPLE_ANALYSIS_DATA = '/data/visual/monitOverView/crowdCharacteristicAnalysis',
  // 数据驾驶舱
  GET_DATA_COCKPIT_DATA = '/data/visual/dataCockpit',
}
export const taskOverviewDataApi = () =>
  request.get(API.GET_TASK_OVERVIEW_DATA);
export const taskProgressDataApi = () =>
  request.get(API.GET_TASK_PROGRESS_DATA);
export const mapContainerAnalysisDataApi = () =>
  request.get(API.GET_MAP_CONTAINER_ANALYSIS_DATA);
export const implementationStatusDataApi = () =>
  request.get(API.GET_IMPLEMENTATION_STATUS_DATA);
export const ageAnalysisDataApi = () => request.get(API.GET_AGE_ANALYSIS_DATA);
export const peopleAnalysisDataApi = (params: Record<string, any>) =>
  request.get(API.GET_PEOPLE_ANALYSIS_DATA, { params });

/**
 * @description  数据驾驶舱
 */
export const dataCockpitDataApi = () => request.get(API.GET_DATA_COCKPIT_DATA);
