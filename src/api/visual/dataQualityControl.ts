import request from '@/utils/request';

enum API {
  // 数据质控
  // 机构分析
  GET_ORG_ANALYSIS_DATA = '/data/visual/dataQc/institutionalAnalysis',
  // 异常类型分析
  GET_EXCEPTION_TYPE_ANALYSIS_DATA = '/data/visual/dataQc/abnormalTypeAnalysis',
  // 月度分析
  GET_MONTHLY_ANALYSIS_DATA = '/data/visual/dataQc/monthlyAnalysis',
  // 年度分析
  GET_ANNUAL_ANALYSIS_DATA = '/data/visual/dataQc/yearlyAnalysis',

  // 获取地图区域数据
  Get_Map_Area_Data = '/data/visual/dataQc/abnormalMapAna',
}
export const orgAnalysisDataApi = () => request.get(API.GET_ORG_ANALYSIS_DATA);
export const exceptionTypeAnalysisDataApi = () =>
  request.get(API.GET_EXCEPTION_TYPE_ANALYSIS_DATA);
export const monthlyAnalysisDataApi = () =>
  request.get(API.GET_MONTHLY_ANALYSIS_DATA);
export const annualAnalysisDataApi = () =>
  request.get(API.GET_ANNUAL_ANALYSIS_DATA);

/**
 * 获取地图区域数据
 */
export const getMapAreaData = (params: Record<string, any>) =>
  request.get(API.Get_Map_Area_Data, { params });
