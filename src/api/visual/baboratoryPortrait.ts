import request from '@/utils/request';

// 实验室画像
enum API {
  // 样本检测情况
  // 样本接收数
  Get_Sample_Record_Count = '/data/report/process/sampleAcceptRecord/listByItemVisualization',
  // 样本检测数
  Get_Sample_Testing_Count = '/data/report/process/detectionProcessRecord/listByItemVisualization',
  // 报告编制数
  Get_Report_Compilation_Count = '/data/report/process/reportPreparation/listByItemVisualization',
  // 实验室评级数据
  Get_Laboratory_Rating_Data = '/data/visual/pic/scorePageVisualization',
  // 异常数据
  Get_Abnormal_Data = '/data/visual/pic/anomalousData',
  // 机构基本信息
  Get_Organization_Basic_Information = '/data/visual/pic/orgNamInfo',
  // 环境监控
  Get_Environment_Monitoring_Data = '/data/visual/pic/tempHumidity',
  // 设备监控数据
  Get_Device_Monitoring_Data = '/data/visual/pic/equipmentMonitoring',
  // 机构搜索
  Search_Org_Handler = '/system/dept/listLab',
}

/**
 * 获取样本接收数
 */
export const getSampleRecordCount = (params?: Record<string, any>) =>
  request.get(API.Get_Sample_Record_Count, { params });

/**
 * 获取样本检测数
 */
export const getSampleTestingCount = (params?: Record<string, any>) =>
  request.get(API.Get_Sample_Testing_Count, { params });

/**
 * 获取报告编制数
 */
export const getReportCompilationCount = (params?: Record<string, any>) =>
  request.get(API.Get_Report_Compilation_Count, { params });

/**
 * 获取实验室评级数据
 */
export const getLaboratoryRatingData = (params?: Record<string, any>) =>
  request.get(API.Get_Laboratory_Rating_Data, { params });

/**
 * 获取异常数据
 */
export const getAbnormalData = (params?: Record<string, any>) =>
  request.get(API.Get_Abnormal_Data, { params });

/**
 * 获取机构基本信息
 */
export const getOrganizationBasicInformation = (params?: Record<string, any>) =>
  request.get(API.Get_Organization_Basic_Information, { params });

/**
 * 获取环境监控数据
 */
export const getEnvironmentMonitoringData = (params?: Record<string, any>) =>
  request.get(API.Get_Environment_Monitoring_Data, { params });

/**
 * 获取设备监控数据
 */
export const getDeviceMonitoringData = (params?: Record<string, any>) =>
  request.get(API.Get_Device_Monitoring_Data, { params });

/**
 * 搜索机构
 */

export const handleSearchOrg = (params?: Record<string, any>) =>
  request.get(API.Search_Org_Handler, { params });
