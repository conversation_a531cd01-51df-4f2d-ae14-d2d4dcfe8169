/*
 * @Author: l
 * @Date: 2025-01-15 16:13:01
 * @LastEditors: l
 * @LastEditTime: 2025-01-23 11:23:33
 * @FilePath: \xr-qc-jk-web\src\api\visual\monitorEarlyWarning.ts
 * @Description: 
 */
import request from '@/utils/request';

enum API {
  // 监测预警
  // 预警数据统计
  GET_WARNING_DATA_STATISTICS = '/data/visual/monitWarn/diseaseNature',
  // 病原阳性预警
  GET_POSITIVE_WARNING = '/data/visual/monitWarn/positiveFeature',
  // 区域态势预警
  GET_AREA_WARNING = '/data/visual/monitWarn/areaFeature',
  // 人群态势预警
  GET_PEOPLE_WARNING = '/data/visual/monitWarn/peopleFeature',
  // 年龄态势预警
  GET_AGE_WARNING = '/data/visual/monitWarn/ageFeature',

  // 区域地图数据
  Get_Map_Area_Data = '/data/visual/monitWarn/warnMap',
}
export const warningDataApi = () =>
  request.get(API.GET_WARNING_DATA_STATISTICS);
export const positiveWarningApi = (warnArea?: number) =>
  request.get(API.GET_POSITIVE_WARNING + `?warnArea=${warnArea || ''}`);
export const areaWarningApi = () => request.get(API.GET_AREA_WARNING);
export const peopleWarningApi = () => request.get(API.GET_PEOPLE_WARNING);
export const ageWarningApi = () => request.get(API.GET_AGE_WARNING);

export const areaFeatureCharts = () => request.get("/data/visual/monitWarn/areaFeatureCharts");

export const peopleFeatureCharts = () => request.get("/data/visual/monitWarn/peopleFeatureCharts");

export const ageFeatureCharts = () => request.get("/data/visual/monitWarn/ageFeatureCharts");


export const timeFeature = () => request.get("/data/visual/monitWarn/timeFeature");

export const timeFeatureCharts = () => request.get("/data/visual/monitWarn/timeFeatureCharts");




// Get_Map_Area_Data
/**
 * 获取区域地图数据
 */
export const getMapAreaData = (params?: Record<string, any>) =>
  request.get(API.Get_Map_Area_Data, { params });
