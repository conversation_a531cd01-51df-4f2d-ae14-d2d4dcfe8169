import request from '@/utils/request';

enum API {
  // 业务指标
  // 样本采集性别分布
  GET_POPULATION_DISTRIBUTION = '/data/visual/bizIndex/collectSampleVisualization',
  // 耐药性分析
  GET_DRUG_RESISTANCE = '/data/visual/bizIndex/drugResistanceVisualization',
  // 地图数据
  GET_MAP_CONTAINER_DATA = '/data/visual/bizIndex/strainReviewVisualization',
  // 病原谱
  GET_PATHOGEN_SPECTRUM_DATA = '/data/visual/bizIndex/pathogenSpectrumVisualization',
  // 样本采集季节分布
  GET_SEASONAL_DISTRIBUTION = '/data/visual/bizIndex/seasonVisualization',
  // 样本采集年龄分布
  GET_AGE_DISTRIBUTION = '/data/visual/bizIndex/ageVisualization'
}
export const populationDistributionApi = (projectId: string) =>
  request.get(API.GET_POPULATION_DISTRIBUTION + `?projectId=${projectId}`);
export const drugResistanceApi = (projectId: string) =>
  request.get(API.GET_DRUG_RESISTANCE + `?projectId=${projectId}`);
export const mapContainerDataApi = (projectId: string) =>
  request.get(API.GET_MAP_CONTAINER_DATA + `?projectId=${projectId}`);
export const pathogenSpectrumDataApi = (projectId: string) =>
  request.get(API.GET_PATHOGEN_SPECTRUM_DATA + `?projectId=${projectId}`);
export const seasonalDistributionApi = (projectId: string) =>
  request.get(API.GET_SEASONAL_DISTRIBUTION + `?projectId=${projectId}`);
export const ageDistributionApi = (projectId: string) =>
  request.get(API.GET_AGE_DISTRIBUTION + `?projectId=${projectId}`);
