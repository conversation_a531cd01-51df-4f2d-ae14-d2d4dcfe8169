import request from '@/utils/request';

export enum API {
  RECEPT = '/qa/receipt-forms',
  // 根据样本编号查询任务
  GET_TASK_BY_SAMPLE_NUM = '/qa/fillingTasks/getFillingTaskBySampleCode',
  // 确认任务
  CONFIRM_TASK = '/qa/fillingTasks/correctTask',
}

// 查询列表
export const getGradeList = async (params: any) =>
  request.get("/grade/app/page", { params });


//查询评级申请进度状态 

export const getGradeConfig = async () =>
  request.get("/gradeConfig/status");


//查询申请等级 
export const gradeConfig = async () =>
  request.get("/gradeConfig/set");

//查询申请等级 
export const gradeConfig1 = async () =>
  request.get("/gradeConfig/set");


//查询所有申请等级
export const gradeConfigAllSet = async () =>
    request.get("/gradeConfig/allSet");


//查询当前登录者所在机构信息
export const getLabInfo = async () =>
  request.get("/grade/app/getLabInfo");



// 上传附件
export const getIndicesList = async () =>
  request.get("/grade/app/indices");





//删除列表当前行
export const deleteGradeData = async (id:any) =>
  request.get("/grade/app/delete?id=" + id);

//新增
export const saveGrade = async (data: any) =>
  request.post("/grade/app/save", { data });


//复制数据

export const copyGrade = async (id:any) =>
  request.get("/grade/app/copy/" + id);



//修改
export const editGrade = async (data: any) =>
  request.post("/grade/app/edit", { data });


export const getGradeDetail = async (id: any) =>
  request.get("/grade/app/detail?id="+id);








/**
 * 确认任务
 */
export const handleConfirmTask = async (data: Record<string, any>) =>
  request.post(API.CONFIRM_TASK, { data });




//申请受理
export const getGradeReviewList = async (params: any) =>
  request.get("/grade/review/page", { params });



export const getGradeReviewDetail = async (id: any) =>
  request.get("/grade/review/details/"+id);





//申请材料文审列表
export const getGradeDocReviewList = async (params: any) =>
  request.get("/grade/review/docReviewPage", { params });

//申请材料文审详情

//申请材料文审详情

export const getGradedocReviewDetail = async (id: any) =>
  request.get("/grade/review/docReviewLookAt/"+id);





//查询字典


export const getExpertiseExpertArea = async () =>
  request.get("/system/dict/data/type/expertise_expert_area");


export const getGroupList = async (params: any) =>
  request.get("/gradeConfig/expertGroup/groupList", { params });



export const getExpertGroup = async (id: any) =>
  request.get("/gradeConfig/expertGroup/"+id);



export const randomGroup = async (data: any) =>
  request.post("/gradeConfig/randomGroup", { data });


export const reviewProcess = async (data: any) =>
  request.post("/grade/review/process", { data });


export const getTaskAllocationBasicMsg = async (id: any) =>
  request.post("/grade/review/taskAllocation/"+id);




export const getUnAllocationIndex = async (id: any, params: any) =>
  request.get("/grade/review/unAllocationIndex/"+id, { params });


export const getAllocationIndex = async (id: any, params: any) =>
  request.get("/grade/review/allocationIndex/"+id, { params });



export const selectAllocationGroup = async (id: any, params: any) =>
  request.get("/grade/review/selectAllocationGroup/"+id, { params });



export const confirmAllocation = async (data: any) =>
  request.post("/grade/review/confirmAllocation", { data });

// 重新分配
export const reConfirmAllocation = async (data: any) =>
  request.post("/grade/review/reConfirmAllocation", { data });

export const docProcess = async (data: any) =>
  request.post("/grade/review/docProcess", { data });
// 退回给组员
export const backDocTask = async (data: any) =>
  request.post("/grade/review/backDocTask", { data });


export const gradeConfigLevel = async (id: any) =>
  request.get("/gradeConfig/sets/"+id);

//不定级
export const gradeConfigLevelNotSets = async (id: any) =>
    request.get("/gradeConfig/notSets/"+id);

export const fieldReviewPage = async (params: any) =>
  request.get("/grade/review/fieldReviewPage", { params });



//申请材料文审详情

export const getFieldReviewLookAt = async (id: any) =>
  request.get("/grade/review/fieldReviewLookAt/"+id);




export const fieldProcess = async (data: any) =>
  request.post("/grade/review/fieldProcess", { data });



export const downloadReportPdf = async (id: any) =>
  request.get("/grade/review/downloadReportPdf/"+id);



export const downloadReportWord = async (id: any) =>
  request.get("/grade/review/downloadReportWord/"+id);

// 下载专家总结报告
export const downloadExpertsReportWord = async (id: any) =>
  request.get("/grade/review/downloadSummarize/"+id);



export const gradeConfigTip = async () =>
  request.get("/gradeConfig/tip");



export const generateReport = async (id: any) =>
  request.get("/grade/review/generateReport/"+id);





export const uploadSignedReport = async (data: any) =>
  request.post("/grade/review/uploadSignedReport", { data });



export const uploadElectronicReport = async (data: any) =>
  request.post("/grade/review/uploadElectronicReport", { data });

// 分配列表直接绑定专家并分配
export const assignExpertToIndex = async (data: any) =>
  request.post("/grade/review/directAllocation", { data });

// 生成申请书
export const generateApplication = async (data: any) =>
  request.post("/grade/app/generateAppFrom", {data});







