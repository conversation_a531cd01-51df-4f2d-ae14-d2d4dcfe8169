import request from '@/utils/request';

enum API {
  // 事件列表
  GET_EVENT_LIST = '/event/list',
  // 根据ID查询事件详情
  GET_EVENT_DETAIL = '/event/info',
  // 事件上报保存
  POST_EVENT_REPORTING_SAVE = '/event/save',
  // 事件上报提交
  POST_EVENT_REPORTING_COMMIT = '/event/commit',
  // 事件核实提交
  POST_EVENT_VERIFICATION_COMMIT = '/event/confirm',
  // 事件续报提交
  POST_EVENT_FOLLOW_UP_COMMIT = '/event/append',
  // 事件终报提交
  POST_EVENT_FINAL_REPORT_COMMIT = '/event/summary',
  // 删除事件
  DELETE_EVENT = '/event/del',
}

export type TEventListParams = {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: 'asc' | 'desc';
  startDate?: string;
  endDate?: string;
  cityCode?: number;
  areaCode?: number;
  eventCode?: string;
  deptName?: string;
  status: string; // 0:草稿 1:已上报/待核实 2:已核实/待终报 3：已终报
};
/**
 * @description 事件列表
 */
export const eventListApi = (params: TEventListParams) =>
  request.get(API.GET_EVENT_LIST, { params });

/**
 * @description 根据ID查询事件详情
 */
export const eventDetailApi = (params: { eventId: string }) =>
  request.get(API.GET_EVENT_DETAIL, { params });

export type TFiles = {
  url: string;
  fileName: string;
  size?: string;
};
export type TEventReportingAddParams = {
  eventId?: string;
  eventTime: string;
  cityId: number;
  cityName: string;
  areaId: number;
  areaName: string;
  eventAddr: string;
  deptId: number;
  deptName: string;
  committer: string;
  committerId: number;
  mobile: string;
  contactAddr: string;
  eventContent: string;
  files: TFiles[];
};
/**
 * @description 事件上报保存
 */
export const eventReportingSaveApi = (data: TEventReportingAddParams) =>
  request.post(API.POST_EVENT_REPORTING_SAVE, { data });

/**
 * @description 事件上报提交
 */
export const eventReportingCommitApi = (data: TEventReportingAddParams) =>
  request.post(API.POST_EVENT_REPORTING_COMMIT, { data });

export type TEventVerificationCommitParams = {
  eventId: number;
  eventTypeCode: number;
  confirm: string;
  confirmId: number;
  confirmContent: string;
  confirmTime: string;
  files: TFiles[];
};
/**
 * @description 事件核实提交
 */
export const eventVerificationCommitApi = (
  data: TEventVerificationCommitParams
) => request.post(API.POST_EVENT_VERIFICATION_COMMIT, { data });

export type TEventFollowUpCommitParams = {
  eventId: string;
  appender: string;
  appenderId: number;
  appendContent: string;
  appendTime: string;
  files: TFiles[];
};
/**
 * @description 事件续报提交
 */
export const eventFollowUpCommitApi = (data: TEventFollowUpCommitParams[]) =>
  request.post(API.POST_EVENT_FOLLOW_UP_COMMIT, { data });

export type TEventFinalReportCommitParams = {
  eventId: string;
  summary: string;
  summaryId: number;
  summaryContent: string;
  summaryTime: string;
  files: TFiles[];
};
/**
 * @description 事件终报提交
 */
export const eventFinalReportCommitApi = (
  data: TEventFinalReportCommitParams
) => request.post(API.POST_EVENT_FINAL_REPORT_COMMIT, { data });

/**
 * @description 删除事件
 */
export const deleteEventApi = (data: string[]) =>
  request.delete(API.DELETE_EVENT, { data });
