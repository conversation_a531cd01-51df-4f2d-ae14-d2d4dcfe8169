import request from '@/utils/request';

export enum API {
  FILE_UP = '/system/oss/upload',
  FILE_DOWN = '/system/oss/download',
  GET_OSS_BY_ID = '/system/oss/listByIds',
  // 预览文件接口
  PREVIEW_FILE = '/system/oss/preview',
}

//上传地址
export const uploadFiles = async () => {
  return import.meta.env.VITE_URL + API.FILE_UP;
};

//下载地址
export const downloadFile = async (
  id: any,
  fileNames?: string,
  downApi?: string
) => {
  try {
    const _downApi = downApi || API.FILE_DOWN;
    const { data, fileName } = await request.get(_downApi + '/' + id);
    const reader = data.getReader();

    // 手动控制读取流以便回传下载进度
    const stream = new ReadableStream({
      start(controller) {
        // 声明一个 pumpRead 方法读取片段
        // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
        const pumpRead = (): any =>
          reader.read().then(({ done, value }: any) => {
            // done  - 当 stream 传完所有数据时则变成 true
            // value - 数据片段 - done 为 true 时为 undefined
            if (done) {
              // 结束读取 关闭读取流
              controller.close();
              return;
            }
            // 每次推入读取队列 并链式执行
            controller.enqueue(value);
            return pumpRead();
          });
        // 开始读取
        return pumpRead();
      },
    });

    // 最后我们通过 Response 函数接收这个文件流 并转为 blob
    const blob = await new Response(stream).blob();
    const url = window.URL.createObjectURL(blob);
    const aLink = document.createElement('a');
    aLink.style.display = 'none';
    aLink.href = url;
    aLink.setAttribute('download', decodeURI(fileNames ?? fileName));
    document.body.appendChild(aLink);

    // 通过 Promise 来确保下载过程完成
    return new Promise<void>((resolve) => {
      aLink.onclick = () => {
        setTimeout(() => {
          document.body.removeChild(aLink);
          window.URL.revokeObjectURL(url);
          resolve();
        }, 100);
      };
      aLink.click();
      // 如果点击事件没有触发回调，这里作为备选方案
      setTimeout(() => {
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(url);
        resolve();
      }, 1000);
    });
  } catch (error) {
    console.error('下载文件出错:', error);
    throw error;
  }
};

//下载地址2
export const getFileData = async (id: any) => {
  const { data } = await request.get(API.FILE_DOWN + '/' + id);
  const reader = data.getReader();
  // 手动控制读取流以便回传下载进度
  const stream = new ReadableStream({
    start(controller) {
      // 声明一个 pumpRead 方法读取片段
      // 因为reader.read() 方法是 Promise 异步的，所以这里是在进行链式调用
      const pumpRead = (): any =>
        reader.read().then(({ done, value }: any) => {
          // done  - 当 stream 传完所有数据时则变成 true
          // value - 数据片段 - done 为 true 时为 undefined
          if (done) {
            // 结束读取 关闭读取流
            controller.close();
            return;
          }
          // 每次推入读取队列 并链式执行
          controller.enqueue(value);
          return pumpRead();
        });
      // 开始读取
      return pumpRead();
    },
  });
  // 最后我们通过 Response 函数接收这个文件流 并转为 blob
  const blob = await new Response(stream).blob();
  return window.URL.createObjectURL(blob);
};

// 获取文件oss对象
export const getFileOssObjApi = (id: string) =>
  request.get(API.GET_OSS_BY_ID + `/${id}`);

/**
 *  获取文件流
 */
export const getFileStream = (id: string) =>
  request.get(API.FILE_DOWN + `/${id}`);

/**
 *  为预览文件获取文件流
 */
export const getFileStreamForPreview = (id: string) =>
  request.get(API.PREVIEW_FILE + `/${id}`);
