import request from '@/utils/request';

enum API {
  // 整体情况
  Get_Overall_Overview_Data = '/data/visual/assessment/total',
  // 机构分析
  Get_Institutional_Analysis_Data = '/data/visual/assessment/orgAnalyze',
  // 任务分析
  Get_Task_Analysis_Data = '/data/visual/assessment/taskAnalyze',
  // 考核类型分析
  Get_Assessment_Type_Analysis_Data = '/data/visual/assessment/assTypeAnalyze',
  // 获取考核类型分析明细
  Get_Assessment_Type_Analysis_Detail_Data = '/data/visual/assessment/assTypeAnalyzeDetail',
}

/**
 * @description 获取整体情况数据
 */
export const getOverallOverviewData = (params: Record<string, any>) =>
  request.get(API.Get_Overall_Overview_Data, { params });

/**
 * @description 获取机构分析数据
 */
export const getInstitutionalAnalysisData = (params: Record<string, any>) =>
  request.get(API.Get_Institutional_Analysis_Data, { params });

/**
 * @description 获取任务分析数据
 */
export const getTaskAnalysisData = (params: Record<string, any>) =>
  request.get(API.Get_Task_Analysis_Data, { params });

/**
 * @description 获取考核类型分析数据
 */
export const getAssessmentTypeAnalysisData = (params: Record<string, any>) =>
  request.get(API.Get_Assessment_Type_Analysis_Data, { params });

/**
 * @description 获取考核类型分析明细
 */
export const getAssessmentTypeAnalysisDetailData = (
  params: Record<string, any>
) => request.get(API.Get_Assessment_Type_Analysis_Detail_Data, { params });
