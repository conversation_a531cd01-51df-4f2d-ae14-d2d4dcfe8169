import request from '@/utils/request';

enum API {
  // ---- 风险感知指标体系 ----
  GET_RISK_PERCEPTION_INDEX_LIST = '/data/riskIndex/listByStatus',
  PUT_UPDATE_RISK_PERCEPTION_INDEX_STATUS = '/qc/riskIndex/updateIndexStatus',
  GET_RISK_PERCEPTION_INDEX_ALL = '/data/riskIndex/selectorList',
  // ---- 工作台 ----
  // 疾控工作台
  GET_STATISTIC_OVERVIEW = '/data/risk/analysis/workbench/riskProfile',
  GET_EVALUATION_OVERVIEW = '/data/risk/analysis/workbench/riskAssessmentInspectionAgency',
  GET_RISK_CLUE_BOX = '/data/risk/analysis/workbench/riskClueBox',
  GET_EVALUATION_RANKING = '/data/risk/analysis/workbench/evaluationRanking',
  GET_MONTH_RISK_CLUES = '/data/risk/analysis/workbench/cluesStatistics',
  GET_PERCEIVED_SITUATION = '/data/risk/analysis/workbench/perceptionRiskIndex',
  // 机构工作台
  GET_ORG_STATISTIC_OVERVIEW = '/data/risk/analysis/workbench/orgRiskProfile',
  GET_ORG_RISK_CLUE_BOX = '/data/risk/analysis/workbench/orgRiskClueBox',
  GET_ORG_RISK_CLUES = '/data/risk/analysis/workbench/orgClueSituation',
  GET_ORG_RISK_ASSESSMENT_TREND = '/data/risk/analysis/workbench/orgEvaluateTrend',
  GET_ORG_MONTH_RISK_CLUES = '/data/risk/analysis/workbench/orgCluesStatistics',
  GET_ORG_INSPECTION_LOT_QUANTITY = '/data/risk/analysis/workbench/orgInspectNum',
  // ---- 机构风险评价
  GET_ORG_LIST = '/data/orgRiskEvaluation/orgSelectorList',
  // ---- 风险管理 ----
  // 风险线索
  GET_RISK_CLUE_LIST = '/data/riskClue/list',
  GET_RISK_CLUE_DETAIL = '/data/riskClue/detail',
  GET_RISK_CLUE_DETAIL_QUESTION_LIST = '/data/clue/problem/list',
  // 风险自查
  GET_RISK_SELF_EXAMINATION_LIST = '/data/clue/selfCheck/listSelfCheckPage',
  POST_ADD_RISK_SELF_EXAMINATION = '/qc/clue/selfCheck/submitCheckResult',
  PUT_UPDATE_RISK_SELF_EXAMINATION = '/qc/clue/selfCheck/reSubmitCheckResult',
  GET_RISK_SELF_EXAMINATION_DETAIL = '/data/clue/selfCheck/getSelfCheckByClueId',
  GET_RISK_SELF_EXAMINATION_TOTAL = '/data/clue/selfCheck/getSelfCheckShowCount',
  // 自查结果审核
  GET_RESULTS_AUDIT_LIST = '/data/clue/selfCheck/listCheckAuditPage',
  POST_RESULTS_AUDIT_SUBMIT = '/qc/clue/selfCheck/submitAuditResult',
  GET_RESULTS_AUDIT_TOTAL = '/data/clue/selfCheck/getSelfCheckAuditCount',
}

type TCommonPage = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
};

// ---- 风险感知指标体系 ----
/**
 * @description 获取风险感知指标体系列表
 */
export const riskPerceptionIndexListApi = (
  params: TCommonPage & {
    indexName?: string;
    clueType?: string;
    status?: number; // 指标状态，0启用，1停用
  }
) => request.get(API.GET_RISK_PERCEPTION_INDEX_LIST, { params });
/**
 * @description 修改风险感知指标体系状态
 */
export const updateRiskPerceptionIndexStatusApi = (data: {
  id: string;
  status: number;
}) => request.put(API.PUT_UPDATE_RISK_PERCEPTION_INDEX_STATUS, { data });
/**
 * @description 风险感知指标所有数据
 */
export const riskPerceptionIndexAllApi = () =>
  request.get(API.GET_RISK_PERCEPTION_INDEX_ALL);

// ---- 工作台
// 疾控工作台
/**
 * @description 获取统计总览
 */
export const statisticOverviewApi = () =>
  request.get(API.GET_STATISTIC_OVERVIEW);
/**
 * @description 获取检验机构风险评价情况
 */
export const evaluationOverviewApi = () =>
  request.get(API.GET_EVALUATION_OVERVIEW);
/**
 * @description 获取风险线索盒
 */
export const riskClueBoxApi = () => request.get(API.GET_RISK_CLUE_BOX);
/**
 * @description 获取6月检验机构评价排行
 */
export const evaluationRankingApi = () =>
  request.get(API.GET_EVALUATION_RANKING);
/**
 * @description 获取各月风险线索情况
 */
export const monthRiskCluesApi = () => request.get(API.GET_MONTH_RISK_CLUES);
/**
 * @description 获取风险指标感知情况
 */
export const perceivedSituationApi = () =>
  request.get(API.GET_PERCEIVED_SITUATION);

// 机构工作台
/**
 * @description 获取机构统计总览
 */
export const orgStatisticOverview = () =>
  request.get(API.GET_ORG_STATISTIC_OVERVIEW);
/**
 * @description 获取机构风险线索盒
 */
export const orgRiskClueBoxApi = () => request.get(API.GET_ORG_RISK_CLUE_BOX);
/**
 * @description 获取机构风险线索情况
 */
export const orgRiskCluesApi = () => request.get(API.GET_ORG_RISK_CLUES);
/**
 * @description 获取机构风险评价趋势
 */
export const orgRiskAssessmentTrendApi = () =>
  request.get(API.GET_ORG_RISK_ASSESSMENT_TREND);
/**
 * @description 获取机构各月风险线索情况
 */
export const orgMonthRiskCluesApi = () =>
  request.get(API.GET_ORG_MONTH_RISK_CLUES);
/**
 * @description 获取机构检验批次数量
 */
export const orgInspectionLotQuantityApi = () =>
  request.get(API.GET_ORG_INSPECTION_LOT_QUANTITY);

// ---- 机构风险评价
/**
 * @description 检验机构列表
 */
export const orgListApi = () => request.get(API.GET_ORG_LIST);

// ---- 风险管理
// 风险线索
export type TRiskClueListParams = TCommonPage & {
  updateBeginTime?: string;
  updateEndTime?: string;
  orgId?: number;
  indexId?: number;
  clueType?: string;
};
/**
 * @description 风险线索列表
 */
export const riskClueListApi = (params: TRiskClueListParams) =>
  request.get(API.GET_RISK_CLUE_LIST, { params });
/**
 * @description 风险线索详情
 */
export const riskClueDetailApi = (id: string) =>
  request.get(API.GET_RISK_CLUE_DETAIL + `/${id}?id=${id}`);
/**
 * @description 详情问题列表
 */
export const riskClueDetailQuestionListApi = (
  params: TCommonPage & {
    id: string;
  }
) => request.get(API.GET_RISK_CLUE_DETAIL_QUESTION_LIST, { params });

// 风险自查
export type TRiskSelfExaminationListParams = TCommonPage & {
  updateBeginTime?: string;
  updateEndTime?: string;
  indexId?: string;
  status: string; // 审核状态
};
/**
 * @description 风险自查列表
 */
export const riskSelfExaminationListApi = (
  params: TRiskSelfExaminationListParams
) => request.get(API.GET_RISK_SELF_EXAMINATION_LIST, { params });
/**
 * @description 新增风险自查
 */
export const addRiskSelfExaminationApi = (data: {}) =>
  request.post(API.POST_ADD_RISK_SELF_EXAMINATION, { data });
/**
 * @description 修改风险自查
 */
export const updateRiskSelfExaminationApi = (data: {}) =>
  request.put(API.PUT_UPDATE_RISK_SELF_EXAMINATION, { data });
/**
 * @description 风险自查详情
 */
export const riskSelfExaminationDetailApi = (id: string) =>
  request.get(API.GET_RISK_SELF_EXAMINATION_DETAIL + `?clueId=${id}`);
/**
 * @description 风险自查列表各个状态total
 */
export const riskSelfExaminationTotalApi = (params: {
  updateBeginTime?: string;
  updateEndTime?: string;
  indexId?: string;
}) => request.get(API.GET_RISK_SELF_EXAMINATION_TOTAL, { params });

// 自查结果审核
export type TResultsAuditListParams = TCommonPage & {
  updateBeginTime?: string;
  updateEndTime?: string;
  indexId?: string;
  orgName?: string;
  auditStatus: string;
};
/**
 * @description 自查结果审核列表
 */
export const resultsAuditListApi = (params: TResultsAuditListParams) =>
  request.get(API.GET_RESULTS_AUDIT_LIST, { params });
/**
 * @description 自查结果审核
 */
export const resultsAuditSubmitApi = (data: {
  id: number;
  clueId: string;
  auditDate?: string;
  auditUser?: string;
  auditResult: number; // 审核结果，2通过，3不通过
  auditSuggest?: string;
}) => request.post(API.POST_RESULTS_AUDIT_SUBMIT, { data });
/**
 * @description 自查结果审核列表各个状态total
 */
export const resultsAuditTotalApi = (params: {
  updateBeginTime?: string;
  updateEndTime?: string;
  indexId?: string;
  orgName?: string;
}) => request.get(API.GET_RESULTS_AUDIT_TOTAL, { params });


// 获取风险模型信息
export const riskModelList = () =>
  request.get("/riskModel/list");



export const editIndex = (data: {}) =>
  request.post("/riskModel/editIndex", { data });




export const riskModelEdit = (data: {}) =>
  request.post("/riskModel/edit", { data });
