import request from '@/utils/request';

export enum API {
  // 菜单列表
  GET_MENU_LIST = '/system/menu/list',
  // 菜单详情
  GET_MENU_DETAIL = '/system/menu',
  // 新增菜单
  POST_ADD_MENU = '/system/menu',
  // 删除菜单
  GET_DELETE_MENU = '/system/menu',
  // 修改菜单
  POST_EDIT_MENU = '/system/menu',
  // 菜单列表（不分页）
  GET_MENU_SELECT_LIST = '/system/menu/treeselect',
}

/**
 * @interface 获取菜单列表参数
 */
export interface IGetMenuListParam {
  pageSize?: number;
  pageNum?: number;
}
/**
 * @description 获取菜单管理列表
 */
export const queryMenuList = async (params: IGetMenuListParam) =>
  request.get(API.GET_MENU_LIST, { params });

/**
 * @description 获取菜单详情
 */
export const queryMenuDetail = async (id: number) =>
  request.get(API.GET_MENU_DETAIL + '/' + id);

/**
 * @interface 新增/编辑菜单参数
 */
export interface ISaveMenuParam {
  menuName: string;
  parentId: number;
  orderNum: number;
  path: string;
  menuType: string;
  status: number;
  perms?: string;
  menuId?: number;
}
/**
 * @description 新增菜单
 */
export const queryAddMenu = async (data: ISaveMenuParam) =>
  request.post(API.POST_ADD_MENU, { data });

/**
 * @description 删除菜单
 */
export const queryDeleteMenu = async (params: { menuId: number }) =>
  request.delete(API.GET_DELETE_MENU + '/' + params.menuId);

/**
 * @description 修改菜单
 */
export const queryEditMenu = async (data: ISaveMenuParam) =>
  request.put(API.POST_EDIT_MENU, { data });

/**
 * @description 获取菜单列表（不分页）
 */
export const queryMenuSelectList = async () =>
  request.get(API.GET_MENU_SELECT_LIST);
