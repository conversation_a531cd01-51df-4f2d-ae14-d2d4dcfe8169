import request from '@/utils/request';

enum API {
  // ------ 基础数据配置 ------
  // 实验室名录
  GET_LABORATORY_LIST = '/gradeConfig/labPage',
  POST_ADD_LABORATORY = '/gradeConfig/addLab',
  GET_LABORATORY_DETAIL = '/gradeConfig/labInfo',
  POST_UPDATE_LABORATORY = '/gradeConfig/freshLab',
  DELETE_LABORATORY = '/gradeConfig/removeLab',
  // 专家库管理
  GET_EXPERT_LIST = '/gradeConfig/expert/lib',
  DELETE_EXPERT = '/gradeConfig/expert/delete',
  BATCH_DELETE_EXPERT = '/gradeConfig/expert/batchDelete',
  EXPORT_EXPERT = '/gradeConfig/expert/export',
  // 专家审核
  GET_EXPERT_REVIEW_LIST = '/gradeConfig/expert/review/list',
  APPROVE_EXPERT = '/gradeConfig/expert/review/approve',
  REJECT_EXPERT = '/gradeConfig/expert/review/reject',
  BATCH_APPROVE_EXPERT = '/gradeConfig/expert/review/batchApprove',
  BATCH_REJECT_EXPERT = '/gradeConfig/expert/review/batchReject',
  EXPORT_EXPERT_REVIEW = '/gradeConfig/expert/review/export',
  // 评审小组
  GET_REVIEW_PANEL_LIST = '/gradeConfig/panel/list',
  GET_REVIEW_PANEL_DETAIL = '/gradeConfig/panel/detail',
  ADD_REVIEW_PANEL = '/gradeConfig/panel/add',
  UPDATE_REVIEW_PANEL = '/gradeConfig/panel/update',
  DELETE_REVIEW_PANEL = '/gradeConfig/panel/delete',
  BATCH_DELETE_REVIEW_PANEL = '/gradeConfig/panel/batchDelete',
  EXPORT_REVIEW_PANEL = '/gradeConfig/panel/export',
  // 评级指标
  GET_RATING_INDEX_LIST = '/gradeConfig/idxPage',
  // 评级指标列表数据,
  Get_Rating_Index_List = '/gradeTable/listPage',
  // 删除评审表
  Delete_Rating_Index = '/gradeTable/delete',
  // 复制新增数据
  Copy_Add_New_Record = '/gradeTable/copyTable',
  // 获取评审表详情
  Get_Rating_Details = '/gradeTable/detail',
  // 获取评审表编号(新建)
  Get_Rating_Number_Code = '/gradeTable/getCode',
  // 新增或编辑时动态获取当前评审表的总分值
  Get_Total_Score = '/gradeTable/computeTotalScore',
  // 创建评审表，不包含指标配置
  Create_New_Rating_Table = '/gradeTable/saveTableNew',
  // 下载导入模板
  Download_Import_Template = '/gradeTable/downloadModel',
  // 导出评审表
  Export_Rating_Table = '/gradeTable/exportTable',
  // 导入评审表
  Import_Rating_Table = '/gradeTable/importTable',
  // 批量删除评审要点
  Delete_Idx_Batch = '/gradeTable/deleteIdxBatch',

  // 新增条件、类别、指标和要点
  Create_New_Configuate = '/gradeTable/saveIndex',
  // 编辑条件、类别、指标和要点
  Edit_Configuate = '/gradeTable/editIndex',
  // 删除条件、类别、指标和要点
  Delete_Configuate = '/gradeTable/deleteIndex',

  // 获取树结构数据
  Get_Metric_Config_Data = '/gradeTable/flushTree',
  // 获取评审要点
  Get_Rating_Point_By_Id = '/gradeTable/listIndex',

  POST_ADD_RATING_INDEX = '/gradeConfig/addIdx',
  POST_UPDATE_RATING_INDEX = '/gradeConfig/freshIdx',
  POST_UPDATE_RATING_INDEX_STATUS = '/gradeConfig/idxState',
  // 等级划分规则
  GET_GRADING_RULE_LIST = '/gradeConfig/grades',
  POST_ADD_GRADING_RULE = '/gradeConfig/addGrade',
  POST_UPDATE_GRADING_RULE = '/gradeConfig/freshGrade',

  // ---- 评级管理 ----
  // 评审申请
  Get_Review_Application_List = '/grade/lab/application/listPage',
  // 新增评审申请
  Post_New_Review_Application = '/grade/lab/application/save',
  // 获取当前登录人所在的实验室信息
  Get_Lib_Info_By_User = '/grade/lab/application/getLabInfo',
  // 获取评审详情
  Get_Review_Application_Details = '/grade/lab/application/detail',
  // 编辑评审申请
  Edit_Review_Application = '/grade/lab/application/edit',
  // 删除评审申请
  Delete_Review_Application = '/grade/lab/application/delete',
  // 修改评审表状态, status 0 - 草稿、 1 - 启用
  Modified_Rating_Table_Status = '/gradeTable/editTableNew',
  // 获取不同状态评级申请的数量
  Get_Review_Count = '/grade/lab/application/countStatusNum',

  // 实验室管理待审核列表
  Get_Pending_Review_List = '/grade/lab/application/listAwaitingAudit',
  // 实验室管理已审核列表
  Get_Reviewed_List = '/grade/lab/application/listAudited',
  // 统计待审核、已审核列表数量
  Get_Count_Statistics = '/grade/lab/application/countNum',
  // 获取评审详情
  Get_Review_Detaiils = '/grade/lab/application/audit/detail',
  // 审核操作
  Handle_Operation_Review = '/grade/lab/application/audit',
  // 获取评审表列表数据
  Get_Rating_Table_List = '/gradeTable/listTable',
  // 获取实验室管理待审核、已审核数量
  Get_Lib_Management_Count = '/grade/lab/application/countNum',

  // 实验室评定
  GET_LABORATORY_EVALUATION_LIST = '/gradeScore/scorePage',
  POST_ADJUST_SCORE = '/gradeScore/adjust',
  POST_RATE_CONFIRM = '/gradeScore/confirm',

  // 新的实验室评定接口
  // 获取实验室待评定列表
  Get_Lib_Evaluation_List = '/grade/lab/assess/listAwaiting',
  // 获取实验室已评定列表
  Get_Lib_Evaluationed_List = '/grade/lab/assess/listAssessed',
  // 获取待评定、已评定列表数据
  Get_Evaluation_Count = '/grade/lab/assess/countAssess',
  // 查询评定详情
  Get_Lib_Evaluation_Details = '/grade/lab/assess/detail',
  // 提交评定结果
  Post_Lib_Evaluation_Result = '/grade/lab/assess/assess',
  // 动态计算评定结果
  Dynamic_Calc_Assess_Result = '/grade/lab/assess/computeAssessResult',

  // 评级结果统计
  Get_Rage_Result_Statistic = '/grade/lab/assess/ana',
}

type TCommonPage = {
  pageNum: number;
  pageSize: number;
  orderByColumn?: string;
  isAsc?: string;
};

/**
 * @description 查询实验室名录列表
 */
export const laboratoryListApi = (
  params: TCommonPage & {
    labName?: string;
    labType?: number;
    manager?: string;
    isGrade?: number;
  }
) => request.get(API.GET_LABORATORY_LIST, { params });
type TLaboratoryDetail = {
  id?: number;
  labName: string;
  labType: number;
  manager: string;
  cityId: number;
  cityName: string;
  areaId: number;
  areaName: string;
  address: string;
  isGrade: number;
};
/**
 * @description 新增实验室名录
 */
export const addLaboratoryApi = (data: TLaboratoryDetail) =>
  request.post(API.POST_ADD_LABORATORY, { data });
/**
 * @description 查询实验室名录详情
 */
export const laboratoryDetailApi = (id: string) =>
  request.get(API.GET_LABORATORY_DETAIL + `?id=${id}`);
/**
 * @description 更新实验室名录
 */
export const updateLaboratoryApi = (data: TLaboratoryDetail) =>
  request.post(API.POST_UPDATE_LABORATORY, { data });
/**
 * @description 删除实验室名录
 */
export const deleteLaboratoryApi = (id: string) =>
  request.delete(API.DELETE_LABORATORY + `?id=${id}`);

/**
 * @description 查询评级指标列表
 */
export const ratingIndexListApi = (
  params: TCommonPage & {
    indexCode?: string;
    indexName?: string;
    description?: string;
    status?: number; // 0禁用 1启用
  }
) => request.get(API.GET_RATING_INDEX_LIST, { params });
export interface TRatingIndexDetail {
  id?: number;
  indexCode: string;
  indexName: string;
  description: string;
  score: number;
  weight: number;
  status: number;
}
/**
 * @description 新增评价指标
 */
export const addRatingIndexApi = (data: TRatingIndexDetail) =>
  request.post(API.POST_ADD_RATING_INDEX, { data });
/**
 * @description 更新评价指标
 */
export const updateRatingIndexApi = (data: TRatingIndexDetail) =>
  request.post(API.POST_UPDATE_RATING_INDEX, { data });
/**
 * @description 更新评价指标状态
 */
export const updateRatingIndexStatusApi = (params: {
  id: string;
  state: number;
}) => request.post(API.POST_UPDATE_RATING_INDEX_STATUS, { params });

/**
 * @description 查询等级划分规则列表
 */
export const gradingRuleListApi = () => request.get(API.GET_GRADING_RULE_LIST);
export interface TGradingRuleDetail {
  id?: number;
  grade: string;
  topScore: number;
  floorScore: number;
}
/**
 * @description 新增等级划分规则
 */
export const addGradingRuleApi = (data: TGradingRuleDetail) =>
  request.post(API.POST_ADD_GRADING_RULE, { data });
/**
 * @description 更新等级划分规则
 */
export const updateGradingRuleApi = (data: TGradingRuleDetail) =>
  request.post(API.POST_UPDATE_GRADING_RULE, { data });

/**
 * @description 实验室评级列表
 */
export const laboratoryEvaluationListApi = (
  params: TCommonPage & {
    gradeYear?: number;
    labName?: string;
    labType?: number;
    grade?: string;
    status: string; // 确认状态 1：已确认 0：待确认
  }
) => request.get(API.GET_LABORATORY_EVALUATION_LIST, { params });
/**
 * @description 调整得分
 */
export const adjustScoreApi = (data: { id: string; adjustScore: number }) =>
  request.post(API.POST_ADJUST_SCORE, { data });
/**
 * @description 确认评级
 */
export const rateConfirmApi = (params: { id: string }) =>
  request.post(API.POST_RATE_CONFIRM, { params });
/**
 * @description 实验室评级结果统计
 */
export const rateResultStatisticApi = (params: {
  cityId?: string;
  areaId?: string;
  gradeYear?: number;
}) => request.get(API.Get_Rage_Result_Statistic, { params });

/**
 * 获取评级指标列表数据
 */
export const getRatingIndexListNew = (params: Record<string, any>) =>
  request.get(API.Get_Rating_Index_List, { params });

// Delete_Rating_Index
export const deleteRatingIndex = (
  params: Record<string, any> & { id: string }
) => request.get(API.Delete_Rating_Index, { params });

/**
 * 复制新增记录
 * @param params
 * @returns
 */
export const copyAddNewRecord = (params: Record<string, any>) =>
  request.get(API.Copy_Add_New_Record, { params });

export const getRatingDetailsById = (params: Record<string, any>) =>
  request.get(API.Get_Rating_Details, { params });

/**
 * 新建评审表时获取新的标号
 * @param params
 * @returns
 */
export const getRatingNumberCode = (params: Record<string, any>) =>
  request.get(API.Get_Rating_Number_Code, { params });

/**
 * 新增&编辑时获取当前评审表的动态的总分值
 * @param data
 * @returns
 */
export const getTotalScore = (params: Record<string, any>) =>
  request.get(API.Get_Total_Score, { params });

/**
 * 创建评审表
 * @param params
 * @returns
 */
export const createNewRatingTable = (data: Record<string, any>) =>
  request.post(API.Create_New_Rating_Table, { data });

//
/**
 * 新增条件、类型、指标和要点
 * @param params
 * @returns
 */
export const createNewConfiguate = (data: Record<string, any>) =>
  request.post(API.Create_New_Configuate, { data });

/**
 * b编辑条件、类型、指标和要点
 * @param data
 * @returns
 */
export const editConfiguate = (data: Record<string, any>) =>
  request.post(API.Edit_Configuate, { data });

/**
 * 删除条件、类型、指标和要点
 * @param data
 * @returns
 */
export const deleteConfiguate = (params: Record<string, any>) =>
  request.get(API.Delete_Configuate, { params });

/**
 * 获取指标配置树的数据 (使用新接口地址)
 * @param params 包含 tableId 参数
 * @returns
 */
export const getMetricConfigData = (params: Record<string, any>) =>
  request.get(API.Get_Metric_Config_Data, { params });

/**
 * 获取指标配置树的数据 (原有接口的别名，保持兼容性)
 * @param params 包含 tableId 参数  
 * @returns
 */
export const getFlushTree = (params: Record<string, any>) =>
  request.get(`/gradeTable/flushTree`, { params });

/**
 * 获取评审要点 (旧接口，保持兼容性)
 * @param params 包含 tableId, pageSize, pageNum, id(可选-选中分类时传入), lab, point, rule(搜索参数)
 * @returns
 */
export const getRatingPointById = (params: Record<string, any>) =>
  request.get(API.Get_Rating_Point_By_Id, { params });

/**
 * 获取指标列表数据 (新接口地址)
 * 支持两种调用模式：
 * 1. 获取所有指标：传入 tableId + pageSize + pageNum (+ 搜索参数)
 * 2. 获取分类下指标：传入 id(分类ID) + pageSize + pageNum (+ 搜索参数)
 * @param params 
 *   - tableId: 评审表ID (获取所有指标时使用)
 *   - id: 分类ID (获取特定分类下指标时使用)  
 *   - pageSize: 每页数量
 *   - pageNum: 页码
 *   - lab: 指标名称搜索 (可选)
 *   - point: 评审要点搜索 (可选)
 *   - rule: 评审规则搜索 (可选)
 * @returns
 */
export const getListIndex = (params: Record<string, any>) =>
  request.get(`/gradeTable/listIndex`, { params });

/**
 * 获取评审申请列表数据
 */
export const getReviewApplicationList = (params: Record<string, any>) =>
  request.get(API.Get_Review_Application_List, { params });

/**
 * 新增评审申请
 */
export const postNewReviewApplication = (data: Record<string, any>) =>
  request.post(API.Post_New_Review_Application, { data });

/**
 * 获取当前登录人所在实验室信息
 */
export const getLibInfoByUser = (params: Record<string, any>) =>
  request.get(API.Get_Lib_Info_By_User, { params });

/**
 * 根据ID获取评审申请详情
 * @param data
 * @returns
 */
export const getReviewApplicationDetails = (
  params: Record<string, any> & { id: string }
) => request.get(API.Get_Review_Application_Details, { params });

/**
 * 编辑评审申请
 */
export const editReviewApplication = (data: Record<string, any>) =>
  request.post(API.Edit_Review_Application, { data });

/**
 * 删除评审申请
 */

export const deleteReviewApplication = (
  params: Record<string, any> & { id: string }
) => request.get(API.Delete_Review_Application, { params });

/**
 * 修改评审表状态
 * @param data
 * @returns
 */
export const modifiedRatingTableStatus = (data: Record<string, any>) =>
  request.post(API.Modified_Rating_Table_Status, { data });

/**
 * 获取不同状态评级申请的数量
 */
export const getReviewCount = (params: Record<string, any>) =>
  request.get(API.Get_Review_Count, { params });

/**
 * 获取实验室管理待审核列表数据
 */
export const getPendingReviewList = (params: Record<string, any>) =>
  request.get(API.Get_Pending_Review_List, { params });

/**
 * 获取实验室管理已审核列表数据
 */
export const getReviewedList = (params: Record<string, any>) =>
  request.get(API.Get_Reviewed_List, { params });

/**
 * 统计待审核、已审核数量
 */
export const getCountStatistics = (params: Record<string, any>) =>
  request.get(API.Get_Count_Statistics, { params });

/**
 * 获取评审详情
 */
export const getReviewDetaiils = (
  params: Record<string, any> & { id: string }
) => request.get(API.Get_Review_Detaiils, { params });

/**
 * 审核操作
 */
export const handleOperationReview = (data: Record<string, any>) =>
  request.post(API.Handle_Operation_Review, { data });

/**
 * 获取评审表列表数据
 */
export const getRatingTableList = (params: Record<string, any>) =>
  request.get(API.Get_Rating_Table_List, { params });

/**
 * 获取实验室管理待审核、已审核数量
 */
export const getLibManagementCount = (params: Record<string, any>) =>
  request.get(API.Get_Lib_Management_Count, { params });

/**
 * 获取实验室待评定列表接口数据
 */
export const getLibEvaluationList = (params: Record<string, any>) =>
  request.get(API.Get_Lib_Evaluation_List, { params });

/**
 * 获取实验室已评定列表接口数据
 */
export const getLibEvaluationedList = (params: Record<string, any>) =>
  request.get(API.Get_Lib_Evaluationed_List, { params });

/**
 * 获取待评定、y已评定列表数量
 */
export const getEvaluationCount = (params: Record<string, any>) =>
  request.get(API.Get_Evaluation_Count, { params });

/**
 * 获取实验室评定详情
 */
export const getLibEvaluationDetails = (params: Record<string, any>) =>
  request.get(API.Get_Lib_Evaluation_Details, { params });

/**
 * 提交评定结果
 */
export const postLibEvaluationResult = (data: Record<string, any>) =>
  request.post(API.Post_Lib_Evaluation_Result, { data });

/**
 * 动态计算评定结果
 */
export const dynamicCalcAssessResult = (data: Record<string, any>) =>
  request.post(API.Dynamic_Calc_Assess_Result, { data });

/**
 * 实验室评级结果统计
 */
export const getRageResultStatistic = (params: Record<string, any>) =>
  request.get(API.Get_Rage_Result_Statistic, { params });

/**
 * 下载评审要点导入模板
 * @returns 返回二进制流
 */
export const downloadImportTemplate = () =>
  request.get(API.Download_Import_Template, {
    responseType: 'blob',
  });

/**
 * 导出评审表
 * @param id 评审表ID
 * @returns 返回二进制流
 */
export const exportRatingTable = (id: string) =>
  request.post(`${API.Export_Rating_Table}?id=${id}`, {
    responseType: 'blob',
  });

/**
 * 导入评审表
 * @param file 导入的文件
 * @param id 评审表ID
 * @returns 请求结果
 */
export const importRatingTable = (file: File, id: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('id', id);

  return request.post(API.Import_Rating_Table, {
    data: formData,
    requestType: 'form',
  });
};

/**
 * 批量删除评审要点
 * @param ids 要删除的评审要点ID数组
 * @returns 请求结果
 */
export const deleteIdxBatch = (ids: string[]) =>
  request.post(API.Delete_Idx_Batch, {
    data: ids,
  });

/**
 * @description 获取专家库列表
 */
export const getExpertListApi = (params: {
  pageNum?: number;
  pageSize?: number;
  nickName?: string; // 专家姓名
  deptName?: string; // 机构名称
  phonenumber?: string; // 联系方式
}) => request.get(API.GET_EXPERT_LIST, { params });

/**
 * @description 删除专家
 */
export const deleteExpertApi = (id: string) =>
  request.delete(`${API.DELETE_EXPERT}?id=${id}`);

/**
 * @description 批量删除专家
 */
export const batchDeleteExpertApi = (ids: string[]) =>
  request.post(API.BATCH_DELETE_EXPERT, { data: { ids } });

/**
 * @description 导出专家数据
 */
export const exportExpertDataApi = () =>
  request.get(API.EXPORT_EXPERT, { responseType: 'blob' });

/**
 * @description 获取专家审核列表
 */
export const getExpertReviewListApi = (params: {
  pageNum?: number;
  pageSize?: number;
  status?: string; // 0: 待审核, 1: 已审核
  nickName?: string; // 专家姓名
  deptName?: string; // 机构名称
  phonenumber?: string; // 联系方式
  registerDateStart?: string; // 注册日期起始
  registerDateEnd?: string; // 注册日期结束
}) => request.get(API.GET_EXPERT_REVIEW_LIST, { params });

/**
 * @description 审核通过专家
 */
export const approveExpertApi = (params: { id: string; remark: string }) =>
  request.post(API.APPROVE_EXPERT, { data: params });

/**
 * @description 审核驳回专家
 */
export const rejectExpertApi = (params: { id: string; remark: string }) =>
  request.post(API.REJECT_EXPERT, { data: params });

/**
 * @description 批量审核通过专家
 */
export const batchApproveExpertApi = (params: {
  ids: string[];
  remark: string;
}) => request.post(API.BATCH_APPROVE_EXPERT, { data: params });

/**
 * @description 批量审核驳回专家
 */
export const batchRejectExpertApi = (params: {
  ids: string[];
  remark: string;
}) => request.post(API.BATCH_REJECT_EXPERT, { data: params });

/**
 * @description 导出专家审核数据
 */
export const exportExpertReviewDataApi = () =>
  request.get(API.EXPORT_EXPERT_REVIEW, { responseType: 'blob' });

/**
 * @description 获取评审小组列表
 */
export const getReviewPanelListApi = (params: {
  pageNum?: number;
  pageSize?: number;
  name?: string; // 小组名称
  leader?: string; // 组长
  member?: string; // 成员
}) => request.get(API.GET_REVIEW_PANEL_LIST, { params });

/**
 * @description 获取评审小组详情
 */
export const getReviewPanelDetailApi = (id: string) =>
  request.get(`${API.GET_REVIEW_PANEL_DETAIL}?id=${id}`);

/**
 * @description 新增评审小组
 */
export const addReviewPanelApi = (data: any) =>
  request.post(API.ADD_REVIEW_PANEL, { data });

/**
 * @description 更新评审小组
 */
export const updateReviewPanelApi = (data: any) =>
  request.post(API.UPDATE_REVIEW_PANEL, { data });

/**
 * @description 删除评审小组
 */
export const deleteReviewPanelApi = (id: string) =>
  request.delete(`${API.DELETE_REVIEW_PANEL}?id=${id}`);

/**
 * @description 批量删除评审小组
 */
export const batchDeleteReviewPanelApi = (ids: string[]) =>
  request.post(API.BATCH_DELETE_REVIEW_PANEL, { data: { ids } });

/**
 * @description 导出评审小组数据
 */
export const exportReviewPanelDataApi = () =>
  request.get(API.EXPORT_REVIEW_PANEL, { responseType: 'blob' });
