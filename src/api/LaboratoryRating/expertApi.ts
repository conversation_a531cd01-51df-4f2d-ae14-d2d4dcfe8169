import request from '@/utils/request';

// 评审专家库相关API常量
export enum ExpertAPI {
  // 获取专家列表
  GET_EXPERT_LIST = '/expert/list',
  // 新增专家
  ADD_EXPERT = '/expert/add',
  // 更新专家
  UPDATE_EXPERT = '/expert/update',
  // 删除专家
  DELETE_EXPERT = '/expert/delete',
  // 获取专家详情
  GET_EXPERT_DETAIL = '/expert/detail',
  // 导出专家数据
  EXPORT_EXPERT_DATA = '/expert/export',
  // 批量删除专家
  DELETE_EXPERT_BATCH = '/expert/deleteBatch',
}

/**
 * 获取专家列表
 * @param params 查询参数
 */
export const getExpertList = (params: Record<string, any>) =>
  request.get(ExpertAPI.GET_EXPERT_LIST, { params });

/**
 * 新增专家
 * @param data 专家数据
 */
export const addExpert = (data: Record<string, any>) =>
  request.post(ExpertAPI.ADD_EXPERT, { data });

/**
 * 更新专家
 * @param data 专家数据
 */
export const updateExpert = (data: Record<string, any>) =>
  request.post(ExpertAPI.UPDATE_EXPERT, { data });

/**
 * 删除专家
 * @param id 专家ID
 */
export const deleteExpert = (id: string) =>
  request.post(ExpertAPI.DELETE_EXPERT, { id });

/**
 * 获取专家详情
 * @param id 专家ID
 */
export const getExpertDetail = (id: string) =>
  request.get(`${ExpertAPI.GET_EXPERT_DETAIL}?id=${id}`);

/**
 * 导出专家数据
 * @returns 文件流
 */
export const exportExpertData = () =>
  request.get(ExpertAPI.EXPORT_EXPERT_DATA, {
    responseType: 'blob',
  });

/**
 * 批量删除专家
 * @param ids 专家ID数组
 */
export const deleteExpertBatch = (ids: string[]) =>
  request.post(ExpertAPI.DELETE_EXPERT_BATCH, { ids });
