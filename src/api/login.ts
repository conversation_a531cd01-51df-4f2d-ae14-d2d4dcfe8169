import request from '@/utils/request';

enum API {
  GET_CAPTCHA_IMAGE = '/captchaImage',
  POST_LOGIN = '/login',
  GET_INFO = '/getInfo',
  LOGOUT = '/logout',
  EBS_LOGIN = '/singleSignOnEbs',
  CHANGE_PASSWORD = '/system/user/profile/updatePwd',

  // 未绑定统一认证平台的登录绑定接口
  Signin_Bind_Account = '/loginAndBindingLocalUser',
  // 未绑定统一认证平台的注册登录绑定接口
  Signup_And_Signin_Bind_Account = '/registAndlogin',
}

export type LOGIN = {
  uuid?: string;
  code?: string;
  username: string;
  password: string;
};

//获取验证码
export const getCaptchaImage = async () => {
  return request.get(API.GET_CAPTCHA_IMAGE, {});
};

//登录提交
export const submitLogin = async (data: LOGIN) => {
  return request.post(API.POST_LOGIN, { data });
};

//获取用户信息
export const getUserInfo = async (params?: Record<string, any>) =>
  request.get(API.GET_INFO, { params });

// 用户退出登录
export const submitLogout = async () => request.post(API.LOGOUT);

export interface ebsType {
  ebsToken: string;
}
// 易检测登录
export const ebsLogin = async (params: ebsType) =>
  request.get(API.EBS_LOGIN, { params });

//获取用户信息
interface IChangePasswordParams {
  oldPassword: string;
  newPassword: string;
}
export const changePassword = async (data: IChangePasswordParams) =>
  request.put(API.CHANGE_PASSWORD, { params: data });

/**
 *  登录绑定
 */
export const signInBindAccount = async (data: Record<string, any>) =>
  request.post(API.Signin_Bind_Account, { data });

/**
 * 注册并登录、绑定账号接口
 */
export const signUpAndSignInBindAccount = async (data: Record<string, any>) =>
  request.post(API.Signup_And_Signin_Bind_Account, { data });
