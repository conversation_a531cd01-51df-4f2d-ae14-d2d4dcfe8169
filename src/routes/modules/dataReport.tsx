// 数据上报
import { lazy } from 'react';

// 检测过程数据
const SampleCollectionRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/SampleCollectionRecord')
);
const SampleReceiptRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/SampleReceiptRecord')
);
const SampleAcquisitionRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/SampleAcquisitionRecord')
);
const SamplePreProcessingRecords = lazy(
  () =>
    import('@/pages/DataReporting/TestProcessData/SamplePreProcessingRecords')
);
const TestProcessRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/TestProcessRecord')
);
const ReportPreparationRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/ReportPreparationRecord')
);
const SampleHandlingRecord = lazy(
  () => import('@/pages/DataReporting/TestProcessData/SampleHandlingRecord')
);
// 机构能力数据
const QualityInfo = lazy(
  () => import('@/pages/DataReporting/OrgPowerData/QualityInfo')
);
const QualityControlRecord = lazy(
  () => import('@/pages/DataReporting/OrgPowerData/QualityControlRecord')
);
const TestMember = lazy(
  () => import('@/pages/DataReporting/OrgPowerData/TestMember')
);
const PersonnelTrainingRecord = lazy(
  () => import('@/pages/DataReporting/OrgPowerData/PersonnelTrainingRecord')
);
// 机构设备信息
const DeviceInfo = lazy(
  () => import('@/pages/DataReporting/OrgDeviceInfo/DeviceInfo')
);
const DeviceCalibrateRecord = lazy(
  () => import('@/pages/DataReporting/OrgDeviceInfo/DeviceCalibrateRecord')
);
const DeviceInspectRecord = lazy(
  () => import('@/pages/DataReporting/OrgDeviceInfo/DeviceInspectRecord')
);
// 异常数据管理
const DataRectification = lazy(
  () => import('@/pages/DataReporting/ExceptionData/DataRectification')
);
// 病例信息
const PatientBaseInfo = lazy(
  () => import('@/pages/DataReporting/CaseInfo/PatientBaseInfo')
);
const MedicalActivityInfo = lazy(
  () => import('@/pages/DataReporting/CaseInfo/MedicalActivityInfo')
);
const InfectiousDiseaseReportCard = lazy(
  () => import('@/pages/DataReporting/CaseInfo/InfectiousDiseaseReportCard')
);
const InspectionReport = lazy(
  () => import('@/pages/DataReporting/CaseInfo/InspectionReport')
);
const CheckoutReport = lazy(
  () => import('@/pages/DataReporting/CaseInfo/CheckoutReport')
);

export const dataReportRouter = [
  { title: '数据上报', path: '/dataReporting' },
  // 检测过程数据
  {
    title: '检测过程数据',
    path: 'testProcessData',
    completePath: '/dataReporting/testProcessData',
  },
  {
    title: '样本采集记录',
    path: 'sampleCollectionRecord',
    completePath: '/dataReporting/testProcessData/sampleCollectionRecord',
    element: <SampleCollectionRecord />,
  },
  {
    title: '样本接收记录',
    path: 'sampleReceiptRecord',
    completePath: '/dataReporting/testProcessData/sampleReceiptRecord',
    element: <SampleReceiptRecord />,
  },
  {
    title: '领样记录',
    path: 'sampleAcquisitionRecord',
    completePath: '/dataReporting/testProcessData/sampleAcquisitionRecord',
    element: <SampleAcquisitionRecord />,
  },
  {
    title: '样本前期处理记录',
    path: 'samplePreProcessingRecords',
    completePath: '/dataReporting/testProcessData/samplePreProcessingRecords',
    element: <SamplePreProcessingRecords />,
  },
  {
    title: '检测过程记录',
    path: 'testProcessRecord',
    completePath: '/dataReporting/testProcessData/testProcessRecord',
    element: <TestProcessRecord />,
  },
  {
    title: '报告编制记录',
    path: 'reportPreparationRecord',
    completePath: '/dataReporting/testProcessData/reportPreparationRecord',
    element: <ReportPreparationRecord />,
  },
  {
    title: '样本处置记录',
    path: 'sampleHandlingRecord',
    completePath: '/dataReporting/testProcessData/sampleHandlingRecord',
    element: <SampleHandlingRecord />,
  },
  // 机构能力数据
  {
    title: '机构能力数据',
    path: 'orgPowerData',
    completePath: '/dataReporting/orgPowerData',
  },
  {
    title: '资质能力信息',
    path: 'qualityInfo',
    completePath: '/dataReporting/orgPowerData/qualityInfo',
    element: <QualityInfo />,
  },
  {
    title: '质量控制记录',
    path: 'qualityControlRecord',
    completePath: '/dataReporting/orgPowerData/qualityControlRecord',
    element: <QualityControlRecord />,
  },
  {
    title: '检测人员',
    path: 'testMember',
    completePath: '/dataReporting/orgPowerData/testMember',
    element: <TestMember />,
  },
  {
    title: '人员培训记录',
    path: 'personnelTrainingRecord',
    completePath: '/dataReporting/orgPowerData/personnelTrainingRecord',
    element: <PersonnelTrainingRecord />,
  },
  // 机构设备信息
  {
    title: '机构设备信息',
    path: 'orgDeviceInfo',
    completePath: '/dataReporting/orgDeviceInfo',
  },
  {
    title: '设备信息',
    path: 'deviceInfo',
    completePath: '/dataReporting/orgDeviceInfo/deviceInfo',
    element: <DeviceInfo />,
  },
  {
    title: '设备检定校准记录',
    path: 'deviceCalibrateRecord',
    completePath: '/dataReporting/orgDeviceInfo/deviceCalibrateRecord',
    element: <DeviceCalibrateRecord />,
  },
  {
    title: '设备核查记录',
    path: 'deviceInspectRecord',
    completePath: '/dataReporting/orgDeviceInfo/deviceInspectRecord',
    element: <DeviceInspectRecord />,
  },
  // 异常数据管理
  {
    title: '异常数据管理',
    path: 'exceptionData',
    completePath: '/dataReporting/exceptionData',
  },
  {
    title: '异常数据整改',
    path: 'dataRectification',
    completePath: '/dataReporting/exceptionData/dataRectification',
    element: <DataRectification />,
  },
  // 病例信息
  {
    title: '病例信息',
    path: 'caseInfo',
    completePath: '/dataReporting/caseInfo',
  },
  {
    title: '患者基本信息',
    path: 'patientBaseInfo',
    completePath: '/dataReporting/caseInfo/patientBaseInfo',
    element: <PatientBaseInfo />,
  },
  {
    title: '诊疗活动信息卡',
    path: 'medicalActivityInfo',
    completePath: '/dataReporting/caseInfo/medicalActivityInfo',
    element: <MedicalActivityInfo />,
  },
  {
    title: '传染病报告卡',
    path: 'infectiousDiseaseReportCard',
    completePath: '/dataReporting/caseInfo/infectiousDiseaseReportCard',
    element: <InfectiousDiseaseReportCard />,
  },
  {
    title: '检查报告',
    path: 'inspectionReport',
    completePath: '/dataReporting/caseInfo/inspectionReport',
    element: <InspectionReport />,
  },
  {
    title: '检验报告',
    path: 'checkoutReport',
    completePath: '/dataReporting/caseInfo/checkoutReport',
    element: <CheckoutReport />,
  },
];
