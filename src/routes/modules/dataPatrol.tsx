// 数据巡查
import { lazy } from 'react';

// 检测过程数据
const SampleCollectionRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/SampleCollectionRecord')
);
const SampleReceiptRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/SampleReceiptRecord')
);
const SampleAcquisitionRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/SampleAcquisitionRecord')
);
const SamplePreProcessingRecords = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/SamplePreProcessingRecords')
);
const TestProcessRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/TestProcessRecord')
);
const ReportPreparationRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/ReportPreparationRecord')
);
const SampleHandlingRecord = lazy(
  () => import('@/pages/DataPatrol/TestProcessData/SampleHandlingRecord')
);
// 机构能力数据
const QualityInfo = lazy(
  () => import('@/pages/DataPatrol/OrgPowerData/QualityInfo')
);
const QualityControlRecord = lazy(
  () => import('@/pages/DataPatrol/OrgPowerData/QualityControlRecord')
);
const TestMember = lazy(
  () => import('@/pages/DataPatrol/OrgPowerData/TestMember')
);
const PersonnelTrainingRecord = lazy(
  () => import('@/pages/DataPatrol/OrgPowerData/PersonnelTrainingRecord')
);
// 机构设备信息
const DeviceInfo = lazy(
  () => import('@/pages/DataPatrol/OrgDeviceInfo/DeviceInfo')
);
const DeviceCalibrateRecord = lazy(
  () => import('@/pages/DataPatrol/OrgDeviceInfo/DeviceCalibrateRecord')
);
const DeviceInspectRecord = lazy(
  () => import('@/pages/DataPatrol/OrgDeviceInfo/DeviceInspectRecord')
);
const DeviceMonitor = lazy(
  () => import('@/pages/DataPatrol/OrgDeviceInfo/DeviceMonitor')
);
// 异常数据管理
const DataCheckRule = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/DataCheckRule')
);
const ExceptionDataInfo = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/ExceptionDataInfo')
);
const SampleTestData = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/SampleTestData')
);
const DataVerification = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/DataVerification')
);
const RectificationAudit = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/RectificationAudit')
);
const DataStatistics = lazy(
  () => import('@/pages/DataPatrol/ExceptionData/DataStatistics')
);
// 病例信息
const PatientBaseInfo = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/PatientBaseInfo')
);
const MedicalActivityInfo = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/MedicalActivityInfo')
);
const InfectiousDiseaseReportCard = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/InfectiousDiseaseReportCard')
);
const InspectionReport = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/InspectionReport')
);
const CheckoutReport = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/CheckoutReport')
);
const PatientPortrait = lazy(
  () => import('@/pages/DataPatrol/CaseInfo/PatientPortrait')
);

// 质控指标分析
const DeliveryTimelyRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/DeliveryTimelyRate')
);
const PathogenTestRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/PathogenTestRate')
);
const PathogenTestTimelyRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/PathogenTestTimelyRate')
);
const ReportTimeliness = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/ReportTimeliness')
);

export const dataPatrolRouter = [
  { title: '数据巡查', path: '/dataPatrol' },
  // 检测过程数据
  {
    title: '检测过程数据',
    path: 'testProcessData',
    completePath: '/dataPatrol/testProcessData',
  },
  {
    title: '样本采集记录',
    path: 'sampleCollectionRecord',
    completePath: '/dataPatrol/testProcessData/sampleCollectionRecord',
    element: <SampleCollectionRecord />,
  },
  {
    title: '样本接收记录',
    path: 'sampleReceiptRecord',
    completePath: '/dataPatrol/testProcessData/sampleReceiptRecord',
    element: <SampleReceiptRecord />,
  },
  {
    title: '领样记录',
    path: 'sampleAcquisitionRecord',
    completePath: '/dataPatrol/testProcessData/sampleAcquisitionRecord',
    element: <SampleAcquisitionRecord />,
  },
  {
    title: '样本前期处理记录',
    path: 'samplePreProcessingRecords',
    completePath: '/dataPatrol/testProcessData/samplePreProcessingRecords',
    element: <SamplePreProcessingRecords />,
  },
  {
    title: '检测过程记录',
    path: 'testProcessRecord',
    completePath: '/dataPatrol/testProcessData/testProcessRecord',
    element: <TestProcessRecord />,
  },
  {
    title: '报告编制记录',
    path: 'reportPreparationRecord',
    completePath: '/dataPatrol/testProcessData/reportPreparationRecord',
    element: <ReportPreparationRecord />,
  },
  {
    title: '样本处置记录',
    path: 'sampleHandlingRecord',
    completePath: '/dataPatrol/testProcessData/sampleHandlingRecord',
    element: <SampleHandlingRecord />,
  },
  // 机构能力数据
  {
    title: '机构能力数据',
    path: 'orgPowerData',
    completePath: '/dataPatrol/orgPowerData',
  },
  {
    title: '资质能力信息',
    path: 'qualityInfo',
    completePath: '/dataPatrol/orgPowerData/qualityInfo',
    element: <QualityInfo />,
  },
  {
    title: '质量控制记录',
    path: 'qualityControlRecord',
    completePath: '/dataPatrol/orgPowerData/qualityControlRecord',
    element: <QualityControlRecord />,
  },
  {
    title: '检测人员',
    path: 'testMember',
    completePath: '/dataPatrol/orgPowerData/testMember',
    element: <TestMember />,
  },
  {
    title: '人员培训记录',
    path: 'personnelTrainingRecord',
    completePath: '/dataPatrol/orgPowerData/personnelTrainingRecord',
    element: <PersonnelTrainingRecord />,
  },
  // 设备仪器信息
  {
    title: '设备仪器信息',
    path: 'orgDeviceInfo',
    completePath: '/dataPatrol/orgDeviceInfo',
  },
  {
    title: '设备信息',
    path: 'deviceInfo',
    completePath: '/dataPatrol/orgDeviceInfo/deviceInfo',
    element: <DeviceInfo />,
  },
  {
    title: '设备检定校准记录',
    path: 'deviceCalibrateRecord',
    completePath: '/dataPatrol/orgDeviceInfo/deviceCalibrateRecord',
    element: <DeviceCalibrateRecord />,
  },
  {
    title: '设备核查记录',
    path: 'deviceInspectRecord',
    completePath: '/dataPatrol/orgDeviceInfo/deviceInspectRecord',
    element: <DeviceInspectRecord />,
  },
  {
    title: '温湿度监控',
    path: 'deviceMonitor',
    completePath: '/dataPatrol/orgDeviceInfo/deviceMonitor',
    element: <DeviceMonitor />,
  },
  // 异常数据管理
  {
    title: '异常数据管理',
    path: 'exceptionData',
    completePath: '/dataPatrol/exceptionData',
  },
  {
    title: '数据校验规则',
    path: 'dataCheckRule',
    completePath: '/dataPatrol/exceptionData/dataCheckRule',
    element: <DataCheckRule />,
  },
  {
    title: '异常数据信息',
    path: 'exceptionDataInfo',
    completePath: '/dataPatrol/exceptionData/exceptionDataInfo',
    element: <ExceptionDataInfo />,
  },
  {
    title: '样品检测数据管理',
    path: 'sampleTestData',
    completePath: '/dataPatrol/exceptionData/sampleTestData',
    element: <SampleTestData />,
  },
  {
    title: '异常数据核实',
    path: 'dataVerification',
    completePath: '/dataPatrol/exceptionData/dataVerification',
    element: <DataVerification />,
  },
  {
    title: '异常整改审核',
    path: 'rectificationAudit',
    completePath: '/dataPatrol/exceptionData/rectificationAudit',
    element: <RectificationAudit />,
  },
  {
    title: '异常数据统计',
    path: 'dataStatistics',
    completePath: '/dataPatrol/exceptionData/dataStatistics',
    element: <DataStatistics />,
  },
  // 病例信息
  {
    title: '病例信息',
    path: 'caseInfo',
    completePath: '/dataPatrol/caseInfo',
  },
  {
    title: '患者基本信息',
    path: 'patientBaseInfo',
    completePath: '/dataPatrol/caseInfo/patientBaseInfo',
    element: <PatientBaseInfo />,
  },
  {
    title: '诊疗活动信息卡',
    path: 'medicalActivityInfo',
    completePath: '/dataPatrol/caseInfo/medicalActivityInfo',
    element: <MedicalActivityInfo />,
  },
  {
    title: '传染病报告卡',
    path: 'infectiousDiseaseReportCard',
    completePath: '/dataPatrol/caseInfo/infectiousDiseaseReportCard',
    element: <InfectiousDiseaseReportCard />,
  },
  {
    title: '检查报告',
    path: 'inspectionReport',
    completePath: '/dataPatrol/caseInfo/inspectionReport',
    element: <InspectionReport />,
  },
  {
    title: '检验报告',
    path: 'checkoutReport',
    completePath: '/dataPatrol/caseInfo/checkoutReport',
    element: <CheckoutReport />,
  },
  {
    title: '患者画像',
    path: 'patientPortrait',
    completePath: '/dataPatrol/caseInfo/patientPortrait',
    element: <PatientPortrait />,
  },

  // 质控指标分析
  {
    title: '质控指标分析',
    path: 'qualityControl',
    completePath: '/dataPatrol/qualityControl',
  },
  {
    title: '送检及时率分析',
    path: 'deliveryTimelyRate',
    completePath: '/dataPatrol/qualityControl/deliveryTimelyRate',
    element: <DeliveryTimelyRate />,
  },
  {
    title: '病原检测率分析',
    path: 'pathogenTestRate',
    completePath: '/dataPatrol/qualityControl/pathogenTestRate',
    element: <PathogenTestRate />,
  },
  {
    title: '病原检测及时率分析',
    path: 'pathogenTestTimelyRate',
    completePath: '/dataPatrol/qualityControl/pathogenTestTimelyRate',
    element: <PathogenTestTimelyRate />,
  },
  {
    title: '报告及时性分析',
    path: 'reportTimeliness',
    completePath: '/dataPatrol/qualityControl/reportTimeliness',
    element: <ReportTimeliness />,
  },
];
