import { lazy } from 'react';

// 风险感知指标
const RiskPerceptionIndex = lazy(
  () => import('@/pages/OrgRiskAssessment/RiskPerceptionIndex')
);


// 风险感知模型
const RiskPerceptionModel = lazy(
  () => import('@/pages/OrgRiskAssessment/riskPerceptionModel')
);
// 工作台
const DiseaseControlUser = lazy(
  () => import('@/pages/OrgRiskAssessment/Workbench/DiseaseControlUser')
);
const OrgUser = lazy(
  () => import('@/pages/OrgRiskAssessment/Workbench/OrgUser')
);
// 机构风险评价
const RiskAssessment = lazy(
  () => import('@/pages/OrgRiskAssessment/RiskAssessment')
);
// 风险管理
const RiskClue = lazy(
  () => import('@/pages/OrgRiskAssessment/RiskManagement/RiskClue')
);
const RiskSelfExamination = lazy(
  () => import('@/pages/OrgRiskAssessment/RiskManagement/RiskSelfExamination')
);
const ResultsAudit = lazy(
  () => import('@/pages/OrgRiskAssessment/RiskManagement/ResultsAudit')
);

export const orgRiskAssessmentRouter = [
  { title: '机构风险评价', path: '/orgRiskAssessment' },
  // 风险感知指标
  {
    title: '风险感知指标',
    path: 'riskPerceptionIndex',
    completePath: '/orgRiskAssessment/riskPerceptionIndex',
    element: <RiskPerceptionIndex />,
  },
  {
    title: '风险感知模型',
    path: 'riskPerceptionModel',
    completePath: '/orgRiskAssessment/riskPerceptionModel',
    element: <RiskPerceptionModel />,
  },
  // 工作台
  {
    title: '工作台',
    path: 'workbench',
    completePath: '/orgRiskAssessment/workbench',
  },
  {
    title: '疾控用户工作台',
    path: 'diseaseControlUser',
    completePath: '/orgRiskAssessment/workbench/diseaseControlUser',
    element: <DiseaseControlUser />,
  },
  {
    title: '机构用户工作台',
    path: 'orgUser',
    completePath: '/orgRiskAssessment/workbench/orgUser',
    element: <OrgUser />,
  },
  // 机构风险评价
  {
    title: '机构风险评价',
    path: 'riskAssessment',
    completePath: '/orgRiskAssessment/riskAssessment',
    element: <RiskAssessment />,
  },
  // 风险管理
  {
    title: '风险管理',
    path: 'riskManagement',
    completePath: '/orgRiskAssessment/riskManagement',
  },
  {
    title: '风险线索',
    path: 'riskClue',
    completePath: '/orgRiskAssessment/riskManagement/riskClue',
    element: <RiskClue />,
  },
  {
    title: '风险自查',
    path: 'riskSelfExamination',
    completePath: '/orgRiskAssessment/riskManagement/riskSelfExamination',
    element: <RiskSelfExamination />,
  },
  {
    title: '自查结果审核',
    path: 'resultsAudit',
    completePath: '/orgRiskAssessment/riskManagement/resultsAudit',
    element: <ResultsAudit />,
  },
];
