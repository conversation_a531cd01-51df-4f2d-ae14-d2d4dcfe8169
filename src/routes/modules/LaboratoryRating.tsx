import { lazy } from 'react';

// 评级工作台
const RatingWorkbench = lazy(
  () => import('@/pages/LaboratoryRating/RatingWorkbench')
);

// 评审管理
// 评级申请受理
const RatingApplicationReview = lazy(
  () =>
    import(
      '@/pages/LaboratoryRating/RatingManagementV2/RatingApplicationReview'
    )
);

// 申请材料文审
const RatingApplicationReviewMaterials = lazy(
  () =>
    import(
      '@/pages/LaboratoryRating/RatingManagementV2/RatingApplicationReviewMaterials'
    )
);

// 申请现场评审

const RatingApplicationReviewField = lazy(
  () =>
    import(
      '@/pages/LaboratoryRating/RatingManagementV2/RatingApplicationReviewField'
    )
);

// 申请管理
// 评级申请
const RatingApplication = lazy(
  () =>
    import('@/pages/LaboratoryRating/ApplicationManagement/RatingApplication')
);

// 基础数据配置
const LaboratoryList = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/LaboratoryList')
);
const RatingIndex = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/RatingIndex')
);
const GradingRule = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/GradingRule')
);
const ReviewExpertDB = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/ReviewExpertDB')
);
const ReviewPanel = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/ReviewPanel')
);
const ExpertReview = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/ExpertReview')
);
const RatingSettings = lazy(
  () => import('@/pages/LaboratoryRating/BaseDataConfig/RatingSettings')
);

// 评级统计
const RatingResultRecords = lazy(
  () => import('@/pages/LaboratoryRating/RatingStatistics/RatingResultRecords')
);

// 评级管理
const ReviewApplication = lazy(
  () => import('@/pages/LaboratoryRating/RatingManagement/ReviewApplication')
);
const LaboratoryManagement = lazy(
  () => import('@/pages/LaboratoryRating/RatingManagement/LaboratoryManagement')
);
const LaboratoryEvaluation = lazy(
  () => import('@/pages/LaboratoryRating/RatingManagement/LaboratoryEvaluation')
);
const RatingResultRecord = lazy(
  () => import('@/pages/LaboratoryRating/RatingManagement/RatingResultRecord')
);
const RatingResultsStatistics = lazy(
  () =>
    import('@/pages/LaboratoryRating/RatingManagement/RatingResultsStatistics')
);

// 统计分析
const RatingResultStatistic = lazy(
  () =>
    import('@/pages/LaboratoryRating/StatisticalAnalysis/RatingResultStatistic')
);

export const laboratoryRatingRouter = [
  { title: '实验室评级', path: '/laboratoryRating' },
  // 评级工作台
  {
    title: '评级工作台',
    path: 'ratingWorkbench',
    completePath: '/laboratoryRating/ratingWorkbench',
    element: <RatingWorkbench />,
  },
  // 评审管理
  {
    title: '评审管理',
    path: 'ratingManagement',
    completePath: '/laboratoryRating/ratingManagement',
  },
  {
    title: '评级申请受理',
    path: 'ratingApplicationReview',
    completePath: '/laboratoryRating/ratingManagement/ratingApplicationReview',
    element: <RatingApplicationReview />,
  },

  {
    title: '申请材料文审',
    path: 'RatingApplicationReviewMaterials',
    completePath:
      '/laboratoryRating/ratingManagement/RatingApplicationReviewMaterials',
    element: <RatingApplicationReviewMaterials />,
  },

  {
    title: '申请现场评审',
    path: 'RatingApplicationReviewField',
    completePath:
      '/laboratoryRating/ratingManagement/RatingApplicationReviewField',
    element: <RatingApplicationReviewField />,
  },

  // 申请管理
  {
    title: '申请管理',
    path: 'applicationManagement',
    completePath: '/laboratoryRating/applicationManagement',
  },
  {
    title: '评级申请',
    path: 'ratingApplication',
    completePath: '/laboratoryRating/applicationManagement/ratingApplication',
    element: <RatingApplication />,
  },
  // 基础数据
  {
    title: '基础数据配置',
    path: 'baseDataConfig',
    completePath: '/laboratoryRating/baseDataConfig',
  },
  {
    title: '实验室名录',
    path: 'laboratoryList',
    completePath: '/laboratoryRating/baseDataConfig/laboratoryList',
    element: <LaboratoryList />,
  },
  {
    title: '评级指标管理',
    path: 'ratingIndex',
    completePath: '/laboratoryRating/baseDataConfig/ratingIndex',
    element: <RatingIndex />,
  },
  {
    title: '等级划分',
    path: 'gradingRule',
    completePath: '/laboratoryRating/baseDataConfig/gradingRule',
    element: <GradingRule />,
  },
  {
    title: '评审专家库',
    path: 'reviewExpertdb',
    completePath: '/laboratoryRating/baseDataConfig/reviewExpertdb',
    element: <ReviewExpertDB />,
  },
  {
    title: '评审小组',
    path: 'reviewPanel',
    completePath: '/laboratoryRating/baseDataConfig/reviewPanel',
    element: <ReviewPanel />,
  },
  {
    title: '专家审核',
    path: 'expertReview',
    completePath: '/laboratoryRating/baseDataConfig/expertReview',
    element: <ExpertReview />,
  },
  {
    title: '评级设置',
    path: 'ratingSettings',
    completePath: '/laboratoryRating/baseDataConfig/ratingSettings',
    element: <RatingSettings />,
  },

  // 评级统计
  {
    title: '评级统计',
    path: 'ratingStatistics',
    completePath: '/laboratoryRating/ratingStatistics',
  },
  {
    title: '评级结果记录',
    path: 'ratingResultRecords',
    completePath: '/laboratoryRating/ratingStatistics/ratingResultRecords',
    element: <RatingResultRecords />,
  },
  // 评级管理
  {
    title: '评级管理',
    path: 'ratingManagementV1',
    completePath: '/laboratoryRating/ratingManagementV1',
  },
  {
    title: '评审申请',
    path: 'reviewApplication',
    completePath: '/laboratoryRating/ratingManagementV1/reviewApplication',
    element: <ReviewApplication />,
  },
  {
    title: '实验室管理',
    path: 'laboratoryManagement',
    completePath: '/laboratoryRating/ratingManagementV1/laboratoryManagement',
    element: <LaboratoryManagement />,
  },
  {
    title: '实验室评定',
    path: 'laboratoryEvaluation',
    completePath: '/laboratoryRating/ratingManagementV1/laboratoryEvaluation',
    element: <LaboratoryEvaluation />,
  },
  {
    title: '实验室评级结果记录',
    path: 'ratingResultRecord',
    completePath: '/laboratoryRating/ratingManagementV1/ratingResultRecord',
    element: <RatingResultRecord />,
  },
  {
    title: '评审结果统计',
    path: 'ratingResultsStatistics',
    completePath: '/laboratoryRating/ratingManagementV1/ratingResultsStatistics',
    element: <RatingResultsStatistics />,
  },

  // 统计分析
  {
    title: '评级结果统计',
    path: 'ratingResultStatistic',
    completePath: '/laboratoryRating/ratingResultStatistic',
    element: <RatingResultStatistic />,
  },
];
