import { lazy } from 'react';

// 系统管理
const SystemMenu = lazy(() => import('@/pages/System/Menu'));
const SystemRole = lazy(() => import('@/pages/System/Role'));
const SystemUser = lazy(() => import('@/pages/System/User'));
const SystemDept = lazy(() => import('@/pages/System/Dept'));
const SystemConfig = lazy(() => import('@/pages/System/Config'));
const SystemDict = lazy(() => import('@/pages/System/Dict'));
const SystemOSS = lazy(() => import('@/pages/System/Oss'));

export const systemRouter = [
  { title: '系统管理', path: '/system' },
  {
    title: '机构管理',
    path: 'dept',
    completePath: '/system/dept',
    element: <SystemDept />,
  },
  {
    title: '用户管理',
    path: 'user',
    completePath: '/system/user',
    element: <SystemUser />,
  },
  {
    title: '角色管理',
    path: 'role',
    completePath: '/system/role',
    element: <SystemRole />,
  },
  {
    title: '菜单管理',
    path: 'menu',
    completePath: '/system/menu',
    element: <SystemMenu />,
  },
  {
    title: '参数管理',
    path: 'config',
    completePath: '/system/config',
    element: <SystemConfig />,
  },
  {
    title: '字典管理',
    path: 'dict',
    completePath: '/system/dict',
    element: <SystemDict />,
  },
  {
    title: 'OSS配置',
    path: 'ossconfig',
    completePath: '/system/ossconfig',
    element: <SystemOSS />,
  },
];
