import { lazy } from 'react';
import NewDetectionRate from '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/NewDetectionRate';
import NewSamplingRate from '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/NewSamplingRate';

// 病原监测数据字典
const InfectiousLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/InfectiousLibrary')
);
const GuizhouLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/GuizhouLibrary')
);
const InfectiousAllLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/InfectiousAllLibrary')
);

// 病原监测预警
const PositiveWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/PositiveWarning')
);
const AreaWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AreaWarning')
);
const AgeWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AgeWarning')
);
const PeopleWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/PeopleWarning')
);
const AcutePathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AcutePathogenWarning')
);
const HighlyPathogenicWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/HighlyPathogenicWarning')
);
const NewPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/NewPathogenWarning')
);
const SuddenlyPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/SuddenlyPathogenWarning')
);
const ImportPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/ImportPathogenWarning')
);

// =======

const RegionalSamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/RegionalSamplingRate'
    )
);
const AgeSamplingRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/AgeSamplingRate')
);
const PopulationSamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PopulationSamplingRate'
    )
);
const SeasonSamplingRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/SeasonSamplingRate')
);
const MonthlySamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/MonthlySamplingRate'
    )
);
const RegionalDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/RegionalDetectionRate'
    )
);
const AgeDetectionRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/AgeDetectionRate')
);

const PopulationDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PopulationDetectionRate'
    )
);
const SeasonDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/SeasonDetectionRate'
    )
);
const MonthlyDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/MonthlyDetectionRate'
    )
);
const PathogenDrugResistance = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenDrugResistance'
    )
);
const DrugResistanceTrends = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DrugResistanceTrends'
    )
);
const PathogenDrugResistanceTestRecords = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenDrugResistanceTestRecords'
    )
);
const PathogenComplexInfection = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfection'
    )
);
const DrugResistanceAnalysis = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DrugResistanceAnalysis'
    )
);
const PathogenSpectrumAnalysis = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenSpectrumAnalysis'
    )
);
const PathogenComplexInfectionArea = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionArea'
    )
);
const PathogenComplexInfectionAge = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionAge'
    )
);
const PathogenComplexInfectionPopulation = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionPopulation'
    )
);
const PathogenPositiveSeasonality = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveSeasonality'
    )
);
const PathogenPositiveTrend = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveTrend'
    )
);
const PathogenPositiveRegionality = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveRegionality'
    )
);
const DiseaseSpectrumMap = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DiseaseSpectrumMap')
);

// 质控指标分析
const DeliveryTimelyRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/DeliveryTimelyRate')
);
const PathogenTestRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/PathogenTestRate')
);
const PathogenTestTimelyRate = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/PathogenTestTimelyRate')
);
const ReportTimeliness = lazy(
  () => import('@/pages/IndexAnalysis/QualityControl/ReportTimeliness')
);

// 专题分析
// 艾滋病专题分析
const AcquiredImmunodeficiency = lazy(
  () =>
    import('@/pages/IndexAnalysis/SpecializedAnalysis/AcquiredImmunodeficiency')
);

// 高校传染病感染情况
const CollegeInfectiousDiseases = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/SpecializedAnalysis/CollegeInfectiousDiseases'
    )
);

export const indexAnalysisRouter = [
  { title: '指标分析', path: '/indexAnalysis' },
  // 病原监测数据字典
  {
    title: '病原监测数据字典',
    path: 'pathogenDictionary',
    completePath: '/indexAnalysis/pathogenDictionary',
  },
  {
    title: '传染病库',
    path: 'infectiousLibrary',
    completePath: '/indexAnalysis/pathogenDictionary/infectiousLibrary',
    element: <InfectiousLibrary />,
  },
  {
    title: '贵州省病原库',
    path: 'guizhouLibrary',
    completePath: '/indexAnalysis/pathogenDictionary/guizhouLibrary',
    element: <GuizhouLibrary />,
  },
  {
    title: '传染病病原全库',
    path: 'infectiousAllLibrary',
    completePath: '/indexAnalysis/pathogenDictionary/infectiousAllLibrary',
    element: <InfectiousAllLibrary />,
  },

  // 病原监测预警
  {
    title: '病原监测预警',
    path: 'pathogenWarning',
    completePath: '/indexAnalysis/pathogenWarning',
  },
  {
    title: '病原阳性预警',
    path: 'positiveWarning',
    completePath: '/indexAnalysis/pathogenWarning/positiveWarning',
    element: <PositiveWarning />,
  },
  {
    title: '区域态势预警',
    path: 'areaWarning',
    completePath: '/indexAnalysis/pathogenWarning/areaWarning',
    element: <AreaWarning />,
  },
  {
    title: '年龄态势预警',
    path: 'ageWarning',
    completePath: '/indexAnalysis/pathogenWarning/ageWarning',
    element: <AgeWarning />,
  },
  {
    title: '人群态势预警',
    path: 'peopleWarning',
    completePath: '/indexAnalysis/pathogenWarning/peopleWarning',
    element: <PeopleWarning />,
  },
  {
    title: '急性病原预警',
    path: 'acutePathogenWarning',
    completePath: '/indexAnalysis/pathogenWarning/acutePathogenWarning',
    element: <AcutePathogenWarning />,
  },
  {
    title: '高致病性病原预警',
    path: 'highlyPathogenicWarning',
    completePath: '/indexAnalysis/pathogenWarning/highlyPathogenicWarning',
    element: <HighlyPathogenicWarning />,
  },
  {
    title: '新发病原预警',
    path: 'newPathogenWarning',
    completePath: '/indexAnalysis/pathogenWarning/newPathogenWarning',
    element: <NewPathogenWarning />,
  },
  {
    title: '突发病原预警',
    path: 'suddenlyPathogenWarning',
    completePath: '/indexAnalysis/pathogenWarning/suddenlyPathogenWarning',
    element: <SuddenlyPathogenWarning />,
  },
  {
    title: '输入性病原预警',
    path: 'importPathogenWarning',
    completePath: '/indexAnalysis/pathogenWarning/importPathogenWarning',
    element: <ImportPathogenWarning />,
  },
  {
    title: '业务指标分析',
    path: 'businessIndicator',
    completePath: '/indexAnalysis/businessIndicator',
  },
  {
    title: '采样率分析',
    path: 'newSamplingRate',
    completePath: '/indexAnalysis/businessIndicator/newSamplingRate',
    element: <NewSamplingRate />,
  },
  {
    title: '检出率分析',
    path: 'newDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/newDetectionRate',
    element: <NewDetectionRate />,
  },
  {
    title: '区域采样率分析',
    path: 'regionalSamplingRate',
    completePath: '/indexAnalysis/businessIndicator/regionalSamplingRate',
    element: <RegionalSamplingRate />,
  },
  {
    title: '年龄采样率分析',
    path: 'ageSamplingRate',
    completePath: '/indexAnalysis/businessIndicator/ageSamplingRate',
    element: <AgeSamplingRate />,
  },
  {
    title: '人群采样率分析',
    path: 'populationSamplingRate',
    completePath: '/indexAnalysis/businessIndicator/populationSamplingRate',
    element: <PopulationSamplingRate />,
  },
  {
    title: '季节采样率分析',
    path: 'seasonSamplingRate',
    completePath: '/indexAnalysis/businessIndicator/seasonSamplingRate',
    element: <SeasonSamplingRate />,
  },
  {
    title: '月度采样率分析',
    path: 'monthlySamplingRate',
    completePath: '/indexAnalysis/businessIndicator/monthlySamplingRate',
    element: <MonthlySamplingRate />,
  },
  {
    title: '区域检出率分析',
    path: 'regionalDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/regionalDetectionRate',
    element: <RegionalDetectionRate />,
  },
  {
    title: '年龄检出率分析',
    path: 'ageDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/ageDetectionRate',
    element: <AgeDetectionRate />,
  },
  {
    title: '人群检出率分析',
    path: 'populationDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/populationDetectionRate',
    element: <PopulationDetectionRate />,
  },
  {
    title: '季节检出率分析',
    path: 'seasonDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/seasonDetectionRate',
    element: <SeasonDetectionRate />,
  },
  {
    title: '月度检出率分析',
    path: 'monthlyDetectionRate',
    completePath: '/indexAnalysis/businessIndicator/monthlyDetectionRate',
    element: <MonthlyDetectionRate />,
  },
  {
    title: '病原耐药性分析',
    path: 'pathogenDrugResistance',
    completePath: '/indexAnalysis/businessIndicator/pathogenDrugResistance',
    element: <PathogenDrugResistance />,
  },
  {
    title: '耐药性趋势分析',
    path: 'drugResistanceTrends',
    completePath: '/indexAnalysis/businessIndicator/drugResistanceTrends',
    element: <DrugResistanceTrends />,
  },
  {
    title: '病原耐药性检测记录',
    path: 'pathogenDrugResistanceTestRecords',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenDrugResistanceTestRecords',
    element: <PathogenDrugResistanceTestRecords />,
  },
  {
    title: '耐药性分析',
    path: 'drugResistanceAnalysis',
    completePath: '/indexAnalysis/businessIndicator/drugResistanceAnalysis',
    element: <DrugResistanceAnalysis />,
  },
  {
    title: '病原谱分析',
    path: 'pathogenSpectrumAnalysis',
    completePath: '/indexAnalysis/businessIndicator/pathogenSpectrumAnalysis',
    element: <PathogenSpectrumAnalysis />,
  },
  {
    title: '复合感染分析',
    path: 'pathogenComplexInfection',
    completePath: '/indexAnalysis/businessIndicator/pathogenComplexInfection',
    element: <PathogenComplexInfection />,
  },
  {
    title: '病原复合感染区域分析',
    path: 'pathogenComplexInfectionArea',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenComplexInfectionArea',
    element: <PathogenComplexInfectionArea />,
  },
  {
    title: '病原复合感染年龄分析',
    path: 'pathogenComplexInfectionAge',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenComplexInfectionAge',
    element: <PathogenComplexInfectionAge />,
  },
  {
    title: '病原复合感染人群分析',
    path: 'pathogenComplexInfectionPopulation',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenComplexInfectionPopulation',
    element: <PathogenComplexInfectionPopulation />,
  },
  {
    title: '病原阳性季节性分析',
    path: 'pathogenPositiveSeasonality',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenPositiveSeasonality',
    element: <PathogenPositiveSeasonality />,
  },
  {
    title: '病原阳性趋势分析',
    path: 'pathogenPositiveTrend',
    completePath: '/indexAnalysis/businessIndicator/pathogenPositiveTrend',
    element: <PathogenPositiveTrend />,
  },
  {
    title: '病原阳性区域性分析',
    path: 'pathogenPositiveRegionality',
    completePath:
      '/indexAnalysis/businessIndicator/pathogenPositiveRegionality',
    element: <PathogenPositiveRegionality />,
  },
  {
    title: '病谱图构成分析',
    path: 'diseaseSpectrumMap',
    completePath: '/indexAnalysis/businessIndicator/diseaseSpectrumMap',
    element: <DiseaseSpectrumMap />,
  },
  // 质控指标分析
  {
    title: '质控指标分析',
    path: 'qualityControl',
    completePath: '/indexAnalysis/qualityControl',
  },
  {
    title: '送检及时率分析',
    path: 'deliveryTimelyRate',
    completePath: '/indexAnalysis/qualityControl/deliveryTimelyRate',
    element: <DeliveryTimelyRate />,
  },
  {
    title: '病原检测率分析',
    path: 'pathogenTestRate',
    completePath: '/indexAnalysis/qualityControl/pathogenTestRate',
    element: <PathogenTestRate />,
  },
  {
    title: '病原检测及时率分析',
    path: 'pathogenTestTimelyRate',
    completePath: '/indexAnalysis/qualityControl/pathogenTestTimelyRate',
    element: <PathogenTestTimelyRate />,
  },
  {
    title: '报告及时性分析',
    path: 'reportTimeliness',
    completePath: '/indexAnalysis/qualityControl/reportTimeliness',
    element: <ReportTimeliness />,
  },

  // 艾滋病
  {
    title: '艾滋病',
    path: 'acquiredImmunodeficiency',
    completePath: '/indexAnalysis/acquiredImmunodeficiency',
    element: <AcquiredImmunodeficiency />,
  },
  // 高校传染病
  {
    title: '高校传染病感染情况',
    path: 'collegeInfectiousDiseases',
    completePath: '/indexAnalysis/collegeInfectiousDiseases',
    element: <CollegeInfectiousDiseases />,
  },
];
