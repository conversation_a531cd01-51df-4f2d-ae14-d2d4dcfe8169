import { lazy } from 'react';

// 疫情追踪
const EventReporting = lazy(
  () => import('@/pages/EpidemicTracking/EventManagement/EventReporting')
);
const EventVerification = lazy(
  () => import('@/pages/EpidemicTracking/EventManagement/EventVerification')
);
const EventFollowUp = lazy(
  () => import('@/pages/EpidemicTracking/EventManagement/EventFollowUp')
);
const EventFinalReport = lazy(
  () => import('@/pages/EpidemicTracking/EventManagement/EventFinalReport')
);
const EventQuery = lazy(() => import('@/pages/EpidemicTracking/EventQuery'));

export const epidemicTrackingRouter = [
  { title: '疫情追踪', path: '/epidemicTracking' },
  // 事件办理
  {
    title: '事件办理',
    path: 'eventManagement',
    completePath: '/epidemicTracking/eventManagement',
  },
  {
    title: '事件上报',
    path: 'eventReporting',
    completePath: '/epidemicTracking/eventManagement/eventReporting',
    element: <EventReporting />,
  },
  {
    title: '事件核实',
    path: 'eventVerification',
    completePath: '/epidemicTracking/eventManagement/eventVerification',
    element: <EventVerification />,
  },
  {
    title: '事件续报',
    path: 'eventFollowUp',
    completePath: '/epidemicTracking/eventManagement/eventFollowUp',
    element: <EventFollowUp />,
  },
  {
    title: '事件终报',
    path: 'eventFinalReport',
    completePath: '/epidemicTracking/eventManagement/eventFinalReport',
    element: <EventFinalReport />,
  },
  // 事件查询
  {
    title: '事件查询',
    path: 'eventQuery',
    completePath: '/epidemicTracking/eventQuery',
    element: <EventQuery />,
  },
];
