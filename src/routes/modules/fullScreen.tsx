/* eslint-disable @typescript-eslint/no-unused-vars */
import { lazy } from 'react';
import FullScreenLayout from '@/layouts/FullScreenLayout';

// 监测概况
const MonitoringOverview = lazy(
  () => import('@/pages/DataVisualizationDashboard/MonitoringOverview')
);
// 数据驾驶舱
const DataCockpit = lazy(
  () => import('@/pages/DataVisualizationDashboard/DataCockpit')
);
// 业务指标
const BusinessMetrics = lazy(
  () => import('@/pages/DataVisualizationDashboard/BusinessMetrics')
);
// 质控指标
const QualityControlMetrics = lazy(
  () => import('@/pages/DataVisualizationDashboard/QualityControlMetrics')
);
// 监测预警
const MonitoringEarlyWarning = lazy(
  () => import('@/pages/DataVisualizationDashboard/MonitoringEarlyWarning')
);

// 实验室画像
const LaboratoryPortrait = lazy(
  () => import('@/pages/DataVisualizationDashboard/LaboratoryPortrait')
);

// 质控在线
const QualityControlOnline = lazy(
  () => import('@/pages/DataVisualizationDashboard/QualityControlOnline')
);

// 数据质控
const DataQualityControl = lazy(
  () => import('@/pages/DataVisualizationDashboard/DataQualityControl')
);

// 质量考核
const QualityAssessment = lazy(
  () => import('@/pages/DataVisualizationDashboard/QualityAssessment')
);

export const fullScreenRouter = [
  {
    element: <FullScreenLayout />,
    path: "/dataVisualizationDashboard",
    children: [
      {
        title: '监测概况',
        path: '/dataVisualizationDashboard/monitoringOverview',
        element: <MonitoringOverview />,
      },
      {
        title: '数据驾驶舱',
        path: '/dataVisualizationDashboard/dataCockpit',
        element: <DataCockpit />,
      },
      {
        title: '业务指标',
        path: '/dataVisualizationDashboard/businessMetrics',
        element: <BusinessMetrics />,
      },
      {
        title: '质控指标',
        path: '/dataVisualizationDashboard/qualityControlMetrics',
        element: <QualityControlMetrics />,
      },
      {
        title: '监测预警',
        path: '/dataVisualizationDashboard/monitoringEarlyWarning',
        element: <MonitoringEarlyWarning />,
      },
      {
        title: '实验室画像',
        path: '/dataVisualizationDashboard/laboratoryPortrait',
        element: <LaboratoryPortrait />,
      },
      {
        title: '质控在线',
        path: '/dataVisualizationDashboard/qualityControlOnline',
        element: <QualityControlOnline />,
      },
      {
        title: '数据质控',
        path: '/dataVisualizationDashboard/dataQualityControl',
        element: <DataQualityControl />,
      },
      {
        title: '质量考核',
        path: '/dataVisualizationDashboard/qualityAssessment',
        element: <QualityAssessment />,
      },
    ],
  },
];
