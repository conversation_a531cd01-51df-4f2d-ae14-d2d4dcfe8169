/* eslint-disable @typescript-eslint/no-unused-vars */
import { lazy } from 'react';

// 病原监测数据字典
const InfectiousLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/InfectiousLibrary')
);
const GuizhouLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/GuizhouLibrary')
);
const InfectiousAllLibrary = lazy(
  () => import('@/pages/IndexAnalysis/PathogenDictionary/InfectiousAllLibrary')
);

// 病原监测
const PathogenPoint = lazy(() => import('@/pages/Pathogen/Point'));
const PathogenPlan = lazy(() => import('@/pages/Pathogen/Plan'));
const PathogenTask = lazy(() => import('@/pages/Pathogen/Task'));
const PathogenSmaple = lazy(() => import('@/pages/Pathogen/Smaple'));
const TestResultInspection = lazy(
  () => import('@/pages/Pathogen/TestResultInspection')
);
const StrainVerificationTask = lazy(
  () => import('@/pages/Pathogen/StrainVerificationTask')
);
const ResultsInspection = lazy(
  () => import('@/pages/Pathogen/ResultsInspection')
);

const DrugResistanceTesting = lazy(
  () => import('@/pages/Pathogen/DrugResistanceTesting')
);

const TaskProgress = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/TaskProgress')
);
const StrainReview = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/StrainReview')
);
const Distribution = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/Distribution')
);
const AreaDistribution = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/AreaDistribution')
);
const AgeDistribution = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/AgeDistribution')
);
const PopulationDistribution = lazy(
  () => import('@/pages/Pathogen/StatisticalAnalysis/PopulationDistribution')
);
const SampleDetectionInfo = lazy(
  () => import('@/pages/Pathogen/ActiveSurveillance/SampleDetectionInfo')
);
const TestResultInspect = lazy(
  () => import('@/pages/Pathogen/ActiveSurveillance/TestResultInspect')
);
const ActiveDistribution = lazy(
  () => import('@/pages/Pathogen/ActiveStatisticalAnalysis/ActiveDistribution')
);
const ActiveAreaDistribution = lazy(
  () =>
    import('@/pages/Pathogen/ActiveStatisticalAnalysis/ActiveAreaDistribution')
);
const ActiveAgeDistribution = lazy(
  () =>
    import('@/pages/Pathogen/ActiveStatisticalAnalysis/ActiveAgeDistribution')
);
const ActivePopulationDistribution = lazy(
  () =>
    import(
      '@/pages/Pathogen/ActiveStatisticalAnalysis/ActivePopulationDistribution'
    )
);

// 业务指标分析
const NewDetectionRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/NewDetectionRate')
);

const NewSamplingRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/NewSamplingRate')
);

const RegionalSamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/RegionalSamplingRate'
    )
);
const AgeSamplingRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/AgeSamplingRate')
);
const PopulationSamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PopulationSamplingRate'
    )
);
const SeasonSamplingRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/SeasonSamplingRate')
);
const MonthlySamplingRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/MonthlySamplingRate'
    )
);
const RegionalDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/RegionalDetectionRate'
    )
);
const AgeDetectionRate = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/AgeDetectionRate')
);

const PopulationDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PopulationDetectionRate'
    )
);
const SeasonDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/SeasonDetectionRate'
    )
);
const MonthlyDetectionRate = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/MonthlyDetectionRate'
    )
);
const PathogenDrugResistance = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenDrugResistance'
    )
);
const DrugResistanceTrends = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DrugResistanceTrends'
    )
);
const PathogenDrugResistanceTestRecords = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenDrugResistanceTestRecords'
    )
);
const PathogenComplexInfection = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfection'
    )
);
const DrugResistanceAnalysis = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DrugResistanceAnalysis'
    )
);
const PathogenSpectrumAnalysis = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenSpectrumAnalysis'
    )
);
const PathogenComplexInfectionArea = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionArea'
    )
);
const PathogenComplexInfectionAge = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionAge'
    )
);
const PathogenComplexInfectionPopulation = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenComplexInfectionPopulation'
    )
);
const PathogenPositiveSeasonality = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveSeasonality'
    )
);
const PathogenPositiveTrend = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveTrend'
    )
);
const PathogenPositiveRegionality = lazy(
  () =>
    import(
      '@/pages/IndexAnalysis/BusinessIndicatorAnalysis/PathogenPositiveRegionality'
    )
);
const DiseaseSpectrumMap = lazy(
  () =>
    import('@/pages/IndexAnalysis/BusinessIndicatorAnalysis/DiseaseSpectrumMap')
);

// 病原监测预警
const PositiveWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/PositiveWarning')
);
const AreaWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AreaWarning')
);
const AgeWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AgeWarning')
);
const PeopleWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/PeopleWarning')
);
const AcutePathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/AcutePathogenWarning')
);
const HighlyPathogenicWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/HighlyPathogenicWarning')
);
const NewPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/NewPathogenWarning')
);
const SuddenlyPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/SuddenlyPathogenWarning')
);
const ImportPathogenWarning = lazy(
  () => import('@/pages/IndexAnalysis/PathogenWarning/ImportPathogenWarning')
);

export const pathogenRouter = [
  // 病原监测数据字典
  {
    title: '病原监测数据字典',
    path: 'pathogenDictionary',
    completePath: '/pathogen/pathogenDictionary',
  },
  {
    title: '传染病库',
    path: 'infectiousLibrary',
    completePath: '/pathogen/pathogenDictionary/infectiousLibrary',
    element: <InfectiousLibrary />,
  },
  {
    title: '贵州省病原库',
    path: 'guizhouLibrary',
    completePath: '/pathogen/pathogenDictionary/guizhouLibrary',
    element: <GuizhouLibrary />,
  },
  {
    title: '传染病病原全库',
    path: 'infectiousAllLibrary',
    completePath: '/pathogen/pathogenDictionary/infectiousAllLibrary',
    element: <InfectiousAllLibrary />,
  },

  { title: '病原监测', path: '/pathogen' },
  // 监测业务
  {
    title: '监测业务',
    path: 'detectionBusiness',
    completePath: '/pathogen/detectionBusiness',
  },
  {
    title: '监测哨点',
    path: 'point',
    completePath: '/pathogen/detectionBusiness/point',
    element: <PathogenPoint />,
  },
  {
    title: '监测计划',
    path: 'plan',
    completePath: '/pathogen/detectionBusiness/plan',
    element: <PathogenPlan />,
  },
  {
    title: '监测任务',
    path: 'ptask',
    completePath: '/pathogen/detectionBusiness/ptask',
    element: <PathogenTask />,
  },
  {
    title: '样本检测信息',
    path: 'psample',
    completePath: '/pathogen/detectionBusiness/psample',
    element: <PathogenSmaple />,
  },
  {
    title: '监测结果巡查',
    path: 'testResultInspection',
    completePath: '/pathogen/detectionBusiness/testResultInspection',
    element: <TestResultInspection />,
  },
  {
    title: '菌株复核任务',
    path: 'strainVerificationTask',
    completePath: '/pathogen/detectionBusiness/strainVerificationTask',
    element: <StrainVerificationTask />,
  },
  {
    title: '复核检测结果巡查',
    path: 'resultsInspection',
    completePath: '/pathogen/detectionBusiness/resultsInspection',
    element: <ResultsInspection />,
  },
  {
    title: '耐药性检测',
    path: 'drugResistanceTesting',
    completePath: '/pathogen/detectionBusiness/drugResistanceTesting',
    element: <DrugResistanceTesting />,
  },

  // 监测业务统计分析
  {
    title: '监测业务统计分析',
    path: 'statisticalAnalysis',
    completePath: '/pathogen/statisticalAnalysis',
  },
  {
    title: '任务进度',
    path: 'taskProgress',
    completePath: '/pathogen/statisticalAnalysis/taskProgress',
    element: <TaskProgress />,
  },
  {
    title: '菌株复核统计',
    path: 'strainReview',
    completePath: '/pathogen/statisticalAnalysis/strainReview',
    element: <StrainReview />,
  },
  {
    title: '监测分布',
    path: 'distribution',
    completePath: '/pathogen/statisticalAnalysis/distribution',
    element: <Distribution />,
  },
  {
    title: '监测地区分布',
    path: 'areaDistribution',
    completePath: '/pathogen/statisticalAnalysis/areaDistribution',
    element: <AreaDistribution />,
  },
  {
    title: '监测年龄分布',
    path: 'ageDistribution',
    completePath: '/pathogen/statisticalAnalysis/ageDistribution',
    element: <AgeDistribution />,
  },
  {
    title: '监测人群分布',
    path: 'populationDistribution',
    completePath: '/pathogen/statisticalAnalysis/populationDistribution',
    element: <PopulationDistribution />,
  },
  // 主动监测
  {
    title: '主动监测',
    path: 'activeSurveillance',
    completePath: '/pathogen/activeSurveillance',
  },
  {
    title: '样本检测信息',
    path: 'sampleDetectionInfo',
    completePath: '/pathogen/activeSurveillance/sampleDetectionInfo',
    element: <SampleDetectionInfo />,
  },
  {
    title: '检测结果巡查',
    path: 'testResultInspect',
    completePath: '/pathogen/activeSurveillance/testResultInspect',
    element: <TestResultInspect />,
  },
  {
    title: '增量监测分布',
    path: 'activeDistribution',
    completePath: '/pathogen/activeSurveillance/activeDistribution',
    element: <ActiveDistribution />,
  },
  // 主动监测统计分析
  {
    title: '主动监测统计分析',
    path: 'activeStatisticalAnalysis',
    completePath: '/pathogen/activeStatisticalAnalysis',
  },
  // {
  //   title: '增量监测分布',
  //   path: 'activeDistribution',
  //   completePath: '/pathogen/activeStatisticalAnalysis/activeDistribution',
  //   element: <ActiveDistribution />,
  // },
  {
    title: '地区分布统计',
    path: 'activeAreaDistribution',
    completePath: '/pathogen/activeStatisticalAnalysis/activeAreaDistribution',
    element: <ActiveAreaDistribution />,
  },
  {
    title: '年龄分布统计',
    path: 'activeAgeDistribution',
    completePath: '/pathogen/activeStatisticalAnalysis/activeAgeDistribution',
    element: <ActiveAgeDistribution />,
  },
  {
    title: '人群分布统计',
    path: 'activePopulationDistribution',
    completePath:
      '/pathogen/activeStatisticalAnalysis/activePopulationDistribution',
    element: <ActivePopulationDistribution />,
  },

  // 业务指标分析
  {
    title: '业务指标分析',
    path: 'businessIndicator',
    completePath: '/pathogen/businessIndicator',
  },
  {
    title: '采样率分析',
    path: 'newSamplingRate',
    completePath: '/pathogen/businessIndicator/newSamplingRate',
    element: <NewSamplingRate />,
  },
  {
    title: '检出率分析',
    path: 'newDetectionRate',
    completePath: '/pathogen/businessIndicator/newDetectionRate',
    element: <NewDetectionRate />,
  },
  {
    title: '区域采样率分析',
    path: 'regionalSamplingRate',
    completePath: '/pathogen/businessIndicator/regionalSamplingRate',
    element: <RegionalSamplingRate />,
  },
  {
    title: '年龄采样率分析',
    path: 'ageSamplingRate',
    completePath: '/pathogen/businessIndicator/ageSamplingRate',
    element: <AgeSamplingRate />,
  },
  {
    title: '人群采样率分析',
    path: 'populationSamplingRate',
    completePath: '/pathogen/businessIndicator/populationSamplingRate',
    element: <PopulationSamplingRate />,
  },
  {
    title: '季节采样率分析',
    path: 'seasonSamplingRate',
    completePath: '/pathogen/businessIndicator/seasonSamplingRate',
    element: <SeasonSamplingRate />,
  },
  {
    title: '月度采样率分析',
    path: 'monthlySamplingRate',
    completePath: '/pathogen/businessIndicator/monthlySamplingRate',
    element: <MonthlySamplingRate />,
  },
  {
    title: '区域检出率分析',
    path: 'regionalDetectionRate',
    completePath: '/pathogen/businessIndicator/regionalDetectionRate',
    element: <RegionalDetectionRate />,
  },
  {
    title: '年龄检出率分析',
    path: 'ageDetectionRate',
    completePath: '/pathogen/businessIndicator/ageDetectionRate',
    element: <AgeDetectionRate />,
  },
  {
    title: '人群检出率分析',
    path: 'populationDetectionRate',
    completePath: '/pathogen/businessIndicator/populationDetectionRate',
    element: <PopulationDetectionRate />,
  },
  {
    title: '季节检出率分析',
    path: 'seasonDetectionRate',
    completePath: '/pathogen/businessIndicator/seasonDetectionRate',
    element: <SeasonDetectionRate />,
  },
  {
    title: '月度检出率分析',
    path: 'monthlyDetectionRate',
    completePath: '/pathogen/businessIndicator/monthlyDetectionRate',
    element: <MonthlyDetectionRate />,
  },
  {
    title: '病原耐药性分析',
    path: 'pathogenDrugResistance',
    completePath: '/pathogen/businessIndicator/pathogenDrugResistance',
    element: <PathogenDrugResistance />,
  },
  {
    title: '耐药性趋势分析',
    path: 'drugResistanceTrends',
    completePath: '/pathogen/businessIndicator/drugResistanceTrends',
    element: <DrugResistanceTrends />,
  },
  {
    title: '病原耐药性检测记录',
    path: 'pathogenDrugResistanceTestRecords',
    completePath:
      '/pathogen/businessIndicator/pathogenDrugResistanceTestRecords',
    element: <PathogenDrugResistanceTestRecords />,
  },
  {
    title: '耐药性分析',
    path: 'drugResistanceAnalysis',
    completePath: '/pathogen/businessIndicator/drugResistanceAnalysis',
    element: <DrugResistanceAnalysis />,
  },
  {
    title: '病原谱分析',
    path: 'pathogenSpectrumAnalysis',
    completePath: '/pathogen/businessIndicator/pathogenSpectrumAnalysis',
    element: <PathogenSpectrumAnalysis />,
  },
  {
    title: '复合感染分析',
    path: 'pathogenComplexInfection',
    completePath: '/pathogen/businessIndicator/pathogenComplexInfection',
    element: <PathogenComplexInfection />,
  },
  {
    title: '病原复合感染区域分析',
    path: 'pathogenComplexInfectionArea',
    completePath: '/pathogen/businessIndicator/pathogenComplexInfectionArea',
    element: <PathogenComplexInfectionArea />,
  },
  {
    title: '病原复合感染年龄分析',
    path: 'pathogenComplexInfectionAge',
    completePath: '/pathogen/businessIndicator/pathogenComplexInfectionAge',
    element: <PathogenComplexInfectionAge />,
  },
  {
    title: '病原复合感染人群分析',
    path: 'pathogenComplexInfectionPopulation',
    completePath:
      '/pathogen/businessIndicator/pathogenComplexInfectionPopulation',
    element: <PathogenComplexInfectionPopulation />,
  },
  {
    title: '病原阳性季节性分析',
    path: 'pathogenPositiveSeasonality',
    completePath: '/pathogen/businessIndicator/pathogenPositiveSeasonality',
    element: <PathogenPositiveSeasonality />,
  },
  {
    title: '病原阳性趋势分析',
    path: 'pathogenPositiveTrend',
    completePath: '/pathogen/businessIndicator/pathogenPositiveTrend',
    element: <PathogenPositiveTrend />,
  },
  {
    title: '病原阳性区域性分析',
    path: 'pathogenPositiveRegionality',
    completePath: '/pathogen/businessIndicator/pathogenPositiveRegionality',
    element: <PathogenPositiveRegionality />,
  },
  {
    title: '病谱图构成分析',
    path: 'diseaseSpectrumMap',
    completePath: '/pathogen/businessIndicator/diseaseSpectrumMap',
    element: <DiseaseSpectrumMap />,
  },

  // 病原监测预警
  {
    title: '病原监测预警',
    path: 'pathogenWarning',
    completePath: '/pathogen/pathogenWarning',
  },
  {
    title: '病原阳性预警',
    path: 'positiveWarning',
    completePath: '/pathogen/pathogenWarning/positiveWarning',
    element: <PositiveWarning />,
  },
  {
    title: '区域态势预警',
    path: 'areaWarning',
    completePath: '/pathogen/pathogenWarning/areaWarning',
    element: <AreaWarning />,
  },
  {
    title: '年龄态势预警',
    path: 'ageWarning',
    completePath: '/pathogen/pathogenWarning/ageWarning',
    element: <AgeWarning />,
  },
  {
    title: '人群态势预警',
    path: 'peopleWarning',
    completePath: '/pathogen/pathogenWarning/peopleWarning',
    element: <PeopleWarning />,
  },
  {
    title: '急性病原预警',
    path: 'acutePathogenWarning',
    completePath: '/pathogen/pathogenWarning/acutePathogenWarning',
    element: <AcutePathogenWarning />,
  },
  {
    title: '高致病性病原预警',
    path: 'highlyPathogenicWarning',
    completePath: '/pathogen/pathogenWarning/highlyPathogenicWarning',
    element: <HighlyPathogenicWarning />,
  },
  {
    title: '新发病原预警',
    path: 'newPathogenWarning',
    completePath: '/pathogen/pathogenWarning/newPathogenWarning',
    element: <NewPathogenWarning />,
  },
  {
    title: '突发病原预警',
    path: 'suddenlyPathogenWarning',
    completePath: '/pathogen/pathogenWarning/suddenlyPathogenWarning',
    element: <SuddenlyPathogenWarning />,
  },
  {
    title: '输入性病原预警',
    path: 'importPathogenWarning',
    completePath: '/pathogen/pathogenWarning/importPathogenWarning',
    element: <ImportPathogenWarning />,
  },
];
