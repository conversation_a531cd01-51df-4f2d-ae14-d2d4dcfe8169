import { lazy } from 'react';

// 机构考核
const QualityTask = lazy(() => import('@/pages/Quality/Task'));
const QualityExamine = lazy(() => import('@/pages/Quality/Examine'));
const QualityTaskList = lazy(() => import('@/pages/Quality/TaskList'));
const QualityInspect = lazy(() => import('@/pages/Quality/Inspect'));
const BatchAttDownload = lazy(() => import('@/pages/Quality/BatchAttDownload'));
const QualitySample = lazy(() => import('@/pages/Quality/Sample'));
const QualityJudge = lazy(() => import('@/pages/Quality/Judge'));
const QualityTemp = lazy(() => import('@/pages/Quality/Temp'));
const QualityAssessmentType = lazy(
  () => import('@/pages/Quality/AssessmentTypeConfig')
);
const CertificateManage = lazy(
  () => import('@/pages/Quality/CertificateManage')
);
const CertificateQuery = lazy(() => import('@/pages/Quality/CertificateQuery'));
const PublicityManagement = lazy(
  () => import('@/pages/Quality/PublicityManagement')
);
const MechanismAnalysis = lazy(
  () => import('@/pages/Quality/StatisticalAnalysis/MechanismAnalysis')
);
const AssessmentType = lazy(
  () => import('@/pages/Quality/StatisticalAnalysis/AssessmentType')
);
const AssessmentTask = lazy(
  () => import('@/pages/Quality/StatisticalAnalysis/AssessmentTask')
);

export const qualityRouter = [
  { title: '质量考核', path: '/quality' },
  {
    title: '考核任务',
    path: 'task',
    completePath: '/quality/task',
    element: <QualityTask />,
  },
  {
    title: '样本接收',
    path: 'sample',
    completePath: '/quality/sample',
    element: <QualitySample />,
  },
  {
    title: '检验管理',
    path: 'inspect',
    completePath: '/quality/inspect',
    element: <QualityInspect />,
  },
  {
    title: '结果评判',
    path: 'judge',
    completePath: '/quality/judge',
    element: <QualityJudge />,
  },
  {
    title: '批量附件下载',
    path: 'batchAttDownload',
    completePath: '/quality/batchAttDownload',
    element: <BatchAttDownload />,
  },
  {
    title: '考核列表',
    path: 'examine',
    completePath: '/quality/examine',
    element: <QualityExamine />,
  },
  {
    title: '任务列表',
    path: 'tasklist',
    completePath: '/quality/tasklist',
    element: <QualityTaskList />,
  },
  {
    title: '模板管理',
    path: 'temp',
    completePath: '/quality/temp',
    element: <QualityTemp />,
  },
  {
    title: '考核类型管理',
    path: 'assessmentType',
    completePath: '/quality/assessmentType',
    element: <QualityAssessmentType />,
  },

  {
    title: '证书管理',
    path: 'certificateManage',
    completePath: '/quality/certificateManage',
    element: <CertificateManage />,
  },
  {
    title: '证书查询',
    path: 'certificateQuery',
    completePath: '/quality/certificateQuery',
    element: <CertificateQuery />,
  },
  {
    title: '公示管理',
    path: 'publicityManagement',
    completePath: '/quality/publicityManagement',
    element: <PublicityManagement />,
  },
  {
    title: '统计分析',
    path: 'statisticalAnalysis',
    completePath: '/quality/statisticalAnalysis',
  },
  {
    title: '机构分析',
    path: 'mechanismAnalysis',
    completePath: '/quality/statisticalAnalysis/mechanismAnalysis',
    element: <MechanismAnalysis />,
  },
  {
    title: '考核类型',
    path: 'assessmentTypeStatistic',
    completePath: '/quality/statisticalAnalysis/assessmentTypeStatistic',
    element: <AssessmentType />,
  },
  {
    title: '考核任务',
    path: 'assessmentTask',
    completePath: '/quality/statisticalAnalysis/assessmentTask',
    element: <AssessmentTask />,
  },
];
