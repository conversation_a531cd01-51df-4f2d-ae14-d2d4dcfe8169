/* eslint-disable @typescript-eslint/no-unused-vars */
import { lazy, useEffect } from 'react';
import {
  Location,
  NavigateFunction,
  useLocation,
  useNavigate,
  useRoutes,
} from 'react-router-dom';
import { appTitle } from '@/constants';
import FullScreenLayout from '@/layouts/FullScreenLayout';
import { dataPatrolRouter } from './modules/dataPatrol';
import { dataReportRouter } from './modules/dataReport';
import { epidemicTrackingRouter } from './modules/epidemicTracking';
import { fullScreenRouter } from './modules/fullScreen';
import { indexAnalysisRouter } from './modules/indexAnalysis';
import { laboratoryRatingRouter } from './modules/LaboratoryRating';
import { orgRiskAssessmentRouter } from './modules/orgRiskAssessment';
import { pathogenRouter } from './modules/pathogen';
import { qualityRouter } from './modules/quality';
import { systemRouter } from './modules/system';

const PageLayout = lazy(() => import('@/layouts/PageLayout'));
const Home = lazy(() => import('@/pages/HomeNew'));
const HomeOld = lazy(() => import('@/pages/Home'));
const Login = lazy(() => import('@/pages/Login/Login'));
const Pwd = lazy(() => import('@/pages/Pwd'));
const NotFound = lazy(() => import('@/pages/404'));
const UserCenter = lazy(() => import('@/pages/UserCenter'));
const NotAuth = lazy(() => import('@/pages/403'));

// 监测概况
const MonitoringOverview = lazy(
  () => import('@/pages/DataVisualizationDashboard/MonitoringOverview')
);

// 文件预览
const EFileViewWrapper = lazy(() => import('@/components/EFileView'));

// 专家模块
const ExpertAuth = lazy(() => import('@/pages/LaboratoryRating/ExpertAuth'));

//证书管理

export const InitialRoutes: any[] = [
  // 页面路由策略
  {
    element: <PageLayout />,
    children: [
      // 首页
      {
        path: '/',
        element: <Home />,
        title: '首页',
      },
      {
        path: '/HomeOld',
        element: <HomeOld />,
        title: '首页',
      },
      // 用户中心
      {
        path: '/usercenter',
        element: <UserCenter />,
        title: '用户中心',
      },
      // 没有访问权限的路由 403
      {
        path: '/notAuth',
        element: <NotAuth />,
        title: '没有访问权限',
      },
      // 未匹配上的路由 404
      {
        path: '/*',
        element: <NotFound />,
        title: '未知页面',
      },
    ],
  },
  // 其他不需要 PageLayout 控制的页面
  {
    path: '/login',
    element: <Login />,
    title: '登录',
  },
  // 第一次登录修改密码页面
  {
    path: '/pwd',
    element: <Pwd />,
    title: '修改密码',
  },
  // 专家模块
  {
    path: '/expertAuth',
    element: <ExpertAuth />,
    title: '专家模块',
  },
  // 可视化大屏路由
  ...fullScreenRouter,
];

export const AllRouteMappings: Record<string, any>[] = [
  {
    title: '首页',
    path: '/',
    element: <Home />,
    completePath: '/',
  },
  { title: '未知页面', path: '/*', element: <NotFound /> },
  { title: '没有访问权限', path: '/notAuth', element: <NotAuth /> },
  { title: '登录', path: '/login', element: <Login /> },
  {
    title: '修改密码',
    path: '/pwd',
    element: <Pwd />,
  },
  {
    title: '个人中心',
    path: '/usercenter',
    element: <UserCenter />,
  },

  // 质量考核
  ...qualityRouter,

  // 系统管理
  ...systemRouter,

  // 疫情追踪
  ...epidemicTrackingRouter,

  // 病原监测
  ...pathogenRouter,

  // 数据上报
  ...dataReportRouter,

  // 数据巡查
  ...dataPatrolRouter,

  // 实验室评级
  ...laboratoryRatingRouter,

  // 机构风险评价
  ...orgRiskAssessmentRouter,

  // 指标分析
  ...indexAnalysisRouter,
];

/**
 * @description 递归查询对应的路由
 * @param path
 * @param routes
 * @returns
 */
export function searchRouteDetail(
  path: string,
  routes: Record<string, any>[]
): Record<string, any> | null {
  let data: any;
  (routes || []).forEach((item) => {
    if (item.path === path || item.completePath === path) {
      data = item;
    } else if (item.children) {
      const child = searchRouteDetail(path, item.children);
      if (child) {
        data = child;
      }
    }
  });
  return data;
}

/**
 * @description 全局路由守卫
 * @param location
 * @param navigate
 * @param routes
 * @returns
 */
function guard(
  location: Location,
  navigate: NavigateFunction,
  routes: Record<string, any>[]
) {
  const { pathname } = location;
  //找到对应的路由信息
  const routeDetail = searchRouteDetail(pathname, routes);
  //没有找到路由，跳转404
  if (!routeDetail) {
    return false;
  }

  /**
   * @TODO 动态自定义窗口标题
   */
  if (routeDetail.title) {
    document.title = `${appTitle} - ${routeDetail.title}`;
  } else {
    document.title = `${appTitle}`;
  }

  //如果需要权限验证
  const tokenStr = sessionStorage.getItem('gzjk_token_storage');
  const token = tokenStr ? JSON.parse(tokenStr).state.token : null;
  if (!token && routeDetail.path !== '/login') {
    sessionStorage.setItem('gzjk_user_info', '');
    navigate('/login');
    return false;
  }
  const userInfoStr = sessionStorage.getItem('gzjk_user_info');
  const userInfo = userInfoStr ? JSON.parse(userInfoStr).state.userInfo : null;
  if (
    userInfo &&
    userInfo.user &&
    userInfo.user.inited === '0' &&
    routeDetail.path !== '/pwd'
  ) {
    navigate('/pwd');
    return false;
  }
  return true;
}

export const RouterGurad = (routes: Record<string, any>[]) => {
  const location = useLocation();
  const navigate = useNavigate();
  useEffect(() => {
    guard(location, navigate, routes);
  }, [location, navigate, routes]);
  const Route = useRoutes(routes);
  return Route;
};
