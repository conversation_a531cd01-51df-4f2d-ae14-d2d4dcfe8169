/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router';
import { App, ConfigProvider, message } from 'antd';
import { Locale } from 'antd/es/locale';
import zhCN from 'antd/lib/locale/zh_CN';
import { AllRouteMappings, InitialRoutes, RouterGurad } from '@/routes';
import { cloneDeep } from 'lodash';
import Loading from '@/components/Loading';
import { queryPermissionRouter } from './api/auth';
import { codeDefinition } from './constants';



import {
  useLoadingStore,
  usePermissionRouterStore,
  useThemeStore,
  useTokenStore,
} from './store';

/**
 * @TODO 生成react-router依赖的route数据
 */
export const getInitialRoutes = (route: any, pre: string = ''): any => {
  const arr = route;
  const data: any = arr.map(() => ({
    children: [],
  }));
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const completePath = pre + item.path;
    let val = AllRouteMappings.find((_route) => {
      if (_route.completePath) {
        return _route.completePath === completePath;
      } else {
        return (
          _route.path === item.path ||
          _route.path === '/' + item.path ||
          (item.path === 'home' && _route.path === '/')
        );
      }
    });
    if (val) {
      data[i] = { ...data[i], ...val, meta: item.meta }; // todo
      if (item.children && item.children.length) {
        data[i].children = [
          ...getInitialRoutes(item.children, pre + item.path + '/'),
        ];
      } else {
        delete data[i].children;
      }
    }
  }
  return [...data.filter((item: any) => item.path)];
};
const APP: React.FC = () => {
  // 当前默认主题
  const { theme } = useThemeStore();
  const { token } = useTokenStore();
  // 当前系统语言
  const [locale] = useState<Locale>(zhCN);
  const { golbalLoading } = useLoadingStore();

  const { setAllRouter, setRouteStructure, setMenuData, routeStructure } =
    usePermissionRouterStore();

  /**
   * @TODO 获取用户路由
   */
  const getUserRouter = async () => {
    try {
      const { code, data, msg }: any = await queryPermissionRouter();
      if (code === codeDefinition.QUERY_SUCCESS) {
        setAllRouter(data);
        // 生成react-router依赖的route数据
        const route = cloneDeep(InitialRoutes);
        const menus = getInitialRoutes(data);
        route[0].children = [...route[0].children, ...menus];
        setRouteStructure(route);
        setMenuData(menus);
      } else {
        message.error(msg);
      }
    } catch (error) {
      throw new Error(`Error: ${error}`);
    }
  };

  useEffect(() => {
    // TODO 为了解决第三方登录问题，暂时注释掉这行代码 这段代码是为了获取用户的路由信息的，但是在登录时候已经获取了路由信息，所以不需要再次获取了
    token && getUserRouter();
  }, [token]);

  const [routeStructure1, setRouteStructure1] = useState<any>(routeStructure);

  useEffect(() => {
    let  parsedData:any
    
    const storedRouteStructure = localStorage.getItem('routeStructure');
    
    setRouteStructure1(routeStructure);
    
    if (storedRouteStructure) {
      try {
        parsedData = JSON.parse(storedRouteStructure);
        console.log(parsedData.routeStructure, routeStructure);
        
        setRouteStructure1(parsedData);
      } catch (error) {
        console.error('解析 routeStructure 数据出错:', error);
      }
    } else {
        setRouteStructure1(routeStructure);
    }


    
  }, [routeStructure]);


  return (
    <ConfigProvider locale={locale} theme={theme}>
      <App>
        <React.Suspense fallback={<Loading />}>
          {/* 之前取了动态的路由 routeStructure */}
          {/* 当时由于这个routeStructure的数据是用store储存的，所以刷新之后，可视化就白屏了，为了解决白屏问题路由先写死 */}
          <div className={`App h-[100vh]`}>{RouterGurad(routeStructure)}</div>
          {golbalLoading ? (
            <div className="absolute top-0 left-0 w-full h-full bg-[rgba(0,0,0,0.2)]">
              <Loading
                text="正在下载, 请稍候..."
                textClassName="text-white transform -translate-y-4"
              />
            </div>
          ) : null}
        </React.Suspense>
      </App>
    </ConfigProvider>
  );
};

export default APP;
