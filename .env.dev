###
 # @Author: l
 # @Date: 2025-01-15 16:07:05
 # @LastEditors: <PERSON><PERSON>hong<PERSON><PERSON>
 # @LastEditTime: 2025-03-25 09:37:30
 # @FilePath: \xr-qc-jk-web\.env.dev
 # @Description: 
### 
# .env 所有模式下都会加载的配置信息
# 这里没有修改默认的前缀名，仍然是 VITE_

VITE_ENV = 'development'
VITE_PORT = 8081

# 公共请求前缀
VITE_URL = '/api'

## 代理地址

# 测试
# VITE_PROXY_TARGET = 'http://**************:6115/qc'
# VITE_PROXY_TARGET = 'http://jobsz.xrdev.cn/xr-qc-jk'
# 本地
VITE_PROXY_TARGET = 'http://***************:8998/biz'
# 正式环境
#  VITE_PROXY_TARGET = 'http://**************:5113/qc'

VITE_PUBLIC_PATH = '/xr-qc-jk-web/'

# 是否显示版权 备案信息
VITE_SHOW_COPYRIGHT = false
