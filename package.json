{"name": "vite-react-template", "description": "基于Vite创建的React Typescript模版工程, 包含Antd、TailwindCSS", "private": true, "version": "0.0.1", "company": "北京信睿", "keywords": ["vite", "react", "typescript", "tailwindcss", "antd"], "homepage": "www.example.com", "author": "liuzhen <EMAIL> (https://github.com/kaindy7633)", "type": "module", "scripts": {"dev:mock": "vite --mode mock", "dev": "vite --mode dev", "build:test": "vite build --mode test", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "2.4.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@react-spring/web": "^9.7.4", "ahooks": "^3.8.1", "antd": "5.24.2", "canvas-datagrid": "^0.4.7", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "history": "^5.3.0", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "mammoth": "^1.9.0", "mathjs": "^12.4.3", "nano-memoize": "^1.3.1", "pptxgenjs": "^3.12.0", "pptxjs": "^0.0.0", "ramda": "^0.28.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-router": "^6.26.1", "react-router-dom": "^6.26.1", "swiper": "^9.4.1", "umi-request": "^1.4.0", "xlsx": "^0.18.5", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-flow": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0", "@types/lodash": "^4.17.7", "@types/node": "^18.19.47", "@types/ramda": "^0.28.25", "@types/react": "^18.3.4", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@types/swiper": "^6.0.0", "@vitejs/plugin-react": "^2.2.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-config-react-app": "^7.0.1", "less": "^4.2.0", "mockjs": "^1.1.0", "postcss": "^8.4.41", "prettier": "^2.8.8", "react-doc-viewer": "^0.1.14", "rollup": "^3.29.4", "tailwindcss": "^3.4.10", "terser": "^5.39.0", "typescript": "^4.9.5", "vite": "^4.5.3", "vite-plugin-imp": "^2.4.0", "vite-plugin-mock": "^2.9.8"}, "license": "UNLICENSED", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}